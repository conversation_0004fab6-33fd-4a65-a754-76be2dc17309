import os
import django
import logging

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_application.settings")
django.setup()

from django.conf import settings
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from app.models import CoursePackageContent, SuperViseInitStudentStatus

# 添加日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置文本大模型
llm_text = ChatOpenAI(
    openai_api_key=settings.DOUBAO_API_KEY,
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-seed-1-6-250615"
)


def generate_personal_learning_guidance():





    # 构造提示词模板
    template = """### 你是一个专业的408考纲分析专家。请仔细分析{content}的内容，
请按以下要求进行分析：
1. 识别所有图片中的文本内容并准确提取
2. 请把所有图片当成一个整体进行分析，并根据图片下方的页码顺序分析，
3. 帮我深度整理一下408计算机大纲，包括（数据结构、计算机组成原理、计算机网络、操作系统）
4. 并针对历年考试的要求和真题分析，对大纲涉及到的章节考点进行重要程度和难度进行区分，用合适的标签标注，并以列表形式输出。
"""

    prompt = PromptTemplate.from_template(template)
    llm_chain = prompt | llm_text

    # 调用大模型生成学习指导报告
    result = llm_chain.invoke({
        "unit_name": course_content.unit_name,
        "study_target": course_content.study_target or '',
        "study_guidence": course_content.study_guidence or '',
        "unit_task": course_content.unit_task or '',
        "important_sections": course_content.important_sections or '',
        "analysis_data": analysis_data or ''
    })

    return result


def save_result_as_markdown(result, filename):
    """
    将结果保存为Markdown文件

    Args:
        result: 模型生成的结果
        filename: 保存的文件名，默认为personal_learning_guidance.md

    Returns:
        str: 保存的文件路径
    """
    # 确保文件以.md结尾
    if not filename.endswith('.md'):
        filename += '.md'

    # 提取结果内容
    if hasattr(result, 'content'):
        content = result.content
    else:
        content = str(result)

    # 保存到文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

    return filename


if __name__ == "__main__":
    # 生成个人学习指导报告
    result = generate_personal_learning_guidance()

    if result:
        # 保存结果为文件
        saved_file = save_result_as_markdown(result, "../数学-个人学习指导报告1.md")
        print(f"个人学习指导报告已保存到: {saved_file}")

        # 打印结果内容
        content = result.content if hasattr(result, 'content') else str(result)
        print("生成的个人学习指导报告内容:")
        print(content)
    else:
        print("生成个人学习指导报告失败")