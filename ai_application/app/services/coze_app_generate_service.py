
import json
import logging
import time
import uuid
from typing import Generator

from cozepy import Chat<PERSON>ventType, ChatUsage, MessageObjectString, MessageObjectStringType
from cozepy import Message as CozeMessage
from django.conf import settings
from django.core.cache import cache
from pydantic_core import ValidationError

from app.api.dto import CozeConversationAdd, CozeChatMessageDto
from app.constants.app import AppMode
from app.libs.coze_api import coze_api_client
from app.errors import AppNotFoundError, MessageNotFoundError
from app.models import App, Account, Message, InvokeFrom, Conversation

logger = logging.getLogger(__name__)


class ChatCancelCachedHelper:
    STOP_MESSAGE_KEY = 'coze_chat_message_cancel_{message_no}'

    @classmethod
    def gen_key(cls, message_no: str) -> str:
        return cls.STOP_MESSAGE_KEY.format(message_no=message_no)

    @classmethod
    def set_stop_msg_cache(cls, message_no: str):
        cache.set(cls.gen_key(message_no), message_no, timeout=60)

    @classmethod
    def get_stop_msg_cache(cls, message_no: str) -> str:
        return cache.get(cls.gen_key(message_no), '')

    @classmethod
    def delete_stop_msg_cache(cls, message_no: str):
        cache.delete(cls.gen_key(message_no))



def save_message_success(
    dto: CozeChatMessageDto,
    message_no,
    conversation,
    app_model,
    from_account,
    answer: str,
    usage: ChatUsage | None
):
    if not message_no:
        message_no = str(uuid.uuid4())

    file_objs = []
    if dto.image:
        file_objs = [dto.image]

    Message.objects.create(
        message_no=message_no,
        conversation=conversation,
        app=app_model,
        app_model_config=app_model.app_model_config,
        query=dto.query,
        file_objs=file_objs,
        message_tokens=usage.input_count if usage else 0,
        answer_tokens=usage.output_count if usage else 0,
        total_tokens=usage.token_count if usage else 0,
        response_latency=0,
        answer=answer,
        status='normal',
        from_account=from_account,
        from_biz_id=dto.biz_id,
        invoke_from=InvokeFrom.api.value,
        userinfo=dto.userinfo,
    )


def save_message_fail(
    dto: CozeChatMessageDto,
    message_no,
    conversation,
    app_model,
    from_account,
    err_msg,
):
    if not message_no:
        message_no = str(uuid.uuid4())

    file_objs = []
    if dto.image:
        file_objs = [dto.image]

    Message.objects.create(
        message_no=message_no,
        conversation=conversation,
        app=app_model,
        app_model_config=app_model.app_model_config,
        query=dto.query,
        file_objs=file_objs,
        message_tokens=0,
        answer_tokens=0,
        total_tokens=0,
        response_latency=0,
        answer=settings.LLM_NETWORK_ERROR_ANSWER,
        from_account=from_account,
        from_biz_id=dto.biz_id,
        invoke_from=InvokeFrom.api.value,
        userinfo=dto.userinfo,
        status='error',
        is_exception=True,
        error=err_msg,
    )


def publish_message_end(
    conversation_no: str,
    message_no: str,
    usage: ChatUsage | None
):
    chunk = {
        "event": "message_end",
        "created_at": int(time.time()),
        "metadata": {
            "message_tokens": usage.input_count if usage else 0,
            "answer_tokens": usage.output_count if usage else 0,
            "total_tokens": usage.token_count if usage else 0,
        },
        "conversation_id": conversation_no,
        "message_id": message_no
    }
    return json.dumps(chunk, ensure_ascii=False)


def coze_chat_stream(app_model: App, dto: CozeChatMessageDto, from_account: Account, coze_client=None):
    # custom coze client
    coze_client = coze_client or coze_api_client

    conversation = Conversation.objects.filter(conversation_no=dto.conversation_id).first()

    bot_id = app_model.app_model_config.third_app_key
    answer = ''
    message_no = None
    user_id = dto.userinfo.get('user_id', '') if dto.userinfo else ''
    if not user_id:
        # TODO 随机生成user_id
        user_id = 'test_user_id'

    message_objs = []
    if dto.query:
        message_objs.append(MessageObjectString(
            type=MessageObjectStringType.TEXT,
            text=dto.query
        ))
    if dto.image:
        message_objs.append(MessageObjectString(
            type=MessageObjectStringType.IMAGE,
            file_url=dto.image
        ))

    try:
        for event in coze_api_client.chat.stream(
                bot_id=bot_id,
                user_id=user_id,
                additional_messages=[
                    CozeMessage.build_user_question_objects(message_objs),
                ],
                conversation_id=dto.conversation_id,
        ):

            if event.event == ChatEventType.CONVERSATION_MESSAGE_DELTA:
                if not message_no:
                    message_no = event.message.id

                if event.message.content:
                    answer += event.message.content

                chunk = {
                    "event": "message",
                    "created_at": int(time.time()),
                    "answer": event.message.content,
                    "conversation_id": conversation.conversation_no,
                    "message_id": message_no,
                    'chat_id': event.message.chat_id,
                }
                chunk_str = json.dumps(chunk, ensure_ascii=False)

                # check cancel chat
                if ChatCancelCachedHelper.get_stop_msg_cache(message_no):
                    try:
                        coze_api_client.chat.cancel(
                            conversation_id=conversation.conversation_no,
                            chat_id=event.message.chat_id
                        )
                    except Exception as e:
                        logger.error(f'取消会话失败, message_no:{message_no}, error:{e}')

                    save_message_success(
                        dto=dto,
                        message_no=message_no,
                        conversation=conversation,
                        app_model=app_model,
                        from_account=from_account,
                        answer=answer,
                        usage=None,
                    )
                    yield f'data: {chunk_str}\n\n'
                    chunk_str = publish_message_end(conversation.conversation_no, message_no, None)
                    yield f'data: {chunk_str}\n\n'
                    return

                yield f'data: {chunk_str}\n\n'
            if event.event in (ChatEventType.CONVERSATION_CHAT_FAILED, ChatEventType.ERROR):
                err_msg = event.chat.last_error.model_dump_json()

                save_message_fail(
                    dto=dto,
                    message_no=message_no,
                    conversation=conversation,
                    app_model=app_model,
                    from_account=from_account,
                    err_msg=err_msg,
                )

                chunk = {
                    "event": "error",
                    "created_at": int(time.time()),
                    "answer": settings.LLM_NETWORK_ERROR_ANSWER,
                    "conversation_id": conversation.conversation_no,
                    "message_id": message_no
                }
                chunk_str = json.dumps(chunk, ensure_ascii=False)
                yield f'data: {chunk_str}\n\n'
            elif event.event == ChatEventType.CONVERSATION_MESSAGE_COMPLETED:
                if event.message.type == 'answer':
                    answer = event.message.content
                elif event.message.type == 'verbose':
                    completed_content = json.loads(event.message.content)
                    logger.debug(f'verbose.content={completed_content}')
            elif event.event == ChatEventType.CONVERSATION_CHAT_COMPLETED:
                try:
                    usage = event.chat.usage
                except:
                    usage = None

                save_message_success(
                    dto=dto,
                    message_no=message_no,
                    conversation=conversation,
                    app_model=app_model,
                    from_account=from_account,
                    answer=answer,
                    usage=usage
                )

                chunk_str = publish_message_end(conversation.conversation_no, message_no, usage)
                yield f'data: {chunk_str}\n\n'

    except ValidationError as v_e:
        # 特殊处理cozepy处理tool工具
        # if "input_value='reply_message'" in v_e.
        v_e_log = v_e.errors()
        if v_e_log[0].get('type') == 'enum' and v_e_log[0].get('input') == 'reply_message':
            # 保存message
            save_message_success(
                dto=dto,
                message_no=message_no,
                conversation=conversation,
                app_model=app_model,
                from_account=from_account,
                answer=answer,
                usage=None
            )
            chunk_str = publish_message_end(conversation.conversation_no, message_no, None)
            yield f'data: {chunk_str}\n\n'
        else:
            raise v_e
    except Exception as e:
        logger.exception(e)
        save_message_fail(
            dto=dto,
            message_no=message_no,
            conversation=conversation,
            app_model=app_model,
            from_account=from_account,
            err_msg=str(e),
        )
        chunk = {
            "event": "error",
            "created_at": int(time.time()),
            "answer": settings.LLM_NETWORK_ERROR_ANSWER,
            "conversation_id": conversation.conversation_no,
            "message_id": message_no
        }
        chunk_str = json.dumps(chunk, ensure_ascii=False)
        yield f'data: {chunk_str}\n\n'


def coze_chat_block(app_model: App, dto: CozeChatMessageDto, from_account: Account, coze_client=None):
    # custom coze client
    coze_client = coze_client or coze_api_client

    if dto.conversation_id:
        conversation = Conversation.objects.filter(conversation_no=dto.conversation_id).first()
    else:
        conversation_add_dto = CozeConversationAdd(app_id=app_model.app_no)
        conversation = CozeGenerateService.add_conversation(conversation_add_dto, from_account, coze_client)

    bot_id = app_model.app_model_config.third_app_key
    answer = ''
    message_no = None
    user_id = dto.userinfo.get('user_id', '') if dto.userinfo else ''
    if not user_id:
        # TODO 随机生成user_id
        user_id = 'test_user_id'

    message_objs = []
    if dto.query:
        message_objs.append(MessageObjectString(
            type=MessageObjectStringType.TEXT,
            text=dto.query
        ))
    if dto.image:
        message_objs.append(MessageObjectString(
            type=MessageObjectStringType.IMAGE,
            file_url=dto.image
        ))

    try:
        for event in coze_client.chat.stream(
                bot_id=bot_id,
                user_id=user_id,
                additional_messages=[
                    CozeMessage.build_user_question_objects(message_objs),
                ],
                conversation_id=dto.conversation_id,
        ):

            if event.event == ChatEventType.CONVERSATION_MESSAGE_DELTA:
                if not message_no:
                    message_no = event.message.id
                if event.message.content:
                    answer += event.message.content
            if event.event in (ChatEventType.CONVERSATION_CHAT_FAILED, ChatEventType.ERROR):
                try:
                    err_msg = json.dumps(event.chat.last_error, ensure_ascii=False)
                except:
                    err_msg = '对话失败'

                save_message_fail(
                    dto=dto,
                    message_no=message_no,
                    conversation=conversation,
                    app_model=app_model,
                    from_account=from_account,
                    err_msg=err_msg,
                )
                raise Exception(err_msg)
            elif event.event == ChatEventType.CONVERSATION_MESSAGE_COMPLETED:
                if event.message.type == 'answer':
                    answer = event.message.content
                elif event.message.type == 'verbose':
                    completed_content = json.loads(event.message.content)
                    logger.debug(f'verbose.content={completed_content}')
            elif event.event == ChatEventType.CONVERSATION_CHAT_COMPLETED:
                try:
                    usage = event.chat.usage
                except:
                    usage = None

                save_message_success(
                    dto=dto,
                    message_no=message_no,
                    conversation=conversation,
                    app_model=app_model,
                    from_account=from_account,
                    answer=answer,
                    usage=usage
                )
                return answer
    except ValidationError as v_e:
        # 特殊处理cozepy处理tool工具
        # if "input_value='reply_message'" in v_e.
        v_e_log = v_e.errors()
        if v_e_log[0].get('type') == 'enum' and v_e_log[0].get('input') == 'reply_message':
            # 保存message
            save_message_success(
                dto=dto,
                message_no=message_no,
                conversation=conversation,
                app_model=app_model,
                from_account=from_account,
                answer=answer,
                usage=None
            )
            return answer
        else:
            raise v_e
    except Exception as e:
        logger.exception(e)
        save_message_fail(
            dto=dto,
            message_no=message_no,
            conversation=conversation,
            app_model=app_model,
            from_account=from_account,
            err_msg=str(e),
        )
        raise Exception(str(e))


class CozeGenerateService:

    @classmethod
    def add_conversation(cls, dto: CozeConversationAdd, from_account: Account, coze_client=None) -> Conversation:
        coze_client = coze_client or coze_api_client

        app_model: App = App.objects.filter(is_deleted=False, app_no=dto.app_id).first()
        if not app_model:
            raise AppNotFoundError()

        bot_id = app_model.app_model_config.third_app_key
        coze_conversation = coze_client.conversations.create(bot_id=bot_id)
        return Conversation.objects.create(
            conversation_no=coze_conversation.id,
            app=app_model,
            app_model_config=app_model.app_model_config,
            name='New conversation',
            from_account=from_account,
            from_biz_id=dto.biz_id,
        )

    @classmethod
    def generate(
            cls,
            dto: CozeChatMessageDto,
            from_account: Account,
    ) -> Generator | dict:
        app_model: App = App.objects.filter(is_deleted=False, app_no=dto.app_id).first()
        if not app_model:
            raise AppNotFoundError()

        if app_model.mode == AppMode.CHAT.value:
            if dto.stream:
                return coze_chat_stream(
                    app_model=app_model,
                    dto=dto,
                    from_account=from_account
                )
            else:
                answer = coze_chat_block(
                    app_model=app_model,
                    dto=dto,
                    from_account=from_account
                )
                return {'answer': answer}
        else:
            raise ValueError(f'Invalid app mode {app_model.mode}')

    @classmethod
    def chat_cancel(cls, message_no: str, user: Account):
        # set cache
        ChatCancelCachedHelper.set_stop_msg_cache(message_no)
