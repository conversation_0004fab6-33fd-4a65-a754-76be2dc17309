# Generated by Django 4.2 on 2025-05-21 13:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0054_merge_20250513_1509'),
    ]

    operations = [
        migrations.CreateModel(
            name='AssistantRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('system_prompt', models.TextField(help_text='用户自定义的提示词内容', verbose_name='系统提示词')),
                ('user_input', models.TextField(help_text='用户提交的问题内容', verbose_name='用户输入')),
                ('model_response', models.TextField(help_text='大模型返回的完整答案', verbose_name='模型响应')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('subject_id', models.Char<PERSON>ield(max_length=50, verbose_name='学科ID')),
            ],
            options={
                'verbose_name': '助手请求记录',
                'verbose_name_plural': '助手请求记录',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SubjectPrompt',
            fields=[
                ('subject_id', models.CharField(choices=[('math', '数学'), ('law', '法硕'), ('english', '英语'), ('english_translation', '英语翻硕'), ('Psychology', '心理学'), ('Mechanical Engineering', '机械工程'), ('Electrical Engineering', '电气工程'), ('Computer Science', '计算机'), ('education', '教育'), ('politics', '政治'), ('P.E', '体育'), ('finance', '金融'), ('Nursing Comprehensive 308', '308护理综合'), ('The Management Comprehensive Examination 199', '199管理类联考'), ('art', '艺术'), ('Comprehensive Examination of Western Medicine 306', '306西医综合'), ('other', '其他')], max_length=50, primary_key=True, serialize=False, verbose_name='学科ID')),
                ('name', models.CharField(editable=False, max_length=100, verbose_name='学科名称')),
                ('prompt', models.TextField(help_text='该学科的默认提示词', verbose_name='学科提示词')),
                ('real_prompt', models.TextField(help_text='该学科的默认提示词', null=True, verbose_name='正式学科提示词')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='最后更新时间')),
            ],
            options={
                'verbose_name': '学科提示词配置',
                'verbose_name_plural': '学科提示词配置',
                'db_table': 'ai_subject_prompt',
                'ordering': ['-updated_at'],
            },
        ),
    ]
