import uuid

from app.core.rag.cleaner.clean_processor import CleanProcessor
from app.core.rag.extractor.entities.extract_setting import ExtractSetting
from app.core.rag.extractor.extract_processor import ExtractProcessor
from app.core.rag.index_processor.index_processor_base import BaseIndexProcessor
from app.core.rag.knowledge_service import KnowledgeService
from app.core.rag.models.document import Document
from app.core.rag.vdb.vector_factory import Vector
from app.models import Dataset, Knowledge, DatasetDocument
from app.utils import generate_text_hash


class KnowledgeIndexProcessor(BaseIndexProcessor):

    def extract(self, extract_setting: ExtractSetting, **kwargs) -> list[Document]:
        text_docs = ExtractProcessor.extract(extract_setting)
        return text_docs

    def transform_special(self, documents: list[Document], **kwargs) -> list[Document]:
        pass

    def transform(self, documents: list[Document], **kwargs) -> list[Document]:
        dataset_document: DatasetDocument = kwargs.get('dataset_document')
        knowledge_list = KnowledgeService.get_knowledge_from_document(documents)

        knowledge_objs = []
        documents = []
        for kg in knowledge_list:
            doc_id = str(uuid.uuid4())
            knowledge_objs.append(Knowledge(
                dataset=dataset_document.dataset,
                dataset_document=dataset_document,
                name=kg['name'],
                definition=kg['definition'],
                index_node_id=doc_id,
            ))
            documents.append(Document(
                page_content=kg['name'],
                metadata={
                    'document_no': dataset_document.document_no,
                    'doc_id': doc_id,
                    'doc_hash': generate_text_hash(kg['name']),
                }
            ))
        Knowledge.objects.bulk_create(knowledge_objs)
        return documents

    def load(self, dataset: Dataset, documents: list[Document]):
        # TODO 知识点搜索暂时不写入向量库
        return
        # vector = Vector(dataset)
        # vector.create(documents)

    def clean(self, dataset: Dataset, node_ids: list[str] | None):
        vector = Vector(dataset)
        if node_ids:
            vector.delete_by_doc_ids(node_ids)
        else:
            vector.delete()
