# Generated by Django 4.2 on 2024-12-24 01:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0016_message_is_answer_token_exceed_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UnifiedExaminationQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_num', models.IntegerField(verbose_name='题目编号')),
                ('library', models.IntegerField(choices=[('1', 'Cs408')], default='1', verbose_name='题库类型')),
                ('year', models.CharField(default='', max_length=4, verbose_name='年份')),
                ('lost_result', models.BooleanField(default=False, verbose_name='是否确实数据')),
                ('has_llm_analysis', models.BooleanField(default=False, verbose_name='是否使用llm提取解析')),
                ('title', models.TextField(null=True, verbose_name='题干')),
                ('answer_body', models.TextField(null=True, verbose_name='answer_body')),
                ('choice_body', models.TextField(null=True, verbose_name='choice_body')),
                ('analysis', models.TextField(null=True, verbose_name='解析')),
                ('content', models.JSONField(null=True, verbose_name='题目内容')),
                ('llm_analysis', models.TextField(null=True, verbose_name='大模型解析结果')),
            ],
            options={
                'verbose_name': '统考真题',
                'verbose_name_plural': '统考真题',
                'db_table': 'unified_examination_question',
            },
        ),
    ]
