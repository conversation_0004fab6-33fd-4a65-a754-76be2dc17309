import datetime

from app.api.paper_answer_detail_api import paper_answer_detail
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import  CompletionAppGenerateEntity
from app.core.entities.queue_entities import QueueLLMChunkEvent, QueueStopEvent
from app.core.features.learning_report_generator import Learning_Report_Generator_Feature
from app.core.model_runtime.entities.llm_entities import LLMResult, LLMUsage, LLMResultChunk, LLMWorkflowChunk
from app.core.prompt.entities import AssistantPromptMessage
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError,  GrammarMessageNotQuestionError
from app.models import Message, QuestionKnowledge, CourseSectionKnowledge
from app.services.main_subject.answer_service import AnswerService


class LearningReportGeneratorWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

        self.report_prompt = self.inputs.get('learning_report_generator')
        self.subject = self.inputs.get('subject')
        self.not_question_err = ' 请输入id或知识库内容！'
        self.answer_id = self.inputs.get('answer_id')
        self.generate_mode = self.inputs.get('generate_mode')

    def run(self):
        answer_ids = self.answer_id.split(',')
        generate_mode = self.generate_mode
        if not answer_ids:
            raise ParameterError(detail=self.not_question_err)

        all_student_performance_tables = []
        for answer_id in answer_ids:
            answer_id = answer_id.strip()
            if not answer_id:
                continue

            # 调用 paper_answer_detail接口 获取试卷内容
            exam_paper_content = paper_answer_detail(answer_id)
            # 提取所需的信息
            paper_score = exam_paper_content.get('data', {}).get('paper_score')
            answer_score = exam_paper_content.get('data', {}).get('answer_score')
            questions = exam_paper_content.get('data', {}).get('questions', [])

            for question in questions:
                question_id = question.get('q_id')
                # 查询数据库中 question_id 等于当前 question_id 的记录

                question_knowledge_records = QuestionKnowledge.objects.filter(question_id=question_id)
                # 提取 knowledge_list
                knowledge_points = [record.knowledge_list for record in question_knowledge_records]

                # 提取所有的 knowledge
                knowledge_list = [point['knowledge'] for sublist in knowledge_points for point in sublist]
                question['knowledge'] = knowledge_list

                # 提取正确答案和解析
                question_detail = question.get('question', {})
                right_answer = question_detail.get('right_answer', '')
                analysis = question_detail.get('analysis', '')

                # 拼接正确答案和解析
                correct_answer_and_analysis = f"正确答案: {right_answer}\n解析: {analysis}"
                question['correct_answer_and_analysis'] = correct_answer_and_analysis

                # 提取学生的答案和子题答案
                user_answer_detail = question.get('user_answer', {})
                user_answer = user_answer_detail.get('user_answer', '')

                user_sub_answers = question.get('user_sub_answers', [])

                if not (user_answer or user_sub_answers):
                    user_answer_detail['user_answer_status'] = 2

                user_sub_answers_str = "\n".join(
                    [f"子题 {i + 1}: {sub_answer}" for i, sub_answer in enumerate(user_sub_answers)])

                # 拼接学生的答案
                user_answer_str = f"学生答案: {user_answer}\n{user_sub_answers_str}"
                question['user_answer_str'] = user_answer_str
            # 生成学习情况表
            student_performance_table = self._student_performance_table(exam_paper_content, answer_id, paper_score,
                                                                        answer_score )
            all_student_performance_tables.append(student_performance_table)
        math_video_knowledge_list = []
        if generate_mode == "paper_with_record":
            # 获取所学视频知识点
            main_subject = self.subject

            # 已学视频课节id
            learnt_course_section_ids = [
                "10153101", "10153301", "10153501", "10153701", "10154001",
                "10154201", "10154401", "10154501", "10154901", "10155401",
                "13566401", "13566901", "13567001", "13567201"
            ]

            course_section_knowledge_records = CourseSectionKnowledge.objects.filter(
                main_subject=main_subject,
                course_section_id__in=learnt_course_section_ids
            )

            # 提取 math_video_knowledge_list
            math_video_knowledge_list = [record.knowledge_list for record in course_section_knowledge_records]

        # 拼接所有学生的学习情况表
        combined_student_performance_table = "\n\n".join(all_student_performance_tables)

        # 生成最终报告
        result = self._report_generator(combined_student_performance_table, math_video_knowledge_list)

        self._publish_workflow_end_message()
        return result

    # 试卷知识点和学生答题情况整理成表格
    def _student_performance_table(self, exam_paper_content: dict, answer_id: str, paper_score: int,
                                   answer_score: int) -> str:
        # 提取 questions 列表
        questions = exam_paper_content.get('data', {}).get('questions', [])

        # TODO user_id固定
        user_id = '2VspVAzUwtNhm29fgND7KE'
        if user_id:
            user_detail = AnswerService.get_user_detail(user_id)
        else:
            user_detail = {}
        nickname = user_detail.get('nickname', '学生')

        paper_name = exam_paper_content.get('data', {}).get('paper_name')
        paper_score = exam_paper_content.get('data', {}).get('paper_score')
        answer_score = exam_paper_content.get('data', {}).get('answer_score')
        start_time = exam_paper_content.get('data', {}).get('start_time')
        end_time = exam_paper_content.get('data', {}).get('end_time')

        # 初始化表格内容
        table_content = f"## 试卷名称: {paper_name}\n\n"
        table_content += f"## 用户昵称: {nickname}\n\n"
        table_content += f"## 答卷日期: {start_time}\n\n"

        try:
            start_time_date = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            end_time_date = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            duration = int((end_time_date - start_time_date).total_seconds() / 60)
            table_content += f"## 答卷用时: {duration}分钟 \n\n"
        except:
            pass

        table_content += f"| 试卷总分: {paper_score} | 学生得分: {answer_score} | 答卷时间: 48分钟 |\n"
        table_content += "|------------|------------|\n"
        table_content += "| ID | 答题情况 | 考察知识点 | 题目难度等级 | 正确答案 | 学生答案 |\n"
        table_content += "|----|----------|------------|--------------|------------|------------|\n"

        # 遍历每个问题并生成表格行
        for idx, question in enumerate(questions, start=1):
            knowledge = question.get('knowledge', [])
            user_answer_status = question.get('user_answer', {}).get('user_answer_status', None)
            difficulty = question.get('difficulty', None)
            correct_answer_and_analysis = question.get('correct_answer_and_analysis', '')
            user_answer_str = question.get('user_answer_str', '')

            # 根据 user_answer_status 设置答题情况
            if user_answer_status == 0:
                answer_status = "答错"
            elif user_answer_status == 1:
                answer_status = "答对"
            elif user_answer_status == 2:
                answer_status = "未作答"
            else:
                answer_status = "未批阅"

            # 将知识点列表转换为字符串
            knowledge_str = ", ".join(knowledge)

            # 添加表格行
            table_content += f"| {idx} | {answer_status} | {knowledge_str} | {difficulty} | {correct_answer_and_analysis} | {user_answer_str} |\n"

        return table_content

    def _report_generator(self, student_performance_table: str, math_video_knowledge_list: list) -> LLMResult:
        generate_mode = self.generate_mode
        if generate_mode == 'only_paper':
            self.learning_report_generator_feature = Learning_Report_Generator_Feature(
                app_no='learning_report_generator_single')
            query = f"以下是学生的答题情况：\n{student_performance_table}"
            result = self.learning_report_generator_feature.run(
                self.application_generate_entity,
                query=query,
                stream=True,
                prompt_content=self.report_prompt
            )

            tracing_info = {
                'tracing_type': 'learning_report_generator_single',
                'query': query
            }
            return self._handle_node_generate_response(result, tracing_info=tracing_info)

        else:
            self.learning_report_generator_feature =  Learning_Report_Generator_Feature(app_no='learning_report_generator_mul')
            query = f"以下是学生的答题情况：\n{student_performance_table} \n 以下是学生已学知识点列表：\n{math_video_knowledge_list} "
            result = self.learning_report_generator_feature.run(
                self.application_generate_entity,
                query = query,
                stream=True,
                prompt_content= self.report_prompt
            )

            tracing_info = {
                'tracing_type': 'learning_report_generator_mul',
                'query': query
            }
            return self._handle_node_generate_response(result, tracing_info=tracing_info)