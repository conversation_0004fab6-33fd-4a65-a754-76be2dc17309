--
-- Add field support_tools to appmodelconfig
--
ALTER TABLE `ai_app_model_config`
    ADD COLUMN `support_tools` json NULL;
--
-- Create model MessageToolCall
--
CREATE TABLE `ai_message_tool_call`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `tool_call_id`  varchar(100) NOT NULL,
    `name`          varchar(100) NOT NULL,
    `arguments`     varchar(255) NOT NULL,
    `content`       longtext NULL,
    `message_id`    bigint       NOT NULL
);
CREATE INDEX `ai_message_tool_call_add_time_5ab40631` ON `ai_message_tool_call` (`add_time`);
CREATE INDEX `ai_message_tool_call_message_id_b3e54ee7` ON `ai_message_tool_call` (`message_id`);