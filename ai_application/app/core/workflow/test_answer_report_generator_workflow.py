import datetime

from app.core.features.learning_report_generator import Learning_Report_Generator_Feature
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import QuestionKnowledge
from app.services.main_subject.answer_service import AnswerService


class TestAnswerReportGeneratorWorkflow(BaseWorkflow):

    def run(self):
        answer_id = self.application_generate_entity.query

        exam_paper_content = AnswerService.get_test_answer_detail(answer_id)

        # 提取所需的信息
        questions = exam_paper_content.get('questions', [])

        q_ids = [q['q_id'] for q in questions]
        question_knowledge_map = {}
        question_knowledge_records = QuestionKnowledge.objects.filter(question_id__in=q_ids)
        for r in question_knowledge_records:
            question_knowledge_map[r.question_id] = [r_s['knowledge'] for r_s in r.knowledge_list]

        for question in questions:
            question_id = question.get('q_id')

            knowledge_list = question_knowledge_map.get(question_id, [])

            question['knowledge'] = knowledge_list

            # 提取正确答案和解析
            question_detail = question.get('question', {})
            right_answer = question_detail.get('right_answer', '')
            analysis = question_detail.get('analysis', '')

            # 拼接正确答案和解析
            correct_answer_and_analysis = f"正确答案: {right_answer}\n解析: {analysis}"
            question['correct_answer_and_analysis'] = correct_answer_and_analysis

            # 提取学生的答案和子题答案
            user_answer_detail = question.get('user_answer', {})
            user_answer = user_answer_detail.get('user_answer', '')
            user_sub_answers = question.get('user_sub_answers', [])
            if not (user_answer or user_sub_answers):
                user_answer_detail['user_answer_status'] = 2

            user_sub_answers_str = "\n".join(
                [f"子题 {i + 1}: {sub_answer}" for i, sub_answer in enumerate(user_sub_answers)])

            # 拼接学生的答案
            user_answer_str = f"学生答案: {user_answer}\n{user_sub_answers_str}"
            question['user_answer_str'] = user_answer_str

        # 生成学习情况表
        student_performance_table = self._student_performance_table(questions, exam_paper_content)

        # 生成最终报告
        result = self._report_generator(student_performance_table)
        self._publish_workflow_end_message()
        return result

    # 试卷知识点和学生答题情况整理成表格
    def _student_performance_table(
            self,
            questions: list,
            exam_paper_content: dict,
    ) -> str:
        user_id = self.application_generate_entity.inputs.get('user_id')
        if user_id:
            user_detail = AnswerService.get_user_detail(user_id)
        else:
            user_detail = {}
        nickname = user_detail.get('nickname', '学生')

        paper_name = exam_paper_content.get('paper_name')
        paper_score = exam_paper_content.get('paper_score')
        answer_score = exam_paper_content.get('answer_score')
        start_time = exam_paper_content.get('start_time')
        end_time = exam_paper_content.get('end_time')

        # 初始化表格内容
        table_content = f"## 试卷名称: {paper_name}\n\n"
        table_content += f"## 用户昵称: {nickname}\n\n"
        table_content += f"## 答卷日期: {start_time}\n\n"

        try:
            start_time_date = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            end_time_date = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            duration = int((end_time_date - start_time_date).total_seconds() / 60)
            table_content += f"## 答卷用时: {duration}分钟 \n\n"
        except:
            pass

        table_content += f"| 试卷总分: {paper_score} | 学生得分: {answer_score} |\n"
        table_content += "|------------|------------|\n"
        table_content += "| ID | 答题情况 | 考察知识点 | 题目难度等级 | 正确答案 | 学生答案 |\n"
        table_content += "|----|----------|------------|--------------|------------|------------|\n"

        # 遍历每个问题并生成表格行
        for idx, question in enumerate(questions, start=1):
            knowledge = question.get('knowledge', [])
            user_answer_status = question.get('user_answer', {}).get('user_answer_status', None)
            difficulty = question.get('difficulty', None)
            correct_answer_and_analysis = question.get('correct_answer_and_analysis', '')
            user_answer_str = question.get('user_answer_str', '')

            # 根据 user_answer_status 设置答题情况
            if user_answer_status == 0:
                answer_status = "答错"
            elif user_answer_status == 1:
                answer_status = "答对"
            elif user_answer_status == 2:
                answer_status = "未作答"
            else:
                answer_status = "未批阅"

            # 将知识点列表转换为字符串
            knowledge_str = ", ".join(knowledge)

            # 添加表格行
            table_content += f"| {idx} | {answer_status} | {knowledge_str} | {difficulty} | {correct_answer_and_analysis} | {user_answer_str} |\n"

        return table_content

    def _report_generator(self, student_performance_table: str) -> LLMResult:
        feature = Learning_Report_Generator_Feature(app_no='learning_report_generator_single')
        query = student_performance_table

        result = feature.run(
            self.application_generate_entity,
            query=query,
            stream=True
        )

        tracing_info = {
            'tracing_type': 'learning_report_generator_single',
            'query': query
        }
        return self._handle_node_generate_response(result, tracing_info=tracing_info)
