from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.models import PromptTemplate


class MathVideoJudgeFeature:
    # 判断视频内容是否是导学相关，无重点讲解知识点

    def __init__(self, app_no):
        self.prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no=app_no).first()
        if not self.prompt_template:
            raise Exception('prompt template not found')

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            math_video_content: str
    ) -> LLMResult:
        # 返回是否是无关视频内容
        user_inputs = application_generate_entity.inputs.copy()

        prompt_template = self.prompt_template
        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=''
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=math_video_content,
            stream=False
        )
