import logging

from django.conf import settings

from app.constants.app import AppMode
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.apps.base_app_runner import AppRunner
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.callback_handler.index_tool_callback_handler import DatasetIndex<PERSON>oolCallbackHandler
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.features.problem_solving.judge_is_question import JudgeIsQuestionFeature
from app.core.model_manager import ModelInstance
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.prompt.conversation_buffer_memory import ConversationBufferMemory
from app.core.prompt.entities import PromptParser
from app.core.prompt.prompt_templates.context_query_prompt import CONTEXT_QUERY_PROMPT
from app.core.prompt.utils.prompt_message_util import PromptMessageUtil
from app.core.rag.retrieval.dataset_retrieval import <PERSON><PERSON><PERSON><PERSON><PERSON>val
from app.core.workflow.chat_code_workflow import Chat<PERSON><PERSON><PERSON>orkflow
from app.core.workflow.chat_question_workflow import ChatQuestionWorkflow
from app.core.workflow.math_problem_solving_workflow import MathProblemSolvingWorkflow
from app.core.workflow.waikan_question_second_report import ChatWaikanAgainWorkflow
from app.errors import ContainSensitiveError
from app.models import Conversation, Message, Dataset, RagMessage

logger = logging.getLogger(__name__)


class ChatAppRunner(AppRunner):

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            queue_manager: AppQueueManager,
            conversation: Conversation,
            message: Message,
            last_message: Message | None = None,
    ) -> None:
        # 统一处理敏感词
        if message.is_sensitive:
            raise ContainSensitiveError(detail=message.answer)

        if application_generate_entity.dataset_id:
            self.run_rag(
                application_generate_entity=application_generate_entity,
                queue_manager=queue_manager,
                conversation=conversation,
                message=message,
            )
        else:
            # 处理前优先处理token上限
            self.check_conversation_token_exceed(application_generate_entity, conversation)

            # 特殊处理解题助手
            if application_generate_entity.message_type == Message.MessageType.question:
                # 解题助手需要前置判断是否是一个问题
                is_question = JudgeIsQuestionFeature(
                    app_no='judge_is_408_question'
                ).run(
                    application_generate_entity=application_generate_entity,
                    message=message
                )
                if is_question:
                    return ChatQuestionWorkflow(
                        application_generate_entity=application_generate_entity,
                        message=message,
                        queue_manager=queue_manager,
                    ).run()
                else:
                    # TODO 临时方案
                    message.message_type = 'normal'
                    message.save(update_fields=['message_type'])

            elif application_generate_entity.message_type == Message.MessageType.code:
                return ChatCodeWorkflow(
                    application_generate_entity=application_generate_entity,
                    message=message,
                    queue_manager=queue_manager,
                ).run()
            # elif application_generate_entity.message_type == Message.MessageType.waikan:
            #     return ChatWaikanWorkflow(
            #         application_generate_entity=application_generate_entity,
            #         message=message,
            #         queue_manager=queue_manager,
            #     ).run()
            elif application_generate_entity.message_type == Message.MessageType.waikan_again:
                return ChatWaikanAgainWorkflow(
                    application_generate_entity=application_generate_entity,
                    message=message,
                    queue_manager=queue_manager,
                ).run()
            elif application_generate_entity.message_type == Message.MessageType.math_question:
                # 解题助手需要前置判断是否是一个问题
                is_question = JudgeIsQuestionFeature(
                    app_no='judge_is_math_question'
                ).run(
                    application_generate_entity=application_generate_entity,
                    message=message
                )
                if is_question:
                    return MathProblemSolvingWorkflow(
                        application_generate_entity=application_generate_entity,
                        message=message,
                        queue_manager=queue_manager,
                    ).run()
                else:
                    # TODO 临时方案
                    message.message_type = 'normal'
                    message.save(update_fields=['message_type'])

            self.run_direct(
                application_generate_entity=application_generate_entity,
                queue_manager=queue_manager,
                conversation=conversation,
                last_message=last_message
            )

    def run_direct(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            queue_manager: AppQueueManager,
            conversation: Conversation,
            last_message: Message | None = None,
    ):
        pre_prompt = application_generate_entity.pre_prompt
        query = application_generate_entity.query
        context_limit = application_generate_entity.context_limit

        memory = None
        if conversation and conversation.id:
            memory = ConversationBufferMemory(
                conversation=conversation,
                last_message=last_message,
                context_limit=context_limit
            )

        prompt_messages = self.organize_prompt_messages(
            app_mode=AppMode.CHAT,
            query=query,
            pre_prompt=pre_prompt,
            memory=memory
        )

        model_instance = ModelInstance(
            provider_model_bundle=application_generate_entity.model_conf.provider_model_bundle,
            model=application_generate_entity.model_conf.model
        )

        support_tools = application_generate_entity.support_tools
        if 'tool-call' not in application_generate_entity.model_conf.model_schema.features:
            support_tools = []

        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            model_parameters=application_generate_entity.model_conf.model_params,
            tools=support_tools,
            stream=application_generate_entity.stream,
        )

        self._handle_invoke_result(
            invoke_result=invoke_result,
            queue_manager=queue_manager,
            stream=application_generate_entity.stream,
        )

    def run_rag(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            queue_manager: AppQueueManager,
            conversation: Conversation,
            message: Message,
    ):
        tracing_log = MessageTracingLog(message)

        pre_prompt = application_generate_entity.pre_prompt
        query = application_generate_entity.query

        # 判断提问是否正常
        if not self._determine_query(application_generate_entity, query, tracing_log):
            self._run_rag_direct(
                application_generate_entity=application_generate_entity,
                conversation=conversation,
                queue_manager=queue_manager,
            )
            return

        # 本地知识库问答忽略历史对话
        # get rewrite query
        query = self._rewrite_query(
            application_generate_entity=application_generate_entity,
            message=message,
            query=query,
            tracing_log=tracing_log
        )

        # get context from datasets
        hit_callback = DatasetIndexToolCallbackHandler(
            message=message,
        )
        dataset_retrieval = DatasetRetrieval(application_generate_entity)

        dataset: Dataset = Dataset.objects.filter(is_deleted=False, id=application_generate_entity.dataset_id).first()

        context = None
        if dataset:
            query_documents = []
            if application_generate_entity.document_nos:
                # 只需要获取已经完成解析的文档
                query_documents = dataset.datasetdocument_set.filter(
                    is_deleted=False,
                    document_no__in=application_generate_entity.document_nos,
                    indexing_status='completed'
                )

            context = dataset_retrieval.retrieve(
                query, dataset, query_documents, hit_callback, tracing_log=tracing_log
            )

        if not context:
            self._run_rag_direct(
                application_generate_entity=application_generate_entity,
                conversation=conversation,
                queue_manager=queue_manager,
            )
            return

        # 包含上下文查询
        rag_invoke_result = self._get_rag_result(
            application_generate_entity=application_generate_entity,
            pre_prompt=pre_prompt,
            context=context,
            query=query,
            tracing_log=tracing_log,
        )

        # TODO 特殊处理回复内容
        if '根据已知信息无法回答该问题' in rag_invoke_result.message.content:
            rag_answer = '根据已知信息无法回答该问题'
        else:
            rag_answer = rag_invoke_result.message.content

        # 处理评分
        score = self._get_context_answer_score(
            application_generate_entity,
            context=context,
            question=query,
            answer=rag_answer,
            tracing_log=tracing_log
        )

        self._save_rag_message(message, rag_invoke_result, score)

        if score > settings.RAG_ANSWER_SCORE_THRESHOLD:
            self._publish_end_result(rag_invoke_result, queue_manager, with_chunk=True)
        else:
            self._run_rag_direct(
                application_generate_entity=application_generate_entity,
                conversation=conversation,
                queue_manager=queue_manager,
            )

    def _run_rag_direct(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            conversation: Conversation,
            queue_manager: AppQueueManager,
    ):
        pre_prompt = application_generate_entity.pre_prompt
        query = application_generate_entity.query

        model_instance = ModelInstance(
            provider_model_bundle=application_generate_entity.model_conf.provider_model_bundle,
            model=application_generate_entity.model_conf.model
        )

        prompt_messages = self.organize_prompt_messages(
            app_mode=AppMode.CHAT,
            query=query,
            pre_prompt=pre_prompt,
        )

        if conversation.only_use_dataset:
            self._handle_invoke_full_result(
                model=application_generate_entity.model_conf.model,
                prompt_messages=prompt_messages,
                answer=settings.RAG_DEFAULT_ANSWER,
                queue_manager=queue_manager
            )
            return

        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            model_parameters=application_generate_entity.model_conf.model_params,
            stream=True
        )

        self._handle_invoke_result(
            invoke_result=invoke_result,
            queue_manager=queue_manager,
            stream=application_generate_entity.stream,
        )

    def _get_rag_result(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            pre_prompt: str,
            context: str,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        # TODO 兼容旧版本的接口，10月版本后替换
        if '{{context}}' in pre_prompt:
            real_query = pre_prompt.replace('{{context}}', context).replace('{{query}}', query)
        else:
            prompt_parser = PromptParser(
                prompt_template=CONTEXT_QUERY_PROMPT,
                input_variables={
                    'pre_prompt': pre_prompt,
                    'context': context,
                    'question': query,
                },
                output_parser=None,
            )
            real_query = prompt_parser.get_prompt()

        prompt_messages = self.organize_prompt_messages(
            app_mode=AppMode.CHAT,
            query=real_query,
            pre_prompt='',
        )

        self.get_pre_calculate_rest_tokens(application_generate_entity.model_conf, prompt_messages)

        model_instance = ModelInstance(
            provider_model_bundle=application_generate_entity.model_conf.provider_model_bundle,
            model=application_generate_entity.model_conf.model
        )

        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            model_parameters=application_generate_entity.model_conf.model_params,
            stream=False
        )

        if tracing_log:
            log_content = PromptMessageUtil.prompt_messages_to_prompt_for_saving(invoke_result.prompt_messages)
            tracing_log.add_tracing(
                tracing_type='context_answer',
                query=query,
                content=log_content,
                answer=invoke_result.message.content,
                message_tokens=invoke_result.usage.prompt_tokens,
                answer_tokens=invoke_result.usage.completion_tokens,
                total_tokens=invoke_result.usage.total_tokens,
                latency=invoke_result.usage.latency,
            )

        return invoke_result

    def _save_rag_message(self, message: Message, llm_result: LLMResult, score: int):
        rag_message: RagMessage = message.rag_message
        if rag_message:
            rag_message.content = PromptMessageUtil.prompt_messages_to_prompt_for_saving(
                llm_result.prompt_messages)
            rag_message.answer = llm_result.message.content
            rag_message.message_tokens = llm_result.usage.prompt_tokens
            rag_message.answer_tokens = llm_result.usage.completion_tokens
            rag_message.latency = llm_result.usage.latency
            rag_message.model_score = score
            rag_message.save()
