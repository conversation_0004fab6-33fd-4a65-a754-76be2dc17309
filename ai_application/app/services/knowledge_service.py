import uuid
from pathlib import Path
from typing import Generator

from django.db import transaction

from app.api.dto import KnowledgeAddDto, KnowledgeEditDto, ChatMessageDto, KnowledgeSearchDto
from app.core.indexing_runner import IndexingRunner
from app.core.rag.index_processor.constant.index_type import IndexType
from app.core.rag.index_processor.index_processor_factory import IndexProcessorFactory
from app.core.rag.models.document import Document
from app.errors import DatasetDocumentNotFoundError
from app.models import Account, DatasetDocument, Knowledge, DocumentSegment, InvokeFrom
from app.services.app_generate_service import AppGenerateService
from app.services.dataset_service import DocumentService
from app.utils import generate_text_hash


class KnowledgeService:

    @classmethod
    def generate_document_knowledge(cls, document_no: str) -> list:
        dataset_document: DatasetDocument = DatasetDocument.objects.filter(document_no=document_no).first()
        if not dataset_document:
            raise DatasetDocumentNotFoundError()
        dataset = dataset_document.dataset

        # 需要先删除之前的旧知识点
        del_document_nos = list(DatasetDocument.objects.filter(
            is_deleted=False,
            index_type=IndexType.KNOWLEDGE_INDEX.value,
            original_knowledge_document_no=document_no
        ).values_list('document_no', flat=True))
        DocumentService.delete_document(dataset, del_document_nos)

        kg_document = DocumentService.build_document(
            dataset=dataset,
            name=dataset_document.name,
            data_source_type=dataset_document.data_source_type,
            document_language=dataset_document.doc_language,
            data_source_info=dataset_document.data_source_info,
        )
        kg_document.index_type = IndexType.KNOWLEDGE_INDEX.value
        kg_document.original_knowledge_document_no = dataset_document.document_no
        kg_document.save()

        file_url = dataset_document.data_source_info.get('url')
        suffix = Path(file_url).suffix
        file_extension = suffix.lower()
        # 目前暂时只解析特定的markdown表格
        if file_extension not in ['.md', '.markdown']:
            return []

        IndexingRunner().run(dataset, [kg_document])

        # 返回相关知识点
        qs = kg_document.knowledge_set.filter(is_deleted=False).order_by('id')
        return [{'id': str(i.id), 'name': i.name, 'definition': i.definition} for i in qs]

    @classmethod
    def add_knowledge(cls, dto: KnowledgeAddDto, account: Account) -> Knowledge:
        dataset_document: DatasetDocument = DatasetDocument.objects.filter(document_no=dto.document_no).first()
        if not dataset_document:
            raise DatasetDocumentNotFoundError()

        document = Document(
            page_content=dto.name,
            metadata={
                'document_no': dataset_document.document_no,
                'doc_id': str(uuid.uuid4()),
                'doc_hash': generate_text_hash(dto.name),
            }
        )

        max_position_segment: DocumentSegment = dataset_document.documentsegment_set.all().order_by('-position').first()
        max_position = max_position_segment.position if max_position_segment else 0
        DocumentSegment.objects.create(
            dataset=dataset_document.dataset,
            dataset_document=dataset_document,
            index_node_id=document.metadata['doc_id'],
            index_node_hash=document.metadata['doc_hash'],
            position=max_position + 1,
            content=document.page_content,
            word_count=len(document.page_content),
        )

        index_processor = IndexProcessorFactory(IndexType.PARAGRAPH_INDEX.value).init_index_processor()
        index_processor.load_segment(dataset_document.dataset, [document])

        knowledge = Knowledge.objects.create(
            dataset=dataset_document.dataset,
            dataset_document=dataset_document,
            name=dto.name,
            definition=dto.definition,
            index_node_id=document.metadata['doc_id'],
        )
        return knowledge

    @classmethod
    def edit_knowledge(cls, knowledge: Knowledge, dto: KnowledgeEditDto):
        if knowledge.name != dto.name:
            dataset_document = knowledge.dataset_document

            index_node_id = knowledge.index_node_id
            document = Document(
                page_content=dto.name,
                metadata={
                    'document_no': dataset_document.document_no,
                    'doc_id': index_node_id,
                    'doc_hash': generate_text_hash(dto.name),
                }
            )

            # TODO修改索引
            segment: DocumentSegment = DocumentSegment.objects.filter(index_node_id=index_node_id).first()
            if not segment:
                max_position_segment: DocumentSegment = dataset_document.documentsegment_set.all().order_by(
                    '-position').first()
                max_position = max_position_segment.position if max_position_segment else 0
                DocumentSegment.objects.create(
                    dataset=dataset_document.dataset,
                    dataset_document=dataset_document,
                    index_node_id=document.metadata['doc_id'],
                    index_node_hash=document.metadata['doc_hash'],
                    position=max_position + 1,
                    content=document.page_content,
                    word_count=len(document.page_content),
                )
            else:
                segment.content = document.page_content
                segment.index_node_hash = document.metadata['doc_hash']
                segment.word_count = len(document.page_content)
                segment.save(update_fields=['content', 'index_node_hash', 'word_count'])

            index_processor = IndexProcessorFactory(dataset_document.index_type).init_index_processor()
            index_processor.clean(knowledge.dataset, [index_node_id])

            index_processor.load_segment(dataset_document.dataset, [document])

        knowledge.name = dto.name
        knowledge.definition = dto.definition
        knowledge.save(update_fields=['name', 'definition'])

    @classmethod
    def delete_knowledge(cls, knowledge: Knowledge):
        index_node_id = knowledge.index_node_id

        index_processor = IndexProcessorFactory(IndexType.PARAGRAPH_INDEX.value).init_index_processor()
        index_processor.clean(knowledge.dataset, [index_node_id])

        with transaction.atomic():
            DocumentSegment.objects.filter(
                is_deleted=False, index_node_id=index_node_id
            ).update(is_deleted=True)
            knowledge.is_deleted = True
            knowledge.save(update_fields=['is_deleted'])

    @classmethod
    def query_knowledge(
            cls,
            dto: KnowledgeSearchDto,
            account: Account,
            invoke_from: str = InvokeFrom.api.value
    ) -> Generator:
        is_debug = invoke_from == InvokeFrom.console.value

        dto = ChatMessageDto(
            app_id='knowledge_query',
            biz_id=dto.biz_id,
            userinfo=dto.userinfo,
            inputs={
                "course_id": dto.biz_id or dto.course_id,
                "document_no": dto.document_no,
                "search_mode": dto.search_mode,
                "search_type": dto.search_type,
                "split_prompt": dto.split_prompt,
                "local_prompt": dto.local_prompt,
                "llm_prompt": dto.llm_prompt,
                "deep_local_prompt": dto.deep_local_prompt,
                "deep_llm_prompt": dto.deep_llm_prompt,
                "deep_question_prompt": dto.deep_question_prompt,
                "deep_no_question_prompt": dto.deep_no_question_prompt,
                'is_debug': is_debug,
                'is_recommend': dto.is_recommend,
                'enable_recommend_video': dto.enable_recommend_video,
            },
            query=dto.query,
            stream=True,
        )
        return AppGenerateService.generate(dto, account, invoke_from=invoke_from)

    @classmethod
    def regenerate_knowledge(
            cls,
            dto: KnowledgeSearchDto,
            account: Account,
            invoke_from: str = InvokeFrom.api.value
    ) -> Generator:
        dto = ChatMessageDto(
            app_id='knowledge_query',
            biz_id=dto.biz_id,
            userinfo=dto.userinfo,
            inputs={
                "course_id": dto.biz_id or dto.course_id,
                "document_no": dto.document_no,
                "search_mode": dto.search_mode,
                "search_type": dto.search_type,
                "local_prompt": dto.local_prompt,
                "llm_prompt": dto.llm_prompt,
                "is_regenerate": True,
                "enable_recommend_video": dto.enable_recommend_video,
            },
            query=dto.query,
            stream=True,
        )
        return AppGenerateService.generate(dto, account, invoke_from=invoke_from)

    @classmethod
    def retry_query_knowledge(
            cls,
            message_id: str,
            account: Account,
            enable_recommend_video: bool = False,
            invoke_from: str = InvokeFrom.api.value
    ) -> Generator:
        dto = ChatMessageDto(
            app_id='knowledge_query',
            inputs={
                "original_message_id": message_id,
                "is_exception_retry": True,
                "enable_recommend_video": enable_recommend_video,
            },
            stream=True,
        )
        return AppGenerateService.generate(dto, account, invoke_from=invoke_from)
