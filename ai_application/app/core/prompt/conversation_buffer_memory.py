from app.constants.app import MessageStatus
from app.core.prompt.entities import PromptMessage, UserPromptMessage, AssistantPromptMessage
from app.models import Conversation, Message


class ConversationBufferMemory:
    def __init__(
            self,
            conversation: Conversation,
            last_message: Message | None = None,
            context_limit: int = 0,
    ) -> None:
        self.conversation = conversation
        self.last_message = last_message
        self.context_limit = context_limit

    def get_history_prompt_messages(
            self,
    ) -> list[PromptMessage]:
        message_qs = self.conversation.message_set.filter(
            is_deleted=False,
            status=MessageStatus.NORMAL.value,
            is_sensitive=False,
        ).order_by('-id')

        # 需保持同一个模型参数的上下文
        if self.conversation.conversation_hash:
            message_qs = message_qs.filter(conversation_hash=self.conversation.conversation_hash)

        if self.last_message:
            message_qs = message_qs.filter(id__lt=self.last_message.id)

        if self.context_limit > 0:
            message_qs = message_qs[:self.context_limit]

        message_qs = message_qs.values('query', 'answer')
        messages = list(reversed(message_qs))

        prompt_messages = []
        for message in messages:
            # 没有回答的消息忽略
            if not message['answer']:
                continue

            prompt_messages.append(UserPromptMessage(content=message['query']))
            prompt_messages.append(AssistantPromptMessage(content=message['answer']))

        return prompt_messages
