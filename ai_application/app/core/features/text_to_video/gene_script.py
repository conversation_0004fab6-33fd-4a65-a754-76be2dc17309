import json
from django.conf import settings
from app.core.utils.latex_utils import replace_latex
from openai import OpenAI

def gene_sc(report_text):
    default_prompt = """
    # 角色
    你是一位专业且经验丰富的ppt内容策划师，擅长将数据结构知识点解析转化为结构清晰、画面丰富、重点突出的教学脚本。你善于像制作PPT一样，把每个知识点细化成适合做成ppt的"文案+讲解"对应内容，突出节奏感、重点明确。

    ## 输入任务
    你将收到一段结构化的数据结构知识点解析文本，内容包括定义介绍、前置知识点、术语解释、应用场景、总结概括等模块，原始输入为：{knowledge_analysis}

    ## 输出目标
    将解析内容拆解为**一组ppt文案+口语化讲解脚本序列**，总时长两分钟左右，结构清晰，画面数量控制在6-10张之间。遵循如下结构：

    **知识点定义（1张）**：根据原文本用几句话介绍该知识点的课程名称及定义  
    **前置知识点（1张）**：介绍一下前置知识点内容。  
    **知识点讲解（2-5张）**：每个核心知识点使用能让基础较差的学生理解的方式生成详细的文案和讲解  
    **举例说明（每例1张）**：对应每个知识点提供1个具体案例或类比说明，突出与日常生活的连接  
    **总结概括（1张）**：总结该知识点的内容。

    ## 输出结构（json）
    内容为  
    id: 1  
    title: ppt的文案标题  
    text: 能够用于制作ppt的文案正文，每页的内容量需能用一页ppt容纳  
    audio: 通俗、口语化、自然流畅的讲解知识点

    ## title格式
    前置知识点ppt的title为前置知识点
    知识点讲解ppt的title为知识点讲解-xxx(这部分补充核心知识点）
    举例说明ppt的title为举例说明-xxx(这部分补充具体案例或类比说明）
    总结概括ppt的title为总结概括

    ## 特别说明
    - 每张ppt的内容要适量，控制在三至五句话；
    - ppt文案要尽可能详细具体，能够让基础较差的学生理解对应知识点；
    - 具体案例可以使用例题的形式给出一步一步的解题步骤；
    - 文案中可以有行内公式和块内公式，但是必须要用latex的格式，并且每页最多只能有三个公式；
    - 旁白内容必须简洁、生动、第一人称或教学口吻，不能出现括号；
    - 旁白内容要前后连贯，可以使用连接词来过渡每个脚本，不要每次切换ppt都重复知识点名；
    - 旁白内容要完整，不要省略或中断；
    - 确保画面与内容紧密贴合，可视化比喻和案例；
    - 总ppt数量控制在6-10张之间；
    - 总旁白内容控制在90秒至150秒左右。

    ## 限制
    - 仅围绕数据结构知识点解析→ppt文案+口语讲解脚本构建；
    - 输出内容必须严格按照上述结构和格式进行组织。
    """
    
    # 使用原生 OpenAI 客户端
    client = OpenAI(
        api_key=settings.DOUBAO_API_KEY,
        base_url="https://ark.cn-beijing.volces.com/api/v3",
    )
    
    # 格式化提示词
    formatted_prompt = default_prompt.format(knowledge_analysis=report_text)
    
    # 调用 API
    response = client.chat.completions.create(
        model="doubao-1.5-pro-32k-250115",
        messages=[
            {"role": "user", "content": formatted_prompt}
        ],
        temperature=0.7
    )
    
    result = response.choices[0].message.content
    print(f"Generated script: {result}")

    # return json.loads(result)
    result_transcoding = replace_latex(result)
    result_safe = result_transcoding.replace("\\", "\\\\")
    return json.loads(result_safe)
