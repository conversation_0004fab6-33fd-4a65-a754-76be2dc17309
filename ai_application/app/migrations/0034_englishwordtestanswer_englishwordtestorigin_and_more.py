# Generated by Django 4.2 on 2025-04-01 03:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0033_englishwordtestquestion_analysis'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnglishWordTestAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('right_num', models.IntegerField(default=0, verbose_name='正确数量')),
            ],
            options={
                'verbose_name': '英语单词测试结果',
                'verbose_name_plural': '英语单词测试结果',
                'db_table': 'ai_data_english_word_test_answer',
            },
        ),
        migrations.CreateModel(
            name='EnglishWordTestOrigin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('year', models.IntegerField(default=0, verbose_name='年份')),
                ('origin_content', models.TextField(null=True, verbose_name='出策略文章')),
            ],
            options={
                'verbose_name': '英语单词测试真题来源',
                'verbose_name_plural': '英语单词测试真题来源',
                'db_table': 'ai_data_english_word_test_origin',
            },
        ),
        migrations.CreateModel(
            name='EnglishWordTestRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(blank=True, db_index=True, max_length=100, verbose_name='用户id')),
                ('question_num', models.IntegerField(default=0, verbose_name='题目数量')),
            ],
            options={
                'verbose_name': '英语单词测试出题记录',
                'verbose_name_plural': '英语单词测试出题记录',
                'db_table': 'ai_data_english_word_test_record',
            },
        ),
        migrations.AlterModelOptions(
            name='englishwordtestquestion',
            options={'verbose_name': '英语单词测试题目', 'verbose_name_plural': '英语单词测试题目'},
        ),
        migrations.AlterModelOptions(
            name='englishwordteststrategy',
            options={'verbose_name': '英语单词测试策略', 'verbose_name_plural': '英语单词测试策略'},
        ),
        migrations.AddField(
            model_name='englishwordtestquestion',
            name='en_word',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordlibrary'),
        ),
        migrations.AddField(
            model_name='englishwordtestquestion',
            name='options',
            field=models.JSONField(null=True, verbose_name='选项'),
        ),
        migrations.AddField(
            model_name='englishwordtestquestion',
            name='question_type',
            field=models.CharField(blank=True, max_length=16, verbose_name='问题类型'),
        ),
        migrations.CreateModel(
            name='EnglishWordTestRecordDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordtestquestion')),
                ('record', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordtestrecord')),
            ],
            options={
                'verbose_name': '英语单词测试出题记录详情',
                'verbose_name_plural': '英语单词测试出题记录详情',
                'db_table': 'ai_data_english_word_test_record_detail',
            },
        ),
        migrations.CreateModel(
            name='EnglishWordTestAnswerDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_answer', models.CharField(blank=True, max_length=16, verbose_name='用户答案')),
                ('is_right', models.BooleanField(default=False, verbose_name='是否正确')),
                ('answer', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordtestanswer')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordtestquestion')),
                ('record', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordtestrecord')),
            ],
            options={
                'verbose_name': '英语单词测试结果详情',
                'verbose_name_plural': '英语单词测试结果详情',
                'db_table': 'ai_data_english_word_test_answer_detail',
            },
        ),
        migrations.AddField(
            model_name='englishwordtestanswer',
            name='record',
            field=models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordtestrecord'),
        ),
        migrations.AddField(
            model_name='englishwordteststrategy',
            name='test_origin',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordtestorigin'),
        ),
    ]
