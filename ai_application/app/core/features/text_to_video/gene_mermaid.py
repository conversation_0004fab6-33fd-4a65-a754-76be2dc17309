import requests
import zlib
import base64
import time

def encode_mermaid_diagram(mermaid_code: str) -> str:
    # Mermaid code -> compressed + base64-url-safe
    compressed = zlib.compress(mermaid_code.encode('utf-8'), 9)
    return base64.urlsafe_b64encode(compressed).decode('utf-8')

def render_mermaid_to_png(mermaid_code: str, output_file: str = "output.png"):
    # Kroki render URL
    encoded_diagram = encode_mermaid_diagram(mermaid_code)
    url = f"https://kroki.kaoyan-vip.cn/mermaid/png/{encoded_diagram}"

    response = requests.get(url)
    if response.status_code == 200:
        with open(output_file, "wb") as f:
            f.write(response.content)
        print(f"✅ Mermaid 图像已保存为 {output_file}")
    else:
        print(f"❌ 渲染失败，状态码: {response.status_code}")
        print(mermaid_code)
        print(response.text)
        time.sleep(100)

# # 示例 Mermaid 代码（可替换）
# mermaid_code = """
# graph TD
#     classDef tree fill:#D6EAF8,stroke:#2980B9,stroke-width:2px;

#     %% 树1
#     tree1(树1):::tree --> A(A):::tree
#     A --> B(B):::tree
#     B --> D(D):::tree
#     A --> C(C):::tree
#     C --> E(E):::tree
#     C --> F(F):::tree

#     %% 树2
#     tree2(树2):::tree --> G(G):::tree
#     G --> H(H):::tree

#     %% 树3
#     tree3(树3):::tree --> I(I):::tree
# """

# # 调用函数生成图像
# render_mermaid_to_png(mermaid_code, "linked_list_diagram.png")