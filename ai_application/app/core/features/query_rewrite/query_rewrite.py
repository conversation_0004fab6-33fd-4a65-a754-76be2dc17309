from app.constants.app import MessageStatus, AppMode
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.model_manager import ModelInstance
from app.core.prompt.entities import PromptParser
from app.core.prompt.prompt_templates.query_rewrite_prompt import QUERY_REWRITE_PROMPT
from app.core.prompt.simple_prompt_transform import SimplePromptTransform
from app.core.prompt.utils.prompt_message_util import PromptMessageUtil
from app.models import Message, MessageQueryRewrite


class QueryRewriteFeature:

    def rewrite(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            message: Message,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ) -> str:
        # 查询是否是第一条消息
        conversation = message.conversation
        prev_message: Message = Message.objects.filter(
            is_deleted=False,
            conversation=conversation,
            status=MessageStatus.NORMAL.value,
            id__lt=message.id
        ).order_by('-id').first()
        if not prev_message:
            return query

        chat_history = []
        rewrite_obj: MessageQueryRewrite | None = prev_message.query_rewrite
        if rewrite_obj:
            chat_history.append(f'Q: {rewrite_obj.new_query}')
        else:
            chat_history.append(f'Q: {prev_message.query}')
        chat_history.append(f'A: {prev_message.answer}')

        prompt_parser = PromptParser(
            prompt_template=QUERY_REWRITE_PROMPT,
            input_variables={
                'chat_history': '\n'.join(chat_history),
                'question': query,
            },
        )
        prompt_messages = SimplePromptTransform().get_prompt(
            app_mode=AppMode.COMPLETION,
            pre_prompt=prompt_parser.get_prompt(),
            query=''
        )

        model_instance = ModelInstance(
            provider_model_bundle=application_generate_entity.model_conf.provider_model_bundle,
            model=application_generate_entity.model_conf.model
        )

        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            model_parameters=application_generate_entity.model_conf.model_params,
            stream=False
        )

        new_query = invoke_result.message.content

        message.is_query_rewrite = True
        message.save(update_fields=['is_query_rewrite'])

        MessageQueryRewrite.objects.create(
            conversation=conversation,
            message=message,
            origin_query=query,
            new_query=new_query,
            prompt=prompt_parser.get_prompt(),
            chat_history=chat_history,
        )

        if tracing_log:
            log_content = PromptMessageUtil.prompt_messages_to_prompt_for_saving(invoke_result.prompt_messages)
            tracing_log.add_tracing(
                tracing_type='query_rewrite',
                query=query,
                content=log_content,
                answer=invoke_result.message.content,
                message_tokens=invoke_result.usage.prompt_tokens,
                answer_tokens=invoke_result.usage.completion_tokens,
                total_tokens=invoke_result.usage.total_tokens,
                latency=invoke_result.usage.latency,
            )

        return new_query
