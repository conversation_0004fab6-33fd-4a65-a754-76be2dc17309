from langchain.chains.llm import <PERSON><PERSON><PERSON>n
from langchain_core.prompts import PromptTemplate
from django.conf import settings
from langchain_openai import ChatOpenAI
from ai_application.settings import DAYI_TEXT_MODEL
import json
import logging

logger = logging.getLogger(__name__)


class PoliticsCoursePlanningService:
    """
    考研政治课程规划服务
    使用LangChain实现基于课程大纲的学习指南生成
    """
    
    def __init__(self):
        """初始化服务，配置LLM模型"""
        self.llm = self._get_llm_model()
        self.prompt_template = self._get_prompt_template()
    
    def _get_llm_model(self):
        """获取LLM模型实例，参考CollegeAnalysisNewService的配置"""
        return ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base=settings.DOUBAO_API_BASE,
            model_name=DAYI_TEXT_MODEL,
            temperature=0.3,
            stream_options={
                "include_usage": True
            }
        )
    
    def _get_prompt_template(self):
        """构建提示词模板"""
        template = """你是一个专业的考研政治课程规划师。根据我提供的课程大纲JSON数据，请完成以下任务：

1. **分阶段和单元生成学习指南**：
   针对每一个"单元"，生成包含以下三个方面的详细内容：
   * **学习目标**：学员学完本单元后应掌握的知识和能力。
   * **核心内容**：根据单元名称推断并列出本单元涵盖的关键知识点。
   * **学习建议**：提供高效学习本单元的方法、注意事项和时间分配建议。

2. **分析各科目设置逻辑**：
   在所有单元指南生成完毕后，总结并分析"马原"、"毛中特"、"史纲"、"思道法"等主要科目的内容设置逻辑和学习主线。

**请注意**：在生成内容时，**请完全忽略“入学指南”和“直播答疑”这两个阶段，以及任何名称中明确包含“答疑”、“批改”的单元**，不要为它们生成任何输出。

请使用清晰的Markdown格式进行组织，确保内容具有指导性和可操作性。

课程大纲数据：
{course_data}

请开始分析并生成学习指南："""
        
        return PromptTemplate(
            input_variables=["course_data"],
            template=template
        )
    
    def generate_learning_guide(self, course_packages_data):
        """
        生成考研政治学习指南
        
        Args:
            course_packages_data: 课包信息数据，来自SupervisePlanScheme.get_course_unit方法
            
        Returns:
            str: 生成的学习指南内容
        """
        try:
            # 将课包数据转换为JSON字符串
            course_data_json = json.dumps(course_packages_data, ensure_ascii=False, indent=2)
            
            logger.info(f"开始生成政治课程学习指南，课包数量: {len(course_packages_data)}")
            
            # 创建LLM链
            chain = LLMChain(
                llm=self.llm,
                prompt=self.prompt_template,
                verbose=True
            )
            
            # 执行生成
            result = chain.run(course_data=course_data_json)
            
            logger.info("政治课程学习指南生成完成")
            return result
            
        except Exception as e:
            logger.error(f"生成政治课程学习指南失败: {str(e)}", exc_info=True)
            raise e
    
    def generate_learning_guide_streaming(self, course_packages_data):
        """
        流式生成考研政治学习指南
        
        Args:
            course_packages_data: 课包信息数据
            
        Returns:
            Generator: 流式输出的生成器
        """
        try:
            # 配置流式输出的LLM
            streaming_llm = ChatOpenAI(
                openai_api_key=settings.DOUBAO_API_KEY,
                openai_api_base=settings.DOUBAO_API_BASE,
                model_name=DAYI_TEXT_MODEL,
                temperature=0.3,
                streaming=True,
                stream_options={
                    "include_usage": True
                }
            )
            
            # 将课包数据转换为JSON字符串
            course_data_json = json.dumps(course_packages_data, ensure_ascii=False, indent=2)
            
            logger.info(f"开始流式生成政治课程学习指南，课包数量: {len(course_packages_data)}")
            
            # 创建流式LLM链
            chain = LLMChain(
                llm=streaming_llm,
                prompt=self.prompt_template,
                verbose=True
            )
            
            # 执行流式生成
            for chunk in chain.stream({"course_data": course_data_json}):
                yield chunk
                
        except Exception as e:
            logger.error(f"流式生成政治课程学习指南失败: {str(e)}", exc_info=True)
            raise e
    
    @classmethod
    def analyze_course_structure(cls, course_packages_data):
        """
        分析课程结构，提取关键信息
        
        Args:
            course_packages_data: 课包信息数据
            
        Returns:
            dict: 分析结果
        """
        analysis = {
            "总课包数": len(course_packages_data),
            "适用学科": [],
            "课包详情": [],
            "统计信息": {
                "总阶段数": 0,
                "总单元数": 0,
                "总知识点数": 0
            }
        }
        
        for package in course_packages_data:
            if isinstance(package, dict):
                # 提取适用学科
                subjects = package.get("适用学科", [])
                analysis["适用学科"].extend(subjects)
                
                # 提取课包信息
                package_info = {
                    "课包名称": package.get("课包名称", "未知课包"),
                    "适用学科": subjects,
                    "阶段数": len(package.get("课包信息", [])),
                    "阶段详情": []
                }
                
                # 分析每个阶段
                for stage in package.get("课包信息", []):
                    stage_info = {
                        "阶段名称": stage.get("阶段名称", "未知阶段"),
                        "单元数": len(stage.get("单元信息", [])),
                        "单元列表": []
                    }
                    
                    # 分析每个单元
                    for unit in stage.get("单元信息", []):
                        if isinstance(unit, dict):
                            if "组合单元名称" in unit:
                                # 处理组合单元
                                unit_info = {
                                    "类型": "组合单元",
                                    "名称": unit.get("组合单元名称", "未知组合单元"),
                                    "分支单元数": len(unit.get("分支单元信息", [])),
                                    "知识点数": sum(len(sub_unit.get("涉及重要知识点", [])) 
                                                  for sub_unit in unit.get("分支单元信息", []))
                                }
                                analysis["统计信息"]["总知识点数"] += unit_info["知识点数"]
                            else:
                                # 处理普通单元
                                unit_info = {
                                    "类型": "普通单元",
                                    "名称": unit.get("单元名称", "未知单元"),
                                    "知识点数": len(unit.get("涉及重要知识点", [])),
                                    "计划时长": unit.get("计划时长", 0)
                                }
                                analysis["统计信息"]["总知识点数"] += unit_info["知识点数"]
                            
                            stage_info["单元列表"].append(unit_info)
                            analysis["统计信息"]["总单元数"] += 1
                    
                    package_info["阶段详情"].append(stage_info)
                    analysis["统计信息"]["总阶段数"] += 1
                
                analysis["课包详情"].append(package_info)
        
        # 去重适用学科
        analysis["适用学科"] = list(set(analysis["适用学科"]))
        
        return analysis
