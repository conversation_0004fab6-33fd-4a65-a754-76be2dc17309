--
-- Create model CityGDP
--
CREATE TABLE `ai_city_gdp` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `city` varchar(100) NULL, `rank` integer NULL, `score` double precision NULL);
--
-- Create model <PERSON><PERSON><PERSON><PERSON><PERSON>ueryResult
--
CREATE TABLE `ai_kao_yan_query_result` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `undergraduate_secondary_major` varchar(255) NULL, `related_college_majors` json NULL);
--
-- Create model MajorInfo
--
CREATE TABLE `ai_major_info` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `major_code` varchar(10) NULL, `major_evaluation` varchar(10) NULL, `major_type` varchar(10) NULL, `college_name` varchar(255) NULL, `college_code` varchar(10) NULL, `college_level` varchar(255) NULL, `college_nature` varchar(255) NULL, `college_rank` varchar(255) NULL, `is_doctoral_point` bool NULL, `has_scholarship` bool NULL, `province` varchar(100) NULL, `region_category` varchar(10) NULL, `national_line_score` double precision NULL);
--
-- Create model MajorRelation
--
CREATE TABLE `ai_major_relation` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `undergraduate_first_discipline` varchar(255) NULL, `undergraduate_second_category` varchar(255) NULL, `graduate_first_discipline` varchar(255) NULL, `graduate_second_category_code` varchar(10) NULL, `graduate_second_category` varchar(255) NULL, `graduate_third_major_code` varchar(10) NULL, `graduate_third_major` varchar(255) NULL, `relation_level` integer NULL);
--
-- Create model UndergraduateCollegeInfo
--
CREATE TABLE `ai_undergraduate_college_info` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `name` varchar(255) NULL, `undergraduate_code` varchar(10) NULL, `level_display` varchar(255) NULL);
--
-- Create model UndergraduateMajorCourse
--
CREATE TABLE `ai_undergraduate_major_course` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `first_discipline` varchar(100) NULL, `second_category` varchar(100) NULL, `core_courses` longtext NULL);
CREATE INDEX `ai_city_gdp_add_time_818903d7` ON `ai_city_gdp` (`add_time`);
CREATE INDEX `ai_kao_yan_query_result_add_time_fd724ee1` ON `ai_kao_yan_query_result` (`add_time`);
CREATE INDEX `ai_major_info_add_time_63fb3687` ON `ai_major_info` (`add_time`);
CREATE INDEX `ai_major_relation_add_time_c05489f6` ON `ai_major_relation` (`add_time`);
CREATE INDEX `ai_undergraduate_college_info_add_time_f254ed1f` ON `ai_undergraduate_college_info` (`add_time`);
CREATE INDEX `ai_undergraduate_major_course_add_time_52a32617` ON `ai_undergraduate_major_course` (`add_time`);