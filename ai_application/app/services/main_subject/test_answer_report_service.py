from app.api.dto import ChatMessageDto
from app.errors import AppNotFoundError
from app.models import App, Account
from app.services.app_generate_service import AppGenerateService


class TestAnswerReportService:

    @classmethod
    def gen_answer_report(cls, answer_id: str, user_id: str, account: Account):
        app: App = App.objects.filter(is_deleted=False, app_type='test_answer_report').first()
        if not app:
            raise AppNotFoundError()

        chat_dto = ChatMessageDto(
            app_id=app.app_no,
            query=answer_id,
            stream=True,
            inputs={"answer_id": answer_id, "user_id": user_id},
        )
        return AppGenerateService.generate(chat_dto, account)
