from pydantic import ConfigDict

from django_ext.base_dto_model import MyBaseModel


class RemoteFileInfo(MyBaseModel):
    """
    Notion import info.
    """
    name: str = ''
    url: str
    model_config = ConfigDict(arbitrary_types_allowed=True)


class ExtractSetting(MyBaseModel):
    """
    Model class for provider response.
    """
    datasource_type: str
    remote_file_info: RemoteFileInfo | None = None
    data_source_info: dict | None = None
    document_model: str | None = None
    model_config = ConfigDict(arbitrary_types_allowed=True)
