import datetime
import logging
from collections import defaultdict

from celery import shared_task
from django.utils import timezone

from app.libs.ding_robot import send_dingtalk_message_by_env2
from app.models import Message

logger = logging.getLogger(__name__)


@shared_task(queue='message_task')
def auto_check_message_error_task():
    logger.info('auto_check_message_error_task begin...')

    min_error_num = 3
    interval_minutes = 5
    # 每隔5分钟检查一次异常消息是否超过3条
    time_point = timezone.now() - datetime.timedelta(minutes=interval_minutes)
    messages = Message.objects.filter(is_deleted=False, is_exception=True, add_time__gte=time_point)

    # 知舟问答
    special_app_no = 'app_28482099ed2945e2'
    app_nos = ['content_extraction', 'article_generation', 'prompt_optimize',
               'document_summary', 'document_extraction', 'video_script',
               'knowledge_query', 'complex_sentence_analysis', 'lecture_note_generation',
               'math_problem_solving',]

    app_type_map = {
        'chat_app2': '知舟问答2.0',
        'zhihenRadar': '智痕雷达'
    }

    app_types = list(app_type_map.keys())

    app_name_map = {}
    err_app_map = defaultdict(int)
    for m in messages:
        if m.app.app_no == special_app_no:
            if m.message_type == 'question':
                app_name_map['chat_question'] = '知舟问答_解题助手'
                err_app_map['chat_question'] += 1
            elif m.message_type == 'code':
                app_name_map['chat_code'] = '知舟问答_代码优化'
                err_app_map['chat_code'] += 1
            if m.message_type == 'math_question':
                app_name_map['chat_math_question'] = '数学解题助手'
                err_app_map['chat_math_question'] += 1
            else:
                app_name_map['chat_normal'] = '知舟问答'
                err_app_map['chat_normal'] += 1
        elif m.app.app_no in app_nos:
            app_name_map[m.app.app_no] = m.app.name
            err_app_map[m.app.app_no] += 1
        elif m.app.app_type in app_types:
            app_name_map[m.app.app_no] = m.app.name
            err_app_map[m.app.app_no] += 1

    for app_no, error_num in err_app_map.items():
        if error_num >= min_error_num:
            app_name = app_name_map.get(app_no, '知舟应用')
            message = f'【{app_name}】在过去{interval_minutes}分钟内出现异常消息【{error_num}条】，请及时处理！'
            send_dingtalk_message_by_env2(message, at_all=False)
