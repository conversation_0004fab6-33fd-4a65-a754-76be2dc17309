from moviepy.editor import ImageSequenceClip, AudioFileClip, concatenate_audioclips

def geneVideo(a_list,v_list,folder):

    # 将MP3存入audio_list,对应音频长度存入duration_list
    duration_list = []
    audio_list = []
    for item in a_list:
        audio = AudioFileClip(item)
        duration_list.append(audio.duration)
        audio_list.append(audio)

    final_audio = concatenate_audioclips(audio_list) # 合并音频文件

    # 生成合并的音频文件
    final_audio.write_audiofile(f"{folder}/final_audio.mp3")

    # 图片合并成视频并设置每张图片播放时长
    clip = ImageSequenceClip(v_list, durations=duration_list)

    # 视频配置音频
    final_clip = clip.set_audio(final_audio)

    # 输出
    final_clip.write_videofile(f"{folder}/final_video.mp4", fps=24)
    

    # 根据字幕配置视频的命令，这里的video改成对应的视频文件名，final_audio.srt改成对应的字幕文件
    # ffmpeg -i video.mp4 -vf subtitles=final_audio.srt -c:a copy video_subtitle.mp4
