# -*- coding: utf-8 -*-
from django.conf import settings
from django.utils.module_loading import import_string
from logging import getLogger
from rest_framework.request import Request
from django.core.handlers.wsgi import WSGIRequest

logger = getLogger(__name__)


def log_msg(msg_dict, hooks=None):
    """
    将字典转成可以输出的log

    :param dict msg_dict: 需要输出的字典
    :param list(func) hooks: 转换函数
    :return:
    :rtype: str
    """
    msgs = []
    default_hooks = [import_string(item) for item in settings.LOGGING.get("hooks", [])]
    hooks = default_hooks.extend(hooks) if hooks else default_hooks
    for key, value in msg_dict.items():
        try:
            msg = None

            # 调用hooks函数
            for hook in hooks:
                msg = hook(key, value) or msg

            value_class_name = f'{value.__class__.__module__}.{value.__class__.__name__}'
            if not msg and value_class_name == 'requests.models.Response':
                msg = ("url: {0} \n headers: {1} \n method: {2} \n content: {3} \n"
                       .format(value.url, value.headers.__repr__(),
                               value.request.method, value.content))
                if hasattr(value.request, 'body'):
                    msg += 'body: {0} \n'.format(value.request.body)

            if not msg and isinstance(value, (Request, WSGIRequest)):
                headers = {k: v for k, v in value.META.items()
                           if k in ('CONTENT_TYPE', 'CONTENT_LENGTH') or 'HTTP_' in k}
                msg = ("url: {0} \n headers: {1} \n method: {2} \n "
                       .format(value.path, headers.__repr__(),
                               value.method))

                try:
                    if hasattr(value, 'data'):
                        msg += 'data: {0} \n'.format(value.data.__repr__())
                except Exception:
                    pass

                try:
                    if hasattr(value, 'body'):
                        msg += 'body: {0} \n'.format(value.body.__repr__())
                except Exception:
                    pass

            msg = msg or "{0}: {1} \n".format(key, value.__repr__())
            msgs.append(msg)

        except Exception as e:
            logger.exception(e)

    return "".join(msgs)
