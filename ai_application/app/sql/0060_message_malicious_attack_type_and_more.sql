--
-- Add field malicious_attack_type to message
--
ALTER TABLE `ai_message`
    ADD COLUMN `malicious_attack_type` integer NULL;
--
-- Add field malicious_attack_type_display to message
--
ALTER TABLE `ai_message`
    ADD COLUMN `malicious_attack_type_display` longtext NULL;
--
-- <PERSON>reate model KnowledgeVideo
--
CREATE TABLE `ai_knowledge_video`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `video`         varchar(200) NOT NULL,
    `image`         varchar(200) NOT NULL,
    `name_id`       bigint       NOT NULL
);
ALTER TABLE `ai_knowledge_video`
    ADD CONSTRAINT `ai_knowledge_video_name_id_0e1d9683_fk_ai_knowledge_simple_id` FOREIGN KEY (`name_id`) REFERENCES `ai_knowledge_simple` (`id`);
CREATE INDEX `ai_knowledge_video_add_time_a70b4639` ON `ai_knowledge_video` (`add_time`);