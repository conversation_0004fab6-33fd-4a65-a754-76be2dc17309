name,core_course_code
计算机网络的定义和功能,CC_JSXW
计算机网络性能指标,CC_JSXW
网络分层结构,CC_JSXW
ISO/OSI参考模型,CC_JSXW
TCP/IP参考模型,CC_JSXW
通信基础概念,CC_JSXW
奈奎斯特定理,CC_JSXW
香农定理,CC_JSXW
编码与调制,CC_JSXW
电路交换,CC_JSXW
报文交换,CC_JSXW
分组交换,CC_JSXW
数据报,CC_JSXW
虚电路,CC_JSXW
传输介质,CC_JSXW
中继器,CC_JSXW
集线器,CC_JSXW
数据链路层的功能,CC_JSXW
组帧,CC_JSXW
检错编码,CC_JSXW
纠错编码,CC_JSXW
流量控制,CC_JSXW
滑动窗口,CC_JSXW
停止-等待协议,CC_JSXW
后退N帧协议,CC_JSXW
选择重传协议,CC_JSXW
信道划分,CC_JSXW
随机访问,CC_JSXW
轮询访问,CC_JSXW
局域网,CC_JSXW
以太网,CC_JSXW
IEEE802.3,CC_JSXW
IEEE802.11,CC_JSXW
VLAN概念和原理,CC_JSXW
广域网,CC_JSXW
PPP协议,CC_JSXW
异构网络互连,CC_JSXW
路由与转发,CC_JSXW
SDN,CC_JSXW
拥塞控制,CC_JSXW
静态路由,CC_JSXW
动态路由,CC_JSXW
距离-向量路由算法,CC_JSXW
链路状态路由算法,CC_JSXW
层次路由,CC_JSXW
IPv-4,CC_JSXW
IPv-6,CC_JSXW
自治系统,CC_JSXW
域内路由,CC_JSXW
域间路由,CC_JSXW
RIP路由协议,CC_JSXW
OSPF路由协议,CC_JSXW
BGP路由协议,CC_JSXW
IP组播,CC_JSXW
移动IP,CC_JSXW
路由器的组成和功能,CC_JSXW
路由表与分组转发,CC_JSXW
传输层功能,CC_JSXW
传输层寻址与端口,CC_JSXW
无连接服务,CC_JSXW
面向连接服务,CC_JSXW
UDP协议,CC_JSXW
TCP协议,CC_JSXW
客户/服务器模型,CC_JSXW
对等模型,CC_JSXW
DNS系统,CC_JSXW
FTP,CC_JSXW
电子邮件,CC_JSXW
WWW的概念和结构,CC_JSXW
HTTP协议,CC_JSXW
DHCP协议,CC_JSXW
冯·诺依曼计算机,CC_JSZZYL
计算机性能指标,CC_JSZZYL
高级语言程序与机器语言程序之间的转换,CC_JSZZYL
定点数的表示和运算,CC_JSZZYL
浮点数的表示和运算,CC_JSZZYL
C语言数据类型,CC_JSZZYL
溢出,CC_JSZZYL
算术逻辑部件（ALU）,CC_JSZZYL
加法器,CC_JSZZYL
补码加减运算,CC_JSZZYL
标志位的生成,CC_JSZZYL
乘除运算,CC_JSZZYL
整数的表示和运算,CC_JSZZYL
存储器分类,CC_JSZZYL
Cache基本原理,CC_JSZZYL
Cache映射,CC_JSZZYL
Cache中主存块的替换算法,CC_JSZZYL
Cache写策略,CC_JSZZYL
虚拟存储器,CC_JSZZYL
主存和CPU之间的连接,CC_JSZZYL
Flash存储器,CC_JSZZYL
SRAM存储器,CC_JSZZYL
DRAM存储器,CC_JSZZYL
DRAM芯片和内存条,CC_JSZZYL
多模块存储器,CC_JSZZYL
磁盘存储器,CC_JSZZYL
固态硬盘,CC_JSZZYL
页式虚拟存储器,CC_JSZZYL
段式虚拟存储器,CC_JSZZYL
段页式虚拟存储器,CC_JSZZYL
指令格式,CC_JSZZYL
寻址方式,CC_JSZZYL
数据的对齐和大小端存放方式,CC_JSZZYL
CISC/RISC,CC_JSZZYL
高级语言程序与机器级代码之间的对应,CC_JSZZYL
CPU功能和结构,CC_JSZZYL
指令执行,CC_JSZZYL
指令流水线,CC_JSZZYL
控制器,CC_JSZZYL
异常,CC_JSZZYL
中断,CC_JSZZYL
数据通路,CC_JSZZYL
多处理器,CC_JSZZYL
程序中断方式,CC_JSZZYL
DMA方式,CC_JSZZYL
外部设备,CC_JSZZYL
总线的组成,CC_JSZZYL
总线性能指标,CC_JSZZYL
总线事务和定时,CC_JSZZYL
总线标准,CC_JSZZYL
I/O端口,CC_JSZZYL
I/O接口,CC_JSZZYL
操作系统的作用,CC_CZXT
单道批处理系统,CC_CZXT
多道批处理系统,CC_CZXT
分时系统,CC_CZXT
实时系统,CC_CZXT
微机操作系统,CC_CZXT
并发,CC_CZXT
并行,CC_CZXT
共享,CC_CZXT
虚拟,CC_CZXT
异步,CC_CZXT
操作系统的结构,CC_CZXT
进程的定义和特征,CC_CZXT
进程状态转换,CC_CZXT
进程控制,CC_CZXT
进程控制块,CC_CZXT
临界资源,CC_CZXT
信号量,CC_CZXT
管程,CC_CZXT
进程通信,CC_CZXT
线程,CC_CZXT
中断,CC_CZXT
异常,CC_CZXT
内核模式,CC_CZXT
用户模式,CC_CZXT
系统调用,CC_CZXT
程序的链接,CC_CZXT
程序的装入,CC_CZXT
CPU调度,CC_CZXT
调度方式,CC_CZXT
进程调度,CC_CZXT
先来先服务调度算法,CC_CZXT
优先级调度算法,CC_CZXT
高响应比调度算法,CC_CZXT
轮转调度算法,CC_CZXT
多级反馈队列调度算法,CC_CZXT
最早截止时间优先算法,CC_CZXT
最低松弛度优先算法,CC_CZXT
同步,CC_CZXT
互斥,CC_CZXT
锁,CC_CZXT
死锁的概念,CC_CZXT
死锁预防,CC_CZXT
死锁避免,CC_CZXT
死锁检测和解除,CC_CZXT
内存,CC_CZXT
地址变换,CC_CZXT
内存共享,CC_CZXT
内存保护,CC_CZXT
内存分配,CC_CZXT
内存回收,CC_CZXT
连续分配管理方式,CC_CZXT
页式管理,CC_CZXT
段式管理,CC_CZXT
段页式管理,CC_CZXT
对换,CC_CZXT
虚拟内存,CC_CZXT
请求页式管理,CC_CZXT
最佳置换算法,CC_CZXT
先进先出页面置换算法,CC_CZXT
最近最久未使用置换算法,CC_CZXT
最少使用置换算法,CC_CZXT
Clock置换算法,CC_CZXT
页面缓冲算法,CC_CZXT
抖动,CC_CZXT
工作集,CC_CZXT
请求分段管理,CC_CZXT
设备,CC_CZXT
轮询,CC_CZXT
DMA,CC_CZXT
设备驱动程序,CC_CZXT
设备独立软件,CC_CZXT
I/O接口,CC_CZXT
I/O端口,CC_CZXT
设备控制器,CC_CZXT
I/O通道,CC_CZXT
中断处理程序,CC_CZXT
缓冲区管理,CC_CZXT
假脱机技术（SPOOLing）,CC_CZXT
磁盘,CC_CZXT
磁盘的调度方法,CC_CZXT
固态硬盘,CC_CZXT
磨损均衡,CC_CZXT
文件的基本概念,CC_CZXT
文件的操作,CC_CZXT
文件保护,CC_CZXT
文件的逻辑结构,CC_CZXT
文件的物理结构,CC_CZXT
文件目录,CC_CZXT
文件控制块,CC_CZXT
索引结点,CC_CZXT
硬链接,CC_CZXT
软连接,CC_CZXT
访问矩阵,CC_CZXT
保护域,CC_CZXT
连续组织方式,CC_CZXT
链接组织方式,CC_CZXT
FAT技术,CC_CZXT
NTFS文件组织方式,CC_CZXT
索引组织方式,CC_CZXT
空闲表法,CC_CZXT
空闲链表法,CC_CZXT
位示图法,CC_CZXT
成组链接法,CC_CZXT
磁盘高速缓存,CC_CZXT
