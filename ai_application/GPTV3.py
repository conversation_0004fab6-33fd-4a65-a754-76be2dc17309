# -*- coding: utf-8 -*-
import os
import django
import numpy as np
import pandas as pd
from typing import Dict, Optional, Union

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()
from app.models import ExamAnalysisKnowledgePointWithStats


# ---------------------------
# 1) 数据加载
# ---------------------------
def load_knowledge_points(subjects=None) -> pd.DataFrame:
    """
    从数据库读取知识点数据 -> DataFrame
    需要字段: subject, knowledge_point, total_freq, avg_difficulty
    其他字段保留以便后续扩展，但本简化方案不使用。
    """
    if subjects is None:
        subjects = ['计算机组成原理', '计算机网络', '操作系统', '数据结构']

    data = {
        'subject': [],
        'knowledge_point': [],
        'total_freq': [],
        'avg_difficulty': [],
        'mcq_freq': [],
        'mcq_difficulty': [],
        'subjective_freq': [],
        'subjective_difficulty': []
    }
    for subj in subjects:
        qs = ExamAnalysisKnowledgePointWithStats.objects.filter(subject=subj).values()
        for item in qs:
            data['subject'].append(item["subject"])
            data['knowledge_point'].append(item["point_name"])
            data['total_freq'].append(item.get("exam_count", 0))
            data['avg_difficulty'].append(item.get("avg_difficulty", 0.0))
            data['mcq_freq'].append(item.get("choice_count", 0))
            data['mcq_difficulty'].append(item.get("choice_avg_difficulty", 0.0))
            data['subjective_freq'].append(item.get("comprehensive_count", 0))
            data['subjective_difficulty'].append(item.get("comprehensive_avg_difficulty", 0.0))

    df = pd.DataFrame(data)
    # 基础清洗与类型安全
    for col in ['total_freq', 'avg_difficulty']:
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0.0)
    return df


# ---------------------------
# 2) 优先级与选集：核心类
# ---------------------------
class KnowledgePointSelector:
    """
    简化方案（科目内 z-score + 近似分值质量 + 可学性 + 贪心累加到目标分）
    """
    def __init__(
        self,
        df: pd.DataFrame,
        exam_total_score: float = 150.0,
        alpha: float = 0.7,         # priority 对考频的权重
        beta: float = 0.3,          # priority 对难度惩罚的权重
        kappa: float = 1.0,         # learnability 中对难度的敏感度
        use_subject_caps: bool = False,   # 是否按科目满分分摊分值质量
        subject_max_scores: Optional[Dict[str, float]] = None,
        subject_col: str = 'subject'
    ):
        self.df = df.copy()
        self.exam_total_score = float(exam_total_score)
        self.alpha = float(alpha)
        self.beta = float(beta)
        self.kappa = float(kappa)
        self.use_subject_caps = bool(use_subject_caps)
        self.subject_max_scores = subject_max_scores or {}
        self.subject_col = subject_col

        self._validate()
        self._compute_groupwise_zscores()
        self._compute_score_mass()
        self._compute_learnability()
        self._compute_gain()
        self._compute_priority()

    # ---------- 工具 ----------
    def _validate(self):
        needed = {'subject', 'knowledge_point', 'total_freq', 'avg_difficulty'}
        missing = needed - set(self.df.columns)
        if missing:
            raise ValueError(f"缺少必要字段: {missing}")
        # 过滤无 subject / 知识点的脏数据
        self.df['subject'] = self.df['subject'].astype(str)
        self.df['knowledge_point'] = self.df['knowledge_point'].astype(str)
        self.df = self.df[(self.df['subject'] != '') & (self.df['knowledge_point'] != '')].reset_index(drop=True)

    @staticmethod
    def _zscore_safe(x: pd.Series) -> pd.Series:
        mu = x.mean()
        sigma = x.std(ddof=0)
        if sigma == 0 or np.isnan(sigma):
            return pd.Series(np.zeros(len(x)), index=x.index)
        return (x - mu) / sigma

    # ---------- 核心计算 ----------
    def _compute_groupwise_zscores(self):
        # 科目内 z-score
        self.df['z_total_freq'] = self.df.groupby(self.subject_col)['total_freq'].transform(self._zscore_safe)
        self.df['z_avg_difficulty'] = self.df.groupby(self.subject_col)['avg_difficulty'].transform(self._zscore_safe)

    def _compute_score_mass(self):
        """
        以考频近似“分值质量”。
        - 默认: 全局按 total_freq 占比把 exam_total_score 分摊到每个知识点。
        - 可选: use_subject_caps=True 时，先在科目内按占比分摊到科目满分，再汇总（更贴合官方配分）。
        """
        if self.use_subject_caps:
            # 按科目满分分摊
            if not self.subject_max_scores:
                raise ValueError("use_subject_caps=True 但未提供 subject_max_scores。")
            self.df['score_mass'] = 0.0
            for subj, g in self.df.groupby(self.subject_col):
                cap = float(self.subject_max_scores.get(subj, 0.0))
                den = g['total_freq'].sum()
                if den <= 0:
                    # 若该科目无频次，均分该科满分（或直接给 0；此处采用均分更温和）
                    share = (cap / max(len(g), 1))
                    self.df.loc[g.index, 'score_mass'] = share
                else:
                    self.df.loc[g.index, 'score_mass'] = cap * (g['total_freq'] / den)
            # 校验总体近似为 sum(cap)
        else:
            # 全局分摊
            den = self.df['total_freq'].sum()
            if den <= 0:
                # 极端情况：所有频次为 0，则全体均分到总分
                self.df['score_mass'] = self.exam_total_score / max(len(self.df), 1)
            else:
                self.df['score_mass'] = self.exam_total_score * (self.df['total_freq'] / den)

    def _compute_learnability(self):
        """
        根据难度的科目内 z 分数，使用逻辑函数得到可学性(0~1)，难度越大越小。
        l = 1 / (1 + exp(kappa * z_diff))
        """
        z = self.df['z_avg_difficulty'].astype(float)
        self.df['learnability'] = 1.0 / (1.0 + np.exp(self.kappa * z))

    def _compute_gain(self):
        """
        预期可拿分 = 分值质量 * 可学性
        """
        self.df['gain'] = self.df['score_mass'] * self.df['learnability']

    def _compute_priority(self):
        """
        性价比优先级：priority = alpha * z_freq - beta * z_diff
        """
        self.df['priority'] = self.alpha * self.df['z_total_freq'] - self.beta * self.df['z_avg_difficulty']

    # ---------- 对外接口 ----------
    def recommend_global(self, top_k: int = 30, by: str = 'priority') -> pd.DataFrame:
        """
        返回全局 Top-K，by 可选 'priority' 或 'gain'
        """
        by = by.lower()
        if by not in ('priority', 'gain'):
            raise ValueError("by 必须是 'priority' 或 'gain'")

        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority'
        ]
        df_sorted = (self.df.sort_values([by, 'total_freq', 'avg_difficulty'],
                                         ascending=[False, False, True])
                         .loc[:, cols]
                         .reset_index(drop=True))
        return df_sorted.head(top_k)

    def recommend_per_subject(self, k_per_subject: int = 5, by: str = 'priority') -> pd.DataFrame:
        """
        每科 Top-k，by 可选 'priority' 或 'gain'
        """
        by = by.lower()
        if by not in ('priority', 'gain'):
            raise ValueError("by 必须是 'priority' 或 'gain'")

        def topk(g):
            return g.sort_values([by, 'total_freq', 'avg_difficulty'],
                                 ascending=[False, False, True]).head(k_per_subject)

        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority'
        ]
        out = (self.df.groupby(self.subject_col, group_keys=False)
                    .apply(topk)
                    .loc[:, cols]
                    .reset_index(drop=True))
        return out

    def recommend_to_target(
        self,
        target_score: float = 114.0,
        safety: float = 1.05,
        ensure_per_subject_k: Optional[Union[int, Dict[str, int]]] = None,
        rank_by: str = 'priority'
    ) -> pd.DataFrame:
        """
        给定目标分，输出一个“学习清单”，使累计 gain ≥ target_score * safety。
        - ensure_per_subject_k:
            * None: 不强制覆盖
            * int: 每个科目至少取 k 个
            * dict: 每科自定义保底数量，如 {'计算机网络':2, '数据结构':1}
        - rank_by: 'priority' 或 'gain'
        返回列包含 cumulative_gain 以便查看覆盖进度。
        """
        rank_by = rank_by.lower()
        if rank_by not in ('priority', 'gain'):
            raise ValueError("rank_by 必须是 'priority' 或 'gain'")

        # 1) 初步排序（稳定 tie-breaker：total_freq desc, avg_difficulty asc, kp 名字字典序）
        base = self.df.assign(
            kp_key=self.df['knowledge_point'].astype(str)
        ).sort_values(
            [rank_by, 'total_freq', 'avg_difficulty', 'kp_key'],
            ascending=[False, False, True, True]
        ).drop(columns=['kp_key'])

        # 2) 先做科目保底（如果需要）
        picked_idx = []
        if ensure_per_subject_k is not None:
            if isinstance(ensure_per_subject_k, int):
                want = {s: ensure_per_subject_k for s in base[self.subject_col].unique()}
            elif isinstance(ensure_per_subject_k, dict):
                want = {str(k): int(v) for k, v in ensure_per_subject_k.items()}
            else:
                raise ValueError("ensure_per_subject_k 需为 int 或 dict")

            for subj, need in want.items():
                if need <= 0:
                    continue
                g = base[base[self.subject_col] == subj]
                take = g.head(need)
                picked_idx.extend(list(take.index))

        picked_idx = list(dict.fromkeys(picked_idx))  # 去重保持顺序
        picked = base.loc[picked_idx].copy()
        rest = base.drop(index=picked_idx)

        # 3) 贪心补齐到目标 gain
        goal = float(target_score) * float(safety)
        if not picked.empty:
            picked['cumulative_gain'] = picked['gain'].cumsum()
        else:
            picked = pd.DataFrame(columns=base.columns.tolist() + ['cumulative_gain'])

        current = picked['gain'].sum() if not picked.empty else 0.0
        for i, row in rest.iterrows():
            if current >= goal:
                break
            picked = pd.concat([picked, row.to_frame().T], ignore_index=True)
            current += float(row['gain'])
            picked.loc[picked.index[-1], 'cumulative_gain'] = current

        # 4) 结果整理
        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority', 'cumulative_gain'
        ]
        result = picked.loc[:, cols].reset_index(drop=True)

        # 附加元信息
        result.attrs['target_score'] = target_score
        result.attrs['safety'] = safety
        result.attrs['goal_gain'] = goal
        result.attrs['achieved_gain'] = float(result['gain'].sum()) if not result.empty else 0.0
        result.attrs['alpha'] = self.alpha
        result.attrs['beta'] = self.beta
        result.attrs['kappa'] = self.kappa
        result.attrs['use_subject_caps'] = self.use_subject_caps
        return result


# ---------------------------
# 3) 示例运行
# ---------------------------
if __name__ == '__main__':
    SUBJECT_MAX_SCORES = {
        '计算机组成原理': 45,
        '计算机网络': 25,
        '操作系统': 35,
        '数据结构': 45
    }

    # 读取数据
    df = load_knowledge_points()

    # 初始化（默认不按科目配分，直接全局把 150 分按频次分摊）
    selector = KnowledgePointSelector(
        df=df,
        exam_total_score=150,
        alpha=0.7,
        beta=0.3,
        kappa=1.0,
        use_subject_caps=False,                 # 若想尊重科目配分改 True
        subject_max_scores=SUBJECT_MAX_SCORES
    )

    # 全局 Top-30（按 priority）
    print("="*60)
    print("全局 Top-30（按 priority）")
    print("="*60)
    print(selector.recommend_global(top_k=30, by='priority').to_string(index=False))

    # 每科 Top-5（按 priority）
    print("\n" + "="*60)
    print("各科 Top-5（按 priority）")
    print("="*60)
    per_subj = selector.recommend_per_subject(k_per_subject=5, by='priority')
    for subj, g in per_subj.groupby('subject'):
        print(f"\n【{subj}】")
        print(g.drop(columns=['subject']).to_string(index=False))

    # 面向目标分：114 分（含 5% 安全系数），先每科保底 2 个，再全局补齐
    print("\n" + "="*60)
    print("学习清单：面向 114/150（safety=1.05），每科保底 2 个（按 priority 先选）")
    print("="*60)
    plan = selector.recommend_to_target(
        target_score=114,
        safety=1.05,
        ensure_per_subject_k=2,        # 或 {'计算机网络':2, '数据结构':1}
        rank_by='priority'             # 也可 'gain'
    )
    print(plan.to_string(index=False))
    print("\n--- 摘要 ---")
    print(f"目标分: 114  | 安全系数: 1.05  | 覆盖目标 gain: {plan.attrs['goal_gain']:.2f}")
    print(f"累计 gain: {plan.attrs['achieved_gain']:.2f}  | 选中条目: {len(plan)}")
    print(f"α={plan.attrs['alpha']}, β={plan.attrs['beta']}, κ={plan.attrs['kappa']}, use_subject_caps={plan.attrs['use_subject_caps']}")
