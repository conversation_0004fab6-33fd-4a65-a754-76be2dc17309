from typing import Dict

from rest_framework import serializers

from app.api.dto import (
    AppAddDto, ChatMessageDto, ConversationAdd, DatasetSetDocumentsDto, DatasetCreateDto,
    PromptOptimizeDto, KnowledgeAddDto, KnowledgeEditDto, KnowledgeSearchDto,
    ContentExtractorDto, ProblemSolvingDto, ComplexSentenceAnalysisDto,
    DatasetAddSubtitleDocumentDto, ChapterNoteGeneratorDto, LectureNoteGeneratorDto, CourseNoteTaskCreateDto,
    MathProblemSolvingDto, ProblemSolvingOnlyDto, CodeOptimizationOnlyDto, DatasetAddKnowledgeDocumentDto,
    EnglishReaderReportDto, EnglishReaderAgainReportDto, EnglishReaderQuestionDetailDto,
    CSVideoKnowledgeExtractDto, QuestionKnowledgeExtractDto, CozeConversationAdd, CozeChatMessageDto,
    TestPaperSubmitDto, QuestionAnswerDto, WordReciteBasicPaperSubmitDto, WordRecitePostQuestionDto,
    GaoshuScreenshotQuestionDto, WordRecitePlanUseDto, WordRecitePlanManualChangeDto,
    HuolandeTestDto, CollegeAnalysisDto, KaoYanReviewPlanDto, StudyGuidesDto, StudyGuidesAppDto,
    ExercisesLearnStatusDto, ZhihenRadarDto, CodeExerciseDto, DayiApptDto, SuperviseLearnStatusDto,
    ExercisesLearnStatusDto, ZhihenRadarDto, CodeExerciseDto, DayiApptDto, GeneQuestionDto, KnowledgeAnalysisDto,
    SupervisedLearnUserInitStatus, CollegeAnalysisNewDto, PersonalizedExamSyllabus
)
from django_ext import base_view
from django_ext.base_serializer import MyBaseSerializer
from django_ext.base_validator import BaseValidator


class AppAddValidator(BaseValidator):
    name = serializers.CharField(max_length=32)
    app_key = serializers.CharField(required=False, allow_null=True, allow_blank=True, max_length=32)
    support_params = serializers.ListField(child=serializers.CharField())

    dto_class = AppAddDto


class PromptOptimizeParamValidator(MyBaseSerializer):
    role = serializers.CharField()
    tone = serializers.CharField(required=False, allow_blank=True)
    task = serializers.CharField(required=False, allow_blank=True)
    examples = serializers.CharField(required=False, allow_blank=True)
    instructions = serializers.CharField(required=False, allow_blank=True)
    note = serializers.CharField(required=False, allow_blank=True)


class PromptOptimizeValidator(BaseValidator):
    query_params = PromptOptimizeParamValidator()

    dto_class = PromptOptimizeDto


class ContentExtractorValidator(BaseValidator):
    text = serializers.CharField(required=True)
    dto_class = ContentExtractorDto


class ProblemSolvingValidator(BaseValidator):
    user_question = serializers.CharField(required=True)
    user_requirement = serializers.CharField(required=False, allow_blank=True)  # 允许为空
    dto_class = ProblemSolvingDto


class ProblemSolvingOnlyValidator(BaseValidator):
    conversation_id = serializers.CharField()
    user_question = serializers.CharField(required=False, allow_blank=True)
    question_prompts = serializers.DictField()
    file_objs = serializers.ListField(
        child=serializers.CharField(allow_blank=True),
        required=False,
        default=list  # 默认值为一个空列表
    )
    biz_id = serializers.CharField(required=False, allow_blank=True)
    userinfo = serializers.DictField(required=False, allow_null=True, allow_empty=True)

    dto_class = ProblemSolvingOnlyDto


class CodeOptimizationOnlyValidator(BaseValidator):
    conversation_id = serializers.CharField()
    lang_code = serializers.CharField()
    code_content = serializers.CharField()
    code_prompts = serializers.DictField()
    biz_id = serializers.CharField(required=False, allow_blank=True)
    userinfo = serializers.DictField(required=False, allow_null=True, allow_empty=True)

    dto_class = CodeOptimizationOnlyDto


class MathProblemSolvingValidator(BaseValidator):
    user_question = serializers.CharField(required=False, allow_blank=True)
    image_url = serializers.ListField(
        child=serializers.CharField(allow_blank=True),
        required=False,
        default=list  # 默认值为一个空列表
    )
    conversation_id = serializers.CharField(required=False, allow_blank=True)
    biz_id = serializers.CharField(required=False, allow_blank=True)
    userinfo = serializers.DictField(required=False, allow_null=True, allow_empty=True)

    dto_class = MathProblemSolvingDto


class ComplexSentenceAnalysisValidator(BaseValidator):
    sentence = serializers.CharField(required=True)
    dto_class = ComplexSentenceAnalysisDto


class ChapterNoteGeneratorValidator(BaseValidator):
    lecture_slides = serializers.CharField(required=True)

    dto_class = ChapterNoteGeneratorDto


class LectureNoteGeneratorValidator(BaseValidator):
    subtitle = serializers.ListField(
        child=serializers.DictField(
            child=serializers.CharField(),
            required=True,
        ),
        required=True,
        error_messages={
            'required': 'Subtitles are required and must be a list of dictionaries each with "title" and "content" keys.',
            'not_a_list': 'Subtitles field must be a list.'
        }
    )
    dto_class = LectureNoteGeneratorDto


class ConversationAddValidator(BaseValidator):
    app_id = serializers.CharField()
    biz_id = serializers.CharField(required=False, allow_blank=True)
    inputs = serializers.DictField(required=False)
    pre_prompt = serializers.CharField(required=False, allow_blank=True)
    dataset_no = serializers.CharField(required=False, allow_blank=True)
    document_nos = serializers.ListField(required=False, allow_empty=True, child=serializers.CharField())

    dto_class = ConversationAdd


class CozeConversationAddValidator(BaseValidator):
    app_id = serializers.CharField()
    biz_id = serializers.CharField(required=False, allow_blank=True)

    dto_class = CozeConversationAdd


class ChatMessageValidator(BaseValidator):
    app_id = serializers.CharField()
    biz_id = serializers.CharField(required=False, allow_blank=True)
    userinfo = serializers.DictField(required=False, allow_null=True, allow_empty=True)
    inputs = serializers.DictField(required=False, allow_null=True, allow_empty=True)
    pre_prompt = serializers.CharField(required=False, allow_blank=True)
    query = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    conversation_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    is_override = serializers.BooleanField(required=False, default=False)
    stream = serializers.BooleanField(required=False, default=True)
    message_type = serializers.CharField(required=False, allow_blank=True, default='normal')
    file_objs = serializers.ListField(required=False, allow_empty=True)

    dto_class = ChatMessageDto


class CozeChatMessageValidator(BaseValidator):
    app_id = serializers.CharField()
    biz_id = serializers.CharField(required=False, allow_blank=True)
    userinfo = serializers.DictField(required=False, allow_null=True, allow_empty=True)
    query = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    image = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    conversation_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    stream = serializers.BooleanField(required=False, default=True)

    dto_class = CozeChatMessageDto


class DatasetSetDocumentValidator(BaseValidator):
    document_no = serializers.CharField(required=False, allow_blank=True)
    name = serializers.CharField(required=False, allow_blank=True)
    url = serializers.CharField(required=False, allow_blank=True)


class DatasetCreateValidator(BaseValidator):
    name = serializers.CharField()
    documents = serializers.ListSerializer(
        child=DatasetSetDocumentValidator(), required=False, allow_empty=True)

    dto_class = DatasetCreateDto


class DatasetSetDocumentsValidator(BaseValidator):
    documents = serializers.ListSerializer(child=DatasetSetDocumentValidator())

    dto_class = DatasetSetDocumentsDto


class DatasetAddKnowledgeDocumentValidator(BaseValidator):
    dataset_no = serializers.CharField()
    name = serializers.CharField()
    url = serializers.CharField()

    dto_class = DatasetAddKnowledgeDocumentDto


class DatasetAddSubtitleDocumentValidator(BaseValidator):
    name = serializers.CharField()
    content = serializers.CharField()

    dto_class = DatasetAddSubtitleDocumentDto


class KnowledgeAddValidator(BaseValidator):
    document_no = serializers.CharField()
    name = serializers.CharField()
    definition = serializers.CharField()

    dto_class = KnowledgeAddDto


class KnowledgeEditValidator(BaseValidator):
    name = serializers.CharField()
    definition = serializers.CharField()

    dto_class = KnowledgeEditDto


class KnowledgeSearchValidator(BaseValidator):
    app_id = serializers.CharField(required=False, allow_blank=True)
    query = serializers.CharField()
    biz_id = serializers.CharField(required=False, allow_blank=True)
    userinfo = serializers.DictField(required=False, default=False)
    course_id = serializers.CharField()
    document_no = serializers.CharField(required=False, allow_blank=True)
    search_type = serializers.CharField()
    split_prompt = serializers.CharField(required=False, allow_blank=True)
    local_prompt = serializers.CharField(required=False, allow_blank=True)
    llm_prompt = serializers.CharField(required=False, allow_blank=True)
    deep_local_prompt = serializers.CharField(required=False, allow_blank=True)
    deep_llm_prompt = serializers.CharField(required=False, allow_blank=True)
    deep_question_prompt = serializers.CharField(required=False, allow_blank=True)
    deep_no_question_prompt = serializers.CharField(required=False, allow_blank=True)
    is_recommend = serializers.BooleanField(required=False, default=False)
    enable_recommend_video = serializers.BooleanField(required=False, default=False)

    dto_class = KnowledgeSearchDto


class KnowledgeRegenerateValidator(BaseValidator):
    query = serializers.CharField()
    biz_id = serializers.CharField(required=False, allow_blank=True)
    course_id = serializers.CharField()
    document_no = serializers.CharField(required=False, allow_blank=True)
    search_type = serializers.CharField()
    local_prompt = serializers.CharField(required=False, allow_blank=True)
    llm_prompt = serializers.CharField(required=False, allow_blank=True)

    dto_class = KnowledgeSearchDto


class CourseNoteTaskCreateValidator(BaseValidator):
    course_id = serializers.CharField()
    course_lecture_id = serializers.CharField()
    chapter_id = serializers.CharField()
    chapter_name = serializers.CharField()
    chapter_lecture = serializers.CharField()
    document_nos = serializers.ListField(child=serializers.CharField())

    dto_class = CourseNoteTaskCreateDto


class EnglishReaderQuestionDetailValidator(BaseValidator):
    userinfo = serializers.DictField(
        child=serializers.CharField(),
        required=True,
        error_messages={
            'required': 'userinfo 字段是必填项。',
            'invalid': 'userinfo 必须是一个字典。'
        }
    )

    dto_class = EnglishReaderQuestionDetailDto

    def validate_userinfo(self, value):
        if 'user_id' not in value:
            raise serializers.ValidationError("userinfo 字段必须包含 'user_id'。")
        return value


class SubQuestionValidator(serializers.Serializer):
    sub_question_id = serializers.CharField()
    user_answer = serializers.CharField()
    is_right = serializers.BooleanField()
    question_type = serializers.ListField(child=serializers.CharField())



class GaoshuScreenshotQuestionValidator(BaseValidator):

    pic = serializers.CharField()
    question = serializers.CharField(required=False, allow_blank=True)
    # keywords = serializers.ListField(child=serializers.CharField())
    userinfo = serializers.DictField(
        child=serializers.CharField(),
        required=True,
        error_messages={
            'required': 'userinfo 字段是必填项。',
            'invalid': 'userinfo 必须是一个字典。'
        }
    )
    course_id = serializers.CharField()
    dto_class = GaoshuScreenshotQuestionDto


class EnglishReaderReportValidator(BaseValidator):
    question_id = serializers.CharField()
    userinfo = serializers.DictField(
        child=serializers.CharField(),
        required=True,
        error_messages={
            'required': 'userinfo 字段是必填项。',
            'invalid': 'userinfo 必须是一个字典。'
        }
    )
    answer_detail = serializers.ListField(
        child=SubQuestionValidator()
    )

    dto_class = EnglishReaderReportDto

    def validate_userinfo(self, value):
        if 'user_id' not in value:
            raise serializers.ValidationError("userinfo 字段必须包含 'user_id'。")
        return value


class DayiAppValidator(BaseValidator):
    query = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    conversation_id = serializers.CharField()
    images = serializers.ListField(child=serializers.CharField(required=False, allow_blank=True))
    userinfo = serializers.DictField(required=False)
    scene_info = serializers.DictField(required=False)
    stream = serializers.BooleanField(required=False, default=True)

    dto_class = DayiApptDto


class EnglishReaderReportAgainValidator(BaseValidator):
    question_id = serializers.IntegerField()
    userinfo = serializers.DictField(
        child=serializers.CharField(),
        required=True,
        error_messages={
            'required': 'userinfo 字段是必填项。',
            'invalid': 'userinfo 必须是一个字典。'
        }
    )
    wrong_sub_question_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        default=list
    )

    dto_class = EnglishReaderAgainReportDto

    def validate_userinfo(self, value):
        if 'user_id' not in value:
            raise serializers.ValidationError("userinfo 字段必须包含 'user_id'。")
        return value


class CSVideoKnowledgeExtractValidator(BaseValidator):
    main_subject = serializers.CharField()
    course_section_id = serializers.CharField()
    video_content = serializers.CharField()

    dto_class = CSVideoKnowledgeExtractDto


class QuestionKnowledgeExtractValidator(BaseValidator):
    subject = serializers.CharField(required=False, allow_blank=True)
    question_id = serializers.CharField()

    dto_class = QuestionKnowledgeExtractDto


class QuestionAnswerValidator(BaseValidator):
    question_id = serializers.IntegerField()
    user_answer = serializers.CharField()

    dto_class = QuestionAnswerDto


class TargetCollegeMajorSerializer(serializers.Serializer):

    college_name = serializers.CharField(required=False, allow_blank=True)
    college_code = serializers.CharField(required=False, allow_blank=True)
    major_name = serializers.CharField(required=False, allow_blank=True)
    major_code = serializers.CharField(required=False, allow_blank=True)


class TargetMajorDirectionSerializer(serializers.Serializer):
    name = serializers.CharField(required=False, allow_blank=True)
    code = serializers.CharField(required=False, allow_blank=True)


class CollegeAnalysisValidator(BaseValidator):
    report_id = serializers.CharField(required=True)
    bachelor_level = serializers.CharField(required=False, allow_blank=True)
    bachelor_major = serializers.CharField(required=False, allow_blank=True)
    gpa_range = serializers.CharField(required=False, allow_blank=True)
    major_ranking = serializers.CharField(required=False, allow_blank=True)
    research_experience = serializers.CharField(required=False, allow_blank=True)
    #竞赛经历
    competition_experience = serializers.CharField(required=False, allow_blank=True)
    # #编程语言
    # programming_languages = serializers.CharField(required=False, allow_blank=True)
    english_ability = serializers.CharField(required=False, allow_blank=True)
    math_basis_select = serializers.CharField(required=False, allow_blank=True)
    candidate_status = serializers.CharField(required=False, allow_blank=True)
    intern_experience = serializers.CharField(required=False, allow_blank=True)
    job_experience = serializers.CharField(required=False, allow_blank=True)
    internship_accommodation_needs = serializers.CharField(required=False, allow_blank=True)
    concurrent_preparation = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    regions = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    tuition_sensitivity = serializers.CharField(required=False, allow_blank=True)
    priority_order = serializers.CharField(required=False, allow_blank=True)
    preparation_pain_points = serializers.CharField(required=False, allow_blank=True)
    preferred_services = serializers.CharField(required=False, allow_blank=True)
    admission_baseline = serializers.CharField(required=False, allow_blank=True)

    contact_info = serializers.CharField(required=False, allow_blank=True)

    holland_test_result = serializers.CharField(required=False, allow_blank=True)
    daily_learning_time = serializers.CharField(required=False, allow_blank=True)
    stream = serializers.BooleanField(required=False, default=True)

    # 新增的字段
    is_cross_exam = serializers.CharField(required=False, allow_blank=True)
    target_major_direction = serializers.ListSerializer(child=TargetMajorDirectionSerializer(), required=False, allow_empty=True)
    personal_needs = serializers.CharField(required=False, allow_blank=True)
    master_type = serializers.CharField(required=False, allow_blank=True)
    master_degree_type = serializers.CharField(required=False, allow_blank=True)
    exam_year = serializers.CharField(required=False, allow_blank=True)
    # 新增的 target_college_major 字段
    target_college_major = serializers.ListSerializer(child=TargetCollegeMajorSerializer(), required=False,
                                                      allow_empty=True)
    dto_class = CollegeAnalysisDto


class CollegeAnalysisNewValidator(BaseValidator):
    # report_id = serializers.CharField(required=True)
    bachelor_college_name = serializers.CharField(required=True)
    bachelor_college_code = serializers.CharField(required=True)
    bachelor_major = serializers.CharField(required=True)

    gpa_range = serializers.CharField(required=False, allow_blank=True)
    major_ranking = serializers.CharField(required=False, allow_blank=True)
    research_experience = serializers.CharField(required=False, allow_blank=True)
    competition_experience = serializers.CharField(required=False, allow_blank=True)

    english_ability = serializers.CharField(required=False, allow_blank=True)
    math_basis_select = serializers.CharField(required=False, allow_blank=True)

    regions = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    priority_order = serializers.CharField(required=False, allow_blank=True)
    master_type = serializers.CharField(required=False, allow_blank=True)

    personal_needs = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )

    cross_exam = serializers.DictField(required=True)
    target_college_code = serializers.CharField(required=True)
    target_major_code = serializers.CharField(required=True)
    target_college_name = serializers.CharField(required=True)
    target_major_name = serializers.CharField(required=True)
    holland_test_result = serializers.CharField(required=False, allow_blank=True)
    stream = serializers.BooleanField(required=False, default=True)

    dto_class = CollegeAnalysisNewDto

class KaoYanReviewPlanValidator(BaseValidator):
    report_id = serializers.CharField(required=True)
    target_college_level = serializers.CharField(required=True)

    #是否流式
    stream = serializers.BooleanField(required=False, default=True)

    dto_class = KaoYanReviewPlanDto


class HuolandeTestValidator(BaseValidator):
    part1_realistic = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part1_realistic is required and must be a list of integers.'
        }
    )
    part1_investigative = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part1_investigative is required and must be a list of integers.'
        }
    )
    part1_artistic = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part1_artistic is required and must be a list of integers.'
        }
    )
    part1_social = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part1_social is required and must be a list of integers.'
        }
    )
    part1_enterprising = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part1_enterprising is required and must be a list of integers.'
        }
    )
    part1_conventional = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part1_conventional is required and must be a list of integers.'
        }
    )

    part2_realistic = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part2_realistic is required and must be a list of integers.'
        }
    )
    part2_investigative = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part2_investigative is required and must be a list of integers.'
        }
    )
    part2_artistic = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part2_artistic is required and must be a list of integers.'
        }
    )
    part2_social = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part2_social is required and must be a list of integers.'
        }
    )
    part2_enterprising = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part2_enterprising is required and must be a list of integers.'
        }
    )
    part2_conventional = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part2_conventional is required and must be a list of integers.'
        }
    )


    part3_realistic = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part3_realistic is required and must be a list of integers.'
        }
    )
    part3_investigative = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part3_investigative is required and must be a list of integers.'
        }
    )
    part3_artistic = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part3_artistic is required and must be a list of integers.'
        }
    )
    part3_social = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part3_social is required and must be a list of integers.'
        }
    )
    part3_enterprising = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part3_enterprising is required and must be a list of integers.'
        }
    )
    part3_conventional = serializers.ListField(
        child=serializers.IntegerField(),
        required=True,
        error_messages={
            'required': 'part3_conventional is required and must be a list of integers.'
        }
    )


    part4_realistic = serializers.IntegerField()
    part4_investigative = serializers.IntegerField()
    part4_artistic = serializers.IntegerField()
    part4_social = serializers.IntegerField()
    part4_enterprising = serializers.IntegerField()
    part4_conventional = serializers.IntegerField()


    part5_realistic = serializers.IntegerField()
    part5_investigative = serializers.IntegerField()
    part5_artistic = serializers.IntegerField()
    part5_social = serializers.IntegerField()
    part5_enterprising = serializers.IntegerField()
    part5_conventional = serializers.IntegerField()


    dto_class = HuolandeTestDto





class TestPaperSubmitValidator(BaseValidator):
    record_id = serializers.IntegerField()
    answer_detail = QuestionAnswerValidator()

    dto_class = TestPaperSubmitDto


class WordReciteBasicPaperSubmitValidator(BaseValidator):
    paper_id = serializers.IntegerField()
    answer_detail = serializers.ListSerializer(child=QuestionAnswerValidator())

    dto_class = WordReciteBasicPaperSubmitDto


class WordRecitePlanManualChangeValidator(BaseValidator):
    low = serializers.IntegerField()
    middle = serializers.IntegerField()
    high = serializers.IntegerField()

    dto_class = WordRecitePlanManualChangeDto


class WordRecitePostQuestionValidator(BaseValidator):
    question_id = serializers.IntegerField()
    user_answer = serializers.CharField(max_length=1)

    dto_class = WordRecitePostQuestionDto


class WordRecitePlanUseValidator(BaseValidator):
    plan_record_id = serializers.IntegerField()
    use_status = serializers.CharField(max_length=32)

    dto_class = WordRecitePlanUseDto


class StudyGuidesValidator(BaseValidator):
    """<UNK>"""
    userinfo = serializers.DictField(child=serializers.CharField(),required=True,allow_empty=False)
    subject_id = serializers.CharField(required=False)
    weakness_knowledge = serializers.ListField(
        child=serializers.CharField(allow_blank=True),
        required=True,
        error_messages={}
    )
    section_learn_stat = serializers.ListField(
        child=serializers.DictField(),
        required=True,
        error_messages={}
    )
    dto_class = StudyGuidesDto


class StudyGuidesAppValidator(BaseValidator):

    user_id = serializers.CharField()
    subject_id = serializers.CharField()
    # my_delivery_id = serializers.CharField()
    # outline_number = serializers.CharField()
    course_section_ids = serializers.ListSerializer(child=serializers.CharField(),allow_null=True)
    # stage_name = serializers.CharField()

    dto_class = StudyGuidesAppDto


class ExercisesLearnStatusValidator(BaseValidator):
    subject_id = serializers.CharField(required=False, allow_null=True)
    section_name = serializers.CharField()
    section_kgs = serializers.ListField()
    video_duration = serializers.CharField()
    learn_duration = serializers.CharField()
    video_kg_distribute = serializers.ListField()
    class_exercise = serializers.DictField(required=False, allow_null=True)
    class_test = serializers.DictField()
    userinfo = serializers.DictField()

    dto_class = ExercisesLearnStatusDto


class CodeExerciseValidator(BaseValidator):
    question_type = serializers.ChoiceField(choices=['concept', 'programming', 'only_code'])
    question = serializers.CharField()
    answer = serializers.CharField(required=False, allow_blank=True)
    lang = serializers.CharField(required=False, allow_blank=True)
    stream = serializers.BooleanField(required=False, default=True)

    dto_class = CodeExerciseDto


class ZhihenRadarValidator(BaseValidator):
    assistant_id = serializers.CharField(required=False, allow_blank=True)
    subject_id = serializers.CharField()
    question_id = serializers.CharField()
    answer_id = serializers.CharField()
    question = serializers.CharField()
    answer = serializers.CharField()
    answer_duration = serializers.IntegerField()

    dto_class = ZhihenRadarDto


class SuperviseLearnStatusValidator(BaseValidator):
    user_id = serializers.CharField()
    course_id = serializers.CharField()
    subject_id = serializers.CharField()
    start_date = serializers.CharField()
    end_date = serializers.CharField()

    dto_class = SuperviseLearnStatusDto


class GeneQuestionValidator(BaseValidator):
    subject_id = serializers.CharField()
    main_subject = serializers.CharField()
    knowledge = serializers.CharField()
    chapter = serializers.CharField()

    dto_class = GeneQuestionDto


class KnowledgeAnalysisValidator(BaseValidator):
    query = serializers.CharField()
    biz_id = serializers.CharField(required=False, allow_blank=True)
    userinfo = serializers.DictField(child=serializers.CharField(),required=False,allow_empty=False)
    course_id = serializers.CharField()
    document_no = serializers.CharField(required=False, allow_blank=True)
    split_prompt = serializers.CharField(required=False, allow_blank=True)
    recommend_prompt = serializers.CharField(required=False,allow_blank=True)
    local_prompt = serializers.CharField(required=False, allow_blank=True)
    llm_prompt = serializers.CharField(required=False, allow_blank=True)
    search_type = serializers.ChoiceField(choices=['llm', 'local'])
    is_stream = serializers.BooleanField(required=False, default=True)

    dto_class = KnowledgeAnalysisDto

class SupervisedLearnUserInitStatusValidator(BaseValidator):
    user_id = serializers.CharField(required=True)
    start_date = serializers.CharField(required=False)
    exam_date = serializers.CharField(required=True)
    graduation_info = serializers.DictField(required=False, allow_empty=True)
    target = serializers.DictField(required=False, allow_empty=True)
    education_level = serializers.IntegerField()
    study_status = serializers.IntegerField()
    graduation_years = serializers.IntegerField(required=False,allow_null=True)
    study_stage = serializers.IntegerField(required=False, allow_null=True, default=None)
    academic_performance = serializers.IntegerField(required=False,allow_null=True)
    english_level = serializers.IntegerField(required=False,allow_null=True)
    math_subjects = serializers.ListField(required=False,allow_empty=True,child=serializers.IntegerField())
    math_mastery = serializers.IntegerField(required=False,allow_null=True)


    dto_class = SupervisedLearnUserInitStatus

class SupervisedLearnUserStageStatusValidator(BaseValidator):
    user_id = serializers.CharField()
    course_id = serializers.CharField()

    dto_class = SuperviseLearnStatusDto


class PersonalizedExamSyllabusValidator(BaseValidator):
    user_id = serializers.CharField(required=True)

    dto_class = PersonalizedExamSyllabus
