import re

from django.db import models
from ._base import BaseModel


class EnglishReaderPublications(BaseModel):
    publications_content = models.TextField('外刊内容', null=True)
    article_translate = models.TextField('外刊翻译', null=True)
    publications_name = models.CharField('外刊名称', max_length=32, null=True)
    publications_source = models.CharField('外刊来源', max_length=32, null=True)

    class Meta:
        verbose_name = verbose_name_plural = '外刊阅读_外刊材料'
        db_table = 'ai_english_reader_publications'


class EnglishReaderStrategy(BaseModel):
    strategy_content = models.TextField('策略内容', null=True)
    strategy_name = models.CharField('策略名称', max_length=32, null=True)
    strategy_year = models.CharField('年份', max_length=32, null=True)

    class Meta:
        verbose_name = verbose_name_plural = '外刊阅读_真题策略'
        db_table = 'ai_english_reader_strategy'


# ===== 废弃
class EnglishReaderQuestionBank(BaseModel):
    strategy_id = models.TextField('策略id', null=True, blank=True)
    publication_id = models.TextField('外刊id', null=True, blank=True)
    article = models.TextField('阅读文章', null=True)
    # question = models.TextField('阅读问题', null=True)
    option = models.TextField('选项', null=True)
    answer = models.TextField('答案', null=True)
    analysis = models.TextField('解析', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '外刊阅读_题库'
        db_table = 'ai_english_reader_question_bank'
# ===== 废弃


class EnglishWaikanQuestionBank(BaseModel):
    publication = models.ForeignKey(EnglishReaderPublications, on_delete=models.CASCADE, db_constraint=False)
    strategy = models.ForeignKey(EnglishReaderStrategy, on_delete=models.CASCADE, db_constraint=False)
    article = models.TextField('阅读文章', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '外刊阅读_新题库'
        db_table = 'ai_english_waikan_question_bank'


class EnglishWaikanSubQuestion(BaseModel):
    question = models.ForeignKey(EnglishWaikanQuestionBank, on_delete=models.CASCADE, db_constraint=False)
    question_stem = models.TextField('子题内容', null=True)
    question_options = models.JSONField('子题选项', null=True)
    question_answer = models.CharField('答案', max_length=8, blank=True)
    # 区分是配套题目还是专项练习
    question_feature = models.CharField('题目性质', max_length=16, blank=True)
    # 题目类型，类似细节题等
    question_type = models.JSONField('题目类型', null=True)
    question_analysis = models.TextField('题目解析', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '外刊阅读_新题库'
        db_table = 'ai_english_waikan_sub_question'



class UserQuestion(BaseModel):
    user_id = models.TextField('用户id', null=True)
    question_id = models.JSONField('已做过题列表', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '用户信息记录'
        db_table = 'ai_english_waikan_user_question'


class EnglishWaikanTestRecord(BaseModel):
    user_id = models.TextField('用户id', null=True)
    question = models.ForeignKey(EnglishWaikanQuestionBank, on_delete=models.CASCADE, db_constraint=False)
    sub_question_ids = models.JSONField('已经做过的子题列表', null=True)
    question_feature = models.CharField('子题性质', max_length=16, blank=True)   # 首次出题/再次出题
    is_exception = models.BooleanField(default=False, verbose_name='是否异常')
    exception_reason = models.TextField(null=True, verbose_name='异常原因')
    exception_image = models.TextField(null=True, verbose_name='异常图片')

    class Meta:
        verbose_name = verbose_name_plural = '用户信息记录'
        db_table = 'ai_english_waikan_test_record'
