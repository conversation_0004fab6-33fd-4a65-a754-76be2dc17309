import datetime
import logging

from django.utils import timezone

from app.models import MessageTask, Message
from app.services.app_generate_service import AppGenerateService

logger = logging.getLogger(__name__)


class MessageTaskService:

    @classmethod
    def process_task(cls, task_id, is_retry=False):
        task: MessageTask = MessageTask.objects.filter(
            task_id=task_id
        ).first()
        if not task or task.process_status == MessageTask.ProcessStatus.success:
            return

        task.process_status = MessageTask.ProcessStatus.ing
        task.save(update_fields=['process_status', 'modified_time'])

        if is_retry:
            task.retry_times = task.retry_times + 1
            task.last_retry_time = timezone.now()

        try:
            AppGenerateService.generate_for_task(task)

            # 处理消除返回异常
            message = Message.objects.get(id=task.message_id)
            if message.status == 'error':
                raise Exception(message.error)

            task.process_status = MessageTask.ProcessStatus.success
            task.save()
        except Exception as e:
            logger.exception(e)
            task.process_status = MessageTask.ProcessStatus.fail
            interval_min = task.retry_times ** 2
            task.next_retry_time = timezone.now() + datetime.timedelta(minutes=interval_min)
            task.save()
