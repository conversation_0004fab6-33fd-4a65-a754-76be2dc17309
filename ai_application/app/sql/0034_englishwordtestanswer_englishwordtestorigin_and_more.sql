--
-- <PERSON>reate model EnglishWordTestAnswer
--
CREATE TABLE `ai_data_english_word_test_answer`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool    NOT NULL,
    `right_num`     integer NOT NULL
);
--
-- <PERSON>reate model EnglishWordTestOrigin
--
CREATE TABLE `ai_data_english_word_test_origin`
(
    `id`             bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`       datetime(6) NOT NULL,
    `modified_time`  datetime(6) NOT NULL,
    `is_deleted`     bool    NOT NULL,
    `year`           integer NOT NULL,
    `origin_content` longtext NULL
);
--
-- <PERSON>reate model EnglishWordTestRecord
--
CREATE TABLE `ai_data_english_word_test_record`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `user_id`       varchar(100) NOT NULL,
    `question_num`  integer      NOT NULL
);
--
-- Change Meta options on englishwordtestquestion
--
-- (no-op)
--
-- Change Meta options on englishwordteststrategy
--
-- (no-op)
--
-- Add field en_word to englishwordtestquestion
--
ALTER TABLE `ai_data_english_word_test_question`
    ADD COLUMN `en_word_id` bigint NULL;
--
-- Add field options to englishwordtestquestion
--
ALTER TABLE `ai_data_english_word_test_question`
    ADD COLUMN `options` json NULL;
--
-- Add field question_type to englishwordtestquestion
--
ALTER TABLE `ai_data_english_word_test_question`
    ADD COLUMN `question_type` varchar(16) DEFAULT '' NOT NULL;
ALTER TABLE `ai_data_english_word_test_question`
    ALTER COLUMN `question_type` DROP DEFAULT;
--
-- Create model EnglishWordTestRecordDetail
--
CREATE TABLE `ai_data_english_word_test_record_detail`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool   NOT NULL,
    `question_id`   bigint NOT NULL,
    `record_id`     bigint NOT NULL
);
--
-- Create model EnglishWordTestAnswerDetail
--
CREATE TABLE `ai_data_english_word_test_answer_detail`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool        NOT NULL,
    `user_answer`   varchar(16) NOT NULL,
    `is_right`      bool        NOT NULL,
    `answer_id`     bigint      NOT NULL,
    `question_id`   bigint      NOT NULL,
    `record_id`     bigint      NOT NULL
);
--
-- Add field record to englishwordtestanswer
--
ALTER TABLE `ai_data_english_word_test_answer`
    ADD COLUMN `record_id` bigint NOT NULL;
--
-- Add field test_origin to englishwordteststrategy
--
ALTER TABLE `ai_data_english_word_test_strategy`
    ADD COLUMN `test_origin_id` bigint NULL;
CREATE INDEX `ai_data_english_word_test_answer_add_time_b91870c9` ON `ai_data_english_word_test_answer` (`add_time`);
CREATE INDEX `ai_data_english_word_test_origin_add_time_3099dc73` ON `ai_data_english_word_test_origin` (`add_time`);
CREATE INDEX `ai_data_english_word_test_record_add_time_58fc2e44` ON `ai_data_english_word_test_record` (`add_time`);
CREATE INDEX `ai_data_english_word_test_record_user_id_351f052e` ON `ai_data_english_word_test_record` (`user_id`);
CREATE INDEX `ai_data_english_word_test_question_en_word_id_abade39d` ON `ai_data_english_word_test_question` (`en_word_id`);
CREATE INDEX `ai_data_english_word_test_record_detail_add_time_dae8c360` ON `ai_data_english_word_test_record_detail` (`add_time`);
CREATE INDEX `ai_data_english_word_test_record_detail_question_id_c98b0d2c` ON `ai_data_english_word_test_record_detail` (`question_id`);
CREATE INDEX `ai_data_english_word_test_record_detail_record_id_a2121753` ON `ai_data_english_word_test_record_detail` (`record_id`);
CREATE INDEX `ai_data_english_word_test_answer_detail_add_time_30d751a3` ON `ai_data_english_word_test_answer_detail` (`add_time`);
CREATE INDEX `ai_data_english_word_test_answer_detail_answer_id_dd9fa106` ON `ai_data_english_word_test_answer_detail` (`answer_id`);
CREATE INDEX `ai_data_english_word_test_answer_detail_question_id_10c9b32d` ON `ai_data_english_word_test_answer_detail` (`question_id`);
CREATE INDEX `ai_data_english_word_test_answer_detail_record_id_1e2f26dc` ON `ai_data_english_word_test_answer_detail` (`record_id`);
CREATE INDEX `ai_data_english_word_test_answer_record_id_9274f8b1` ON `ai_data_english_word_test_answer` (`record_id`);
CREATE INDEX `ai_data_english_word_test_strategy_test_origin_id_00bd720a` ON `ai_data_english_word_test_strategy` (`test_origin_id`);