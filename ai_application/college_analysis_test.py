import os
import django
import csv

from app.models import UndergraduateMajorCourse


def import_undergraduate_major_courses():
    csv_file_path = '本科专业及主干课程表格.csv'

    with open(csv_file_path, mode='r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)

        for row in reader:
            first_discipline = row.get('一级学科', '').strip()
            second_category = row.get('二级门类', '').strip()
            core_courses = row.get('主干课程（按核心程度排序）', '').strip()

            # 创建记录
            UndergraduateMajorCourse.objects.create(
                first_discipline=first_discipline,
                second_category=second_category,
                core_courses=core_courses
            )

    print(f"✅ 成功导入 {UndergraduateMajorCourse.objects.count()} 条本科专业主干课程数据")


