from pydantic import ConfigDict

from app.core.model_runtime.model_providers._base.ai_model import AIModel
from app.core.model_runtime.model_providers._base.model_provider import ModelProvider
from django_ext.base_dto_model import MyBaseModel


class ProviderModelBundle(MyBaseModel):
    provider_instance: ModelProvider
    model_type_instance: AIModel

    # pydantic configs
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        protected_namespaces=()
    )
