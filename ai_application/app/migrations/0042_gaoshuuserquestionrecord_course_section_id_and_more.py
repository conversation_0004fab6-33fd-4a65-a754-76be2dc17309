# Generated by Django 4.2 on 2025-04-10 12:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0041_gaoshuuserquestionrecord'),
    ]

    operations = [
        migrations.AddField(
            model_name='gaoshuuserquestionrecord',
            name='course_section_id',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='gaoshuuserquestionrecord',
            name='message',
            field=models.ForeignKey(db_constraint=False, default=None, on_delete=django.db.models.deletion.CASCADE, to='app.message'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='gaoshuuserquestionrecord',
            name='knowledge_list',
            field=models.J<PERSON><PERSON>ield(null=True),
        ),
    ]
