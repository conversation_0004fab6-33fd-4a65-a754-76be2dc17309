# Generated by Django 4.2 on 2025-04-17 05:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0043_merge_20250417_1329'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentLearnStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(db_index=True, max_length=100, verbose_name='用户ID')),
                ('task_id', models.Char<PERSON><PERSON>(max_length=36, null=None, unique=True, verbose_name='任务id')),
                ('query', models.TextField(null=True, verbose_name='请求内容')),
                ('analysis', models.TextField(null=True, verbose_name='解析内容')),
                ('status', models.CharField(blank=True, max_length=100, verbose_name='解析状态')),
                ('fail_reason', models.TextField(null=True, verbose_name='失败原因')),
            ],
            options={
                'verbose_name': '学情分析记录',
                'verbose_name_plural': '学情分析记录',
                'db_table': 'ai_student_learn_stat',
            },
        ),
    ]
