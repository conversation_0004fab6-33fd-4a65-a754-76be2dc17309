--
-- Create model Account
--
CREATE TABLE `ai_account`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `name`          varchar(32)  NOT NULL,
    `api_key`       varchar(32)  NOT NULL UNIQUE,
    `secret_key`    varchar(100) NOT NULL
);
--
-- Create model App
--
CREATE TABLE `ai_app`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool        NOT NULL,
    `app_no`        varchar(32) NOT NULL UNIQUE,
    `mode`          varchar(32) NOT NULL,
    `name`          varchar(32) NOT NULL,
    `is_public`     bool        NOT NULL,
    `account_id`    bigint      NOT NULL
);
--
-- <PERSON>reate model AppModelConfig
--
CREATE TABLE `ai_app_model_config`
(
    `id`             bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`       datetime(6) NOT NULL,
    `modified_time`  datetime(6) NOT NULL,
    `is_deleted`     bool        NOT NULL,
    `app_no`         varchar(32) NOT NULL,
    `mode`           varchar(32) NOT NULL,
    `run_type`       varchar(16) NOT NULL,
    `model_provider` varchar(32) NOT NULL,
    `model_id`       varchar(32) NOT NULL,
    `third_app_key`  varchar(32) NOT NULL,
    `support_params` json NULL
);
--
-- Create model Conversation
--
CREATE TABLE `ai_conversation`
(
    `id`                  bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`            datetime(6) NOT NULL,
    `modified_time`       datetime(6) NOT NULL,
    `is_deleted`          bool         NOT NULL,
    `conversation_no`     varchar(36)  NOT NULL UNIQUE,
    `model_provider`      varchar(32)  NOT NULL,
    `model_id`            varchar(32)  NOT NULL,
    `inputs`              json NULL,
    `model_params`        json NULL,
    `pre_prompt`          longtext NULL,
    `name`                varchar(200) NOT NULL,
    `status`              varchar(32)  NOT NULL,
    `app_id`              bigint       NOT NULL,
    `app_model_config_id` bigint       NOT NULL,
    `from_account_id`     bigint       NOT NULL
);
--
-- Create model Message
--
CREATE TABLE `ai_message`
(
    `id`                  bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`            datetime(6) NOT NULL,
    `modified_time`       datetime(6) NOT NULL,
    `is_deleted`          bool             NOT NULL,
    `message_no`          varchar(36)      NOT NULL UNIQUE,
    `model_provider`      varchar(32)      NOT NULL,
    `model_id`            varchar(32)      NOT NULL,
    `query`               longtext NULL,
    `message`             json NULL,
    `message_tokens`      integer          NOT NULL,
    `answer`              longtext NULL,
    `answer_tokens`       integer          NOT NULL,
    `total_tokens`        integer          NOT NULL,
    `response_latency`    double precision NOT NULL,
    `status`              varchar(32)      NOT NULL,
    `stopped_by`          varchar(16)      NOT NULL,
    `error`               longtext NULL,
    `replaced_message_no` varchar(36)      NOT NULL,
    `app_id`              bigint           NOT NULL,
    `app_model_config_id` bigint           NOT NULL,
    `conversation_id`     bigint           NOT NULL,
    `from_account_id`     bigint           NOT NULL
);
--
-- Add field app_model_config to app
--
ALTER TABLE `ai_app`
    ADD COLUMN `app_model_config_id` bigint NOT NULL;
CREATE INDEX `ai_account_add_time_75691a06` ON `ai_account` (`add_time`);
CREATE INDEX `ai_app_add_time_4ba905a0` ON `ai_app` (`add_time`);
CREATE INDEX `ai_app_account_id_1b710482` ON `ai_app` (`account_id`);
CREATE INDEX `ai_app_model_config_add_time_4d0d7ede` ON `ai_app_model_config` (`add_time`);
CREATE INDEX `ai_app_model_config_app_no_9076a1ca` ON `ai_app_model_config` (`app_no`);
CREATE INDEX `ai_conversation_add_time_ad44754f` ON `ai_conversation` (`add_time`);
CREATE INDEX `ai_conversation_app_id_dcea0c56` ON `ai_conversation` (`app_id`);
CREATE INDEX `ai_conversation_app_model_config_id_dd9235bc` ON `ai_conversation` (`app_model_config_id`);
CREATE INDEX `ai_conversation_from_account_id_c07d8bf1` ON `ai_conversation` (`from_account_id`);
CREATE INDEX `ai_message_add_time_53234ac8` ON `ai_message` (`add_time`);
CREATE INDEX `ai_message_app_id_3feffcc7` ON `ai_message` (`app_id`);
CREATE INDEX `ai_message_app_model_config_id_4cd50a42` ON `ai_message` (`app_model_config_id`);
CREATE INDEX `ai_message_conversation_id_0ad97782` ON `ai_message` (`conversation_id`);
CREATE INDEX `ai_message_from_account_id_f384abd7` ON `ai_message` (`from_account_id`);
CREATE INDEX `ai_app_app_model_config_id_9297e0b0` ON `ai_app` (`app_model_config_id`);