import json
import logging

from app.constants.app import AppMode
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import PromptTemplate

logger = logging.getLogger(__name__)


class DocAICreateWorkflow(BaseWorkflow):
    def run(self):
        user_inputs = self.application_generate_entity.inputs.copy()

        prompt_template = PromptTemplate.objects.get(app_no='document_proofreader_ai_create')
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
        )

        model_conf = get_llm_model_config(
            app_model_conf=self.application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        response = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=self.stream
        )
        self._handle_node_response(
            response,
            self.stream,
            tracing_info={
                'tracing_type': 'doc_proofreader_ai_create',
                'query': json.dumps(user_inputs, ensure_ascii=False),
            }
        )
        self._publish_workflow_end_message()
