from lxml import etree


def parse_mathml(mml_content):
    """解析MathML内容并提取数学表达式"""
    # 解析XML内容
    root = etree.fromstring(mml_content.encode('utf-8'))

    # 定义一个递归函数处理MathML元素
    def process_element(element):
        result = []
        for child in element:
            # 处理数字标签
            if child.tag.endswith('mn'):
                result.append(child.text)
            # 处理运算符标签
            elif child.tag.endswith('mo'):
                # 转换特殊符号（如×）
                text = child.text
                if text == '&#xD7;':
                    result.append('×')
                else:
                    result.append(text)
            # 处理上标标签
            elif child.tag.endswith('msup'):
                # 递归处理底数和指数
                base = process_element([child[0]])
                exp = process_element([child[1]])
                result.append(f"{''.join(base)}^{''.join(exp)}")
            # 处理其他标签（可根据需要扩展）
            else:
                result.extend(process_element(child))
        return result

    # 处理根元素下的所有内容
    parts = process_element(root)
    return ''.join(parts)
