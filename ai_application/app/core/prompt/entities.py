from enum import Enum

from pydantic import ConfigDict, Field, BaseModel, field_validator

from app.core.output_parsers.regex import RegexParser
from app.core.prompt.utils.prompt_template_parser import PromptTemplateParser
from django_ext.base_dto_model import MyBaseModel


class PromptMessageRole(Enum):
    """
    Enum class for prompt message.
    """
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"


class PromptMessageContentType(Enum):
    """
    Enum class for prompt message content type.
    """

    TEXT = "text"
    IMAGE = "image"
    IMAGE_URL = "image_url"


class PromptMessageContent(MyBaseModel):
    """
    Model class for prompt message content.
    """

    type: PromptMessageContentType


class TextPromptMessageContent(PromptMessageContent):
    """
    Model class for text prompt message content.
    """

    type: PromptMessageContentType = PromptMessageContentType.TEXT
    data: str


class ImagePromptMessageContent(PromptMessageContent):
    """
    Model class for image prompt message content.
    """

    class DETAIL(Enum):
        LOW = "low"
        HIGH = "high"
        AUTO = "auto"

    type: PromptMessageContentType = PromptMessageContentType.IMAGE
    url: str = Field(default="", description="the url of multi-modal file")
    detail: DETAIL = DETAIL.AUTO

    @property
    def data(self):
        return self.url
        # return self.url or f"data:{self.mime_type};base64,{self.base64_data}"


class PromptMessage(MyBaseModel):
    """
    Model class for prompt message.
    """
    role: PromptMessageRole
    content: str | list[PromptMessageContent] | None = None
    name: str | None = None


class UserPromptMessage(PromptMessage):
    role: PromptMessageRole = PromptMessageRole.USER


class AssistantPromptMessage(PromptMessage):
    class ToolCall(BaseModel):
        """
        Model class for assistant prompt message tool call.
        """

        class ToolCallFunction(BaseModel):
            """
            Model class for assistant prompt message tool call function.
            """

            name: str
            arguments: str

        id: str
        type: str
        function: ToolCallFunction

        @field_validator("id", mode="before")
        @classmethod
        def transform_id_to_str(cls, value) -> str:
            if not isinstance(value, str):
                return str(value)
            else:
                return value

    role: PromptMessageRole = PromptMessageRole.ASSISTANT
    tool_calls: list[ToolCall] = []
    reasoning_content: str = ''


class SystemPromptMessage(PromptMessage):
    role: PromptMessageRole = PromptMessageRole.SYSTEM


class ToolPromptMessage(PromptMessage):
    """
    Model class for tool prompt message.
    """

    role: PromptMessageRole = PromptMessageRole.TOOL
    tool_call_id: str

    def is_empty(self) -> bool:
        """
        Check if prompt message is empty.

        :return: True if prompt message is empty, False otherwise
        """
        if not super().is_empty() and not self.tool_call_id:
            return False

        return True


class PromptParser(MyBaseModel):
    prompt_template: PromptTemplateParser
    input_variables: dict
    output_parser: RegexParser | None = None

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def get_prompt(self):
        return self.prompt_template.format(self.input_variables)

    def parse_answer(self, answer: str) -> dict[str, str]:
        return self.output_parser.parse(answer)
