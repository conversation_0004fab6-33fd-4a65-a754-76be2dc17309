import logging
import time

from celery import shared_task

from app.core.rag.index_processor.index_processor_factory import IndexProcessorFactory
from app.models import Dataset, DocumentSegment, Knowledge, DatasetDocument

logger = logging.getLogger(__name__)


@shared_task(queue='dataset')
def clean_document_task(
        dataset_no: str,
        document_nos: list[str],
        index_type: str
):
    logger.info('Start clean document when document deleted: {}'.format(document_nos))
    start_at = time.perf_counter()

    dataset: Dataset = Dataset.objects.filter(
        is_deleted=False, dataset_no=dataset_no).first()
    # 资料库不存在，则不处理
    if not dataset:
        return

    del_document_ids = list(DatasetDocument.objects.filter(
        document_no__in=document_nos).values_list('id', flat=True))

    try:
        segments = DocumentSegment.objects.filter(
            is_deleted=False, dataset_document__document_no__in=document_nos
        )

        index_node_ids = [segment.index_node_id for segment in segments]
        if index_node_ids:
            index_node_ids = [segment.index_node_id for segment in segments]
            index_processor = IndexProcessorFactory(index_type).init_index_processor()
            index_processor.clean(dataset, index_node_ids)

        DocumentSegment.objects.filter(
            is_deleted=False, dataset_document__document_no__in=document_nos
        ).update(is_deleted=True)
        Knowledge.objects.filter(
            is_deleted=False, dataset_document_id__in=del_document_ids
        ).update(is_deleted=True)

        end_at = time.perf_counter()
        logger.info('Cleaned document when document deleted: {} latency: {}'.format(document_nos, end_at - start_at),)
    except Exception as e:
        logger.error("Cleaned document when document deleted failed")
        logger.exception(e)
