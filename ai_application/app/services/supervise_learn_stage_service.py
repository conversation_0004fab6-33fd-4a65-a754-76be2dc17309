import re
import os
import csv
import json
import logging
import pdb


from django.forms import model_to_dict

from app.api.dto import ChatMessageDto
from datetime import date,datetime
from api_client.yantucs_data.client import yantucs_data_client
from app.services.learn_status_service import generate_learning_data, submit_learn_status, check_learn_status
from app.services.supervise_learn_status_service import SuperviseLearnStatusService
from app.models.main_subject import StudentLearnStat,SuperviseLearnStageStat
from django.utils import timezone
from app.services.app_generate_service import AppGenerateService
from app.models import App, Account

from app.models import PromptTemplate

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.join(current_dir, 'Subject.json')

with open(file_path, 'r', encoding='utf-8') as f:
    json_data_string = f.read()

    data = json.loads(json_data_string)

data = json.loads(json_data_string).get("data")


logger = logging.getLogger(__name__)

class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, date):
            return obj.isoformat()
        return super().default(obj)

class SuperviseLearnStageService:
    

    @classmethod
    def gen_analysis_report(cls, record: SuperviseLearnStageStat):
        
        content = {}
        processed_inputs = None
        logger.info(f"Starting analysis for record: {record.id}")
        try:
            logger.info("Getting account and app instances")
            from_account = Account.objects.first()
            
            math_app: App = App.objects.filter(is_deleted=False, app_type='supervise_learn_stage_math').first()
            if not math_app:
                raise AppNotFoundError("未找到数学评分对应的 App 实例")
            
            eng_app: App = App.objects.filter(is_deleted=False, app_type='supervise_learn_stage_eng').first()
            if not eng_app:
                raise AppNotFoundError("未找到英语评分对应的 App 实例")
            
            pol_app: App = App.objects.filter(is_deleted=False, app_type='supervise_learn_stage_pol').first()
            if not pol_app:
                raise AppNotFoundError("未找到政治评分对应的 App 实例")

            professional_app: App = App.objects.filter(is_deleted=False, app_type='supervise_learn_stage_pro').first()
            if not professional_app:
                raise AppNotFoundError("未找到专业课评分对应的 App 实例")
            
            inputs = yantucs_data_client.get_user_subjects_stat(
                record.user_id, 
                record.course_id
                )

            processed_data = cls.parse_learning_data(inputs)
            processed_inputs = {
                "Math": [],
                "English": [],
                "Polictics": [],
                "Professional": []
            }

            for key,item in processed_data.items():

                if item.get("category") == "数学":
                    processed_inputs["Math"].append({
                        "subject_name": item.get("subject_name"),
                        "tests": item.get("tests", [])
                    })
                elif item.get("category") == "政治":
                    processed_inputs["Polictics"].append({
                        "subject_name": item.get("subject_name"),
                        "tests": item.get("tests", [])
                    })
                elif item.get("category") == "英语":
                    processed_inputs["English"].append({
                        "subject_name": item.get("subject_name"),
                        "tests": item.get("tests", [])
                    })
                elif item.get("category") == "专业课":
                    processed_inputs["Professional"].append({
                        "subject_name": item.get("subject_name"),
                        "tests": item.get("tests", [])
                    })
            

            pdb.set_trace()
            if processed_inputs["Math"]:

                math_pt = PromptTemplate.objects.filter(app_no='supervise_learn_stage_math').first()
                math_input = {processed_inputs.get("Math")[0].get("subject_name"):processed_inputs["Math"]}
                math_chat_dto = ChatMessageDto(
                    app_id = math_app.app_no,
                    query = json.dumps(math_input, ensure_ascii=False, cls=DateTimeEncoder), 
                    stream = False,
                    inputs = math_input,
                    pre_prompt = math_pt.prompt_content
                )
                math_res = AppGenerateService.generate(math_chat_dto, from_account)

            if processed_inputs["English"]:

                eng_pt = PromptTemplate.objects.filter(app_no='supervise_learn_stage_eng').first()
                eng_input = {processed_inputs.get("English")[0].get("subject_name"):processed_inputs["English"]}
                eng_chat_dto = ChatMessageDto(
                    app_id = eng_app.app_no,
                    query = json.dumps(eng_input, ensure_ascii=False, cls=DateTimeEncoder), 
                    stream = False,
                    inputs = eng_input,
                    pre_prompt = eng_pt.prompt_content
                )
                eng_res = AppGenerateService.generate(eng_chat_dto, from_account)

            if processed_inputs["Polictics"]:

                pol_pt = PromptTemplate.objects.filter(app_no='supervise_learn_stage_pol').first()
                pol_input = {processed_inputs.get("Polictics")[0].get("subject_name"):processed_inputs["Polictics"]}
                pol_chat_dto = ChatMessageDto(
                    app_id = pol_app.app_no,
                    query = json.dumps(pol_input, ensure_ascii=False, cls=DateTimeEncoder), 
                    stream = False,
                    inputs = pol_input,
                    pre_prompt = pol_pt.prompt_content
                )
                pol_res = AppGenerateService.generate(pol_chat_dto, from_account)
            
            if processed_inputs["Professional"]:

                pro_pt = PromptTemplate.objects.filter(app_no='supervise_learn_stage_pro').first()
                professional_input = {processed_inputs.get("Professional")[0].get("subject_name"):processed_inputs["Professional"]}
                pro_chat_dto = ChatMessageDto(
                    app_id = professional_app.app_no,
                    query = json.dumps(professional_input, ensure_ascii=False, cls=DateTimeEncoder), 
                    stream = False,
                    inputs = professional_input,
                    pre_prompt = pro_pt.prompt_content
                )
                pro_res = AppGenerateService.generate(pro_chat_dto, from_account)

            math_content = math_res.get('answer', '')
            eng_content = eng_res.get('answer', '')
            pol_content = pol_res.get('answer', '')
            pro_content = pro_res.get('answer', '')
            content = {
                "eng_content": eng_content,
                "pro_content": pro_content,
                "pol_content": pol_content,
                "math_content": math_content
            }
            # record.query = processed_inputs["Professional"] if processed_inputs["Professional"] is not None else []
            record.analysis = content
            record.status = "SUCCESS"
            record.save()
            return content


        except Exception as e:
            logger.error(f"Error in gen_analysis_report for record {record.id}", exc_info=True)
            if record:
                # record.query = processed_inputs["Professional"] if processed_inputs["Professional"] is not None else []
                record.fail_reason = str(e)
                record.status = "FAIL"
                record.save()
            raise
    @classmethod
    def get_subject_name(cls, subject_code):

        try:
            subject_info = data.get(str(subject_code))  # 确保 subject_code 是字符串
            if subject_info and isinstance(subject_info, dict):
                subject_name = subject_info.get("subject_name")
                category = subject_info.get("category", {}).get("category_name", "未分类")
                if subject_name:
                    return subject_name, category
            
            # 如果没有找到对应信息，返回默认值
            return f"未知科目({subject_code})", "未分类"
            
        except Exception as e:
            logger.error(f"Error getting subject name for code {subject_code}: {str(e)}")
            return f"错误科目({subject_code})", "未分类"
    

    @classmethod
    def parse_learning_data(cls,response):
        
        subjects = {}
        user_id = response.get('user_id')
        course_id = response.get('course_id')
        start_time = response.get("start_time")
        match = re.match(r"^(\d{4}-\d{2}-\d{2}).*", start_time)
        start_date_str = match.group(1) if match else None
        end_date = datetime.now().date()
        end_date_str = end_date.isoformat() if end_date else None
        outlines = response.get("my_outlines", [])
        for outline in outlines:
            subject_id = outline.get("subject_id")
            subject_name,category = cls.get_subject_name(subject_id)
            if subject_name:
                subjects[subject_id] = {
                    "category" : category,
                    "subject_name": subject_name,
                    "tests": []
                }
            data1 = yantucs_data_client.get_user_supervise_learn_stat(
                user_id,
                course_id,
                subject_id,
                start_date_str,
                end_date_str
            )            
            has_plan = data1.get("has_plan", False)
            
            if has_plan:
                plan_info_list = data1.get('plan_info', {})

                for plan_info in plan_info_list:
                    course_sections = plan_info.get('course_sections', [])

                    for course_section in course_sections:
                        course_section_name = course_section.get('course_section_name')
                        # 转换为更简洁的课节类型表述
                        end_date = plan_info.get('day')
                        is_learned = "已学" if plan_info.get('is_learned', False) else "未学"

                        course_section_info = {
                            "测试名": course_section_name,
                            "学习状态": is_learned,
                        }

                        if course_section.get('test_list'):
                            test_list = course_section.get('test_list')
                            if test_list:
                                first_test = test_list[0]
                                total_questions = int(first_test.get('question_mark_sum', 0))
                                total_score = int(first_test.get('score_sum', 0))
                                correct_rate = (total_score / total_questions * 100) if total_questions else 0
                                course_section_info.update({
                                    "测试类型": SuperviseLearnStatusService._get_test_type(first_test.get('test_type')),
                                    "试卷名称": first_test.get('paper_name', ''),
                                    "正确率": correct_rate,
                                    "截止时间": end_date,
                                    "完成时间": first_test.get('answer_end_time', '').split(' ')[0],
                                    "测试题": SuperviseLearnStatusService._parse_questions(first_test.get('questions', []), subject_id)
                                })

                            subjects[subject_id]["tests"].append(course_section_info)
            else:
                np_plan_info_list = data1.get('no_plan_info', {})

                for no_plan_info in np_plan_info_list:
                    course_sections = no_plan_info.get('course_sections', [])

                    for course_section in course_sections:
                        course_section_name = course_section.get('course_section_name')
                        # 转换为更简洁的课节类型表述
                        end_date = no_plan_info.get('day')
                        is_learned = "已学" if no_plan_info.get('is_learned', False) else "未学"

                        course_section_info = {
                            "测试名": course_section_name,
                            "学习状态": is_learned,
                        }

                        if course_section.get('test_list'):
                            test_list = course_section.get('test_list')
                            if test_list:
                                first_test = test_list[0]
                                total_questions = int(first_test.get('question_mark_sum', 0))
                                total_score = int(first_test.get('score_sum', 0))
                                correct_rate = (total_score / total_questions * 100) if total_questions else 0
                                course_section_info.update({
                                    "测试类型": SuperviseLearnStatusService._get_test_type(first_test.get('test_type')),
                                    "试卷名称": first_test.get('paper_name', ''),
                                    "正确率": correct_rate,
                                    "截止时间": end_date,
                                    "完成时间": first_test.get('answer_end_time', '').split(' ')[0],
                                    "测试题": SuperviseLearnStatusService._parse_questions(first_test.get('questions', []), subject_id)
                                })

                            subjects[subject_id]["tests"].append(course_section_info)

        return subjects
