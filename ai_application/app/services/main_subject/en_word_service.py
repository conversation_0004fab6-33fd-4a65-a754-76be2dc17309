from collections import defaultdict

from django.core.cache import cache
from django.db.models import Count

from app.models import EnglishWordLibrary, EnWordReciteQuestion


class EnWordService:

    @classmethod
    def get_word_freq_group_for_recite(cls):
        cache_key = 'word_freq_group_for_recite'
        data = cache.get(cache_key)
        if data is not None:
            return data

        has_question_words_qs = EnWordReciteQuestion.objects.filter(
            is_deleted=False,
            question_type=EnWordReciteQuestion.QuestionType.en2ch
        ).values('word_id').annotate(question_count=Count('id'))
        has_question_words = [i['word_id'] for i in has_question_words_qs]

        qs = EnglishWordLibrary.objects.filter(
            is_deleted=False, id__in=has_question_words,
        ).values('id', 'word_freq')

        word_freq_group = defaultdict(list)
        for i in qs:
            word_freq_group[i['word_freq']].append(i['id'])
        cache.set(cache_key, word_freq_group, timeout=300)
        return word_freq_group

    @classmethod
    def get_word_freq_group(cls):
        cache_key = 'en_word_freq_group'
        data = cache.get(cache_key)
        if data is not None:
            return data

        qs = EnglishWordLibrary.objects.filter(
            is_deleted=False
        ).values('id', 'word_freq')

        word_freq_group = defaultdict(list)
        for i in qs:
            word_freq_group[i['word_freq']].append(i['id'])

        cache.set(cache_key, word_freq_group, timeout=600)
        return word_freq_group
