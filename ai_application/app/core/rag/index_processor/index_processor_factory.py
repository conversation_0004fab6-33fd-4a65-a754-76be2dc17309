from app.core.rag.index_processor.constant.index_type import IndexType
from app.core.rag.index_processor.index_processor_base import BaseIndexProcessor
from app.core.rag.index_processor.processor.chat_doc_index_processor import ChatDocIndexProcessor
from app.core.rag.index_processor.processor.knowledge_index_processor import KnowledgeIndexProcessor
from app.core.rag.index_processor.processor.paragraph_index_processor import ParagraphIndexProcessor
from app.core.rag.index_processor.processor.subtitle_index_processor import SubtitleIndexProcessor


class IndexProcessorFactory:
    """IndexProcessorInit.
    """

    def __init__(self, index_type: str):
        self._index_type = index_type

    def init_index_processor(self) -> BaseIndexProcessor:
        """Init index processor."""

        if not self._index_type:
            raise ValueError("Index type must be specified.")

        if self._index_type == IndexType.PARAGRAPH_INDEX.value:
            return ParagraphIndexProcessor()
        elif self._index_type == IndexType.CHAT_DOC_INDEX.value:
            return ChatDocIndexProcessor()
        elif self._index_type == IndexType.KNOWLEDGE_INDEX.value:
            return KnowledgeIndexProcessor()
        elif self._index_type == IndexType.SUBTITLE_INDEX.value:
            return SubtitleIndexProcessor()
        # elif self._index_type == IndexType.QA_INDEX.value:
        #     return QAIndexProcessor()
        else:
            raise ValueError(f"Index type {self._index_type} is not supported.")
