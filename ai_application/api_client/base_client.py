import json
import logging

from django.conf import settings
from django_ext.exceptions import InternalException

from api_client.base_request import make_request, InternalApiError

logger = logging.getLogger(__name__)


def filter_none_params(params: dict) -> dict:
    new_params = {}
    for k, v in params.items():
        if k is not None:
            new_params[k] = v
    return new_params


class BaseClient:
    host = None
    host_local = None
    host_test = None
    host_preview = None
    host_product = None

    def __init__(self):
        if settings.ENVIRONMENT == settings.ENV_LOCAL and self.host_local:
            self.host = self.host_local
        elif settings.ENVIRONMENT == settings.ENV_TEST and self.host_test:
            self.host = self.host_test
        elif settings.ENVIRONMENT == settings.ENV_PREVIEW and self.host_preview:
            self.host = self.host_test
        elif settings.ENVIRONMENT == settings.ENV_PRODUCT and self.host_product:
            self.host = self.host_product

        if not self.host:
            raise InternalException(code=50000, detail='host配置错误')

    @property
    def base_url(self):
        return f'{self.host}/internal_api/v1'

    def _get(self, url, params=None, **kwargs):
        if params:
            params = filter_none_params(params)

        return self._request(
            url,
            method='get',
            params=params,
            **kwargs
        )

    def _post(self, url, data=None, headers=None, **kwargs):
        if data is None:
            data = {}

        if headers is None:
            headers = {}

        return self._request(
            url,
            method='post',
            headers={'Content-Type': 'application/json', **headers},
            data=json.dumps(data),
            **kwargs
        )

    def _put(self, url, data=None, **kwargs):
        if data is None:
            data = {}

        return self._request(
            url,
            method='put',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(data),
            **kwargs
        )

    def _request(self, url, **kwargs):
        full_url = f'{self.base_url}{url}'
        try:
            res = make_request(full_url, **kwargs)
        except InternalApiError as i_e:
            logger.exception(i_e)
            raise InternalException(code=50000, detail='网络请求失败', internal_err=i_e.internal_err)
        except Exception as e:
            logger.exception(e)
            raise InternalException(code=50000, detail='网络连接失败')

        if int(res.get('code')) >= 50000:
            raise InternalException(code=50000, detail=res.get('msg'))
        return res.get('data')


class BaseDataClient(BaseClient):

    def _request(self, url, **kwargs):
        full_url = f'{self.base_url}{url}'
        try:
            res = make_request(full_url, **kwargs)
        except InternalApiError as i_e:
            logger.exception(i_e)
            raise InternalException(code=50000, detail='网络请求失败', internal_err=i_e.internal_err)
        except Exception as e:
            logger.exception(e)
            raise InternalException(code=50000, detail='网络连接失败')

        if int(res.get('code')) != 200:
            raise InternalException(code=50000, detail=res.get('msg'))
        return res.get('result')
