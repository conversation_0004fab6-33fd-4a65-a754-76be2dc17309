import logging

from django.views.generic import TemplateView

from app.api.dto import ChatMessageDto
from app.libs.baidu_ocr import BaiduOcr
from app.models import PromptTemplate, Account, InvokeFrom
from app.services.app_generate_service import AppGenerateService
from django_ext.base_view import BaseView
from django_ext.response import make_response

logger = logging.getLogger(__name__)


class OcrTestView(TemplateView):
    template_name = 'app/ocr_test.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        return context


class OcrResultView(BaseView):

    def get_baidu_result(self, image_file):
        image_data = image_file.read()
        return BaiduOcr().basic_accurate(image_file=image_data)

    def post(self, request, *args, **kwargs):
        image_file = request.data.get('image')
        evaluate_ocr_text_completeness = request.data.get('evaluate_ocr_text_completeness')
        if not image_file:
            raise ValueError('file is required')

        res = self.get_baidu_result(image_file)
        logger.info(f'get_baidu_result:{res}')
        words = [i['words'] for i in res]
        ocr_text = '\n'.join(words)
        if ocr_text:
            # 暂时先返回原始识别结果
            return make_response({
                'content': ocr_text
            })

            # account = Account.objects.first()
            # PromptTemplate.objects.filter(app_no='evaluate_ocr_text_completeness').update(
            #     debug_prompt_content=evaluate_ocr_text_completeness
            # )
            # completeness_result = self.evaluate_ocr_text_completeness(ocr_text,account)
            #
            # if completeness_result["result"] == "1":
            #     # 文本完整，直接返回识别的内容
            #     return make_response({
            #         'content': ocr_text
            #     })
            # else:
            #     # 文本不完整，提示用户重新拍照上传
            #     return make_response({
            #         'content': "题目不完整，请重新上传图片"
            #     })
        else:
            return make_response({
                    'content': "识别内容为空，请重新上传图片"
                })

    @classmethod
    def evaluate_ocr_text_completeness(cls, ocr_text: str, account: Account):
        template = PromptTemplate.objects.filter(app_no='evaluate_ocr_text_completeness').first()
        dto = ChatMessageDto(
            app_id='evaluate_ocr_text_completeness',
            inputs={
                "max_tokens": 1500,
                "temperature": 0.1,
                'top_p': 0.5,
                'prompt_template': template.id,
                'ocr_text': ocr_text,
                'is_debug': 1,
            },

            stream=False,
        )

        response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)
        answer = response.get('answer', '')  # 返回判断结果（“1” 完整 ，“-1” 不完整)

        return {
            "result": answer,
        }
