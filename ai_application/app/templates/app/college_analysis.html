<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>院系分析助手</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <style>        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
        }
        .left-section, .right-section {
            padding: 20px;
            box-sizing: border-box;
        }
        .left-section {
            width: 50%;
            background-color: #f9f9f9;
            border-right: 1px solid #ddd;
        }
        .right-section {
            width: 50%;
            background-color: #fff;
        }
        h2 {
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], select, textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .checkbox-group label {
            display: inline-block;
            margin-right: 10px;
        }
        .hidden {
            display: none;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result-box {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #loadingOverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        #loadingOverlay div {
            color: white;
            font-size: 24px;
        }
        /* 新增模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1001;
        }
        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            max-width: 400px;
            text-align: center;
        }
        .modal.hidden {
            display: none;
        }
        /* 院校目标样式 */
        .college-major-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .college-major-group input {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="left-section">
        <h2>填写个人信息</h2>
        <form id="college-analysis-form">

            <!-- 填写日期 -->
            <div class="form-group">
                <label for="fill-date">填写日期</label>
                <input type="text" id="fill-date" readonly>
            </div>

            <!-- 本科院校层次 -->
            <div class="form-group">
                <label for="bachelor-level">本科院校层次</label>
                <select id="bachelor-level">
                    <option value="985工程">985工程</option>
                    <option value="211工程">211工程</option>
                    <option value="双一流大学" selected>双一流大学</option>
                    <option value="双非院校">双非院校</option>
                </select>
            </div>

            <!-- 本科专业 -->
            <div class="form-group">
                <label for="bachelor-major">本科专业</label>
                <input type="text" id="bachelor-major" maxlength="15" value="" required>
            </div>

            <!-- 是否跨考 -->
            <div class="form-group">
                <label for="is_cross_exam">是否跨考</label>
                <select id="is_cross_exam">
                    <option value="是">是</option>
                    <option value="否" selected>否</option>
                </select>
            </div>

            <!-- 考研年份 -->
            <div class="form-group">
                <label for="exam_year">考研年份</label>
                <select id="exam_year">
                    <option value="2026"selected>2026</option>
                    <option value="2027">2027</option>
                    <option value="2028">2028</option>

                </select>
            </div>

            <!-- 总GPA区间 -->
            <div class="form-group">
                <label for="gpa-range">总GPA区间</label>
                <select id="gpa-range">
                    <option value="3.8+">3.8+</option>
                    <option value="3.5-3.7">3.5-3.7</option>
                    <option value="3.0-3.4" selected>3.0-3.4</option>
                    <option value="3.0">3.0</option>
                </select>
            </div>

            <!-- 专业排名 -->
            <div class="form-group">
                <label for="major-ranking">专业排名</label>
                <select id="major-ranking">
                    <option value="前10%">前10%</option>
                    <option value="11%-30%" selected>11%-30%</option>
                    <option value="其他">其他</option>
                </select>
            </div>

            <!-- 科研经历 -->
            <div class="form-group">
                <label for="research-experience">科研经历</label>
                <input type="text" id="research-experience" value="" required>
            </div>

            <!-- 竞赛经历 -->
            <div class="form-group">
                <label for="competition-experience">竞赛经历</label>
                <textarea id="competition-experience" maxlength="50" required></textarea>
            </div>

             <!-- 修改后的英语能力 -->
            <div class="form-group">
                <label for="english-ability">英语能力</label>
                <div style="display: flex; gap: 10px;">
                    <select id="english-ability" style="flex: 1;">
                        <option value="无">无</option>
                        <option value="CET-6">六级</option>
                        <option value="雅思">雅思</option>
                        <option value="托福">托福</option>
                        <option value="其他">其他</option>
                    </select>
                    <input type="text" id="english-score" style="flex: 1;" placeholder="分数/详情">
                </div>
            </div>

            <!-- 修改后的数学基础 -->
            <div class="form-group">
                <label for="math-basis">数学基础</label>
                <div style="display: flex; gap: 10px;">
                    <select id="math-basis-select" style="flex: 1;">
                        <option value="无">无</option>
                        <option value="基础薄弱">基础薄弱</option>
                        <option value="系统学习过高数线代概率" selected>系统学习过高数线代概率</option>
                    </select>
                    <input type="text" id="math-basis-text" style="flex: 1;" placeholder="补充说明">
                </div>
            </div>


            <!-- 修改后的考生身份 -->
            <div class="form-group">
                <label for="candidate-status">考生身份</label>
                <div style="display: flex; gap: 10px;">
                    <select id="candidate-status" style="flex: 1;">
                        <option value="应届生">应届生</option>
                        <option value="往届生">往届生</option>
                        <option value="在职">在职</option>
                    </select>
                    <input type="text" id="candidate-details" class="hidden" style="flex: 1;" placeholder="详情">
                </div>
            </div>

            <!-- 修改后的实习经历 -->
            <div class="form-group">
                <label for="intern-experience">实习经历</label>
                <textarea id="intern-experience" placeholder="请描述实习经历"></textarea>
            </div>

            <!-- 修改后的工作经历 -->
            <div class="form-group">
                <label for="job-experience">工作经历</label>
                <textarea id="job-experience" placeholder="请描述工作经历"></textarea>
            </div>

            <!-- 同步备考 -->
            <div class="form-group">
                <label for="concurrent-preparation">同步备考</label>
                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="preparation-civil-service" name="concurrent-preparation" value="考公">
                        考公
                    </label>
                    <label>
                        <input type="checkbox" id="preparation-civil-examination" name="concurrent-preparation" value="考编">
                        考编
                    </label>
                    <label>
                        <input type="checkbox" id="preparation-job-seeking" name="concurrent-preparation" value="求职">
                        求职
                    </label>
                    <label>
                        <input type="checkbox" id="preparation-none" name="concurrent-preparation" value="无">
                        无
                    </label>
                </div>
            </div>

            <!-- 意向地区 -->
            <div class="form-group">
                <label for="preferred-regions">意向地区</label>
                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="region-northeast" name="preferred-regions" value="华北">
                        华北
                    </label>
                    <label>
                        <input type="checkbox" id="region-east" name="preferred-regions" value="华东">
                        华东
                    </label>
                    <label>
                        <input type="checkbox" id="region-southeast" name="preferred-regions" value="华南">
                        华南
                    </label>
                    <label>
                        <input type="checkbox" id="region-central" name="preferred-regions" value="华中">
                        华中
                    </label>
                    <label>
                        <input type="checkbox" id="region-northwest" name="preferred-regions" value="西北">
                        西北
                    </label>
                    <label>
                        <input type="checkbox" id="region-southwest" name="preferred-regions" value="西南">
                        西南
                    </label>
                    <label>
                        <input type="checkbox" id="region-northeastern" name="preferred-regions" value="东北">
                        东北
                    </label>
                </div>
            </div>

            <!-- 修改后的院校目标部分 -->
             <div class="form-group">
                <label>目标院校1</label>
                <div class="college-major-group">
                    <input type="text" id="target-college-1" placeholder="院校名称" required>
                    <input type="text" id="target-college-code-1" placeholder="院校代码" required>
                    <input type="text" id="target-major-1" placeholder="对应专业" required>
                    <input type="text" id="target-major-code-1" placeholder="专业代码" required>
                </div>
            </div>

            <div class="form-group">
                <label>目标院校2</label>
                <div class="college-major-group">
                    <input type="text" id="target-college-2" placeholder="院校名称">
                    <input type="text" id="target-college-code-2" placeholder="院校代码">
                    <input type="text" id="target-major-2" placeholder="对应专业">
                    <input type="text" id="target-major-code-2" placeholder="专业代码">
                </div>
            </div>

            <div class="form-group">
                <label>目标院校3</label>
                <div class="college-major-group">
                    <input type="text" id="target-college-3" placeholder="院校名称">
                    <input type="text" id="target-college-code-3" placeholder="院校代码">
                    <input type="text" id="target-major-3" placeholder="对应专业">
                    <input type="text" id="target-major-code-3" placeholder="专业代码">
                </div>
            </div>
            <!-- 学费敏感度 -->
            <div class="form-group">
                <label for="tuition-sensitivity">学费敏感度</label>
                <select id="tuition-sensitivity">
                    <option value="需全额奖学金">需全额奖学金</option>
                    <option value="可承担3万/年" selected>可承担3万/年</option>
                    <option value="可承担5万/年">可承担5万/年</option>
                    <option value="不限">不限</option>
                </select>
            </div>

            <!-- 院校专业优先级排序 -->
            <div class="form-group">
                <label for="priority-order">院校层级及地区优先级排序</label>
                <select id="priority-order">
                    <option value="region,college_level">院校地区优先</option>
                    <option value="college_level,region">院校层次优先</option>
                </select>
            </div>

            <!-- 目标专业方向 -->
            <div class="form-group">
                <label>目标专业方向</label>
                <div style="display: flex; gap: 10px;">
                    <input type="text" id="target_major_direction_1" placeholder="目标专业方向1" required>
                    <input type="text" id="target_major_direction_code_1" placeholder="专业代码1" required>
                    <input type="text" id="target_major_direction_2" placeholder="目标专业方向2">
                    <input type="text" id="target_major_direction_code_2" placeholder="专业代码2">
                    <input type="text" id="target_major_direction_3" placeholder="目标专业方向3">
                    <input type="text" id="target_major_direction_code_3" placeholder="专业代码3">
                </div>
            </div>

             <!--想要考学硕/专硕-->
            <div class="form-group">
                <label for="master_type">想要考学硕/专硕</label>
                <div style="display: flex; gap: 10px;">
                    <select id="master_type" style="flex: 1;">
                        <option value="学硕" selected>学硕</option>
                        <option value="专硕">专硕</option>
                    </select>
                </div>
            </div>


             <!-- 日均学习时间 -->
            <div class="form-group">
                <label for="daily_learning_time">日均学习时间</label>
                <select id="daily_learning_time">
                    <option value="<3h"><3h</option>
                    <option value="3-5h" selected>3-5h</option>
                    <option value="5-8h">5-8h</option>
                    <option value=">8h">8h</option>
                </select>
            </div>

            <!-- 备考痛点 -->
            <div class="form-group">
                <label for="preparation-pain-points">备考痛点</label>
                <input type="text" id="preparation-pain-points" placeholder="例如自律性差、信息差、基础薄弱、时间不足、心理压力" required>
            </div>

            <!-- 优先服务 -->
            <div class="form-group">
                <label for="preferred-services">优先服务</label>
                <input type="text" id="preferred-services" placeholder="例如精准择校、专业课辅导、复试模拟、心理疏导" required>
            </div>

            <!-- 读研期间对实习、住宿资源需求 -->
            <div class="form-group">
                <label for="internship-accommodation-needs">读研期间对实习、住宿资源需求</label>
                <input type="text" id="internship-accommodation-needs" required>
            </div>

                        <!-- 霍兰德职业兴趣测试 -->
            <div class="form-group">
                <label for="holland-test">霍兰德职业兴趣测试</label>
                <select id="holland-test">
                    <option value="没有做过测试">没有做过测试</option>
                    <option value="做过测试">做过测试</option>
                </select>
                <input type="text" id="holland-result" class="hidden" placeholder="请输入测试结果" required>
            </div>

             <!-- 个人需求 -->
            <div class="form-group">
                <label for="personal_needs">个人需求</label>
                <input type="text" id="personal_needs" placeholder="不考数学/不接受二战" required>
            </div>

            <!-- 硕士类型-->
            <div class="form-group">
                <label for="master_degree_type">硕士类型</label>
                <select id="master_degree_type">
                    <option value="全日制" selected>全日制</option>
                    <option value="非全日制">非全日制</option>
                </select>
            </div>

            <!-- 调剂底线 -->
            <div class="form-group">
                <label for="admission-baseline">调剂底线</label>
                <input type="text" id="admission-baseline" maxlength="30" placeholder="不接受B区调剂" required>
            </div>


            <!-- 联系方式 -->
            <div class="form-group">
                <label for="contact-info">微信/邮箱</label>
                <input type="text" id="contact-info" placeholder=""  >
            </div>

            <!-- 提交按钮 -->
            <button type="button" id="generateAdviceBtn">生成建议</button>
        </form>
    </div>

    <div class="right-section">
        <h2>模型返回结果</h2>
        <div class="result-box" id="result-box" aria-live="polite" aria-atomic="true" aria-busy="false"></div>
        <div class="tracing-detail" id="tracingDetails"></div>

        <!-- 提示词编辑窗口 -->
        <div class="form-group">
            <label for="college-analysis-prompt">提示词编辑</label>
            <textarea id="college-analysis-prompt" rows="4" placeholder="请输入提示词">{{ college_analysis }}</textarea>
        </div>
    </div>

    <!-- 新增生成考研复习规划按钮 -->
    <div class="form-group">
        <button type="button" id="generateReviewPlanBtn">生成考研复习规划</button>
    </div>

    <div id="loadingOverlay">
        <div>加载中...</div>
    </div>


    <!-- 霍兰德测试弹窗 -->
    <div id="holland-modal" class="modal hidden">
        <div class="modal-content">
            <p>您还没有做过霍兰德职业兴趣测试，是否现在开始测试？</p>
            <div style="display: flex; justify-content: center; gap: 20px; margin-top: 20px;">
                <button id="holland-test-yes">是</button>
                <button id="holland-test-no">否</button>
            </div>
        </div>
    </div>


     <script>
document.addEventListener("DOMContentLoaded", function () {
    // 初始化日期
    const today = new Date();
    document.getElementById('fill-date').value = today.toISOString().split('T')[0];

    // 获取生成考研复习规划按钮
    const generateReviewPlanBtn = document.getElementById('generateReviewPlanBtn');

    // 生成考研复习规划按钮点击事件
    generateReviewPlanBtn.addEventListener('click', () => {
        window.location.href = 'https://ai-dev.yantucs.com/console/app/kaoyan_review_plan_index';
    });

    // 获取DOM元素
    const generateAdviceBtn = document.getElementById('generateAdviceBtn');
    const resultBox = document.getElementById('result-box');
    const tracingDetails = document.getElementById('tracingDetails');
    const loadingOverlay = document.getElementById('loadingOverlay');
    let message_id = null ;

    // 动态显示/隐藏字段的函数
    function toggleOtherProgrammingInput() {
        const otherCheckbox = document.getElementById('programming-other');
        const otherInput = document.getElementById('programming-other-input');
        if (otherCheckbox.checked) {
            otherInput.classList.remove('hidden');
        } else {
            otherInput.classList.add('hidden');
        }
    }

    function toggleCandidateDetails() {
        const candidateStatus = document.getElementById('candidate-status').value;
        const detailsInput = document.getElementById('candidate-details');
        if (candidateStatus === '往届生' || candidateStatus === '在职') {
            detailsInput.classList.remove('hidden');
            detailsInput.placeholder = candidateStatus === '往届生' ? '毕业几年' : '行业';
        } else {
            detailsInput.classList.add('hidden');
        }
    }

    function toggleHollandResult() {
        const hollandTest = document.getElementById('holland-test').value;
        const hollandResult = document.getElementById('holland-result');
        const hollandModal = document.getElementById('holland-modal');

        if (hollandTest === '做过测试') {
            hollandResult.classList.remove('hidden');
            hollandModal.classList.add('hidden');
        } else {
            hollandResult.classList.add('hidden');
            hollandModal.classList.remove('hidden');
        }
    }

    // 绑定事件监听器
    document.getElementById('candidate-status').addEventListener('change', toggleCandidateDetails);
    document.getElementById('holland-test').addEventListener('change', toggleHollandResult);

    // 霍兰德测试弹窗按钮事件
    document.getElementById('holland-test-yes').addEventListener('click', () => {
        window.location.href = 'https://ai-dev.yantucs.com/console/app/huolande_test_index';
    });

    document.getElementById('holland-test-no').addEventListener('click', () => {
        document.getElementById('holland-modal').classList.add('hidden');
    });

    // 生成建议按钮点击事件

    generateAdviceBtn.addEventListener('click', () => {
        // 创建包含所有字段的对象（英文变量名）
        const allFields = {};

        // 收集所有表单字段
        document.querySelectorAll('#college-analysis-form input, #college-analysis-form select, #college-analysis-form textarea').forEach(element => {
            const labelElement = element.closest('.form-group')?.querySelector('label');
            let label = labelElement?.innerText.replace(':', '').trim() || element.id;

            // 转换为英文变量名
            const englishLabel = chineseToEnglishLabel(label);

            // 处理不同类型的输入元素
            if (element.tagName === 'SELECT') {
                allFields[englishLabel] = element.value;
            } else if (element.type === 'checkbox') {
                if (!allFields[englishLabel]) allFields[englishLabel] = [];
                if (element.checked) allFields[englishLabel].push(element.value);
            } else if (element.type === 'text' || element.type === 'textarea') {
                allFields[englishLabel] = element.value;
            }
        });

        // 单独处理院校目标字段
        const targetCollegeMajor = [];
        for (let i = 1; i <= 3; i++) {
            const collegeName = document.getElementById(`target-college-${i}`).value;
            const collegeCode = document.getElementById(`target-college-code-${i}`).value;
            const majorName = document.getElementById(`target-major-${i}`).value;
            const majorCode = document.getElementById(`target-major-code-${i}`).value;

            targetCollegeMajor.push({
                college_name: collegeName,
                college_code: collegeCode,
                major_name: majorName,
                major_code: majorCode
            });
        }

    // 单独处理目标专业方向字段
    const targetMajorDirections = [];
    for (let i = 1; i <= 3; i++) {
        const majorName = document.getElementById(`target_major_direction_${i}`).value;
        const majorCode = document.getElementById(`target_major_direction_code_${i}`).value;

        if (majorName || majorCode) { // 确保不添加空值
            targetMajorDirections.push({
                name: majorName,
                code: majorCode
            });
        }
    }

    allFields['target_major_direction'] = targetMajorDirections;


        // 处理霍兰德测试结果
        const hollandTest = document.getElementById('holland-test').value;
        if (hollandTest === '做过测试') {
            allFields['holland_test_result'] = document.getElementById('holland-result').value;
        } else {
            allFields['holland_test_result'] = '未做测试';
        }

        // 获取提示词编辑窗口的内容
        const collegeAnalysisPrompt = document.getElementById('college-analysis-prompt').value;

        // 创建整合后的studentInfo对象（中文变量名）
        const studentInfo = {
            // 基本信息
            '基本信息': {
                '填写日期': allFields['fill_date'],
                '本科院校层次': allFields['bachelor_level'],
                '本科专业': allFields['bachelor_major'],
                '是否跨考': allFields['is_cross_exam'],
                '考研年份': allFields['exam_year'],
            },
            '成绩背景': {
                '总GPA区间': allFields['gpa_range'],
                '专业排名': allFields['major_ranking'],
                '科研经历': allFields['research_experience'],
                '竞赛经历': allFields['competition_experience'],
            },
            '能力评估': {
                '英语能力': `${allFields['english_ability']}${allFields['english_ability'] !== '无' ? ':' + allFields['english_score'] : ''}`,
                '数学基础': `${allFields['math_basis_select']}${allFields['math_basis_text'] ? ':' + allFields['math_basis_text'] : ''}`,
            },
            '备考状态': {
                '考生身份': getCandidateStatus(),
                '实习经历': allFields['intern_experience'],
                '工作经历': allFields['job_experience'],
                '同步备考': allFields['concurrent_preparation'] ? allFields['concurrent_preparation'].join('、') : '无',
                '日均学习时间': allFields['daily_learning_time'],
            },
            '院校需求': {
                '意向地区': allFields['preferred_regions'] ? allFields['preferred_regions'].join('、') : '无',
                '学费敏感度': allFields['tuition_sensitivity'],
                '院校专业优先级排序': allFields['priority_order'],
                '目标专业方向': allFields['target_major_direction'],
                '想要考学硕/专硕': allFields['master_type'],
                '目标院校和专业': targetCollegeMajor
            },
            '个性需求': {
                '备考痛点': allFields['preparation_pain_points'],
                '优先服务': allFields['preferred_services'],
                '读研期间对实习、住宿资源需求': allFields['internship_accommodation_needs'],
                '个人需求': allFields['personal_needs'],
                '硕士类型': allFields['master_degree_type'],
                '霍兰德职业兴趣测试': allFields['holland_test'],
                '霍兰德测试结果': allFields['holland_test_result'],
                '调剂底线': allFields['admission_baseline'],
            },
            '联系方式': {
                '联系方式': allFields['contact_info']
            },
        };
        tracingDetails.innerHTML = ''; // 清空之前的细节
        // 新增 user_info 变量
        const report_id = "01";

        // 显示加载动画
        loadingOverlay.style.display = 'flex';

        // 发送请求到后端
        fetch("{% url 'college_analysis' %}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                all_fields: allFields,  // 所有字段的独立变量（英文）
                student_info: studentInfo, // 整合后的studentInfo对象（中文）
                college_analysis: collegeAnalysisPrompt,
                report_id: report_id
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.body;
        })
        .then(stream => {
            const reader = stream.getReader();
            const decoder = new TextDecoder(); // 创建TextDecoder实例
            let result_text = '';

            function readChunk() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        console.log('Stream complete, returning collected data');
                        find_tracing();
                        return;
                    }
                    const textChunkStr = decoder.decode(value, { stream: true });

                    // 根据 \n\n 对流数据分块
                    const chunkArr = textChunkStr.split("\n\n");
                    for (let i = 0; i < chunkArr.length; i++) {
                        const textChunk = chunkArr[i];
                        if (textChunk.startsWith("data: ")) {
                            // 去除前缀"data: "
                            const json_str = textChunk.slice(6);
                            console.log('json_str', json_str);
                            try {
                                // 将匹配到的字符串转换为JSON对象
                                const jsonData = JSON.parse(json_str);
                                if (!message_id) {
                                    message_id = jsonData.message_id;
                                }
                                if (jsonData.answer) {
                                    result_text += jsonData.answer;
                                    resultBox.innerHTML = marked.parse(result_text);
                                } else if (jsonData.err) {
                                    resultBox.innerHTML = jsonData.err;
                                }
                            } catch (error) {
                                console.error('Error parsing JSON', json_str, error);
                            }
                        }
                    }
                    readChunk();
                }).catch(error => {
                    console.error('Error collecting stream data', error);
                });
            }
            readChunk();
        })
        .catch(error => {
            console.error('Error:', error);
            resultBox.innerHTML = '<p>生成建议失败，请重试。</p>';
        })
        .finally(() => {
            // 隐藏加载动画
            loadingOverlay.style.display = 'none';
        });
    });

    // 辅助函数：中文标签转英文变量名
    function chineseToEnglishLabel(chineseLabel) {
        const mapping = {
            '填写日期': 'fill_date',
            '本科院校层次': 'bachelor_level',
            '本科专业': 'bachelor_major',
            '是否跨考': 'is_cross_exam',
            '考研年份': 'exam_year',
            '总GPA区间': 'gpa_range',
            '专业排名': 'major_ranking',
            '科研经历': 'research_experience',
            '竞赛经历': 'competition_experience',
            '英语能力': 'english_ability',
            '分数/详情': 'english_score',
            '数学基础': 'math_basis_select',
            '补充说明': 'math_basis_text',
            '考生身份': 'candidate_status',
            '详情': 'candidate_details',
            '实习经历': 'intern_experience',
            '工作经历': 'job_experience',
            '同步备考': 'concurrent_preparation',
            '意向地区': 'preferred_regions',
            '学费敏感度': 'tuition_sensitivity',
            '院校层级及地区优先级排序': 'priority_order',
            '目标专业方向': 'target_major_direction',
            '想要考学硕/专硕': 'master_type',
            '日均学习时间': 'daily_learning_time',
            '备考痛点': 'preparation_pain_points',
            '优先服务': 'preferred_services',
            '读研期间对实习、住宿资源需求': 'internship_accommodation_needs',
            '霍兰德职业兴趣测试': 'holland_test',
            '霍兰德测试结果': 'holland_result',
            '个人需求': 'personal_needs',
            '硕士类型': 'master_degree_type',
            '调剂底线': 'admission_baseline',
            '微信/邮箱': 'contact_info'
        };
        return mapping[chineseLabel] || chineseLabel.toLowerCase().replace(/[^a-z0-9]+/g, '_');
    }

    // 辅助函数：获取考生身份（拼接详情）
    function getCandidateStatus() {
        const status = document.getElementById('candidate-status').value;
        const details = document.getElementById('candidate-details').value;

        if (status === '往届生' || status === '在职') {
            return details ? `${status}(${details})` : status;
        }
        return status;
    }

    function find_tracing() {
    if (!message_id) return;
    const $el = $('#tracingDetails');  // 指定展示追踪信息的容器
    $.ajax({
        type: 'GET',
        url: "{% url 'message_tracing' %}",
        data: { message_id: message_id },
        success: function(response) {
            $('#loadingOverlay').css('display', 'none');
            const results = response.data;

            $el.empty(); // 确保清空之前的内容

            if (results && results.tracing) {
                results.tracing.forEach(function(traceDetail) {
                    const detailElement = document.createElement('p');
                    detailElement.textContent = traceDetail;
                    $el.append(detailElement);
                });
            } else {
                $el.append('<p>没有找到追踪信息。</p>');
            }
        },
        error: function(xhr, status, error) {
            alert('请求失败');
            $('#loadingOverlay').css('display', 'none');
            console.error('AJAX Error:', xhr.responseText);
        }
    });
}
});

    </script>
</body>
</html>