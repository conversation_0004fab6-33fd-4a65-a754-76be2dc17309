from app.core.features.supervise_plan_scheme import (
    Supervise_Learn_Stage_Feature,
    SupervisePlanSchemeStageFeature,
    SupervisePlanSchemeMapperFeature,
    SupervisePlanSchemePersonalizFeature,
    SupervisePrePromptFeature
)
from app.core.model_runtime.entities.llm_entities import LLMResult, LLMUsage
from app.core.workflow.base_workflow import BaseWorkflow
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import json
import logging

logger = logging.getLogger(__name__)
    
class SuperviseLearnPlanSchemeWorkflow(BaseWorkflow):

    def run(self):
        # 获取并处理数据
        data = self.application_generate_entity.inputs
        logger.info(f"SuperviseLearnPlanSchemeWorkflow 开始处理，数据字段: {list(data.keys())}")

        # 生成最终报告 - 直接使用新流程
        result = self._new_pipeline_generator(data)
        self._publish_workflow_end_message()
        return result

    def _extract_data_summary_comparison(self, analysis_text):
        """
        从学生分析文本中提取"六、数据总结与对比"部分

        Args:
            analysis_text (str): 学生分析文本

        Returns:
            str: 提取的数据总结与对比部分
        """
        try:
            from app.utils.text_analysis_utils import TextAnalysisUtils
            return TextAnalysisUtils.extract_data_summary_comparison(analysis_text)
        except Exception as e:
            logger.warning(f"提取数据总结与对比失败: {str(e)}")
            return ""



    def _new_pipeline_generator(self, data) -> LLMResult:
        """新的四阶段处理流程：pre_prompt -> stage -> mapper -> personaliz"""
        logger.info("开始执行新的四阶段处理流程")

        try:
            # 提取必要的数据，提供合理的默认值
            student_analysis = data.get('学生分析', '')
            course_packages = data.get('课包信息', [])
            target_info = data.get('目标院校信息', [])
            study_stage = data.get('学习阶段', '备考初期')
            start_date = data.get('开始时间', '')
            exam_date = data.get('考试时间', '')
            entrance_foundation_positioning = data.get('入学基础定位', '')

            logger.info(f"数据检查 - 学生分析: {len(student_analysis)}字符, 课包: {len(course_packages)}个, 目标院校: {len(target_info)}个, 学习阶段: {study_stage}")

            # 提取数据总结与对比部分
            data_summary_comparison = self._extract_data_summary_comparison(student_analysis)
            logger.info(f"提取数据总结与对比: {len(data_summary_comparison)}字符")

            # 并行执行第一阶段（pre_prompt）和第二阶段（stage）
            logger.info("开始并行执行 pre_prompt 和 stage 阶段")
            with ThreadPoolExecutor(max_workers=2) as executor:
                # 第一阶段：pre_prompt 处理
                pre_prompt_feature = SupervisePrePromptFeature()
                future_pre_prompt = executor.submit(
                    pre_prompt_feature.run,
                    self.application_generate_entity,
                    student_analysis,
                    start_date,
                    exam_date,
                    False  # stream
                )

                # 第二阶段：stage 处理
                stage_feature = SupervisePlanSchemeStageFeature()
                future_stage = executor.submit(
                    stage_feature.run,
                    self.application_generate_entity,
                    target_info,
                    data_summary_comparison,
                    start_date,
                    exam_date,
                    entrance_foundation_positioning,
                    False  # stream
                )

                # 获取结果
                try:
                    pre_prompt_result = future_pre_prompt.result()
                    pre_prompt_content = pre_prompt_result.message.content if hasattr(pre_prompt_result.message, 'content') else str(pre_prompt_result.message)
                    logger.info(f"Pre-prompt 阶段完成，输出长度: {len(pre_prompt_content)}")
                except Exception as e:
                    logger.error(f"Pre-prompt 阶段失败: {str(e)}", exc_info=True)
                    pre_prompt_content = f"Pre-prompt 阶段失败: {str(e)}"

                try:
                    stage_result = future_stage.result()
                    stage_content = stage_result.message.content if hasattr(stage_result.message, 'content') else str(stage_result.message)
                    logger.info(f"Stage 阶段完成，输出长度: {len(stage_content)}")
                except Exception as e:
                    logger.error(f"Stage 阶段失败: {str(e)}", exc_info=True)
                    stage_content = f"Stage 阶段失败: {str(e)}"

            # 第三阶段：mapper 处理
            logger.info("开始执行 mapper 阶段")
            try:
                mapper_feature = SupervisePlanSchemeMapperFeature()
                mapper_result = mapper_feature.run(
                    self.application_generate_entity,
                    stage_content,
                    course_packages,
                    False  # stream
                )
                mapper_content = mapper_result.message.content if hasattr(mapper_result.message, 'content') else str(mapper_result.message)
                logger.info(f"Mapper 阶段完成，输出长度: {len(mapper_content)}")
            except Exception as e:
                logger.error(f"Mapper 阶段失败: {str(e)}", exc_info=True)
                mapper_content = f"Mapper 阶段失败: {str(e)}"

            # 第四阶段：personaliz 处理
            logger.info("开始执行 personaliz 阶段")
            try:
                personaliz_feature = SupervisePlanSchemePersonalizFeature()
                personaliz_result = personaliz_feature.run(
                    self.application_generate_entity,
                    mapper_content,
                    study_stage,
                    False  # stream
                )
                personaliz_content = personaliz_result.message.content if hasattr(personaliz_result.message, 'content') else str(personaliz_result.message)
                logger.info(f"Personaliz 阶段完成，输出长度: {len(personaliz_content)}")
            except Exception as e:
                logger.error(f"Personaliz 阶段失败: {str(e)}", exc_info=True)
                personaliz_content = f"Personaliz 阶段失败: {str(e)}"

            # 合并最终结果
            logger.info("合并最终结果")

            # 检查是否有任何阶段成功
            has_success = not any("失败" in content for content in [pre_prompt_content, stage_content, mapper_content, personaliz_content])

            if has_success:
                # 如果所有阶段都成功，返回最终的个性化内容
                combined_result = {
                    "pre_prompt_analysis": pre_prompt_content,
                    "personaliz_output": personaliz_content,
                }
                final_content = json.dumps(combined_result, ensure_ascii=False)
                logger.info(f"所有阶段成功，返回最终内容长度: {len(final_content)}")
            else:
                # 如果有阶段失败，返回详细的错误信息
                combined_result = {
                    "pre_prompt_analysis": pre_prompt_content,
                    "stage_output": stage_content,
                    "mapper_output": mapper_content,
                    "personaliz_output": personaliz_content,
                    "status": "部分失败"
                }
                final_content = json.dumps(combined_result, ensure_ascii=False)
                logger.warning(f"部分阶段失败，返回详细信息长度: {len(final_content)}")

            # 创建最终的 LLMResult
            from app.core.prompt.entities import AssistantPromptMessage

            final_result = LLMResult(
                model='supervise_plan_scheme_pipeline',
                prompt_messages=[],
                message=AssistantPromptMessage(content=final_content),
                usage=LLMUsage(
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                    latency=0.0
                )
            )

            tracing_info = {
                'tracing_type': 'supervise_plan_scheme_new_pipeline',
                'query': str(data)
            }
            logger.info("新流程处理完成，返回结果")
            return self._handle_node_response(final_result, stream=False, tracing_info=tracing_info)

        except Exception as e:
            logger.error(f"新流程处理失败: {str(e)}", exc_info=True)

            # 创建错误结果
            from app.core.prompt.entities import AssistantPromptMessage

            error_message = json.dumps({"error": f"处理失败: {str(e)}"}, ensure_ascii=False)
            error_result = LLMResult(
                model='error_fallback',
                prompt_messages=[],
                message=AssistantPromptMessage(content=error_message),
                usage=LLMUsage(
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                    latency=0.0
                )
            )

            tracing_info = {
                'tracing_type': 'supervise_plan_scheme_error',
                'query': str(data)[:500] + "..." if len(str(data)) > 500 else str(data),
                'error': str(e)
            }
            return self._handle_node_response(error_result, stream=False, tracing_info=tracing_info)

