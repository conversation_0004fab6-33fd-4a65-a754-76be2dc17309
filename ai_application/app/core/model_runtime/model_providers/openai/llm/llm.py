import json
import logging
import time
from typing import Generator, cast

import tiktoken
from django.conf import settings
from openai import OpenAI, Stream
from openai.types.chat import Chat<PERSON><PERSON>pletionChunk, ChatCompletion, ChatCompletionMessageToolCall
from openai.types.chat.chat_completion_chunk import ChoiceDeltaToolCall

from app.core.model_runtime.entities.llm_entities import (
    LLMResult, LLMResultChunk, LLMResultChunkDelta, LLMUsage, LLMToolCallResponse
)
from app.core.model_runtime.model_providers._base.large_language_model import LargeLanguageModel
from app.core.prompt.entities import (
    PromptMessage, UserPromptMessage, AssistantPromptMessage, SystemPromptMessage, PromptMessageContentType,
    TextPromptMessageContent, ImagePromptMessageContent, ToolPromptMessage
)
from app.core.tools.bocha_web_search.web_search import bocha_web_search
from app.core.utils.latex_utils import replace_latex
from app.errors import LLMRequestError

logger = logging.getLogger(__name__)


class OpenAILargeLanguageModel(LargeLanguageModel):
    model_provider = 'openai'

    def __init__(self):
        super().__init__()
        web_search_json_path = settings.BASE_DIR.joinpath('app/core/tools/bocha_web_search/web_search.json')
        with open(web_search_json_path, 'r', encoding='utf-8') as f:
            web_search_config = json.loads(f.read())
        self.web_search_config = web_search_config

    def _invoke(
            self,
            model: str,
            prompt_messages: list[PromptMessage],
            model_parameters: dict,
            tools: list[str] | None = None,
            stream: bool = True
    ) -> LLMResult | Generator:
        return self._generate(model, prompt_messages, model_parameters, tools, stream)

    def get_num_tokens(self, model: str, prompt_messages: list[PromptMessage]) -> int:
        if model.startswith("ft:"):
            base_model = model.split(":")[1]
        else:
            base_model = model

        return self._num_tokens_from_messages(base_model, prompt_messages)

    def _get_openai_config(self):
        return {
            'base_url': '',
            'api_key': '',
        }

    def _generate(
            self,
            model: str,
            prompt_messages: list[PromptMessage],
            model_parameters: dict,
            tools: list[str] | None = None,
            stream: bool = True
    ) -> LLMResult | Generator:
        # init model client
        openai_config = self._get_openai_config()
        client = OpenAI(
            base_url=openai_config.get('base_url'),
            api_key=openai_config.get('api_key'),
        )

        extra_model_kwargs = {}
        if stream:
            extra_model_kwargs["stream_options"] = {"include_usage": True}

        if tools:
            model_tools = []
            for tool in tools:
                if 'web_search' == tool:
                    model_tools.append(self.web_search_config)
            extra_model_kwargs['tools'] = model_tools

        # TODO 处理返回response_format

        # chat model
        req = dict(
            messages=[self._convert_prompt_message_to_dict(m) for m in prompt_messages],
            model=model,
            stream=stream,
            **model_parameters,
            **extra_model_kwargs,
        )

        # 调用工具，需要非流式，先截断之前的输出
        if extra_model_kwargs.get('tools'):
            req['stream'] = False

        try:
            response = client.chat.completions.create(**req)
        except Exception as e:
            logger.exception(e)
            raise LLMRequestError(detail_err=str(e))

        if extra_model_kwargs.get('tools'):
            tool_block_result = self._handle_chat_tool_generate_response(
                model, response, prompt_messages, model_parameters)

            # 未调用工具，直接返回
            if not tool_block_result.message.tool_calls:
                if stream:
                    return self._handle_chat_generate_stream_response_by_result(tool_block_result)
                return tool_block_result

            # 再次查询不使用tools，避免循环调用
            if 'tools' in req:
                req.pop('tools')
            new_prompt_messages = self._handle_tool_prompt_messages(tool_block_result)
            req['messages'] = [self._convert_prompt_message_to_dict(m) for m in new_prompt_messages]
            req['stream'] = stream
            try:
                tool_response = client.chat.completions.create(**req)
            except Exception as e:
                logger.exception(e)
                raise LLMRequestError(detail_err=str(e))

            if stream:
                return self._handle_chat_generate_stream_response(
                    model, tool_response, new_prompt_messages, model_parameters,
                    tool_block_result.tool_calls_response
                )
            return self._handle_chat_generate_response(
                model, tool_response, new_prompt_messages, model_parameters,
                tool_block_result.tool_calls_response)
        else:
            # 不调用工具旧逻辑
            if stream:
                return self._handle_chat_generate_stream_response(
                    model, response, prompt_messages, model_parameters)
            return self._handle_chat_generate_response(
                model, response, prompt_messages, model_parameters)

    def _handle_chat_generate_stream_response(
        self,
        model: str,
        response: Stream[ChatCompletionChunk],
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tool_calls_response: list[LLMToolCallResponse] | None = None
    ) -> Generator:
        """
        Handle llm chat stream response

        :param model: model name
        :param response: response
        :param prompt_messages: prompt messages
        :param model_parameters:
        :return: llm response chunk generator
        """
        if tool_calls_response is None:
            tool_calls_response = []

        full_reasoning_content = ''
        full_assistant_content = ""
        prompt_tokens = 0
        completion_tokens = 0
        final_tool_calls = []
        final_chunk = LLMResultChunk(
            model_provider=self.model_provider,
            model=model,
            model_params=model_parameters,
            prompt_messages=prompt_messages,
            delta=LLMResultChunkDelta(
                index=0,
                message=AssistantPromptMessage(content=""),
                tool_calls_response=tool_calls_response,
            ),
        )

        # 豆包需要处理latex公式
        is_last_cut = False
        temp_exp_str = ''
        last_chunk = None

        for chunk in response:
            if len(chunk.choices) == 0:
                if chunk.usage:
                    # calculate num tokens
                    prompt_tokens = chunk.usage.prompt_tokens
                    completion_tokens = chunk.usage.completion_tokens
                continue

            last_chunk = chunk

            delta = chunk.choices[0]
            has_finish_reason = delta.finish_reason is not None

            try:
                reasoning_content = delta.delta.reasoning_content
            except:
                reasoning_content = ''

            resp_content = delta.delta.content
            if (
                not has_finish_reason
                and (resp_content is None or resp_content == "")
                and not reasoning_content
                and delta.delta.tool_calls is None
            ):
                continue

            if reasoning_content:
                full_reasoning_content += reasoning_content
                assistant_thinking_message = AssistantPromptMessage(content='', reasoning_content=reasoning_content)
                yield LLMResultChunk(
                    model_provider=self.model_provider,
                    model=chunk.model,
                    model_params=model_parameters,
                    prompt_messages=prompt_messages,
                    system_fingerprint=chunk.system_fingerprint,
                    delta=LLMResultChunkDelta(
                        index=delta.index,
                        message=assistant_thinking_message,
                        usage=LLMUsage.only_latency_usage(time.perf_counter() - self.started_at),
                        tool_calls_response=tool_calls_response,
                    ),
                )
                continue

            if self.model_provider == 'doubao':
                # 处理公式转换
                if is_last_cut:
                    resp_content = f'{temp_exp_str}{resp_content}'
                    is_last_cut = False
                    temp_exp_str = ''
                if resp_content and resp_content[-1] == '\\':
                    is_last_cut = True
                    temp_exp_str = resp_content
                    continue
                resp_content = replace_latex(resp_content)

            # transform assistant message to prompt message
            assistant_prompt_message = AssistantPromptMessage(content=resp_content or "")

            full_assistant_content += resp_content or ""

            if has_finish_reason:
                final_chunk = LLMResultChunk(
                    model_provider=self.model_provider,
                    model=chunk.model,
                    model_params=model_parameters,
                    prompt_messages=prompt_messages,
                    system_fingerprint=chunk.system_fingerprint,
                    delta=LLMResultChunkDelta(
                        index=delta.index,
                        message=assistant_prompt_message,
                        finish_reason=delta.finish_reason,
                        usage=LLMUsage.only_latency_usage(time.perf_counter() - self.started_at),
                        tool_calls_response=tool_calls_response,
                    ),
                )
            else:
                yield LLMResultChunk(
                    model_provider=self.model_provider,
                    model=chunk.model,
                    model_params=model_parameters,
                    prompt_messages=prompt_messages,
                    system_fingerprint=chunk.system_fingerprint,
                    delta=LLMResultChunkDelta(
                        index=delta.index,
                        message=assistant_prompt_message,
                        usage=LLMUsage.only_latency_usage(time.perf_counter() - self.started_at),
                        tool_calls_response=tool_calls_response,
                    ),
                )

        if self.model_provider == 'doubao':
            if last_chunk and temp_exp_str:
                last_delta = last_chunk.choices[0]
                full_assistant_content += temp_exp_str
                yield LLMResultChunk(
                    model_provider=self.model_provider,
                    model=last_chunk.model,
                    model_params=model_parameters,
                    prompt_messages=prompt_messages,
                    system_fingerprint=last_chunk.system_fingerprint,
                    delta=LLMResultChunkDelta(
                        index=last_delta.index,
                        message=AssistantPromptMessage(content=temp_exp_str),
                        usage=LLMUsage.only_latency_usage(time.perf_counter() - self.started_at),
                        tool_calls_response=tool_calls_response,
                    ),
                )

        if not full_assistant_content:
            raise LLMRequestError(detail_err="LLM response is empty")

        if not prompt_tokens:
            prompt_tokens = self._num_tokens_from_messages(model, prompt_messages)

        if not completion_tokens:
            full_assistant_prompt_message = AssistantPromptMessage(
                content=full_assistant_content, tool_calls=final_tool_calls
            )
            completion_tokens = self._num_tokens_from_messages(model, [full_assistant_prompt_message])

        # transform usage
        usage = self._calc_response_usage(prompt_tokens, completion_tokens)
        final_chunk.delta.usage = usage

        yield final_chunk

    def _handle_chat_generate_stream_response_by_result(
        self,
        llm_result: LLMResult
    ) -> Generator:
        yield LLMResultChunk(
            model_provider=self.model_provider,
            model=llm_result.model,
            model_params=llm_result.model_params,
            prompt_messages=llm_result.prompt_messages,
            delta=LLMResultChunkDelta(
                index=0,
                message=llm_result.message,
                usage=llm_result.usage,
                tool_calls_response=llm_result.tool_calls_response,
            ),
        )

    def _handle_chat_generate_response(
        self,
        model: str,
        response: ChatCompletion,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tool_calls_response: list[LLMToolCallResponse] | None = None
    ) -> LLMResult:
        """
        Handle llm chat response

        :param model: model
        :param response: response
        :param prompt_messages: prompt messages
        :param model_parameters:
        :return: llm response
        """
        if tool_calls_response is None:
            tool_calls_response = []

        assistant_message = response.choices[0].message
        if not assistant_message.content:
            raise LLMRequestError(detail_err=f"LLM response is empty")

        # 记录思考内容, 确保没有思考也能过去
        try:
            reasoning_content = getattr(assistant_message, 'reasoning_content', '')
        except:
            reasoning_content = ''

        # 最终的助手消息
        resp_content = replace_latex(assistant_message.content)
        assistant_prompt_message = AssistantPromptMessage(content=resp_content, reasoning_content=reasoning_content)

        # calculate num tokens
        if response.usage:
            # transform usage
            prompt_tokens = response.usage.prompt_tokens
            completion_tokens = response.usage.completion_tokens
        else:
            # calculate num tokens
            prompt_tokens = self._num_tokens_from_messages(model, prompt_messages)
            completion_tokens = self._num_tokens_from_messages(model, [assistant_prompt_message])

        # transform usage
        usage = self._calc_response_usage(prompt_tokens, completion_tokens)

        # transform response
        response = LLMResult(
            model_provider=self.model_provider,
            model=response.model,
            model_params=model_parameters or {},
            prompt_messages=prompt_messages,
            message=assistant_prompt_message,
            usage=usage,
            tool_calls_response=tool_calls_response,
            system_fingerprint=response.system_fingerprint,
        )

        return response

    def _handle_chat_tool_generate_response(
        self,
        model: str,
        response: ChatCompletion,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
    ) -> LLMResult:
        """
        Handle llm chat response

        :param model: 模型id
        :param response: response
        :param prompt_messages: prompt messages
        :param model_parameters:
        :return: llm response
        """
        assistant_message = response.choices[0].message
        assistant_message_tool_calls = assistant_message.tool_calls

        if not assistant_message.content and not assistant_message_tool_calls:
            raise LLMRequestError(detail_err=f"LLM response is empty")

        tool_calls = self._extract_response_tool_calls(assistant_message_tool_calls)

        # 最终的助手消息
        resp_content = replace_latex(assistant_message.content)
        assistant_prompt_message = AssistantPromptMessage(content=resp_content, tool_calls=tool_calls)

        # calculate num tokens
        if response.usage:
            # transform usage
            prompt_tokens = response.usage.prompt_tokens
            completion_tokens = response.usage.completion_tokens
        else:
            # calculate num tokens
            prompt_tokens = self._num_tokens_from_messages(model, prompt_messages)
            completion_tokens = self._num_tokens_from_messages(model, [assistant_prompt_message])

        # transform usage
        usage = self._calc_response_usage(prompt_tokens, completion_tokens)

        result_tool_calls = []
        for t in tool_calls:
            result_tool_calls.append(LLMToolCallResponse(
                tool_call_id=t.id,
                name=t.function.name,
                arguments=t.function.arguments,
                content='',
            ))

        # transform response
        response = LLMResult(
            model_provider=self.model_provider,
            model=response.model,
            model_params=model_parameters or {},
            prompt_messages=prompt_messages,
            message=assistant_prompt_message,
            usage=usage,
            tool_calls_response=result_tool_calls,
            system_fingerprint=response.system_fingerprint,
        )

        return response

    def _handle_tool_prompt_messages(
        self, tool_result: LLMResult,
    ) -> list[PromptMessage]:
        """

        :param tool_result: response
        :return: llm response
        """
        if not tool_result.message.tool_calls:
            return []

        tool_call = tool_result.message.tool_calls[0]
        func_name = tool_call.function.name
        func_args = json.loads(tool_call.function.arguments)
        if func_name != 'web_search':
            raise ValueError(f'func_name: [{func_name}] not support')

        prompt_messages = tool_result.prompt_messages
        tool_res_content = bocha_web_search(func_args.get('query'))
        # TODO 判断tool_id是否一致
        tool_result.tool_calls_response[0].content = tool_res_content
        if tool_res_content:
            if tool_result.message.content:
                prompt_messages.append(tool_result.message)
            prompt_messages.append(ToolPromptMessage(
                tool_call_id=tool_call.id,
                content=tool_res_content,
                name=tool_call.function.name
            ))

        return prompt_messages

    def _extract_response_tool_calls(
        self, response_tool_calls: list[ChatCompletionMessageToolCall | ChoiceDeltaToolCall]
    ) -> list[AssistantPromptMessage.ToolCall]:
        """
        Extract tool calls from response

        :param response_tool_calls: response tool calls
        :return: list of tool calls
        """
        tool_calls = []
        if response_tool_calls:
            for response_tool_call in response_tool_calls:
                function = AssistantPromptMessage.ToolCall.ToolCallFunction(
                    name=response_tool_call.function.name, arguments=response_tool_call.function.arguments
                )

                tool_call = AssistantPromptMessage.ToolCall(
                    id=response_tool_call.id, type=response_tool_call.type, function=function
                )
                tool_calls.append(tool_call)

        return tool_calls

    def _num_tokens_from_messages(
        self, model: str, messages: list[PromptMessage]
    ) -> int:
        """Calculate num tokens for gpt-3.5-turbo and gpt-4 with tiktoken package.

        Official documentation: https://github.com/openai/openai-cookbook/blob/main/examples/How_to_format_inputs_to_ChatGPT_models.ipynb"""
        if model.startswith("ft:"):
            model = model.split(":")[1]

        # Currently, we can use gpt4o to calculate chatgpt-4o-latest's token.
        if model == "chatgpt-4o-latest" or model.startswith("o1"):
            model = "gpt-4o"

        try:
            encoding = tiktoken.encoding_for_model(model)
        except KeyError:
            logger.warning("Warning: model not found. Using cl100k_base encoding.")
            model = "cl100k_base"
            encoding = tiktoken.get_encoding(model)

        if model.startswith("gpt-3.5-turbo-0301"):
            # every message follows <im_start>{role/name}\n{content}<im_end>\n
            tokens_per_message = 4
            # if there's a name, the role is omitted
            tokens_per_name = -1
        elif model.startswith("gpt-3.5-turbo") or model.startswith("gpt-4") or model.startswith("o1"):
            tokens_per_message = 3
            tokens_per_name = 1
        else:
            raise NotImplementedError(
                f"get_num_tokens_from_messages() is not presently implemented "
                f"for model {model}."
                "See https://platform.openai.com/docs/advanced-usage/managing-tokens for "
                "information on how messages are converted to tokens."
            )
        num_tokens = 0
        messages_dict = [self._convert_prompt_message_to_dict(m) for m in messages]
        for message in messages_dict:
            num_tokens += tokens_per_message
            for key, value in message.items():
                # Cast str(value) in case the message value is not a string
                # This occurs with function messages
                # TODO: The current token calculation method for the image type is not implemented,
                #  which need to download the image and then get the resolution for calculation,
                #  and will increase the request delay

                if key == "tool_calls":
                    for tool_call in value:
                        for t_key, t_value in tool_call.items():
                            num_tokens += len(encoding.encode(t_key))
                            if t_key == "function":
                                for f_key, f_value in t_value.items():
                                    num_tokens += len(encoding.encode(f_key))
                                    num_tokens += len(encoding.encode(f_value))
                            else:
                                num_tokens += len(encoding.encode(t_key))
                                num_tokens += len(encoding.encode(t_value))
                else:
                    if isinstance(value, list):
                        text = ""
                        for item in value:
                            if isinstance(item, dict) and item["type"] == "text":
                                text += item["text"]

                        value = text

                    num_tokens += len(encoding.encode(str(value)))

                if key == "name":
                    num_tokens += tokens_per_name

        # every reply is primed with <im_start>assistant
        num_tokens += 3
        return num_tokens

    def _convert_prompt_message_to_dict(self, message: PromptMessage) -> dict:
        """
        Convert PromptMessage to dict for OpenAI API
        """
        if isinstance(message, UserPromptMessage):
            message = cast(UserPromptMessage, message)
            if isinstance(message.content, str):
                message_dict = {"role": "user", "content": message.content}
            else:
                # 文本和图片的混合
                sub_messages = []
                for message_content in message.content:
                    if message_content.type == PromptMessageContentType.TEXT:
                        message_content = cast(TextPromptMessageContent, message_content)
                        sub_message_dict = {"type": "text", "text": message_content.data}
                        sub_messages.append(sub_message_dict)
                    elif message_content.type == PromptMessageContentType.IMAGE:
                        message_content = cast(ImagePromptMessageContent, message_content)
                        sub_message_dict = {
                            "type": "image_url",
                            "image_url": {"url": message_content.data, "detail": message_content.detail.value},
                        }
                        sub_messages.append(sub_message_dict)

                message_dict = {"role": "user", "content": sub_messages}
        elif isinstance(message, AssistantPromptMessage):
            message = cast(AssistantPromptMessage, message)
            message_dict = {"role": "assistant", "content": message.content}
            if message.tool_calls:
                message_dict["tool_calls"] = [tool_call.dict() for tool_call in
                                              message.tool_calls]
                # function_call = message.tool_calls[0]
                # message_dict["function_call"] = {
                #     "name": function_call.function.name,
                #     "arguments": function_call.function.arguments,
                # }
        elif isinstance(message, SystemPromptMessage):
            message = cast(SystemPromptMessage, message)
            message_dict = {"role": "system", "content": message.content}
        elif isinstance(message, ToolPromptMessage):
            message = cast(ToolPromptMessage, message)
            message_dict = {"role": "tool", "content": message.content, "tool_call_id": message.tool_call_id}
        else:
            raise ValueError(f"Got unknown type {message}")

        if message.name:
            message_dict["name"] = message.name

        return message_dict
