<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考研复习规划</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
        }
        .left-section, .right-section {
            padding: 20px;
            box-sizing: border-box;
        }
        .left-section {
            width: 50%;
            background-color: #f9f9f9;
            border-right: 1px solid #ddd;
        }
        .right-section {
            width: 50%;
            background-color: #fff;
        }
        h2 {
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], select, textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .checkbox-group label {
            display: inline-block;
            margin-right: 10px;
        }
        .hidden {
            display: none;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result-box {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #loadingOverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        #loadingOverlay div {
            color: white;
            font-size: 24px;
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1001;
        }
        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            max-width: 400px;
            text-align: center;
        }
        .modal.hidden {
            display: none;
        }
        .college-major-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .college-major-group input {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="left-section">
        <h2>考研复习规划</h2>
        <div class="form-group">
            <label for="kaoyan-review-prompt">提示词编辑</label>
            <textarea id="kaoyan-review-prompt" rows="4" placeholder="请输入提示词">{{ kaoyan_review_plan }}</textarea>
        </div>
        <div class="form-group">
            <button id="intensive-plan-btn">生成冲刺档复习规划</button>
            <button id="steady-plan-btn">生成稳妥档复习规划</button>
        </div>
    </div>

    <div class="right-section">
        <h2>模型返回结果</h2>
        <div class="result-box" id="result-box" aria-live="polite" aria-atomic="true" aria-busy="false"></div>
        <div class="tracing-detail" id="tracingDetails"></div>
    </div>

    <div id="loadingOverlay">
        <div>加载中...</div>
    </div>

    <script>
    document.addEventListener("DOMContentLoaded", function () {
        const resultBox = document.getElementById('result-box');
        const tracingDetails = document.getElementById('tracingDetails');
        const loadingOverlay = document.getElementById('loadingOverlay');
        let message_id;

        const report_id = "01";

        function sendRequest(targetCollegeLevel) {
            message_id = null;
            // 清空追踪信息
            const tracingDetails = document.getElementById('tracingDetails');
            tracingDetails.innerHTML = '';
            // 重新获取提示词编辑窗口的内容
            const kaoyanReviewPrompt = document.getElementById('kaoyan-review-prompt').value;

            loadingOverlay.style.display = 'flex';
            fetch("{% url 'kaoyan_review_plan' %}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({
                    target_college_level: targetCollegeLevel,
                    kaoyan_review_plan: kaoyanReviewPrompt,
                    report_id: report_id
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.body;
            })
            .then(stream => {
                const reader = stream.getReader();
                const decoder = new TextDecoder();
                let result_text = '';

                function readChunk() {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            console.log('Stream complete, returning collected data');
                            find_tracing();
                            return;
                        }
                        const textChunkStr = decoder.decode(value, { stream: true });
                        const chunkArr = textChunkStr.split("\n\n");
                        for (let i = 0; i < chunkArr.length; i++) {
                            const textChunk = chunkArr[i];
                            if (textChunk.startsWith("data: ")) {
                                const json_str = textChunk.slice(6);
                                console.log('json_str', json_str);
                                try {
                                    const jsonData = JSON.parse(json_str);
                                    if (!message_id) {
                                        message_id = jsonData.message_id;
                                    }
                                    if (jsonData.answer) {
                                        result_text += jsonData.answer;
                                        resultBox.innerHTML = marked.parse(result_text);
                                    } else if (jsonData.err) {
                                        resultBox.innerHTML = jsonData.err;
                                    }
                                } catch (error) {
                                    console.error('Error parsing JSON', json_str, error);
                                }
                            }
                        }
                        readChunk();
                    }).catch(error => {
                        console.error('Error collecting stream data', error);
                    });
                }
                readChunk();
            })
            .catch(error => {
                console.error('Error:', error);
                resultBox.innerHTML = '<p>生成建议失败，请重试。</p>';
            })
            .finally(() => {
                loadingOverlay.style.display = 'none';
            });
        }

        document.getElementById('intensive-plan-btn').addEventListener('click', function() {
            sendRequest('intensive');
        });

        document.getElementById('steady-plan-btn').addEventListener('click', function() {
            sendRequest('steady');
        });

        function find_tracing() {
            if (!message_id) return;
            const $el = $('#tracingDetails');
            $.ajax({
                type: 'GET',
                url: "{% url 'message_tracing' %}",
                data: { message_id: message_id },
                success: function(response) {
                    $('#loadingOverlay').css('display', 'none');
                    const results = response.data;

                    $el.empty();

                    if (results && results.tracing) {
                        results.tracing.forEach(function(traceDetail) {
                            const detailElement = document.createElement('p');
                            detailElement.textContent = traceDetail;
                            $el.append(detailElement);
                        });
                    } else {
                        $el.append('<p>没有找到追踪信息。</p>');
                    }
                },
                error: function(xhr, status, error) {
                    alert('请求失败');
                    $('#loadingOverlay').css('display', 'none');
                    console.error('AJAX Error:', xhr.responseText);
                }
            });
        }
    });
    </script>
</body>
</html>
