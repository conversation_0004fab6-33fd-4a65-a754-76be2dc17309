# Generated by Django 4.2 on 2025-03-16 11:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0028_merge_20250316_1307'),
    ]

    operations = [
        migrations.AddField(
            model_name='appmodelconfig',
            name='support_tools',
            field=models.JSONField(null=True, verbose_name='支持工具'),
        ),
        migrations.CreateModel(
            name='MessageToolCall',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('tool_call_id', models.CharField(blank=True, max_length=100, verbose_name='工具id')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='工具名称')),
                ('arguments', models.CharField(blank=True, max_length=255, verbose_name='工具调用参数')),
                ('content', models.TextField(null=True, verbose_name='工具调用结果')),
                ('message', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
            ],
            options={
                'verbose_name': '消息工具调用',
                'verbose_name_plural': '消息工具调用',
                'db_table': 'ai_message_tool_call',
            },
        ),
    ]
