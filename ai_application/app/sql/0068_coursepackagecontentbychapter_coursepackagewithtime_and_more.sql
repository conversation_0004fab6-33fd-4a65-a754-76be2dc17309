--
-- <PERSON>reate model Course<PERSON>ackageContentByChapter
--
CREATE TABLE `ai_course_package_content_by_chapter`
(
    `id`                                       bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`                                 datetime(6) NOT NULL,
    `modified_time`                            datetime(6) NOT NULL,
    `is_deleted`                               bool        NOT NULL,
    `chapter_name`                             varchar(36) NOT NULL,
    `unit_name`                                varchar(36) NOT NULL,
    `subject`                                  varchar(36) NULL,
    `course_package_name`                      varchar(36) NULL,
    `stage_name`                               varchar(36) NULL,
    `combine_unit_name`                        varchar(36) NULL,
    `study_target`                             longtext NULL,
    `study_guidence`                           longtext NULL,
    `chapter_task`                             longtext NULL,
    `chapter_date`                             varchar(36) NULL,
    `spend_time`                               varchar(36) NULL,
    `major_or_minor`                           varchar(36) NULL,
    `knowledge_exam_count_and_avg_difficulity` json NULL,
    `important_degree`                         varchar(36) NULL
);
--
-- <PERSON>reate model CoursePackageWithTime
--
CREATE TABLE `ai_course_package_time`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool NOT NULL,
    `subject`       varchar(36) NULL,
    `course_date`   varchar(36) NULL,
    `course_name`   longtext NULL
);
--
-- Create model KaoGangAnalysis
--
CREATE TABLE `ai_kaogang_analysis`
(
    `id`              bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`        datetime(6) NOT NULL,
    `modified_time`   datetime(6) NOT NULL,
    `is_deleted`      bool NOT NULL,
    `subject`         varchar(36) NULL,
    `kaogang_content` longtext NULL,
    `kaogang_logic`   longtext NULL
);
--
-- Create model PersonalizedExamSyllabus
--
CREATE TABLE `ai_personalized_exam_syllabus`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool NOT NULL,
    `student_score` integer NULL,
    `exam_syllabus` longtext NULL,
    `subject`       varchar(36) NULL
);
--
-- Add field important_sections to coursepackagecontent
--
ALTER TABLE `ai_course_package_content`
    ADD COLUMN `important_sections` longtext NULL;
--
-- Add field unit_date to coursepackagecontent
--
ALTER TABLE `ai_course_package_content`
    ADD COLUMN `unit_date` varchar(36) NULL;
--
-- Add field priority to examanalysisknowledgepointwithstats
--
ALTER TABLE `ai_exam_analysis_knowledge_point_with_stats`
    ADD COLUMN `priority` varchar(100) NULL;
--
-- Alter field target on superviseinitstudentstatus
--
-- (no-op)
CREATE INDEX `ai_course_package_content_by_chapter_add_time_c38541eb` ON `ai_course_package_content_by_chapter` (`add_time`);
CREATE INDEX `ai_course_package_content_by_chapter_chapter_name_17728fdb` ON `ai_course_package_content_by_chapter` (`chapter_name`);
CREATE INDEX `ai_course_package_content_by_chapter_unit_name_15021d36` ON `ai_course_package_content_by_chapter` (`unit_name`);
CREATE INDEX `ai_course_package_time_add_time_ce3819cb` ON `ai_course_package_time` (`add_time`);
CREATE INDEX `ai_kaogang_analysis_add_time_bd4999c9` ON `ai_kaogang_analysis` (`add_time`);
CREATE INDEX `ai_personalized_exam_syllabus_add_time_fb5be052` ON `ai_personalized_exam_syllabus` (`add_time`);