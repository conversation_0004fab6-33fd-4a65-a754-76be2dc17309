from django.db import models
from ._base import BaseModel

class SubjecttoKnowledge(BaseModel):
    subject_1 = models.CharField(max_length=100, verbose_name="一级学科")
    subject_2 = models.CharField(max_length=100, verbose_name="二级学科")
    categories = models.CharField(max_length=100, verbose_name="章节")
    knowledge_points = models.CharField(max_length=100, verbose_name="知识点")

    class Meta:
        verbose_name = verbose_name_plural = "学科知识点"
        db_table = 'ai_subject_to_knowledge'

    def __str__(self):
        return f"{self.subject_1} - {self.subject_2} - {self.categories} - {self.knowledge_points}"