from rest_framework import serializers

from app.models import Message, UnifiedExaminationQuestion
from app.models import EnglishWaikanTestRecord, EnglishWaikanSubQuestion, EnglishWordTestAnswerDetail
from app.models import EnWordReciteBasicAnswerDetail, SuperviseLearnStat


class MessageListSerializer(serializers.ModelSerializer):
    # app_id = serializers.CharField(source='app_no')
    app_no = serializers.CharField(source='app.app_no')
    app_type = serializers.CharField(source='app.app_type')
    app_name = serializers.CharField(source='app.name')
    question_img = serializers.SerializerMethodField()
    sensitive_content = serializers.SerializerMethodField()

    class Meta:
        model = Message
        fields = [
            'id', 'add_time', 'message_no', 'query', 'answer', 'message_tokens', 'answer_tokens',
            'is_exception', 'exception_reason', 'exception_image', 'question_img', 'message_type',
            'userinfo', 'from_biz_id', 'sensitive_content', 'app_no', 'app_type', 'app_name', 'userinfo',
            'stopped_by',
        ]

    def get_sensitive_content(self, obj: Message) -> str:
        return obj.sensitive_content or ''

    def get_question_img(self, obj: Message) -> str:
        if obj.message_type in (Message.MessageType.question, Message.MessageType.math_question):
            file_objs_list = obj.file_objs_list
            return file_objs_list[0] if file_objs_list else ''
        elif obj.app.app_type in ('math_problem_solving', 'en_article_judgment', 'chat_app2'):
            file_objs_list = obj.file_objs_list
            return file_objs_list[0] if file_objs_list else ''
        return ''


class UnifiedExaminationQuestionSerializer(serializers.ModelSerializer):
    library = serializers.CharField(source='get_library_display')

    class Meta:
        model = UnifiedExaminationQuestion
        fields = [
            'question_num', 'question_no', 'year', 'title', 'analysis', 'llm_analysis',
            'library', 'answer', 'choice',
        ]


class WaikanQuestionSerializer(serializers.ModelSerializer):
    query = serializers.SerializerMethodField()
    app_name = serializers.SerializerMethodField()
    goods_name = serializers.SerializerMethodField()
    platform = serializers.SerializerMethodField()
    message_tokens = serializers.SerializerMethodField()
    answer_tokens = serializers.SerializerMethodField()

    class Meta:
        model = EnglishWaikanTestRecord
        fields = [
            'id', 'add_time', 'user_id', 'query', 'app_name', 'goods_name', 'platform', 'is_exception',
            'message_tokens', 'answer_tokens',
        ]

    def get_app_name(self, _):
        return '外刊模拟出题'

    def get_query(self, obj: EnglishWaikanTestRecord):
        m = {0: 'A', 1: 'B', 2: 'C', 3: 'D'}
        query = f'# 题目\n\n' + obj.question.article
        for idx, sq in enumerate(EnglishWaikanSubQuestion.objects.filter(id__in=obj.sub_question_ids), start=1):
            query += f'\n{idx} {sq.question_stem}\n'
            for o, option in enumerate(sq.question_options):
                query += f'\n{m.get(o)} {option}\n'

        return query

    def get_goods_name(self, _):
        return ''

    def get_platform(self, _):
        return ''

    def get_message_tokens(self, _):
        return ''

    def get_answer_tokens(self, _):
        return ''


class EnWordTestSerializer(serializers.ModelSerializer):
    user_id = serializers.CharField(source='record.user_id')
    query = serializers.SerializerMethodField()
    app_name = serializers.SerializerMethodField()
    goods_name = serializers.SerializerMethodField()
    platform = serializers.SerializerMethodField()
    answer = serializers.CharField(source='user_answer')
    message_tokens = serializers.SerializerMethodField()
    answer_tokens = serializers.SerializerMethodField()

    class Meta:
        model = EnglishWordTestAnswerDetail
        fields = [
            'id', 'add_time', 'user_id', 'query', 'app_name', 'goods_name', 'platform', 'answer', 'is_exception',
            'message_tokens', 'answer_tokens',
        ]

    def get_app_name(self, _):
        return '英语词汇训练'

    def get_query(self, obj: EnglishWordTestAnswerDetail):
        m = {0: 'A', 1: 'B', 2: 'C', 3: 'D'}
        s = obj.question.question + '\n'
        for idx, a in enumerate(obj.question.options):
            s += f'\n{m.get(idx)} {a}\n'
        return s
    def get_goods_name(self, _):
        return ''

    def get_platform(self, _):
        return ''

    def get_message_tokens(self, _):
        return ''

    def get_answer_tokens(self, _):
        return ''


class EnWordReciteSerializer(serializers.ModelSerializer):
    user_id = serializers.CharField(source='basic_paper.user_id')
    query = serializers.SerializerMethodField()
    app_name = serializers.SerializerMethodField()
    goods_name = serializers.SerializerMethodField()
    platform = serializers.SerializerMethodField()
    answer = serializers.CharField(source='user_answer')
    message_tokens = serializers.SerializerMethodField()
    answer_tokens = serializers.SerializerMethodField()

    class Meta:
        model = EnWordReciteBasicAnswerDetail
        fields = [
            'id', 'add_time', 'user_id', 'query', 'app_name', 'goods_name', 'platform', 'answer', 'is_exception',
            'message_tokens', 'answer_tokens',
        ]

    def get_app_name(self, _):
        return '英语单词背诵'

    def get_query(self, obj: EnWordReciteBasicAnswerDetail):
        m = {0: 'A', 1: 'B', 2: 'C', 3: 'D'}
        s = obj.question.question + '\n'
        for idx, a in enumerate(obj.question.options):
            s += f'\n{m.get(idx)} {a}\n'
        return s

    def get_goods_name(self, _):
        return ''

    def get_platform(self, _):
        return ''

    def get_message_tokens(self, _):
        return ''

    def get_answer_tokens(self, _):
        return ''


class SuperviseLearnStatSerializer(serializers.ModelSerializer):
    user_id = serializers.CharField()
    app_name = serializers.SerializerMethodField()
    goods_name = serializers.SerializerMethodField()
    platform = serializers.SerializerMethodField()
    answer = serializers.CharField(source='analysis')
    message_tokens = serializers.SerializerMethodField()
    answer_tokens = serializers.SerializerMethodField()
    is_exception = serializers.SerializerMethodField()

    class Meta:
        model = SuperviseLearnStat
        fields = [
            'id', 'add_time', 'user_id', 'query', 'app_name', 'goods_name', 'platform', 'answer', 'is_exception',
            'message_tokens', 'answer_tokens',
        ]

    def get_is_exception(self, obj: SuperviseLearnStat):
        return obj.status != 'SUCCESS'

    def get_app_name(self, _):
        return '教务督学'

    def get_goods_name(self, _):
        return ''

    def get_platform(self, _):
        return ''

    def get_message_tokens(self, _):
        return ''

    def get_answer_tokens(self, _):
        return ''


from rest_framework import serializers
from ..models import UndergraduateCategory, GraduateCategory, UndergraduateMajor, GraduateMajor, CategoryCorrelation, \
    MajorCorrelation


class UndergraduateCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = UndergraduateCategory
        fields = '__all__'


class GraduateCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = GraduateCategory
        fields = '__all__'


class UndergraduateMajorSerializer(serializers.ModelSerializer):
    class Meta:
        model = UndergraduateMajor
        fields = '__all__'


class GraduateMajorSerializer(serializers.ModelSerializer):
    class Meta:
        model = GraduateMajor
        fields = '__all__'


class CategoryCorrelationSerializer(serializers.ModelSerializer):
    undergrad_category_name = serializers.CharField(source='undergrad_category.name', read_only=True)
    grad_category_name = serializers.CharField(source='grad_category.name', read_only=True)

    class Meta:
        model = CategoryCorrelation
        fields = ['id', 'undergrad_category', 'grad_category', 'level', 'undergrad_category_name', 'grad_category_name']


class MajorCorrelationSerializer(serializers.ModelSerializer):
    undergrad_major_name = serializers.CharField(source='undergrad_major.name', read_only=True)
    grad_major_name = serializers.CharField(source='grad_major.name', read_only=True)
    undergrad_category_name = serializers.CharField(source='undergrad_major.category.name', read_only=True)
    grad_category_name = serializers.CharField(source='grad_major.category.name', read_only=True)

    class Meta:
        model = MajorCorrelation
        fields = ['id', 'undergrad_major', 'grad_major', 'level', 'undergrad_major_name', 'grad_major_name',
                  'undergrad_category_name', 'grad_category_name']