<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智学视听搜索</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body {
            padding: 20px;
        }
        .script-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            width: 100%;
        }
        .container {
            max-width: 95%;
        }
        .search-container {
            max-width: 800px;
            margin: 0 auto 20px;
        }
        .script-column {
            flex: 1;
            min-height: 600px;
            min-width: 45%;
        }
        .script-column .script-items-container {
            height: 550px;
            overflow-y: auto;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            padding: 10px;
            background-color: white;
        }
        .script-item {
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .script-item-id {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .script-item-content {
            width: 100%;
            min-height: 60px;
            padding: 5px;
            border: 1px solid #ced4da;
            border-radius: 3px;
        }
        .script-header {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h2 class="mb-3">智学视听搜索</h2>
                <div class="d-flex justify-content-between mb-3 search-container">
                    <button class="btn btn-secondary" onclick="window.location.href='/console/app/text_to_video'">返回</button>
                    <div class="input-group" style="width: calc(100% - 360px)">
                        <input type="text" id="searchQuery" class="form-control" placeholder="输入知识点...">
                        <button class="btn btn-primary" id="searchBtn">生成文案旁白</button>
                    </div>
                    <button class="btn btn-success" id="generateVideoBtn">生成视频</button>
                    <button class="btn btn-info" id="historyBtn">生成历史</button>
                </div>
                <div id="loadingIndicator" class="text-center mt-2" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <span class="ms-2">正在生成文案和旁白...</span>
                </div>
                <div id="videoLoading" class="text-center mt-2" style="display: none;">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <span class="ms-2">正在生成视频...</span>
                </div>
                <div class="script-container">
                    <div class="script-column">
                        <div class="script-header">文案内容</div>
                        <div id="scriptTextOutput" class="script-items-container">
                            <!-- 动态生成的脚本条目将放在这里 -->
                        </div>
                        <button class="btn btn-secondary mt-2" id="saveTextBtn">保存文案修改</button>
                    </div>
                    <div class="script-column">
                        <div class="script-header">旁白内容</div>
                        <div id="scriptAudioOutput" class="script-items-container">
                            <!-- 动态生成的音频条目将放在这里 -->
                        </div>
                        <button class="btn btn-secondary mt-2" id="saveAudioBtn">保存旁白修改</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    let lastSearchResult = null;

    $(document).ready(function() {
        $('#searchBtn').click(function() {
            const query = $('#searchQuery').val().trim();
            if (!query) {
                alert('请输入搜索内容');
                return;
            }

            $('#loadingIndicator').show();
            $('#searchBtn').prop('disabled', true);
            
            $.ajax({
                url: "{% url 'text_to_video_search' %}",
                method: "POST",
                data: {
                    'query': query,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                complete: function() {
                    $('#loadingIndicator').hide();
                    $('#searchBtn').prop('disabled', false);
                },
                success: function(response) {
                    // 保存搜索结果（包含原始script和kp数据）
                    // 保存搜索结果（包含原始script和kp数据）
                    // 确保response包含kp字段
                    console.log('Search response:', response); // 调试日志
                    lastSearchResult = {
                        script: [],
                        kp: response.kp || '',
                        text: response.text,
                        audio: response.audio,
                        title: response.title || ''
                    };
                    // 从text和audio数据重建script结构
                    const textItems = response.text.split('\n');
                    const audioItems = response.audio.split('\n');
                    const titleItems = response.title.split('\n');
                    
                    lastSearchResult.script = textItems.map((textItem, index) => {
                        const [id, ...textParts] = textItem.split(' ');
                        const audioItem = audioItems[index] || '';
                        const [audioId, ...audioParts] = audioItem.split(' ');
                        const titleItem = titleItems[index] || '';
                        const [titleid, ...titleParts] = titleItem.split(' ')

                        return {
                            id: id,
                            text: textParts.join(' '),
                            audio: audioParts.join(' '),
                            title: titleParts.join(' ')
                        };
                    });
                    
                    // 清空容器
                    $('#scriptTextOutput').empty();
                    $('#scriptAudioOutput').empty();
                    
                    // 生成文本条目
                    textItems.forEach(item => {
                        const [id, ...contentParts] = item.split(' ');
                        const content = contentParts.join(' ');
                        $('#scriptTextOutput').append(`
                            <div class="script-item">
                                <div class="script-item-id">ID: ${id} - Title: ${lastSearchResult.script.find(s => s.id === id)?.title || ''}</div>
                                <textarea class="script-item-content">${content}</textarea>
                            </div>
                        `);
                    });
                    
                    // 生成音频条目
                    audioItems.forEach(item => {
                        const [id, ...contentParts] = item.split(' ');
                        const content = contentParts.join(' ');
                        $('#scriptAudioOutput').append(`
                            <div class="script-item">
                                <div class="script-item-id">ID: ${id} - Title: ${lastSearchResult.script.find(s => s.id === id)?.title || ''}</div>
                                <textarea class="script-item-content">${content}</textarea>
                            </div>
                        `);
                    });
                },
                error: function(xhr, status, error) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.error) {
                            alert(response.error);
                        } else {
                            alert('搜索失败: ' + error);
                        }
                    } catch (e) {
                        alert('搜索失败: ' + error);
                    }
                }
            });
        });

        // 生成视频
        $('#generateVideoBtn').click(function() {
            if (!lastSearchResult) {
                alert('请先进行搜索获取内容');
                return;
            }

            $('#videoLoading').show();
            $('#generateVideoBtn').prop('disabled', true);
            
            // 收集修改过的内容
            const modifiedText = [];
            $('#scriptTextOutput .script-item').each(function() {
                const idRaw = $(this).find('.script-item-id').text().replace('ID: ', '').trim();
                const idMatch = idRaw.match(/^\d+/);
                const id = idMatch ? idMatch[0] : '';
                const content = $(this).find('.script-item-content').val();
                modifiedText.push({
                    id: id,
                    text: content
                });
            });

            const modifiedAudio = [];
            $('#scriptAudioOutput .script-item').each(function() {
                const idRaw = $(this).find('.script-item-id').text().replace('ID: ', '').trim();
                const idMatch = idRaw.match(/^\d+/);
                const id = idMatch ? idMatch[0] : '';
                const content = $(this).find('.script-item-content').val();
                modifiedAudio.push({
                    id: id,
                    audio: content
                });
            });

            $.ajax({
                url: "{% url 'text_to_video_search' %}/generate_video",
                method: "POST",
                data: {
                    'original_script': JSON.stringify(lastSearchResult.script),
                    'modified_text': JSON.stringify(modifiedText),
                    'modified_audio': JSON.stringify(modifiedAudio),
                    'kp_data': lastSearchResult.kp,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                complete: function() {
                    $('#videoLoading').hide();
                    $('#generateVideoBtn').prop('disabled', false);
                },
                success: function(response) {
                    if (response.video_url) {
                        // 创建并显示视频模态框
                        const modalHtml = `
                            <div class="modal fade" id="videoModal" tabindex="-1" aria-labelledby="videoModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="videoModalLabel">视频生成成功</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="ratio ratio-16x9 mb-3">
                                                <iframe src="${response.video_url}" allowfullscreen></iframe>
                                            </div>
                                            <div class="input-group mb-3">
                                                <input type="text" class="form-control" id="videoUrlInput" value="${response.video_url}" readonly>
                                                <button class="btn btn-outline-secondary" type="button" id="copyUrlBtn">复制URL</button>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        // 添加模态框到DOM
                        $('body').append(modalHtml);
                        
                        // 初始化模态框并显示
                        const videoModal = new bootstrap.Modal(document.getElementById('videoModal'));
                        videoModal.show();
                        
                        // 复制URL功能
                        $('#copyUrlBtn').click(function() {
                            const urlInput = document.getElementById('videoUrlInput');
                            urlInput.select();
                            document.execCommand('copy');
                            $(this).text('已复制');
                            setTimeout(() => {
                                $(this).text('复制URL');
                            }, 2000);
                        });
                        
                        // 模态框关闭时移除
                        $('#videoModal').on('hidden.bs.modal', function () {
                            $(this).remove();
                        });
                    } else {
                        alert('视频生成完成，但未返回URL');
                    }
                },
                error: function(xhr, status, error) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.error) {
                            alert(response.error);
                        } else {
                            alert('视频生成失败: ' + error);
                        }
                    } catch (e) {
                        alert('视频生成失败: ' + error);
                    }
                }
            });
        });

        // 保存文本修改
        $('#saveTextBtn').click(function() {
            const modifiedItems = [];
            $('#scriptTextOutput .script-item').each(function() {
                const id = $(this).find('.script-item-id').text().replace('ID: ', '').trim();
                const content = $(this).find('.script-item-content').val();
                modifiedItems.push({
                    id: id,
                    text: content
                });
            });

            $.ajax({
                url: "{% url 'text_to_video_search' %}/save_text",
                method: "POST",
                data: {
                    'modified_items': JSON.stringify(modifiedItems),
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function() {
                    alert('文案修改已保存');
                },
                error: function(xhr, status, error) {
                    alert('保存失败: ' + error);
                }
            });
        });

        // 保存音频修改
        $('#saveAudioBtn').click(function() {
            const modifiedItems = [];
            $('#scriptAudioOutput .script-item').each(function() {
                const id = $(this).find('.script-item-id').text().replace('ID: ', '').trim();
                const content = $(this).find('.script-item-content').val();
                modifiedItems.push({
                    id: id,
                    audio: content
                });
            });

            $.ajax({
                url: "{% url 'text_to_video_search' %}/save_audio",
                method: "POST",
                data: {
                    'modified_items': JSON.stringify(modifiedItems),
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function() {
                    alert('旁白修改已保存');
                },
                error: function(xhr, status, error) {
                    alert('保存失败: ' + error);
                }
            });
        });
    });
        // 更新分页导航
        function updatePagination(currentPage, totalPages) {
            const pagination = $('#historyPagination');
            pagination.empty();
            
            // 上一页按钮
            pagination.append(`
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a>
                </li>
            `);
            
            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `);
            }
            
            // 下一页按钮
            pagination.append(`
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a>
                </li>
            `);
            
            // 分页按钮点击事件 - 使用事件委托避免重复绑定
            pagination.off('click', '.page-link').on('click', '.page-link', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const page = $(this).data('page');
                loadHistoryPage(page);
            });
        }
        
        // 加载指定页的历史记录
        function loadHistoryPage(page) {
            const userInfo = JSON.stringify({"id": "1", "name": "test"}); // 使用示例用户信息
            
            $.ajax({
                url: "{% url 'text_to_video_search' %}/get_history",
                method: "POST",
                data: {
                    'user_info': userInfo,
                    'page': page,
                    'csrfmiddlewaretoken': '{{ csrf_token }}'
                },
                success: function(response) {
                    if (response.history && response.history.length > 0) {
                        // 清空历史记录容器
                        $('#historyList').empty();
                        
                        // 添加历史记录项
                        response.history.forEach(item => {
                            const encodedScript = encodeURIComponent(JSON.stringify(item.script)); // 对 script 编码

                            $('#historyList').append(`
                                <div class="history-item">
                                    <div class="history-name">
                                        <a href="${item.video}" target="_blank">${item.name}</a>
                                    </div>
                                    <div class="history-time">${item.time}</div>
                                    <button class="btn btn-sm btn-primary view-script-btn" data-script="${encodedScript}">查看文案及旁白</button>
                                </div>
                            `);
                        });
                        
                        // 更新分页导航
                        updatePagination(response.current_page, response.total_pages);
                        
                        // 显示模态框 - 确保只初始化一次
                        let historyModal = bootstrap.Modal.getInstance(document.getElementById('historyModal'));
                        if (!historyModal) {
                            historyModal = new bootstrap.Modal(document.getElementById('historyModal'));
                        }
                        historyModal.show();
                    } else {
                        alert('没有找到历史记录');
                    }
                },
                error: function(xhr, status, error) {
                    alert('获取历史记录失败: ' + error);
                }
            });
        }
        
        // 获取生成历史
        $('#historyBtn').click(function() {
            loadHistoryPage(1); // 默认加载第一页
        });

        // 查看文案及旁白按钮点击事件
        $(document).on('click', '.view-script-btn', function() {
            const scriptEncoded = $(this).data('script');
            let scriptData;

            try {
                scriptData = JSON.parse(decodeURIComponent(scriptEncoded)); // 解码后解析
            } catch (e) {
                alert('脚本解析失败，请检查数据格式');
                return;
            }

            // 清空容器
            $('#scriptTextOutput').empty();
            $('#scriptAudioOutput').empty();

            // 保存当前script数据
            lastSearchResult = {
                script: scriptData,
                kp: '',
                text: scriptData.map(item => `${item.id} ${item.text}`).join('\n'),
                audio: scriptData.map(item => `${item.id} ${item.audio}`).join('\n'),
                title: scriptData.map(item => `${item.id} ${item.title}`).join('\n')
            };

            // 生成文本条目
            scriptData.forEach(item => {
                $('#scriptTextOutput').append(`
                    <div class="script-item">
                        <div class="script-item-id">ID: ${item.id} - Title: ${item.title}</div>
                        <textarea class="script-item-content">${item.text}</textarea>
                    </div>
                `);
            });

            // 生成音频条目
            scriptData.forEach(item => {
                $('#scriptAudioOutput').append(`
                    <div class="script-item">
                        <div class="script-item-id">ID: ${item.id} - Title: ${item.title}</div>
                        <textarea class="script-item-content">${item.audio}</textarea>
                    </div>
                `);
            });

            // 关闭历史记录模态框
            const historyModal = bootstrap.Modal.getInstance(document.getElementById('historyModal'));
            if (historyModal) {
                historyModal.hide();
            }
        });
    </script>

    <!-- 历史记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="historyModalLabel">生成历史记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="historyList" class="history-container">
                        <!-- 历史记录将动态加载到这里 -->
                    </div>
                    <!-- 分页导航 -->
                    <nav aria-label="历史记录分页" class="mt-3">
                        <ul id="historyPagination" class="pagination justify-content-center">
                            <!-- 分页按钮将动态加载到这里 -->
                        </ul>
                    </nav>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .history-container {
            max-height: 500px;
            overflow-y: auto;
        }
        .history-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .history-name {
            flex: 1;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .history-time {
            width: 160px;
            text-align: right;
            flex-shrink: 0;
        }
        .view-script-btn {
            white-space: nowrap;
            flex-shrink: 0;
        }
        .history-item:last-child {
            border-bottom: none;
        }
        .history-name a {
            color: #0068fa;
            text-decoration: none;
        }
        .history-name a:hover {
            text-decoration: underline;
        }
        .history-time {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</body>
</html>
