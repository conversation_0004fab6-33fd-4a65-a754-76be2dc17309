# 阿纪
import http.client
import json

from app.errors import ParameterError


def paper_answer_detail(answer_id):
    conn = http.client.HTTPSConnection("ai.yantucs.com")
    payload = ''
    headers = {}
    conn.request("GET", f"/api/v1/skip/paper_answer_detail?answer_id={answer_id}", payload, headers)
    res = conn.getresponse()
    data = res.read()
    response_data = json.loads(data.decode("utf-8"))

    # 检查响应的 code 字段
    if response_data.get('code') != 0:
        raise ParameterError(f"获取用户答卷信息接口调取失败: {response_data.get('msg', '未知错误')}")

    return response_data

