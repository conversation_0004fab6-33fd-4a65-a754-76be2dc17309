from app.core.prompt.utils.prompt_template_parser import PromptTemplateParser

CONTEXT_QUERY_PROMPT_TMPL_EN = """{{pre_prompt}}

# Guidelines
Use the following context as your learned knowledge, inside <context></context> XML tags. No additional knowledge required.

<context>
{{context}}
</context>

When answer to user:
- If you don't know, just say that you don't know.
- If you don't know when you are not sure, just say that you don't know.
- Avoid mentioning that you obtained the information from the context.
- Answer according to the language of the user's question.

Answer the question: {{question}}"""


CONTEXT_QUERY_PROMPT_TMPL_ZH = """{{pre_prompt}}

# Guidelines

基于“<context>”至“</context>”中的知识片段回答用户的问题，如果不能根据上下文获得答案，则回复“不知道”，不要额外回复。
注意：根据用户问题和上下文的语言返回对应语言的回答。

<context>
{{context}}
</context>

用户问题: {{question}}"""

CONTEXT_QUERY_PROMPT = PromptTemplateParser(CONTEXT_QUERY_PROMPT_TMPL_ZH)
