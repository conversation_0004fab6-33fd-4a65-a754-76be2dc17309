<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语长难句语法分析助手</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        .container {
            max-width: 1500px;
            width: 100%;
        }
        label {
            font-weight: bold;
            margin-top: 15px;
        }
        textarea, input {
            width: 95%;
            padding: 10px;
            margin-top: 5px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        /* Adjusting the height of the textareas */
        #user-question {
            height: 150px; /* Increase the height of the input textarea */
        }
        button {
            width: 100%;
            padding: 10px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .prompt_input {
            resize: vertical;
            height: 300px; /* 设置一个初始高度 */
            width: 600px;
        }
        /* 新增弹出层样式 */
      .promptManagementModal {
        display: none;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background-color: #fff;
        padding: 20px;
        border: 1px solid #ccc;
        z-index: 1000;
      }
        /* 遮罩层样式 */
      #overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
      }
       .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
       }
       .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
       }
        #ocrLoadingOverlay {
        display: none; /* 初始时隐藏 */
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
        z-index: 1000; /* 确保加载框在最上层 */
        justify-content: center;
        align-items: center;
        }

        #ocrLoadingOverlay .loading-content p {
            font-size: 16px;
            color: #333;
        }

        .request_container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px
        }
        .upload_container {
            width: 25%;
        }
        .query_container {
            width: 70%;
        }
        .results-container {
            display: flex;
            justify-content: space-between;
        }
        .result-view {
            width: 70%;
        }
        .tracing-detail {
            width: 25%;
        }
        #tracingDetails{
            white-space: pre-line;
        }
        #tracingDetails p{
            border-bottom: 1px solid red;
            margin-bottom: 5px;
        }

    </style>
</head>
<body>

<div class="container">
    <h2>英语长难句语法分析助手</h2>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>奋力分析中...</p>
        </div>
    </div>

    <div class="loading-overlay" id="ocrLoadingOverlay">
        <div class="loading-content">
            <p>努力识别中...</p>
        </div>
    </div>
    <!-- 新增遮罩层 -->
    <div id="overlay"></div>

    <div style="display: flex; margin-bottom: 20px">
        <div>
            <button id="complex_sentence_analysis_btn" class="prompt-button">提示词编辑</button>
            <div id="complex_sentence_analysis_modal" class="promptManagementModal">
              <h2>提示词编辑</h2>
                <textarea id="complex_sentence_analysis_prompt" class="prompt_input" name="complex_sentence_analysis">{{ complex_sentence_analysis }}</textarea><br>
                <button id="confirm_complex_sentence_analysis" type="button">确定</button>
                <button id="cancel_complex_sentence_analysis" type="button">取消</button>
            </div>
        </div>


    </div>

    <div class="request_container">
        <div class="upload_container">
            <label for="user-question">上传图片：</label>
            <form id="uploadForm" action="{% url 'ocr_result' %}">
                <!-- 使用capture属性指定输入类型为摄像头 -->
                <div class="form-group" style="margin-bottom: 20px">
                    <input id="image" type="file" name="image" accept="image/*">
                </div>

                <button id="uploadBtn" type="button">OCR识别</button>
            </form>
            <!-- 用于显示拍摄的照片 -->
            <img id="photo" src="" alt="Your photo will appear here"
                 style="display:none; margin-bottom: 20px; margin-top: 20px; max-width: 95%; width: 200px">
        </div>

        <div class="query_container">
            <div>
                <label for="user-question">用户问题：</label>
                <textarea id="user-question" placeholder="输入句子"></textarea>
            </div>

        </div>
    </div>

    <button onclick="submitQuestion()">提交</button>

    <h3>返回结果:</h3>
    <div class="results-container">
        <div class="result-view" id="resultView">
          <!-- 搜索结果将在这里显示 -->
        </div>
        <div class="tracing-detail" id="tracingDetails">
          <!-- 搜索细节将在这里显示 -->
        </div>
    </div>

</div>

<script>
    let temp_complex_sentence_analysis_prompt = $('#complex_sentence_analysis_prompt').val()

    let message_id = null;

    const questionInput = document.getElementById("user-question");
    const resultView = document.getElementById('resultView');
    const tracingDetails = document.getElementById('tracingDetails');

    $('#complex_sentence_analysis_btn').click(function() {
        $('#complex_sentence_analysis_modal').show();
        $('#overlay').show();
        $('#complex_sentence_analysis_prompt').val(temp_complex_sentence_analysis_prompt)
    });

    $('#confirm_complex_sentence_analysis').click(function() {
        temp_complex_sentence_analysis_prompt = $('#complex_sentence_analysis_prompt').val()
        $('#complex_sentence_analysis_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_complex_sentence_analysis').click(function() {
        $('#complex_sentence_analysis_modal').hide();
        $('#overlay').hide();
    });

    // 监听用户输入事件，移除空白和换行符
    questionInput.addEventListener('input', function() {
       // 移除所有空白和换行符
    });

    document.querySelector('input[type="file"]').addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const photo = document.getElementById('photo');
                photo.src = e.target.result;
                photo.style.display = 'block'; // 显示照片

                $('#user-question').val('')
            };
            reader.readAsDataURL(file);
        }
    });

    $('#uploadBtn').click(function() {

        const fileInput = document.querySelector('input[type="file"]');
        const files = fileInput.files;

        if (files.length === 0) {
            // 如果没有选择文件，显示警告信息并阻止上传
            alert('请先选择要上传的图片文件！');
            $('#ocrLoadingOverlay').css('display', 'none'); // 确保加载提示被隐藏
            return;
        }
        // 显示 OCR 识别加载中提示
        $('#ocrLoadingOverlay').css('display', 'flex');
        const formData = new FormData($('#uploadForm')[0]);


        $.ajax({
            url: $('#uploadForm').attr('action'), // 服务器端处理上传的URL
            type: 'POST',
            data: formData,

            processData: false,  // 告诉jQuery不要处理发送的数据
            contentType: false,  // 告诉jQuery不要设置Content-Type请求头
            success: function(response) {
                // 文件上传成功后的回调
                $('#user-question').val(response.data.content)
                $('#ocrLoadingOverlay').css('display', 'none');
            },
            error: function() {
                // 文件上传失败后的回调
                console.error('File upload failed.');
                // 隐藏 OCR 加载提示
                $('#ocrLoadingOverlay').css('display', 'none');
            }
        });

    });

    function submitQuestion() {
        const userQuestion = questionInput.value.trim();


        if (!userQuestion) {
            alert("请输入需要解析的句子！");
            return;
        }

        // 清空返回结果的内容
        resultView.innerHTML = ''; // 清空之前的结果
        tracingDetails.innerHTML = ''; // 清空之前的细节

        $('#loadingOverlay').css('display', 'flex');

        message_id = null;
        const url = "{% url 'complex_sentence_analysis' %}";
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                complex_sentence_analysis: $('#complex_sentence_analysis_prompt').val(),

                user_question: userQuestion,

            })
        })
        .then(response => {
            $('#loadingOverlay').css('display', 'none');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.body;
        })
        .then(stream => {
            $('#loadingOverlay').css('display', 'none');
            let streamComplete = false;
            const reader = stream.getReader();
            const decoder = new TextDecoder(); // 创建TextDecoder实例
            let result_text = '';
            function readChunk() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        streamComplete = true;
                        console.log('Stream complete, returning collected data');
                        find_tracing()
                        return;
                    }
                    const textChunkStr = decoder.decode(value, { stream: true });

                    // 根据 \n\n 对流数据分块
                    const chunkArr = textChunkStr.split("\n\n");
                    for (let i = 0; i < chunkArr.length; i++) {
                        const textChunk = chunkArr[i];
                        if (textChunk.startsWith("data: ")) {
                            // 去除前缀"data: "
                            const json_str = textChunk.slice(6);
                            console.log('json_str', json_str)
                            try {
                                // 将匹配到的字符串转换为JSON对象
                                const jsonData = JSON.parse(json_str);
                                if (!message_id) {
                                    message_id = jsonData.message_id
                                }
                                if (jsonData.answer) {
                                    result_text += jsonData.answer;
                                    resultView.innerHTML = marked.parse(result_text);
                                } else if (jsonData.err) {
                                    resultView.innerHTML = jsonData.err;
                                }
                            } catch (error) {
                                console.error('Error parsing JSON', json_str, error);
                            }
                        }
                    }
                    readChunk();
                }).catch(error => {
                    console.error('Error collecting stream data', error);
                });
            }
            readChunk();
        })
        .catch(error => {
            $('#loadingOverlay').css('display', 'none');
            console.error('Error:', error);
        });
    }

function find_tracing() {
    if (!message_id) return;
    const $el = $('#tracingDetails');  // 指定展示追踪信息的容器
    $.ajax({
        type: 'GET',
        url: "{% url 'message_tracing' %}",
        data: { message_id: message_id },
        success: function(response) {
            $('#loadingOverlay').css('display', 'none');
            const results = response.data;

            $el.empty(); // 确保清空之前的内容

            if (results && results.tracing) {
                results.tracing.forEach(function(traceDetail) {
                    const detailElement = document.createElement('p');
                    detailElement.textContent = traceDetail;
                    $el.append(detailElement);
                });
            } else {
                $el.append('<p>没有找到追踪信息。</p>');
            }
        },
        error: function(xhr, status, error) {
            alert('请求失败');
            $('#loadingOverlay').css('display', 'none');
            console.error('AJAX Error:', xhr.responseText);
        }
    });
}


</script>

</body>
</html>


