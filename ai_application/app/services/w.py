import os
import logging
from typing import List, TypedDict, Optional, Dict, Any

from django.conf import settings
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.graph import StateGraph, END

logger = logging.getLogger(__name__)

"""
LangGraph 文本摘要流程实现

该模块实现了一个基于LangGraph的多阶段文本摘要流程，包含:
1. 文本分割
2. 分块摘要(Map)
3. 摘要整合(Reduce)
"""

# --- 1. 定义状态 ---
class SummarizeState(TypedDict):
    """
    定义图的状态
    
    Attributes:
        input_text: 用户的原始输入文本
        chunks: 分割后的文本块列表
        chunk_summaries: 每个文本块的摘要列表
        final_summary: 最终的全局摘要
    """
    input_text: str
    chunks: List[str]
    chunk_summaries: List[str]
    final_summary: str

# --- 2. 初始化模型和文本分割器 ---
def init_llm(model_name: str, temperature: float = 0.3) -> ChatOpenAI:
    """初始化LLM模型"""
    try:
        return ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base=settings.DOUBAO_API_BASE,
            model_name=model_name,
            temperature=temperature,
        )
    except Exception as e:
        logger.error(f"初始化模型失败: {e}")
        raise

# 初始化模型
map_llm = init_llm("doubao-1-5-pro-32k-250115")
reduce_llm = init_llm("doubao-1.5-thinking-pro-250415")

def get_text_splitter(chunk_size: int = 2000, chunk_overlap: int = 100) -> RecursiveCharacterTextSplitter:
    """获取配置化的文本分割器"""
    return RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap
    )

text_splitter = get_text_splitter()

# --- 3. 定义节点函数 ---

def split_text_node(state: SummarizeState) -> Dict[str, Any]:
    """
    分割文本节点
    
    Args:
        state: 包含input_text的摘要状态
        
    Returns:
        包含分割后chunks的字典
        
    Raises:
        ValueError: 如果输入文本为空
    """
    logger.info("正在分割文本")
    text = state.get('input_text')
    if not text:
        logger.error("输入文本为空")
        raise ValueError("输入文本不能为空")
        
    try:
        chunks = text_splitter.split_text(text)
        return {"chunks": chunks}
    except Exception as e:
        logger.error(f"文本分割失败: {e}")
        raise

def summarize_chunk_node(state: Dict[str, Any]):
    """
    摘要单个文本块节点 (Map)
    
    Args:
        state: 包含当前chunk的状态字典
        
    Returns:
        包含摘要结果的字典
    """
    # LangGraph的map操作会将当前chunk直接作为state传递
    chunk = state if isinstance(state, str) else state.get("chunk", "")
    print(f"--- 正在摘要块: {chunk[:50]}... ---")
    prompt = ChatPromptTemplate.from_template(
        "请为以下文本生成一个简洁、精确的摘要:\n\n---\n\n{text}\n\n---\n\n摘要:"
    )
    chain = prompt | map_llm | StrOutputParser()
    summary = chain.invoke({"text": chunk})
    return {"chunk_summaries": summary}

def reduce_summaries_node(state: SummarizeState):
    """
    规约所有摘要节点 (Reduce)
    """
    print("--- 正在整合所有摘要 ---")
    summaries = state['chunk_summaries']
    
    prompt = ChatPromptTemplate.from_template(
        "你是一位专业的编辑。这里有一系列关于同一主题的摘要。请将它们整合成一个单一的、连贯的、高质量的最终摘要。"
        "确保最终摘要流畅、全面且没有冗余信息。\n\n"
        "以下是待整合的摘要列表:\n\n---\n\n{summaries}\n\n---\n\n最终摘要:"
    )
    
    # 将摘要列表合并成一个字符串
    summaries_str = "\n\n".join(summaries)
    
    chain = prompt | reduce_llm | StrOutputParser()
    final_summary = chain.invoke({"summaries": summaries_str})
    
    return {"final_summary": final_summary}

# --- 4. 构建图 ---

builder = StateGraph(SummarizeState)

# 添加节点
builder.add_node("split_text", split_text_node)
# .map() 会将输入列表的每个元素传递给 summarize_chunk_node
# 并将所有节点的输出聚合到一个新列表中
builder.add_node("summarize_chunk", summarize_chunk_node)
builder.add_node("reduce_summaries", reduce_summaries_node)

# 添加边
builder.add_edge("split_text", "summarize_chunk")
builder.add_edge("summarize_chunk", "reduce_summaries")
builder.add_edge("reduce_summaries", END)

builder.set_entry_point("split_text")

# 编译图
graph = builder.compile()


# --- 5. 执行图 ---

def create_test_graph() -> StateGraph:
    """创建并返回配置好的摘要图实例"""
    builder = StateGraph(SummarizeState)
    builder.add_node("split_text", split_text_node)
    builder.add_node("summarize_chunk", summarize_chunk_node)
    builder.add_node("reduce_summaries", reduce_summaries_node)
    builder.add_edge("split_text", "summarize_chunk")
    builder.add_edge("summarize_chunk", "reduce_summaries")
    builder.add_edge("reduce_summaries", END)
    builder.set_entry_point("split_text")
    return builder.compile()

if __name__ == "__main__":
    """模块测试入口"""
    graph = create_test_graph()
    test_text = "测试文本..."
    initial_state = {"input_text": test_text}
    
    try:
        result = graph.invoke(initial_state)
        logger.info(f"摘要结果: {result['final_summary']}")
    except Exception as e:
        logger.error(f"摘要测试失败: {e}")
