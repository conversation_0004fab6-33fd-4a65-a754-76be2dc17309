--
-- Add field cycle_num to stuserknowledgedistribution
--
ALTER TABLE `ai_st_user_knowledge_distribution` ADD COLUMN `cycle_num` integer DEFAULT 1 NOT NULL;
--
-- Add field cycle_num to stuserpaper
--
ALTER TABLE `ai_st_user_paper` ADD COLUMN `cycle_num` integer DEFAULT 1 NOT NULL;
--
-- Add field cycle_num to stuserpaperanswer
--
ALTER TABLE `ai_st_user_paper_answer` ADD COLUMN `cycle_num` integer DEFAULT 1 NOT NULL;
--
-- Add field cycle_num to stuserpaperquestion
--
ALTER TABLE `ai_st_user_paper_question` ADD COLUMN `cycle_num` integer DEFAULT 1 NOT NULL;
--
-- Add field cycle_num to stuserpaperquestionanswer
--
ALTER TABLE `ai_st_user_paper_question_answer` ADD COLUMN `cycle_num` integer DEFAULT 1 NOT NULL;
--
-- Add field cycle_num to stuserroundanalysis
--
<PERSON>TER TABLE `ai_st_user_round_analysis` ADD COLUMN `cycle_num` integer DEFAULT 1 NOT NULL;
--
-- Add field cycle_num to stuserroundchangerecord
--
ALTER TABLE `ai_st_user_round_change_record` ADD COLUMN `cycle_num` integer DEFAULT 1 NOT NULL;
--
-- Alter field learning_stage on stsubjectstagestrategy
--
-- (no-op)
--
-- Alter field learning_stage on stuserpaper
--
-- (no-op)
--
-- Alter field learning_stage on stuserroundanalysis
--
-- (no-op)
--
-- Alter field stage_change_type on stuserroundanalysis
--
-- (no-op)
--
-- Alter field change_type on stuserroundchangerecord
--
-- (no-op)
--
-- Alter field new_stage on stuserroundchangerecord
--
-- (no-op)
--
-- Alter field old_stage on stuserroundchangerecord
--
-- (no-op)