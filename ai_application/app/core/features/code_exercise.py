from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate


class CodeExerciseFeature(BaseFeature):

    def __init__(self):
        # 预先获取两个提示词模板
        self.concept_prompt_template = PromptTemplate.objects.filter(app_no='dsx_code_cocept').first()
        self.programming_prompt_template = PromptTemplate.objects.filter(app_no='dsx_code_programming').first()
        self.only_code_prompt_template = PromptTemplate.objects.filter(app_no='dsx_code_optimize').first()
        if not self.concept_prompt_template:
            raise Exception('dsx_code_cocept prompt template not found')
        if not self.programming_prompt_template:
            raise Exception('dsx_code_programming prompt template not found')
        if not self.only_code_prompt_template:
            raise Exception('dsx_only_code_prompt_template prompt template not found')

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            question_type: str,
            question: str,
            answer: str,
            lang: str,
            stream: bool,
    ):

        user_inputs = {'question': question, 'answer': answer, 'code_lang': lang}

        # 根据 question_type 选择提示词模板
        if question_type == 'concept':
            prompt_template = self.concept_prompt_template
        elif question_type == 'programming':
            prompt_template = self.programming_prompt_template
        elif question_type == 'only_code':
            prompt_template = self.only_code_prompt_template
        else:
            raise ValueError("question_type 必须为 'concept' 或 'programming' 或 'only_code'")
        
        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        # 调用模型
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=stream
        )
