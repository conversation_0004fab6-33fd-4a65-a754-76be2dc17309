import time
from typing import Generator, cast

from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.entities.queue_entities import QueueLLMChunkEvent, QueueStopEvent, QueueMessageEndEvent
from app.core.model_runtime.entities.llm_entities import (
    LLMUsage, LLMResultChunk, LLMWorkflowChunk, LLMResultChunkDelta, LLMResult
)
from app.core.prompt.entities import AssistantPromptMessage
from app.models import Message


class BaseWorkflow:
    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        self.application_generate_entity = application_generate_entity
        self.inputs = application_generate_entity.inputs
        self.message = message
        self.stream = self.application_generate_entity.stream

        self.tracing_log = MessageTracingLog(message)
        self.queue_manager = queue_manager

        self.is_debug = self.inputs.get('is_debug', False)

        self.is_answer_token_exceed = False
        # 多节点的应用，记录合并结果
        self.workflow_answer = ''

    def run(self):
        pass

    def _handle_node_response(
            self,
            result: LLMResult | Generator,
            stream: bool,
            workflow_chunk: dict | None = None,
            tracing_info: dict | None = None
    ):
        if stream:
            return self._handle_node_generate_response(
                result, workflow_chunk=workflow_chunk, tracing_info=tracing_info)
        else:
            self._handle_node_direct_response(
                result, workflow_chunk=workflow_chunk, tracing_info=tracing_info)
            return result

    def _handle_node_generate_response(
            self,
            generator: Generator,
            workflow_chunk: dict | None = None,
            tracing_info: dict | None = None
    ) -> LLMResult:
        model_provider = ''
        model = ''
        model_params = {}
        prompt_messages = []
        reasoning_content = ''
        text = ''
        usage = LLMUsage.empty_usage()

        for result in generator:
            result = cast(LLMResultChunk, result)

            chunk_answer = result.delta.message.content
            chunk_reasoning_content = result.delta.message.reasoning_content
            if not (chunk_answer or chunk_reasoning_content):
                continue

            result.workflow_chunk = LLMWorkflowChunk(
                id=workflow_chunk.get('id', ''),
                pos=workflow_chunk.get('pos', ''),
                type=workflow_chunk.get('type', ''),
                title=workflow_chunk.get('title', '')
            ) if workflow_chunk else None

            if not model_provider:
                model_provider = result.model_provider
            if not model:
                model = result.model
            if not model_params:
                model_params = result.model_params

            if not prompt_messages:
                prompt_messages = result.prompt_messages

            if result.delta.usage:
                usage = result.delta.usage

            self.queue_manager.publish(
                QueueLLMChunkEvent(chunk=result)
            )

            reasoning_content += chunk_reasoning_content
            text += chunk_answer
            self.workflow_answer += chunk_answer

            if self.queue_manager.is_stopped():
                self.queue_manager.publish(
                    QueueStopEvent(stopped_by=QueueStopEvent.StopBy.USER_MANUAL),
                )
                is_answer_token_exceed = LLMResult.judge_answer_token_exceed(model_params, usage)
                if is_answer_token_exceed:
                    self.is_answer_token_exceed = True
                return LLMResult(
                    model_provider=model_provider,
                    model=model,
                    model_params=model_params,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(content=text, reasoning_content=reasoning_content),
                    usage=usage,
                    is_answer_token_exceed=is_answer_token_exceed
                )

        self._publish_workflow_custom_message("\n\n", workflow_chunk=workflow_chunk)

        is_answer_token_exceed = LLMResult.judge_answer_token_exceed(model_params, usage)
        if is_answer_token_exceed:
            self.is_answer_token_exceed = True
        llm_result = LLMResult(
            model_provider=model_provider or '',
            model=model,
            model_params=model_params,
            prompt_messages=prompt_messages,
            message=AssistantPromptMessage(content=text, reasoning_content=reasoning_content),
            usage=usage,
            is_answer_token_exceed=is_answer_token_exceed
        )

        if tracing_info:
            self.tracing_log.add_tracing_by_llm_result(
                tracing_type=tracing_info.get('tracing_type'),
                query=tracing_info.get('query'),
                llm_result=llm_result
            )

        return llm_result

    def _handle_node_direct_response(
            self,
            llm_result: LLMResult,
            workflow_chunk: dict | None = None,
            tracing_info: dict | None = None,
    ):
        is_answer_token_exceed = LLMResult.judge_answer_token_exceed(llm_result.model_params, llm_result.usage)
        if is_answer_token_exceed:
            self.is_answer_token_exceed = True

        self._publish_workflow_custom_message(llm_result.message.content, workflow_chunk=workflow_chunk)

        if tracing_info:
            self.tracing_log.add_tracing_by_llm_result(
                tracing_type=tracing_info.get('tracing_type'),
                query=tracing_info.get('query'),
                llm_result=llm_result
            )

    def _publish_workflow_custom_message(self, text, workflow_chunk: dict | None = None, is_sleep: bool = True):
        self.workflow_answer += text
        assistant_message = AssistantPromptMessage(content=text)
        self.queue_manager.publish(
            QueueLLMChunkEvent(
                chunk=LLMResultChunk(
                    model='',
                    prompt_messages=[],
                    delta=LLMResultChunkDelta(index=0, message=assistant_message),
                    workflow_chunk=LLMWorkflowChunk(
                        id=workflow_chunk.get('id', ''),
                        pos=workflow_chunk.get('pos', ''),
                        type=workflow_chunk.get('type', ''),
                        title=workflow_chunk.get('title', '')
                    ) if workflow_chunk else None
                )
            )
        )
        if is_sleep:
            time.sleep(0.01)

    def _publish_workflow_end_message(self):
        workflow_usage = self.tracing_log.usage
        llm_result = LLMResult(
            model='',
            prompt_messages=[],
            message=AssistantPromptMessage(content=self.workflow_answer),
            usage=workflow_usage,
            is_answer_token_exceed=self.is_answer_token_exceed,
        )
        self.queue_manager.publish(
            QueueMessageEndEvent(llm_result=llm_result)
        )
