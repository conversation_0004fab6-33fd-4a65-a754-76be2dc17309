# myproject9/ai_application/app/management/commands/migrate_questions.py

import re
from django.core.management.base import BaseCommand
from app.models import EnglishReaderQuestionBank, EnglishWaikanQuestionBank, EnglishWaikanSubQuestion

class Command(BaseCommand):
    help = 'Migrate questions from EnglishReaderQuestionBank to EnglishWaikanSubQuestion'

    def handle(self, *args, **kwargs):
        # 获取所有废弃的题库记录
        old_question_banks = EnglishReaderQuestionBank.objects.all()

        for old_question_bank in old_question_banks:
            # 创建新的题库记录
            new_question_bank = EnglishWaikanQuestionBank.objects.create(
                publication_id=old_question_bank.publication_id,
                strategy_id=old_question_bank.strategy_id,
                article=old_question_bank.article
            )

            # 解析 option 字段
            options_text = old_question_bank.option.strip()
            options = re.split(r'\n(?=[A-Z]\. )', options_text)

            # 解析 answer 字段
            answers_text = old_question_bank.answer.strip().replace('正确答案', '').replace('**', '').replace('\n', '').strip()
            answers = list(answers_text)  # 将答案按字符分割

            # 解析 analysis 字段
            analysis_text = old_question_bank.analysis.strip()
            analyses = re.split(r'\n答案：[A-Z] 解析：', analysis_text)

            for i, option in enumerate(options):
                # 提取问题和选项
                question_stem, *option_parts = option.split('\n')
                question_stem = question_stem.strip()
                question_options = []

                for j, part in enumerate(option_parts):
                    option_id = chr(65 + j)  # A, B, C, D
                    option_stem = part.strip().replace(f'{option_id}. ', '')
                    question_options.append({"option_id": option_id, "option_stem": option_stem})

                # 提取答案
                question_answer = answers[i] if i < len(answers) else ''

                # 提取解析
                question_analysis = analyses[i + 1].strip() if i + 1 < len(analyses) else ''

                # 创建子问题记录
                EnglishWaikanSubQuestion.objects.create(
                    question=new_question_bank,
                    question_stem=question_stem,
                    question_options=question_options,
                    question_answer=question_answer,
                    question_feature='set',
                    question_type='',
                    question_analysis=question_analysis
                )

            self.stdout.write(self.style.SUCCESS(f'Successfully migrated question bank with ID: {old_question_bank.id}'))
