import re
from typing import Generator, cast

from ai_application import settings
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import  CompletionAppGenerateEntity
from app.core.entities.queue_entities import QueueLLMChunkEvent, QueueStopEvent
from app.core.features.complex_sentence_analysis import ComplexSentenceAnalysisFeature
from app.core.model_runtime.entities.llm_entities import LLMResult, LLMUsage, LLMResultChunk, LLMWorkflowChunk
from app.core.prompt.entities import AssistantPromptMessage
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError,  GrammarMessageNotQuestionError
from app.models import Message, QuestionNewRecord, EnglishSentenceAnalysis


class GrammarWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

        self.grammar_prompts = self.inputs.get('complex_sentence_analysis')
        self.complex_sentence_feature = ComplexSentenceAnalysisFeature()
        self.sentence_qs = EnglishSentenceAnalysis.objects.filter(is_deleted=False).order_by('id')
        self.no_relation_err_msg = '请输入与英语长难句相关的问题哦'

        self.not_question_err = '看起来您上传的图片内容可能不够清晰。为了确保我们能够准确地为您提供帮助，请重新上传相关的图片或详细描述您的问题。谢谢！'

    def run(self):
        user_question = self.application_generate_entity.query
        if not user_question:
            raise ParameterError(detail=self.not_question_err)

        # 去除句子开头的标点符号
        clean_pattern = r"^[，。、；：？！“”‘’‘’《》（）{}【】\[\]<>\"'`~@#%^&*+=|\\\-_/.,;:?!\s]+"
        user_question = re.sub(clean_pattern, "", user_question)
        # 判断是否是一个英文句子
        if not self._is_english_sentence(user_question):
            self._publish_workflow_custom_message(self.no_relation_err_msg)
        else:
            self._analysis_sentence(user_question)
        self._publish_workflow_end_message()

    def _is_english_sentence(self, text):
        # 是否以一个英文单词开头
        pattern2 = r"^\b[a-zA-Z][a-zA-Z]*"
        if not re.search(pattern2, str(text)):
            return False

        words = text.split(' ')
        return len(words) > 3

    def _analysis_sentence(self, original_sentence_query: str) -> LLMResult:
        # 处理OCR识别后换行的句子
        original_sentence_arr = original_sentence_query.split('\n')
        original_sentence_query = ' '.join(original_sentence_arr)

        matched_sentence = self._find_sentence_components_row(original_sentence_query)
        if matched_sentence:
            # 使用匹配到的句子替代原始查询
            sentence_to_analyze = matched_sentence.sentence
            components = matched_sentence.content_str
        else:
            sentence_to_analyze = original_sentence_query
            components = "无"

        question_found_prompt = self.grammar_prompts

        # doc = ner_client.get_en_sentence_dependency(sentence_to_analyze)
        # dependency_relations = [
        #     f"{item['text']}: {item['dep']} -> {item['head_text']}"
        #     for item in doc
        # ]

        result = self.complex_sentence_feature.run(
            self.application_generate_entity,
            sentence=sentence_to_analyze,
            # dependency_relations=dependency_relations,
            components=components,
            stream=True,
            prompt_content=question_found_prompt
        )

        tracing_info = {
            'tracing_type': 'complex_sentence_analysis',
            'query': sentence_to_analyze
        }
        return self._handle_node_generate_response(result, tracing_info=tracing_info)

    def _handle_node_generate_response(
            self,
            generator: Generator,
            workflow_chunk: dict | None = None,
            tracing_info: dict | None = None
    ) -> LLMResult:
        model_provider = ''
        model = ''
        model_params = {}
        prompt_messages = []
        text = ''
        usage = LLMUsage.empty_usage()

        # 请输入与英语长难句相关的问题哦
        first_message_str = ''
        first_message_chunk = None
        first_message_str_len = 20
        is_first_message_publish = False
        for result in generator:
            result = cast(LLMResultChunk, result)
            result.workflow_chunk = LLMWorkflowChunk(
                id=workflow_chunk.get('id', ''),
                pos=workflow_chunk.get('pos', ''),
                type=workflow_chunk.get('type', ''),
                title=workflow_chunk.get('title', '')
            ) if workflow_chunk else None

            if not model_provider:
                model_provider = result.model_provider
            if not model:
                model = result.model
            if not model_params:
                model_params = result.model_params

            if not prompt_messages:
                prompt_messages = result.prompt_messages

            if result.delta.usage:
                usage = result.delta.usage

            if not first_message_chunk:
                first_message_chunk = result

            # 特殊处理长难句输出判断
            if not is_first_message_publish:
                first_message_str += result.delta.message.content
                first_message_chunk.delta.message.content = first_message_str
                if len(first_message_str) < first_message_str_len:
                    continue
                else:
                    if self.no_relation_err_msg in first_message_str:
                        break
                    result.delta.message.content = first_message_str
                    is_first_message_publish = True

            self.queue_manager.publish(
                QueueLLMChunkEvent(chunk=result)
            )

            text += result.delta.message.content
            self.workflow_answer += result.delta.message.content

            if self.queue_manager.is_stopped():
                self.queue_manager.publish(
                    QueueStopEvent(stopped_by=QueueStopEvent.StopBy.USER_MANUAL),
                )
                is_answer_token_exceed = LLMResult.judge_answer_token_exceed(model_params, usage)
                if is_answer_token_exceed:
                    self.is_answer_token_exceed = True
                return LLMResult(
                    model_provider=model_provider,
                    model=model,
                    model_params=model_params,
                    prompt_messages=prompt_messages,
                    message=AssistantPromptMessage(content=text),
                    usage=usage,
                    is_answer_token_exceed=is_answer_token_exceed
                )

        if not is_first_message_publish and first_message_str:
            if self.no_relation_err_msg in first_message_str:
                first_message_chunk.workflow_chunk = LLMWorkflowChunk(type='exception')

            self.queue_manager.publish(
                QueueLLMChunkEvent(chunk=first_message_chunk)
            )
            text = first_message_str
            self.workflow_answer += first_message_str

        is_answer_token_exceed = LLMResult.judge_answer_token_exceed(model_params, usage)
        if is_answer_token_exceed:
            self.is_answer_token_exceed = True
        llm_result = LLMResult(
            model_provider=model_provider or '',
            model=model,
            model_params=model_params,
            prompt_messages=prompt_messages,
            message=AssistantPromptMessage(content=text),
            usage=usage,
            is_answer_token_exceed=is_answer_token_exceed
        )

        if tracing_info:
            self.tracing_log.add_tracing_by_llm_result(
                tracing_type=tracing_info.get('tracing_type'),
                query=tracing_info.get('query'),
                llm_result=llm_result
            )

        return llm_result

    def _find_sentence_components_row(self, sentence: str) -> EnglishSentenceAnalysis | None:
        """
        查找句子对应的语法成分，支持完全相同或部分相同匹配，并返回匹配行
        """
        for s in self.sentence_qs:
            if s.sentence in sentence:
                return s
        return None

    def _check_pic_content_is_sentence(self, content: str):
        """
        判断图片内容是否是题目
        """
        if not content:
            self._raise_chat_question_no_result()

        cleaned_content = re.sub(r'[^\w\s]', '', content)
        if len(cleaned_content) <= settings.JUDGE_QUESTION_MIN_LEN:
            self._raise_chat_question_no_result()

    def _save_not_found_question(self, question_pic_url, question_content):
        QuestionNewRecord.objects.create(
            message=self.message,
            question_pic_url=question_pic_url,
            question_content=question_content
        )

    def _raise_chat_question_no_result(self):
        # 接受配置的错误输出
        not_found_err = self.grammar_prompts.get('sentence_not_found_prompt', '')
        if not not_found_err:
            not_found_err = self.not_question_err

        raise GrammarMessageNotQuestionError(detail=not_found_err)
