# Generated by Django 4.2 on 2025-07-11 03:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0059_documentproofreadererror_error_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubjecttoKnowledge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_1', models.CharField(max_length=100, verbose_name='一级学科')),
                ('subject_2', models.Char<PERSON>ield(max_length=100, verbose_name='二级学科')),
                ('categories', models.Char<PERSON>ield(max_length=100, verbose_name='章节')),
                ('knowledge_points', models.Char<PERSON>ield(max_length=100, verbose_name='知识点')),
            ],
            options={
                'verbose_name': '学科知识点',
                'verbose_name_plural': '学科知识点',
                'db_table': 'ai_subject_to_knowledge',
            },
        ),
        migrations.CreateModel(
            name='TexttoVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(max_length=20, verbose_name='知识点名称')),
                ('video', models.CharField(blank=True, max_length=200, verbose_name='视频链接')),
                ('script', models.JSONField(null=True, verbose_name='视频脚本')),
                ('user_info', models.JSONField(verbose_name='用户信息')),
            ],
            options={
                'verbose_name': '智学视听搜索',
                'verbose_name_plural': '智学视听搜索',
                'db_table': 'ai_text_to_video',
            },
        ),
        migrations.CreateModel(
            name='KnowledgeVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('video', models.CharField(blank=True, max_length=200, verbose_name='视频链接')),
                ('image', models.CharField(blank=True, max_length=200, verbose_name='图片链接')),
                ('name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.knowledgesimple', verbose_name='对应知识点')),
            ],
            options={
                'verbose_name': '知识解析视频',
                'verbose_name_plural': '知识解析视频',
                'db_table': 'ai_knowledge_video',
            },
        ),
    ]
