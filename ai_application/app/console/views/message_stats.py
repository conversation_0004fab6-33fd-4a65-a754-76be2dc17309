from contextlib import suppress
from datetime import datetime, timed<PERSON><PERSON>
from itertools import chain
import json
from typing import Any

from django.core.cache import cache
from django.conf import settings
from django.db.models import QuerySet
from django.http import Http404
from django.shortcuts import render
from django.views.generic import TemplateView, DetailView
from django.utils import timezone
import requests

from api_client.data.vector_data_client import vector_data_client
from app.api.dto import ChatMessageDto, ProblemSolvingDto, ComplexSentenceAnalysisDto
from app.api.validators import ProblemSolvingValidator, ComplexSentenceAnalysisValidator, \
    ChapterNoteGeneratorValidator, LectureNoteGeneratorValidator
from app.console.services import extract_docx_content

from app.console.serializers import MessageListSerializer, UnifiedExaminationQuestionSerializer
from app.constants.app import AppMode, OUTER_APP_NOS, CHAT_MESSAGE_TYPES_MAP, OUTER_APP_TYPES
from app.core.entities.app_entities import ModelConfigEntity
from app.core.indexing_runner import IndexingRunner
from app.core.model_manager import ModelInstance
from app.core.model_provider_manager import Model<PERSON>roviderManager
from app.core.model_runtime.entities.provider_entities import ModelType
from app.core.prompt.params_prompt import comb_prompt_by_params
from app.core.prompt.simple_prompt_transform import SimplePromptTransform
from app.core.rag.embedding.cached_embedding import CacheEmbedding
from app.core.rag.models.document import Document
from app.core.rag.splitter.semantic_splitter import SemanticSplitter
from app.libs.baidu_ocr import BaiduOcr
from app.models import PromptTemplate, Account, App, Conversation, Message, MessageTracing, PromptTemplateChangeLog, \
    AppModelConfig, CourseNoteTask, CourseNoteTaskChangeDetail, CourseNoteTaskDebug
from app.models import UnifiedExaminationQuestion as Ueq
from app.services.app_generate_service import AppGenerateService
from app.services.course_note.course_note_service import CourseNoteService
from app.services.course_note.debug_course_note_service import DebugCourseNoteService
from app.services.course_note.utils import get_chapter_names
from app.services.document_summary_service import DocumentSummaryService
from app.services.prompt_optimize_service import PromptOptimizeService
from django_ext import base_view
from django_ext.base_view import BaseView
from django_ext.paginator import queryset_paginate
from django_ext.response import make_response, make_stream_response
from django_ext.utils.date_utils import local2utc, utc2local, utc2str
from app.console.services import MessageStatsWaikanQuestion, MessageStatsEnWordTestQuestion
from app.console.services import MessageStatsEnWordReciteQuestion
import os
from django.http import JsonResponse
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.views import APIView
from docx import Document
from helpers.upload_helper import upload_file


class AppMsgDailyStatsView(TemplateView):
    template_name = 'app/app_message_daily_stats.html'

    def get_query_days(self) -> int:
        days = self.request.GET.get('days')
        days = 7 if not days or not days.isdigit() else int(days)
        if days > 30:
            raise Http404()
        return days

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        end_dt = timezone.localtime(timezone.now()).replace(hour=23, minute=59, second=59, microsecond=999999)
        date_list = []
        for i in range(self.get_query_days()):
            start_dt = (end_dt - timedelta(days=i)).replace(hour=0, minute=0, second=0, microsecond=0)
            date_list.insert(0, start_dt.strftime('%Y-%m-%d'))

        app_id = kwargs['app_id']

        if app_id == 'waikan_question':
            queryset = MessageStatsWaikanQuestion.ge_queryset()
            app_name = '外刊模拟出题'
        elif app_id == 'en_word_test':
            queryset = MessageStatsEnWordTestQuestion.ge_queryset()
            app_name = '英语词汇训练'
        elif app_id == 'en_word_recite':
            queryset = MessageStatsEnWordReciteQuestion.ge_queryset()
            app_name = '英语单词背诵'
        else:
            if app_id.isdigit():
                app = App.objects.filter(id=kwargs['app_id']).first()
                app_name = app.name
                queryset = Message.objects.filter(app=app).order_by('-pk')
            else:
                # 兼容处理教辅应用
                app: App = App.objects.filter(app_type=app_id).first()
                chat_app_types = CHAT_MESSAGE_TYPES_MAP.keys()
                if app_id in chat_app_types:
                    message_type = CHAT_MESSAGE_TYPES_MAP.get(app_id)
                    app_types = chat_app_types
                else:
                    message_type = 'normal'
                    app_types = [app_id]
                app_name = OUTER_APP_TYPES.get(app_id, '')
                queryset = Message.objects.filter(
                    app__run_type=app.run_type,
                    app__app_type__in=app_types,
                    message_type=message_type,
                )

        queryset = queryset.filter(
            add_time__range=[local2utc(start_dt), local2utc(end_dt)]
        ).order_by('-pk')

        # 初始化每天的消息计数器
        data_by_date = {d: [0, 0] for d in date_list}   # 每天的正常消息数和异常消息数量

        # 统计每天的消息数量
        for m in queryset:
            dt_str = utc2local(m.add_time).strftime('%Y-%m-%d')
            if dt_str in data_by_date:
                s_e_list = data_by_date[dt_str]
                if m.is_exception:
                    s_e_list[1] += 1
                else:
                    s_e_list[0] += 1

        context.update({
            'app_id': app_id,
            'app_name': app_name,
            'date_list': date_list,
            'days': len(date_list),
            'data_by_date_normal': [data_by_date[d][0] for d in date_list],
            'data_by_date_abnormal': [data_by_date[d][1] for d in date_list],
            'max_count': max(chain(*[data_by_date[d] for d in date_list])),
            'interval': max(chain(*[data_by_date[d] for d in date_list])) // 5,
            # 'message_type': message_type,
        })
        return context

