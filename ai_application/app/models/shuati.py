import logging
from collections import defaultdict

from django.db import models
from pydantic import Field

from django_ext.base_dto_model import MyBaseModel
from ._base import BaseModel
from app.api.api_dto.ai_practice import AIPracticeSubjectiveAnswerDto
from app.services.shuati_app.constants import LearningStage, StageChangeType
from app.services.shuati_app.values import question_score_rules

logger = logging.getLogger(__name__)

STCoreCourseList = [
    {
        "core_course_code": "CC_SJJG",
        "core_course_name": "数据结构",
        "subject_id": "TcT7x2dUZfG7BPyAeRNGFM",
        "subject_name": "408计算机",
    },
    {
        "core_course_code": "CC_CZXT",
        "core_course_name": "操作系统",
        "subject_id": "TcT7x2dUZfG7BPyAeRNGFM",
        "subject_name": "408计算机",
    },
    {
        "core_course_code": "CC_JSXW",
        "core_course_name": "计算机网络",
        "subject_id": "TcT7x2dUZfG7BPyAeRNGFM",
        "subject_name": "408计算机",
    },
    {
        "core_course_code": "CC_JSZZYL",
        "core_course_name": "计算机组成原理",
        "subject_id": "TcT7x2dUZfG7BPyAeRNGFM",
        "subject_name": "408计算机",
    }
]


class FormatQuestionContentDto(MyBaseModel):
    type: int  # 问题类型
    title: str
    choices: list[str] = Field(default=[])
    choices_answer: list[str] = Field(default=[])


class STSubjectStageStrategy(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    learning_stage = models.CharField(
        '阶段', max_length=32, choices=LearningStage.choices, default=LearningStage.MODI)
    # is_stage_strengthen = models.BooleanField('是否阶段强化', default=False)
    stage_question_strategy = models.JSONField('阶段出题策略', null=True)
    stage_strengthen_question_strategy = models.JSONField('强化阶段出题策略', null=True)
    stage_change_required = models.JSONField('阶段变更要求', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '科目阶段策略要求'
        db_table = 'ai_st_subject_stage_strategy'

    # def get_stage_question_strategy(self):
    #     return self.stage_question_strategy
    #
    # def get_stage_change_required(self):
    #     return self.stage_question_strategy


class STQuestion(BaseModel):
    class Type(models.IntegerChoices):
        SINGLE = 0, '单选题'
        MULTIPLE = 1, '多选题'
        SUBJECTIVE = 2, '主观题'
        MATERIAL = 3, '材料题'
        SHARE_TITLE = 4, '共享题干题'
        SHARE_OPTION = 5, '共享选项题'
        FILL_BLANK = 6, '填空题'

    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    subject_name = models.CharField('学科名称', max_length=32)
    core_course_name = models.CharField('核心课名称', max_length=32)
    question_intid = models.CharField('题目id', max_length=16, blank=True, db_index=True)
    question_type = models.IntegerField('题目类型', choices=Type.choices, default=Type.SINGLE)
    question_content = models.TextField('题目内容', null=True)
    difficulty = models.IntegerField('难度', default=0)
    is_exam = models.BooleanField('是否真题', default=False)
    is_unified = models.BooleanField('是否统考', default=False)
    exam_year = models.IntegerField('考试年份', default=0)
    exam_school = models.CharField('考试学校', max_length=32, blank=True)
    analysis = models.TextField('解析', null=True)
    # 格式化后题目信息
    format_question_content = models.JSONField('格式化题目内容', null=True)
    knowledge_list = models.JSONField('知识点列表', null=True)
    knowledge_count = models.IntegerField('知识点数量', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '题目表'
        db_table = 'ai_st_question'
        indexes = [
            models.Index(fields=['difficulty', 'question_type', 'core_course_code']),
        ]

    def get_format_question_content(self) -> FormatQuestionContentDto:
        return FormatQuestionContentDto(
            type=self.question_type,
            title=self.format_question_content.get("title"),
            choices=self.format_question_content.get("choices", []),
            choices_answer=self.format_question_content.get("choices_answer", []),
        )

    def get_knowledge_list(self):
        qs = self.stquestionknowledge_set.filter(is_deleted=False).select_related("knowledge")
        return [q.knowledge for q in qs]

    def get_question_score(self) -> float:
        question_score_rules_map = defaultdict(lambda: defaultdict(int))
        for i in question_score_rules:
            question_score_rules_map[i.question_type][i.difficulty] = i.score
        return question_score_rules_map[self.question_type][self.difficulty]


class STKnowledge(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    subject_name = models.CharField('学科名称', max_length=32)
    core_course_name = models.CharField('核心课名称', max_length=32)
    name = models.CharField('知识点名称', max_length=32)
    definition = models.CharField('知识点定义', max_length=255, blank=True)
    kg_qs_count = models.IntegerField('知识点下题目数量(选择+主观)', default=0)
    related_video_url = models.CharField('知识点视频链接', max_length=255, blank=True)
    percentage = models.DecimalField('百分比', max_digits=5, decimal_places=2, default=0)
    is_first_round = models.BooleanField('是否第一轮知识点', default=False)

    class Meta:
        verbose_name = verbose_name_plural = '知识点库'
        db_table = 'ai_st_knowledge'


class STKnowledgeRelation(BaseModel):
    knowledge = models.ForeignKey(STKnowledge, on_delete=models.CASCADE, db_constraint=False, related_name="knowledges")
    pre_knowledge = models.ForeignKey(STKnowledge, on_delete=models.CASCADE, db_constraint=False, related_name="pre_knowledges")

    class Meta:
        verbose_name = verbose_name_plural = '知识点关联关系'
        db_table = 'ai_st_knowledge_relation'


class STQuestionKnowledge(BaseModel):
    question = models.ForeignKey(STQuestion, on_delete=models.CASCADE, db_constraint=False)
    knowledge = models.ForeignKey(STKnowledge, on_delete=models.CASCADE, db_constraint=False)

    class Meta:
        verbose_name = verbose_name_plural = '题目知识点关联'
        db_table = 'ai_st_question_knowledge'


class STUserPaperDistribution(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    paper_distribution = models.JSONField('组卷规则', null=True)
    is_selected = models.BooleanField('是否选中', default=False)
    selected_questions = models.JSONField('选中的题目', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '用户组卷规则'
        db_table = 'ai_st_user_paper_distribution'


class STUserPaper(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    question_ids = models.JSONField('试卷id', null=True)
    is_answered = models.BooleanField('是否答过', default=False)
    learning_stage = models.CharField(
        '阶段', max_length=32, choices=LearningStage.choices, default=LearningStage.MODI)
    is_stage_strengthen = models.BooleanField('是否阶段强化', default=False)
    stage_round = models.IntegerField('阶段轮数', default=1)
    stage_total_rounds = models.IntegerField('阶段总轮数', default=1)
    total_round = models.IntegerField('总轮数', default=1)
    paper_score = models.DecimalField('试卷总分', max_digits=5, decimal_places=2, default=0)
    # subjective_score = models.DecimalField('主观题总分', max_digits=5, decimal_places=2, default=0)

    cycle_num = models.IntegerField('周期数', default=1)

    class Meta:
        verbose_name = verbose_name_plural = '用户试卷'
        db_table = 'ai_st_user_paper'

    def get_question_ids(self) -> list:
        return self.question_ids or []


class STUserPaperQuestion(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    paper = models.ForeignKey(STUserPaper, on_delete=models.CASCADE, db_constraint=False)
    question = models.ForeignKey(STQuestion, on_delete=models.CASCADE, db_constraint=False)
    score = models.DecimalField('分数', max_digits=5, decimal_places=2, default=0)

    cycle_num = models.IntegerField('周期数', default=1)

    class Meta:
        verbose_name = verbose_name_plural = '用户试卷问题'
        db_table = 'ai_st_user_paper_question'


class STUserPaperAnswer(BaseModel):
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    paper = models.ForeignKey(STUserPaper, on_delete=models.CASCADE, db_constraint=False)
    is_finished = models.BooleanField('是否完成', default=False)
    finished_time = models.DateTimeField('完成时间', null=True)
    answered_question_ids = models.JSONField('已答题的题目id列表', null=True)
    answered_score = models.DecimalField('答题总分', max_digits=5, decimal_places=2, default=0)
    subjective_score = models.DecimalField('主观题得分', max_digits=5, decimal_places=2, default=0)

    # not_start ing success fail
    report_status = models.CharField('答卷报告状态', max_length=16, default='not_start')
    report_retry_count = models.IntegerField('重试次数', default=0)
    report_params = models.JSONField('答卷报告参数', null=True)
    report = models.TextField('答卷报告', null=True)
    report_id = models.CharField('答卷报告id', max_length=32, blank=True, db_index=True)

    cycle_num = models.IntegerField('周期数', default=1)

    class Meta:
        verbose_name = verbose_name_plural = '用户答卷'
        db_table = 'ai_st_user_paper_answer'

    def get_answered_question_ids(self) -> list:
        return self.answered_question_ids or []

    def check_is_finished(self) -> bool:
        answered_question_ids = self.get_answered_question_ids()
        paper_question_ids = self.paper.get_question_ids()
        return set(answered_question_ids) == set(paper_question_ids)

    def check_is_finished_real(self) -> bool:
        # 获取主观题真实答完的情况
        answered_question_ids = list(self.stuserpaperquestionanswer_set.filter(
            is_deleted=False, score_rate__gte=0
        ).values_list('question_id', flat=True))
        paper_question_ids = self.paper.get_question_ids()
        return set(answered_question_ids) == set(paper_question_ids)


class STUserPaperQuestionAnswer(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    paper = models.ForeignKey(STUserPaper, on_delete=models.CASCADE, db_constraint=False)
    answer = models.ForeignKey(STUserPaperAnswer, on_delete=models.CASCADE, db_constraint=False)
    question = models.ForeignKey(STQuestion, on_delete=models.CASCADE, db_constraint=False)
    # right, wrong, subjective
    answer_status = models.CharField('答题状态', max_length=16, blank=True)
    choice_answer = models.JSONField('选择题答案', null=True)
    subjective_answer = models.JSONField('主观题答案', null=True)
    image_text = models.TextField('图片文字', null=True)
    # 得分率：正确100；错误0 主观题得分：通过模型获取，-1表示还未生成
    score_rate = models.IntegerField('得分率', default=0)
    answer_score = models.DecimalField('答题得分', max_digits=5, decimal_places=2, default=0)

    # not_start ing success fail
    report_status = models.CharField('报告状态', max_length=16, default='not_start')
    report_retry_count = models.IntegerField('重试次数', default=0)
    report = models.TextField('答题报告', null=True)

    cycle_num = models.IntegerField('周期数', default=1)

    class Meta:
        verbose_name = verbose_name_plural = '用户答卷问题详情'
        db_table = 'ai_st_user_paper_question_answer'
        indexes = [
            models.Index(fields=['user_id', 'core_course_code']),
        ]

    def get_subjective_answer(self) -> AIPracticeSubjectiveAnswerDto | None:
        if not self.subjective_answer:
            return None

        try:
            subjective_answer = self.subjective_answer or {}
            return AIPracticeSubjectiveAnswerDto(**subjective_answer)
        except:
            return None

    def get_subjective_answer_str(self) -> str:
        subjective_answer = self.get_subjective_answer()
        if not subjective_answer.images:
            return subjective_answer.text

        image_text = self.image_text or ''
        return subjective_answer.text + '\n' + image_text


class STUserKnowledgeMasteryLevel(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    knowledge = models.ForeignKey(STKnowledge, on_delete=models.CASCADE, db_constraint=False)
    answer_count = models.IntegerField('答题次数', default=0)
    right_count = models.IntegerField('正确次数', default=0)
    wrong_count = models.IntegerField('错误次数', default=0)
    accuracy = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="正确率", default=0)
    total_accuracy = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="总正确率(做对次数/该知识点总题数)", default=0)

    # 上次答题状态right, wrong
    last_answer_status = models.CharField('上次答题状态', max_length=16, blank=True)
    # 上次答题时间
    last_answer_time = models.DateTimeField('上次答题时间', null=True)
    # 出现轮数
    occr_round = models.IntegerField('出现轮数', default=0)
    # 连续答对轮数
    consecutive_right_round = models.IntegerField('连续答对轮数', default=0)
    # 连续答错轮数
    consecutive_wrong_round = models.IntegerField('连续答错轮数', default=0)
    # 等待轮数
    waiting_round = models.IntegerField('等待轮数', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '用户知识点掌握情况'
        db_table = 'ai_st_user_knowledge_mastery_level'


# 用户知识点答题情况
class STUserKnowledgeDistribution(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    knowledge = models.ForeignKey(STKnowledge, on_delete=models.CASCADE, db_constraint=False)
    question_type = models.IntegerField('题目类型', default=0)
    difficulty = models.IntegerField('难度', default=0)
    answer_count = models.IntegerField('答题次数', default=0)
    right_count = models.IntegerField('正确次数', default=0)
    wrong_count = models.IntegerField('错误次数', default=0)
    accuracy = models.DecimalField(max_digits=5, decimal_places=2, verbose_name="正确率", default=0)

    cycle_num = models.IntegerField('周期数', default=1)

    class Meta:
        verbose_name = verbose_name_plural = '用户知识点答题分布情况'
        db_table = 'ai_st_user_knowledge_distribution'


class STUserRoundAnalysis(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    examined_rounds = models.IntegerField('已考察轮次', default=0)
    total_assessment_score = models.DecimalField('总评估得分', max_digits=8, decimal_places=2, default=0)
    total_subjective_score = models.DecimalField('主观题得分', max_digits=8, decimal_places=2, default=0)
    learning_stage = models.CharField(
        '答题阶段', max_length=32, choices=LearningStage.choices, default=LearningStage.MODI)
    stage_examined_rounds = models.IntegerField('阶段已考察轮次', default=0)
    stage_total_rounds = models.IntegerField('阶段总轮数', default=1)
    stage_change_type = models.CharField(
        '阶段变更类型',
        max_length=16,
        choices=StageChangeType.choices, default=StageChangeType.UP)
    is_stage_strengthen = models.BooleanField('是否阶段强化', default=False)
    is_test_stop = models.BooleanField('是否停止测试', default=False)
    # 1 通关 2 降级2次
    stop_reason = models.IntegerField('停止原因', default=0)

    cycle_num = models.IntegerField('周期数', default=1)

    # ⚠️废弃
    strengthen_pass_rounds = models.IntegerField('强化已通过轮次', default=0)
    strengthen_fail_rounds = models.IntegerField('强化连续失败轮次', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '用户答题轮次分析表'
        db_table = 'ai_st_user_round_analysis'


# 阶段变更记录
class STUserRoundChangeRecord(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    answer = models.ForeignKey(STUserPaperAnswer, on_delete=models.CASCADE, db_constraint=False)
    old_stage = models.CharField(
        '旧阶段', max_length=32, choices=LearningStage.choices)
    new_stage = models.CharField(
        '新阶段', max_length=32, choices=LearningStage.choices)
    change_type = models.CharField(
        '变更类型', max_length=16, choices=StageChangeType.choices)
    change_report = models.TextField('变更报告', null=True)

    cycle_num = models.IntegerField('周期数', default=1)

    class Meta:
        verbose_name = verbose_name_plural = '阶段变更记录'
        db_table = 'ai_st_user_round_change_record'


class STFirstRoundPaper(BaseModel):
    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    # {"question_ids": [1, 2]}
    paper_content = models.JSONField('试卷内容', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '第一轮试卷表'
        db_table = 'ai_st_first_round_paper'


class STUserAssessmentDetail(BaseModel):
    class AssessmentType(models.TextChoices):
        BASELINE = 'baseline', '摸底'
        REGULAR = 'regular', '常规'

    subject_id = models.CharField('学科id', max_length=32)
    core_course_code = models.CharField('核心课code', max_length=32)
    user_id = models.CharField('用户id', max_length=32, db_index=True)
    paper = models.ForeignKey(STUserPaper, on_delete=models.CASCADE, db_constraint=False)
    answer = models.ForeignKey(STUserPaperAnswer, on_delete=models.CASCADE, db_constraint=False)
    assessment_type = models.CharField('评估类型', max_length=16, choices=AssessmentType.choices)
    assessment_round = models.IntegerField('评估轮次', default=0)
    assessment_score = models.DecimalField('本轮评估得分', max_digits=5, decimal_places=2, default=0)
    subjective_score = models.DecimalField('主观题得分', max_digits=5, decimal_places=2, null=True)

    class Meta:
        verbose_name = verbose_name_plural = '用户评估明细表'
        db_table = 'ai_st_user_assessment_detail'
