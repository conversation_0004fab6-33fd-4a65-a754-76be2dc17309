from app.core.rag.extractor.extractor_base import BaseExtractor
from app.core.rag.extractor.helpers import detect_file_encodings
from app.core.rag.models.document import Document


class TextExtractor(BaseExtractor):
    def __init__(
            self,
            file_path: str,
            encoding: str | None = None,
            autodetect_encoding: bool = False
    ):
        self._file_path = file_path
        self._encoding = encoding
        self._autodetect_encoding = autodetect_encoding

    def extract(self) -> list[Document]:
        try:
            with open(self._file_path, encoding=self._encoding) as f:
                text = f.read()
        except UnicodeDecodeError as e:
            if self._autodetect_encoding:
                detected_encodings = detect_file_encodings(self._file_path)
                text = ''
                for encoding in detected_encodings:
                    try:
                        with open(self._file_path, encoding=encoding.encoding) as f:
                            text = f.read()
                        break
                    except UnicodeDecodeError:
                        continue
                if not text:
                    raise RuntimeError(f"Detected encodings error {self._file_path}") from e
            else:
                raise RuntimeError(f"UnicodeDecodeError loading {self._file_path}") from e
        except Exception as e:
            raise RuntimeError(f"Error loading {self._file_path}") from e

        metadata = {"source": self._file_path}
        return [Document(page_content=text, metadata=metadata)]
