--
-- Create model CourseNoteTask
--
CREATE TABLE `ai_course_note_task` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `course_id` varchar(40) NOT NULL, `chapter_id` varchar(40) NOT NULL, `chapter_name` varchar(100) NOT NULL, `chapter_lecture` longtext NULL, `document_nos` json NULL, `status` varchar(20) NOT NULL, `processing_started_at` datetime(6) NULL, `completed_at` datetime(6) NULL);
--
-- Create model CourseNoteTaskDebug
--
CREATE TABLE `ai_course_note_task_debug` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `course_id` varchar(40) NOT NULL, `status` varchar(20) NOT NULL, `name` varchar(100) NOT NULL, `processing_started_at` datetime(6) NULL, `completed_at` datetime(6) NULL);
--
-- Create model CourseNoteTaskChangeDetail
--
CREATE TABLE `ai_course_note_task_change_detail` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `document_no` varchar(40) NOT NULL, `video_lecture` longtext NULL, `video_note` longtext NULL, `status` varchar(20) NOT NULL, `processing_started_at` datetime(6) NULL, `completed_at` datetime(6) NULL, `message_id` bigint NULL, `task_id` bigint NOT NULL, `video_content_id` bigint NOT NULL);
--
-- Create model CourseNoteTaskChange
--
CREATE TABLE `ai_course_note_task_change` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `change_content` longtext NULL, `task_id` bigint NOT NULL);
--
-- Add field task_debug to coursenotetask
--
ALTER TABLE `ai_course_note_task` ADD COLUMN `task_debug_id` bigint NULL;
--
-- Create model CourseNoteContent
--
CREATE TABLE `ai_course_note_content` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `course_id` varchar(40) NOT NULL, `chapter_id` varchar(40) NOT NULL, `chapter_name` varchar(100) NOT NULL, `document_no` varchar(40) NOT NULL, `video_lecture` longtext NULL, `video_note` longtext NULL, `video_content_id` bigint NOT NULL);
CREATE INDEX `ai_course_note_task_add_time_da065fb6` ON `ai_course_note_task` (`add_time`);
CREATE INDEX `ai_course_note_task_course_id_432997ab` ON `ai_course_note_task` (`course_id`);
CREATE INDEX `ai_course_note_task_chapter_id_56c0d712` ON `ai_course_note_task` (`chapter_id`);
CREATE INDEX `ai_course_note_task_debug_add_time_60525993` ON `ai_course_note_task_debug` (`add_time`);
CREATE INDEX `ai_course_note_task_debug_course_id_d5a66f24` ON `ai_course_note_task_debug` (`course_id`);
CREATE INDEX `ai_course_note_task_change_detail_add_time_92578b6d` ON `ai_course_note_task_change_detail` (`add_time`);
CREATE INDEX `ai_course_note_task_change_detail_document_no_46806adb` ON `ai_course_note_task_change_detail` (`document_no`);
CREATE INDEX `ai_course_note_task_change_detail_message_id_483c171d` ON `ai_course_note_task_change_detail` (`message_id`);
CREATE INDEX `ai_course_note_task_change_detail_task_id_90dfe2bf` ON `ai_course_note_task_change_detail` (`task_id`);
CREATE INDEX `ai_course_note_task_change_detail_video_content_id_4a01a16f` ON `ai_course_note_task_change_detail` (`video_content_id`);
CREATE INDEX `ai_course_note_task_change_add_time_899cdbe8` ON `ai_course_note_task_change` (`add_time`);
CREATE INDEX `ai_course_note_task_change_task_id_b4b61cc5` ON `ai_course_note_task_change` (`task_id`);
CREATE INDEX `ai_course_note_task_task_debug_id_c1cddd7d` ON `ai_course_note_task` (`task_debug_id`);
CREATE INDEX `ai_course_note_content_add_time_7946330a` ON `ai_course_note_content` (`add_time`);
CREATE INDEX `ai_course_note_content_course_id_1cc59810` ON `ai_course_note_content` (`course_id`);
CREATE INDEX `ai_course_note_content_chapter_id_83395b12` ON `ai_course_note_content` (`chapter_id`);
CREATE INDEX `ai_course_note_content_document_no_a0dfd1e4` ON `ai_course_note_content` (`document_no`);
CREATE INDEX `ai_course_note_content_video_content_id_4c41d227` ON `ai_course_note_content` (`video_content_id`);