from app.services.shuati_app.constants import QuestionType, LearningStage
from app.services.shuati_app.dto import QuestionScoreRule, QuestionSelectStrategy, ModiUpRequired, UpgradeRequired, \
    LearningStageRequired

question_score_rules = [
    QuestionScoreRule(question_type=QuestionType.SINGLE.value, difficulty=1, score=1),
    QuestionScoreRule(question_type=QuestionType.SINGLE.value, difficulty=2, score=1.2),
    QuestionScoreRule(question_type=QuestionType.SINGLE.value, difficulty=3, score=1.5),
    QuestionScoreRule(question_type=QuestionType.SINGLE.value, difficulty=4, score=1.8),
    QuestionScoreRule(question_type=QuestionType.SINGLE.value, difficulty=5, score=1.8),
    QuestionScoreRule(question_type=QuestionType.SUBJECTIVE.value, difficulty=1, score=1.2),
    QuestionScoreRule(question_type=QuestionType.SUBJECTIVE.value, difficulty=2, score=1.5),
    QuestionScoreRule(question_type=QuestionType.SUBJECTIVE.value, difficulty=3, score=1.8),
    QuestionScoreRule(question_type=QuestionType.SUBJECTIVE.value, difficulty=4, score=1.8),
    QuestionScoreRule(question_type=QuestionType.SUBJECTIVE.value, difficulty=5, score=1.8),
]


subject_learning_stage_map = {
    'CC_SJJG': [
        # LearningStage.MODI.value, # 摸底逻辑暂不考虑
        LearningStage.BASIC.value,
        LearningStage.BASIC_IMPROVE_L.value,
        LearningStage.BASIC_IMPROVE_M.value,
        LearningStage.BASIC_IMPROVE_H.value,
        LearningStage.CORE_BASIC.value,
        LearningStage.CORE_IMPROVE_L.value,
        LearningStage.CORE_IMPROVE_M.value,
        LearningStage.CORE_IMPROVE_H.value,
    ],
    'CC_CZXT': [
        LearningStage.BASIC.value,
        LearningStage.BASIC_IMPROVE_L.value,
        LearningStage.BASIC_IMPROVE_M.value,
        LearningStage.BASIC_IMPROVE_H.value,
    ],
    'CC_JSXW': [
        LearningStage.BASIC.value,
        LearningStage.BASIC_IMPROVE_L.value,
        LearningStage.BASIC_IMPROVE_M.value,
        LearningStage.BASIC_IMPROVE_H.value,
        LearningStage.CORE_IMPROVE.value,
    ],
    'CC_JSZZYL': [
        LearningStage.BASIC.value,
        LearningStage.BASIC_IMPROVE_L.value,
        LearningStage.BASIC_IMPROVE_M.value,
        LearningStage.BASIC_IMPROVE_H.value,
    ],
}
