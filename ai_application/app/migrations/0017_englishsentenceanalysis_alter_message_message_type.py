# Generated by Django 4.2 on 2025-01-06 03:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0016_message_is_answer_token_exceed_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnglishSentenceAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('sentence', models.TextField(null=True, verbose_name='句子')),
                ('year', models.PositiveSmallIntegerField(default=0, verbose_name='年份')),
                ('type', models.Char<PERSON><PERSON>(blank=True, max_length=255, verbose_name='题型')),
                ('title', models.CharField(blank=True, max_length=255, verbose_name='篇目')),
                ('content', models.JSONField(null=True, verbose_name='解析内容')),
            ],
            options={
                'verbose_name': '英文句子分析',
                'verbose_name_plural': '英文句子分析',
                'db_table': 'english_sentence_analysis',
            },
        ),
        migrations.AlterField(
            model_name='message',
            name='message_type',
            field=models.CharField(choices=[('normal', '普通消息'), ('question', '解题助手'), ('code', '代码优化'), ('grammar', '英语长难句语法分析')], default='normal', max_length=32, verbose_name='消息类型'),
        ),
    ]
