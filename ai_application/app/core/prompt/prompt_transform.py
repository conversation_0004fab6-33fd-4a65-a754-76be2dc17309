from app.core.prompt.entities import PromptMessage
from app.core.prompt.conversation_buffer_memory import ConversationBufferMemory


class PromptTransform:

    def _append_chat_histories(
            self,
            memory: ConversationBufferMemory,
            prompt_messages: list[PromptMessage]
    ):
        histories = self._get_history_messages_list_from_memory(memory)
        prompt_messages.extend(histories)
        return prompt_messages

    def _get_history_messages_list_from_memory(
            self, memory: ConversationBufferMemory
    ):
        return memory.get_history_prompt_messages()
