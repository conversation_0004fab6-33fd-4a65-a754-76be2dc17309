import socket

from django.http import HttpResponse
from django.urls import path, include

from ai_application.nacos import nacos


def nacos_beat_view(request):
    def get_network_ip():
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        __ip__ = s.getsockname()[0]
        s.close()

        return __ip__

    nacos.client.send_heartbeat(nacos.data_id, get_network_ip(), "8000", group_name=nacos.group)
    return HttpResponse(status=200)


urlpatterns = [
    path('check/beat/', nacos_beat_view),
    path('api/', include('app.api.urls')),
]

urlpatterns += [
    path('console/app/', include('app.console.urls')),
]
