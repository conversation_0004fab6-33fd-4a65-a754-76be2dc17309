from django.db import models
from ._base import BaseModel

class AppKnowledgePoint(BaseModel):
    
    name = models.CharField(max_length=100, verbose_name='知识点名称')
    desc = models.TextField(verbose_name='知识点描述', null=True, blank=True)
    en_desc = models.TextField(verbose_name='英文描述', null=True, blank=True)
    importance = models.CharField(max_length=50, verbose_name='重要性', null=True, blank=True)
    difficulty = models.CharField(max_length=50, verbose_name='难度', null=True, blank=True)
    core_course_code = models.CharField(max_length=20, verbose_name='核心课程代码', null=True, blank=True)
    order = models.IntegerField(default=0, verbose_name='排序')
    creator_id = models.Char<PERSON>ield(max_length=50, verbose_name='创建者ID', null=True, blank=True)
    modifier_id = models.Char<PERSON>ield(max_length=50, verbose_name='修改者ID', null=True, blank=True)
    parent_id = models.Char<PERSON>ield(max_length=50, verbose_name='父知识点ID', null=True, blank=True)
    root_id = models.Char<PERSON>ield(max_length=50, verbose_name='根知识点ID', null=True, blank=True)


    class Meta:
        verbose_name = verbose_name_plural = 'app-新知识点'
        db_table = 'ai_app_knowledge_point'