--
-- Create model CoursePackageContent
--
CREATE TABLE `ai_course_package_content` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `unit_name` varchar(36) NOT NULL, `subject` varchar(36) NULL, `course_package_name` varchar(36) NULL, `stage_name` varchar(36) NULL, `combine_unit_name` varchar(36) NULL, `study_target` longtext NULL, `study_guidence` longtext NULL, `unit_task` longtext NULL);
--
-- Create model SuperViseInitStudentStatus
--
CREATE TABLE `ai_student_supervise_init_status` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_id` varchar(100) NOT NULL, `date` date NOT NULL, `exam_date` date NOT NULL, `graduation_major` varchar(100) NULL, `graduation_school` varchar(100) NULL, `graduation_major_code` varchar(100) NULL, `graduation_school_code` varchar(100) NULL, `target` json NULL, `education_level` varchar(100) NULL, `study_status` varchar(100) NULL, `graduation_years` varchar(100) NULL, `study_stage` varchar(100) NULL, `academic_performance` varchar(255) NULL, `english_level` varchar(100) NULL, `math_subjects` json NULL, `math_mastery` varchar(255) NULL, `political_subjects` json NULL, `political_mastery` varchar(255) NULL, `task_id` varchar(36) NULL UNIQUE, `fail_reason` longtext NULL, `query` longtext NULL, `analysis` longtext NULL, `status` varchar(100) NULL);
--
-- Create model SuperviseLearnStageStat
--
CREATE TABLE `ai_student_supervise_learn_stage_stat` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_id` varchar(100) NOT NULL, `course_id` varchar(100) NULL, `task_id` varchar(36) NULL UNIQUE, `status` varchar(100) NULL, `fail_reason` longtext NULL, `query` longtext NULL, `analysis` longtext NULL);
--
-- Alter field malicious_attack_type on message
--
-- (no-op)
CREATE INDEX `ai_course_package_content_add_time_465ed45c` ON `ai_course_package_content` (`add_time`);
CREATE INDEX `ai_course_package_content_unit_name_9c54218f` ON `ai_course_package_content` (`unit_name`);
CREATE INDEX `ai_student_supervise_init_status_add_time_d70c1bcc` ON `ai_student_supervise_init_status` (`add_time`);
CREATE INDEX `ai_student_supervise_init_status_user_id_3823e3e5` ON `ai_student_supervise_init_status` (`user_id`);
CREATE INDEX `ai_student_supervise_learn_stage_stat_add_time_e22a1500` ON `ai_student_supervise_learn_stage_stat` (`add_time`);
CREATE INDEX `ai_student_supervise_learn_stage_stat_user_id_556447c9` ON `ai_student_supervise_learn_stage_stat` (`user_id`);