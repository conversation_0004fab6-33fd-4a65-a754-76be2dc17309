import time
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>

import dashscope
from django.conf import settings

from app.core.model_runtime.entities.text_embedding_entities import TextEmbeddingResult, EmbeddingUsage
from app.core.model_runtime.model_providers._base.text_embedding_model import TextEmbeddingModel


class TongyiTextEmbeddingModel(TextEmbeddingModel):
    def _invoke(self, model: str, texts: list[str]) -> TextEmbeddingResult:
        embeddings, embedding_used_tokens = self.embed_documents(
            model=model,
            texts=texts
        )

        return TextEmbeddingResult(
            embeddings=embeddings,
            usage=self._calc_response_usage(embedding_used_tokens),
            model=model
        )

    def get_num_tokens(self, model: str, credentials: dict, texts: list[str]) -> int:
        """
        Get number of tokens for given prompt messages

        :param model: model name
        :param credentials: model credentials
        :param texts: texts to embed
        :return:
        """
        if len(texts) == 0:
            return 0
        total_num_tokens = 0
        for text in texts:
            total_num_tokens += self._get_num_tokens_by_gpt2(text)

        return total_num_tokens

    @staticmethod
    def embed_documents(model: str, texts: list[str]) -> tuple[list[list[float]], int]:
        """Call out to Tongyi's embedding endpoint.

        Args:
            model: The model to use for embedding.
            texts: The list of texts to embed.

        Returns:
            List of embeddings, one for each text, and tokens usage.
        """
        embeddings = []
        embedding_used_tokens = 0
        for text in texts:
            response = dashscope.TextEmbedding.call(
                api_key=settings.DASHSCOPE_API_KEY,
                model=model,
                input=text,
                text_type="document"
            )
            if response.status_code != HTTPStatus.OK:
                raise Exception(f"Failed to get embedding from Tongyi: {response.status_code}, {dict(response)}")

            data = response.output["embeddings"][0]
            embeddings.append(data["embedding"])
            embedding_used_tokens += response.usage["total_tokens"]

        return [list(map(float, e)) for e in embeddings], embedding_used_tokens

    def _calc_response_usage(self, tokens: int) -> EmbeddingUsage:
        return EmbeddingUsage(
            tokens=tokens,
            total_tokens=tokens,
            latency=time.perf_counter() - self.started_at
        )
