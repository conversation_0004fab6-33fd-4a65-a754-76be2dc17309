from typing import cast

from app.core.prompt.entities import PromptMessage, TextPromptMessageContent, ImagePromptMessageContent, \
    PromptMessageRole, AssistantPromptMessage
from langchain_core.messages import BaseMessage, SystemMessage, HumanMessage, AIMessage


class PromptMessageUtil:
    @staticmethod
    def prompt_messages_to_prompt_for_saving(prompt_messages: list[PromptMessage]) -> list[dict]:
        prompts = []
        tool_calls = []
        for prompt_message in prompt_messages:
            text = ""
            files = []
            if prompt_message.role == PromptMessageRole.ASSISTANT:
                if isinstance(prompt_message, AssistantPromptMessage):
                    tool_calls = [
                        {
                            "id": tool_call.id,
                            "type": "function",
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments,
                            },
                        }
                        for tool_call in prompt_message.tool_calls
                    ]

            if isinstance(prompt_message.content, list):
                for content in prompt_message.content:
                    if isinstance(content, TextPromptMessageContent):
                        text += content.data
                    elif isinstance(content, ImagePromptMessageContent):
                        files.append(
                            {
                                "type": "image",
                                "data": content.data[:10] + "...[TRUNCATED]..." + content.data[-10:],
                                "detail": content.detail.value,
                            }
                        )
            else:
                text = cast(str, prompt_message.content)

            prompt = {"role": prompt_message.role.value, "text": text, "files": files}
            if tool_calls:
                prompt["tool_calls"] = tool_calls
            prompts.append(prompt)
        return prompts

    @staticmethod
    def langchain_prompt_messages_to_prompt_for_saving(prompt_messages: list[BaseMessage]) -> list[dict]:
        prompts = []
        for prompt_message in prompt_messages:
            role = ""
            text = ""
            files = []

            if isinstance(prompt_message, SystemMessage):
                role = 'system'
            elif isinstance(prompt_message, HumanMessage):
                role = 'user'
            elif isinstance(prompt_message, AIMessage):
                role = 'assistant'

            if isinstance(prompt_message.content, list):
                for content in prompt_message.content:
                    if isinstance(content, dict):
                        if content.get('type') == 'image_url':
                            files.append(
                                {
                                    "type": "image",
                                    "detail": content['image_url'].get('url', '')
                                }
                            )
                        elif content.get('type') == 'text':
                            text += content['text']
                    else:
                        text += content
            else:
                text = prompt_message.content

            prompts.append({"role": role, "text": text, "files": files})
        return prompts
