import os
import sys
import django
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_application.settings")
django.setup()

from django.conf import settings
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate

# 添加日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置文本大模型
llm_text = ChatOpenAI(
    openai_api_key=settings.DOUBAO_API_KEY,
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-seed-1-6-250615"
)

def read_markdown_file(file_path):
    """
    读取Markdown文件内容

    Args:
        file_path (str): Markdown文件路径

    Returns:
        str: 文件内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        return content
    except FileNotFoundError:
        logger.error(f"文件未找到: {file_path}")
        return None
    except Exception as e:
        logger.error(f"读取文件时出错: {e}")
        return None

def process_markdown_content(markdown_content):
    """
    使用大模型处理Markdown内容

    Args:
        markdown_content (str): Markdown文件内容

    Returns:
        str: 处理结果
    """
    try:
        # 构造提示词模板
        template = """你是一个专业的文档编辑专家。请对以下Markdown文档内容进行优化和修改，要求如下：

1.将markdown文件中的列表的难度等级的五角星数量修改成数字，数量1-5分别对应数字1、3、5、7、9。
2.同时将markdown文件中的列表的标签字段不再是用了解、基础、核心、重要（分四层）等字眼，而是用五角星数量（分五层）来表示，数量1-5分别对应了解、基础、核心、重要等层级。

请直接输出修改后的Markdown内容，不要添加其他说明文字。

## 原始Markdown内容：
{content}
"""

        prompt = PromptTemplate.from_template(template)
        llm_chain = prompt | llm_text

        # 调用大模型处理内容
        result = llm_chain.invoke({
            "content": markdown_content
        })

        return result
    except Exception as e:
        logger.error(f"处理Markdown内容时出错: {e}")
        return None

def save_result_as_markdown(result, filename):
    """
    将结果保存为Markdown文件

    Args:
        result: 模型生成的结果
        filename: 保存的文件名

    Returns:
        str: 保存的文件路径
    """
    # 确保文件以.md结尾
    if not filename.endswith('.md'):
        filename += '.md'

    # 提取结果内容
    if hasattr(result, 'content'):
        content = result.content
    else:
        content = str(result)

    # 保存到文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

    return filename

def process_markdown_file(input_file_path, output_file_path):
    """
    处理Markdown文件：读取、修改并保存

    Args:
        input_file_path (str): 输入文件路径
        output_file_path (str): 输出文件路径
    """
    # 读取Markdown文件
    markdown_content = read_markdown_file(input_file_path)
    if not markdown_content:
        logger.error("读取Markdown文件失败")
        return False

    # 使用大模型处理内容
    result = process_markdown_content(markdown_content)
    if not result:
        logger.error("处理Markdown内容失败")
        return False

    # 保存结果到新文件
    saved_file = save_result_as_markdown(result, output_file_path)
    if saved_file:
        logger.info(f"修改后的Markdown内容已保存到: {saved_file}")
        return True
    else:
        logger.error("保存文件失败")
        return False

if __name__ == "__main__":
    # 示例用法
    input_file = "408计算机考试大纲分析报告.md"  # 输入文件路径
    output_file = "408计算机考试大纲分析报告新.md"  # 输出文件路径
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        logger.warning(f"输入文件 {input_file} 不存在，请创建该文件或修改文件路径")
        print("请提供要处理的Markdown文件路径作为参数")
        print("用法: python change_md.py <输入文件路径> <输出文件路径>")
        sys.exit(1)
    
    # 如果提供了命令行参数，则使用参数中的文件路径
    if len(sys.argv) >= 3:
        input_file = sys.argv[1]
        output_file = sys.argv[2]
    elif len(sys.argv) >= 2:
        input_file = sys.argv[1]
        # 自动生成输出文件名
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_modified.md"
    
    # 处理Markdown文件
    success = process_markdown_file(input_file, output_file)
    
    if success:
        print(f"成功处理Markdown文件，修改后的内容已保存到: {output_file}")
    else:
        print("处理Markdown文件失败")
        sys.exit(1)