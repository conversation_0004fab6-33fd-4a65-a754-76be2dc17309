from pydantic import Field, ConfigDict

from app.core.prompt.entities import PromptMessage, AssistantPromptMessage
from django_ext.base_dto_model import MyBaseModel


class LLMUsage(MyBaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    latency: float

    @classmethod
    def empty_usage(cls):
        return cls(
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            latency=0.0,
        )

    @classmethod
    def only_latency_usage(cls, latency: float):
        return cls(
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            latency=latency,
        )


class LLMToolCallResponse(MyBaseModel):
    tool_call_id: str
    name: str = ''
    arguments: str = ''
    content: str = ''


class LLMResult(MyBaseModel):
    model_provider: str = ''
    model: str
    model_params: dict = Field(default={})
    prompt_messages: list[PromptMessage] = Field(default=[])
    message: AssistantPromptMessage
    usage: LLMUsage
    is_answer_token_exceed: bool = False
    is_sensitive: bool = False
    is_success: bool = True
    tool_calls_response: list[LLMToolCallResponse] = []

    model_config = ConfigDict(protected_namespaces=())

    @classmethod
    def judge_answer_token_exceed(cls, model_params: dict, usage: LLMUsage) -> bool:
        max_tokens = model_params.get('max_tokens')
        if not max_tokens:
            return False

        completion_tokens = usage.completion_tokens
        if completion_tokens > max_tokens:
            return True

        return abs(max_tokens - completion_tokens) <= 5


class LLMResultChunkDelta(MyBaseModel):
    index: int
    message: AssistantPromptMessage
    usage: LLMUsage | None = None
    tool_calls_response: list[LLMToolCallResponse] = []
    finish_reason: str | None = None


class LLMWorkflowChunk(MyBaseModel):
    id: str = ''  # 分块流式唯一id
    type: str = ''  # "title"|"definition"|"detail"|"recommend"|"is_usable"|"regeneration" recommend 一条 data
    pos: str = ''  # head, body, tail
    title: str = ''  # node标题


class LLMResultChunk(MyBaseModel):
    model_provider: str = ''
    model: str
    model_params: dict = Field(default={})
    prompt_messages: list[PromptMessage]
    delta: LLMResultChunkDelta
    workflow_chunk: LLMWorkflowChunk | None = None

    model_config = ConfigDict(protected_namespaces=())
