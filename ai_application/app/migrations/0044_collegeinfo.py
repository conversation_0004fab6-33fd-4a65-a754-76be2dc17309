# Generated by Django 4.2 on 2025-04-16 10:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0043_merge_20250416_1810'),
    ]

    operations = [
        migrations.CreateModel(
            name='CollegeInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('discipline_category_code', models.CharField(max_length=10, verbose_name='学科门类编号')),
                ('discipline_category', models.CharField(max_length=255, verbose_name='学科门类')),
                ('primary_major_code', models.Char<PERSON>ield(max_length=10, verbose_name='一级专业编号')),
                ('primary_major', models.Char<PERSON>ield(max_length=255, verbose_name='一级专业')),
                ('secondary_major_code', models.CharField(max_length=10, verbose_name='二级专业编号')),
                ('secondary_major', models.CharField(max_length=255, verbose_name='二级专业')),
                ('suitable_population', models.TextField(verbose_name='适合人群')),
                ('description', models.TextField(verbose_name='说明')),
                ('employment_direction', models.TextField(verbose_name='就业方向')),
                ('common_examination_major', models.TextField(verbose_name='常考专业')),
                ('recommended_colleges', models.TextField(verbose_name='院校推荐')),
            ],
            options={
                'verbose_name': '院校资料',
                'verbose_name_plural': '院校资料',
                'db_table': 'ai_college_info',
            },
        ),
    ]
