import hashlib
import hmac
import time

from django.conf import settings
from rest_framework.authentication import BaseAuthentication

from app.errors import SignatureInvalidError, SignatureExpiredError
from app.auth.account_config import get_all_account
from app.models import Account


class MyAuthAuthentication(BaseAuthentication):
    def authenticate(self, request):
        # 判断如果request.path不已/api开头，则不校验
        if not request.path.startswith('/api'):
            return None

        if request.path.startswith('/api/v1/skip/'):
            return None

        # 跳过学习状态相关API的认证（用于Demo页面）
        if request.path.startswith('/api/v1/learning_status/'):
            return None

        api_key = request.META.get("HTTP_X_API_KEY", '')
        signature = request.META.get("HTTP_X_SIGNATURE", '')
        timestamp = request.META.get("HTTP_TIMESTAMP", '')
        nonce = request.META.get("HTTP_NONCE", '')
        sign_debug = bool(request.META.get("HTTP_X_SIGN_DEBUG", ''))

        # 生产环境不校验debug参数
        if settings.ENVIRONMENT == settings.ENV_PRODUCT:
            sign_debug = False

        all_account = get_all_account()
        if not api_key or api_key not in all_account:
            raise SignatureInvalidError()

        account: Account = all_account.get(api_key)
        secret_key = account.secret_key
        sign_dict = {
            'api_key': api_key,
            'timestamp': timestamp,
            'nonce': nonce,
        }
        calculated_signature = gen_sign(sign_dict, secret_key)
        if not hmac.compare_digest(calculated_signature, signature):
            raise SignatureInvalidError()

        if not sign_debug and abs(int(time.time()) - int(timestamp)) > 1800:
            raise SignatureExpiredError()

        return account, None


def gen_sign(params: dict, secret_key: str):
    string_to_sign = "&".join([f"{key}={value}" for key, value in sorted(params.items())])
    return hmac.new(secret_key.encode(), string_to_sign.encode(), hashlib.sha256).hexdigest()
