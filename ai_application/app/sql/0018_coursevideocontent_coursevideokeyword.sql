--
-- Create model CourseVideoContent
--
CREATE TABLE `ai_course_video_content`
(
    `id`                bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`          datetime(6) NOT NULL,
    `modified_time`     datetime(6) NOT NULL,
    `is_deleted`        bool         NOT NULL,
    `name`              varchar(100) NOT NULL,
    `content`           longtext NULL,
    `is_optimized`      bool         NOT NULL,
    `optimized_content` longtext NULL,
    `dataset_id`        bigint       NOT NULL
);
--
-- Create model CourseVideoKeyword
--
CREATE TABLE `ai_course_video_keyword`
(
    `id`                  bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`            datetime(6) NOT NULL,
    `modified_time`       datetime(6) NOT NULL,
    `is_deleted`          bool             NOT NULL,
    `keyword`             varchar(100)     NOT NULL,
    `weight`              double precision NOT NULL,
    `dataset_id`          bigint           NOT NULL,
    `dataset_document_id` bigint           NOT NULL,
    `video_content_id`    bigint           NOT NULL
);
CREATE INDEX `ai_course_video_content_add_time_1f858a37` ON `ai_course_video_content` (`add_time`);
CREATE INDEX `ai_course_video_content_dataset_id_d9a7bcf2` ON `ai_course_video_content` (`dataset_id`);
CREATE INDEX `ai_course_video_keyword_add_time_78aafe8d` ON `ai_course_video_keyword` (`add_time`);
CREATE INDEX `ai_course_video_keyword_dataset_id_f78e73b6` ON `ai_course_video_keyword` (`dataset_id`);
CREATE INDEX `ai_course_video_keyword_dataset_document_id_3b6fa821` ON `ai_course_video_keyword` (`dataset_document_id`);
CREATE INDEX `ai_course_video_keyword_video_content_id_a239cf5b` ON `ai_course_video_keyword` (`video_content_id`);