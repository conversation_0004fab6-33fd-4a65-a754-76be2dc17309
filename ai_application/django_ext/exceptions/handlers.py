from logging import getLogger

from django.core.exceptions import PermissionDenied
from django.http import Http404
from rest_framework.views import set_rollback
from rest_framework import exceptions, status
from rest_framework.response import Response

from django_ext.response import res_msg
from django_ext.logger.utils import log_msg
from django_ext.exceptions import InternalException

logger = getLogger(__name__)


def rest_handler(exc, context):
    """
    restframework 异常处理

    :param Exception exc:
    :param Context context: 上下文
    :return:
    """
    msg = log_msg({
        "request": context['request'],
        "query_params": context['request'].query_params.dict(),
        "error": exc
    })
    logger.exception(msg)

    if isinstance(exc, Http404):
        exc = exceptions.NotFound(detail=str(exc), code='10404')  # Not Found
    elif isinstance(exc, (PermissionDenied, exceptions.PermissionDenied)):
        exc = InternalException(code='10005')  # 没有权限
    elif isinstance(exc, (exceptions.AuthenticationFailed, exceptions.NotAuthenticated)):
        exc = InternalException(code='10001')
    elif isinstance(exc, exceptions.NotFound):
        exc = InternalException(code='10404')  # 未找到链接
    elif isinstance(exc, exceptions.Throttled):
        exc = InternalException(code='429')  # 请求超出最大调用频率

    headers = {}
    if getattr(exc, 'wait', None):
        headers['Retry-After'] = '%d' % getattr(exc, 'wait')

    if isinstance(exc, exceptions.APIException):
        if isinstance(exc, InternalException):
            internal_err = getattr(exc, 'internal_err', None)
            data = res_msg(code=exc.code, msg=exc.detail, detail=exc.detail_err, internal_err=internal_err)
        elif isinstance(exc, exceptions.ValidationError):
            data = res_msg(code='41001', detail=exc.detail)
        elif isinstance(exc.detail, (list, dict)):
            data = exc.detail
        elif isinstance(exc.detail, exceptions.ErrorDetail):
            data = res_msg(code='-2', detail=f'{exc.detail.code}:{exc.detail}')
        else:
            data = res_msg(code='-2', detail=str(exc.detail))
    else:
        data = res_msg(code='-2', detail=str(exc))

    set_rollback()
    return Response(data, status=status.HTTP_200_OK, headers=headers)
