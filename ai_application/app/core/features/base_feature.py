from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.invoke_llm import query_llm_by_prompt, get_llm_model_config
from app.models import PromptTemplate


class BaseFeature:

    def query_llm_by_prompt_template(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            prompt_template: PromptTemplate,
            user_inputs: dict,
            stream: bool,
            prompt_content: str = ''
    ):
        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=prompt_content
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        # 调用模型
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=stream
        )
