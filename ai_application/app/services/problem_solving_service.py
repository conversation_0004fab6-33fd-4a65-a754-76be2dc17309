# 阿纪
import re
import json
import http.client
from django.http import JsonResponse

from api_client.data.vector_data_client import vector_data_client
from app.api.dto import ChatMessageDto
from app.models import Account, InvokeFrom, App, PromptTemplate
from app.services.app_generate_service import AppGenerateService


class ProblemSolvingService:
    @classmethod
    def get_answer(cls, user_question: str, account: Account, user_requirement: str):
        # 数据搜索接口 word参数不能有 \n
        user_question = user_question.replace('\n', ' ')
        params = {'word': user_question, 'type': 'question', 'from': 1, 'size': 3}

        # 搜索三道相似题目：
        questions = vector_data_client.get_answer(params)
        if questions:
            # 提取数据
            first_master_title = questions['data']['data'][0]['master_title_format']
            second_master_title = questions['data']['data'][1]['master_title_format']
            third_master_title = questions['data']['data'][2]['master_title_format']

            first_choice_body = questions['data']['data'][0]['sub_question_info_format'][0]['choice_body']
            second_choice_body = questions['data']['data'][1]['sub_question_info_format'][0]['choice_body']
            third_choice_body = questions['data']['data'][2]['sub_question_info_format'][0]['choice_body']

            # 拼接
            first_title_combined = f"{first_master_title}\n{first_choice_body}"
            second_title_combined = f"{second_master_title}\n{second_choice_body}"
            third_title_combined = f"{third_master_title}\n{third_choice_body}"

            # 答案+解析
            first_answer_body = questions['data']['data'][0]['sub_question_info_format'][0]['answer_body']
            first_answer_analysis = questions['data']['data'][0]['sub_question_info_format'][0]['analysis']

            second_answer_body = questions['data']['data'][1]['sub_question_info_format'][0]['answer_body']
            second_answer_analysis = questions['data']['data'][1]['sub_question_info_format'][0]['analysis']

            third_answer_body = questions['data']['data'][2]['sub_question_info_format'][0]['answer_body']
            third_answer_analysis = questions['data']['data'][2]['sub_question_info_format'][0]['analysis']

            first_answer_combined = f"{first_answer_body}\n{first_answer_analysis}"
            second_answer_combined = f"{second_answer_body}\n{second_answer_analysis}"
            third_answer_combined = f"{third_answer_body}\n{third_answer_analysis}"

            template = PromptTemplate.objects.filter(app_no='compare_three_inputs').first()

            dto = ChatMessageDto(
                app_id='compare_three_inputs',
                inputs={
                    "max_tokens": 1500,
                    "temperature": 0.1,
                    'top_p': 0.5,
                    'prompt_template': template.id,
                    'user_question': user_question,
                    'first_title_combined': first_title_combined,
                    'second_title_combined': second_title_combined,
                    'third_title_combined': third_title_combined,
                    'is_debug': 1,
                },
                query=user_question,
                stream=False,
            )
            response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)
            answer_1 = response.get('answer', '')

            # 在 get_answer 中调用 process_comparison 并传入 user_requirement
            if int(answer_1) == 1:
                answer = cls.process_comparison(user_question, first_title_combined, first_answer_combined, account, user_requirement)["answer"]
            elif int(answer_1) == 2:
                answer = cls.process_comparison(user_question, second_title_combined, second_answer_combined, account, user_requirement)["answer"]
            else:
                answer = cls.process_comparison(user_question, third_title_combined, third_answer_combined, account, user_requirement)["answer"]

            return {
                "result": answer,
            }
        else:
            return {
                "result": "抱歉！没有匹配到对应题目,请给出更多信息或者搜索其他题目",
            }

    @classmethod
    def compare_user_input(cls, user_question: str, title_combined: str, account: Account, user_requirement: str) -> str:
        template = PromptTemplate.objects.filter(app_no='compare_user_input').first()
        dto = ChatMessageDto(
            app_id='compare_user_input',
            inputs={
                "max_tokens": 1500,
                "temperature": 0.1,
                'top_p': 0.5,
                'prompt_template': template.id,
                'user_question': user_question,
                'title_combined': title_combined,
                'user_requirement': user_requirement,  # 加入 user_requirement
                'is_debug': 1,
            },
            query=user_question,
            stream=False,
        )
        response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)
        return response.get('answer', '')

    @classmethod
    def process_comparison(cls, user_question: str, title_combined: str, answer_combined: str, account: Account, user_requirement: str) -> dict:
        answer_2 = cls.compare_user_input(user_question, title_combined, account, user_requirement)

        if int(answer_2) > 0:
            template = PromptTemplate.objects.filter(app_no='process_comparison').first()
            dto = ChatMessageDto(
                app_id='process_comparison',
                inputs={
                    "max_tokens": 1500,
                    "temperature": 0.3,
                    'top_p': 0.5,
                    'prompt_template': template.id,
                    'title_combined': title_combined,
                    'answer_combined': answer_combined,
                    'user_requirement': user_requirement,  # 加入 user_requirement
                    'is_debug': 1,
                },

                stream=False,
            )
            response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)
            answer = response.get('answer', '')

            return {"answer": answer}
        else:
            return {"answer": "抱歉！没有匹配到对应题目,请给出更多信息或者搜索其他题目"}
