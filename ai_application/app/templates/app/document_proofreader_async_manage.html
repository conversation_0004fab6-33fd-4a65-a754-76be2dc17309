<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异步校对任务管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% load static %}
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--gray-800);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            text-align: center;
            border: 1px solid var(--gray-200);
        }

        .stat-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-card .label {
            color: var(--gray-600);
            font-size: 0.9rem;
        }

        .stat-card.pending { color: var(--warning-color); }
        .stat-card.processing { color: var(--primary-color); }
        .stat-card.success { color: var(--secondary-color); }
        .stat-card.failed { color: var(--danger-color); }

        .main-content {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-200);
        }

        .content-header {
            background: var(--gray-50);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-800);
        }

        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        .table-container {
            overflow-x: auto;
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
        }

        .task-table th,
        .task-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .task-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .task-table tr:hover {
            background: var(--gray-50);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .status-badge.pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-badge.processing {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge.success {
            background: #d1fae5;
            color: #065f46;
        }

        .status-badge.failed {
            background: #fee2e2;
            color: #991b1b;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            padding: 0.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: white;
            font-size: 0.8rem;
            transition: all 0.2s;
        }

        .action-btn.view {
            background: var(--primary-color);
        }

        .action-btn.retry {
            background: var(--warning-color);
        }

        .action-btn.delete {
            background: var(--danger-color);
        }

        .action-btn:hover {
            transform: translateY(-1px);
            filter: brightness(1.1);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--gray-400);
        }

        .pagination {
            display: flex;
            justify-content: center;
            padding: 2rem;
            gap: 0.5rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            border: 1px solid var(--gray-300);
            background: white;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .pagination button:hover:not(:disabled) {
            background: var(--gray-50);
        }

        .pagination button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--gray-500);
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .tooltip {
            position: relative;
        }

        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--gray-800);
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .content-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .controls {
                justify-content: center;
            }

            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tasks"></i> 异步校对任务管理</h1>
            <p>管理和监控所有文档校对任务的执行状态</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-cards" id="statsCards">
            <div class="stat-card pending">
                <div class="icon"><i class="fas fa-clock"></i></div>
                <div class="number" id="pendingCount">0</div>
                <div class="label">等待中</div>
            </div>
            <div class="stat-card processing">
                <div class="icon"><i class="fas fa-cog fa-spin"></i></div>
                <div class="number" id="processingCount">0</div>
                <div class="label">处理中</div>
            </div>
            <div class="stat-card success">
                <div class="icon"><i class="fas fa-check-circle"></i></div>
                <div class="number" id="successCount">0</div>
                <div class="label">已完成</div>
            </div>
            <div class="stat-card failed">
                <div class="icon"><i class="fas fa-times-circle"></i></div>
                <div class="number" id="failedCount">0</div>
                <div class="label">失败</div>
            </div>
        </div>

        <div class="main-content">
            <div class="content-header">
                <h2>任务列表</h2>
                <div class="controls">
                    <button class="btn btn-secondary" onclick="refreshTasks()">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                    <a href="/console/app/document_proofreader/" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        新建校对
                    </a>
                </div>
            </div>

            <div class="table-container">
                <div id="loading" class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>正在加载任务列表...</p>
                </div>

                <table class="task-table" id="taskTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>完成时间</th>
                            <th>重试次数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="taskTableBody">
                    </tbody>
                </table>

                <div id="emptyState" class="empty-state" style="display: none;">
                    <i class="fas fa-inbox"></i>
                    <h3>暂无任务</h3>
                    <p>还没有任何校对任务，点击上方"新建校对"开始</p>
                </div>
            </div>

            <div class="pagination" id="pagination" style="display: none;">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTasks();
            // 每30秒自动刷新一次
            setInterval(loadTasks, 10000);
        });

        // 加载任务列表
        async function loadTasks(page = 1) {
            try {
                const response = await fetch(`/console/app/document_proofreader_async_manage/api/?page=${page}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    updateStats(data.data.stats);
                    updateTaskTable(data.data.tasks);
                    updatePagination(data.data.pagination);
                    
                    document.getElementById('loading').style.display = 'none';
                    
                    if (data.data.tasks.length > 0) {
                        document.getElementById('taskTable').style.display = 'table';
                        document.getElementById('emptyState').style.display = 'none';
                    } else {
                        document.getElementById('taskTable').style.display = 'none';
                        document.getElementById('emptyState').style.display = 'block';
                    }
                } else {
                    console.error('加载任务失败:', data.message);
                }
            } catch (error) {
                console.error('加载任务时发生错误:', error);
            }
        }

        // 更新统计数据
        function updateStats(stats) {
            document.getElementById('pendingCount').textContent = stats.pending || 0;
            document.getElementById('processingCount').textContent = stats.processing || 0;
            document.getElementById('successCount').textContent = stats.success || 0;
            document.getElementById('failedCount').textContent = stats.failed || 0;
        }

        // 更新任务表格
        function updateTaskTable(tasks) {
            const tbody = document.getElementById('taskTableBody');
            tbody.innerHTML = '';
            
            tasks.forEach(task => {
                const row = document.createElement('tr');
                
                // 状态图标和文本
                let statusIcon = '';
                let statusText = '';
                let statusClass = '';
                
                switch(task.status) {
                    case 'pending':
                        statusIcon = 'fas fa-clock';
                        statusText = '等待中';
                        statusClass = 'pending';
                        break;
                    case 'processing':
                        statusIcon = 'fas fa-cog fa-spin';
                        statusText = '处理中';
                        statusClass = 'processing';
                        break;
                    case 'success':
                        statusIcon = 'fas fa-check-circle';
                        statusText = '已完成';
                        statusClass = 'success';
                        break;
                    case 'failed':
                        statusIcon = 'fas fa-times-circle';
                        statusText = '失败';
                        statusClass = 'failed';
                        break;
                }
                
                // 操作按钮
                let actionButtons = '';
                if (task.status === 'success' && task.proofreader_id) {
                    actionButtons += `<button class="action-btn view tooltip" data-tooltip="查看结果" onclick="viewResult(${task.proofreader_id})">
                        <i class="fas fa-eye"></i>
                    </button>`;
                }
                if (task.status === 'failed') {
                    actionButtons += `<button class="action-btn retry tooltip" data-tooltip="重试任务" onclick="retryTask('${task.task_id}')">
                        <i class="fas fa-redo"></i>
                    </button>`;
                }
                actionButtons += `<button class="action-btn delete tooltip" data-tooltip="删除任务" onclick="deleteTask('${task.task_id}')">
                    <i class="fas fa-trash"></i>
                </button>`;
                
                row.innerHTML = `
                    <td>
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            ${task.file_name}
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${statusClass}">
                            <i class="${statusIcon}"></i>
                            ${statusText}
                        </span>
                        ${task.status === 'failed' && task.fail_reason ? 
                            `<div style="font-size: 0.8rem; color: var(--gray-500); margin-top: 0.25rem;">
                                ${task.fail_reason.substring(0, 50)}${task.fail_reason.length > 50 ? '...' : ''}
                            </div>` : ''}
                    </td>
                    <td>${task.add_time}</td>
                    <td>${task.update_time}</td>
                    <td>
                        <span class="status-badge ${task.retry_times > 0 ? 'failed' : 'success'}">
                            ${task.retry_times}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            ${actionButtons}
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 更新分页
        function updatePagination(pagination) {
            currentPage = pagination.page;
            totalPages = pagination.total_pages;
            
            const paginationContainer = document.getElementById('pagination');
            if (totalPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
            }
            
            paginationContainer.style.display = 'flex';
            paginationContainer.innerHTML = '';
            
            // 上一页
            const prevBtn = document.createElement('button');
            prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => loadTasks(currentPage - 1);
            paginationContainer.appendChild(prevBtn);
            
            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    const pageBtn = document.createElement('button');
                    pageBtn.textContent = i;
                    pageBtn.className = i === currentPage ? 'active' : '';
                    pageBtn.onclick = () => loadTasks(i);
                    paginationContainer.appendChild(pageBtn);
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    const dots = document.createElement('span');
                    dots.textContent = '...';
                    dots.style.padding = '0.5rem';
                    paginationContainer.appendChild(dots);
                }
            }
            
            // 下一页
            const nextBtn = document.createElement('button');
            nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => loadTasks(currentPage + 1);
            paginationContainer.appendChild(nextBtn);
        }

        // 刷新任务
        function refreshTasks() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('taskTable').style.display = 'none';
            loadTasks(currentPage);
        }

        // 查看结果
        function viewResult(proofreaderId) {
            window.open(`/console/app/document_proofreader/result/${proofreaderId}/`, '_blank');
        }

        // 重试任务
        async function retryTask(taskId) {
            if (!confirm('确定要重试这个任务吗？')) return;
            
            try {
                const response = await fetch('/console/app/document_proofreader_async_manage/retry/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ task_id: taskId })
                });
                
                const data = await response.json();
                if (data.status === 'success') {
                    alert('任务已重新提交到队列');
                    loadTasks(currentPage);
                } else {
                    alert('重试失败: ' + data.message);
                }
            } catch (error) {
                alert('重试时发生错误: ' + error.message);
            }
        }

        // 删除任务
        async function deleteTask(taskId) {
            if (!confirm('确定要删除这个任务吗？此操作不可撤销。')) return;
            
            try {
                const response = await fetch('/console/app/document_proofreader_async_manage/delete/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ task_id: taskId })
                });
                
                const data = await response.json();
                if (data.status === 'success') {
                    alert('任务已删除');
                    loadTasks(currentPage);
                } else {
                    alert('删除失败: ' + data.message);
                }
            } catch (error) {
                alert('删除时发生错误: ' + error.message);
            }
        }
    </script>
</body>
</html> 