# Generated by Django 4.2 on 2025-04-01 09:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0034_englishwordtestanswer_englishwordtestorigin_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='cozeworkflowresult',
            name='process_fail_reason',
            field=models.TextField(null=True, verbose_name='处理流水线失败原因'),
        ),
        migrations.AddField(
            model_name='cozeworkflowresult',
            name='process_status',
            field=models.CharField(blank=True, max_length=32, verbose_name='处理流水线结果状态'),
        ),
        migrations.AddField(
            model_name='englishwordtestanswerdetail',
            name='is_answered',
            field=models.BooleanField(default=False, verbose_name='是否答题'),
        ),
        migrations.CreateModel(
            name='EnglishWordWrongQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(blank=True, db_index=True, max_length=100, verbose_name='用户id')),
                ('answer_num', models.IntegerField(default=0, verbose_name='答题次数')),
                ('is_right_again', models.BooleanField(default=False, verbose_name='是否重新答对')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordtestquestion')),
            ],
            options={
                'verbose_name': '英语单词错题集',
                'verbose_name_plural': '英语单词错题集',
                'db_table': 'ai_data_english_word_wrong_question',
            },
        ),
    ]
