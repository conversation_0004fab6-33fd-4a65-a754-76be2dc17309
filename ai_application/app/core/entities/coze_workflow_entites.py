from enum import Enum

from django_ext.base_dto_model import MyBaseModel


class WorkflowEvent(str, Enum):
    """
    QueueEvent enum
    """
    MESSAGE = "message"
    MESSAGE_END = "message_end"
    ERROR = "error"


class CozeWorkflowEvent(MyBaseModel):
    event: WorkflowEvent


class CozeWorkflowMessageEvent(CozeWorkflowEvent):
    """
    QueueLLMChunkEvent entity
    """
    event: WorkflowEvent = WorkflowEvent.MESSAGE
    content: str


class CozeWorkflowMessageEndEvent(CozeWorkflowEvent):
    """
    QueueLLMChunkEvent entity
    """
    event: WorkflowEvent = WorkflowEvent.MESSAGE_END
    answer: str
    latency: float


class CozeWorkflowMessageErrorEvent(CozeWorkflowEvent):
    """
    QueueLLMChunkEvent entity
    """
    event: WorkflowEvent = WorkflowEvent.ERROR
    error_code: int
    error_message: str
