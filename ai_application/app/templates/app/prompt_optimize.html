<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt优化器</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-size: 14px;
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
        }

        .container {
            display: flex;
            justify-content: center;
            padding: 20px;
        }

        .left-panel, .middle-panel, .right-panel {
            margin: 10px;
            padding: 0 20px 10px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            box-sizing: border-box;
            border-radius: 8px;
        }

        .left-panel {
            flex: 1;
            max-width: 400px;
        }

        .middle-panel {
            flex: 1;
        }

        .right-panel {
            flex: 2;
        }

        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .llm-preview .result {
            margin: 0;
            font-size: 14px;
            white-space: pre-line;
        }
        .llm-preview .usage {
            margin: 0;
            font-size: 14px;
        }
        .llm-preview .preview-title {
            font-weight: bold;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            .left-panel, .middle-panel, .right-panel {
                margin: 10px 0;
                flex-basis: auto;
            }
        }
        /* 加载框样式 */
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>正在请求...</p>
        </div>
    </div>
    <div class="container">
        <div class="left-panel">
            <h3>原始文本</h3>
            <!-- 表单区域 -->
            <form id="form1" action="{% url 'prompt_optimize_submit1' %}">
                <label for="role1">角色:</label><br>
                <textarea rows="3" id="role1" name="role"></textarea>
                <span id="role1-count">0/200</span><br><br>

                <label for="tone1">情感基调:</label><br>
                <textarea rows="3" id="tone1" name="tone"></textarea>
                <span id="tone1-count">0/200</span><br><br>

                <label for="task1">任务:</label><br>
                <textarea rows="3" id="task1" name="task"></textarea>
                <span id="task1-count">0/200</span><br><br>

                <label for="examples1">示例:</label><br>
                <textarea rows="3" id="examples1" name="examples"></textarea>
                <span id="examples1-count">0/200</span><br><br>

                <label for="instructions1">说明:</label><br>
                <textarea rows="3" id="instructions1" name="instructions"></textarea>
                <span id="instructions1-count">0/200</span><br><br>

                <label for="note1">注意:</label><br>
                <textarea rows="3" id="note1" name="note"></textarea>
                <span id="note1-count">0/200</span><br><br>

                <input type="submit" value="提交">
            </form>
        </div>
        <div class="middle-panel">
            <div class="new-form">
                <h3>优化结果</h3>
                <form id="form2">
                    <label for="role2">角色:</label><br>
                    <textarea rows="3" id="role2" name="role"></textarea>
                    <span id="role2-count">0/200</span><br><br>

                    <label for="tone2">情感基调:</label><br>
                    <textarea rows="3" id="tone2" name="tone"></textarea>
                    <span id="tone2-count">0/200</span><br><br>

                    <label for="task2">任务:</label><br>
                    <textarea rows="3" id="task2" name="task"></textarea>
                    <span id="task2-count">0/200</span><br><br>

                    <label for="examples2">示例:</label><br>
                    <textarea rows="3" id="examples2" name="examples"></textarea>
                    <span id="examples2-count">0/200</span><br><br>

                    <label for="instructions2">说明:</label><br>
                    <textarea rows="3" id="instructions2" name="instructions"></textarea>
                    <span id="instructions2-count">0/200</span><br><br>

                    <label for="note2">注意:</label><br>
                    <textarea rows="3" id="note2" name="note"></textarea>
                    <span id="note2-count">0/200</span><br><br>
                </form>
            </div>

            <div class="preview">
                <h3>优化提示词</h3>
                <div id="preview-pre-prompt" class="llm-preview">
                    <!-- 预览内容 -->
                </div>
            </div>

            <div class="preview">
                <h3>原始优化结果</h3>
                <div id="preview-content1" class="llm-preview">
                    <!-- 预览内容 -->
                </div>
            </div>
        </div>
        <div class="right-panel">
            <h3>预览会话</h3>
            <!-- 展示层区域 -->
            <div id="displayLayer">
                <div id="preview-content2" class="llm-preview">
                </div>
                <hr>

                <!-- 这里可以放置一些静态内容或者动态加载的内容 -->
                <form id="queryForm" action="{% url 'prompt_optimize_submit2' %}">
                    <div class="form-group">
                        <label for="query2">用户输入:</label>
                        <textarea id="query2" name="query2" rows="3" placeholder="Enter additional input here..."></textarea>
                    </div>
                    <input type="submit" class="submit" value="提交">
                    <input type="button" class="rest" value="重置会话">
                </form>
            </div>
        </div>
    </div>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script>
        $(function() {
            let conversationId = null

            $('textarea').each(function() {
                const textarea = $(this);
                const countId = textarea.attr('id') + '-count';
                const countLabel = $('#' + countId);

                // 更新字符计数器
                textarea.on('input', function() {
                    const length = $(this).val().length;
                    const maxLength = 200; // 可以根据需要调整最大长度
                    countLabel.text(length + '/' + maxLength);

                    // 如果超过最大长度，截断输入
                    if (length > maxLength) {
                        $(this).val($(this).val().substring(0, maxLength));
                        // 更新计数器
                        countLabel.text(maxLength + '/' + maxLength);
                    }
                });
            });

            function updateTextareaValue(textareaId, newValue) {
                const textarea = $('#' + textareaId);
                const countId = textareaId + '-count';
                const countLabel = $('#' + countId);
                const maxLength = 200; // 最大允许的字符数

                // 赋值
                textarea.val(newValue);

                // 更新计数器
                const length = newValue.length;
                countLabel.text(length + '/' + maxLength);

                // 如果超过最大长度，截断输入
                if (length > maxLength) {
                    textarea.val(newValue.substring(0, maxLength));
                    // 更新计数器
                    countLabel.text(maxLength + '/' + maxLength);
                }
            }

            $('#form1').submit(function(e) {
                e.preventDefault(); // 阻止表单的默认提交行为

                const formData = {
                    role: $('#role1').val(),
                    tone: $('#tone1').val(),
                    task: $('#task1').val(),
                    examples: $('#examples1').val(),
                    instructions: $('#instructions1').val(),
                    note: $('#note1').val(),
                };

                if (!formData.role) {
                    alert('角色不能为空')
                    return
                }

                $('#loadingOverlay').css('display', 'flex')
                $.ajax({
                    type: 'POST',
                    url: $('#form1').attr('action'), // 替换为你的服务器端点
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        // 处理响应数据
                        console.log(response)
                        $('#preview-pre-prompt').html(
                            '<div class="result">' + response.data.pre_prompt + '</div>'
                        );
                        $('#preview-content1').html(
                            '<div class="result">' + response.data.prompt_str + '</div>'
                        );

                        updateTextareaValue('role2', response.data.prompt_dict.role || '')
                        updateTextareaValue('tone2', response.data.prompt_dict.tone || '')
                        updateTextareaValue('task2', response.data.prompt_dict.task || '')
                        updateTextareaValue('examples2', response.data.prompt_dict.examples || '')
                        updateTextareaValue('instructions2', response.data.prompt_dict.instructions || '')
                        updateTextareaValue('note2', response.data.prompt_dict.note || '')

                        $('#loadingOverlay').css('display', 'none')
                    },
                    error: function(xhr, status, error) {
                        {#// 处理错误情况#}
                        $('#preview-content1').html('Error submitting form.');
                        $('#loadingOverlay').css('display', 'none')
                    }
                });


            })

            $('#queryForm').submit(function(e) {
                e.preventDefault(); // 阻止表单的默认提交行为

                const formData = {
                    role: $('#role2').val(),
                    tone: $('#tone2').val(),
                    task: $('#task2').val(),
                    examples: $('#examples2').val(),
                    note: $('#note2').val(),
                    query: $('#query2').val(),
                    conversation_id: conversationId
                };

                if (!formData.role) {
                    alert('角色不能为空')
                    return
                }

                if (!formData.query) {
                    alert('用户输入不能为空')
                    return
                }

                $('#preview-content2').append(
                    '<div class="preview-title">用户输入：</div><div><div class="query">' + formData.query + '</div>' +
                    '</div>'
                )

                $('#query2').val('')
                $('#loadingOverlay').css('display', 'flex')
                $.ajax({
                    type: 'POST',
                    url: $('#queryForm').attr('action'), // 替换为你的服务器端点
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        conversationId = response.data.conversation_id
                        // 处理响应数据
                        $('#preview-content2').append(
                            '<div class="preview-title">模型回复：</div><div><div class="result">' + response.data.answer + '</div>' +
                            '<hr><div class="usage">' +
                            '<div>耗时：' + response.data.usage.latency + '秒</div>' +
                            '<div>请求token：' + response.data.usage.prompt_tokens + '</div>' +
                            '<div>响应token：' + response.data.usage.completion_tokens + '</div>' +
                            '<div>总token：' + response.data.usage.total_tokens + '</div>' +
                            '</div></div>'
                        );
                        $('#loadingOverlay').css('display', 'none')
                    },
                    error: function(xhr, status, error) {
                        {#// 处理错误情况#}
                        $('#preview-content2').html('Error submitting form.');
                        $('#loadingOverlay').css('display', 'none')
                    }
                });

                $('.rest').on('click', function () {
                    conversationId = null
                    $('#preview-content2').html('')
                })


            })


        })
    </script>
</body>
</html>