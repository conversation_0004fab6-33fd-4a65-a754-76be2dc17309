# Generated by Django 4.2 on 2025-07-08 09:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0057_merge_20250708_1752'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentProofreader',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('file_name', models.CharField(blank=True, max_length=255, verbose_name='文件名')),
                ('original_content', models.TextField(blank=True, verbose_name='原始内容')),
                ('corrected_content', models.TextField(blank=True, verbose_name='校正后内容')),
                ('labeled_content', models.TextField(blank=True, verbose_name='带标记内容')),
                ('status', models.CharField(default='pending', max_length=20, verbose_name='状态')),
                ('task_directory', models.CharField(blank=True, max_length=255, verbose_name='任务目录')),
            ],
            options={
                'verbose_name': '文档校正',
                'verbose_name_plural': '文档校正',
                'db_table': 'ai_document_proofreader',
            },
        ),
        migrations.CreateModel(
            name='ExamAnalysisSubjectCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_code', models.CharField(max_length=10, verbose_name='学科代码')),
                ('subject_name', models.CharField(max_length=20, verbose_name='学科名称')),
                ('enable', models.BooleanField(default=False, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '学科代码对应名称',
                'verbose_name_plural': '学科代码对应名称',
                'db_table': 'ai_exam_analysis_subject_code',
            },
        ),
        migrations.CreateModel(
            name='GenerateQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(db_index=True, max_length=36, verbose_name='学科id')),
                ('main_subject', models.CharField(max_length=36, null=True, verbose_name='学科名称')),
                ('question', models.TextField(null=True, verbose_name='题干')),
                ('options', models.TextField(null=True, verbose_name='选项')),
                ('answer', models.CharField(max_length=36, null=True, verbose_name='答案')),
                ('analysis', models.TextField(null=True, verbose_name='解析')),
                ('knowledge', models.CharField(max_length=36, null=True, verbose_name='知识点')),
                ('error_point', models.TextField(null=True, verbose_name='易错点')),
                ('course_section_id', models.CharField(max_length=36, null=True, verbose_name='课节id')),
                ('chapter', models.CharField(max_length=36, null=True, verbose_name='课节名称')),
            ],
            options={
                'verbose_name': '知识点出题',
                'verbose_name_plural': '知识点出题',
                'db_table': 'ai_generate_question',
            },
        ),
        migrations.CreateModel(
            name='keyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('keyword_no', models.CharField(max_length=255, unique=True)),
                ('keyword', models.TextField(help_text='关键词名称', verbose_name='关键词名称')),
                ('keyword_content', models.TextField(help_text='触发关键词使用的具体内容', verbose_name='关键词内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '助手请求记录',
                'verbose_name_plural': '助手请求记录',
                'db_table': 'ai_dayiapp_keywords',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KnowledgePointPsychology',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('lession_name', models.CharField(db_index=True, max_length=36, verbose_name='课节名称')),
                ('knowledge_point', models.JSONField(verbose_name='知识点')),
            ],
            options={
                'verbose_name': '学科出题',
                'verbose_name_plural': '学科出题',
                'db_table': 'ai_knowledge_psychology',
            },
        ),
        migrations.CreateModel(
            name='KnowledgeSimple',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('course_id', models.CharField(db_index=True, max_length=40, verbose_name='课程ID')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='名称')),
                ('definition', models.TextField(null=True, verbose_name='定义')),
                ('desc', models.TextField(null=True, verbose_name='描述')),
                ('tokens', models.IntegerField(default=0, verbose_name='token数')),
                ('is_usable', models.BooleanField(default=False, verbose_name='是否可用')),
            ],
            options={
                'verbose_name': '知识解析',
                'verbose_name_plural': '知识解析',
                'db_table': 'ai_knowledge_simple',
            },
        ),
        migrations.CreateModel(
            name='ProofreadingDictionary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('word', models.CharField(db_index=True, max_length=255, verbose_name='词语')),
                ('category', models.CharField(default='专有名词', help_text='词语类别，如：专有名词、技术术语、品牌名等', max_length=50, verbose_name='类别')),
                ('description', models.TextField(blank=True, help_text='词语描述或使用说明', verbose_name='描述')),
                ('usage_count', models.IntegerField(default=0, help_text='该词被添加到词库的次数', verbose_name='使用次数')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '校对词库',
                'verbose_name_plural': '校对词库',
                'db_table': 'ai_document_proofreading_dictionary',
                'ordering': ['-add_time'],
            },
        ),
        migrations.CreateModel(
            name='SuperviseLearnStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(db_index=True, max_length=100, verbose_name='用户ID')),
                ('course_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='课程ID')),
                ('subject_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='科目ID')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='开始日期')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='结束日期')),
                ('task_id', models.CharField(blank=True, default=None, max_length=36, null=True, unique=True, verbose_name='任务id')),
                ('status', models.CharField(blank=True, max_length=100, null=True, verbose_name='解析状态')),
                ('fail_reason', models.TextField(null=True, verbose_name='失败原因')),
                ('query', models.TextField(null=True, verbose_name='请求内容')),
                ('analysis', models.TextField(null=True, verbose_name='报告内容')),
            ],
            options={
                'verbose_name': '教务督学记录',
                'verbose_name_plural': '教务督学记录',
                'db_table': 'ai_student_supervise_learn_stat',
            },
        ),
        migrations.AddField(
            model_name='knowledgestore',
            name='like_count',
            field=models.IntegerField(default=0, verbose_name='点赞次数'),
        ),
        migrations.AddField(
            model_name='knowledgestore',
            name='search_count',
            field=models.IntegerField(default=0, verbose_name='搜索次数'),
        ),
        migrations.AddField(
            model_name='message',
            name='image_text',
            field=models.TextField(null=True, verbose_name='图片内容'),
        ),
        migrations.AlterField(
            model_name='appmodelconfig',
            name='model_id',
            field=models.CharField(blank=True, max_length=64, verbose_name='模型名称'),
        ),
        migrations.AlterField(
            model_name='categorycorrelation',
            name='level',
            field=models.IntegerField(choices=[(0, '0'), (1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5'), (6, '6'), (7, '7'), (8, '8'), (9, '9'), (10, '10')], verbose_name='关联度等级'),
        ),
        migrations.AlterField(
            model_name='conversation',
            name='model_id',
            field=models.CharField(blank=True, max_length=64, verbose_name='模型名称'),
        ),
        migrations.AlterField(
            model_name='hotarticlesource',
            name='status',
            field=models.CharField(choices=[('not_start', '未同步任务'), ('no_content', '已同步，内容未完成'), ('success', '已同步，内容已完成'), ('fail', '同步失败')], default='not_start', max_length=32, verbose_name='状态'),
        ),
        migrations.AlterField(
            model_name='majorcorrelation',
            name='level',
            field=models.IntegerField(choices=[(0, '0'), (1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5'), (6, '6'), (7, '7'), (8, '8'), (9, '9'), (10, '10')], verbose_name='关联度等级'),
        ),
        migrations.AlterField(
            model_name='message',
            name='model_id',
            field=models.CharField(blank=True, max_length=64, verbose_name='模型名称'),
        ),
        migrations.AlterField(
            model_name='messagetracing',
            name='model_id',
            field=models.CharField(blank=True, max_length=64, verbose_name='模型名称'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='model_id',
            field=models.CharField(blank=True, max_length=64, verbose_name='模型名称'),
        ),
        migrations.CreateModel(
            name='ExamAnalysisPrompt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('prompt', models.TextField(null=True, verbose_name='提示词')),
                ('subject_code', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.examanalysissubjectcode')),
            ],
            options={
                'verbose_name': '各学科提示词',
                'verbose_name_plural': '各学科提示词',
                'db_table': 'ai_exam_analysis_prompt',
            },
        ),
        migrations.CreateModel(
            name='ExamAnalysisKnowledgePointAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('year', models.CharField(max_length=4, verbose_name='年份')),
                ('subject', models.CharField(max_length=100, verbose_name='学科')),
                ('point_name', models.CharField(max_length=200, verbose_name='知识点名称')),
                ('question_count', models.IntegerField(verbose_name='考查题目数')),
                ('question_difficulty', models.JSONField(verbose_name='题号难度')),
                ('subject_code', models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.examanalysissubjectcode')),
            ],
            options={
                'verbose_name': '知识点分析',
                'verbose_name_plural': '知识点分析',
                'db_table': 'ai_exam_analysis_knowledge_point_analysis',
            },
        ),
        migrations.CreateModel(
            name='ExamAnalysisExamQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('year', models.CharField(max_length=4, verbose_name='年份')),
                ('question_number', models.IntegerField(verbose_name='题号')),
                ('content', models.TextField(null=True, verbose_name='题干')),
                ('score', models.DecimalField(decimal_places=1, max_digits=3, verbose_name='分值')),
                ('question_type', models.CharField(max_length=50, verbose_name='题型')),
                ('subject', models.CharField(max_length=50, verbose_name='学科')),
                ('knowledge_points', models.JSONField(default=list, verbose_name='知识点')),
                ('difficulty', models.IntegerField(verbose_name='难度')),
                ('subject_code', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.examanalysissubjectcode')),
            ],
            options={
                'verbose_name': '考试题目',
                'verbose_name_plural': '考试题目',
                'db_table': 'ai_exam_analysis_exam_question',
            },
        ),
        migrations.CreateModel(
            name='DocumentProofreaderError',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('error_id', models.CharField(max_length=50, verbose_name='错误ID')),
                ('error_text', models.TextField(verbose_name='错误文本')),
                ('error_reason', models.TextField(verbose_name='错误理由')),
                ('error_suggestion', models.TextField(verbose_name='修改建议')),
                ('proofreader', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.documentproofreader', verbose_name='所属校正任务')),
            ],
            options={
                'verbose_name': '文档校正错误',
                'verbose_name_plural': '文档校正错误',
                'db_table': 'ai_document_proofreader_error',
            },
        ),
        migrations.CreateModel(
            name='DocumentProofreaderAsyncTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('task_id', models.CharField(db_index=True, max_length=100, unique=True, verbose_name='任务ID')),
                ('file_name', models.CharField(max_length=255, verbose_name='文件名')),
                ('original_content', models.TextField(blank=True, verbose_name='原始内容')),
                ('status', models.CharField(choices=[('pending', '未开始'), ('processing', '校对中'), ('success', '成功'), ('failed', '失败')], default='pending', max_length=20, verbose_name='任务状态')),
                ('fail_reason', models.TextField(blank=True, verbose_name='失败原因')),
                ('retry_times', models.IntegerField(default=0, verbose_name='重试次数')),
                ('user_session', models.CharField(blank=True, help_text='用于桌面通知', max_length=100, verbose_name='用户会话')),
                ('notification_email', models.EmailField(blank=True, help_text='任务完成后发送邮件通知', max_length=254, verbose_name='通知邮箱')),
                ('notification_sent', models.BooleanField(default=False, verbose_name='通知已发送')),
                ('proofreader', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='app.documentproofreader', verbose_name='关联的校对记录')),
            ],
            options={
                'verbose_name': '异步文档校正任务',
                'verbose_name_plural': '异步文档校正任务',
                'db_table': 'ai_document_proofreader_async_task',
                'ordering': ['-add_time'],
            },
        ),
        migrations.CreateModel(
            name='ExamAnalysisKnowledgePointWithStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject', models.CharField(max_length=100, verbose_name='学科')),
                ('point_name', models.CharField(max_length=200, verbose_name='知识点名称')),
                ('exam_count', models.IntegerField(default=0, verbose_name='考查次数')),
                ('avg_difficulty', models.FloatField(default=0.0, verbose_name='平均难度')),
                ('choice_count', models.IntegerField(default=0, verbose_name='选择题数量')),
                ('choice_avg_difficulty', models.FloatField(default=0.0, verbose_name='选择题平均难度')),
                ('comprehensive_count', models.IntegerField(default=0, verbose_name='综合题数量')),
                ('comprehensive_avg_difficulty', models.FloatField(default=0.0, verbose_name='综合题平均难度')),
                ('subject_code', models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.examanalysissubjectcode')),
            ],
            options={
                'verbose_name': '知识点统计',
                'verbose_name_plural': '知识点统计',
                'db_table': 'ai_exam_analysis_knowledge_point_with_stats',
                'unique_together': {('subject', 'point_name')},
            },
        ),
        migrations.CreateModel(
            name='ExamAnalysisKnowledgePoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject', models.CharField(max_length=100, verbose_name='学科')),
                ('point_name', models.CharField(max_length=200, verbose_name='知识点名称')),
                ('subject_code', models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.examanalysissubjectcode')),
            ],
            options={
                'verbose_name': '知识点',
                'verbose_name_plural': '知识点',
                'db_table': 'ai_exam_analysis_knowledge_point',
                'unique_together': {('subject', 'point_name')},
            },
        ),
    ]
