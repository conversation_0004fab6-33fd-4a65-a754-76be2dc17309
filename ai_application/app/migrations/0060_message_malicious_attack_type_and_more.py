# Generated by Django 4.2 on 2025-07-14 09:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0059_documentproofreadererror_error_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='malicious_attack_type',
            field=models.IntegerField(blank=True, choices=[(0, '0'), (1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5'), (6, '6'), (7, '7'), (8, '8'), (9, '9'), (10, '10')], null=True, verbose_name='恶意攻击类型'),
        ),
        migrations.AddField(
            model_name='message',
            name='malicious_attack_type_display',
            field=models.TextField(blank=True, null=True, verbose_name='恶意攻击类型描述'),
        ),
        migrations.CreateModel(
            name='KnowledgeVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('video', models.CharField(blank=True, max_length=200, verbose_name='视频链接')),
                ('image', models.CharField(blank=True, max_length=200, verbose_name='图片链接')),
                ('name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.knowledgesimple', verbose_name='对应知识点')),
            ],
            options={
                'verbose_name': '知识解析视频',
                'verbose_name_plural': '知识解析视频',
                'db_table': 'ai_knowledge_video',
            },
        ),
    ]
