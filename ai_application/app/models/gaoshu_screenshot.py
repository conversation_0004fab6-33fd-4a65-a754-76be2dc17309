from django.db import models

from ._base import BaseModel

from django.db import models


class GaoshuUserQuestionRecord(BaseModel):
    message = models.ForeignKey('app.Message', on_delete=models.CASCADE, db_constraint=False)
    user_id = models.CharField(max_length=255)
    course_section_id = models.CharField(max_length=255, blank=True)
    question = models.TextField(null=True, blank=True)
    pic_url = models.TextField(null=True, blank=True)
    knowledge_list = models.JSONField(null=True)

    class Meta:
        verbose_name = verbose_name_plural = '高数截屏答疑用户记录'
        db_table = 'ai_gaoshu_screenshot_user_record'
