# Generated by Django 4.2 on 2025-05-14 06:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0055_studentlearnstat_rec_days'),
    ]

    operations = [
        migrations.CreateModel(
            name='GraduateCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(max_length=100, verbose_name='研究生一级学科名称')),
            ],
            options={
                'verbose_name': '研究生一级学科',
                'verbose_name_plural': '研究生一级学科',
                'db_table': 'ai_graduate_category',
            },
        ),
        migrations.CreateModel(
            name='GraduateMajor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(max_length=100, verbose_name='研究生二级门类名称')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='majors', to='app.graduatecategory', verbose_name='所属一级学科')),
            ],
            options={
                'verbose_name': '研究生二级门类',
                'verbose_name_plural': '研究生二级门类',
                'db_table': 'ai_graduate_major',
            },
        ),
        migrations.CreateModel(
            name='UndergraduateCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(max_length=100, verbose_name='本科一级学科名称')),
            ],
            options={
                'verbose_name': '本科一级学科',
                'verbose_name_plural': '本科一级学科',
                'db_table': 'ai_undergraduate_category',
            },
        ),
        migrations.CreateModel(
            name='UndergraduateMajor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(max_length=100, verbose_name='本科二级门类名称')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='majors', to='app.undergraduatecategory', verbose_name='所属一级学科')),
            ],
            options={
                'verbose_name': '本科二级门类',
                'verbose_name_plural': '本科二级门类',
                'db_table': 'ai_undergraduate_major',
            },
        ),
        migrations.CreateModel(
            name='MajorCorrelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('level', models.IntegerField(choices=[(1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5'), (6, '6'), (7, '7'), (8, '8'), (9, '9'), (10, '10')], verbose_name='关联度等级')),
                ('grad_major', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='undergrad_correlations', to='app.graduatemajor', verbose_name='研究生二级门类')),
                ('undergrad_major', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grad_correlations', to='app.undergraduatemajor', verbose_name='本科二级门类')),
            ],
            options={
                'verbose_name': '二级门类关联度',
                'verbose_name_plural': '二级门类关联度',
                'db_table': 'ai_major_correlation',
                'unique_together': {('undergrad_major', 'grad_major')},
            },
        ),
        migrations.CreateModel(
            name='CategoryCorrelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('level', models.IntegerField(choices=[(1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5'), (6, '6'), (7, '7'), (8, '8'), (9, '9'), (10, '10')], verbose_name='关联度等级')),
                ('grad_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='undergrad_correlations', to='app.graduatecategory', verbose_name='研究生一级学科')),
                ('undergrad_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grad_correlations', to='app.undergraduatecategory', verbose_name='本科一级学科')),
            ],
            options={
                'verbose_name': '一级学科关联度',
                'verbose_name_plural': '一级学科关联度',
                'db_table': 'ai_category_correlation',
                'unique_together': {('undergrad_category', 'grad_category')},
            },
        ),
    ]
