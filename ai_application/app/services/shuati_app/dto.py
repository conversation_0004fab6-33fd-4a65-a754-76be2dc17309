from pydantic import Field

from django_ext.base_dto_model import MyBaseModel


class KnowledgeMasteryLevel(MyBaseModel):
    knowledge_name: str
    # 正确率保持整数
    accuracy: int


class QuestionCondition(MyBaseModel):
    question_type: str
    difficulty: int
    knowledge: str
    question_count: int


class PaperDistribution(MyBaseModel):
    rules: list[QuestionCondition] = Field(default=[])


# 问题分值
class QuestionScoreRule(MyBaseModel):
    question_type: int
    difficulty: int
    score: float


# 摸底测单项分值要求
class ModiQuestionRequired(MyBaseModel):
    question_type: int
    difficulty: int
    score_threshold: float


# 摸底测升级要求
class ModiUpRequired(MyBaseModel):
    # 考察轮次
    rounds_required: int = 0
    # 摸底轮总分
    total_score_ratio: float
    # 单项题型难度要求
    question_required: list[ModiQuestionRequired]


class UpgradeKnowledgeRequired(MyBaseModel):
    question_type: int
    difficulty: int
    # 已练知识点数量，为0表示没有要求
    num_rate: float = 0


class UpgradeQuestionRequired(MyBaseModel):
    # 问题类型 为-1表示没有限制
    question_type: int
    # 问题难度 为-1表示没有限制
    difficulty: int
    # 已练题目数量，为0表示没有要求
    num_rate: float = 0
    # 已练题目正确率，为0表示没有要求
    # 为float是 >= accuracy，为tuple是 >= accuracy[0] and < accuracy[1]
    accuracy: float | tuple = 0


# 晋级要求
class UpgradeRequired(MyBaseModel):
    # 考察轮次，为0则是全部，否则为前几轮，用于总题量统计要求
    rounds_required: int = 0
    # 该阶段需要考察的轮数，降级后使用，至少需要几轮，用于计算该阶段的正确率
    stage_rounds_required: int = 0
    # ⚠️已废弃。是否每一轮都检验，配合rounds_required使用，检查轮次
    check_every_round: bool = False
    knowledge_required: list[UpgradeKnowledgeRequired] = Field(default=[])
    question_required: list[UpgradeQuestionRequired] = Field(default=[])


class LearningStageRequired(MyBaseModel):
    # 升级要求
    up_required: UpgradeRequired | None = None
    # 再次升级要求
    up_again_required: UpgradeRequired | None = None
    # 强化阶段要求
    strengthen_required: UpgradeRequired | None = None
    # 强化阶段内升级降级标准
    strengthen_round_required: UpgradeRequired | None = None
    # 降级要求
    down_required: UpgradeRequired | None = None


# 基础阶段 晋级要求
class FoundationUpRequired(MyBaseModel):
    knowledge_required: list[UpgradeKnowledgeRequired]
    question_required: list[UpgradeQuestionRequired]


# 能力提示-初 晋级要求
class BasicImproveUpRequired(MyBaseModel):
    knowledge_required: list[UpgradeKnowledgeRequired]
    question_required: list[UpgradeQuestionRequired]


# 出题策略
class QuestionSelectStrategy(MyBaseModel):
    # 选择题题量
    choice_question_num: int
    # 主观题题量
    subjective_question_num: int
    # 试卷中知识点总数
    total_knowledge_num: int
    # 错误知识点占比
    wrong_knowledge_percent: float = 0
    # 区分高频知识点百分比阈值
    high_freq_knowledge_threshold: float
    # 高频知识点占比
    high_freq_knowledge_percent: float = 1
    # 题型难度分布
    question_type_distribution: dict[int, dict] = Field(default={})
    # 选择题难度分布
    # choice_question_distribution: dict = Field(default={})
    # 主观题难度分布
    # subjective_question_distribution: dict = Field(default={})
