--
-- <PERSON>reate model CozeWorkflow
--
CREATE TABLE `ai_coze_workflow`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `workflow_id`   varchar(100) NOT NULL,
    `code`          varchar(100) NOT NULL,
    `name`          varchar(100) NOT NULL
);
--
-- Create model CozeWorkflowResult
--
CREATE TABLE `ai_coze_workflow_result`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `workflow_id`   varchar(100) NOT NULL,
    `parameters`    json NULL,
    `execute_id`    varchar(100) NOT NULL,
    `err_log_id`    varchar(100) NOT NULL,
    `output_str`    longtext NULL,
    `status`        varchar(16)  NOT NULL,
    `ext_params`    json NULL
);
--
-- <PERSON>reate model EnglishWordTestStrategy
--
CREATE TABLE `ai_data_english_word_test_strategy`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool NOT NULL,
    `origin_content`   longtext NULL,
    `strategy_content` longtext NULL
);
--
-- Create model EnglishWordTestQuestion
--
CREATE TABLE `ai_data_english_word_test_question`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool NOT NULL,
    `question`      longtext NULL,
    `answer`        longtext NULL,
    `strategy_id`   bigint NULL
);
CREATE INDEX `ai_coze_workflow_add_time_31e05804` ON `ai_coze_workflow` (`add_time`);
CREATE INDEX `ai_coze_workflow_workflow_id_24d4527f` ON `ai_coze_workflow` (`workflow_id`);
CREATE INDEX `ai_coze_workflow_result_add_time_35aefb79` ON `ai_coze_workflow_result` (`add_time`);
CREATE INDEX `ai_coze_workflow_result_workflow_id_2a0b568f` ON `ai_coze_workflow_result` (`workflow_id`);
CREATE INDEX `ai_coze_workflow_result_execute_id_e2b49d12` ON `ai_coze_workflow_result` (`execute_id`);
CREATE INDEX `ai_data_english_word_test_strategy_add_time_e40474fb` ON `ai_data_english_word_test_strategy` (`add_time`);
CREATE INDEX `ai_data_english_word_test_question_add_time_c568e22c` ON `ai_data_english_word_test_question` (`add_time`);
CREATE INDEX `ai_data_english_word_test_question_strategy_id_6c2d8aaf` ON `ai_data_english_word_test_question` (`strategy_id`);