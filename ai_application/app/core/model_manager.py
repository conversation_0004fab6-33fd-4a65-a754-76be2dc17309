import time
from typing import Generator, cast

from app.api.invoke_llm import invoke_remote_llm
from app.core.entities.provider_entities import ProviderModelBundle
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.model_runtime.entities.text_embedding_entities import TextEmbeddingResult
from app.core.model_runtime.model_providers._base.large_language_model import LargeLanguageModel
from app.core.model_runtime.model_providers._base.text_embedding_model import TextEmbeddingModel
from app.core.prompt.entities import PromptMessage


class ModelInstance:
    def __init__(self, provider_model_bundle: ProviderModelBundle, model: str):
        self.provider_model_bundle = provider_model_bundle
        self.model = model
        self.provider = provider_model_bundle.provider_instance.provider_schema.provider
        self.model_type_instance = self.provider_model_bundle.model_type_instance

    def invoke_llm(
            self,
            prompt_messages: list[PromptMessage],
            model_parameters: dict | None = None,
            tools: list[str] | None = None,
            stream: bool = True,
    ) -> LLMResult | Generator:
        if not isinstance(self.model_type_instance, LargeLanguageModel):
            raise Exception("Model type instance is not LargeLanguageModel")

        self.model_type_instance = cast(LargeLanguageModel, self.model_type_instance)

        return self.model_type_instance.invoke(
            model=self.model,
            prompt_messages=prompt_messages,
            model_parameters=model_parameters,
            tools=tools,
            stream=stream,
        )

    def get_llm_num_tokens(self, prompt_messages: list[PromptMessage]) -> int:
        if not isinstance(self.model_type_instance, LargeLanguageModel):
            raise Exception("Model type instance is not LargeLanguageModel")

        self.model_type_instance = cast(LargeLanguageModel, self.model_type_instance)
        return self.model_type_instance.get_num_tokens(
            model=self.model,
            prompt_messages=prompt_messages,
        )

    def invoke_text_embedding(self, texts: list[str]) -> TextEmbeddingResult:
        if not isinstance(self.model_type_instance, TextEmbeddingModel):
            raise Exception("Model type instance is not TextEmbeddingModel")

        self.model_type_instance = cast(TextEmbeddingModel, self.model_type_instance)
        return self.model_type_instance.invoke(model=self.model, texts=texts)
