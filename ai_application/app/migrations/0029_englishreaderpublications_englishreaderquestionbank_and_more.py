# Generated by Django 4.2 on 2025-04-03 05:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0028_merge_20250319_1736'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnglishReaderPublications',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('publications_content', models.TextField(null=True, verbose_name='外刊内容')),
                ('article_translate', models.TextField(null=True, verbose_name='外刊翻译')),
                ('publications_name', models.CharField(max_length=32, null=True, verbose_name='外刊名称')),
                ('publications_source', models.Char<PERSON>ield(max_length=32, null=True, verbose_name='外刊来源')),
            ],
            options={
                'verbose_name': '外刊阅读_外刊材料',
                'verbose_name_plural': '外刊阅读_外刊材料',
                'db_table': 'ai_english_reader_publications',
            },
        ),
        migrations.CreateModel(
            name='EnglishReaderQuestionBank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('strategy_id', models.TextField(blank=True, null=True, verbose_name='策略id')),
                ('publication_id', models.TextField(blank=True, null=True, verbose_name='外刊id')),
                ('article', models.TextField(null=True, verbose_name='阅读文章')),
                ('option', models.TextField(null=True, verbose_name='选项')),
                ('answer', models.TextField(null=True, verbose_name='答案')),
                ('analysis', models.TextField(null=True, verbose_name='解析')),
            ],
            options={
                'verbose_name': '外刊阅读_题库',
                'verbose_name_plural': '外刊阅读_题库',
                'db_table': 'ai_english_reader_question_bank',
            },
        ),
        migrations.CreateModel(
            name='EnglishReaderStrategy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('strategy_content', models.TextField(null=True, verbose_name='策略内容')),
                ('strategy_name', models.CharField(max_length=32, null=True, verbose_name='策略名称')),
                ('strategy_year', models.CharField(max_length=32, null=True, verbose_name='年份')),
            ],
            options={
                'verbose_name': '外刊阅读_真题策略',
                'verbose_name_plural': '外刊阅读_真题策略',
                'db_table': 'ai_english_reader_strategy',
            },
        ),
        migrations.CreateModel(
            name='EnglishWaikanQuestionBank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('article', models.TextField(null=True, verbose_name='阅读文章')),
                ('publication', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishreaderpublications')),
                ('strategy', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishreaderstrategy')),
            ],
            options={
                'verbose_name': '外刊阅读_新题库',
                'verbose_name_plural': '外刊阅读_新题库',
                'db_table': 'ai_english_waikan_question_bank',
            },
        ),
        migrations.CreateModel(
            name='UserQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.TextField(null=True, verbose_name='用户id')),
                ('question_id', models.JSONField(null=True, verbose_name='已做过题列表')),
            ],
            options={
                'verbose_name': '用户信息记录',
                'verbose_name_plural': '用户信息记录',
                'db_table': 'ai_english_waikan_user_question',
            },
        ),
        migrations.AlterField(
            model_name='message',
            name='message_type',
            field=models.CharField(choices=[('normal', '普通消息'), ('question', '解题助手'), ('waikan', '外刊出题'), ('waikan_again', '外刊出题-薄弱题型专项练习'), ('code', '代码优化'), ('math_question', '数学解题助手'), ('grammar', '英语长难句语法分析')], default='normal', max_length=32, verbose_name='消息类型'),
        ),
        migrations.CreateModel(
            name='EnglishWaikanTestRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.TextField(null=True, verbose_name='用户id')),
                ('sub_question_ids', models.JSONField(null=True, verbose_name='已经做过的子题列表')),
                ('question_feature', models.CharField(blank=True, max_length=16, verbose_name='子题性质')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwaikanquestionbank')),
            ],
            options={
                'verbose_name': '用户信息记录',
                'verbose_name_plural': '用户信息记录',
                'db_table': 'ai_english_waikan_test_record',
            },
        ),
        migrations.CreateModel(
            name='EnglishWaikanSubQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_stem', models.TextField(null=True, verbose_name='子题内容')),
                ('question_options', models.JSONField(null=True, verbose_name='子题选项')),
                ('question_answer', models.CharField(blank=True, max_length=8, verbose_name='答案')),
                ('question_feature', models.CharField(blank=True, max_length=16, verbose_name='题目性质')),
                ('question_type', models.JSONField(null=True, verbose_name='题目类型')),
                ('question_analysis', models.TextField(null=True, verbose_name='题目解析')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwaikanquestionbank')),
            ],
            options={
                'verbose_name': '外刊阅读_新题库',
                'verbose_name_plural': '外刊阅读_新题库',
                'db_table': 'ai_english_waikan_sub_question',
            },
        ),
    ]
