# Generated by Django 4.2 on 2025-02-10 13:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0021_merge_20250210_1054'),
    ]

    operations = [
        migrations.CreateModel(
            name='CourseNoteTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('course_id', models.CharField(db_index=True, max_length=40, verbose_name='课程ID')),
                ('chapter_id', models.Char<PERSON>ield(db_index=True, max_length=40, verbose_name='章ID')),
                ('chapter_name', models.CharField(blank=True, max_length=100, verbose_name='章名称')),
                ('chapter_lecture', models.TextField(null=True, verbose_name='章讲义')),
                ('document_nos', models.JSONField(null=True, verbose_name='document_nos')),
                ('status', models.CharField(default='not_start', max_length=20, verbose_name='状态')),
                ('processing_started_at', models.DateTimeField(null=True, verbose_name='处理时间')),
                ('completed_at', models.DateTimeField(null=True, verbose_name='完成时间')),
            ],
            options={
                'verbose_name': '课程笔记任务',
                'verbose_name_plural': '课程笔记任务',
                'db_table': 'ai_course_note_task',
            },
        ),
        migrations.CreateModel(
            name='CourseNoteTaskDebug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('course_id', models.CharField(db_index=True, max_length=40, verbose_name='课程ID')),
                ('status', models.CharField(default='not_start', max_length=20, verbose_name='状态')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='名称')),
                ('processing_started_at', models.DateTimeField(null=True, verbose_name='处理时间')),
                ('completed_at', models.DateTimeField(null=True, verbose_name='完成时间')),
            ],
            options={
                'verbose_name': '课程笔记任务debug',
                'verbose_name_plural': '课程笔记任务debug',
                'db_table': 'ai_course_note_task_debug',
            },
        ),
        migrations.CreateModel(
            name='CourseNoteTaskChangeDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('document_no', models.CharField(db_index=True, max_length=40, verbose_name='文档号')),
                ('video_lecture', models.TextField(null=True, verbose_name='讲义')),
                ('video_note', models.TextField(null=True, verbose_name='笔记')),
                ('status', models.CharField(default='not_start', max_length=20, verbose_name='状态')),
                ('processing_started_at', models.DateTimeField(null=True, verbose_name='处理时间')),
                ('completed_at', models.DateTimeField(null=True, verbose_name='完成时间')),
                ('message', models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
                ('task', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.coursenotetask')),
                ('video_content', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.coursevideocontent')),
            ],
            options={
                'verbose_name': '课程笔记任务变更详情',
                'verbose_name_plural': '课程笔记任务变更详情',
                'db_table': 'ai_course_note_task_change_detail',
            },
        ),
        migrations.CreateModel(
            name='CourseNoteTaskChange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('change_content', models.TextField(null=True, verbose_name='变更内容')),
                ('task', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.coursenotetask')),
            ],
            options={
                'verbose_name': '课程笔记任务变更',
                'verbose_name_plural': '课程笔记任务变更',
                'db_table': 'ai_course_note_task_change',
            },
        ),
        migrations.AddField(
            model_name='coursenotetask',
            name='task_debug',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.coursenotetaskdebug'),
        ),
        migrations.CreateModel(
            name='CourseNoteContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('course_id', models.CharField(db_index=True, max_length=40, verbose_name='课程ID')),
                ('chapter_id', models.CharField(db_index=True, max_length=40, verbose_name='章ID')),
                ('chapter_name', models.CharField(blank=True, max_length=100, verbose_name='章名称')),
                ('document_no', models.CharField(db_index=True, max_length=40, verbose_name='文档号')),
                ('video_lecture', models.TextField(null=True, verbose_name='讲义')),
                ('video_note', models.TextField(null=True, verbose_name='笔记')),
                ('video_content', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.coursevideocontent')),
            ],
            options={
                'verbose_name': '课程笔记内容',
                'verbose_name_plural': '课程笔记内容',
                'db_table': 'ai_course_note_content',
            },
        ),
    ]
