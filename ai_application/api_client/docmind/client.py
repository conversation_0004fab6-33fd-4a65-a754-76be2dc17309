import logging

from api_client.base_client import BaseClient
from api_client.decorators import catch_error

logger = logging.getLogger(__name__)

__all__ = ['docmind_client']


class DocmindClient(BaseClient):
    host = 'http://docmind-svc:8000'
    host_local = 'https://docmind.kaoyan-vip.cn'

    @property
    def base_url(self):
        return f'{self.host}/internal_api'

    def upload_file(self, file_name, file_url) -> dict:
        url = '/upload_file'
        return self._post(url, data={
            'file_name': file_name,
            'file_url': file_url,
        })

    def query_file_status(self, file_id) -> dict:
        url = f'/file/{file_id}/status'
        return self._get(url)


docmind_client = DocmindClient()
