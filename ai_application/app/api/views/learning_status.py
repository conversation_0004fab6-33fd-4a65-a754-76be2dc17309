import uuid

from django.http import JsonResponse
from datetime import datetime, date

from app.models import PersonalizedExamSyllabus
from django_ext import base_view
from django_ext.exceptions import InternalException
from django_ext.response import make_response, make_stream_response
from app.models.main_subject import StudentLearnStat, SuperviseLearnStat,SuperviseLearnStageStat, SuperViseInitStudentStatus
from app.services.exercises_learn_status_service import ExercisesLearnStatusService
from app.services.supervise_learn_status_service import SuperviseLearnStatusService
from app.services.supervise_init_status_service import SuperviseInitStatusService
from app.services.Personalized_Exam_Syllabus import generate_personal_kaogang
from app.api.dto import (
    ExercisesLearnStatusDto, 
    SuperviseLearnStatusDto,
    SupervisedLearnUserInitStatus,
    SupervisedLearnUserStageStatus
)
from app.api.validators import ExercisesLearnStatusValidator, SuperviseLearnStatusValidator, \
    SupervisedLearnUserInitStatusValidator, PersonalizedExamSyllabusValidator
from app.tasks import auto_check_supervise_learn_stat_task
from app.tasks import auto_check_supervise_init_status
import json
import re

class StudentLearnAnalysisView(base_view.BaseView):
    """学习状态分析视图"""
    
    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        rec_days = request.data.get('rec_days',7)
        
        task_id = str(uuid.uuid4())  # 生成唯一任务ID
        StudentLearnStat.objects.create(
            user_id=user_id,
            rec_days=rec_days,
            task_id=task_id,  # 存储任务ID
            status="NOT_START"  # 未开始分析
        )

        return make_response({
            'task_id': task_id
        })


class StudentLearnAnalysisStatusView(base_view.BaseView):
    """学习状态分析结果查询视图"""
    
    def get(self, request, *args, **kwargs):
        task_id = request.query_params.get('task_id')
        learn_stat = StudentLearnStat.objects.filter(
            task_id=task_id
        ).first()
        
        if not learn_stat:
            raise InternalException(code=50000, detail='该任务不存在')
            
        if learn_stat.status == 'SUCCESS':
            return make_response({
                'status': learn_stat.status,
                'data': {
                    'analysis': learn_stat.analysis
                }
            })
            
        if learn_stat.status == 'FAIL':
            return make_response({
                'status': learn_stat.status,
                'fail_reason': learn_stat.fail_reason
            })
            
        return make_response({
            'status': 'ING'
        })


class ExercisesLearnStatusView(base_view.BaseView):
    """动手学课后练习学情报告查看视图"""
    validator_class = ExercisesLearnStatusValidator

    def post(self, request, *args, **kwargs):
        dto: ExercisesLearnStatusDto = self.validate_request(request.data)
        response = ExercisesLearnStatusService.gen_analysis_report(dto, request.user)
        return make_response(response)

class SuperviseLearnStatusView(base_view.BaseView):
    """教务督学学情报告提交视图"""
    validator_class = SuperviseLearnStatusValidator

    def post(self, request, *args, **kwargs):
        dto: SuperviseLearnStatusDto = self.validate_request(request.data)
        task_id = str(uuid.uuid4())  # 生成唯一任务ID
        # 保存信息
        new_message = SuperviseLearnStat.objects.create(
            user_id=dto.user_id,
            course_id = dto.course_id,
            subject_id = dto.subject_id,
            start_date = dto.start_date,
            end_date = dto.end_date,
            task_id=task_id,  # 存储任务ID
            status="NOT_START"  # 未开始分析
        )
        new_message.save()
        # auto_check_supervise_learn_stat_task.auto_check_supervise_learn_stat_gen()
        return make_response({
            'task_id': task_id
        })
        # return make_response(response)

class SuperviseLearnStatusCheckView(base_view.BaseView):
    """教务督学学情报告查看视图"""

    def get(self, request, *args, **kwargs):
        task_id = request.query_params.get('task_id')
        supervise_learn_stat = SuperviseLearnStat.objects.filter(
            task_id=task_id
        ).first()
        if not supervise_learn_stat:
            raise InternalException(code=50000, detail='该任务不存在')
            
        if supervise_learn_stat.status == 'SUCCESS':
            analysis_str = supervise_learn_stat.analysis
            # 简单处理，将单引号替换为双引号
            analysis_str = analysis_str.replace("'", "\"")
            analysis_data = json.loads(analysis_str)
            # 合并 lessons_content 和 tests_content 的内容
            report_content = analysis_data.get('lessons_content', '') + analysis_data.get('tests_content', '')
            # 分割报告内容
            sections = self.split_report(report_content)
            return make_response({
                'status': supervise_learn_stat.status,
                'data': {
                    'analysis': sections
                }
            })
            
        if supervise_learn_stat.status == 'FAIL':
            return make_response({
                'status': supervise_learn_stat.status,
                'fail_reason': supervise_learn_stat.fail_reason
            })
            
        return make_response({
            'status': 'ING'
        })
    
    def split_report(self, report_content):
        """
        按中文序号标题分割报告内容，并将内容存储在对应的英文单词命名的键下
        """
        # 中文数字转英文单词的映射
        chinese_to_english = {
            '一': 'watch_course', '二': 'learning_behavior', '三': 'test_completion', '四': 'learning_effect', '五': 'wrong_questions',
            '六': 'suggestions'
        }
        # 定义正则表达式匹配中文序号标题
        pattern = re.compile(r'###\s+([一二三四五六七八九十]+)、(.*?)\n', re.DOTALL)
        matches = pattern.findall(report_content)
        
        sections = {}
        for chinese_num, title in matches:
            english_key = chinese_to_english.get(chinese_num)
            if english_key:
                # 提取标题对应的内容
                content_start = report_content.find(f"### {chinese_num}、{title}") + len(f"### {chinese_num}、{title}")
                next_section_start = report_content.find('###', content_start)
                if next_section_start == -1:
                    content = report_content[content_start:]
                else:
                    content = report_content[content_start:next_section_start]
                sections[english_key] = {
                    "title": title.strip(),
                    "content": content.strip()
                }
        return sections

class SuperviseLearnStageView(base_view.BaseView):
    """教务督学学习阶段视图"""

    
    def post(self, request, *args, **kwargs):
        """提交学习阶段分析任务"""
        dto: SupervisedLearnUserStageStatus = self.validate_request(request.data)        
        task_id = str(uuid.uuid4())

        SuperviseLearnStageStat.objects.create(
            user_id=dto.user_id,
            course_id=dto.course_id,
            subject_id=dto.subject_id,
            start_date=dto.start_date,
            end_date=dto.end_date,
            task_id=task_id,
            status="NOT_START"
        )
        
        return make_response({
            'task_id': task_id
        })
    
    def get(self, request, *args, **kwargs):
        task_id = request.query_params.get('task_id')
        supervise_stage_learn_stat = SuperviseLearnStageStat.objects.filter(
            task_id=task_id
        ).first()
        if not supervise_stage_learn_stat:
            raise InternalException(code=50000, detail='该任务不存在')
            
        if supervise_stage_learn_stat.status == 'SUCCESS':
            analysis_str = supervise_stage_learn_stat.analysis
            # 简单处理，将单引号替换为双引号
            analysis_str = analysis_str.replace("'", "\"")
            analysis_data = json.loads(analysis_str)
            # 合并 eng,pol,pro 和 math 的内容
            report_content = analysis_data.get('eng_content', '') + analysis_data.get('pro_content', '') + analysis_data.get('pol_content', '') + analysis_data.get('math_content', '')
            # 分割报告内容
            sections = self.split_report(report_content)
            return make_response({
                'status': supervise_stage_learn_stat.status,
                'data': {
                    'analysis': sections
                }
            })
            
        if supervise_stage_learn_stat.status == 'FAIL':
            return make_response({
                'status': supervise_stage_learn_stat.status,
                'fail_reason': supervise_stage_learn_stat.fail_reason
            })
            
        return make_response({
            'status': 'ING'
        })

class SuperviseInitStatusView(base_view.BaseView):
    """教务督学初始化学情状态视图"""

    english_level = ["四级","六级","专四","专八","托雅","其他"]
    math_subjects = ["高等数学","线性代数","概率统计"]
    education_level = ["本科","专科","自考本"]
    study_status = ["在校生","已毕业"]
    graduation_years = ["1年","2-3年","4-5年","6年及以上"]
    study_stage = ["大一","大二","大三","大四","大五"]
    mastery = ["优秀","良好","一般","较差"]
    academic_performance = ["前10%","前30%","前50%","其他"]


    validator_class = SupervisedLearnUserInitStatusValidator
    def post(self, request, *args, **kwargs):
        """提交初始化状态分析任务"""
        dto: SupervisedLearnUserInitStatus = self.validate_request(request.data)        
        task_id = str(uuid.uuid4())
                
        new_record = SuperViseInitStudentStatus.objects.create(
            user_id=dto.user_id,
            date = dto.start_date if dto.start_date else date.today().isoformat(),
            exam_date = dto.exam_date if dto.exam_date else "2026-12-26",

            # Graduation Info
            graduation_major = dto.graduation_info.get("graduation_major") if dto.graduation_info else None,
            graduation_major_code = dto.graduation_info.get("graduation_major_code") if dto.graduation_info else None,
            graduation_school = dto.graduation_info.get("graduation_school") if dto.graduation_info else None,
            graduation_school_code = dto.graduation_info.get("graduation_school_code") if dto.graduation_info else None,

            target = dto.target if dto.target else {},
            education_level = self.education_level[dto.education_level],
            study_status = self.study_status[dto.study_status] if dto.study_status < len(self.study_status) else None,

            graduation_years = self.graduation_years[dto.graduation_years] if dto.graduation_years  else None,

            study_stage = self.study_stage[dto.study_stage] if dto.study_stage is not None else None,

            academic_performance = self.academic_performance[dto.academic_performance] if dto.academic_performance is not None and dto.academic_performance < len(self.academic_performance) else None,
            english_level = self.english_level[dto.english_level] if dto.english_level is not None and dto.english_level < len(self.english_level) else None,
            math_subjects = [self.math_subjects[i] for i in dto.math_subjects if i < len(self.math_subjects)],
            math_mastery = self.mastery[dto.math_mastery] if dto.math_mastery else None,

            task_id=task_id,
            status="NOT_START"
        )

        # Trigger the background task to process the analysis
        auto_check_supervise_init_status.check_supervise_init_stat_gen(new_record)

        return make_response({
            'task_id': task_id
        })


class SuperviseInitStatusCheckView(base_view.BaseView):
    """教务督学初始化状态检查视图1"""
    
    def get(self, request, *args, **kwargs):
        task_id = request.query_params.get('task_id')
        supervise_init_learn_stat = SuperViseInitStudentStatus.objects.filter(
            task_id=task_id
        ).first()
        if not supervise_init_learn_stat:
            raise InternalException(code=50000, detail='该任务不存在')
            
        if supervise_init_learn_stat.status == 'SUCCESS':
            analysis_str = supervise_init_learn_stat.analysis
            # 简单处理，将单引号替换为双引号
            analysis_str = analysis_str.replace("'", "\"")
            # 添加JSON解析异常处理
            try:
                analysis_data = analysis_str
                print(f"analysis_data:", analysis_data)
            except json.JSONDecodeError as e:
                return JsonResponse({
                    'message': f'数据解析失败: {str(e)}'
                }, status=500)

            return make_response({
                'status': supervise_init_learn_stat.status,
                'analysis': analysis_data
                
            })
        elif supervise_init_learn_stat.status == 'ING':
            return make_response({
                'status': 'ING',
                'message': '任务处理中，请稍后再试'
            })
        elif supervise_init_learn_stat.status == 'FAIL':
            return make_response({
                'status': 'FAIL',
                'fail_reason': supervise_init_learn_stat.fail_reason,
            })
        else:
            return make_response({
                'status': 'UNKNOWN',
                'message': '未知状态'
            }, status=500)


class PersonalizedExamSyllabusView(base_view.BaseView):
    """个性化考纲生成视图"""
    validator_class = PersonalizedExamSyllabusValidator
    def post(self, request, *args, **kwargs):
        """提交个性化考纲生成任务"""

        target_score = request.data.get('target_score')
        subject = request.data.get('subject')
        if not target_score:
            raise InternalException(code=400, detail='缺少target_score参数')

        
        # 生成个性化考纲
        try:
            target_score_record = PersonalizedExamSyllabus.objects.filter(student_score = target_score,subject = subject).first()
            if target_score_record.exam_syllabus:
                result = target_score_record.exam_syllabus
            else:
                result = generate_personal_kaogang(target_score)
            # result = "hello world"

            
            # 检查结果是否为空
            if result is None:
                print("❌生成结果为None")
                return make_response({
                    'status': 'FAIL',
                    'detail': '个性化考纲生成失败，返回结果为空'
                })

            # 检查结果是否为空字符串
            if isinstance(result, str) and not result.strip():
                print("❌生成结果为空字符串")
                return make_response({
                    'status': 'FAIL',
                    'detail': '个性化考纲生成失败，返回结果为空字符串'
                })

            if result:
                # 确保返回的数据是字符串类型
                result_str = str(result) if not isinstance(result, str) else result
                
                response_data = {
                    'status': 'SUCCESS',
                    'analysis': result_str
                }
                print(f"✅准备返回的数据大小: {len(result_str)} 字符")
                return make_response(response_data)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"❌生成考纲时发生错误: {error_details}")
            return make_response({
                'status': 'ERROR',
                'detail': f'生成过程中出现错误: {str(e)}'
            })

