import json
import time
import uuid
from typing import Generator
from django.conf import settings
from app.core.entities.coze_workflow_entites import (
    CozeWorkflowMessageEvent, CozeWorkflowMessageErrorEvent, CozeWorkflowMessageEndEvent
)
from app.models import Conversation, Account, App, Message
from app.services.coze_workflow_service import CozeWorkflowService


class ScreenshotQuestionService:

    @classmethod
    def create_message(cls, app_model, from_account: Account, userinfo, params) -> Message:
        conversation = Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app=app_model,
            app_model_config=app_model.app_model_config,
            name='New conversation',
            from_account=from_account,
            from_biz_id='',
        )
        # file_objs = []
        # if 'pic' in params:
        #     file_objs.append(params['pic'])

        return Message.objects.create(
            message_no=str(uuid.uuid4()),
            conversation=conversation,
            app=app_model,
            app_model_config=app_model.app_model_config,
            query=json.dumps(params, ensure_ascii=False),
            from_account=from_account,

            userinfo=userinfo,
        )

    @classmethod
    def submit_question(
            cls,
            message: Message,
            params
    ) -> Generator:

        ext_params = {}
        code = 'knowledge_gaoshu_00'
        judge_value = params.get("judge", "default_value")
        if judge_value == "01":
            code = 'knowledge_gaoshu_01'
        elif judge_value == "10":
            code = 'knowledge_gaoshu_10'
        elif judge_value == "11":
            code = 'knowledge_gaoshu_11'

        conversation = message.conversation
        res = CozeWorkflowService.create_workflow_runs_stream(code, params, ext_params)

        for e in res:
            if isinstance(e, CozeWorkflowMessageEvent):
                chunk = {
                    "event": "message",
                    "created_at": int(time.time()),
                    "answer": e.content,
                    "conversation_id": conversation.conversation_no,
                    "message_id": message.message_no,
                }
                chunk_str = json.dumps(chunk, ensure_ascii=False)

                yield f'data: {chunk_str}\n\n'
            elif isinstance(e, CozeWorkflowMessageEndEvent):
                chunk = {
                    "event": "message_end",
                    "created_at": int(time.time()),
                    "metadata": {
                        "message_tokens": 0,
                        "answer_tokens": 0,
                        "total_tokens": 0,
                    },
                    "conversation_id": conversation.conversation_no,
                    "message_id": message.message_no
                }
                chunk_str = json.dumps(chunk, ensure_ascii=False)
                keywords = params.get("keywords", "")
                if keywords:
                    e.answer += f"\n\n{keywords}"

                message.answer = e.answer
                message.status = 'normal'
                message.response_latency = e.latency
                message.save()

                yield f'data: {chunk_str}\n\n'
            elif isinstance(e, CozeWorkflowMessageErrorEvent):
                chunk = {
                    "event": "error",
                    "created_at": int(time.time()),
                    "answer": settings.LLM_NETWORK_ERROR_ANSWER,
                    "conversation_id": conversation.conversation_no,
                    "message_id": message.message_no
                }
                chunk_str = json.dumps(chunk, ensure_ascii=False)

                message.answer = settings.LLM_NETWORK_ERROR_ANSWER
                message.error = e.error_message
                message.status = 'error'
                message.response_latency = e.latency
                message.save()

                yield f'data: {chunk_str}\n\n'


        # 在所有流式数据发送完毕后，发送keywords
        keywords = params.get("keywords", "")
        if keywords:
            keywords_chunk = {
                "event": "message",
                "created_at": int(time.time()),
                "answer": keywords,
                "conversation_id": conversation.conversation_no,
                "message_id": message.message_no,
            }
            keywords_chunk_str = json.dumps(keywords_chunk, ensure_ascii=False)
            yield f'data: {keywords_chunk_str}\n\n'