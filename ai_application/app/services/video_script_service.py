import logging

from app.api.dto import ChatMessageDto
from app.models import Account, InvokeFrom
from app.services.app_generate_service import AppGenerateService

logger = logging.getLogger(__name__)


class VideoScriptService:

    @classmethod
    def get_content(cls, params: dict, account: Account, invoke_from: str = InvokeFrom.api.value):
        query = cls.comb_prompt_str(params)
        dto = ChatMessageDto(
            app_id='video_script',
            inputs={"max_tokens": 1500, "temperature": 0.3},
            query=query,
            stream=False,
        )
        try:
            response = AppGenerateService.generate(dto, account, invoke_from=invoke_from)
            return {'content': response.get('answer', ''), 'type': 'normal'}
        except Exception as e:
            logger.exception(e)
            return {'content': str(e), 'type': 'exception'}

    @classmethod
    def comb_prompt_str(cls, prompt_dict: dict) -> str:
        prompt_str_arr = []

        subject_part = []
        if prompt_dict.get('subject'):
            subject_part.append(f"## 主题\n{prompt_dict.get('subject')}")
        if prompt_dict.get('argument'):
            subject_part.append(f"## 内容论点\n{prompt_dict.get('argument')}")
        if prompt_dict.get('example'):
            subject_part.append(f"## 示例\n{prompt_dict.get('example')}")
        if subject_part:
            subject_part_str = '\n'.join(subject_part)
            prompt_str_arr.append(f"# 任务主题\n{subject_part_str}")

        output_part = []
        if prompt_dict.get('char_len'):
            output_part.append(f"## 字符数\n{prompt_dict.get('char_len')}")
        if prompt_dict.get('style'):
            output_part.append(f"## 风格语调\n{prompt_dict.get('style')}")
        if prompt_dict.get('target'):
            output_part.append(f"## 目标受众\n{prompt_dict.get('target')}")
        if output_part:
            output_part_str = '\n'.join(output_part)
            prompt_str_arr.append(f"# 输出要求\n{output_part_str}")

        return '\n\n'.join(prompt_str_arr)
