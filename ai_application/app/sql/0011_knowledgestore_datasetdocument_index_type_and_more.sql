--
-- Create model KnowledgeStore
--
CREATE TABLE `ai_knowledge_store`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `course_id`     varchar(40)  NOT NULL,
    `name`          varchar(100) NOT NULL,
    `definition`    longtext NULL,
    `desc`          longtext NULL,
    `is_usable`     bool         NOT NULL
);
--
-- Add field index_type to datasetdocument
--
ALTER TABLE `ai_dataset_document`
    ADD COLUMN `index_type` varchar(40) DEFAULT 'text_model' NOT NULL;
--
-- Add field original_knowledge_document_no to datasetdocument
--
ALTER TABLE `ai_dataset_document`
    ADD COLUMN `original_knowledge_document_no` varchar(36) DEFAULT '' NOT NULL;
--
-- Add field model_id to messagetracing
--
ALTER TABLE `ai_message_tracing`
    ADD COLUMN `model_id` varchar(32) DEFAULT '' NOT NULL;
--
-- Add field model_provider to messagetracing
--
ALTER TABLE `ai_message_tracing`
    ADD COLUMN `model_provider` varchar(32) DEFAULT '' NOT NULL;
--
-- Add field model_id to prompttemplate
--
ALTER TABLE `ai_prompt_template`
    ADD COLUMN `model_id` varchar(32) DEFAULT '' NOT NULL;
--
-- Add field model_provider to prompttemplate
--
ALTER TABLE `ai_prompt_template`
    ADD COLUMN `model_provider` varchar(32) DEFAULT '' NOT NULL;
--
-- Alter field prompt_type on appmodelconfig
--
-- (no-op)
--
-- Alter field dataset_process_rule on datasetdocument
--
ALTER TABLE `ai_dataset_document` MODIFY `dataset_process_rule_id` bigint NULL;
--
-- Create model Knowledge
--
CREATE TABLE `ai_knowledge`
(
    `id`                  bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`            datetime(6) NOT NULL,
    `modified_time`       datetime(6) NOT NULL,
    `is_deleted`          bool         NOT NULL,
    `name`                varchar(100) NOT NULL,
    `definition`          longtext NULL,
    `index_node_id`       varchar(255) NOT NULL,
    `dataset_id`          bigint       NOT NULL,
    `dataset_document_id` bigint       NOT NULL
);
CREATE INDEX `ai_knowledge_store_add_time_bfa703fc` ON `ai_knowledge_store` (`add_time`);
CREATE INDEX `ai_knowledge_store_course_id_30bc58f3` ON `ai_knowledge_store` (`course_id`);
CREATE INDEX `ai_knowledge_add_time_f4822e8b` ON `ai_knowledge` (`add_time`);
CREATE INDEX `ai_knowledge_dataset_id_ea3b5b1c` ON `ai_knowledge` (`dataset_id`);
CREATE INDEX `ai_knowledge_dataset_document_id_bf40eebe` ON `ai_knowledge` (`dataset_document_id`);