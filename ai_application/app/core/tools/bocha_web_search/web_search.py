import json
import logging

import requests
from django.conf import settings

logger = logging.getLogger(__name__)


def test_get_current_weather(location, unit=""):
    # 这里可以替换为实际的天气查询逻辑，例如调用天气 API
    if location == "北京":
        return f"{location}今天天气晴，温度 24~30°C"
    elif location == "上海":
        return f"{location}今天天气多云，温度 20~25°C"
    else:
        return f"{location}的天气未知"


def bocha_web_search(query, summary=True, count=1, page=1):
    """
    博查搜索API
    count : 返回结果的条数（实际返回结果数量可能会小于count指定的数量）。
            - 可填范围：1-50，最大单次搜索返回50条
            - 默认为10
    Page : 页码，默认值为 1
    """
    url = "https://api.bochaai.com/v1/web-search"
    payload = json.dumps({
        "query": query,
        "summary": summary,
        "count": count,
        "page": page
    })
    headers = {
        'Authorization': f'Bearer {settings.BOCHA_API_KEY}',
        'Content-Type': 'application/json'
    }

    try:
        response = requests.post(url, headers=headers, data=payload, timeout=10)
        res = response.json()  # JSON 解析
        search_res = res.get('data')
        res_list = search_res.get('webPages', {}).get('value', [])
        data = []
        for idx, item in enumerate(res_list, start=1):
            data.append(f"搜索结果{idx}:\n{item['summary']}")
        return '\n\n'.join(data)
    except requests.exceptions.RequestException as e:
        logger.exception(e)
    except Exception as e2:
        logger.exception(e2)
    return ''
