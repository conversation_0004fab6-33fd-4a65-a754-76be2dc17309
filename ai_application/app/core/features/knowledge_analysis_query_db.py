import json
import random
import time

from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.features.base_feature import BaseFeature
from app.core.rag.index_processor.constant.index_type import IndexType
from app.models import DatasetDocument, Knowledge


class KnowledgeQueryDBFeature(BaseFeature):

    def run(
            self,
            query: str,
            keywords: list,
            original_knowledge_document_no: str,
            is_recommend: bool = False,
            tracing_log: MessageTracingLog | None = None
    ) -> dict:
        if not keywords:
            return {}

        start_time = time.perf_counter()
        kg_document = DatasetDocument.objects.filter(
            index_type=IndexType.KNOWLEDGE_INDEX.value,
            original_knowledge_document_no=original_knowledge_document_no,
            is_deleted=False
        ).first()

        exist_kgs_map = {}
        
        exist_kg_names = []
        for k in keywords:
            # 暂时最多查出25个相关的知识点
            kgs = Knowledge.objects.filter(dataset_document=kg_document, name__icontains=k)[:25]
            if kgs:
                # 知识点名称去重复
                this_kg = None
                other_kgs = []

                all_kgs = []
                for kg in kgs:
                    if kg.name in exist_kg_names:
                        continue
                    exist_kg_names.append(kg.name)
                    all_kgs.append(kg)
                for kg in all_kgs:
                    if kg.name in query:
                        this_kg = kg
                    else:
                        other_kgs.append(kg)
                if this_kg is None and other_kgs:
                    this_kg = other_kgs.pop(0)

                other_kg_names = [kg.name for kg in other_kgs]
                exist_kgs_map[k] = {
                    'name': this_kg.name,
                    'definition': this_kg.definition,
                    # 推荐的知识点最多取3个
                    'others': random.sample(other_kg_names, 3) if len(other_kg_names) > 3 else other_kg_names,
                } if this_kg else None

        latency = time.perf_counter() - start_time
        if tracing_log:
            tracing_log.add_tracing(
                tracing_type='knowledge_query_db',
                query=json.dumps(keywords, ensure_ascii=False),
                answer=json.dumps(exist_kgs_map, ensure_ascii=False),
                latency=latency,
            )
        return exist_kgs_map
