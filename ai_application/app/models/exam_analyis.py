from django.db import models
from ._base import BaseModel


class ExamAnalysisSubjectCode(BaseModel):
    subject_code = models.CharField(max_length=10, verbose_name="学科代码")
    subject_name = models.CharField(max_length=20, verbose_name="学科名称")
    enable = models.BooleanField(default=False, verbose_name="是否启用")

    class Meta:
        verbose_name = verbose_name_plural = "学科代码对应名称"
        db_table = 'ai_exam_analysis_subject_code'


class ExamAnalysisPrompt(BaseModel):
    """
    存储各学科代码的题目分析提示词
    """
    subject_code = models.ForeignKey(ExamAnalysisSubjectCode, on_delete=models.CASCADE, db_constraint=False)
    prompt = models.TextField(null=True, verbose_name="提示词")

    class Meta:
        verbose_name_plural = verbose_name = '各学科提示词'
        db_table = 'ai_exam_analysis_prompt'


class ExamAnalysisExamQuestion(BaseModel):
    """
    考试题目模型
    对应multi_year_analysis.json中的数据结构
    """
    year = models.CharField(max_length=4, verbose_name="年份")
    question_number = models.IntegerField(verbose_name="题号")
    content = models.TextField(null=True, verbose_name="题干")
    score = models.DecimalField(max_digits=3, decimal_places=1, verbose_name="分值")
    question_type = models.CharField(max_length=50, verbose_name="题型")
    subject = models.CharField(max_length=50, verbose_name="学科")
    knowledge_points = models.JSONField(default=list, verbose_name="知识点")  # 存储知识点数组
    difficulty = models.IntegerField(verbose_name="难度")
    subject_code = models.ForeignKey(ExamAnalysisSubjectCode, on_delete=models.CASCADE, db_constraint=False)

    class Meta:
        verbose_name = verbose_name_plural = "考试题目"
        db_table = 'ai_exam_analysis_exam_question'


class ExamAnalysisKnowledgePoint(BaseModel):
    """
    知识点模型
    对应knowledge_points.json中的数据结构
    """
    subject = models.CharField(max_length=100, verbose_name="学科")
    point_name = models.CharField(max_length=200, verbose_name="知识点名称")
    subject_code = models.ForeignKey(ExamAnalysisSubjectCode, on_delete=models.CASCADE, db_constraint=False, null=True)

    class Meta:
        verbose_name = verbose_name_plural = "知识点"
        unique_together = ('subject', 'point_name')
        db_table = 'ai_exam_analysis_knowledge_point'


class ExamAnalysisKnowledgePointAnalysis(BaseModel):
    """
    知识点分析模型
    对应knowledge_points_analysis.json中的数据结构
    """
    year = models.CharField(max_length=4, verbose_name="年份")
    subject = models.CharField(max_length=100, verbose_name="学科")
    point_name = models.CharField(max_length=200, verbose_name="知识点名称")
    question_count = models.IntegerField(verbose_name="考查题目数")
    question_difficulty = models.JSONField(verbose_name="题号难度")  # 存储{"题号":难度}
    subject_code = models.ForeignKey(ExamAnalysisSubjectCode, on_delete=models.CASCADE, db_constraint=False, null=True)

    class Meta:
        verbose_name = verbose_name_plural = "知识点分析"
        db_table = 'ai_exam_analysis_knowledge_point_analysis'


class ExamAnalysisKnowledgePointWithStats(BaseModel):
    """
    知识点统计模型
    对应knowledge_points_with_stats.json中的数据结构
    """
    subject = models.CharField(max_length=100, verbose_name="学科")
    point_name = models.CharField(max_length=200, verbose_name="知识点名称")
    exam_count = models.IntegerField(default=0, verbose_name="考查次数")
    avg_difficulty = models.FloatField(default=0.0, verbose_name="平均难度")
    choice_count = models.IntegerField(default=0, verbose_name="选择题数量")
    choice_avg_difficulty = models.FloatField(default=0.0, verbose_name="选择题平均难度")
    comprehensive_count = models.IntegerField(default=0, verbose_name="综合题数量")
    comprehensive_avg_difficulty = models.FloatField(default=0.0, verbose_name="综合题平均难度")
    priority = models.CharField(max_length=100,verbose_name="优先级", null=True)
    subject_code = models.ForeignKey(ExamAnalysisSubjectCode, on_delete=models.CASCADE, db_constraint=False, null=True)

    class Meta:
        verbose_name = verbose_name_plural = "知识点统计"
        unique_together = ('subject', 'point_name')
        db_table = 'ai_exam_analysis_knowledge_point_with_stats'
