import logging
from typing import Generator

from celery import shared_task
from django.core.cache import cache

from app.constants.app import AppMode
from app.core.entities.app_entities import ModelConfigEntity
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.model_provider_manager import ModelProviderManager
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.model_runtime.entities.provider_entities import ModelType
from app.errors import LLMRequestError
from app.libs.ding_robot import send_dingtalk_message_by_env
from app.models import PromptTemplate

logger = logging.getLogger(__name__)


def _direct_query_llm(
        model_provider,
        model_id,
        model_params: dict,
        pre_prompt,
        tools: list[str] | None = None,
        files: list | None = None,
        stream: bool = False
) -> LLMResult | Generator:
    provider_model_bundle = ModelProviderManager().get_provider_model_bundle(
        provider=model_provider,
        model_type=ModelType.LLM
    )
    model_schema = provider_model_bundle.model_type_instance.get_model_schema(model_id)
    model_conf = ModelConfigEntity(
        provider=model_provider,
        model=model_id,
        model_schema=model_schema,
        provider_model_bundle=provider_model_bundle,
        model_params=model_params,
    )

    llm_model_conf = get_llm_model_config(app_model_conf=model_conf)
    return query_llm_by_prompt(
        model_conf=llm_model_conf,
        app_mode=AppMode.COMPLETION,
        pre_prompt=pre_prompt,
        query='',
        files=files,
        tools=tools,
        stream=stream
    )


@shared_task(queue='message_task')
def auto_check_llm_request_task():
    logger.info('auto_check_llm_request_task begin...')
    res = PromptTemplate.objects.filter(is_deleted=False).values('model_provider', 'model_id').distinct()

    for i in res:
        if not i['model_id'] or i['model_provider'] == 'tongyi':
            continue
        model_provider = i['model_provider']
        model_id = i['model_id']

        model_params = {"max_tokens": 100, "temperature": 0.1}
        cache_key = f'check_llm_request:{model_id}'
        try:
            rs = _direct_query_llm(
                model_provider=model_provider,
                model_id=model_id,
                model_params=model_params,
                pre_prompt='你是谁'
            )
            logger.info(f'auto_check_llm_request_task, model_id:{model_id}, res:{rs.message.content}, usage:{rs.usage}')
            if cache.get(cache_key):
                message = f'【{model_provider}】【{model_id}】接口恢复！'
                send_dingtalk_message_by_env(message, at_all=False)

                cache.delete(cache_key)
        except Exception as e:
            err_msg = str(e)
            if isinstance(e, LLMRequestError):
                err_msg = e.detail_err

            if cache.get(cache_key):
                continue

            cache.set(cache_key, 1, timeout=1800)
            message = f'【{model_provider}】【{model_id}】接口出现异常消息，错误如下：\n{err_msg}\n请及时处理！'
            send_dingtalk_message_by_env(message, at_all=False)
