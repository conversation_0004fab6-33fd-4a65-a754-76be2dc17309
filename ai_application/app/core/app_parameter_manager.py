from app.core.entities.app_entities import AppParameterRule
from app.core.model_provider_manager import ModelProviderManager
from app.core.prompt_form_manager import PromptFormManager


def get_app_other_params() -> list:
    return [
        # {'code': 'rewrite', 'label': '内容再编辑', 'type': 'boolean', 'required': False, 'desc': '针对非对话式应用，所返回的结果在后台可再次编辑'},
        {
            'code': 'prologue',
            'label': '对话开场白',
            'type': 'text_multirow',
            'max_length': 300,
            'required': False,
            'desc': '针对问答式应用，设置对话开场白。',
            'placeholder': '请输入对话开场白，不超过300个字符',
        },
        {
            'code': 'repository',
            'label': '访问本地知识库',
            'type': 'file',
            'required': False,
            'desc': '在匹配模型时上传本地知识库，并基于该知识库问答'
        },
        # {'code': 'file', 'label': '上传文档', 'type': 'file', 'required': False, 'desc': '上传对应资料'},
    ]


class AppParameterManager:

    def get_param_code_list(self) -> list:
        model_params = self.get_model_params()
        prompt_params = self.get_prompt_params()
        other_params = get_app_other_params()
        all_params_code = []
        all_params_code.extend([i.code for i in model_params])
        all_params_code.extend([i.code for i in prompt_params])
        all_params_code.extend([i['code'] for i in other_params])
        return all_params_code

    def get_model_params(self) -> list[AppParameterRule]:
        return ModelProviderManager().get_model_params()

    def get_prompt_params(self) -> list[AppParameterRule]:
        return PromptFormManager().get_prompt_params()

    def get_model_params_dict(self) -> list[dict]:
        return [mp.model_dump() for mp in self.get_model_params()]

    def get_prompt_params_dict(self) -> list[dict]:
        return [pp.model_dump() for pp in self.get_prompt_params()]

    def get_other_params_dict(self) -> list[dict]:
        return get_app_other_params()
