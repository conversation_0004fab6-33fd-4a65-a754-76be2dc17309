import logging
from typing import Generator
import requests
import json
from app.models import Message

logger = logging.getLogger(__name__)


def process_stream_response(response,message):
    full_content = ""
    for line in response.iter_lines():

        try:
            json_line = line.decode('utf-8').strip()
            if json_line.startswith("data:"):
                json_str = json_line[len("data:"):].strip()
                data = json.loads(json_str)
                event_type = data.get("event")
                created_at = data.get("created_at")
                message_id = message.message_no

                if event_type == "message":
                    answer_chunk = data.get("answer", "")
                    full_content += answer_chunk
                    # 返回解析后的消息片段
                    yield f"data:{json.dumps({'event': 'message', 'answer': answer_chunk, 'created_at': created_at, 'message_id': message_id}, ensure_ascii=False)}\n\n"

                elif event_type == "message_end":
                    metadata = data.get("metadata", {})
                    message.message_tokens = metadata.get("message_tokens", 0)
                    message.answer_tokens = metadata.get("answer_tokens", 0)
                    message.total_tokens = metadata.get("total_tokens", 0)
                    message.response_latency = metadata.get("latency", 0)
                    message.status = 'normal'
                    message.answer = full_content
                    message.save()
                    # 返回结束事件
                    yield f"data:{json.dumps({'event': 'message_end', 'metadata': metadata, 'message_id': message_id}, ensure_ascii=False)}\n\n"

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}, 原始数据: {json_str[:100]}...")
            yield {"event": "error", "message": "数据解析错误"}
        except Exception as e:
            logger.error(f"处理流数据时发生未知错误: {e}")
            yield {"event": "error", "message": "处理数据时发生错误"}


def process_direct_response(response,message):
    try:
        llm_response = response.json()
        print(f"llm_response在这:{llm_response}")
        if llm_response.get('status') == 'normal':

            message.answer_tokens = llm_response.get('usage', {}).get('answer_tokens', 0)
            message.total_tokens = llm_response.get('usage', {}).get('total_tokens', 0)
            message.message_tokens = llm_response.get('usage', {}).get('prompt_tokens', 0)
            message.response_latency = llm_response.get('usage', {}).get('latency', 0)
            message.answer = llm_response.get('answer',  {})
            message.status = 'normal'
            message.save()

        else:
            message.answer_tokens = llm_response.get('usage', {}).get('answer_tokens', 0)
            message.total_tokens = llm_response.get('usage', {}).get('total_tokens', 0)
            message.message_tokens = llm_response.get('usage', {}).get('prompt_tokens', 0)
            message.response_latency = llm_response.get('usage', {}).get('latency', 0)
            message.answer = llm_response.get('answer', '')
            message.status = 'error'
            message.error = llm_response.get('error',{}).get('message', '')
            message.save()
        result = llm_response.get('data', {}).get('answer', '')

        return result

    except requests.exceptions.JSONDecodeError:
        raise RuntimeError("API 返回非 JSON 数据，请检查接口是否正常") from None


def invoke_remote_llm(
        payload_data: dict,
        authentication_info: dict,
        stream: bool,
        message: Message | None = None,
)-> Generator[bytes, None, None] | str:

    url = "https://ai-dev.yantucs.com/api/llm_invoke/"

    headers = {
        'x-api-key': authentication_info.get("api_key"),
        'x-signature': authentication_info.get("signature"),
        'timestamp': authentication_info.get("timestamp"),
        'nonce': authentication_info.get("nonce"),
        'User-Agent': 'Apifox/1.0.0',
        'Content-Type': 'application/json',
    }

    response = requests.post(url, headers=headers, data=json.dumps(payload_data),stream=stream)

    if stream:
        return process_stream_response(response, message)

    else:
        return process_direct_response(response, message)







