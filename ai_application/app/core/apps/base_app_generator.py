import json
import uuid
from typing import Generator, cast

from app.constants.app import Conversation<PERSON>tatus, MessageStatus
from app.core.entities.app_entities import (
    ModelConfigEntity, ChatAppGenerateEntity, ChatbotAppStreamResponse,
    CompletionAppGenerateEntity, ChatbotAppBlockingResponse, MessageStreamResponse, ErrorStreamResponse
)
from app.core.model_provider_manager import ModelProviderManager
from app.core.model_runtime.entities.provider_entities import ModelType
from app.core.model_runtime.model_param_validator import ModelParamValidator
from app.core.prompt.params_prompt import comb_prompt_by_params
from app.errors import ConversationNotFoundError, ConversationFinishedError
from app.models import (
    AppModelConfig, App, Conversation, Message, Account, Dataset, RagMessage, PromptTemplate,
    InvokeFrom
)
from app.sensitive_words.utils import contains_sensitive_word


class BaseAppGenerator:
    def _get_app_model_config(
            self,
            app_model: App,
            conversation: Conversation | None = None
    ) -> AppModelConfig:
        if conversation:
            return conversation.app_model_config
        return app_model.app_model_config

    def _get_user_inputs(
            self,
            inputs: dict,
            conversation: Conversation | None = None,
            is_override: bool = False
    ) -> dict:
        if is_override:
            return inputs
        return conversation.inputs if conversation else inputs

    def _get_prompt_template(self, user_inputs: dict, app_model_config: AppModelConfig) -> PromptTemplate | None:
        prompt_template = None
        if app_model_config.prompt_type == AppModelConfig.PromptType.for_template:
            prompt_templates = app_model_config.prompt_template_list
            if not prompt_templates:
                raise ValueError('prompt template is empty')

            if len(prompt_templates) == 1:
                prompt_template_id = prompt_templates[0]
            else:
                prompt_template_id = user_inputs.get('prompt_template')

            prompt_template: PromptTemplate | None = None
            if prompt_template_id:
                prompt_template = PromptTemplate.objects.filter(
                    id=prompt_template_id, is_enabled=True, is_deleted=False
                ).first()
            if not prompt_template:
                raise ValueError('prompt_template is required')

        return prompt_template

    def _get_model_config(
            self,
            user_inputs: dict,
            app_model_config: AppModelConfig,
            conversation: Conversation | None = None,
            is_override: bool = False,
            prompt_template: PromptTemplate | None = None
    ) -> ModelConfigEntity:
        model_provider = app_model_config.model_provider
        model_id = app_model_config.model_id
        model_params = None
        # 优先使用会话的模型参数
        if conversation:
            model_provider = conversation.model_provider
            model_id = conversation.model_id
            model_params = conversation.model_params
        if not model_params or is_override:
            origin_model_params = user_inputs.copy()
            if app_model_config.prompt_type == AppModelConfig.PromptType.for_template:
                if not prompt_template:
                    raise ValueError('prompt_template is required')
                origin_model_params.update(prompt_template.model_params_dict)
            else:
                origin_model_params.update(app_model_config.model_params_dict)

            model_params = ModelParamValidator().validate(origin_model_params, app_model_config)

        provider_model_bundle = ModelProviderManager().get_provider_model_bundle(
            provider=model_provider,
            model_type=ModelType.LLM
        )
        model_schema = provider_model_bundle.model_type_instance.get_model_schema(model_id)
        return ModelConfigEntity(
            provider=model_provider,
            model=model_id,
            model_schema=model_schema,
            provider_model_bundle=provider_model_bundle,
            model_params=model_params,
        )

    def _get_pre_prompt(
            self,
            pre_prompt: str,
            user_inputs: dict,
            app_model_config: AppModelConfig,
            conversation: Conversation | None = None,
            has_dataset: bool = False,
            is_override: bool = False,
            query: str = '',
            prompt_template: PromptTemplate | None = None
    ) -> str:
        if is_override or not conversation:
            return self._get_app_pre_prompt(
                pre_prompt,
                user_inputs,
                app_model_config,
                has_dataset=has_dataset,
                query=query,
                prompt_template=prompt_template,
            )
        else:
            return conversation.pre_prompt

    def _get_app_pre_prompt(
            self,
            pre_prompt: str,
            user_inputs: dict,
            app_model_config: AppModelConfig,
            has_dataset: bool = False,
            query: str = '',
            prompt_template: PromptTemplate | None = None

    ):
        if pre_prompt:
            if has_dataset:
                rag_prompt_template: PromptTemplate = PromptTemplate.objects.filter(
                    app_no='local_knowledge_base',
                    is_enabled=True, is_deleted=False
                ).first()
                if rag_prompt_template:
                    return rag_prompt_template.prompt_content.replace('{{pre_prompt}}', pre_prompt)
            return pre_prompt

        if app_model_config.prompt_type == AppModelConfig.PromptType.for_workflow:
            return ''

        elif app_model_config.prompt_type == AppModelConfig.PromptType.for_template:
            if not prompt_template:
                raise ValueError('prompt_template is required')

            return prompt_template.assemble_prompt(query, user_inputs)
        elif app_model_config.prompt_type == AppModelConfig.PromptType.for_params:
            pre_prompt = comb_prompt_by_params(user_inputs)
            if has_dataset:
                rag_prompt_template: PromptTemplate = PromptTemplate.objects.filter(
                    app_no='local_knowledge_base',
                    is_enabled=True, is_deleted=False
                ).first()
                if rag_prompt_template:
                    return rag_prompt_template.prompt_content.replace('{{pre_prompt}}', pre_prompt)
            return pre_prompt

    def _get_conversation(self, app_model: App, conversation_id: str | None) -> Conversation:
        conversation = Conversation.objects.filter(
            is_deleted=False, app=app_model, conversation_no=conversation_id).first()

        if not conversation:
            raise ConversationNotFoundError()

        if conversation.status != ConversationStatus.NORMAL.value:
            raise ConversationFinishedError()

        return conversation

    def _init_generate_records(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            from_account: Account,
            from_biz_id: str = '',
            userinfo: dict | None = None,
            conversation: Conversation | None = None,
            last_message: Message | None = None
    ) -> tuple[Conversation, Message]:
        model_conf = application_generate_entity.model_conf
        if not conversation:
            conversation = Conversation.objects.create(
                conversation_no=str(uuid.uuid4()),
                app_id=application_generate_entity.app_model_id,
                app_model_config_id=application_generate_entity.app_model_config_id,
                model_provider=model_conf.provider,
                model_id=model_conf.model,
                inputs=application_generate_entity.inputs,
                conversation_hash=application_generate_entity.conversation_hash,
                model_params=model_conf.model_params,
                pre_prompt=application_generate_entity.pre_prompt,
                name='New conversation',
                status=ConversationStatus.NORMAL.value,
                from_account=from_account,
                from_biz_id=from_biz_id,
                invoke_from=application_generate_entity.invoke_from,
            )

        message = Message(
            message_no=str(uuid.uuid4()),
            conversation=conversation,
            app_id=application_generate_entity.app_model_id,
            app_model_config_id=application_generate_entity.app_model_config_id,
            model_provider=model_conf.provider,
            model_id=model_conf.model,
            inputs=application_generate_entity.inputs,
            conversation_hash=application_generate_entity.conversation_hash,
            query=application_generate_entity.query,
            message_type=application_generate_entity.message_type,
            file_objs=application_generate_entity.file_objs,
            from_account=from_account,
            from_biz_id=from_biz_id,
            invoke_from=application_generate_entity.invoke_from,
            userinfo=userinfo,
        )

        # 内部应用暂时不处理敏感词
        if application_generate_entity.app_type not in [
            'article_generation',
            'content_extraction',
            'document_extraction',
            'document_summary',
            'dsx_learning_stat',
            # 字幕提取暂不处理敏感词
            'math_video_abstract',
            'math_video_knowledge_extract',
        ]:
            is_contain, tips, sensitive_word = contains_sensitive_word(
                application_generate_entity.query,
                application_generate_entity.app_model_id
            )
            if is_contain:
                message.is_sensitive = True
                message.answer = tips
                message.sensitive_content = sensitive_word

        if last_message:
            message.replaced_message_no = last_message.message_no

            last_message.status = MessageStatus.REPLACED.value
            last_message.save(update_fields=['status'])

        message.save()

        if conversation.dataset:
            RagMessage.objects.create(
                conversation=conversation,
                message=message,
                is_model_rating=True,
            )

        return conversation, message

    def _generate_conversation_hash(self) -> str:
        return uuid.uuid4().hex

    def _convert_generate_response(
            self,
            response: Generator[ChatbotAppStreamResponse, None, None],
    ) -> Generator[str, None, None] | dict:
        if isinstance(response, ChatbotAppBlockingResponse):
            return self.convert_blocking_full_response(response)
        else:
            def _generate():
                for chunk in self._convert_stream_full_response(response):
                    yield f'data: {chunk}\n\n'

            return _generate()

    def convert_blocking_full_response(self, blocking_response: ChatbotAppBlockingResponse) -> dict:
        return {
            "message_id": blocking_response.data.message_id,
            "conversation_id": blocking_response.data.conversation_id,
            "answer": blocking_response.data.answer,
            "usage": blocking_response.data.usage,
            "is_success": blocking_response.data.is_success,
            "is_sensitive": blocking_response.data.is_sensitive,
        }

    def _convert_stream_full_response(
            self,
            stream_response: Generator[ChatbotAppStreamResponse, None, None]
    ) -> Generator[str, None, None]:
        for chunk in stream_response:
            chunk = cast(ChatbotAppStreamResponse, chunk)

            # if isinstance(chunk.stream_response, MessageStreamResponse):
            if isinstance(chunk.stream_response, MessageStreamResponse):
                workflow_chunk = chunk.stream_response.workflow_chunk
                response_chunk = {
                    'event': chunk.stream_response.event.value,
                    'answer': chunk.stream_response.answer,
                    'created_at': chunk.stream_response.created_at,
                    'id': workflow_chunk.get('id', ''),
                    'pos': workflow_chunk.get('pos', ''),
                    'type': workflow_chunk.get('type', ''),
                    'title': workflow_chunk.get('title', ''),
                }
            elif isinstance(chunk.stream_response, ErrorStreamResponse):
                response_chunk = chunk.stream_response.model_dump()
                response_chunk['type'] = 'exception'
            else:
                response_chunk = chunk.stream_response.model_dump()

            response_chunk.update({
                'event': chunk.stream_response.event.value,
                'conversation_id': chunk.conversation_id,
                'message_id': chunk.message_id,
            })
            yield json.dumps(response_chunk, ensure_ascii=False)

    def _add_conversation(
            self,
            app_model: App,
            app_model_config: AppModelConfig,
            inputs: dict,
            model_conf: ModelConfigEntity,
            pre_prompt: str,
            dataset: Dataset,
            document_nos: list[str],
            from_account: Account,
            from_biz_id: str = '',
            invoke_from: str = InvokeFrom.api.value,
    ) -> Conversation:
        return Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app_id=app_model.id,
            app_model_config_id=app_model_config.id,
            model_provider=app_model_config.model_provider,
            model_id=app_model_config.model_id,
            inputs=inputs,
            model_params=model_conf.model_params,
            pre_prompt=pre_prompt,
            name='New conversation',
            status=ConversationStatus.NORMAL.value,
            dataset=dataset,
            document_nos=document_nos,
            from_account=from_account,
            from_biz_id=from_biz_id,
            invoke_from=invoke_from,
        )

    def _add_conversation_for_chat_app(
            self,
            app_model: App,
            app_model_config: AppModelConfig,
            inputs: dict,
            from_account: Account,
            from_biz_id: str = '',
            invoke_from: str = InvokeFrom.api.value,
    ) -> Conversation:
        return Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app_id=app_model.id,
            app_model_config_id=app_model_config.id,
            model_provider=app_model_config.model_provider,
            model_id=app_model_config.model_id,
            inputs=inputs,
            name='New conversation',
            status=ConversationStatus.NORMAL.value,
            from_account=from_account,
            from_biz_id=from_biz_id,
            invoke_from=invoke_from,
        )
