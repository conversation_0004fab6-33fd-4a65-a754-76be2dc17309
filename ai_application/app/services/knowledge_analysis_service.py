from typing import Generator

from app.api.dto import KnowledgeAnalysisDto, ChatMessageDto
from app.models import Account, InvokeFrom
from app.services.app_generate_service import AppGenerateService

class KnowledgeAnalysisService:
    
    @classmethod
    def query_knowledge(
            cls,
            dto: KnowledgeAnalysisDto,
            account: Account,
            invoke_from: str = InvokeFrom.api.value
    ) -> Generator:
        is_debug = invoke_from == InvokeFrom.console.value

        dto = ChatMessageDto(
            app_id='knowledge_analysis_simple',
            biz_id=dto.biz_id,
            userinfo=dto.userinfo,
            inputs={
                "course_id": dto.biz_id or dto.course_id,
                "document_no": dto.document_no,
                "search_type": dto.search_type,
                "split_prompt": dto.split_prompt,
                "recommend_prompt": dto.recommend_prompt,
                "local_prompt": dto.local_prompt,
                "llm_prompt": dto.llm_prompt,
                "is_stream": dto.is_stream,
            },
            query=dto.query,
            stream=dto.is_stream,
        )
        return AppGenerateService.generate(dto, account, invoke_from=invoke_from)
    
    @classmethod
    def regenerate_knowledge(
            cls,
            dto: KnowledgeAnalysisDto,
            account: Account,
            invoke_from: str = InvokeFrom.api.value
    ) -> Generator:
        dto = ChatMessageDto(
            app_id='knowledge_analysis_simple',
            biz_id=dto.biz_id,
            userinfo=dto.userinfo,
            inputs={
                "course_id": dto.biz_id or dto.course_id,
                "document_no": dto.document_no,
                "search_type": dto.search_type,
                "split_prompt": dto.split_prompt,
                "recommend_prompt": dto.recommend_prompt,
                "local_prompt": dto.local_prompt,
                "llm_prompt": dto.llm_prompt,
                "is_regenerate": True,
            },
            query=dto.query,
            stream=dto.is_stream,
        )
        return AppGenerateService.generate(dto, account, invoke_from=invoke_from)
