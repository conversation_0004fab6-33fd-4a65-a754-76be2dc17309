--
-- Create model PromptTemplate
--
CREATE TABLE `ai_prompt_template`
(
    `id`                   bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`             datetime(6) NOT NULL,
    `modified_time`        datetime(6) NOT NULL,
    `is_deleted`           bool        NOT NULL,
    `name`                 varchar(32) NOT NULL,
    `app_no`               varchar(32) NOT NULL,
    `prompt_content`       longtext    NOT NULL,
    `debug_prompt_content` longtext NULL,
    `is_enabled`           bool        NOT NULL
);
--
-- Add field prompt_type to appmodelconfig
--
ALTER TABLE `ai_app_model_config`
    ADD COLUMN `prompt_type` varchar(32) DEFAULT 'for_params' NOT NULL;
--
-- Add field conversation_hash to conversation
--
ALTER TABLE `ai_conversation`
    ADD COLUMN `conversation_hash` varchar(255) DEFAULT '' NOT NULL;
--
-- Add field conversation_hash to message
--
ALTER TABLE `ai_message`
    ADD COLUMN `conversation_hash` varchar(255) DEFAULT '' NOT NULL;
--
-- Add field inputs to message
--
ALTER TABLE `ai_message`
    ADD COLUMN `inputs` json NULL;
CREATE INDEX `ai_prompt_template_add_time_4d59ebc2` ON `ai_prompt_template` (`add_time`);
CREATE INDEX `ai_prompt_template_app_no_79c9ab93` ON `ai_prompt_template` (`app_no`);