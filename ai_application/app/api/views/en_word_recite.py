import datetime
import random
import threading
from collections import defaultdict

from django.db import transaction

from app.api.serializers import EnPlanRecordSerializer, EnWordSerializer
from app.errors import ParameterError, AppSystemError, PlanNotFoundError
from app.services.english_word_recite import en_word_test_service
from django_ext import base_view
from django_ext.response import make_response
from app.api.dto import (
    WordReciteBasicPaperSubmitDto, WordRecitePostQuestionDto, WordRecitePlanUseDto,
    WordRecitePlanManualChangeDto
)
from app.api.validators import (
    WordReciteBasicPaperSubmitValidator, WordRecitePostQuestionValidator,
    WordRecitePlanUseValidator, WordRecitePlanManualChangeValidator
)
from app.models.en_word_recite import EnWordReciteBasicPaper, EnWordReciteBasicAnswer, EnWordReciteBasicAnswerDetail, \
    EnWordReciteReviewPlan, EnWordReciteReviewRecord
from app.models.en_word_recite import EnWordRecitePlan, EnWordRecitePlanRecord, EnWordReciteDayRecord
from app.models import EnglishWordLibrary, EnWordReciteDayPlan, EnWordReciteQuestion
from app.services.main_subject.en_word_recite_service import EnWordReciteService


class EnglishWordSearchView(base_view.BaseListView):
    queryset = EnglishWordLibrary.objects.filter(is_deleted=False).order_by('id')
    serializer_class = EnWordSerializer
    page = True

    def get_queryset(self):
        word_str = self.request.query_params.get('word')
        if not word_str:
            raise Exception('请输入待搜索的单词')
        return self.queryset.filter(word__icontains=word_str)


class EnglishWordReciteBasicQuestion(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        data = en_word_test_service.gen_basic_test_paper(user_id)
        return make_response(data)


class EnglishWordRecitePostBasicQuestion(base_view.BaseView):
    validator_class = WordReciteBasicPaperSubmitValidator

    @staticmethod
    def _get_right_num(dto, questions) -> int:
        right_num = 0
        for a in dto.answer_detail:
            if question := questions.get(a.question_id):
                if question.answer.upper() == a.user_answer.upper():
                    right_num += 1
        return right_num

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        dto: WordReciteBasicPaperSubmitDto = self.validate_request(request.data)

        basic_paper: EnWordReciteBasicPaper = EnWordReciteBasicPaper.objects.filter(
            is_deleted=False, id=dto.paper_id).first()
        if not basic_paper:
            raise ParameterError(detail='试卷不存在')

        if EnWordReciteBasicAnswer.objects.filter(
            is_deleted=False, user_id=user_id, basic_paper=basic_paper,
        ).exists():
            raise ParameterError(detail='试卷已提交')

        questions = {
            d.question.id: d.question
            for d in basic_paper.enwordrecitebasicpaperdetail_set.filter(is_deleted=False).select_related('question')
        }

        # save answer result
        with transaction.atomic():
            basic_answer = EnWordReciteBasicAnswer.objects.create(
                user_id=user_id,
                basic_paper=basic_paper,
                right_num=self._get_right_num(dto, questions)
            )
            to_update_detail_list = []
            for a in dto.answer_detail:
                if question := questions.get(a.question_id):
                    to_update_detail_list.append(EnWordReciteBasicAnswerDetail(
                        basic_paper=basic_paper,
                        basic_answer=basic_answer,
                        question=question,
                        word_id=question.word_id,
                        user_answer=a.user_answer,
                        is_right=a.user_answer.upper() == question.answer.upper(),
                        is_answered=True,
                    ))
            EnWordReciteBasicAnswerDetail.objects.bulk_create(to_update_detail_list)

        # 摸底测提交完要生成背诵计划
        # delay create recite plan
        if basic_paper.paper_type == EnWordReciteBasicPaper.PaperType.basic_test:
            t = threading.Thread(target=self._create_recite_plan, args=(basic_answer, ))
            t.start()

        return make_response({'answer_id': basic_answer.id})

    @staticmethod
    def _create_recite_plan(basic_answer):
        EnWordReciteService.create_plan(basic_answer)


class EnglishWordReciteWeekTest(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        data = en_word_test_service.gen_week_test_paper(user_id)
        return make_response(data)


class PlanCheckMixin:

    def get_plan_record(self, user_id) -> EnWordRecitePlanRecord:
        basic_answer: EnWordReciteBasicAnswer = EnWordReciteBasicAnswer.objects.filter(
            is_deleted=False, user_id=user_id).last()
        if not basic_answer:
            raise ParameterError(detail='未完成摸底测')

        plan: EnWordRecitePlan = basic_answer.enwordreciteplan_set.filter(is_deleted=False).last()
        if not plan:
            raise ParameterError(detail='无背单词计划')
        plan_record: EnWordRecitePlanRecord = plan.enwordreciteplanrecord_set.filter(is_deleted=False).last()
        if not plan_record:
            raise ParameterError(detail='无背单词计划')
        return plan_record

    def get_curr_plan(self, user_id) -> EnWordRecitePlan:
        basic_answer: EnWordReciteBasicAnswer = EnWordReciteBasicAnswer.objects.filter(
            is_deleted=False, user_id=user_id).last()
        if not basic_answer:
            raise ParameterError(detail='未完成摸底测')

        plan: EnWordRecitePlan = basic_answer.enwordreciteplan_set.filter(is_deleted=False).last()
        if not plan:
            raise ParameterError(detail='无背单词计划')
        return plan

    def check_user_used_plan(self, user_id) -> EnWordRecitePlanRecord:
        plan = self.get_curr_plan(user_id)
        if plan.gen_status != 'SUCCESS':
            raise ParameterError(detail='单词计划生成中')

        used_plan_record: EnWordRecitePlanRecord = plan.enwordreciteplanrecord_set.filter(
            is_deleted=False, use_status=EnWordRecitePlanRecord.UseStatus.used
        ).order_by('-id').first()
        if not used_plan_record:
            raise ParameterError(detail='单词计划生成中')

        return used_plan_record


class EnglishWordRecitePlanManualChange(base_view.BaseView, PlanCheckMixin):
    validator_class = WordRecitePlanManualChangeValidator

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        dto: WordRecitePlanManualChangeDto = self.validate_request(request.data)
        plan = self.get_curr_plan(user_id)

        pre_day_num = dto.low + dto.middle + dto.high
        plan_content = {
            "low": dto.low,
            "middle": dto.middle,
            "high": dto.high,
            "total": pre_day_num,
            "days": 0,  # TODO 计算天数
        }
        with transaction.atomic():
            EnWordRecitePlanRecord.objects.create(
                plan=plan,
                gen_status='SUCCESS',
                use_status=EnWordRecitePlanRecord.UseStatus.used,
                plan_content=plan_content,
                is_manual=True,
            )
            plan.plan_content = plan_content
            plan.last_plan_gen_date = datetime.date.today()
            plan.save()

        return make_response()


class EnglishWordReciteDailyQuestion(base_view.BaseView, PlanCheckMixin):

    @staticmethod
    def _get_recited_words(plan) -> list[int]:
        # 获取已经背诵过的所有单词
        queryset = EnWordReciteDayRecord.objects.filter(
            is_deleted=False, plan=plan
        ).values_list('word', flat=True)
        return list(queryset)

    @staticmethod
    def _get_plan_words():
        # 获取所有题目，后期考虑做缓存
        word_id_list = list(
            EnWordReciteQuestion.objects.filter(is_deleted=False).values_list('word_id', flat=True)
        )

        high, middle, low = [], [], []
        for word in EnglishWordLibrary.objects.filter(is_deleted=False):
            if word.id not in word_id_list:
                continue

            if word.word_freq == EnglishWordLibrary.Freq.high:
                high.append(word.id)
            elif word.word_freq == EnglishWordLibrary.Freq.middle:
                middle.append(word.id)
            elif word.word_freq == EnglishWordLibrary.Freq.low:
                low.append(word.id)

        return {'high': high, 'middle': middle, 'low': low}

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        plan_record = self.check_user_used_plan(user_id)
        plan = plan_record.plan

        today = datetime.date.today()
        # today = today + datetime.timedelta(days=1)

        today_plan = EnWordReciteDayPlan.objects.filter(is_deleted=False, plan=plan, day=today).first()
        if not today_plan:
            all_words = self._get_plan_words()
            recited_words = self._get_recited_words(plan)
            word_id_list = []
            # 单词顺序需要打乱
            for key in ('high', 'middle', 'low'):
                freq_words = all_words.get(key)
                freq_num = plan_record.plan_content.get(key)
                word_list = list(set(freq_words) - set(recited_words))
                freq_num = min(freq_num, len(word_list))
                choose_words = random.sample(word_list, freq_num)
                word_id_list.extend(choose_words)

            # 获取复习题目列表
            review_word_ids = list(set(EnWordReciteReviewPlan.objects.filter(
                is_deleted=False, is_review_continue=True,
                next_review_day__lte=today
            ).values_list('word', flat=True)))

            # create daily plan
            today_plan = EnWordReciteDayPlan.objects.create(
                plan=plan,
                plan_record=plan_record,
                day=today,
                recite_words=word_id_list,
                review_words=review_word_ids,
            )

        # filter today recited word and response
        today_recited_questions = EnWordReciteDayRecord.objects.filter(
            is_deleted=False, plan=plan, day_plan=today_plan,
        )
        recited_question_map = {i.question_id: {
            'user_answer': i.user_answer,
            'is_right': i.is_right,
        } for i in today_recited_questions}
        # 单词背诵出英译中
        question_list = en_word_test_service.get_word_questions(today_plan.recite_words, recited_question_map)

        today_reviewed_questions = EnWordReciteReviewRecord.objects.filter(
            is_deleted=False, plan=plan, day_plan=today_plan,
        )
        reviewed_question_map = {i.question_id: {
            'user_answer': i.user_answer,
            'is_right': i.is_right,
        } for i in today_reviewed_questions}
        review_list = en_word_test_service.get_word_questions(today_plan.review_words, reviewed_question_map)

        return make_response({'question_list': question_list, 'review_list': review_list})


class EnglishWordReciteDailyPostQuestion(base_view.BaseView, PlanCheckMixin):
    validator_class = WordRecitePostQuestionValidator

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()
        dto: WordRecitePostQuestionDto = self.validate_request(request.data)

        plan_record = self.check_user_used_plan(user_id)
        plan = plan_record.plan

        today = datetime.date.today()
        day_plan: EnWordReciteDayPlan = EnWordReciteDayPlan.objects.filter(
            is_deleted=False, plan=plan, day=today,
        ).first()
        if not day_plan:
            raise ParameterError(detail='无背单词计划')

        question: EnWordReciteQuestion = EnWordReciteQuestion.objects.filter(
            is_deleted=False, id=dto.question_id).first()
        if not question:
            raise ParameterError(detail='参数错误')

        if question.word_id not in day_plan.recite_words:
            raise ParameterError(detail='单词不在今日的背诵计划内')

        record: EnWordReciteDayRecord = EnWordReciteDayRecord.objects.filter(
            is_deleted=False,
            day_plan=day_plan,
            question_id=dto.question_id,
        ).select_related('question').first()

        is_right = dto.user_answer == question.answer

        if not record:
            # create record
            EnWordReciteDayRecord.objects.create(
                plan=plan,
                plan_record=plan_record,
                day_plan=day_plan,
                question=question,
                word=question.word,
                day=today,
                user_answer=dto.user_answer,
                is_right=is_right,
            )

            if not is_right:
                # 错题记录到复习列表中
                today = datetime.date.today()
                EnWordReciteReviewPlan.objects.create(
                    word=question.word,
                    first_add_day=today,
                    next_review_day=today + datetime.timedelta(days=1),
                )
        elif not record.is_right:
            record.user_answer = dto.user_answer
            record.is_right = is_right
            record.save(update_fields=['user_answer', 'is_right'])

        return make_response()


class EnglishWordReciteReviewPostQuestion(base_view.BaseView, PlanCheckMixin):
    validator_class = WordRecitePostQuestionValidator

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()
        dto: WordRecitePostQuestionDto = self.validate_request(request.data)

        plan_record = self.check_user_used_plan(user_id)
        plan = plan_record.plan

        today = datetime.date.today()
        # today = today + datetime.timedelta(days=1)
        day_plan: EnWordReciteDayPlan = EnWordReciteDayPlan.objects.filter(
            is_deleted=False, plan=plan, day=today,
        ).first()
        if not day_plan:
            raise ParameterError(detail='无背单词计划')

        question: EnWordReciteQuestion = EnWordReciteQuestion.objects.filter(
            is_deleted=False, id=dto.question_id).first()
        if not question:
            raise ParameterError(detail='问题不存在')

        if question.word_id not in day_plan.review_words:
            raise ParameterError(detail='单词不在今日的复习计划内')

        record: EnWordReciteReviewRecord = EnWordReciteReviewRecord.objects.filter(
            is_deleted=False,
            day_plan=day_plan,
            question_id=dto.question_id,
        ).select_related('question').first()

        is_right = dto.user_answer == question.answer

        if not record:
            # create record
            EnWordReciteReviewRecord.objects.create(
                plan=plan,
                plan_record=plan_record,
                day_plan=day_plan,
                question=question,
                word=question.word,
                review_day=today,
                user_answer=dto.user_answer,
                is_right=is_right,
            )

            review_plan: EnWordReciteReviewPlan = EnWordReciteReviewPlan.objects.filter(
                is_deleted=False, word=question.word).first()
            if review_plan:
                review_plan.review_num += 1
                interval_days = (review_plan.review_num + 1) ** 2 - 1
                review_plan.next_review_day = review_plan.first_add_day + datetime.timedelta(days=interval_days)
                if review_plan.review_num >= 3:
                    review_plan.is_review_continue = False
                review_plan.save()
        elif not record.is_right:
            record.user_answer = dto.user_answer
            record.is_right = is_right
            record.save(update_fields=['user_answer', 'is_right'])

        return make_response()


class EnglishWordReciteChangePlan(base_view.BaseView, PlanCheckMixin):

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        plan_record = self.check_user_used_plan(user_id)

        # delay change recite plan
        t = threading.Thread(target=self._change_recite_plan, args=(plan_record.plan,))
        t.start()

        return make_response()

    @staticmethod
    def _change_recite_plan(plan):
        EnWordReciteService.change_plan(plan)


class EnglishWordReciteCheckPlan(base_view.BaseView, PlanCheckMixin):
    serializer_class = EnPlanRecordSerializer

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        plan_record = self.get_plan_record(user_id)
        data = {'status': plan_record.gen_status, 'plan_info': {}}
        if plan_record.gen_status != 'SUCCESS':
            return make_response(data)

        # success
        plan = self.get_curr_plan(user_id)
        latest_plan_record: EnWordRecitePlanRecord = plan.enwordreciteplanrecord_set.filter(
            is_deleted=False,
            use_status__in=[
                EnWordRecitePlanRecord.UseStatus.init,
                EnWordRecitePlanRecord.UseStatus.used,
            ],
            gen_status='SUCCESS'
        ).order_by('-id').first()
        if not latest_plan_record:
            raise ParameterError(detail='无背单词计划')

        if latest_plan_record.use_status == EnWordRecitePlanRecord.UseStatus.used:
            curr_plan_record = latest_plan_record
        else:
            curr_plan_record = plan.enwordreciteplanrecord_set.filter(
                is_deleted=False,
                use_status=EnWordRecitePlanRecord.UseStatus.used
            ).order_by('-id').first()
            if not curr_plan_record:
                raise ParameterError(detail='无背单词计划')

        # 获取最近7天情况
        today = datetime.date.today()
        last_7_days = [(today - datetime.timedelta(days=i)) for i in range(1, 8)]
        # 该计划近7天的答题记录
        day_qs = EnWordReciteDayRecord.objects.filter(
            plan=plan, plan_record=curr_plan_record, day__in=last_7_days
        )
        day_map = set()
        right_num = 0
        for i in day_qs:
            right_num += 1
            day_map.add(i.day.strftime("%Y-%m-%d"))
        recited_days = len(day_map)

        data['plan_info'].update({
            'last_7_info': {
                'recited_days': recited_days,
                'right_num': right_num,
            },
            'latest_plan': self.serializer_class(latest_plan_record).data,
            'curr_plan': self.serializer_class(curr_plan_record).data,
        })
        return make_response(data)


class EnglishWordRecitePlanUse(base_view.BaseView):
    validator_class = WordRecitePlanUseValidator

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        dto: WordRecitePlanUseDto = self.validate_request(request.data)

        plan_record: EnWordRecitePlanRecord = EnWordRecitePlanRecord.objects.filter(
            id=dto.plan_record_id, plan__user_id=user_id
        ).first()
        if not plan_record:
            raise PlanNotFoundError()

        # 非初始化状态则判断结束
        if plan_record.use_status != EnWordRecitePlanRecord.UseStatus.init:
            return make_response()

        if dto.use_status == EnWordRecitePlanRecord.UseStatus.used:
            with transaction.atomic():
                plan = plan_record.plan
                plan.gen_plan_content = plan_record.gen_plan_content
                plan.plan_content = plan_record.plan_content
                plan.last_plan_gen_date = datetime.date.today()
                plan_record.gen_status = plan_record.gen_status     # 同步更新计划状态
                plan.save()

                plan_record.use_status = EnWordRecitePlanRecord.UseStatus.used
                plan_record.save(update_fields=['use_status'])

        elif dto.use_status == EnWordRecitePlanRecord.UseStatus.refused:
            plan_record.use_status = EnWordRecitePlanRecord.UseStatus.refused
            plan_record.save(update_fields=['use_status'])
        else:
            raise ParameterError()

        return make_response()


