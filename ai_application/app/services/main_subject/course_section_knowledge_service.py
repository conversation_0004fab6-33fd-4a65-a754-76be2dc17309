import json

from app.api.dto import CSVideoKnowledgeExtractDto, ChatMessageDto
from app.models import CourseSectionKnowledge, Account, KnowledgeLibrary
from app.services.app_generate_service import AppGenerateService


class CourseSectionKnowledgeService:

    @classmethod
    def abstract_video(cls, dto: CSVideoKnowledgeExtractDto):
        obj: CourseSectionKnowledge = CourseSectionKnowledge.objects.filter(
            course_section_id=dto.course_section_id
        ).first()
        if obj and obj.subtitles_abstract:
            return obj.subtitles_abstract

        if not obj:
            obj = CourseSectionKnowledge.objects.create(
                course_section_id=dto.course_section_id,
                main_subject=dto.main_subject,
                subtitles=dto.video_content,
            )

        cls._get_video_abstract(obj, dto.main_subject, dto.video_content)
        return obj.subtitles_abstract

    @classmethod
    def extract_video_knowledge(cls, dto: CSVideoKnowledgeExtractDto):
        obj: CourseSectionKnowledge = CourseSectionKnowledge.objects.filter(
            course_section_id=dto.course_section_id
        ).first()
        if obj and obj.is_extract_knowledge:
            return obj.knowledge_list

        if not obj:
            obj = CourseSectionKnowledge.objects.create(
                course_section_id=dto.course_section_id,
                main_subject=dto.main_subject,
                subtitles=dto.video_content,
            )

        if not obj.subtitles_abstract:
            cls._get_video_abstract(obj, dto.main_subject, dto.video_content)

        chat_dto = ChatMessageDto(
            app_id='math_video_knowledge_extract',
            query=obj.subtitles_abstract,
            inputs={
                'main_subject': dto.main_subject,
            },
            stream=False
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account)
        try:
            knowledge_list = json.loads(response.get('answer'))
        except:
            knowledge_list = []

        obj.knowledge_list = knowledge_list
        obj.is_extract_knowledge = True
        obj.save()

        return knowledge_list

    @classmethod
    def _get_video_abstract(cls, obj: CourseSectionKnowledge, main_subject, video_subtitles):
        chat_dto = ChatMessageDto(
            app_id='math_video_abstract',
            query=video_subtitles,
            inputs={
                'main_subject': main_subject,
            },
            stream=False
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account)

        video_abstract = response.get('answer', '')
        obj.subtitles_abstract = video_abstract
        obj.save()
