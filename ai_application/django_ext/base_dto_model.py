import logging

from pydantic import BaseModel
from pydantic_core import ValidationError

logger = logging.getLogger(__name__)


class DtoBuildException(Exception):
    def __init__(self, errors):
        self.errors = errors

    def __repr__(self, *args, **kwargs):
        return str(self.errors)

    def __str__(self, *args, **kwargs):
        return str(self.errors)


class MyBaseModel(BaseModel):
    """
    如需要默认返回值，则需自行try-except处理
    """

    @classmethod
    def build_model(cls, data: dict):
        if not isinstance(data, dict):
            raise DtoBuildException({'type': 'typeErr', 'msg': 'data must be dict', 'input': data})

        try:
            return cls(**data)
        except ValidationError as e:
            logger.exception(e)
            raise DtoBuildException(e.errors())

    @classmethod
    def build_model_list(cls, data: list) -> list:
        if not isinstance(data, list):
            raise DtoBuildException({'type': 'typeErr', 'msg': 'data must be list', 'input': data})

        results = []
        for item in data:
            if isinstance(item, list):
                results.append(cls.build_model_list(item))
            elif isinstance(item, dict):
                results.append(cls.build_model(item))
            else:
                results.append(item)
        return results


def model_list_dump(data: list[BaseModel]) -> list[dict]:
    return [item.model_dump() for item in data]
