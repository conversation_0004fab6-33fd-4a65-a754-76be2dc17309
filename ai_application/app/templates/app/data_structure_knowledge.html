<!DOCTYPE html>
<html>
<head>
    <title>知识点编辑器</title>
    <style>
        .sidebar {
            float: left;
            width: 22%;
            border-right: 1px solid #ccc;
            padding: 10px;
            height: 100vh;
            box-sizing: border-box;
            overflow-y: auto;
        }
        .sidebar-title {
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 18px;
            letter-spacing: 8px;
        }
        .content {
            float: left;
            width: 76%;
            padding: 20px 2%;
            height: 100vh;
            box-sizing: border-box;
            position: relative;
            overflow-y: auto;
        }
        /* 总览统计与卡片 */
        .stats-bar { display:flex; gap:24px; align-items:center; color:#555; margin:6px 0 16px 0; }
        .stat-item { font-size:14px; }
        .overview-rows { display:flex; flex-direction:column; gap:16px; width:100%; }
        .overview-row { display:grid; grid-template-columns: repeat(5, 1fr); gap:16px; width:100%; }
        .overview-card { height:84px; border:1px solid #e3e3e3; border-radius:10px; padding:10px 14px; box-sizing:border-box; background:#fff; box-shadow:0 1px 3px rgba(0,0,0,0.04); position:relative; }
        .overview-card:hover { box-shadow:0 4px 12px rgba(0,0,0,0.07); }
        .overview-title { font-size:14px; color:#333; margin-bottom:8px; }
        .overview-count { font-size:12px; color:#888; }
        .overview-more { position:absolute; right:10px; top:8px; font-size:12px; color:#999; cursor:pointer; }
        .overview-detail { display:none; width:100%; }
        .overview-header { display:flex; align-items:center; justify-content:space-between; padding:6px 0 10px 0; }
        .overview-back { cursor:pointer; color:#333; font-size:14px; display:inline-flex; align-items:center; gap:6px; }
        .overview-back::before { content:'\2039'; font-size:16px; line-height:1; }
        .question-list { margin-top:0; }
        .question-item { border:1px solid #d9e2ef; border-radius:8px; padding:12px 14px; margin-bottom:12px; background:#fff; box-shadow:0 1px 2px rgba(0,0,0,0.02); position:relative; }
        .question-item .overview-more { right:12px; top:10px; }
        .edit-tools { display:none; gap:10px; }
        .edit-checkbox { position:absolute; right:12px; top:10px; width:16px; height:16px; accent-color:#f44336; }
        .danger-btn { background:#f44336; color:#fff; border-color:#f44336; }
        .outline-danger-btn { background:transparent; color:#f44336; border:1px solid #f44336; border-radius:18px; padding:4px 12px; }
        .outline-danger-btn:hover { background:rgba(244,67,54,.06); }
        .question-title { font-size:14px; color:#222; margin-bottom:8px; padding-right:86px; }
        .question-options { font-size:13px; color:#444; line-height:1.9; padding-right:86px; }
        .option { display:block; }
        .option.correct { color:#1d64f2; }
        .q-img { max-width:100%; height:auto; display:block; margin-top:6px; }
        /* Markdown 代码块样式（解析区域与题目内统一风格） */
        .question-item pre { background:#f6f8fa; padding:10px; border-radius:6px; overflow:auto; }
        .question-item code { background:#f6f8fa; padding:2px 4px; border-radius:4px; border:1px solid #c9d1d9; }
        /* 避免围栏代码块内每一行出现分段边框 */
        .question-item pre code { background:transparent; border:none; padding:0; }
        /* 顶部标签样式 */
        .tabs {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e5e5e5;
            padding-bottom: 8px;
            margin-bottom: 16px;
        }
        .tabs-left { display: flex; align-items: center; gap: 16px; }
        .tabs-right { display: flex; align-items: center; gap: 12px; }
        .tab {
            cursor: pointer;
            padding: 6px 10px;
            color: #444;
            border-radius: 6px;
            transition: all 0.2s ease;
            user-select: none;
        }
        .tab:hover { background: #f5f5f5; }
        .tab.active {
            color: #1d64f2;
            background: #e9f1ff;
        }
        .tab-content { width: 100%; }
        .tab-pane { display: none; }
        .tab-pane.active { display: block; }
        /* 知识点&题目详情 网格 */
        .kp-overview-grid { display:grid; grid-template-columns: repeat(4, 1fr); gap:16px; }
        .kp-overview-card { position:relative; border:1px solid #e3e3e3; border-radius:10px; background:#fff; padding:12px 14px; box-shadow:0 1px 3px rgba(0,0,0,0.04); }
        .kp-overview-title { font-size:18px; color:#222; font-weight:400; margin:18px 0 6px 0; text-align:center; }
        .kp-overview-count { font-size:12px; color:#666; margin-bottom:8px; }
        .kp-overview-row { display:flex; justify-content:space-between; align-items:center; margin-top:6px; }
        .kp-overview-row .label { font-size:14px; color:#666; }
        .kp-overview-row .right { display:flex; gap:8px; font-size:14px; color:#555; }
        .kp-overview-row .right span { background:#f7f9fc; border:1px solid #edf1f7; border-radius:6px; padding:2px 6px; }
        .kp-overview-detail-btn { position:absolute; right:12px; top:8px; font-size:12px; color:#999; cursor:pointer; }
        .kp-overview-diff-line { font-size:12px; color:#555; margin-top:4px; }
        .kp-overview-diff-grid { display:grid; grid-template-columns: repeat(5, 1fr); column-gap:12px; row-gap:4px; align-items:center; font-size:12px; color:#555; }
        .kp-overview-diff-grid .cell { text-align:left; white-space:nowrap; }
        .rounded-select { border:1px solid #d0d7de; border-radius:8px; padding:4px 10px; background:#fff; outline:none; height:30px; }
        /* 学科二级标题 */
        .content-header { margin-bottom: 10px; }
        .subject2-title {
            display: inline-block;
            font-size: 24px;
            font-weight: 700;
            padding: 0;
            border: none;
            border-radius: 0;
        }
        /* 知识点工具栏 */
        .kp-toolbar { display: flex; justify-content: flex-end; margin: 10px 0 8px 0; }
        .subject-block {
            margin-bottom: 16px;
        }
        .subject-1-title {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .subject-2-list {
            list-style: none;
            padding-left: 16px;
        }
        .subject-2-item {
            cursor: pointer;
            padding: 4px 8px;
            background: #f5f5f5;
            margin: 4px 0;
            border-radius: 4px;
        }
        .subject-2-item.active {
            background: #d0eaff;
        }
        .chapter-group {
            width: 100%;
            margin-bottom: 30px;
            border: 1px solid #aaa;
            border-radius: 8px;
            padding: 16px;
            position: relative;
            background: #fafbfc;
            box-sizing: border-box;
        }
        .chapter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .chapter-title {
            font-size: 18px;
            font-weight: bold;
        }
        .edit-btn {
            font-size: 14px;
            padding: 2px 10px;
            border: 1px solid #007bff;
            background: #fff;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .edit-btn.edit-mode {
            border-color: red;
            color: red;
        }
        .kp-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: flex-start;
        }
        .kp-box {
            min-width: 50px;
            height: 50px;
            min-height: 40px;
            padding: 8px 16px;
            border: 1px solid #333;
            border-radius: 6px;
            background: #fff;
            margin-bottom: 0;
            position: relative;
            text-align: center;
            line-height: 1.2;
            font-size: 15px;
            transition: box-shadow 0.2s;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            overflow: visible; /* 改为visible让删除按钮显示完整 */
            text-overflow: ellipsis;
        }
        .kp-box:hover {
            box-shadow: 0 2px 8px #eee;
        }
        .delete-btn {
            position: absolute;
            top: -8px;      /* 调整位置更靠外 */
            right: -8px;    /* 调整位置更靠外 */
            font-size: 12px;
            color: #fff;
            background: rgba(224, 0, 0, 0.9); /* 增加不透明度 */
            border-radius: 50%;
            width: 20px;     /* 增大尺寸 */
            height: 20px;    /* 增大尺寸 */
            line-height: 20px; /* 对应调整行高 */
            text-align: center;
            cursor: pointer;
            display: none;
            z-index: 20; /* 提高层级 */
            box-shadow: 0 2px 4px rgba(0,0,0,0.4); /* 增强阴影 */
            transition: all 0.2s ease;
        }
        .delete-btn:hover {
            background: #e00; /* 悬停时变为实心红色 */
            transform: scale(1.1); /* 悬停时稍微放大 */
        }
        .edit-mode .delete-btn {
            display: block;
        }
        .add-btn {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            cursor: pointer;
            background: #f0f8ff;
            border: 1px dashed #007bff;
            display: none;
        }
        .edit-mode .add-btn {
            display: inline-block !important;
        }
        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        .modal-overlay.active {
            opacity: 1;
            pointer-events: all;
        }
        .modal-content {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            min-width: 400px;
            max-width: 90%;
            text-align: center;
            transform: scale(0.8);
            transition: transform 0.3s ease;
            width: auto; /* 改为自适应宽度 */
        }
        .modal-overlay.active .modal-content {
            transform: scale(1);
        }
        .modal-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        .kp-input {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 25px;
            box-sizing: border-box;
            min-width: 300px; /* 设置最小宽度 */
            resize: horizontal; /* 允许水平调整大小 */
        }
        .kp-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
        }
        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        .modal-btn {
            padding: 10px 25px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .modal-btn.confirm {
            background: #007bff;
            color: white;
        }
        .modal-btn.confirm:hover {
            background: #0069d9;
        }
        .modal-btn.cancel {
            background: #f5f5f5;
            color: #333;
        }
        .modal-btn.cancel:hover {
            background: #e0e0e0;
        }

        /* 删除确认弹窗样式 */
        #deleteConfirmModal .modal-content {
            width: 350px;
        }
        #deleteConfirmModal .modal-message {
            font-size: 16px;
            margin-bottom: 30px;
            color: #555;
        }
        /* 批量清除确认弹窗与按钮的间距 */
        #bulkConfirm .modal-message {
            font-size: 16px;
            margin-bottom: 28px; /* 增加与按钮的距离 */
            color: #555;
        }
        /* 知识点&题目详情的批量删除确认弹窗与按钮的间距，保持一致 */
        #kpBulkConfirm .modal-message {
            font-size: 16px;
            margin-bottom: 28px; /* 与 #bulkConfirm 保持一致 */
            color: #555;
        }
        #deleteConfirmModal .modal-message strong {
            color: #e00;
        }

        /* 自定义滚动条样式 */
        .content::-webkit-scrollbar {
            width: 10px;
        }
        .content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 5px;
        }
        .content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 5px;
        }
        .content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 提示信息样式 */
        .duplicate-message {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #ff9800;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            display: none;
        }

        /* 操作成功提示弹窗 */
        .success-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #4caf50;
            color: white;
            padding: 16px 24px;
            border-radius: 8px;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.3s ease, transform 0.3s ease;
            pointer-events: none;
        }
        .success-message.show {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    </style>
    <!-- MathJax for LaTeX rendering -->
    <script>
      window.MathJax = {
        tex: {
          inlineMath: [['$', '$'], ['\\(', '\\)']],
          displayMath: [['$$','$$'], ['\\[','\\]']],
          // 确保 amsmath 等扩展可用，支持 \begin{pmatrix} 等环境
          packages: { '[+]': ['ams'] },
          processEscapes: true,
        },
        svg: { fontCache: 'global' },
        options: { skipHtmlTags: ['script','noscript','style','textarea','pre','code'] }
      };
    </script>
    <!-- 使用 full 版本，包含更多 TeX 扩展，确保矩阵环境正常渲染 -->
    <script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg-full.js" id="mathjax-script" async></script>
    <!-- Markdown 渲染与安全清洗库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.1.6/dist/purify.min.js"></script>
    <script>
      // 启用行内换行（单个 \n 视为换行）与 GFM 支持
      try { if (window.marked && window.marked.setOptions) { window.marked.setOptions({ breaks: true, gfm: true }); } } catch (e) {}
    </script>
</head>
<body>
    <!-- 操作成功提示弹窗 -->
    <div class="success-message" id="successMessage"></div>

    <!-- 添加知识点弹窗 -->
    <div class="modal-overlay" id="addKPModal">
        <div class="modal-content">
            <div class="modal-title">添加知识点</div>
            <input type="text" class="kp-input" id="kpNameInput" placeholder="请输入知识点名称">
            <div class="modal-buttons">
                <button class="modal-btn cancel" onclick="closeAddKPModal()">取消</button>
                <button class="modal-btn confirm" onclick="confirmAddKP()">确认</button>
            </div>
        </div>
    </div>

    <!-- 删除知识点确认弹窗 -->
    <div class="modal-overlay" id="deleteConfirmModal">
        <div class="modal-content">
            <div class="modal-title">确认删除</div>
            <div class="modal-message">
                确定要删除知识点 <strong id="deleteKPName"></strong> 吗？
            </div>
            <div class="modal-buttons">
                <button class="modal-btn cancel" onclick="closeDeleteConfirmModal()">取消</button>
                <button class="modal-btn confirm" onclick="confirmDeleteKP()">确认</button>
            </div>
        </div>
    </div>

    <!-- 正在生成视频提示弹窗 -->
    <div class="modal-overlay" id="generatingVideoModal">
        <div class="modal-content">
            <div class="modal-title">提示</div>
            <div style="font-size:16px;margin-bottom:24px;">该知识点正在生成视频，请稍后再试</div>
            <div class="modal-buttons">
                <button class="modal-btn confirm" onclick="closeGeneratingVideoModal()">确定</button>
            </div>
        </div>
    </div>

    <!-- 侧边栏：学科知识点导航 -->
    <div class="sidebar">
        <div class="sidebar-title">学科知识点</div>
        {% for s1 in subject_1_list %}
            <div class="subject-block">
                <div class="subject-1-title">{{ s1.name }}</div>
                <ul class="subject-2-list">
                    {% for s2 in s1.subject_2_list %}
                        <!-- 二级学科点击后展开对应章节 -->
                        <li class="subject-2-item" onclick="selectSubject2(this, '{{ s2 }}')">
                            {{ s2 }}
                        </li>
                    {% endfor %}
                </ul>
            </div>
        {% endfor %}
    </div>

    <!-- 主内容区：顶部标签 + 各页签内容 -->
    <div class="content">
        <div class="content-header">
            <div id="subjectTitle" class="subject2-title"></div>
        </div>
        <div class="tabs">
            <div class="tabs-left">
                <div class="tab active" data-tab="kp">知识点</div>
                <div class="tab" data-tab="overview">题型&难度总览</div>
                <div class="tab" data-tab="details">知识点&题目详情</div>
            </div>
        </div>
        <div class="tab-content">
            <!-- 知识点页签（现有页面内容） -->
            <div id="tab-kp" class="tab-pane active">
                <div class="kp-toolbar"><button id="globalEditBtn" class="edit-btn">编辑</button></div>
                <div id="duplicateMessage" class="duplicate-message"></div>
                {% for s2, cat_dict in subject_2_map.items %}
                    <div class="chapter-groups" id="chapters-{{ s2 }}" style="display:none;">
                        {% for cat, kp_list in cat_dict.items %}
                            <div class="chapter-group" data-category="{{ cat }}">
                                <!-- <div class="chapter-header">
                                    <span class="chapter-title">{{ cat }}</span>
                                </div> -->
                                <div class="kp-container">
                                    {% for kp_obj in kp_list %}
                                    <!-- 知识点：有视频链接为黑色，无视频链接为红色 -->
                                    <div class="kp-box" {% if not kp_obj.has_video %}style="color:red"{% endif %}>
                                        {{ kp_obj.kp }}
                                        <span class="delete-btn" title="删除知识点" onclick="showDeleteConfirmModal('{{ s2 }}', '{{ cat }}', '{{ kp_obj.kp }}', this)">✕</span>
                                    </div>
                                    {% endfor %}
                                    <!-- 添加知识点按钮 -->
                                    <div class="kp-box add-btn" title="添加知识点" onclick="showAddKPModal('{{ s2 }}', '{{ cat }}', this)">+</div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endfor %}
            </div>

            <!-- 题型&难度总览 -->
            <div id="tab-overview" class="tab-pane">
                <div class="stats-bar" id="overviewStats" style="display:none;"></div>
                <div class="overview-rows" id="overviewRows"></div>
                <div class="overview-detail" id="overviewDetail">
                    <div class="overview-header">
                        <div class="overview-back" id="overviewBack">返回</div>
                        <div style="display:flex; gap:10px; align-items:center;">
                            <div class="edit-tools" id="overviewEditTools">
                                <button class="edit-btn outline-danger-btn" id="bulkDelete" style="color:#f44336; border-color:#f44336;">删除</button>
                                <button class="edit-btn" id="exitEdit">退出</button>
                            </div>
                            <button class="edit-btn" id="overviewEdit">编辑</button>
                        </div>
                    </div>
                    <div class="question-list" id="overviewQuestionList"></div>
                    <div class="modal-overlay" id="bulkConfirm">
                        <div class="modal-content">
                            <div class="modal-title">确认删除</div>
                            <div class="modal-message">确定要删除这些题目吗？</div>
                            <div class="modal-buttons">
                                <button class="modal-btn cancel" id="bulkCancel">取消</button>
                                <button class="modal-btn confirm" id="bulkOk">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 知识点&题目详情 -->
            <div id="tab-details" class="tab-pane">
                <div class="stats-bar" id="kpOverviewStats" style="display:none;"></div>
                <div class="kp-overview-grid" id="kpOverviewGrid"></div>
                <div class="overview-detail" id="kpDetailPanel" style="display:none;">
                    <div class="overview-header">
                        <div class="overview-back" id="kpDetailBack">返回</div>
                        <div style="display:flex; gap:10px; align-items:center;">
                            <div class="edit-tools" id="kpEditTools" style="display:none;">
                                <button class="edit-btn outline-danger-btn" id="kpBulkDelete" style="color:#f44336; border-color:#f44336;">删除</button>
                                <button class="edit-btn" id="kpExitEdit">退出</button>
                            </div>
                            <button class="edit-btn" id="kpEdit">编辑</button>
                        </div>
                    </div>
                    <div style="display:flex; align-items:center; gap:8px; margin:6px 0 10px 0;">
                        <div id="kpDetailTitle" class="subject2-title" style="font-size:18px; margin:0; font-weight:400;">&nbsp;</div>
                        <select id="kpTypeDiffSelect" class="rounded-select" style="margin-left:8px; font-size:14px;">
                            <option value="">题型&难度</option>
                            <option value="0,1">单选题 · 难度1</option>
                            <option value="0,2">单选题 · 难度2</option>
                            <option value="0,3">单选题 · 难度3</option>
                            <option value="0,4">单选题 · 难度4</option>
                            <option value="0,5">单选题 · 难度5</option>
                            <option value="2,1">主观题 · 难度1</option>
                            <option value="2,2">主观题 · 难度2</option>
                            <option value="2,3">主观题 · 难度3</option>
                            <option value="2,4">主观题 · 难度4</option>
                            <option value="2,5">主观题 · 难度5</option>
                        </select>
                        <span id="kpTypeDiffLabel" style="font-size:14px; color:#555; display:none; margin-left:8px;"></span>
                    </div>
                    <div class="question-list" id="kpDetailList"></div>
                    <div class="modal-overlay" id="kpBulkConfirm">
                        <div class="modal-content">
                            <div class="modal-title">确认删除</div>
                            <div class="modal-message">确定要删除这些题目吗？</div>
                            <div class="modal-buttons">
                                <button class="modal-btn cancel" id="kpBulkCancel">取消</button>
                                <button class="modal-btn confirm" id="kpBulkOk">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量 
        let currentSubject2 = '';
        let currentCategory = '';
        let currentKPName = '';
        let currentDeleteBtn = null;
        let currentAddBtn = null;
        let duplicateMessage = null;
        let generatingVideos = new Set(); // 正在生成视频的知识点集合

        // 操作成功提示
        function showSuccessMessage(message) {
            const successMsg = document.getElementById('successMessage');
            successMsg.textContent = message;
            successMsg.classList.add('show');
            setTimeout(() => { successMsg.classList.remove('show'); }, 2500);
        }

        // 去重处理 
        function removeDuplicates() {
            document.querySelectorAll('.chapter-group').forEach(chapter => {
                const kpBoxes = chapter.querySelectorAll('.kp-box:not(.add-btn)');
                const seen = new Set();
                let duplicatesFound = 0;
                kpBoxes.forEach(kpBox => {
                    const kpName = kpBox.textContent.replace('✕', '').trim();
                    if (seen.has(kpName)) {
                        duplicatesFound++;
                        kpBox.remove();
                    } else {
                        seen.add(kpName);
                    }
                });
                if (duplicatesFound > 0 && !duplicateMessage) {
                    duplicateMessage = document.getElementById('duplicateMessage');
                    setTimeout(() => { duplicateMessage.style.display = 'none'; }, 5000);
                }
            });
        }

        // 二级学科切换 
        function selectSubject2(elem, s2) {
            console.log('切换到二级学科:', s2);
            currentSubject2 = s2;
            document.querySelectorAll('.subject-2-item').forEach(e => e.classList.remove('active'));
            elem.classList.add('active');
            document.querySelectorAll('.chapter-groups').forEach(c => c.style.display = 'none');
            const group = document.getElementById('chapters-' + s2);
            if (group) group.style.display = 'block';
            const xulun = document.getElementById('chapter-xulun');
            if (xulun) {
                if (s2 === '数据结构') {
                    xulun.style.display = 'block';
                } else {
                    xulun.style.display = 'none';
                }
            }
            // 更新顶部学科名称
            const titleEl = document.getElementById('subjectTitle');
            if (titleEl) titleEl.textContent = s2;
            // 切换学科时重置全局编辑状态
            const globalEditBtn = document.getElementById('globalEditBtn');
            if (globalEditBtn && globalEditBtn.classList.contains('edit-mode')) {
                globalEditBtn.classList.remove('edit-mode');
                globalEditBtn.textContent = '编辑';
                globalEditBtn.style.borderColor = '#007bff';
                globalEditBtn.style.color = '#007bff';
            }
            document.querySelectorAll('#tab-kp .chapter-group.edit-mode').forEach(g => g.classList.remove('edit-mode'));
            document.querySelector('.content').scrollTop = 0;
            setTimeout(removeDuplicates, 100);

            // 如果当前在“题型&难度总览”页签，切换学科后重载统计
            const activeTab = document.querySelector('.tab.active');
            if (activeTab && activeTab.getAttribute('data-tab') === 'overview') {
                // 切换学科时，题型&难度总览强制回到总览网格视图
                try {
                    document.body.removeAttribute('data-overview-edit');
                    const tools = document.getElementById('overviewEditTools');
                    const editBtn = document.getElementById('overviewEdit');
                    if (tools) tools.style.display = 'none';
                    if (editBtn) editBtn.style.display = 'inline-block';

                    const rowsEl = document.getElementById('overviewRows');
                    const detailEl = document.getElementById('overviewDetail');
                    const statsEl = document.getElementById('overviewStats');
                    if (detailEl) detailEl.style.display = 'none';
                    if (rowsEl) rowsEl.style.display = 'flex';
                    if (statsEl) statsEl.style.display = 'flex';
                    overviewMode = 'grid';
                    currentQType = null;
                    currentQDiff = null;
                } catch (e) {}
                loadOverviewStats();
            }
            // 若当前在“知识点&题目详情”，切换学科时强制恢复到知识点网格总览并刷新
            if (activeTab && activeTab.getAttribute('data-tab') === 'details') {
                try {
                    // 清理编辑与面板状态
                    document.body.removeAttribute('data-kpdetail-edit');
                    const grid = document.getElementById('kpOverviewGrid');
                    const stats = document.getElementById('kpOverviewStats');
                    const panel = document.getElementById('kpDetailPanel');
                    const listEl = document.getElementById('kpDetailList');
                    const tools = document.getElementById('kpEditTools');
                    const editBtn = document.getElementById('kpEdit');
                    if (panel) panel.style.display = 'none';
                    if (grid) { grid.style.display = 'grid'; grid.innerHTML = ''; }
                    if (stats) stats.style.display = 'flex';
                    if (listEl) listEl.innerHTML = '';
                    if (tools) tools.style.display = 'none';
                    if (editBtn) editBtn.style.display = 'inline-block';
                    document.querySelectorAll('.kp-kp-checkbox').forEach(cb=> cb.remove());
                } catch (e) {}
                loadKnowledgePointOverview();
            }
        }

        // 顶部标签切换
        function activateTab(name) {
            document.querySelectorAll('.tab').forEach(t => t.classList.toggle('active', t.getAttribute('data-tab') === name));
            document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
            const pane = document.getElementById(`tab-${name}`);
            if (pane) pane.classList.add('active');

            if (name === 'overview') {
                // 切回“题型&难度总览”时始终恢复到网格总览视图
                try {
                    const rowsEl = document.getElementById('overviewRows');
                    const detailEl = document.getElementById('overviewDetail');
                    const statsEl = document.getElementById('overviewStats');
                    const editBtn = document.getElementById('overviewEdit');
                    const tools = document.getElementById('overviewEditTools');
                    const listEl = document.getElementById('overviewQuestionList');

                    // 重置编辑态与界面
                    document.body.removeAttribute('data-overview-edit');
                    if (tools) tools.style.display = 'none';
                    if (editBtn) editBtn.style.display = 'inline-block';
                    if (detailEl) detailEl.style.display = 'none';
                    if (rowsEl) rowsEl.style.display = 'flex';
                    if (statsEl) statsEl.style.display = 'flex';
                    if (listEl) listEl.innerHTML = '';

                    // 重置模式与上下文
                    overviewMode = 'grid';
                    currentQType = null;
                    currentQDiff = null;
                } catch (e) {}
                loadOverviewStats();
            } else if (name === 'details') {
                // 切回“知识点&题目详情”时，强制恢复到知识点题型分布网格总览视图
                try {
                    document.body.removeAttribute('data-kpdetail-edit');
                    const grid = document.getElementById('kpOverviewGrid');
                    const stats = document.getElementById('kpOverviewStats');
                    const panel = document.getElementById('kpDetailPanel');
                    const listEl = document.getElementById('kpDetailList');
                    const tools = document.getElementById('kpEditTools');
                    const editBtn = document.getElementById('kpEdit');
                    if (panel) panel.style.display = 'none';
                    if (grid) { grid.style.display = 'grid'; grid.innerHTML = ''; }
                    if (stats) stats.style.display = 'flex';
                    if (listEl) listEl.innerHTML = '';
                    if (tools) tools.style.display = 'none';
                    if (editBtn) editBtn.style.display = 'inline-block';
                    document.querySelectorAll('.kp-kp-checkbox').forEach(cb=> cb.remove());
                } catch (e) {}
                // 加载“知识点&题目详情”
                loadKnowledgePointOverview();
            }
        }
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() { activateTab(this.getAttribute('data-tab')); });
        });

        // 全局编辑按钮：置于标签行右侧，控制当前学科所有章节的编辑模式
        (function initGlobalEditButton() {
            const btn = document.getElementById('globalEditBtn');
            if (!btn) return;
            btn.addEventListener('click', function() {
                if (!currentSubject2) return;
                const container = document.getElementById('chapters-' + currentSubject2);
                if (!container) return;
                const entering = !this.classList.contains('edit-mode');
                container.querySelectorAll('.chapter-group').forEach(g => g.classList.toggle('edit-mode', entering));
                if (entering) {
                    this.classList.add('edit-mode');
                    this.textContent = '退出编辑';
                    this.style.borderColor = 'red';
                    this.style.color = 'red';
                } else {
                    this.classList.remove('edit-mode');
                    this.textContent = '编辑';
                    this.style.borderColor = '#007bff';
                    this.style.color = '#007bff';
                }
            });
        })();

        // 编辑模式切换
        function toggleEditMode(btn) {
            const chapter = btn.closest('.chapter-group');
            chapter.classList.toggle('edit-mode');
            if (btn.classList.contains('edit-mode')) {
                btn.classList.remove('edit-mode');
                btn.textContent = '编辑';
                btn.style.borderColor = '#007bff';
                btn.style.color = '#007bff';
            } else {
                btn.classList.add('edit-mode');
                btn.textContent = '退出编辑';
                btn.style.borderColor = 'red';
                btn.style.color = 'red';
            }
        }

        // 删除知识点弹窗 
        function showDeleteConfirmModal(subject2, category, kpName, btn) {
            currentCategory = category;
            currentKPName = kpName;
            currentDeleteBtn = btn;
            document.getElementById('deleteKPName').textContent = kpName;
            document.getElementById('deleteConfirmModal').classList.add('active');
        }

        function closeDeleteConfirmModal() {
            document.getElementById('deleteConfirmModal').classList.remove('active');
        }

        // 删除知识点
        function confirmDeleteKP() {
            fetch('/console/app/delete_kp/', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    subject_2: currentSubject2,
                    categories: currentCategory,
                    knowledge_point: currentKPName
                })
            })
            .then(res => {
                if (!res.ok) throw new Error('网络错误或接口未找到');
                return res.json();
            })
            .then(data => {
                if (data.deleted) {
                    currentDeleteBtn.parentElement.remove();
                    removeDuplicates();
                    showSuccessMessage('删除成功！');
                } else {
                    alert('删除失败');
                }
                closeDeleteConfirmModal();
            })
            .catch(err => {
                alert('请求失败，请检查网络或后端接口');
                console.error('删除知识点错误:', err);
                closeDeleteConfirmModal();
            });
        }

        // 添加知识点弹窗
        function showAddKPModal(subject2, category, btn) {
            currentCategory = category;
            currentAddBtn = btn;
            const modal = document.getElementById('addKPModal');
            modal.classList.add('active');
            const input = document.getElementById('kpNameInput');
            input.value = '';
            input.focus();
            
            // 自适应输入框宽度
            input.addEventListener('input', function() {
                const textLength = this.value.length;
                const minWidth = 300;
                const charWidth = 16; // 每个字符大约16px
                const newWidth = Math.max(minWidth, textLength * charWidth + 50);
                this.style.width = newWidth + 'px';
                
                // 同时调整模态框宽度
                const modalContent = this.closest('.modal-content');
                modalContent.style.width = (newWidth + 100) + 'px';
            });
        }

        function closeAddKPModal() {
            document.getElementById('addKPModal').classList.remove('active');
        }

        // 添加知识点
        function confirmAddKP() {
            const input = document.getElementById('kpNameInput');
            const name = input.value.trim();
            if (!name) {
                alert('知识点名称不能为空');
                input.focus();
                return;
            }
            const existingKP = currentAddBtn.closest('.chapter-group').querySelectorAll('.kp-box:not(.add-btn)');
            let alreadyExists = false;
            existingKP.forEach(kp => {
                const kpName = kp.textContent.replace('✕', '').trim();
                if (kpName === name) {
                    alreadyExists = true;
                }
            });
            if (alreadyExists) {
                duplicateMessage = document.getElementById('duplicateMessage');
                duplicateMessage.textContent = `知识点"${name}"已存在，不能重复添加`;
                duplicateMessage.style.display = 'block';
                setTimeout(() => { duplicateMessage.style.display = 'none'; }, 5000);
                return;
            }
            fetch('/console/app/add_kp/', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    subject_2: currentSubject2,
                    categories: currentCategory,
                    knowledge_point: name
                })
            })
            .then(res => {
                if (!res.ok) throw new Error('网络错误或接口未找到');
                return res.json();
            })
            .then(data => {
                if (data.knowledge_point) {
                    const newBox = document.createElement('div');
                    newBox.className = 'kp-box';
                    newBox.style.color = 'red'; // 新知识点默认红色
                    newBox.innerHTML = `${data.knowledge_point}<span class="delete-btn" title="删除知识点" onclick="showDeleteConfirmModal('${currentSubject2}', '${currentCategory}', '${data.knowledge_point}', this)">✕</span>`;
                    currentAddBtn.parentNode.insertBefore(newBox, currentAddBtn);
                    removeDuplicates();
                    showSuccessMessage('添加成功！');
                    closeAddKPModal();
                    // 添加知识点后弹出文生视频提醒弹窗
                    showGenerateVideoConfirmModal(currentSubject2, currentCategory, data.knowledge_point);
                } else {
                    alert('添加失败');
                    input.focus();
                }
            })
            .catch(err => {
                alert('请求失败，请检查网络或后端接口');
                console.error('添加知识点错误:', err);
                closeAddKPModal();
            });
        }

        // 键盘快捷操作 
        document.addEventListener('keydown', function(e) {
            const addModal = document.getElementById('addKPModal');
            const deleteModal = document.getElementById('deleteConfirmModal');
            if (addModal.classList.contains('active')) {
                if (e.key === 'Enter') {
                    confirmAddKP();
                } else if (e.key === 'Escape') {
                    closeAddKPModal();
                }
            } else if (deleteModal.classList.contains('active')) {
                if (e.key === 'Enter') {
                    confirmDeleteKP();
                } else if (e.key === 'Escape') {
                    closeDeleteConfirmModal();
                }
            }
        });

        setTimeout(removeDuplicates, 300);

        // 视频链接处理
        function openVideoIfExists(subject2, category, kpName) {
            fetch('/console/app/get_video/', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    subject_2: subject2,
                    categories: category,
                    knowledge_point: kpName
                })
            })
            .then(res => res.json())
            .then(data => {
                if (data.video) {
                    window.open(data.video, '_blank');
                }
            })
            .catch(err => {
                alert('请求失败，请检查网络或后端接口');
                console.error('获取视频错误:', err);
            });
        }

        // 添加视频链接弹窗 
        function showAddVideoModal(subject2, category, kpName, kpBox) {
            currentCategory = category;
            currentKPName = kpName;
            currentKPBox = kpBox;
            const modalHtml = `
                <div class="modal-overlay active" id="addVideoModal">
                    <div class="modal-content">
                        <div class="modal-title">添加视频链接</div>
                        <input type="text" class="kp-input" id="videoUrlInput" placeholder="请输入视频链接">
                        <div class="modal-buttons">
                            <button class="modal-btn cancel" onclick="closeAddVideoModal()">取消</button>
                            <button class="modal-btn confirm" onclick="confirmAddVideo()">确定</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            document.getElementById('videoUrlInput').focus();
        }

        function closeAddVideoModal() {
            const modal = document.getElementById('addVideoModal');
            if (modal) modal.remove();
        }

        // 添加视频链接 
        function confirmAddVideo() {
            const input = document.getElementById('videoUrlInput');
            const videoUrl = input.value.trim();
            if (!videoUrl) {
                alert('视频链接不能为空');
                input.focus();
                return;
            }
            fetch('/console/app/add_video/', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    subject_2: currentSubject2,
                    categories: currentCategory,
                    knowledge_point: currentKPName,
                    video: videoUrl
                })
            })
            .then(res => {
                if (!res.ok) throw new Error('网络错误或接口未找到');
                return res.json();
            })
            .then(data => {
                if (data.success) {
                    showSuccessMessage('视频链接添加成功！');
                    closeAddVideoModal();
                    // 新增：添加视频后将知识点字体变为黑色
                    document.querySelectorAll('.kp-box').forEach(box => {
                        if (box.textContent.replace('✕', '').trim() === currentKPName) {
                            box.style.color = 'black'; 
                        }
                    });
                } else {
                    alert('添加失败');
                    input.focus();
                }
            })
            .catch(err => {
                alert('请求失败，请检查网络或后端接口');
                console.error('添加视频错误:', err);
                closeAddVideoModal();
            });
        }

        // 删除视频链接弹窗
        function showDeleteVideoConfirmModal(subject2, category, kpName, videoUrl, btn) {
            currentCategory = category;
            currentKPName = kpName;
            currentVideoUrl = videoUrl;
            currentDeleteBtn = btn;
            const modalHtml = `
                <div class="modal-overlay active" id="deleteVideoConfirmModal">
                    <div class="modal-content">
                        <div class="modal-title">确认删除视频</div>
                        <div class="modal-message" style="margin-bottom: 20px;">
                            确定要删除知识点 <strong>${kpName}</strong> 的视频链接吗？
                        </div>
                        <div class="modal-buttons">
                            <button class="modal-btn cancel" onclick="closeDeleteVideoConfirmModal()">取消</button>
                            <button class="modal-btn confirm" onclick="confirmDeleteVideo()">确认</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeDeleteVideoConfirmModal() {
            const modal = document.getElementById('deleteVideoConfirmModal');
            if (modal) modal.remove();
        }

        // 删除视频链接
        function confirmDeleteVideo() {
            fetch('/console/app/delete_video/', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    subject_2: currentSubject2,
                    categories: currentCategory,
                    knowledge_point: currentKPName,
                    video: currentVideoUrl
                })
            })
            .then(res => {
                if (!res.ok) throw new Error('网络错误或接口未找到');
                return res.json();
            })
            .then(data => {
                if (data.success) {
                    showSuccessMessage('视频链接删除成功！');
                    closeDeleteVideoConfirmModal();
                } else {
                    alert('删除失败');
                }
            })
            .catch(err => {
                alert('请求失败，请检查网络或后端接口');
                console.error('删除视频链接错误:', err);
                closeDeleteVideoConfirmModal();
            });
        }

        // 无视频链接弹窗
        function showNoVideoConfirmModal(subject2, category, kpName, kpBox) {
            // 先移除旧弹窗
            const oldModal = document.getElementById('noVideoConfirmModal');
            if (oldModal) oldModal.remove();
            // 插入新弹窗
            const modalHtml = `
                <div class="modal-overlay active" id="noVideoConfirmModal" style="display:flex;justify-content:center;align-items:center;">
                    <div class="modal-content">
                        <div class="modal-title">提示</div>
                        <div style="font-size:16px;margin-bottom:24px;">该知识点暂无视频，是否提醒文生视频生成相关视频？</div>
                        <div class="modal-buttons">
                            <button class="modal-btn cancel" onclick="closeNoVideoConfirmModal()">取消</button>
                            <button class="modal-btn confirm" onclick="confirmNoVideoAdd('${subject2}','${category}','${kpName}')">确定</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeNoVideoConfirmModal() {
            const modal = document.getElementById('noVideoConfirmModal');
            if (modal) modal.remove();
        }

        function confirmNoVideoAdd(subject2, category, kpName) {
            closeNoVideoConfirmModal();
            showAddVideoModal(subject2, category, kpName, null);
        }

        // 文生视频提醒弹窗
        function showGenerateVideoConfirmModal(subject2, category, kpName) {
            // 移除旧弹窗
            const oldModal = document.getElementById('generateVideoConfirmModal');
            if (oldModal) oldModal.remove();
            // 插入新弹窗
            const modalHtml = `
                <div class="modal-overlay active" id="generateVideoConfirmModal" style="display:flex;justify-content:center;align-items:center;">
                    <div class="modal-content">
                        <div class="modal-title">提示</div>
                        <div style="font-size:16px;margin-bottom:24px;">该知识点暂无视频，是否提醒文生视频生成相关视频？</div>
                        <div class="modal-buttons">
                            <button class="modal-btn cancel" onclick="closeGenerateVideoConfirmModal()">否</button>
                            <button class="modal-btn confirm" onclick="confirmGenerateVideo('${subject2}','${category}','${kpName}')">是</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeGenerateVideoConfirmModal() {
            const modal = document.getElementById('generateVideoConfirmModal');
            if (modal) modal.remove();
        }

        function closeGeneratingVideoModal() {
            document.getElementById('generatingVideoModal').classList.remove('active');
        }

        // 已提醒弹窗（自动消失）
        function showRemindedModal() {
            // 移除旧弹窗
            const oldModal = document.getElementById('remindedModal');
            if (oldModal) oldModal.remove();
            // 插入新弹窗
            const modalHtml = `
                <div class="modal-overlay active" id="remindedModal" style="display:flex;justify-content:center;align-items:center;">
                    <div class="modal-content" style="padding:20px;min-width:300px;">
                        <div style="font-size:16px;text-align:center;">已提醒文生视频生成相关视频</div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            // 1秒后自动关闭
            setTimeout(() => {
                const modal = document.getElementById('remindedModal');
                if (modal) modal.remove();
            }, 1000);
        }

        // 提醒弹窗点击“是”时，通知后端并弹出已提醒
        function confirmGenerateVideo(subject2, category, kpName) {
            closeGenerateVideoConfirmModal();
            showRemindedModal(); // 立即弹窗
            
            // 添加到正在生成集合
            const key = `${subject2}-${category}-${kpName}`;
            generatingVideos.add(key);
            
            fetch('/console/app/text_to_video_remind/', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    subject_2: subject2,
                    categories: category,
                    knowledge_point: kpName
                })
            })
            .then(res => res.json())
            .then(data => {
                // 如果生成成功且有视频链接，则将知识点字体变为黑色
                if (data.success && data.video_url) {
                    document.querySelectorAll('.kp-box').forEach(box => {
                        if (box.textContent.replace('✕', '').trim() === kpName) {
                            box.style.color = 'black';
                            // 不要再绑定 box.onclick，事件委托已处理跳转
                        }
                    });
                }
                // 无论成功与否，都从集合中移除
                generatingVideos.delete(key);
            })
            .catch(err => {
                // 错误处理可选
                generatingVideos.delete(key);
            });
        }

        // 修改知识点点击事件（仅在“知识点”页签中委托）
        document.getElementById('tab-kp').addEventListener('click', function(e) {
            const kpBox = e.target.closest('.kp-box:not(.add-btn)');
            if (!kpBox || e.target.classList.contains('delete-btn')) return;

            const kpName = kpBox.textContent.replace('✕', '').trim();
            const chapterGroup = kpBox.closest('.chapter-group');
            const category = chapterGroup ? chapterGroup.getAttribute('data-category') : '';
            const subject2 = currentSubject2;

            // 检查是否正在生成视频
            const key = `${subject2}-${category}-${kpName}`;
            if (generatingVideos.has(key)) {
                document.getElementById('generatingVideoModal').classList.add('active');
                return;
            }

            fetch('/console/app/get_video/', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    subject_2: subject2,
                    categories: category,
                    knowledge_point: kpName
                })
            })
            .then(res => res.json())
            .then(data => {
                if (data.video) {
                    window.open(data.video, '_blank');
                } else {
                    // 只弹提醒文生视频弹窗，不再弹出添加视频弹窗
                    showGenerateVideoConfirmModal(subject2, category, kpName);
                }
            })
            .catch(err => {
                alert('请求失败，请检查网络或后端接口');
                console.error('获取视频错误:', err);
            });
        });

        // 默认进入“数据结构”页面：若存在则选中，否则选中第一个
        document.addEventListener('DOMContentLoaded', function() {
            const items = document.querySelectorAll('.subject-2-item');
            if (!items.length) return;
            let target = null;
            items.forEach(li => { if (li.textContent.trim() === '数据结构') target = li; });
            if (!target) target = items[0];
            if (target) selectSubject2(target, target.textContent.trim());
            // 若默认就在overview标签，加载统计
            const activeTab = document.querySelector('.tab.active');
            if (activeTab && activeTab.getAttribute('data-tab') === 'overview') {
                loadOverviewStats();
            }
            if (activeTab && activeTab.getAttribute('data-tab') === 'details') {
                loadKnowledgePointOverview();
            }
        });

        // 加载题型与难度统计
        let overviewLoaded = false;
        let overviewMode = 'grid'; // grid | list | detail
        let currentQType = null;
        let currentQDiff = null;
        // 知识点&题目详情内部模式与上下文
        let kpDetailMode = 'list'; // list | detail
        let currentKpId = null;
        let currentKpFilterValue = '';
        // 简单HTML转义，避免题干/选项中的 < > 等破坏DOM结构
        function sanitizeText(str){
            if (str === undefined || str === null) return '';
            let s = String(str);
            // 保护允许的标签和实体：<u> </u> <br> &nbsp;
            s = s.replace(/<\s*u\s*>/gi, '[[UTAG_OPEN]]')
                 .replace(/<\s*\/\s*u\s*>/gi, '[[UTAG_CLOSE]]')
                 .replace(/<\s*br\s*\/?\s*>/gi, '[[BRTAG]]')
                 .replace(/&nbsp;|&#160;/gi, '[[NBSP]]');
            // 移除其他所有HTML标签
            s = s.replace(/<[^>]*>/g, '');
            // 安全转义
            s = s
                .replaceAll('&','&amp;')
                .replaceAll('<','&lt;')
                .replaceAll('>','&gt;')
                .replaceAll('"','&quot;')
                .replaceAll("'",'&#39;');
            // 还原被保护的标记
            return s.replaceAll('[[UTAG_OPEN]]','<u>')
                    .replaceAll('[[UTAG_CLOSE]]','</u>')
                    .replaceAll('[[BRTAG]]','<br/>')
                    .replaceAll('[[NBSP]]','&nbsp;');
        }
        // 安全 Markdown 渲染
        function renderMarkdownSafe(md){
            try {
                const src = String(md || '');
                // 预处理：用块元素包裹 $$...$$，避免 Markdown 把起止 $$ 拆成多个段落导致 MathJax 无法成对匹配
                // 这样 MathJax 可以在单个元素内识别整段公式（含多行矩阵）
                const preserved = src.replace(/\$\$([\s\S]*?)\$\$/g, function(_, content){
                    return '<div class="mjx-block">$$' + content + '$$</div>';
                });
                const html = (window.marked && window.marked.parse) ? window.marked.parse(preserved) : preserved.replace(/\n/g,'<br/>');
                // 允许自定义 class，确保 mjx-block 不被清理
                const clean = (window.DOMPurify && window.DOMPurify.sanitize) ? window.DOMPurify.sanitize(html, { ADD_TAGS: ['div','span','pre','code'], ADD_ATTR: ['class'] }) : html;
                return clean;
            } catch (e) {
                return sanitizeText(md || '');
            }
        }

        function loadOverviewStats() {
            if (!currentSubject2) return; // 需先选择二级学科
            // 每次切换学科时允许重新加载
            const rows = document.getElementById('overviewRows');
            const stats = document.getElementById('overviewStats');
            rows.innerHTML = '';
            stats.style.display = 'none';

            fetch('/console/app/question_type_difficulty_overview/', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ subject_2: currentSubject2 })
            })
            .then(r => r.json())
            .then(data => {
                // 渲染统计条
                const total = data.total || 0;
                const typeTotals = data.type_totals || {};
                const single = typeTotals['0'] || 0;
                const subjective = typeTotals['2'] || 0;
                stats.innerHTML = `
                    <div class="stat-item">统计：共${total}道题</div>
                    <div class="stat-item">单选题：共${single}道题</div>
                    <div class="stat-item">主观题：共${subjective}道题</div>
                `;
                stats.style.display = 'flex';

                // 渲染难度卡片：按图示顺序 0:1..5, 2:1..5
                const byDiff = data.by_difficulty || {};
                const makeRow = (typeValue, typeName) => {
                    const row = document.createElement('div');
                    row.className = 'overview-row';
                    [1,2,3,4,5].forEach(d => {
                        const cnt = (byDiff[String(typeValue)] && byDiff[String(typeValue)][String(d)]) || 0;
                        const card = document.createElement('div');
                        card.className = 'overview-card';
                        card.innerHTML = `
                            <div class=\"overview-title\">${typeName} · 难度${d}</div>
                            <div class=\"overview-count\">共${cnt}题</div>
                            <span class=\"overview-more\" data-type=\"${typeValue}\" data-diff=\"${d}\">详情 ›</span>
                        `;
                        row.appendChild(card);
                    });
                    return row;
                };
                rows.appendChild(makeRow(0, '单选题'));
                rows.appendChild(makeRow(2, '主观题'));

                // 绑定详情点击事件（事件委托到rows）
                rows.onclick = function(e){
                    const more = e.target.closest('.overview-more');
                    if (!more) return;
                    const qtype = parseInt(more.getAttribute('data-type'));
                    const diff = parseInt(more.getAttribute('data-diff'));
                    // 切换到覆盖视图
                    document.getElementById('overviewRows').style.display = 'none';
                    document.getElementById('overviewDetail').style.display = 'block';
                    document.getElementById('overviewStats').style.display = 'none';
                    overviewMode = 'list';
                    currentQType = qtype;
                    currentQDiff = diff;
                    loadQuestionList(qtype, diff);
                }
            })
            .catch(() => {
                rows.innerHTML = '<div style="color:#999;">统计加载失败</div>';
            });
        }

        // 加载“知识点&题目详情”
        function loadKnowledgePointOverview(){
            if (!currentSubject2) return;
            const stats = document.getElementById('kpOverviewStats');
            const grid = document.getElementById('kpOverviewGrid');
            grid.innerHTML = '<div style="color:#888; padding:8px 0;">加载中...</div>';
            stats.style.display = 'none';

            fetch('/console/app/knowledge_point_overview/', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ subject_2: currentSubject2 })
            })
            .then(r=>r.json())
            .then(data=>{
                const items = data.items || [];
                if (!items.length){
                    grid.innerHTML = '<div style="color:#999;">暂无知识点</div>';
                    return;
                }
                const total = items.reduce((s,i)=>s+(i.total||0),0);
                stats.innerHTML = `<div class="stat-item">共${items.length}个知识点 · 累计${total}题</div>`;
                stats.style.display = 'flex';

                grid.innerHTML = items.map(renderKnowledgeCard).join('');
                bindKpDetailButtons(items);
            })
            .catch(()=>{ grid.innerHTML = '<div style="color:#999;">加载失败</div>'; });
        }

        function renderKnowledgeCard(it){
            const byType = it.by_type || { '0': {}, '2': {} };
            const singles = ['1','2','3','4','5'].map(d=>`<div class="cell">单${d}:${(byType['0']||{})[d]||0}题</div>`).join('');
            const subjectives = ['1','2','3','4','5'].map(d=>`<div class="cell">主${d}:${(byType['2']||{})[d]||0}题</div>`).join('');
            return `<div class="kp-overview-card">`
                + `<div class="kp-overview-detail-btn">详情 ›</div>`
                + `<div class="kp-overview-title">${sanitizeText(it.name||'')}</div>`
                + `<div class="kp-overview-row" style="margin-top:2px;"><div class="label">题型分布：</div><div style="flex:1;"></div><div class="label">共${it.total||0}题</div></div>`
                + `<div class="kp-overview-diff-grid">${singles}${subjectives}</div>`
                + `</div>`;
        }

        function bindKpDetailButtons(items){
            const grid = document.getElementById('kpOverviewGrid');
            // 使用持久的 onclick，避免一次性监听导致返回后失效；
            // 同时避免重复 addEventListener 带来的多重触发
            grid.onclick = function(e){
                const btn = e.target.closest('.kp-overview-detail-btn');
                if (!btn) return;
                const card = btn.closest('.kp-overview-card');
                const idx = Array.from(grid.children).indexOf(card);
                const item = items[idx];
                if (!item) return;
                showKpDetail(item);
            };
        }

        function showKpDetail(item){
            const grid = document.getElementById('kpOverviewGrid');
            const stats = document.getElementById('kpOverviewStats');
            const panel = document.getElementById('kpDetailPanel');
            const title = document.getElementById('kpDetailTitle');
            const listEl = document.getElementById('kpDetailList');
            const select = document.getElementById('kpTypeDiffSelect');
            const labelEl = document.getElementById('kpTypeDiffLabel');
            const editBtn = document.getElementById('kpEdit');
            const tools = document.getElementById('kpEditTools');
            const bulkBtn = document.getElementById('kpBulkDelete');
            const exitBtn = document.getElementById('kpExitEdit');
            const confirmBox = document.getElementById('kpBulkConfirm');
            const bulkOk = document.getElementById('kpBulkOk');
            const bulkCancel = document.getElementById('kpBulkCancel');

            title.textContent = item.name || '';
            currentKpId = item.id;
            grid.style.display = 'none';
            stats.style.display = 'none';
            panel.style.display = 'block';
            listEl.innerHTML = '';
            kpDetailMode = 'list';
            if (labelEl) labelEl.style.display = 'none';
            if (select) {
                select.style.display = '';
                // 每次进入知识点详情时强制重置为默认占位项“题型&难度”
                select.value = '';
            }

            const fetchAndRender = () => {
                const val = select.value || '';
                currentKpFilterValue = val;
                const [qt, df] = val.split(',');
                loadKpQuestions(item.id, qt || '', df || '');
            };
            select.onchange = fetchAndRender;
            fetchAndRender();

            // 绑定题目内“详情”进入解析
            listEl.onclick = function(e){
                const isEdit = document.body.getAttribute('data-kpdetail-edit') === '1';
                if (isEdit) return;
                const btn = e.target.closest('.overview-more');
                if (!btn) return;
                const card = btn.closest('.question-item');
                const qid = btn.getAttribute('data-qid');
                const qtype = card ? card.getAttribute('data-type') : '';
                const diff = card ? card.getAttribute('data-diff') : '';
                // 内联实现详情展示，避免函数体放在样式块前导致的解析错误
                kpDetailMode = 'detail';
                try {
                    document.body.removeAttribute('data-kpdetail-edit');
                    if (tools) tools.style.display = 'none';
                    if (editBtn) editBtn.style.display = 'none';
                } catch (e) {}
                const labelEl = document.getElementById('kpTypeDiffLabel');
                const selectEl = document.getElementById('kpTypeDiffSelect');
                const typeText = String(qtype) === '0' ? '单选题' : '主观题';
                if (labelEl) { labelEl.textContent = `${typeText} · 难度${diff||''}`.replace(/\s·\s难度$/, ''); labelEl.style.display=''; }
                if (selectEl) selectEl.style.display='none';
                listEl.innerHTML = '<div style="color:#888; padding:8px 0;">加载详情...</div>';
                fetch('/console/app/question_detail/', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ question_id: qid }) })
                .then(r=>r.json())
                .then(data=>{
                    const c = data.content || {};
                    const title = (c.title||'').trim();
                    const rawAnalysis = (data.analysis||'').trim();
                    const analysisHtml = rawAnalysis ? renderMarkdownSafe(rawAnalysis) : '<span style="color:#999;">无解析</span>';
                    let bodyHtml = '';
                    if (data.question_type === 0){
                        const choices = c.choices || c.choice || [];
                        const answers = new Set(((c.choices_answer||c.choice_answer)||[]).map(a=>String(a).toUpperCase()));
                        const letters=['A','B','C','D','E','F'];
                        const opts = choices.map((opt,i)=>{
                            const letter = letters[i]||String.fromCharCode(65+i);
                            const isCorrect = answers.has(letter);
                            return `<div class=\"option ${isCorrect?'correct':''}\">${letter}. ${renderMarkdownSafe(opt)}</div>`;
                        }).join('');
                        bodyHtml = `<div style=\"padding:10px 0;\">${opts}</div>`;
                    }
                    listEl.innerHTML = `
                        <div class="question-item">
                            <div class="question-title">${renderMarkdownSafe(title)}</div>
                            ${bodyHtml}
                        </div>
                        <div class="question-item">
                            <div class="question-title">解析</div>
                            <div style="font-size:13px;color:#333;line-height:1.8;">${analysisHtml}</div>
                        </div>
                    `;
                    if (window.MathJax && window.MathJax.typeset) { window.MathJax.typeset(); }
                })
                .catch(()=>{ listEl.innerHTML = '<div style="color:#999;">详情加载失败</div>'; });
            };

            // 绑定编辑逻辑（与总览一致）
            editBtn.onclick = function(){
                document.body.setAttribute('data-kpdetail-edit', '1');
                tools.style.display = 'flex';
                editBtn.style.display = 'none';
                listEl.querySelectorAll('.edit-checkbox').forEach(cb => cb.style.display = 'block');
                // 与“题型&难度总览”一致：进入编辑态隐藏每题的“详情”按钮
                listEl.querySelectorAll('.overview-more').forEach(m => m.style.display = 'none');
                // 进入编辑时：隐藏知识点网格卡片上的详情按钮并显示选择框
                document.querySelectorAll('.kp-overview-detail-btn').forEach(d=> d.style.display='none');
                document.querySelectorAll('.kp-overview-card').forEach(card=>{
                    if (!card.querySelector('.kp-kp-checkbox')){
                        const cb = document.createElement('input');
                        cb.type = 'checkbox';
                        cb.className = 'kp-kp-checkbox';
                        cb.style.position = 'absolute';
                        cb.style.right = '12px';
                        cb.style.top = '10px';
                        card.appendChild(cb);
                    }
                });
            };
            exitBtn.onclick = function(){
                document.body.removeAttribute('data-kpdetail-edit');
                tools.style.display = 'none';
                editBtn.style.display = 'inline-block';
                listEl.querySelectorAll('.edit-checkbox').forEach(cb => { cb.checked = false; cb.style.display = 'none'; });
                // 恢复“详情”按钮可见
                listEl.querySelectorAll('.overview-more').forEach(m => m.style.display = '');
                document.querySelectorAll('.kp-overview-detail-btn').forEach(d=> d.style.display='');
                document.querySelectorAll('.kp-kp-checkbox').forEach(cb=> cb.remove());
            };
            bulkBtn.onclick = function(){
                const ids = Array.from(listEl.querySelectorAll('.edit-checkbox:checked')).map(cb => cb.getAttribute('data-id'));
                if (!ids.length) { alert('请选择要删除的题目'); return; }
                confirmBox.classList.add('active');
                const onOk = () => {
                    fetch('/console/app/question_bulk_delete/', {
                        method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ question_ids: ids })
                    }).then(r=>r.json()).then(()=>{
                        listEl.querySelectorAll('.edit-checkbox:checked').forEach(cb=>{ const item = cb.closest('.question-item'); if (item) item.remove(); });
                        confirmBox.classList.remove('active');
                        showSuccessMessage('已清除所选题目');
                    }).catch(()=>{ confirmBox.classList.remove('active'); alert('清除失败，请稍后再试'); });
                    bulkOk.removeEventListener('click', onOk);
                };
                const onCancel = () => { confirmBox.classList.remove('active'); bulkOk.removeEventListener('click', onOk); bulkCancel.removeEventListener('click', onCancel); };
                bulkOk.addEventListener('click', onOk);
                bulkCancel.addEventListener('click', onCancel);
            };
        }

        function loadKpQuestions(kid, qtype, diff){
            const listEl = document.getElementById('kpDetailList');
            listEl.innerHTML = '<div style="color:#888; padding:8px 0;">加载中...</div>';
            const payload = { subject_2: currentSubject2, knowledge_id: kid };
            if (qtype !== '') payload['question_type'] = qtype;
            if (diff !== '') payload['difficulty'] = diff;
            fetch('/console/app/knowledge_question_list/', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            })
            .then(r=>r.json())
            .then(data=>{
                const items = data.items || [];
                if (!items.length){ listEl.innerHTML = '<div style="color:#999;">暂无题目</div>'; return; }
                const html = items.map((it, idx)=>renderQuestion(it, idx)).join('');
                listEl.innerHTML = html;
                if (window.MathJax && window.MathJax.typeset) { window.MathJax.typeset(); }
            })
            .catch(()=>{ listEl.innerHTML = '<div style="color:#999;">加载失败</div>'; });
        }

        document.getElementById('kpDetailBack').addEventListener('click', function(){
            // 若在详情态，先返回题目列表
            if (kpDetailMode === 'detail') {
                kpDetailMode = 'list';
                const listEl = document.getElementById('kpDetailList');
                const select = document.getElementById('kpTypeDiffSelect');
                const labelEl = document.getElementById('kpTypeDiffLabel');
                const editBtn = document.getElementById('kpEdit');
                const tools = document.getElementById('kpEditTools');
                if (labelEl) labelEl.style.display = 'none';
                if (select) select.style.display = '';
                if (tools) tools.style.display = 'none';
                if (editBtn) editBtn.style.display = 'inline-block';
                const val = (select && select.value) ? select.value : (currentKpFilterValue || '');
                const [qt, df] = (val||'').split(',');
                loadKpQuestions(currentKpId, qt||'', df||'');
                return;
            }
            const grid = document.getElementById('kpOverviewGrid');
            const stats = document.getElementById('kpOverviewStats');
            const panel = document.getElementById('kpDetailPanel');
            panel.style.display = 'none';
            grid.style.display = 'grid';
            stats.style.display = 'flex';
        });

        // 加载题目列表并渲染（单选题高亮答案）
        function loadQuestionList(qtype, diff) {
            const listEl = document.getElementById('overviewQuestionList');
            const statsEl = document.getElementById('overviewStats');
            if (statsEl) statsEl.style.display = 'none';
            listEl.style.display = 'block';
            listEl.innerHTML = '<div style="color:#888; padding:8px 0;">加载中...</div>';

            fetch('/console/app/question_list/', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ subject_2: currentSubject2, question_type: qtype, difficulty: diff })
            })
            .then(r => r.json())
            .then(data => {
                const items = data.items || [];
                if (!items.length) {
                    listEl.innerHTML = '<div style="color:#999;">暂无题目</div>';
                    return;
                }
                const html = items.map((it, idx) => renderQuestion(it, idx)).join('');
                listEl.innerHTML = `<div style=\"font-size:14px;color:#666;margin:8px 0;\">${qtype===0?'单选题':'主观题'} · 难度${diff}（共${items.length}题）</div>` + html;
                // 绑定题目内详情
                bindQuestionDetail();
                // 列表模式显示编辑按钮
                const editBtn = document.getElementById('overviewEdit');
                if (editBtn) editBtn.style.display = 'inline-block';
                if (window.MathJax && window.MathJax.typeset) { window.MathJax.typeset(); }
            })
            .catch(() => {
                listEl.innerHTML = '<div style="color:#999;">题目加载失败</div>';
            });
        }

        function renderQuestion(item, idx) {
            const type = item.question_type;
            const c = item.content || {};
            const title = (c.title || '').trim();
            if (type === 0) {
                const choices = c.choices || c.choice || [];
                const answers = c.choices_answer || c.choice_answer || [];
                // 将答案（如 ["D"] 或 ["B"]）转换为索引
                const answerLetters = new Set((answers || []).map(a => String(a).trim().toUpperCase()));
                const letters = ['A','B','C','D','E','F'];
                const optionHtml = choices.map((opt, i) => {
                    const letter = letters[i] || String.fromCharCode(65+i);
                    const isCorrect = answerLetters.has(letter);
                    const html = renderMarkdownSafe(opt);
                    return `<div class=\"option ${isCorrect?'correct':''}\">${letter}. ${html}</div>`;
                }).join('');
                return `<div class=\"question-item\" data-id=\"${item.id}\" data-type=\"${item.question_type}\" data-diff=\"${item.difficulty}\"><input class=\"edit-checkbox\" type=\"checkbox\" data-id=\"${item.id}\" style=\"display:none;\"/><span class=\"overview-more\" data-qid=\"${item.id}\">详情 ›</span><div class=\"question-title\">${idx+1}. ${renderMarkdownSafe(title)}</div><div class=\"question-options\">${optionHtml}</div></div>`;
            } else {
                // 题干图片支持：若含 img 标签则原样输出图片，其它文本做清洗
                const safeTitle = renderMarkdownSafe(title);
                return `<div class=\"question-item\" data-id=\"${item.id}\" data-type=\"${item.question_type}\" data-diff=\"${item.difficulty}\"><input class=\"edit-checkbox\" type=\"checkbox\" data-id=\"${item.id}\" style=\"display:none;\"/><span class=\"overview-more\" data-qid=\"${item.id}\">详情 ›</span><div class=\"question-title\">${idx+1}. ${safeTitle}</div></div>`;
            }
        }

        function bindQuestionDetail(){
            const listEl = document.getElementById('overviewQuestionList');
            listEl.addEventListener('click', function(e){
                const isEdit = document.body.getAttribute('data-overview-edit') === '1';
                if (!isEdit) {
                    const btn = e.target.closest('.overview-more');
                    if (!btn) return;
                    const qid = btn.getAttribute('data-qid');
                    overviewMode = 'detail';
                    showQuestionDetail(qid);
                }
            });

            // 复选框切换：采用事件委托，点击题卡右上角小方框切换勾选
            listEl.addEventListener('change', function(e){
                const isEdit = document.body.getAttribute('data-overview-edit') === '1';
                if (!isEdit) return;
                const cb = e.target.closest('.edit-checkbox');
                if (!cb) return;
                // 浏览器默认会切换checked，这里无需手动处理
            });
        }

        function showQuestionDetail(qid){
            // 隐藏统计条（已在进入list时隐藏）
            const listEl = document.getElementById('overviewQuestionList');
            const statsEl = document.getElementById('overviewStats');
            if (statsEl) statsEl.style.display = 'none';
            listEl.innerHTML = '<div style="color:#888; padding:8px 0;">加载详情...</div>';
            // 解析页不显示编辑相关按钮
            try {
                document.body.removeAttribute('data-overview-edit');
                const tools = document.getElementById('overviewEditTools');
                const editBtn = document.getElementById('overviewEdit');
                if (tools) tools.style.display = 'none';
                if (editBtn) editBtn.style.display = 'none';
            } catch (e) {}
            fetch('/console/app/question_detail/', {
                method:'POST',
                headers:{'Content-Type':'application/json'},
                body: JSON.stringify({ question_id: qid })
            })
            .then(r=>r.json())
            .then(data=>{
                const c = data.content || {};
                const title = (c.title||'').trim();
                const rawAnalysis = (data.analysis||'').trim();
                const analysis = rawAnalysis ? renderMarkdownSafe(rawAnalysis) : '<span style="color:#999;">无解析</span>';
                let bodyHtml = '';
                if (data.question_type === 0){
                    const choices = c.choices || c.choice || [];
                    const answers = new Set(((c.choices_answer||c.choice_answer)||[]).map(a=>String(a).toUpperCase()));
                    const letters=['A','B','C','D','E','F'];
                    const opts = choices.map((opt,i)=>{
                        const letter = letters[i]||String.fromCharCode(65+i);
                        const isCorrect = answers.has(letter);
                        return `<div class=\"option ${isCorrect?'correct':''}\">${letter}. ${renderMarkdownSafe(opt)}</div>`;
                    }).join('');
                    bodyHtml = `<div style=\"padding:10px 0;\">${opts}</div>`;
                }
                listEl.innerHTML = `
                    <div class="question-item">
                        <div class="question-title">${renderMarkdownSafe(title)}</div>
                        ${bodyHtml}
                    </div>
                    <div class="question-item">
                        <div class="question-title">解析</div>
                        <div style="font-size:13px;color:#333;line-height:1.8;">${analysis}</div>
                    </div>
                `;
                if (window.MathJax && window.MathJax.typeset) { window.MathJax.typeset(); }
            })
            .catch(()=>{
                listEl.innerHTML = '<div style="color:#999;">详情加载失败</div>';
            });
        }

        // 覆盖视图：返回按钮
        document.getElementById('overviewBack').addEventListener('click', function(){
            if (overviewMode === 'detail') {
                // 返回题目列表
                overviewMode = 'list';
                loadQuestionList(currentQType, currentQDiff);
                return;
            }
            // 返回统计卡片
            document.getElementById('overviewDetail').style.display = 'none';
            document.getElementById('overviewRows').style.display = 'flex';
            document.getElementById('overviewQuestionList').innerHTML = '';
            document.getElementById('overviewStats').style.display = 'flex';
            overviewMode = 'grid';
        });

        // 进入/退出编辑模式
        (function bindOverviewEdit(){
            const editBtn = document.getElementById('overviewEdit');
            const tools = document.getElementById('overviewEditTools');
            const listEl = document.getElementById('overviewQuestionList');
            const bulkBtn = document.getElementById('bulkDelete');
            const exitBtn = document.getElementById('exitEdit');

            if (!editBtn) return;

            editBtn.addEventListener('click', function(){
                // 仅在题目列表视图可进入编辑
                if (overviewMode !== 'list') return;
                document.body.setAttribute('data-overview-edit', '1');
                // 显示工具与复选框，隐藏每题详情
                tools.style.display = 'flex';
                editBtn.style.display = 'none';
                listEl.querySelectorAll('.edit-checkbox').forEach(cb => cb.style.display = 'block');
                listEl.querySelectorAll('.overview-more').forEach(m => m.style.display = 'none');
            });

            exitBtn.addEventListener('click', function(){
                document.body.removeAttribute('data-overview-edit');
                tools.style.display = 'none';
                editBtn.style.display = 'inline-block';
                listEl.querySelectorAll('.edit-checkbox').forEach(cb => { cb.checked = false; cb.style.display = 'none'; });
                listEl.querySelectorAll('.overview-more').forEach(m => m.style.display = '');
            });

            const confirmBox = document.getElementById('bulkConfirm');
            const bulkOk = document.getElementById('bulkOk');
            const bulkCancel = document.getElementById('bulkCancel');

            bulkBtn.addEventListener('click', function(){
                const ids = Array.from(listEl.querySelectorAll('.edit-checkbox:checked')).map(cb => cb.getAttribute('data-id'));
                if (!ids.length) { alert('请选择要清除的题目'); return; }
                confirmBox.classList.add('active');

                const onOk = () => {
                    fetch('/console/app/question_bulk_delete/', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ question_ids: ids })
                    })
                    .then(r => r.json())
                    .then(data => {
                        listEl.querySelectorAll('.edit-checkbox:checked').forEach(cb => {
                            const item = cb.closest('.question-item');
                            if (item) item.remove();
                        });
                        confirmBox.classList.remove('active');
                        showSuccessMessage('已清除所选题目');
                    })
                    .catch(() => { confirmBox.classList.remove('active'); alert('清除失败，请稍后再试'); });

                    bulkOk.removeEventListener('click', onOk);
                };

                const onCancel = () => {
                    confirmBox.classList.remove('active');
                    bulkOk.removeEventListener('click', onOk);
                    bulkCancel.removeEventListener('click', onCancel);
                };

                bulkOk.addEventListener('click', onOk);
                bulkCancel.addEventListener('click', onCancel);
            });
        })();
    </script>
</body>
</html>
