import os

from app.core.prompt.prompt_form.entities import PromptFormEntity
from app.core.utils.yaml_utils import load_yaml_file


class PromptFormInstance:
    form_schema: PromptFormEntity | None = None

    def get_form_schema(self) -> PromptFormEntity:
        if self.form_schema is not None:
            return self.form_schema

        current_path = os.path.abspath(__file__)
        schema_yaml_path = os.path.join(os.path.dirname(current_path), 'chat_prompt_form.yaml')
        yaml_data = load_yaml_file(schema_yaml_path, ignore_error=True)

        new_parameter_rules = []
        for parameter_rule in yaml_data.get('parameter_rules', []):
            if 'default' in parameter_rule:
                parameter_rule['default'] = parameter_rule['default'].replace('\\n', '\n')
            if 'label' not in parameter_rule:
                parameter_rule['label'] = parameter_rule['name']
            new_parameter_rules.append(parameter_rule)

        yaml_data['parameter_rules'] = new_parameter_rules

        try:
            # yaml_data to entity
            form_schema = PromptFormEntity(**yaml_data)
        except Exception as e:
            raise Exception(f'Invalid form schema for {schema_yaml_path}: {str(e)}')

        # cache model schemas
        self.form_schema = form_schema

        return form_schema
