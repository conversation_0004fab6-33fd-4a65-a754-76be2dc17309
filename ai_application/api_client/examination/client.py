import logging

from api_client.base_client import BaseClient
from api_client.decorators import catch_error

logger = logging.getLogger(__name__)

__all__ = ['examination_client']


class ExaminationClient(BaseClient):
    host = 'http://examination-svc:8000'
    host_local = 'https://examination.kaoyan-vip.cn'

    @property
    def base_url(self):
        return f'{self.host}/internal_api/v1'

    @catch_error({'total_page': 1, 'count': 0, 'current_page': 1, 'size': 1, 'results': []})
    def search_questions(self, params: dict, page: int = 1, size: int = 2) -> dict:
        """
        :param params:
            num: 问题id
            nums: 多个问题id
            examination_library: 题库uid，多个逗号分隔
            examination_source_kind: 类型 0:非真题， 1:真题
            examination_source_exam_type: 考试类型 0:非统考，1统考
            examination_knowledge_point: 知识点id，多个逗号分隔
            page
        :param page:
        :param size:
        :return:
        """
        url = '/library/question/list_by_post/'
        return self._post(url, data=params, params={'page': page, 'size': size}, timeout=5)

    def get_question_detail(self, question_id) -> dict:
        url = f'/library/question/{question_id}/'
        return self._get(url)


examination_client = ExaminationClient()
