import pickle
from functools import cached_property
from pathlib import Path

from django.db import models

from ._base import BaseModel
from app.core.rag.index_processor.constant.index_type import IndexType


class Dataset(BaseModel):
    account = models.ForeignKey('app.Account', on_delete=models.CASCADE, db_constraint=False)
    dataset_no = models.CharField('资料库编号', max_length=36, unique=True)
    name = models.CharField('资料库名称', max_length=100, blank=True)
    description = models.TextField('资料库描述', blank=True)
    index_struct = models.JSONField('索引结构', null=True)
    embedding_model_provider = models.CharField('嵌入模型提供方', max_length=100, blank=True)
    embedding_model = models.CharField('嵌入模型', max_length=100, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '资料库'
        db_table = 'ai_dataset'

    @staticmethod
    def gen_collection_name_by_id(dataset) -> str:
        normalized_dataset_id = str(dataset.id)
        return f'vector_index_node_{normalized_dataset_id}'

    @cached_property
    def available_document_count(self):
        return self.datasetdocument_set.filter(is_deleted=False, indexing_status='completed').count()

    @cached_property
    def available_segment_count(self):
        return self.documentsegment_set.filter(is_deleted=False, status='completed').count()

    @property
    def latest_process_rule(self):
        return self.datasetprocessrule_set.filter(is_deleted=False).order_by('-id').first()


class DatasetProcessRule(BaseModel):
    dataset = models.ForeignKey(Dataset, on_delete=models.CASCADE, db_constraint=False)
    rules = models.JSONField('处理规则', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '资料库处理规则'
        db_table = 'ai_dataset_process_rule'

    DEFAULT_RULES = {
        'segmentation': {
            'chunk_size': 500,
            'chunk_overlap': 50,
            'separator': '\n'
        }
    }


class DatasetDocument(BaseModel):
    document_no = models.CharField('文档编号', max_length=36, unique=True)
    dataset = models.ForeignKey(Dataset, on_delete=models.CASCADE, db_constraint=False)
    dataset_process_rule = models.ForeignKey(
        DatasetProcessRule, on_delete=models.CASCADE, db_constraint=False, null=True)
    name = models.CharField('文档名称', max_length=255, blank=True)

    # remote_file knowledge_doc
    data_source_type = models.CharField('数据源类型', max_length=40, blank=True)
    data_source_info = models.JSONField('数据源信息', null=True)
    index_type = models.CharField('索引类型', max_length=40, default=IndexType.PARAGRAPH_INDEX.value)

    # 知识点相关配置
    original_knowledge_document_no = models.CharField('原始文档编号', max_length=36, blank=True)

    # start processing
    processing_started_at = models.DateTimeField('开始处理时间', null=True)

    # parsing
    word_count = models.IntegerField('字数', default=0)
    parsing_completed_at = models.DateTimeField('解析完成时间', null=True)

    # split
    splitting_completed_at = models.DateTimeField('分片完成时间', null=True)

    # indexing
    tokens = models.IntegerField('tokens', default=0)
    indexing_latency = models.FloatField('索引耗时', default=0)
    completed_at = models.DateTimeField('索引完成时间', null=True)

    # error
    error = models.TextField('错误信息', blank=True)
    stopped_at = models.DateTimeField('停止时间', null=True)

    # 预计状态 paused, waiting, parsing, cleaning, splitting, indexing, completed, error
    # 目前状态 parsing, splitting, indexing, completed, error
    indexing_status = models.CharField('索引状态', max_length=40, blank=True)
    enabled = models.BooleanField('是否启用', default=True)
    doc_type = models.CharField('文档类型', max_length=40, blank=True)
    doc_metadata = models.JSONField('文档元数据', null=True)
    doc_language = models.CharField('文档语言', max_length=40, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '文档'
        db_table = 'ai_dataset_document'

    # @property
    # def index_type(self):
    #     return IndexType.PARAGRAPH_INDEX.value

    @property
    def cal_status(self):
        if self.indexing_status in ['completed', 'error']:
            return self.indexing_status
        else:
            return 'indexing'

    @property
    def file_extension(self):
        suffix = Path(self.data_source_info['url']).suffix
        return suffix[1:]


class DocumentSegment(BaseModel):
    dataset = models.ForeignKey(Dataset, on_delete=models.CASCADE, db_constraint=False)
    dataset_document = models.ForeignKey(DatasetDocument, on_delete=models.CASCADE, db_constraint=False)
    position = models.IntegerField('段落位置', default=0)
    content = models.TextField('内容', blank=True)
    word_count = models.IntegerField('字数', default=0)
    tokens = models.IntegerField('tokens', default=0)

    # indexing fields
    index_node_id = models.CharField('索引节点ID', max_length=255, blank=True)
    index_node_hash = models.CharField('索引节点Hash', max_length=255, blank=True)

    # indexing, completed, re_segment
    hit_count = models.IntegerField('命中次数', default=0)
    status = models.CharField('状态', max_length=40, blank=True)
    indexing_at = models.DateTimeField('索引时间', null=True)
    completed_at = models.DateTimeField('完成时间', null=True)
    error = models.TextField('错误信息', null=True)
    stopped_at = models.DateTimeField('停止时间', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '文档分段'
        db_table = 'ai_document_segments'


class Embedding(BaseModel):
    provider_name = models.CharField('嵌入模型提供方', max_length=100, blank=True)
    model_name = models.CharField('嵌入模型', max_length=100, blank=True)
    text_hash = models.CharField('文本Hash', max_length=64, blank=True, db_index=True)
    embedding = models.BinaryField('嵌入向量', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '向量内容缓存'
        db_table = 'ai_embeddings'

    def set_embedding(self, embedding_data: list[float]):
        self.embedding = pickle.dumps(embedding_data, protocol=pickle.HIGHEST_PROTOCOL)

    def get_embedding(self) -> list[float]:
        return pickle.loads(self.embedding)


class DatasetQuery(BaseModel):
    dataset = models.ForeignKey(Dataset, on_delete=models.CASCADE, db_constraint=False)
    app = models.ForeignKey('app.App', on_delete=models.CASCADE, db_constraint=False)
    message = models.ForeignKey('app.Message', on_delete=models.CASCADE, db_constraint=False)
    content = models.TextField('查询内容', blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '资料库查询记录'
        db_table = 'ai_dataset_queries'


class DatasetRetrieverResource(BaseModel):
    message = models.ForeignKey('app.Message', on_delete=models.CASCADE, db_constraint=False)
    position = models.IntegerField('段落位置', default=0)
    dataset = models.ForeignKey(Dataset, on_delete=models.CASCADE, db_constraint=False)
    dataset_name = models.CharField('资料库名称', max_length=100, blank=True)
    dataset_document = models.ForeignKey(DatasetDocument, on_delete=models.CASCADE, db_constraint=False)
    dataset_document_name = models.CharField('文档名称', max_length=255, blank=True)
    data_source_type = models.CharField('数据源类型', max_length=40, blank=True)
    segment = models.ForeignKey(DocumentSegment, on_delete=models.CASCADE, db_constraint=False)
    score = models.FloatField('得分', null=True)
    content = models.TextField('内容', null=True)
    content_with_context = models.JSONField('包含上下文内容', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '检索资源记录'
        db_table = 'ai_dataset_retriever_resources'
