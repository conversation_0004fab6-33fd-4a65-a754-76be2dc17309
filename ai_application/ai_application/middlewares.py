import logging

logger = logging.getLogger(__name__)


class LoggerMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.get_full_path() == '/check/beat/':
            return self.get_response(request)

        # log_msg = f"request_url: {request.get_full_path()} | headers:{request.headers} | body: {request.body}"
        log_msg = f"request_url: {request.get_full_path()}"
        logger.info(log_msg)
        return self.get_response(request)
