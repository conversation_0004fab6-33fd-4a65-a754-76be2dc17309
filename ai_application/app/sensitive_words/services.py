import os


class SensitiveWord:

    def __init__(self):
        self.sensitive_words = self._init_sensitive_words()

    def _init_sensitive_words(self) -> list:
        current_path = os.path.abspath(__file__)
        sq_words_path = os.path.join(os.path.dirname(current_path), 'stop_words/sq.txt')
        zj_words_path = os.path.join(os.path.dirname(current_path), 'stop_words/zj.txt')
        zz_words_path = os.path.join(os.path.dirname(current_path), 'stop_words/zz.txt')

        """
        请注意：为尊重不同的宗教信仰，请避免使用可能引发争议或误解的词汇。
        温馨提示：请保持交流内容的健康与文明，避免任何涉及色情或暴力的内容。
        提醒您：我们倡导平等与尊重，请勿使用具有种族或地域歧视倾向的词语。
        特别提示：为保持平台的非政治性，请不要发表有关政治立场的观点。
        """
        path_map = {
            'sq': {
                'path': sq_words_path,
                'tips': '温馨提示：请保持交流内容的健康与文明，避免任何涉及色情或暴力的内容。',
            },
            'zj': {
                'path': zj_words_path,
                'tips': '请注意：为尊重不同的宗教信仰，请避免使用可能引发争议或误解的词汇。',
            },
            'zz': {
                'path': zz_words_path,
                'tips': '特别提示：为保持平台的非政治性，请不要发表有关政治立场的观点。'
            }
        }
        for i in path_map.values():
            if not os.path.exists(i['path']):
                raise FileNotFoundError(f"Failed to load sensitive_words file {i['path']}: file not found")

        all_words = []
        for k, v in path_map.items():
            words_ = []
            with open(v['path'], 'r', encoding='utf-8') as f:
                for line in f:
                    w = line.strip()
                    if w:
                        words_.append(w)
            all_words.append({
                'words': words_,
                'tips': v['tips'],
            })
        return all_words


sensitive_word_service = SensitiveWord()
