# 阿纪

from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import CompletionAppGenerateEntity, ChatAppGenerateEntity
from app.core.features.math_problem_solving import MathProblemSolvingFeature
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import Message


class MathProblemSolvingWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)


    def run(self):
        if self.application_generate_entity.file_objs:
            image_url=self.application_generate_entity.file_objs
            question = self._analysis_img(image_url)
            result = self._get_math_result(question)
        else:
            result = self._get_math_result(self.application_generate_entity.query)

        self._publish_workflow_end_message()
        return result


    def _analysis_img(self, image_url: list) -> str:
        self.math_problem_feature = MathProblemSolvingFeature(app_no='math_problem_recognition')
        result = self.math_problem_feature.run(
            self.application_generate_entity,
            stream=False,
            file_objs = image_url,
            query=''
        )

        tracing_info = {
            'tracing_type': 'math_problem_recognition',
            'query': image_url
        }

        if tracing_info:
            self.tracing_log.add_tracing_by_llm_result(
                tracing_type=tracing_info.get('tracing_type'),
                query=tracing_info.get('query'),
                llm_result=result
            )

        return result.message.content

    def _get_math_result(self, question: str) -> LLMResult:
        self.math_problem_feature = MathProblemSolvingFeature(app_no='math_problem_solving')
        result = self.math_problem_feature.run(
            self.application_generate_entity,
            stream=True,
            query= question
        )

        tracing_info = {
            'tracing_type': 'math_problem_solving',
            'query': question
        }
        return self._handle_node_generate_response(result, tracing_info=tracing_info)