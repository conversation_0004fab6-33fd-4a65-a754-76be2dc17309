from collections import defaultdict

from django.views.generic import TemplateView
from django.http import JsonResponse
from app.models import KnowledgeSimple, KnowledgeVideo, TexttoVideo, DataStructureKnowledge
from app.services.text_to_video_2 import get_query_knowledge
import json
import requests
import time


class TextToVideoView(TemplateView):
    template_name = 'app/text_to_video.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        all_kg_videos = KnowledgeVideo.objects.filter(is_deleted=False)
        all_kg_videos_map = defaultdict(lambda: defaultdict(dict))
        for i in all_kg_videos:
            all_kg_videos_map[i.core_course_name][i.knowledge_name] = i.video

        # 获取全部科目知识点
        all_kgs = DataStructureKnowledge.objects.filter(is_deleted=False)
        all_kgs_map = defaultdict(lambda: defaultdict(list))
        for i in all_kgs:
            video = all_kg_videos_map[i.subject_2][i.knowledge_points]
            all_kgs_map[i.subject_1][i.subject_2].append({
                'id': i.id,
                'subject_2': i.subject_2,
                'name': i.knowledge_points,
                'desc': i.desc,
                'video_url': video or '',
                'image_url': '',
            })

        category_structure = {}
        first_kp = None
        for subject_1, subject_2_dict in all_kgs_map.items():
            if subject_1 not in category_structure:
                category_structure[subject_1] = {}
            for subject_2, kps in subject_2_dict.items():
                if first_kp is None and kps:
                    first_kp = kps[0]
                category_structure[subject_1][subject_2] = kps

        context['first_kp'] = first_kp
        context['category_structure'] = category_structure
        return context
    

class TextToVideoViewSearch(TemplateView):
    template_name = 'app/text_to_video_search.html'

    def save_text(self, request, *args, **kwargs):
        try:
            modified_items = json.loads(request.POST.get('modified_items', '[]'))
            print(modified_items)
            return JsonResponse({'status': 'success'})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    def save_audio(self, request, *args, **kwargs):
        try:
            modified_items = json.loads(request.POST.get('modified_items', '[]'))
            print(modified_items)
            return JsonResponse({'status': 'success'})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    def generate_video(self, request, *args, **kwargs):
        try:
            # 获取前端传递的原始数据和修改内容
            original_script = json.loads(request.POST.get('original_script', '[]'))
            modified_text = json.loads(request.POST.get('modified_text', '[]'))
            modified_audio = json.loads(request.POST.get('modified_audio', '[]'))
            kp_name = request.POST.get('kp_data[name]', '')
            kp_definition = request.POST.get('kp_data[definition]','')
            kp={"name":kp_name,"definition":kp_definition}
            if not original_script or not kp:
                return JsonResponse({'error': '缺少必要数据'}, status=400)

            # 合并修改内容
            final_script = []
            for item in original_script:
                text_mod = next((x for x in modified_text if x['id'] == item['id']), None)
                print(text_mod)
                audio_mod = next((x for x in modified_audio if x['id'] == item['id']), None)
                
                final_script.append({
                    'id': item['id'],
                    'title': item['title'],
                    'text': text_mod['text'] if text_mod else item['text'],
                    'audio': audio_mod['audio'] if audio_mod else item['audio']
                })

            # 为脚本添加必需的字段
            processed_script = []
            for item in final_script:
                processed_item = {
                    'id': item.get('id'),
                    'title': item.get('title'),
                    'text': item.get('text'),
                    'audio': item.get('audio'),
                    'generate_figure': False,  # 默认不生成图示
                    'figure_code': ''  # 默认空字符串
                }
                processed_script.append(processed_item)
            
            # 调用异步视频生成接口
            task_data = {
                'name': kp['name'],
                'definition': kp['definition'],
                'ppt_script_list': processed_script
            }
            
            try:
                # 创建异步任务
                create_response = requests.post(
                    'http://127.0.0.1:8000/internal_api/knowledge_video_task/create',
                    json=task_data,
                    timeout=60
                )
                
                if create_response.status_code != 200:
                    return JsonResponse({'error': f'创建任务失败: {create_response.text}'}, status=400)
                
                task_result = create_response.json()
                task_id = task_result.get('task_id')
                
                if not task_id:
                    return JsonResponse({'error': '创建任务失败，未获取到task_id'}, status=400)
                
                # 轮询任务状态
                max_attempts = 120  # 最多等待20分钟
                attempt = 0
                
                while attempt < max_attempts:
                    status_response = requests.get(
                        'http://127.0.0.1:8000/internal_api/knowledge_video_task/status',
                        params={'task_id': task_id},
                        timeout=30
                    )
                    
                    if status_response.status_code != 200:
                        time.sleep(10)
                        attempt += 1
                        continue
                    
                    status_result = status_response.json()
                    task_status = status_result.get('status')
                    
                    if task_status == 'completed':
                        video_url = status_result.get('video_url')
                        if video_url:
                            video_result = {'error': False, 'video_url': video_url}
                            
                            # 写入数据库
                            knowledge = KnowledgeSimple.objects.filter(
                                course_id=467,
                                definition__isnull=False,
                                is_usable=1,
                                name=kp['name']
                            ).first()

                            if knowledge:
                                try:
                                    from django.db import transaction
                                    with transaction.atomic():
                                        # 检查是否已存在记录
                                        existing_video = KnowledgeVideo.objects.filter(name=knowledge).first()
                                        if existing_video:
                                            # 更新现有记录
                                            existing_video.video = video_url
                                            existing_video.save()
                                            print(f"更新视频记录: {knowledge.name} -> {video_url}")
                                        else:
                                            # 创建新记录
                                            KnowledgeVideo.objects.create(
                                                video=video_url,
                                                name=knowledge
                                            )
                                            print(f"创建视频记录: {knowledge.name} -> {video_url}")
                                        
                                        # 验证记录是否保存成功
                                        saved_video = KnowledgeVideo.objects.filter(name=knowledge).first()
                                        if not saved_video or saved_video.video != video_url:
                                            raise Exception("验证保存失败")
                                            
                                    return JsonResponse({
                                        'status': 'success',
                                        'video_url': video_url,
                                        'saved': True
                                    })
                                except Exception as e:
                                    import traceback
                                    print(f"保存视频记录失败: {str(e)}")
                                    print(traceback.format_exc())
                                    return JsonResponse({
                                        'error': f'数据库操作失败: {str(e)}',
                                        'video_url': video_url,
                                        'saved': False
                                    }, status=500)
                            else:
                                return JsonResponse({
                                    'error': f'未找到知识点记录: {kp["name"]}',
                                    'video_url': video_url
                                }, status=404)
                        
                            break
                        else:
                            return JsonResponse({'error': '任务完成但未获取到视频链接'}, status=400)
                    elif task_status == 'failed':
                        error_msg = status_result.get('error', '未知错误')
                        return JsonResponse({'error': f'视频生成失败: {error_msg}'}, status=400)
                    elif task_status in ['pending', 'processing']:
                        time.sleep(10)
                        attempt += 1
                    else:
                        time.sleep(10)
                        attempt += 1
                
                if attempt >= max_attempts:
                    return JsonResponse({'error': '视频生成超时，请稍后重试'}, status=400)
                
            except requests.RequestException as e:
                return JsonResponse({'error': f'调用视频生成服务失败: {str(e)}'}, status=500)
            
            if video_result.get('error'):
                return JsonResponse({'error': video_result['message']}, status=400)
            
            return JsonResponse({
                'status': 'success',
                'video_url': video_result.get('video_url')
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    def get_history(self, request, *args, **kwargs):
        try:
            # 获取用户信息
            user_info = request.POST.get('user_info', '{}')
            user_info = json.loads(user_info)
            user_id = user_info.get('id', '')
            
            if not user_id:
                return JsonResponse({'error': '缺少用户信息'}, status=400)
            
            # 获取页码参数，默认为1
            page = int(request.POST.get('page', 1))
            per_page = 10  # 每页10条记录
            
            # 获取查询集
            history_records = TexttoVideo.objects.filter(
                user_info=user_info
            ).order_by('-add_time').values('user_info', 'name', 'video', 'script', 'add_time')
            
            # 使用分页器
            from django.core.paginator import Paginator
            paginator = Paginator(history_records, per_page)
            page_obj = paginator.get_page(page)
            
            # 格式化时间
            history_list = []
            for record in page_obj.object_list:
                history_list.append({
                    'username': record['user_info']['name'],
                    'name': record['name'],
                    'video': record['video'],
                    'script': record['script'],
                    'time': record['add_time'].strftime('%Y-%m-%d %H:%M:%S')
                })
            
            return JsonResponse({
                'status': 'success',
                'history': list(history_list),
                'total_pages': paginator.num_pages,
                'current_page': page
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    def post(self, request, *args, **kwargs):
        if request.path.endswith('/save_text'):
            return self.save_text(request, *args, **kwargs)

        elif request.path.endswith('/save_audio'):
            return self.save_audio(request, *args, **kwargs)
        
        elif request.path.endswith('/generate_video'):
            return self.generate_video(request, *args, **kwargs)
        
        elif request.path.endswith('/get_history'):
            return self.get_history(request, *args, **kwargs)
        
        query = request.POST.get('query', '').strip()

        if not query:
            return JsonResponse({'error': '请输入搜索内容'}, status=400)

        try:
            result = get_query_knowledge(query)
            if result.get('error'):
                return JsonResponse({'error': result['message']}, status=400)
            
            script = result['script']
            kp = result.get('kp', '')
            # 提取text和audio内容
            text_content = []
            audio_content = []
            title = []
            for item in script:
                text_content.append(f"{item['id']} {item['text']}")
                audio_content.append(f"{item['id']} {item['audio']}")
                title.append(f"{item['id']} {item['title']}")
            
            return JsonResponse({
                'text': '\n'.join(text_content),
                'audio': '\n'.join(audio_content),
                'kp': kp,
                'title':'\n'.join(title),
                'script': script
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
