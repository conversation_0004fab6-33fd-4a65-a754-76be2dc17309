import time

from django.conf import settings
from django.core.management import BaseCommand
import requests

from app.models.model import App, Message


class Command(BaseCommand):

    def add_arguments(self, parser):
        parser.add_argument("app_no", type=str)

    def get_userinfo(self, message_no_list, app_type):
        response = requests.post(
            f'{settings.YANTUCS_API_URL}/internal_api/ai_application_api/get_user_id_by_msg_id',
            json={'msg_id_list': message_no_list, 'app_type': app_type},
            timeout=10,
        )
        if response.status_code == 200:
            x = response.json()['data']['data_map']
            return x
        return {}

    def get_app_type(self, app_no: str):
        if app_no == ['app_28482099ed2945e2', 'app_c04f55acabe94352']:
            return 'chat'
        if app_no == 'knowledge_query':
            return 'knowledge'
        if app_no == 'complex_sentence_analysis':
            return 'complex_sentence'

    def handle(self, *app_labels, **options):
        app_no = options['app_no']
        app = App.objects.get(app_no=app_no)

        message_list = Message.objects.filter(app_id=app.id)
        total = message_list.count()

        idx = 1
        step = 50
        count = 0
        while count < total:
            ms = message_list[(idx - 1) * step:idx * step]

            message_no_list = [m.message_no for m in ms if m.invoke_from == 'api']
            x = self.get_userinfo(
                message_no_list=message_no_list,
                app_type=self.get_app_type(app_no)
            )
            for m in ms:
                if m.userinfo:
                    continue
                if m.invoke_from == 'api':
                    d = x.get(m.message_no)
                    if not d:
                        continue
                    user_type = 'admin' if '管理员' in d[1] else 'user'
                    m.userinfo = {'user_id': d[0], 'user_name': d[1], 'user_type': user_type}
                elif m.invoke_from == 'console':
                    m.userinfo = {'user_id': 0, 'user_name': 'console调试', 'user_type': ''}

            # bulk update
            Message.objects.bulk_update(ms, fields=['userinfo'])
            time.sleep(0.01)
            count += len(ms)
            idx += 1

        self.stdout.write(self.style.SUCCESS('Successfully'))
