import json
from app.api.waikan_reader_api.get_question_detail import get_question_detail
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.features.WaikanAgainFeature import WaikanAgainFeature
from app.core.features.WaikanFeature import WaikanFeature
from app.core.features.code_optimization import CodeOptimizationFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError
from app.models import Message


class ChatWaikanAgainWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

    def run(self):
        try:

            query_dict = json.loads(self.application_generate_entity.query)
            query = query_dict.get('content')
            waikan_option = query_dict.get('waikan_option')
            weak_points = query_dict.get('weak_points')
            waikan_article = query_dict.get('waikan_article')
            system_messages= query_dict.get('system_messages')


        except Exception:
            raise ParameterError(detail='外刊专项出题参数错误')
        if query == "":
            self.waikan_feature = WaikanAgainFeature(app_no='waikan_question_second')
            result = self.waikan_feature.run(
                application_generate_entity=self.application_generate_entity,
                query="",
                weak_points=system_messages,
                option=waikan_option,
                article = waikan_article,
                stream=True,

            )

            tracing_info = {
                'tracing_type': 'waikan_question_second', 'query': system_messages
            }
            self._handle_node_generate_response(result, tracing_info=tracing_info)
            self._publish_workflow_end_message()

        else:

            self.waikan_feature = WaikanAgainFeature(app_no='waikan_report_second')
            result = self.waikan_feature.run(
                application_generate_entity=self.application_generate_entity,
                query=query,
                weak_points=system_messages,
                option=waikan_option,
                article=waikan_article,
                stream=True,

            )

            tracing_info = {
                'tracing_type': 'waikan_report_second', 'query': query
            }
            self._handle_node_generate_response(result, tracing_info=tracing_info)
            self._publish_workflow_end_message()
