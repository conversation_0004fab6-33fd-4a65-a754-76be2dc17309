import json
import random
from app.errors import QuestionNotFoundError
from app.models import EnglishWaikanQuestionBank, EnglishWaikanSubQuestion, EnglishWaikanTestRecord
import logging

# 设置日志记录
logger = logging.getLogger(__name__)

class WaiKanQuestionService:

    @classmethod
    def get_question_detail(cls, user_id):
        done_question_ids = EnglishWaikanTestRecord.objects.filter(user_id=user_id).values_list('question_id',
                                                                                            flat=True)
        done_question_ids = set(done_question_ids)

        # 获取所有指定范围内的记录的 id 列表
        all_question_ids = set(EnglishWaikanQuestionBank.objects.filter(id__range=(36, 280)).values_list('id', flat=True))
        print(f"all_question_ids:{all_question_ids}, done_question_ids:{done_question_ids}")
        available_question_ids = all_question_ids - done_question_ids
        if not available_question_ids:
            raise QuestionNotFoundError("No new questions available for the user.")

        # 随机选择一个 question_id
        random_id = random.choice(list(available_question_ids))
        random_record = EnglishWaikanQuestionBank.objects.get(id=random_id)



        # done_question_ids = EnglishWaikanTestRecord.objects.filter(user_id=user_id).values_list('question_id',
        #                                                                                         flat=True)
        # done_question_ids = set(done_question_ids)
        #
        # # 获取所有记录的 id 列表
        # all_question_ids = set(EnglishWaikanQuestionBank.objects.values_list('id', flat=True))
        # print(f"all_question_ids:{all_question_ids},done_question_ids:{done_question_ids}")
        # available_question_ids = all_question_ids - done_question_ids
        # if not available_question_ids:
        #     raise QuestionNotFoundError("No new questions available for the user.")
        #
        # # 随机选择一个 question_id
        # # random_id = random.choice(list(available_question_ids))
        # random_id = "1"
        # random_record = EnglishWaikanQuestionBank.objects.get(id=random_id)

        publication = random_record.publication

        if not publication:
            raise QuestionNotFoundError(f"Publication with id {random_record.publication_id} not found.")

        # 获取子题
        sub_questions = EnglishWaikanSubQuestion.objects.filter(
            question=random_record,
            question_feature='set'
        ).order_by('id')

        if not sub_questions:
            raise QuestionNotFoundError(f"No sub-questions found for question id {random_id} with feature 'set'.")

        # 构建子题列表
        sub_questions_list = []
        sub_question_ids = []
        for sub_question in sub_questions:
            # 确保 question_options 是列表
            try:
                options_data = sub_question.question_options
                if isinstance(options_data, str):
                    options_data = json.loads(options_data)
                options = [option for option in options_data]
            except (json.JSONDecodeError, TypeError) as e:
                logger.error(f"Invalid format for question options: {e}")
                logger.error(f"Question options data: {options_data}")
                raise QuestionNotFoundError(f"Invalid format for question options: {e}")

            sub_questions_list.append({
                "sub_question_id": str(sub_question.id),
                "question_stem": sub_question.question_stem,
                "question_type": sub_question.question_type,
                "question_options": options,
                "answer": sub_question.question_answer,
                "analysis": sub_question.question_analysis
            })
            sub_question_ids.append(str(sub_question.id))
        # 构建返回字典
        response = {
            "question_id": random_record.id,
            "article": random_record.article,
            "article_translate": publication.article_translate,
            "publications_source": publication.publications_source,
            "sub_questions": sub_questions_list
        }

        test_record = EnglishWaikanTestRecord(
            user_id=user_id,
            question=random_record,
            sub_question_ids=sub_question_ids,
            question_feature='首次出题'
        )
        test_record.save()

        return response

    @classmethod
    def get_again_question_detail(cls, user_id, question_id, wrong_sub_question_ids):
        print("aaaaaaaaaaaaaaz")

        try:

            # 获取主问题记录
            question_record = EnglishWaikanQuestionBank.objects.get(id=question_id)
            print(f"question_record在这啊啊啊啊啊{question_record}")
            publication = question_record.publication
            print(f"publication在这啊啊啊啊啊:{publication}")
            if not publication:
                raise QuestionNotFoundError(f"Publication with id {question_record.publication_id} not found.")

            # 获取所有子题记录
            sub_questions = EnglishWaikanSubQuestion.objects.filter(
                question=question_record,
                id__in=wrong_sub_question_ids
            )
            print(f"sub_questions在这啊啊啊啊啊:{sub_questions}")
            if not sub_questions:
                raise QuestionNotFoundError(
                    f"No sub-questions found for question id {question_id} with given wrong_sub_question_ids.")

            # 合并并去重 question_type
            question_types_set = set()

            for sub_question in sub_questions:
                try:
                    question_type_data = sub_question.question_type
                    if isinstance(question_type_data, str):
                        question_type_data = json.loads(question_type_data)
                    question_types_set.update(question_type_data)
                    print(f"question_types_set在这啊啊啊啊啊:{question_types_set}")
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"Invalid format for question_type: {e}")
                    logger.error(f"Question type data: {question_type_data}")
                    raise QuestionNotFoundError(f"Invalid format for question_type: {e}")

            question_types = list(question_types_set)
            print(f"question_types在这啊啊啊啊啊:{question_types}")
            # 获取 specialized 子题记录
            specialized_sub_questions = []
            for question_type in question_types:
                sub_question = EnglishWaikanSubQuestion.objects.filter(
                    question=question_record,
                    question_type__contains=[question_type],  # 使用 contains 进行部分匹配
                    question_feature='specialized'
                ).order_by('id').first()

                if sub_question:
                    specialized_sub_questions.append(sub_question)

            if not specialized_sub_questions:
                raise QuestionNotFoundError(
                    f"No specialized sub-questions found for question id {question_id} with given question_types.")

            # 构建返回字典
            response_sub_questions = []
            sub_question_ids = []
            for sub_question in specialized_sub_questions:
                try:
                    options_data = sub_question.question_options
                    if isinstance(options_data, str):
                        options_data = json.loads(options_data)
                    options = [option for option in options_data]
                except (json.JSONDecodeError, TypeError) as e:
                    logger.error(f"Invalid format for question options: {e}")
                    logger.error(f"Question options data: {options_data}")
                    raise QuestionNotFoundError(f"Invalid format for question options: {e}")

                response_sub_questions.append({
                    "sub_question_id": str(sub_question.id),
                    "question_stem": sub_question.question_stem,
                    "question_type": sub_question.question_type,  # 添加 question_type 字段
                    "question_options": options,
                    "answer": sub_question.question_answer,
                    "analysis": sub_question.question_analysis
                })
                sub_question_ids.append(str(sub_question.id))

            response = {
                "question_id": question_record.id,
                "article": question_record.article,
                "article_translate": publication.article_translate,
                "sub_questions": response_sub_questions
            }
            test_record = EnglishWaikanTestRecord(
                user_id=user_id,
                question=question_record,
                sub_question_ids=sub_question_ids,
                question_feature='再次出题'
            )
            test_record.save()
            return response

        except EnglishWaikanQuestionBank.DoesNotExist:
            raise QuestionNotFoundError(f"Question with id {question_id} not found.")
        except EnglishWaikanSubQuestion.DoesNotExist:
            raise QuestionNotFoundError(
                f"No sub-questions found for question id {question_id} with given wrong_sub_question_ids.")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            raise QuestionNotFoundError(f"An error occurred: {e}")



