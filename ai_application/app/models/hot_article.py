from django.db import models

from ._base import BaseModel


class HotArticleSource(BaseModel):
    class Source(models.TextChoices):
        media_subtitle = ('media_subtitle', '音视频字幕')
        document = ('document', '文档内容')

    class Status(models.TextChoices):
        not_start = ('not_start', '未同步任务')
        no_content = ('no_content', '已同步，内容未完成')
        success = ('success', '已同步，内容已完成')
        fail = ('fail', '同步失败')

    source = models.CharField('来源', max_length=32, choices=Source.choices, default=Source.media_subtitle)
    status = models.CharField('状态', max_length=32, choices=Status.choices, default=Status.not_start)
    content = models.TextField('内容', blank=True)
    biz_id = models.CharField('业务id', max_length=32, blank=True, db_index=True)

    class Meta:
        verbose_name = verbose_name_plural = '文档源'
        db_table = 'ai_hot_article_source'


class HotArticleSegment(BaseModel):
    source = models.ForeignKey(HotArticleSource, on_delete=models.CASCADE, db_constraint=False)
    content = models.TextField('内容', blank=True)
    word_count = models.IntegerField('字数', default=0)
    is_gen_viewpoint = models.BooleanField('是否生成观点', default=False)
    viewpoint = models.TextField('观点', blank=True)
    gen_article_times = models.IntegerField('生成文章次数', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '文档分段'
        db_table = 'ai_hot_article_segment'


class HotArticleContent(BaseModel):
    segment = models.ForeignKey(HotArticleSegment, on_delete=models.CASCADE, db_constraint=False)
    covers = models.JSONField('封面', null=True)
    title = models.CharField('标题', max_length=255, blank=True)
    body = models.TextField('内容', blank=True)
    tags = models.JSONField('标签', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '文章内容'
        db_table = 'ai_hot_article_content'
