import json
from langchain.chains.llm import LLMChain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from django.conf import settings

# 初始化 LLM
llm = ChatOpenAI(
    openai_api_key=settings.DOUBAO_API_KEY,
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-1.5-pro-32k-250115",
)

# 判断是否适合生成图例的函数
def should_generate_figure(ppt_dict):
    judge_prompt = """
    你是一个教学设计专家，你的任务是判断一段PPT文案是否适合生成图示（例如用图画说明结构、流程、关系等）。判断标准如下：

    - ✅ 如果该文案中包含明确具体的例题，并且例题文本中包含具体的图例指示，适合生成图示 → 输出 True。
    - ❌ 如果该文案是解释性、概念性描述，或只是抽象内容的结构图，而非具体题目结构图像内容 → 输出 False。

    示例：
    输入：图由顶点（也叫节点）和边组成。关键词就是“顶点”和“边”。操作规则呢，如果是无向图，边是没有方向的，就好比双向车道；要是有向图，边是有方向的，类似单行道。
    输出：False
    输入：假设有一个图，顶点分别是A、B、C、D、E，边有(A,B)、(B,C)、(C,D)、(D,E)、(A,E)。现在问从A到E的简单路径有哪些？
    输出：True

    以下是文案内容：
    ---
    {text}
    ---

    请你只输出一个布尔值（True或False），不要输出其它内容。
    """
    prompt_obj = PromptTemplate(
        input_variables=["text"],
        template=judge_prompt.strip()
    )
    chain = LLMChain(llm=llm, prompt=prompt_obj)
    result = chain.run(
        text=ppt_dict["text"]
    )
    return result.strip() == "True"

# 生成Python图示代码的函数
def generate_figure_code(ppt_dict):
    code_prompt = """
    你是一位擅长使用 Mermaid 语法绘制结构图的专家。现在请你根据下面这段文本，生成其中包含的题目的对应的 Mermaid 图代码。

    要求：
        •   不是输出解题步骤的流程结构图，而是生成题目的图示
        •	输出 Mermaid 格式的结构图代码（如 graph TD 或 flowchart TD）
        •   生成的图片将会插入到ppt中，要考虑插入后的美观性，让整个图像趋于正方形
        •	保证结构清晰、美观、符合教学场景需求
        •	仅输出 Mermaid 代码本身，不要添加注释、解释或额外说明

    示例：
        输入：典型例题：假设有一个双向链表，节点数据是整数，要在值为5的节点后面插入一个值为10的节点。解题步骤：先遍历找到值为5的节点，创建新节点数据为10，再调整指针。
        输出：
        graph LR
            node1[1]:::node <--> node5[5]:::node
            node5 <--> node10[10]:::inserted
            node10 <--> node3[3]:::node

            classDef node fill:#D6EAF8,stroke:#2980B9,stroke-width:2px;
            classDef inserted fill:#FAD7A0,stroke:#D68910,stroke-width:2px;

    文案内容如下：
    ---
    正文: {text}
    ---
    现在请你生成对应的Mermaid图代码：
    """
    prompt_obj = PromptTemplate(
        input_variables=["text"],
        template=code_prompt.strip()
    )
    chain = LLMChain(llm=llm, prompt=prompt_obj)
    result = chain.run(
        text=ppt_dict["text"]
    )
    return result.strip()

def extract_mermaid_code(entry):
    code = entry.get('code', '')
    # 移除前缀 "mermaid\n"，如果存在
    if code.startswith('mermaid\n'):
        code = code[len('mermaid\n'):]
    return code

# 主函数：对一批ppt文案进行图示判断与生成
def process_ppt_script_list(script_list):
    result_list = []
    for ppt_dict in script_list[2:]:
        try:
            if should_generate_figure(ppt_dict):
                code = generate_figure_code(ppt_dict)
                result_list.append({
                    "id": ppt_dict.get("id"),
                    "text": ppt_dict.get("text"),
                    "generate_figure": True,
                    "code": code
                })
            else:
                result_list.append({
                    "id": ppt_dict.get("id"),
                    "text": ppt_dict.get("text"),
                    "generate_figure": False,
                    "code": None
                })
        except Exception as e:
            result_list.append({
                "id": ppt_dict.get("id"),
                "title": ppt_dict.get("title"),
                "generate_figure": False,
                "code": None,
                "error": str(e)
            })
    return result_list