import logging
import re
from collections import defaultdict
from io import BytesIO
import requests

import olefile
from docx import Document
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.opc.pkgreader import _SerializedRelationships, _SerializedRelationship
from docx.opc.oxml import parse_xml
from docx.oxml import CT_P, CT_Tbl
from docx.oxml.xmlchemy import serialize_for_reading
from docx.table import Table
from docx.text.paragraph import Paragraph
from openpyxl.cell import MergedCell
from openpyxl.reader.excel import load_workbook
from pydantic import BaseModel

from . import omml
from .omml import DOC_XML_ROOT
from helpers.upload_helper import upload_file

logger = logging.getLogger(__name__)

def strip_text_content(content) -> list:
    data = content[:]
    while data and data[0] == '':
        data.pop(0)
    return data

def get_file_bytes(file_path) -> BytesIO:
    response = requests.get(file_path)
    try:
        response.raise_for_status()
    except Exception as e:
        logger.exception(e)
        raise logger.exception(e)

    return BytesIO(response.content)

def escape_html_tags(html_string):
    # 正则表达式匹配HTML标签及其内容
    tag_pattern = re.compile(r'<[^>]+>')

    # 用于替换标签的函数
    def replace_with_entities(match):
        """
        # return html.escape(tag)
        """
        tag = match.group(0)
        return tag.replace('<', '\\' + '<').replace('>', '\\' + '>')

    escaped_html = tag_pattern.sub(replace_with_entities, html_string)
    return escaped_html

# 修复docs打开docx文件时的bug:
# KeyError: "There is no item named 'word/NULL' in the archive" #797

def load_from_xml_v2(base_uri, rels_item_xml):
    """
    Return |_SerializedRelationships| instance loaded with the
    relationships contained in *rels_item_xml*. Returns an empty
    collection if *rels_item_xml* is |None|.
    """
    srels = _SerializedRelationships()
    if rels_item_xml is not None:
        rels_elm = parse_xml(rels_item_xml)
        for rel_elm in rels_elm.Relationship_lst:
            if rel_elm.target_ref in ('../NULL', 'NULL'):
                continue
            srels._srels.append(_SerializedRelationship(base_uri, rel_elm))
    return srels


_SerializedRelationships.load_from_xml = load_from_xml_v2



def get_cell_text_alignment(cell):
    alignments: set[WD_TABLE_ALIGNMENT] = set()
    for paragraph in cell.paragraphs:
        alignments.add(paragraph.alignment)

    if len(alignments) == 0:
        return ''

    align = alignments.pop()
    if align == WD_TABLE_ALIGNMENT.CENTER:
        return 'center'
    elif align == WD_TABLE_ALIGNMENT.LEFT:
        return 'left'
    elif align == WD_TABLE_ALIGNMENT.RIGHT:
        return 'right'
    else:
        return ''


def table_to_html(table: Table):
    """
    将docx表格转换为HTML格式，包括处理跨行合并的单元格。
    """

    def check_cell_exists(row_idx: int, col_idx: int) -> bool:
        try:
            table.cell(row_idx, col_idx)
            return True
        except IndexError:
            return False

    html_content = '<table border="1" style="border-collapse: collapse;">\n'

    # 获取表格中的合并单元格信息
    merged_cols = set()
    merged_rows = set()
    ignore_merged_rows = set()
    for i, row in enumerate(table.rows):
        for j, cell in enumerate(row.cells):
            # 找合并的cols
            if cell._tc is not None and cell._tc.grid_span is not None and cell._tc.grid_span > 1:
                merged_cols.add((cell._tc, cell._tc.grid_span))
            # 找合并的rows
            if cell._tc is not None and cell._tc.vMerge is not None:
                if cell._tc.vMerge == 'restart':
                    if cell._tc in merged_rows:
                        ignore_merged_rows.add((i, j))
                    merged_rows.add(cell._tc)

    for row_num, row in enumerate(table.rows):
        html_content += '<tr>\n'
        colspan_start = True
        for col_num, cell in enumerate(row.cells):
            text_alignment = get_cell_text_alignment(cell)
            align_style = f'style="text-align:{text_alignment}"'
            cell_text = cell.text.strip() if cell.text.strip() else '&nbsp;'  # 处理空单元格
            is_row_spanned = cell._tc in merged_rows
            is_col_spanned = (cell._tc, cell._tc.grid_span) in merged_cols
            if not is_row_spanned and not is_col_spanned:
                html_content += f'<td {align_style}>{cell_text}</td>\n'
                colspan_start = True
            elif not is_col_spanned and is_row_spanned and cell._tc.vMerge == 'restart' and \
                    (row_num, col_num) not in ignore_merged_rows:
                rowspan = 1
                current_row = row_num + 1
                while current_row < len(table.rows) and check_cell_exists(current_row, col_num):
                    _cell = table.cell(current_row, col_num)._tc
                    if _cell.vMerge is not None and _cell.vMerge == 'restart' and _cell == cell._tc:
                        rowspan += 1
                        current_row += 1
                    else:
                        break
                html_content += f'<td rowspan="{rowspan}" {align_style}>{cell_text}</td>\n'
                colspan_start = True
            elif not is_row_spanned and is_col_spanned and colspan_start:
                html_content += f'<td colspan="{cell._tc.grid_span}" {align_style}>{cell_text}</td>\n'
                colspan_start = False

            # a cell with colspan and rowspan

        html_content += '</tr>\n'

    html_content += '</table>\n'
    return html_content


class ListStyle(BaseModel):
    numFmt: str  # decimal
    start: int = 0
    suff: str = 'space'  # space
    lvlText: str = ''


class WordParser:

    def __init__(self, file_path):
        if file_path.startswith('http://') or file_path.startswith('https://'):
            # If it's a URL, use get_file_bytes to fetch content
            self.doc = Document(get_file_bytes(file_path))
        else:
            # Otherwise, assume it's a local file path
            self.doc = Document(file_path)

        self.is_code_block = False
        self.list_part = defaultdict(dict)
        self.title_number_map = defaultdict(int)
        self.list_number_map = defaultdict(int)
        self.content: list = []

    def parse(self):
        body_part = self.doc._body
        body_el = body_part._element

        for el in body_el:
            if isinstance(el, CT_P):
                # 处理ole对象
                if 'o:OLEObject' in el.xml:
                    try:
                        self.parse_embed_excel(el.xml)
                    except Exception as e:
                        logger.exception(e)
                    continue

                p = Paragraph(el, body_part)

                # 公式暂不支持
                if el.xpath('.//m:oMath'):
                    self.parse_math_latex(p)
                    continue

                # 检查段落是否包含图片
                image_content = self.parse_image(p)
                if image_content:
                    self.content.append(image_content)

                # 检查段落是否有文本内容
                if p.text.strip():
                    self.parse_paragraph_text(p)
                elif not image_content:
                    # 只有既没有图片也没有文本的段落才添加空行
                    self.content.append('')

            elif isinstance(el, CT_Tbl):
                self.parse_table(el, body_part)

            self.content.append('')

    def get_content(self) -> list:
        return strip_text_content(self.content)

    def parse_math_latex(self, paragraph: Paragraph):
        is_inline = True if paragraph.text else False
        el = paragraph._element

        if is_inline:
            is_inline_arr = []
            for child in el:
                tag_prefix = '{' + child.nsmap.get(child.prefix) + '}'
                tag_name = child.tag.replace(tag_prefix, '')
                if tag_name == 'oMath':
                    child_xml = serialize_for_reading(child)

                    try:
                        math_text = self._parse_math_latex(child_xml)
                    except Exception as e:
                        logger.exception(e)
                        math_text = '[公式暂不支持]'

                    if math_text:
                        is_inline_arr.append(f'${math_text}$')
                elif tag_name == 'r':
                    text_part = child.xpath('.//w:t')
                    text_ = text_part[0].text if text_part else None
                    if text_:
                        is_inline_arr.append(text_)

            self.content.append(''.join(is_inline_arr))
        else:
            o_math_el = el.xpath('.//m:oMath')
            o_math_el = o_math_el[0] if o_math_el else None
            if o_math_el is not None:
                o_math_xml = serialize_for_reading(o_math_el)

                try:
                    math_text = self._parse_math_latex(o_math_xml)
                except Exception as e:
                    logger.exception(e)
                    math_text = '[公式暂不支持]'
                self.content.append(f'$${math_text}$$')

    def _parse_math_latex(self, xml_string):
        match = re.search(r'<m:oMath(.*?)<\/m:oMath>', xml_string, re.DOTALL)
        if not match:
            return None

        xml_str = DOC_XML_ROOT.format(match.group(0))
        o_math = None
        for o_math_ in omml.load_string(xml_str):
            o_math = o_math_
        return o_math

    def parse_paragraph_text(self, paragraph: Paragraph):
        if not getattr(paragraph, 'style'):
            self.parse_main_body(paragraph)
            return

        if paragraph.style.name.startswith('Heading 1'):
            text_str = self.parse_text_with_list(paragraph, paragraph.text, True)
            text_str = escape_html_tags(text_str)
            self.content.append(f'# {text_str}')
        elif paragraph.style.name.startswith('Heading 2'):
            text_str = self.parse_text_with_list(paragraph, paragraph.text, True)
            text_str = escape_html_tags(text_str)
            self.content.append(f'## {text_str}')
        elif paragraph.style.name.startswith('Heading 3'):
            text_str = self.parse_text_with_list(paragraph, paragraph.text, True)
            text_str = escape_html_tags(text_str)
            self.content.append(f'### {text_str}')
        else:
            self.parse_main_body(paragraph)

    def parse_main_body(self, paragraph: Paragraph):
        p_text = paragraph.text.strip()
        if '代码块开始' == p_text:
            self.is_code_block = True
            self.content.append('```')
            return

        if '代码块结束' == p_text:
            self.is_code_block = False
            self.content.append('```')
            return

        if self.is_code_block:
            p_text = p_text.replace('‘', "'").replace('’', "'")
            p_text = p_text.replace('"', '"').replace('"', '"')

            has_indent = paragraph._element.xpath('.//w:ind')
            if has_indent:
                p_text = '    ' + p_text
            self.content.append(p_text)
            return

        self.parse_text(paragraph)

    def parse_text(self, paragraph: Paragraph):
        text_arr = []
        for run in paragraph.runs:
            text_part = run._element.xpath('.//w:t')
            text_ = text_part[0].text if text_part else None
            if not text_:
                continue

            bold_parts = run._element.xpath('.//w:b')
            is_bold = True if bold_parts and bold_parts[0].val else False

            italic_parts = run._element.xpath('.//w:i')
            is_italic = True if italic_parts and italic_parts[0].val else False

            if text_.strip():
                if is_bold:
                    # 处理前端加粗展示
                    text_ = f'**{text_.strip()}** '
                if is_italic:
                    text_ = f'*{text_}*'
                # Markdown 的列表需要正确的缩进才能被正确解析为有序或无序列表
                if '         ●' in text_:
                    text_ = '- '

                text_arr.append(text_)

        text_str = ''.join(text_arr)

        text_str = escape_html_tags(text_str)
        if re.search(r'^\s*#{1,6}', text_str):
            text_str = "\\" + text_str.strip()
        if re.search(r'^\s*>+', text_str):
            text_str = "\\" + text_str.strip()

        text_str = self.parse_text_with_list(paragraph, text_str)
        self.content.append(text_str)

    def parse_text_with_list(self, paragraph: Paragraph, text_str, is_title=False) -> str:
        xml_part = paragraph._element
        num_pr_list = xml_part.xpath('.//w:numPr')
        if not num_pr_list:
            return text_str

        num_pr = num_pr_list[0]
        list_level_part = num_pr.xpath('.//w:ilvl')
        list_number_id_part = num_pr.xpath('.//w:numId')

        if not (list_number_id_part and list_level_part):
            return text_str

        list_number_id = list_number_id_part[0].val
        list_level = list_level_part[0].val

        list_style = self.get_list_style(list_number_id, list_level)

        if is_title:
            if list_style.numFmt == 'decimal':
                title_number_k = f'{list_number_id}-{list_level}'
                self.title_number_map[title_number_k] += 1
                next_level_title_number_k = f'{list_number_id}-{list_level+1}'
                self.title_number_map[next_level_title_number_k] = 0

                style_text = list_style.lvlText
                for level_ in range(list_level, -1, -1):
                    map_k_ = f'{list_number_id}-{level_}'
                    ol_seq = self.title_number_map[map_k_]
                    style_text = re.sub(f'%{level_+1}', str(ol_seq), style_text)

                if list_style.suff == 'space':
                    style_text = f'{style_text} '

                return f'{style_text}{text_str}'
            else:
                return text_str
        else:
            if list_style.numFmt == 'bullet':
                text_str = f'- {text_str}'
            else:
                list_number_k = f'{list_number_id}-{list_level}'
                self.list_number_map[list_number_k] += 1
                next_level_list_number_k = f'{list_number_id}-{list_level + 1}'
                self.list_number_map[next_level_list_number_k] = 0

                ol_seq = self.list_number_map[list_number_k]
                text_str = f'{ol_seq}. {text_str}'

            space_str = '   ' * list_level
            return f'{space_str}{text_str}'

    def get_list_style(self, num_id, list_level) -> ListStyle:
        try:
            numbering_part = self.doc.part.numbering_part
            abstract_num_id = numbering_part._element.num_having_numId(num_id).abstractNumId.val
            xpath = './/w:abstractNum[@w:abstractNumId="%d"]' % abstract_num_id
            abstract_num_obj = numbering_part._element.xpath(xpath)[0]

            lvl_xpath = './/w:lvl[@w:ilvl="%d"]' % list_level
            lvl_obj = abstract_num_obj.xpath(lvl_xpath, namespaces={
                abstract_num_obj.prefix: abstract_num_obj.nsmap.get(abstract_num_obj.prefix)
            })[0]

            res2 = lvl_obj.xpath('.//w:numFmt', namespaces={
                lvl_obj.prefix: lvl_obj.nsmap.get(lvl_obj.prefix)
            })[0]

            list_style = ListStyle(numFmt=res2.values()[0])
            if list_style.numFmt == 'bullet':
                return list_style
            else:
                start_obj = lvl_obj.xpath('.//w:start', namespaces={
                    lvl_obj.prefix: lvl_obj.nsmap.get(lvl_obj.prefix)
                })
                suff_obj = lvl_obj.xpath('.//w:suff', namespaces={
                    lvl_obj.prefix: lvl_obj.nsmap.get(lvl_obj.prefix)
                })
                lvlText_obj = lvl_obj.xpath('.//w:lvlText', namespaces={
                    lvl_obj.prefix: lvl_obj.nsmap.get(lvl_obj.prefix)
                })

                list_style.start = start_obj[0].values()[0] if start_obj else 1
                list_style.suff = suff_obj[0].values()[0] if suff_obj else 'space'
                list_style.lvlText = lvlText_obj[0].values()[0] if lvlText_obj else ''
                return list_style
        except:
            return ListStyle(numFmt='bullet')

    def parse_image(self, paragraph: Paragraph):
        logger.debug("开始解析图片...")
        img = self.get_picture(paragraph)
        if not img:
            logger.debug("get_picture返回None，没有找到图片")
            return None

        logger.debug(f"找到图片，开始保存...")
        image_url = self.save_image(img.blob)
        
        if not image_url:
            logger.warning("图片保存失败，跳过该图片")
            return None
            
        logger.info(f"图片处理成功: {image_url}")
        return f'![]({image_url})'

    def get_picture(self, paragraph: Paragraph):
        xml_part = paragraph._element
        img_parts = xml_part.xpath('.//pic:pic')
        if not img_parts:
            return

        try:
            img_part = img_parts[0]
            embed = img_part.xpath('.//a:blip/@r:embed')[0]
            related_part = self.doc.part.related_parts[embed]
            image = related_part.image
            return image
        except Exception as e:
            logger.error(f'Word中的图片解析失败:{str(e)}')
            return

    def save_image(self, img) -> str:
        """保存图片并返回URL"""
        try:
            logger.debug("开始保存图片...")
            if not img:
                logger.warning("图片数据为空")
                return ""
                
            logger.debug(f"图片数据大小: {len(img)} bytes")
            path = upload_file(img, 'image.png', 'smart_course_image')
            logger.debug(f"上传结果: {path}")
            
            if not path:
                logger.error("图片上传失败，upload_file返回None")
                return ""
            
            logger.info(f"图片上传成功: {path}")
            return path
            
        except Exception as e:
            logger.error(f"保存图片时发生错误: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return ""

    def parse_embed_excel(self, xml_string: str):
        r_id = None
        match = re.search(r'<o:OLEObject[^>]*r:id="([^"]+)"', xml_string)
        if match:
            r_id = match.group(1)
        if not r_id:
            return

        # 打开OLE对象
        ole_blob = self.doc.part.rels[r_id].target_part.blob
        ole_stream = BytesIO(ole_blob)

        # 使用olefile打开临时文件
        ole = olefile.OleFileIO(ole_stream)
        workbook = load_workbook(ole.openstream('package'))

        for sheet_name in workbook.sheetnames:
            html_str = self._excel_to_html(workbook, sheet_name)
            self.content.append(html_str + '\n')

    def _excel_to_html(self, wb, sheet_name='Sheet1'):
        ws = wb[sheet_name]
        html = '<table border="1" style="border-collapse: collapse;">\n'

        # 处理合并单元格，记录其属性
        merged_cells = {}
        all_merged_cells = []
        for merged_range in ws.merged_cells.ranges:
            cell = ws.cell(row=merged_range.min_row, column=merged_range.min_col)
            colspan = merged_range.max_col - merged_range.min_col + 1
            rowspan = merged_range.max_row - merged_range.min_row + 1
            merged_cells[(cell.row, cell.column)] = (colspan, rowspan)
            if rowspan > 1:
                for i in range(rowspan):
                    all_merged_cells.append((cell.row + i, cell.column))
            if colspan > 1:
                for i in range(colspan):
                    all_merged_cells.append((cell.row, cell.column + i))

        # 遍历所有行
        for row in ws.iter_rows():
            html += '<tr>\n'
            for cell in row:
                if isinstance(cell, MergedCell):
                    # 跳过合并单元格的其他部分
                    if (cell.row, cell.column) in all_merged_cells:
                        continue
                td = '<td'
                if (cell.row, cell.column) in merged_cells:
                    colspan, rowspan = merged_cells[(cell.row, cell.column)]
                    td += f' colspan="{colspan}" rowspan="{rowspan}"'

                if cell.value is not None:
                    td += f'>{cell.value}</td>'
                else:
                    td += '></td>'

                html += td + '\n'
            html += '</tr>\n'

        html += '</table>'
        return html

    def parse_table(self, table_el, body_part):
        t = Table(table_el, body_part)
        t_str = table_to_html(t)
        self.content.append(t_str)
