import json
import time

from django.conf import settings
from langchain_core.messages import HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate

from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import LangChainLlmWrapper


class LLMOcrFeature(BaseFeature):

    def run(
            self,
            image_urls: list,
            tracing_log: MessageTracingLog | None = None

    ) -> str:
        sys_prompt = """请识别并提取出图片内容，不做额外解释或回答"""

        content = [{"type": "text", "text": sys_prompt}]
        for url in image_urls:
            content.append({"type": "image_url", "image_url": {"url": url}})
        prompt_messages = [HumanMessage(content=content)]

        wrapper = LangChainLlmWrapper(
            model_provider='doubao',
            model_id=settings.DAYI_VISUAL_OCR_MODEL,
            model_params={'temperature': 0.3},
        )
        llm = wrapper.get_llm()

        prompt = ChatPromptTemplate.from_messages(prompt_messages)

        start_time = time.perf_counter()
        invoke_result = (prompt | llm).invoke({})
        latency = time.perf_counter() - start_time
        llm_result = wrapper.handle_non_stream_response(invoke_result, latency)

        if tracing_log:
            tracing_log.add_tracing_by_llm_result(
                'llm_ocr',
                query=json.dumps({'images': image_urls}, ensure_ascii=False),
                llm_result=llm_result
            )

        return llm_result.message.content
