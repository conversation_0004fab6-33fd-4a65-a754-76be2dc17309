[{"知识点": "时间复杂度", "前置知识点": ["算法的基本概念", "函数的概念", "极限的基本思想", "程序执行步骤的计数"]}, {"知识点": "空间复杂度", "前置知识点": ["算法的基本概念", "内存存储的基本概念", "变量与数据占用空间的概念"]}, {"知识点": "线性表", "前置知识点": ["数据结构的基本概念", "集合与序列的概念", "元素与元素间的逻辑关系"]}, {"知识点": "顺序表", "前置知识点": ["线性表的概念", "数组的概念", "连续存储的思想"]}, {"知识点": "单链表", "前置知识点": ["线性表的概念", "指针（或引用）的概念", "节点的概念（数据域与指针域）"]}, {"知识点": "循环链表", "前置知识点": ["单链表的概念", "首尾相连的逻辑结构"]}, {"知识点": "双向链表", "前置知识点": ["单链表的概念", "双向指针的概念（前驱与后继）"]}, {"知识点": "栈", "前置知识点": ["线性表的概念", "先进后出（LIFO）的逻辑关系"]}, {"知识点": "顺序栈", "前置知识点": ["栈的概念", "顺序表的概念", "数组的操作"]}, {"知识点": "链栈", "前置知识点": ["栈的概念", "单链表的概念", "链表的插入与删除操作"]}, {"知识点": "递归", "前置知识点": ["函数的概念", "栈的原理（函数调用栈）", "问题的递归分解思想"]}, {"知识点": "队列", "前置知识点": ["线性表的概念", "先进先出（FIFO）的逻辑关系"]}, {"知识点": "顺序队", "前置知识点": ["队列的概念", "顺序表的概念", "循环存储的思想（解决假溢出）"]}, {"知识点": "链队", "前置知识点": ["队列的概念", "单链表的概念", "队头与队尾指针的操作"]}, {"知识点": "串", "前置知识点": ["线性表的概念", "字符的概念", "字符串的基本操作（拼接、比较等）"]}, {"知识点": "模式匹配", "前置知识点": ["串的概念", "子串与主串的概念", "匹配的逻辑（字符相等判断）"]}, {"知识点": "BF 算法", "前置知识点": ["模式匹配的概念", "循环的基本操作", "回溯的思想"]}, {"知识点": "KMP 算法", "前置知识点": ["模式匹配的概念", "BF 算法的缺陷", "部分匹配的概念"]}, {"知识点": "next 表", "前置知识点": ["KMP 算法的概念", "前缀与后缀的概念", "最长公共前后缀的计算"]}, {"知识点": "数组", "前置知识点": ["线性表的概念（多维数组可视为线性表的扩展）", "索引的概念", "连续存储的思想"]}, {"知识点": "特殊矩阵压缩", "前置知识点": ["数组的概念", "矩阵的概念（对称矩阵、三角矩阵等）", "存储空间优化的思想"]}, {"知识点": "树的概念", "前置知识点": ["非线性结构的概念", "节点与边的概念", "层次关系的概念（父节点与子节点）"]}, {"知识点": "二叉树的性质", "前置知识点": ["树的概念", "二叉树的定义（每个节点最多两棵子树）", "节点计数与层次的关系"]}, {"知识点": "二叉树的存储结构", "前置知识点": ["二叉树的概念", "顺序存储（数组）的思想", "链式存储（左右指针）的思想"]}, {"知识点": "二叉树的遍历", "前置知识点": ["二叉树的概念", "递归的思想（或循环与栈/队列的结合）", "节点访问的顺序逻辑（前序、中序、后序）"]}, {"知识点": "线索二叉树", "前置知识点": ["二叉树的遍历", "空指针的概念", "线索化的思想（将空指针改为前驱/后继线索）"]}, {"知识点": "树的存储结构", "前置知识点": ["树的概念", "双亲表示法（数组）", "孩子表示法（链表）", "孩子兄弟表示法（二叉链表）"]}, {"知识点": "树与二叉树的转换", "前置知识点": ["树的概念", "二叉树的概念", "孩子兄弟表示法的思想"]}, {"知识点": "森林与二叉树的转换", "前置知识点": ["森林的概念（多棵树的集合）", "树与二叉树的转换", "二叉树的根节点与森林中第一棵树的关系"]}, {"知识点": "树的遍历", "前置知识点": ["树的概念", "二叉树的遍历（转换后借助二叉树遍历）", "层次遍历的思想"]}, {"知识点": "哈夫曼树", "前置知识点": ["树的概念", "权重的概念", "最优二叉树的思想（带权路径长度最小）"]}, {"知识点": "哈夫曼编码", "前置知识点": ["哈夫曼树的概念", "字符编码的概念", "前缀编码的思想（无歧义）"]}, {"知识点": "图的基本概念", "前置知识点": ["非线性结构的概念", "顶点（节点）与边的概念", "有向图与无向图的区分", "权值的概念"]}, {"知识点": "邻接矩阵", "前置知识点": ["图的基本概念", "二维数组的概念", "顶点间关系的表示（0/1或权值）"]}, {"知识点": "邻接表", "前置知识点": ["图的基本概念", "链表的概念", "顶点与邻接顶点的关系表示"]}, {"知识点": "十字链表", "前置知识点": ["有向图的概念", "邻接表的概念", "弧头与弧尾的双向指针表示"]}, {"知识点": "邻接多重表", "前置知识点": ["无向图的概念", "邻接表的概念", "边的双重表示（避免重复）"]}, {"知识点": "深度优先搜索", "前置知识点": ["图的基本概念", "递归的思想（或栈的使用）", "顶点访问标记的概念"]}, {"知识点": "广度优先搜索", "前置知识点": ["图的基本概念", "队列的概念", "层次访问的思想", "顶点访问标记的概念"]}, {"知识点": "最小生成树", "前置知识点": ["图的基本概念（连通图）", "树的概念（无环）", "权值的概念", "贪心算法的基本思想"]}, {"知识点": "最短路径", "前置知识点": ["图的基本概念（带权图）", "路径的概念", "权值累加的计算", "贪心或动态规划的思想"]}, {"知识点": "拓扑排序", "前置知识点": ["有向图的概念（无环图DAG）", "顶点间的前驱与后继关系", "入度与出度的概念"]}, {"知识点": "关键路径", "前置知识点": ["有向图的概念（AOE网）", "拓扑排序", "活动的最早开始与最晚开始时间计算"]}, {"知识点": "顺序查找", "前置知识点": ["线性表的概念", "元素比较的操作", "循环遍历的思想"]}, {"知识点": "折半查找", "前置知识点": ["有序表的概念", "顺序存储结构（数组）", "二分法的思想（区间缩小）"]}, {"知识点": "分块查找", "前置知识点": ["线性表的分块思想（块内无序、块间有序）", "顺序查找", "折半查找"]}, {"知识点": "二叉搜索树", "前置知识点": ["二叉树的概念", "查找的概念", "左子树值≤根≤右子树的特性"]}, {"知识点": "平衡二叉树", "前置知识点": ["二叉搜索树的概念", "平衡的概念（左右子树高度差限制）", "旋转操作的思想"]}, {"知识点": "红黑树", "前置知识点": ["平衡二叉树的概念", "节点着色的规则（红黑特性）", "插入/删除后的调整思想"]}, {"知识点": "B 树", "前置知识点": ["多路查找树的概念", "平衡的思想", "节点关键字与子树的关系", "磁盘存储的基本概念（减少IO）"]}, {"知识点": "B + 树", "前置知识点": ["B 树的概念", "叶子节点链表连接的思想", "索引与数据分离的特性"]}, {"知识点": "散列表", "前置知识点": ["哈希函数的概念", "数组的概念", "冲突处理的思想（开放定址法、链地址法）"]}, {"知识点": "直接插入排序", "前置知识点": ["线性表的概念", "元素的比较与移动操作", "有序子序列的扩展思想"]}, {"知识点": "折半插入排序", "前置知识点": ["直接插入排序的概念", "折半查找的思想（定位插入位置）"]}, {"知识点": "起泡排序", "前置知识点": ["线性表的概念", "相邻元素的比较与交换", "最大元素“浮起”的思想"]}, {"知识点": "简单选择排序", "前置知识点": ["线性表的概念", "查找最小（大）元素的操作", "元素交换的操作"]}, {"知识点": "希尔排序", "前置知识点": ["直接插入排序的概念", "增量序列的概念", "分组排序的思想"]}, {"知识点": "快速排序", "前置知识点": ["分治法的思想", "基准元素的选择", "分区操作（小于/大于基准）", "递归的思想"]}, {"知识点": "树形选择排序", "前置知识点": ["树的概念（完全二叉树）", "选择排序的思想", "锦标赛树的思想（两两比较）"]}, {"知识点": "堆排序", "前置知识点": ["完全二叉树的概念", "堆的特性（大根堆/小根堆）", "堆的调整操作（筛选）", "选择排序的思想"]}, {"知识点": "归并排序", "前置知识点": ["分治法的思想", "两个有序表的合并操作", "递归的思想"]}, {"知识点": "基数排序", "前置知识点": ["多关键字排序的思想", "队列的概念（分配与收集）", "数位的概念（个位、十位等）"]}, {"知识点": "多路平衡归并", "前置知识点": ["归并排序的概念", "外部排序的概念（数据不能全部载入内存）", "败者树的概念（优化选择最小元素）"]}, {"知识点": "置换选择排序", "前置知识点": ["堆排序的概念", "外部排序的概念", "生成初始归并段的思想"]}, {"知识点": "最佳归并树", "前置知识点": ["归并排序的概念", "哈夫曼树的思想（带权路径长度最小）", "归并段的概念"]}]