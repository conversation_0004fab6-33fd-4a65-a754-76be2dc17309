from django.db import models
from ._base import BaseModel



class UndergraduateCategory(BaseModel):
    name = models.CharField(max_length=100, verbose_name='本科一级学科名称')

    class Meta:
        verbose_name = verbose_name_plural = '本科一级学科'
        db_table = 'ai_undergraduate_category'


class GraduateCategory(BaseModel):
    name = models.CharField(max_length=100, verbose_name='研究生一级学科名称')

    class Meta:
        verbose_name = verbose_name_plural = '研究生一级学科'
        db_table = 'ai_graduate_category'


class UndergraduateMajor(BaseModel):
    name = models.CharField(max_length=100, verbose_name='本科二级门类名称')
    category = models.ForeignKey(UndergraduateCategory, on_delete=models.CASCADE, related_name='majors',
                                 verbose_name='所属一级学科')

    class Meta:
        verbose_name = verbose_name_plural = '本科二级门类'
        db_table = 'ai_undergraduate_major'


class GraduateMajor(BaseModel):
    name = models.CharField(max_length=100, verbose_name='研究生二级门类名称')
    category = models.ForeignKey(GraduateCategory, on_delete=models.CASCADE, related_name='majors',
                                 verbose_name='所属一级学科')

    class Meta:
        verbose_name = verbose_name_plural = '研究生二级门类'
        db_table = 'ai_graduate_major'


class CategoryCorrelation(BaseModel):
    undergrad_category = models.ForeignKey(UndergraduateCategory, on_delete=models.CASCADE,
                                           related_name='grad_correlations', verbose_name='本科一级学科')
    grad_category = models.ForeignKey(GraduateCategory, on_delete=models.CASCADE, related_name='undergrad_correlations',
                                      verbose_name='研究生一级学科')
    level = models.IntegerField(choices=[(i, str(i)) for i in range(0, 11)], verbose_name='关联度等级')

    class Meta:
        unique_together = ('undergrad_category', 'grad_category')
        verbose_name = verbose_name_plural = '一级学科关联度'
        db_table = 'ai_category_correlation'


class MajorCorrelation(BaseModel):
    undergrad_major = models.ForeignKey(UndergraduateMajor, on_delete=models.CASCADE, related_name='grad_correlations',
                                        verbose_name='本科二级门类')
    grad_major = models.ForeignKey(GraduateMajor, on_delete=models.CASCADE, related_name='undergrad_correlations',
                                   verbose_name='研究生二级门类')
    level = models.IntegerField(choices=[(i, str(i)) for i in range(0, 11)], verbose_name='关联度等级')

    class Meta:
        unique_together = ('undergrad_major', 'grad_major')
        verbose_name = verbose_name_plural = '二级门类关联度'
        db_table = 'ai_major_correlation'