import os
import pysrt

def srt_to_ass(srt_path, ass_path, font_name="Microsoft YaHei", font_size=36, spacing=5):
    subs = pysrt.open(srt_path)

    with open(ass_path, 'w', encoding='utf-8') as f:
        # Header
        f.write("[Script Info]\n")
        f.write("Title: Subtitle\n")
        f.write("ScriptType: v4.00+\n")
        f.write("PlayResX: 1280\n")
        f.write("PlayResY: 720\n")
        f.write("WrapStyle: 0\n")
        f.write("ScaledBorderAndShadow: yes\n")
        f.write("YCbCr Matrix: TV.601\n\n")

        # Styles
        f.write("[V4+ Styles]\n")
        f.write("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, "
                "OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, "
                "ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, "
                "MarginL, MarginR, MarginV, Encoding\n")

        f.write(f"Style: Default,{font_name},{font_size},&H00FFFFFF,&H000000FF,&H00000000,&H64000000,"
                f"0,0,0,0,100,100,{spacing},0,1,1.2,0,2,30,30,30,1\n\n")

        # Events
        f.write("[Events]\n")
        f.write("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n")

        for sub in subs:
            start = sub.start.to_time()
            end = sub.end.to_time()
            start_str = f"{start.hour:01}:{start.minute:02}:{start.second:02}.{int(start.microsecond/10000):02}"
            end_str = f"{end.hour:01}:{end.minute:02}:{end.second:02}.{int(end.microsecond/10000):02}"
            text = sub.text.replace("\n", "\\N")  # ASS uses \N for line breaks
            f.write(f"Dialogue: 0,{start_str},{end_str},Default,,0,0,0,,{text}\n")

    print(f"✅ .ass 文件已生成：{ass_path}")
