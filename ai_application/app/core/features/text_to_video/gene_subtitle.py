import whisper
import os

def GetSubtitle(save_path):
    # 1. 加载模型（可以选择 'tiny', 'base', 'small', 'medium', 'large'）
    # 该模型加载需要梯子，或者将whisper文件放入缓存中
    model = whisper.load_model("medium")

    # 2. 转写音频（自动断句、自动打时间戳）
    result = model.transcribe(f'{save_path}/final_audio.mp3', task="transcribe", verbose=True, language="zh")

    # 3. 保存成SRT字幕文件
    with open(f"{save_path}/subtitle.srt", "w", encoding="utf-8") as f:
        for i, segment in enumerate(result["segments"], start=1):
            start = segment['start']
            end = segment['end']
            text = segment['text'].strip()

            def format_time(seconds):
                h = int(seconds // 3600)
                m = int((seconds % 3600) // 60)
                s = int(seconds % 60)
                ms = int((seconds - int(seconds)) * 1000)
                return f"{h:02}:{m:02}:{s:02},{ms:03}"

            f.write(f"{i}\n")
            f.write(f"{format_time(start)} --> {format_time(end)}\n")
            f.write(f"{text}\n\n")

