from pydantic import Field

from django_ext.base_dto_model import MyBaseModel


class AIPracticePaperReqDto(MyBaseModel):
    user_id: str
    subject_id: str
    core_course_code: str


class AIPracticeSubjectiveAnswerDto(MyBaseModel):
    images: list[str] = Field(default=[])
    text: str = ''


class AIPracticeAnswerDetailDto(MyBaseModel):
    question_id: int
    choice_answer: list[str] = Field(default=[])
    subjective_answer: AIPracticeSubjectiveAnswerDto | None = None


class AIPracticePaperSubmitDto(MyBaseModel):
    user_id: str
    paper_id: int
    answer_detail: list[AIPracticeAnswerDetailDto]


class AIPracticePaperQuestionSubmitDto(MyBaseModel):
    user_id: str
    paper_id: int
    question_id: int
    choice_answer: list[str] = Field(default=[])
    subjective_answer: AIPracticeSubjectiveAnswerDto | None = None
