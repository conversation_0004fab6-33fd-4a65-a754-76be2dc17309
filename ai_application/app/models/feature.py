from django.db import models

from app.models import BaseModel


class EnglishSentenceAnalysis(BaseModel):
    sentence = models.TextField('句子', null=True)
    year = models.PositiveSmallIntegerField('年份', default=0)
    type = models.CharField('题型', max_length=255, blank=True)
    title = models.CharField('篇目', max_length=255, blank=True)
    content = models.JSONField('解析内容', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '英文句子分析'
        db_table = 'english_sentence_analysis'

    @property
    def content_str(self):
        if isinstance(self.content, list):
            return ', '.join(self.content)
        return ''
