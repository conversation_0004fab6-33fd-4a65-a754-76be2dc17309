--
-- <PERSON>reate model HotArticleSource
--
CREATE TABLE `ai_hot_article_source`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool        NOT NULL,
    `source`        varchar(32) NOT NULL,
    `content`       longtext    NOT NULL,
    `status`        varchar(32) NOT NULL,
    `biz_id`        varchar(32) NOT NULL
);
--
-- Create model HotArticleSegment
--
CREATE TABLE `ai_hot_article_segment`
(
    `id`                bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`          datetime(6) NOT NULL,
    `modified_time`     datetime(6) NOT NULL,
    `is_deleted`        bool     NOT NULL,
    `content`           longtext NOT NULL,
    `word_count`        integer  NOT NULL,
    `is_gen_viewpoint`  bool     NOT NULL,
    `viewpoint`         longtext NOT NULL,
    `gen_article_times` integer  NOT NULL,
    `source_id`         bigint   NOT NULL
);
--
-- <PERSON>reate model HotArticleContent
--
CREATE TABLE `ai_hot_article_content`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `covers`        json NULL,
    `title`         varchar(255) NOT NULL,
    `body`          longtext     NOT NULL,
    `tags`          json NULL,
    `segment_id`    bigint       NOT NULL
);
CREATE INDEX `ai_hot_article_source_add_time_fdcd7b37` ON `ai_hot_article_source` (`add_time`);
CREATE INDEX `ai_hot_article_source_biz_id_b3dfc2a0` ON `ai_hot_article_source` (`biz_id`);
CREATE INDEX `ai_hot_article_segment_add_time_4ca55f2c` ON `ai_hot_article_segment` (`add_time`);
CREATE INDEX `ai_hot_article_segment_source_id_746c2500` ON `ai_hot_article_segment` (`source_id`);
CREATE INDEX `ai_hot_article_content_add_time_f142218b` ON `ai_hot_article_content` (`add_time`);
CREATE INDEX `ai_hot_article_content_segment_id_8e03a914` ON `ai_hot_article_content` (`segment_id`);