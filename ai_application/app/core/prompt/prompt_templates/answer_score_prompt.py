import re

from app.core.output_parsers.regex import RegexParser
from app.core.prompt.utils.prompt_template_parser import PromptTemplateParser


output_parser = RegexParser(
    regex=re.compile(r"Score: (\d*)\n(.*)", re.IGNORECASE),
    output_keys=["score", "reason"],
    default_output_key='reason'
)

PROMPT_TMPL_EN = """Return a score of how fully it answered the user's question. This should be in the following format:

Question: [question here]
Answer: [answer here]
Score: [score between 0 and 100]
Reason: [reason here]

How to determine the score:
- Better answer, higher score 
- When the answer is accurate but quite brief, it is also considered a qualified answer
- If the contextual content is not related to the answer, score is 0
- Don't be overconfident!

Example #1

Context:
---------
Apples are red
---------
Question: what color are apples?
Answer: red
Score: 100
Reason: good

Example #2

Context:
---------
it was night and the witness forgot his glasses. he was not sure if it was a sports car or an suv
---------
Question: what type was the car?
Answer: a sports car or an suv
Score: 60
Reason: it is also considered a qualified answer

Example #3

Context:
---------
Pears are either red or orange
---------
Question: what color are apples?
Answer: apples is red
Score: 0
Reason: The answer cannot be obtained through context

Begin!

Context:
---------
{{context}}
---------
Question: {{question}}
Answer: {{answer}}"""


PROMPT = PromptTemplateParser(PROMPT_TMPL_EN)
