import logging

from api_client.base_client import BaseClient
from api_client.base_request import make_request, InternalApiError
from api_client.decorators import catch_error
from django_ext.exceptions import InternalException

logger = logging.getLogger(__name__)


class YantuCSClient(BaseClient):
    host = 'http://yantucs-bk-svc:8000'
    host_local = 'http://qiw.yantu.com.cn/yantucs'

    @property
    def base_url(self):
        return f'{self.host}/internal_api'

    @catch_error({'file_list': []})
    def bulk_query_video_subtitle(self, start_file_id, size=10) -> dict:
        # 批量查询文档字幕接口
        url = '/ai_application_api/bulk_query_video_subtitle'
        return self._post(url, params={'size': size}, data={'start_file_id': start_file_id})

    @catch_error({'file_list': []})
    def bulk_query_doc_content(self, start_file_id, size=10) -> dict:
        # 批量查询文档字幕接口
        url = '/ai_application_api/bulk_query_doc_content'
        return self._post(url, params={'size': size}, data={'start_file_id': start_file_id})

    @catch_error([])
    def get_video_subtitle_by_file_ids(self, file_ids: list) -> list:
        # 批量查询文档字幕接口
        url = '/ai_application_api/get_video_subtitle_by_file_ids'
        return self._post(url, data={'file_ids': file_ids})

    @catch_error([])
    def get_doc_content_by_file_ids(self, file_ids: list) -> list:
        # 批量查询文档字幕接口
        url = '/ai_application_api/get_doc_content_by_file_ids'
        return self._post(url, data={'file_ids': file_ids})

    def _request(self, url, **kwargs):
        full_url = f'{self.base_url}{url}'
        try:
            res = make_request(full_url, **kwargs)
        except InternalApiError as i_e:
            logger.exception(i_e)
            raise InternalException(code=50000, detail='网络请求失败', internal_err=i_e.internal_err)
        except Exception as e:
            logger.exception(e)
            raise InternalException(code=50000, detail='网络连接失败')

        if int(res.get('code')) != 200:
            raise InternalException(code=50000, detail=res.get('msg'))
        return res.get('data')


yantucs_client = YantuCSClient()
