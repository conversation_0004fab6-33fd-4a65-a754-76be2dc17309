<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试卷展示</title>
    <link href="https://cdn.kaoyanvip.cn/katex@0.16.9/katex.min.css" rel="stylesheet">
    <script src="https://cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/marked/13.0.2/marked.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/katex@0.16.9/katex.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/katex@0.16.9/contrib/auto-render.min.js"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        p {
            margin: 0;
        }
        .paper_stage {
            padding: 20px 0;
            color: red;
        }
        .question {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 20px;
        }
        .question-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
        }
        .question-prop {
            margin-left: 10px;
        }
        .subjective-answer {
            padding-left: 25px;
        }
        .subjective-answer-input {
            width: 100%;
            height: 150px;
        }
        .question-option-label p {
            display: inline-block;
        }
        /* 自定义alert样式 */
        .custom-alert {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: none;
            max-width: 80%;
            text-align: center;
        }
        .custom-alert-message {
            margin-bottom: 20px;
            font-size: 16px;
        }
        .custom-alert-button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .custom-alert-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 9999;
            display: none;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            display: none;
        }
        .loading-spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4CAF50;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .exam-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .exam-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .question {
            margin-bottom: 25px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .question-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .options {
            margin-left: 10px;
        }
        .option {
            margin-bottom: 5px;
        }
        input[type="radio"] {
            margin-right: 10px;
        }

        /* 新增提示词编辑框样式 */
        .panel {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .panel-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
            color: #333;
        }
        .input-area textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: Arial, sans-serif;
        }
        .button-container {
            margin-top: 15px;
            text-align: right;
        }
        .secondary-button {
            padding: 8px 16px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .secondary-button:hover {
            background-color: #0b7dda;
        }
    </style>
</head>
<body>
    <div class="exam-container">
        <h1 class="exam-title">试卷</h1>
        <p>
            <a target="_blank" href="{% url 'st_paper_dist_debug' %}">出题策略调试</a>
        </p>

        <div id="questions-container">
            <!-- 单选题将在这里动态渲染 -->
        </div>

        <div class="submit-container" style="text-align: center; margin-top: 30px;">
            <button id="submit-btn" style="padding: 10px 25px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">
                提交试卷
            </button>
        </div>
    </div>

    <!-- 自定义alert结构 -->
    <div class="custom-alert-overlay" id="custom-alert-overlay"></div>
    <div class="custom-alert" id="custom-alert">
        <div class="custom-alert-message" id="custom-alert-message"></div>
        <button class="custom-alert-button" id="custom-alert-button">确定</button>
    </div>

    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <script>
        const katexOptions = {
            delimiters: [
                {left: '$$', right: '$$', display: true},
                {left: '$', right: '$', display: false},
                {left: '\\(', right: '\\)', display: false},
                {left: '\\[', right: '\\]', display: true}
            ],
            throwOnError: false,
            trust: true
        };

        // 自定义alert函数
        function showCustomAlert(message) {
            $('#custom-alert-message').text(message);
            $('#custom-alert-overlay').show();
            $('#custom-alert').show();
        }

        function renderExam(examData) {
            console.log(examData)
            const container = document.getElementById('questions-container');

            // 标记阶段
            const stageInfo = examData.stage_info;
            if (stageInfo) {
                const stageElement = document.createElement('div');
                stageElement.className = 'paper_stage';
                stageElement.innerHTML = stageInfo.full_stage_name

                container.appendChild(stageElement);
            }

            for (let i = 0; i < examData.questions.length; i++) {
                const question = examData.questions[i];
                const questionElement = document.createElement('div');
                questionElement.className = 'question';
                questionElement.setAttribute('data-id', question.id);
                questionElement.setAttribute('data-type', question.question_type);

                const propElement = document.createElement('div');
                propElement.className = 'question-prop';
                propElement.innerHTML = `
                <div>问题id：${question.id}</div>
                <div>难度：${question.difficulty}</div>
                <div>知识点：${question.knowledge_list}</div>
                <div>答案：${question.right_answer}</div>
                `
                questionElement.appendChild(propElement);

                const titleElement = document.createElement('div');
                titleElement.className = 'question-title';
                const qTitle = marked.parse(question.title)
                titleElement.innerHTML = `<div>${i+1}. </div><div>${qTitle}</div>`;
                renderMathInElement(titleElement, katexOptions);

                questionElement.appendChild(titleElement);

                if (question.question_type === 0 || question.question_type === 1) {
                    const optionsElement = document.createElement('div');
                    optionsElement.className = 'options';

                    question.choices.forEach(option => {
                        const optionElement = document.createElement('div');
                        optionElement.className = 'option';

                        const radioInput = document.createElement('input');
                        radioInput.type = 'radio';
                        radioInput.name = `question-${question.id}`;
                        radioInput.id = `question-${question.id}-option-${option.id}`;
                        radioInput.value = option.id;

                        const label = document.createElement('label');
                        label.htmlFor = `question-${question.id}-option-${option.id}`;
                        label.className = `question-option-label`;
                        const optionBody = `${option.id}. ${option.body}`;
                        label.innerHTML = marked.parse(optionBody);
                        renderMathInElement(label, katexOptions);

                        optionElement.appendChild(radioInput);
                        optionElement.appendChild(label);
                        optionsElement.appendChild(optionElement);
                    });

                    questionElement.appendChild(optionsElement);
                } else {
                    const subjectiveAnswerElement = document.createElement('div');
                    subjectiveAnswerElement.className = 'subjective-answer';

                    const textareaInput = document.createElement('textarea');
                    textareaInput.className = 'subjective-answer-input';
                    textareaInput.name = `question-${question.id}`;
                    textareaInput.id = `question-${question.id}-subjective`;

                    // 添加图片粘贴事件监听
                    textareaInput.addEventListener('paste', (e) => {
                        const clipboardItems = e.clipboardData.items;
                        for (let i = 0; i < clipboardItems.length; i++) {
                            if (clipboardItems[i].type.indexOf('image') !== -1) {
                                const blob = clipboardItems[i].getAsFile();
                                const formData = new FormData();
                                formData.append('image', blob, 'pasted.png');
                                formData.append('question_id', question.id);

                                $('#loading-overlay').show();
                                fetch("{% url 'common_upload_image' %}", {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.code === 0) {
                                        const imgContainer = document.createElement('div');
                                        imgContainer.style.position = 'relative';

                                        const img = document.createElement('img');
                                        img.src = data.data.url;
                                        img.style.maxWidth = '200px';
                                        img.style.maxHeight = '200px';
                                        img.dataset.url = data.data.url;

                                        const deleteBtn = document.createElement('button');
                                        deleteBtn.textContent = '×';
                                        deleteBtn.style.position = 'absolute';
                                        deleteBtn.style.top = '0';
                                        deleteBtn.style.right = '0';
                                        deleteBtn.style.backgroundColor = 'red';
                                        deleteBtn.style.color = 'white';
                                        deleteBtn.style.border = 'none';
                                        deleteBtn.style.borderRadius = '50%';
                                        deleteBtn.style.width = '20px';
                                        deleteBtn.style.height = '20px';
                                        deleteBtn.style.cursor = 'pointer';
                                        deleteBtn.addEventListener('click', () => {
                                            imgContainer.remove();
                                        });

                                        imgContainer.appendChild(img);
                                        imgContainer.appendChild(deleteBtn);

                                        // 确保图片预览区域存在
                                        let imagePreview = document.getElementById(`question-${question.id}-preview`);
                                        if (!imagePreview) {
                                            imagePreview = document.createElement('div');
                                            imagePreview.id = `question-${question.id}-preview`;
                                            imagePreview.style.marginTop = '10px';
                                            imagePreview.style.display = 'flex';
                                            imagePreview.style.flexWrap = 'wrap';
                                            imagePreview.style.gap = '10px';
                                            subjectiveAnswerElement.appendChild(imagePreview);
                                        }

                                        imagePreview.appendChild(imgContainer);
                                    } else {
                                        showCustomAlert('图片上传失败: ' + data.message);
                                    }
                                    $('#loading-overlay').hide();
                                })
                                .catch(error => {
                                    console.error('上传出错:', error);
                                    showCustomAlert('图片上传过程中出错');
                                    $('#loading-overlay').hide();
                                });
                                break;
                            }
                        }
                    });

                    subjectiveAnswerElement.appendChild(textareaInput);

                    // 添加图片上传功能
                    const imageUploadContainer = document.createElement('div');
                    imageUploadContainer.style.marginTop = '10px';

                    const uploadButton = document.createElement('button');
                    uploadButton.type = 'button';
                    uploadButton.textContent = '上传图片';
                    uploadButton.style.padding = '5px 10px';
                    uploadButton.style.marginRight = '10px';
                    uploadButton.style.backgroundColor = '#4CAF50';
                    uploadButton.style.color = 'white';
                    uploadButton.style.border = 'none';
                    uploadButton.style.borderRadius = '4px';
                    uploadButton.style.cursor = 'pointer';

                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'image/*';
                    fileInput.style.display = 'none';
                    fileInput.id = `question-${question.id}-file`;
                    fileInput.multiple = true; // 支持多选

                    const imagePreview = document.createElement('div');
                    imagePreview.id = `question-${question.id}-preview`;
                    imagePreview.style.marginTop = '10px';
                    imagePreview.style.display = 'flex';
                    imagePreview.style.flexWrap = 'wrap';
                    imagePreview.style.gap = '10px';

                    uploadButton.addEventListener('click', () => {
                        fileInput.click();
                    });

                    fileInput.addEventListener('change', (e) => {
                        const files = e.target.files;
                        if (files.length > 0) {
                            $('#loading-overlay').show();

                            const uploadPromises = Array.from(files).map(file => {
                                const formData = new FormData();
                                formData.append('image', file);
                                return fetch("{% url 'common_upload_image' %}", {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json());
                            });

                            Promise.all(uploadPromises)
                                .then(results => {
                                    results.forEach(data => {
                                        if (data.code === 0) {
                                            const imgContainer = document.createElement('div');
                                            imgContainer.style.position = 'relative';

                                            const img = document.createElement('img');
                                            img.className = 'subjective-answer-image';
                                            img.src = data.data.url;
                                            img.style.maxWidth = '200px';
                                            img.style.maxHeight = '200px';
                                            img.dataset.url = data.data.url;

                                            const deleteBtn = document.createElement('button');
                                            deleteBtn.textContent = '×';
                                            deleteBtn.style.position = 'absolute';
                                            deleteBtn.style.top = '0';
                                            deleteBtn.style.right = '0';
                                            deleteBtn.style.backgroundColor = 'red';
                                            deleteBtn.style.color = 'white';
                                            deleteBtn.style.border = 'none';
                                            deleteBtn.style.borderRadius = '50%';
                                            deleteBtn.style.width = '20px';
                                            deleteBtn.style.height = '20px';
                                            deleteBtn.style.cursor = 'pointer';
                                            deleteBtn.addEventListener('click', () => {
                                                imgContainer.remove();
                                            });

                                            imgContainer.appendChild(img);
                                            imgContainer.appendChild(deleteBtn);
                                            imagePreview.appendChild(imgContainer);
                                        } else {
                                            showCustomAlert('部分图片上传失败: ' + data.message);
                                        }
                                    });
                                    $('#loading-overlay').hide();
                                })
                                .catch(error => {
                                    console.error('上传出错:', error);
                                    showCustomAlert('图片上传过程中出错');
                                    $('#loading-overlay').hide();
                                });
                        }
                    });

                    imageUploadContainer.appendChild(uploadButton);
                    imageUploadContainer.appendChild(fileInput);
                    imageUploadContainer.appendChild(imagePreview);
                    subjectiveAnswerElement.appendChild(imageUploadContainer);

                    questionElement.appendChild(subjectiveAnswerElement);
                }

                container.appendChild(questionElement);
            }
        }

        function initExam() {
            $('#loading-overlay').show();
            fetch("{% url 'st_paper_detail' %}?user_id={{ user_id }}", {
                method: 'GET',
            })
            .then(response => response.json())
            .then(data => {
                if (data.code !== 0) {
                    showCustomAlert(data.msg);
                    return;
                }
                const examData = data.data
                userId = examData.user_id
                paperId = examData.paper_id
                renderExam(examData)
                $('#loading-overlay').hide();
            })
            .catch(error => {
                console.error('获取题目错误:', error);
                showCustomAlert('获取题目错误');
                $('#loading-overlay').hide();
            });
        }

        let userId = null;
        let paperId = null;
        $(function() {
            // 绑定自定义alert按钮事件
            $('#custom-alert-button').click(function() {
                $('#custom-alert-overlay').hide();
                $('#custom-alert').hide();
            });

            initExam()

            $('#submit-btn').click(function() {
                // 收集所有答案
                const answers = [];
                let allAnswered = true;

                $('.question').each(function() {
                    const questionId = parseInt($(this).data('id'))
                    const questionType = parseInt($(this).data('type'))
                    if (questionType === 0 || questionType === 1) {
                        const selectedOption = $(this).find('input[type="radio"]:checked').val();
                        if (selectedOption) {
                            answers.push({
                                question_id: questionId,
                                question_type: questionType,
                                answer: selectedOption
                            });
                        } else {
                            allAnswered = false;
                            return false; // 退出循环
                        }

                    } else {
                        const subjectiveText = $(this).find('.subjective-answer-input').val()
                        const image_urls = Array.from($(this).find('.subjective-answer-image')).map(img => $(img).data('url'))

                        if (subjectiveText || image_urls.length > 0) {
                            answers.push({
                                question_id: questionId,
                                question_type: questionType,
                                answer: subjectiveText,
                                image_urls: image_urls
                            });
                        } else {
                            allAnswered = false;
                            return false; // 退出循环
                        }
                    }
                })

                if (!allAnswered) {
                    showCustomAlert('请回答所有题目后再提交！');
                    return;
                }

                // 显示loading
                $('#loading-overlay').show();

                // 提交答案到服务器
                fetch("{% url 'st_paper_submit' %}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        paper_id: paperId,
                        answers: answers
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log("服务器返回数据:", data); // 调试用

                    if (data.code === 0) {
                        // 显示等待报告生成的消息
                        $('#loading-overlay').hide();
                        showCustomAlert('试卷提交成功，正在生成分析报告，请稍候...');

                        // 存储报告数据到localStorage
                        localStorage.setItem('reportData', JSON.stringify(data.data));

                        // 延迟跳转到报告页面
                        setTimeout(function() {
                            window.location.href = "{% url 'st_paper_report' %}?user_id=" + userId;
                        }, 2000);
                    } else {
                        showCustomAlert('提交失败: ' + data.message);
                        $('#loading-overlay').hide();
                    }
                })
                .catch(error => {
                    console.error('提交出错:', error);
                    showCustomAlert('提交过程中出错');
                    $('#loading-overlay').hide();
                });
            })
        })

    </script>
</body>
</html>