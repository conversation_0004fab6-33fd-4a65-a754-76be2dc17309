"""
文本分析工具类
用于从文本中提取特定的结构化信息
"""
import re
import logging

logger = logging.getLogger(__name__)


class TextAnalysisUtils:
    """文本分析工具类"""
    
    @staticmethod
    def extract_entrance_foundation_positioning(text: str) -> str:
        """
        从文本中提取"五、入学基础定位"部分
        
        Args:
            text (str): 输入文本
            
        Returns:
            str: 提取的入学基础定位部分内容
        """
        if not text:
            return ""
            
        try:
            # 将文本转换为字符串（如果是其他类型）
            text_str = str(text)
            
            # 定义可能的标题模式
            patterns = [
                r'五、入学基础定位[：:]\s*(.*?)(?=六、|$)',
                r'五、入学基础定位\s*(.*?)(?=六、|$)',
                r'入学基础定位[：:]\s*(.*?)(?=六、|数据总结|$)',
                r'入学基础定位\s*(.*?)(?=六、|数据总结|$)',
                r'5、入学基础定位[：:]\s*(.*?)(?=6、|$)',
                r'5、入学基础定位\s*(.*?)(?=6、|$)',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text_str, re.DOTALL | re.IGNORECASE)
                if match:
                    content = match.group(1).strip()
                    if content:
                        logger.debug(f"使用模式 '{pattern}' 成功提取入学基础定位，长度: {len(content)}")
                        return content
            
            # 如果没有找到标准格式，尝试查找包含"基础"、"定位"等关键词的段落
            lines = text_str.split('\n')
            start_idx = -1
            end_idx = len(lines)
            
            for i, line in enumerate(lines):
                if any(keyword in line for keyword in ['入学基础定位', '基础定位', '学习基础']):
                    start_idx = i
                    break
            
            if start_idx >= 0:
                # 查找结束位置
                for i in range(start_idx + 1, len(lines)):
                    line = lines[i].strip()
                    if re.match(r'^[六6]、|^六、|数据总结|对比分析', line):
                        end_idx = i
                        break
                
                if start_idx < end_idx:
                    content = '\n'.join(lines[start_idx:end_idx]).strip()
                    if content:
                        logger.debug(f"通过关键词匹配提取入学基础定位，长度: {len(content)}")
                        return content
            
            logger.warning("未能提取到入学基础定位部分")
            return ""
            
        except Exception as e:
            logger.error(f"提取入学基础定位时发生错误: {str(e)}", exc_info=True)
            return ""
    
    @staticmethod
    def extract_data_summary_comparison(text: str) -> str:
        """
        从文本中提取"六、数据总结与对比"部分
        
        Args:
            text (str): 输入文本
            
        Returns:
            str: 提取的数据总结与对比部分内容
        """
        if not text:
            return ""
            
        try:
            # 将文本转换为字符串（如果是其他类型）
            text_str = str(text)
            
            # 定义可能的标题模式
            patterns = [
                r'六、数据总结与对比[：:]\s*(.*?)(?=七、|$)',
                r'六、数据总结与对比\s*(.*?)(?=七、|$)',
                r'数据总结与对比[：:]\s*(.*?)(?=七、|$)',
                r'数据总结与对比\s*(.*?)(?=七、|$)',
                r'6、数据总结与对比[：:]\s*(.*?)(?=7、|$)',
                r'6、数据总结与对比\s*(.*?)(?=7、|$)',
                r'数据总结[：:]\s*(.*?)(?=七、|$)',
                r'数据对比[：:]\s*(.*?)(?=七、|$)',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text_str, re.DOTALL | re.IGNORECASE)
                if match:
                    content = match.group(1).strip()
                    if content:
                        logger.debug(f"使用模式 '{pattern}' 成功提取数据总结与对比，长度: {len(content)}")
                        return content
            
            # 如果没有找到标准格式，尝试查找包含"数据"、"总结"、"对比"等关键词的段落
            lines = text_str.split('\n')
            start_idx = -1
            end_idx = len(lines)
            
            for i, line in enumerate(lines):
                if any(keyword in line for keyword in ['数据总结与对比', '数据总结', '数据对比', '总结对比']):
                    start_idx = i
                    break
            
            if start_idx >= 0:
                # 查找结束位置
                for i in range(start_idx + 1, len(lines)):
                    line = lines[i].strip()
                    if re.match(r'^[七7]、|^七、|^结论|^建议', line):
                        end_idx = i
                        break
                
                if start_idx < end_idx:
                    content = '\n'.join(lines[start_idx:end_idx]).strip()
                    if content:
                        logger.debug(f"通过关键词匹配提取数据总结与对比，长度: {len(content)}")
                        return content
            
            logger.warning("未能提取到数据总结与对比部分")
            return ""
            
        except Exception as e:
            logger.error(f"提取数据总结与对比时发生错误: {str(e)}", exc_info=True)
            return ""
    
    @staticmethod
    def extract_section_by_title(text: str, title: str, next_title: str = None) -> str:
        """
        通用的章节提取方法
        
        Args:
            text (str): 输入文本
            title (str): 要提取的章节标题
            next_title (str): 下一个章节标题（用于确定结束位置）
            
        Returns:
            str: 提取的章节内容
        """
        if not text or not title:
            return ""
            
        try:
            text_str = str(text)
            
            # 构建正则表达式模式
            title_pattern = re.escape(title)
            if next_title:
                next_pattern = re.escape(next_title)
                pattern = f'{title_pattern}[：:]?\\s*(.*?)(?={next_pattern}|$)'
            else:
                pattern = f'{title_pattern}[：:]?\\s*(.*?)$'
            
            match = re.search(pattern, text_str, re.DOTALL | re.IGNORECASE)
            if match:
                content = match.group(1).strip()
                logger.debug(f"成功提取章节 '{title}'，长度: {len(content)}")
                return content
            
            logger.warning(f"未能提取到章节 '{title}'")
            return ""
            
        except Exception as e:
            logger.error(f"提取章节 '{title}' 时发生错误: {str(e)}", exc_info=True)
            return ""
