<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用消息列表页面</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/marked/13.0.2/marked.min.js"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <script src="https://cdn.kaoyanvip.cn/katex@0.16.9/katex.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/katex@0.16.9/contrib/auto-render.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        td {
            vertical-align: top;
        }

        th {
            background-color: #f2f2f2;
        }
        tr.fixed th {
            position: sticky;
            top: -2px; /* 固定在顶部 */
            z-index: 10; /* 确保在其他内容之上 */
        }

        .table-container {
            max-height: 80vh; /* 设置容器高度以启用滚动 */
            width: 100%; /* 容器宽度 */
            overflow-x: auto; /* 启用水平滚动 */
            overflow-y: auto; /* 启用垂直滚动 */
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        #pagination {
            float: right;
            margin-right: 20px;
        }
        /*.markdown-body {
            max-width: 350px;
        }*/
        .message-content {
            max-height: 400px; /* 设置最大高度 */
            overflow-y: auto; /* 纵向滚动 */
        }
        .markdown-body pre {
            max-width: 340px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .modal-content {
            position: relative;
            background-color: white;
            margin: 20% auto;
            padding: 20px;
            width: 50%;
            height: 300px;
            border: 1px solid #ccc;
        }
        .modal-content.large {
            margin: 10% auto;
            width: 75%;
            height: 500px;
        }
        .modal-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            font-weight: bold;
        }
        .modal-body {
            padding: 10px;
            height: 200px;
            overflow-y: scroll;
        }
        .modal-content.large .modal-body {
            height: 400px;
        }
        .modal-footer {
            border-top: 1px solid #eee;
            text-align: right;
            padding-top: 15px;
            margin-right: 25px;
        }
        .modal-content img {
            max-width: 100%;
            height: 300px;
        }
        .answer-body {
            white-space: pre-line;
        }
        .modal .close {
            cursor: pointer;
            position: absolute;
            right: 20px;
            top: 15px;
        }

        #reason {
            width: 70%;
        }
        .fail-image img {
            width: 70px;
            height: 70px;
            cursor: pointer;
        }
        .opt-btn-group {
            display: flex;
            flex-direction: column;
        }
        .opt-btn-group button {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
<h1>
    {% block title %}
        <span>【{{ app_name }}】消息列表页面</span>
    {% endblock %}
    {% block back %}
        <a style="font-size: 18px" href="{% url 'app_message_stats' %}">返回</a>
    {% endblock %}
</h1>
<div class="table-container" id="table-container">
    <table id="data-table">
        <thead>
        <tr class="fixed">
            <th>账号 ID</th>
            <th style="width:70px">提问时间</th>
            {% if not is_inner_app %}
            <th style="width:70px">所属应用</th>
                {% if app_no != 'shuati_answer_report' and app_no != 'shuati_subjective_report' %}
                <th style="width:70px">课程名称</th>
                <th style="width:70px">平台</th>
                {% endif %}
            {% endif %}
            <th>提问内容</th>
                {% if app_no != 'shuati_answer_report' and app_no != 'shuati_subjective_report' %}
                <th>思考</th>
                {% endif %}
            <th >回答</th>
            <th>tokens</th>
            <th>是否异常</th>
            <th>是否中止</th>
            <th>异常原因</th>
            <th width="70">操作</th>
        </tr>
        </thead>
        <tbody id="table-body">
        {% for msg in message_list %}
            <tr>
                <td style="max-width: 80px; word-wrap: break-word;">{{ msg.user_id }}</td>
                <td>{{ msg.add_time }}</td>
                {% if not is_inner_app %}
                <td>{{ msg.app_name }}</td>
                    {% if app_no != 'shuati_answer_report' and app_no != 'shuati_subjective_report' %}
                    <td>{{ msg.goods_name }}</td>
                    <td>{{ msg.platform }}</td>
                    {% endif %}
                {% endif %}
{#                <td style="min-width:100px; max-width: 300px; overflow: auto;">#}
                <td>
                    <div class="markdown-body message-content" style="max-width:500px; overflow: auto;">{{ msg.query }}</div>
                    {% if msg.question_img %}
                        <div class="image-preview-show"><img width="50" height="50" src="{{ msg.question_img }}" alt=""></div>
                    {% endif %}
                </td>
                {% if app_no != 'shuati_answer_report' and app_no != 'shuati_subjective_report' %}
                <td style="min-width:100px; max-width: 250px; overflow: auto;">
                    {% if msg.reasoning %}
                        <div class="markdown-body message-content">{{ msg.reasoning }}</div>
                    {% else %}
                        -
                    {% endif %}
                </td>
                {% endif %}
                <td>
                    <div class="markdown-body message-content" style="max-width:500px; overflow: auto;">{{ msg.answer }}</div>
                </td>
                <td class="message-token">
                    <div>提问：{{ msg.message_tokens }}</div>
                    <div>回答：{{ msg.answer_tokens }}</div>
                </td>
                <td>
                    {% if msg.is_exception %} ❌异常 {% else %} ✅正常 {% endif %}
                </td>
                <td>
                    {% if msg.stopped_by %}
                        消息中止
                    {% else %}
                        否
                    {% endif %}
                </td>
                <td>
                    {% if msg.is_exception %}
                        {% if msg.exception_reason %}
                            <div>失败原因：{{ msg.exception_reason }}</div>
                        {% endif %}
                        {% if msg.exception_image %}
                            <div class="fail-image image-preview-show">
                                <img width="50" height="50" src="{{ msg.exception_image }}" alt="">
                            </div>
                        {% endif %}
                    {% endif %}
                </td>
                <td>
                    <div class="opt-btn-group">
                        {% if app_no == 'knowledge_query' or app_no == 'chat_app2' %}
                        <button postUrl="{% url 'message_tracing_detail' %}?message_id={{ msg.message_no }}" class="op_btn tracing_btn">
                                分配详情
                        </button>
                        {% endif %}
                        {% if msg.is_exception %}
                            <button postUrl="{% url 'app_msg_mark_not_exception' app_id msg.id %}" class="op_btn unMarkButton">
                                取消异常
                            </button>
                        {% else %}
                            <button postUrl="{% url 'app_msg_mark_exception' app_id msg.id %}" class="op_btn markButton showModalButton">
                                标记异常
                            </button>
                        {% endif %}
                    </div>
                </td>
            </tr>
        {% endfor %}
        </tbody>

    </table>
</div>

<br>


<div class="modal" id="previewModal">
    <div class="modal-content" id="preview-img">
    </div>
</div>


<div class="modal" id="tracingModal">
    <div class="modal-content large">
        <div class="close">X</div>
        <div class="modal-header">消息详情查看</div>
        <div class="modal-body" style="height: 425px;">
            <table>
                <thead>
                    <tr>
                        <th>类型</th>
                        <th>时间</th>
{#                        <th>输入内容</th>#}
                        <th>输出内容</th>
                        <th>使用模型</th>
{#                        <th>tokens</th>#}
                    </tr>
                </thead>
                <tbody class="tracing-list">
                </tbody>
            </table>
        </div>
{#        <div class="modal-footer">#}
{#            <button class="close-btn">关闭</button>#}
{#        </div>#}
    </div>
</div>


<div class="modal" id="modal">
    <div class="modal-content">
        <form id="exceptionForm">
            <div class="fail-reason" style="margin-bottom: 30px">
                <span>异常原因: </span>
                <input type="text" id="reason" name="reason" placeholder="输入异常原因">
            </div>

            <div class="fail-image" style="margin-bottom: 30px">
                <span>异常图片: </span>
                <input id="image" type="file" name="image" accept="image/*">
            </div>

            <button id="submitModal">确认</button>
        </form>
    </div>
</div>

{% block pagination %}
{#<p id="pagination">#}
    {# 上一页 #}
{#    {% if prev_page > 0 %}#}
{#        <button>#}
{#            <a href="{% if message_type %}{% url 'app_message_type_list' app_id message_type %}{% else %}{% url 'app_message_list' app_id %}{% endif %}?page={{ prev_page }}&limit={{ limit }}">上一页</a>#}
{#        </button>#}
{#    {% else %}#}
{#        <button disabled>上一页</button>#}
{#    {% endif %}#}
{##}
    {# 下一页 #}
{#    {% if next_page > 0 %}#}
{#        <button>#}
{#            <a href="{% if message_type %}{% url 'app_message_type_list' app_id message_type %}{% else %}{% url 'app_message_list' app_id %}{% endif %}?page={{ next_page }}&limit={{ limit }}">下一页</a>#}
{#        </button>#}
{#    {% else %}#}
{#        <button disabled>下一页</button>#}
{#    {% endif %}#}
{#</p>#}
{% endblock %}


<script>
    function markNotException(url, is_exception, reason) {
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({is_exception: is_exception, reason: reason}) // 将要发送的数据放在这里
        })
            .then(response => {
                if (response.ok) {
                    console.log('action success')
                    window.location.reload()
                }
                throw new Error('网络请求失败');
            })
    }
</script>

<script>
    const showModalButtons = document.querySelectorAll('.showModalButton');
    const unMarkButtons = document.querySelectorAll('.unMarkButton');
    const modal = document.getElementById('modal');
    const table = document.getElementById("data-table");
    const previewModal = document.getElementById('previewModal');
    const submitModal = document.getElementById('submitModal');
    const $exceptionForm = $('#exceptionForm');
    const katexOptions = {
        // customised options
        // • auto-render specific keys, e.g.:
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        // • rendering keys, e.g.:
        throwOnError : false
    }

    const md_list = document.querySelectorAll('.markdown-body');
    md_list.forEach(md_item => {
        md_item.innerHTML = marked.parse(md_item.innerHTML);
    });

    $('#table-body').on('click', '.tracing_btn', function() {
        $('.tracing-list').html('')

        const url = $(this).attr('postUrl');
        fetch(url, {
            method: 'GET',
        })
      .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
          let html = ''
          for (let i = 0; i < data.data.tracing.length; i++) {
              const log = data.data.tracing[i]
              const cls_name = log.is_answer_markdown ? 'markdown-body' : 'answer-body'
              html += `
                <tr>
                    <td style="width: 150px">${log.log_type}</td>
                    <td style="width: 150px">${log.add_time}</td>
                    {#<td style="width: 100px">#}
                    {#    <div style="max-height: 150px; overflow-y: auto;">${log.input_content}</div>#}
                    {#</td>#}
                    <td>
                        <div class="${cls_name}" style="max-height: 150px; width: 650px; overflow-y: auto;">${log.output_content}</div>
                    </td>
                    <td style="width: 150px">${log.model_id}</td>
{#                    <td style="width: 80px"><div>提问：${log.message_tokens}</div><div>回答：${log.answer_tokens}</div></td>#}
                </tr>
              `
          }
            $('.tracing-list').html(html)
            $('.tracing-list').find('.markdown-body').each(function() {
                console.log(this)
                this.innerHTML = marked.parse(this.innerHTML);
                renderMathInElement(this, katexOptions)
            })
            $('#tracingModal').show()
      })
      .catch(error => {
          console.error('Error:', error);
      });

    })
    $('.modal .close').on('click', function () {
        $('.modal').hide()
    })
    $('.modal .close-btn').on('click', function () {
        $('.modal').hide()
    })
    
    table.addEventListener("click",(res) =>{
        let target = res.target
        if (target.classList.contains('showModalButton')){
            console.log(target)
            modal.removeAttribute('postUrl');
            $('#image').val('')
            $('#reason').val('');
            modal.style.display = 'block';
            let postUrl = target.getAttribute('postUrl');
            modal.setAttribute('postUrl', postUrl);
            
        } else if (target.classList.contains('unMarkButton')){
            let postUrl = target.getAttribute('postUrl');
            markNotException(postUrl, false, '')
            window.location.reload()
        }
    });
    
    // 为每个标记按钮添加点击事件处理程序
    /*
    showModalButtons.forEach(button => {
        button.addEventListener('click', function (event) {
            modal.removeAttribute('postUrl');
            $('#image').val('')
            $('#reason').val('');

            modal.style.display = 'block';
            let postUrl = event.target.getAttribute('postUrl');
            modal.setAttribute('postUrl', postUrl);
        });
    });
     */
    // 为每个标记按钮添加点击事件处理程序
    /*
    unMarkButtons.forEach(button => {
        button.addEventListener('click', function (event) {
            let postUrl = event.target.getAttribute('postUrl');
            markNotException(postUrl, false, '')
        });
    });
*/
    $exceptionForm.submit(function(e) {
        e.preventDefault(); // 阻止表单的默认提交行为
        submit_exception();
        modal.style.display = 'none';
    })

    function submit_exception() {
        const formData = new FormData($exceptionForm[0]);
        const postUrl = modal.getAttribute('postUrl');

        fetch(postUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                console.log('action success')
                return response.json();
            }
            throw new Error('网络请求失败');
        })
        .then(result => {
            console.log(result)
            if (result.code === 0) {
                window.location.reload()
            }
        })
        .catch(error => {
            console.error('Error:', error);
            throw new Error('网络请求失败');
        });

    }

    $('.image-preview-show').on('click', function () {
        const src = $(this).find('img')[0].src
        console.log(src)

        $('#preview-img').html(`
            <img src="${src}"  alt=""/>
        `)
        previewModal.style.display = 'block';
    })

    // 点击模态框外部区域关闭模态框
    window.onclick = function (event) {
        if (event.target === modal) {
            modal.removeAttribute('postUrl');
            modal.style.display = 'none';
        } else if (event.target === previewModal) {
            previewModal.style.display = 'none';
        }
    };

    $('.markdown-body').each(function() {
        renderMathInElement(this, katexOptions)
    })
</script>

{% block stream_page %}
    
<div id="loading" style="display: none;">加载中...</div>

<script>
    let currentPage = 1;
    let isLoading = false;
    let hasMore = true;
    
    // 初始加载第一页数据
    window.addEventListener('DOMContentLoaded', (event) => {
        currentPage = 1;
        setupInfiniteScroll();
    });
    
    function setupInfiniteScroll() {
        var dom = document.getElementById("table-container")
        dom.addEventListener('scroll', function() {
            console.log('xxxxxx')
            const { scrollTop, scrollHeight, clientHeight } = dom;
            
            // 当滚动到接近底部时加载更多
            if (scrollTop + clientHeight >= scrollHeight - 100 && !isLoading && hasMore) {
                loadMoreData();
            }
        });
    }
    
    function replaceLastZero(str, replacement) {
      const lastIndex = str.lastIndexOf('0');
      if (lastIndex === -1) return str;
      return str.substring(0, lastIndex) + replacement + str.substring(lastIndex + 1);
    }
    
    function loadMoreData() {
        isLoading = true;
        document.getElementById('loading').style.display = 'block';
        
        currentPage++;
        
        const options = {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json',
            },
        };
        
        fetch(`?page=${currentPage}`, options)
        .then(response => response.json())
        .then(data => {
            if (data.message_list.length > 0) {
                const tableBody = document.getElementById('table-body');
                console.log(1111)
                console.log(data.message_list)
                
                // 添加新数据到表格
                data.message_list.forEach(msg => {
                    const row = document.createElement('tr');
                    
                    row.innerHTML = `
                        <td style="max-width: 80px; word-wrap: break-word;">${msg.user_id ? msg.user_id:''}</td>
                        <td>${msg.add_time ? msg.add_time:null}</td>
                    `
                    if ( !data.is_inner_app){
                        if (msg.app_no === 'shuati_answer_report' || msg.app_no === 'shuati_subjective_report') {
                            row.innerHTML += `
                                <td>${msg.app_name ? msg.app_name:''}</td>
                            `
                        } else {
                            row.innerHTML += `
                                <td>${msg.app_name ? msg.app_name:''}</td>
                                <td>${msg.goods_name ? msg.goods_name:''}</td>
                                <td>${msg.platform ? msg.platform:''}</td>
                            `
                        }
                    }
                    if (msg.question_img){
                        row.innerHTML += `
                            <td>
                                <div class="markdown-body message-content">${marked.parse(msg.query|| '')}</div>
                                <div class="image-preview-show"><img width="50" height="50" src="${msg.question_img}" alt=""></div>
                            </td>
                        `
                    }else {
                        row.innerHTML += `
                            <td>
                                <div class="markdown-body message-content">${marked.parse(msg.query|| '')}</div>
                            </td>
                        `
                    }
                    if (msg.app_type !== 'shuati_answer_report' && msg.app_type !== 'shuati_subjective_report') {
                        row.innerHTML += `
                            <td>
                                <div class="markdown-body message-content">${marked.parse(msg.reasoning || '-')}</div>
                            </td>
                        `
                    }

                    row.innerHTML += `
                         <td>
                            <div class="markdown-body message-content" style="max-width: 500px;overflow: auto;">
                            ${marked.parse(msg.answer|| '')}
                            </div>
                        </td>
                        <td class="message-token">
                            <div>提问：${msg.message_tokens}</div>
                            <div>回答：${msg.answer_tokens}</div>
                        </td>
                        <td>
                            ${msg.is_exception ? '❌异常': '✅正常'}
                        </td>
                    `
                    if (msg.stopped_by) {
                        row.innerHTML += `<td>否</td>`
                    } else {
                        row.innerHTML += `<td>消息中止</td>`
                    }
                    if (msg.is_exception){
                        let exception_reason = null;
                        let exception_image = null;
                        if (msg.exception_reason){
                            exception_reason = `<div>失败原因：${msg.exception_reason}</div>`
                        }
                        if (msg.exception_image){
                            exception_image = `
                            <div class="fail-image image-preview-show">
                                <img width="50" height="50" src="${msg.exception_image}" alt="">
                            </div>
                            `
                        }
                        if (exception_reason && exception_image){
                            row.innerHTML += `
                                <td>
                                     ${exception_reason}
                                     ${exception_image}
                                </td>
                            `
                        } else if (exception_reason && !exception_image) {
                            row.innerHTML += `
                                <td>
                                     ${exception_reason}
                                </td>
                            `
                        } else if (!exception_reason && exception_image) {
                            row.innerHTML += `
                                <td>
                                     ${exception_image}
                                </td>
                            `
                        }
                    }else{
                        row.innerHTML += '<td></td>'
                    }
                    
                    let exception_button = null;
                    if (msg.is_exception){
                        let url = "{% url 'app_msg_mark_not_exception' app_id 0 %}"
                        let new_url = replaceLastZero(url, msg.id)
                        exception_button = `
                            <button postUrl="${new_url}" class="op_btn unMarkButton">
                                  取消异常
                            </button>
                        `
                    }else {
                        let url = "{% url 'app_msg_mark_exception' app_id 0 %}"
                        let new_url = replaceLastZero(url, msg.id)
                        exception_button = `
                            <button postUrl="${new_url}" class="op_btn markButton showModalButton">
                                 标记异常
                            </button>
                        `
                    }
                    let detail_button = null;
                    if (data.app_type === 'knowledge_query' || data.app_type === 'chat_app2'){
                        let url = "{% url 'message_tracing_detail' %}"
                        let new_url = url += `?message_id=${msg.message_no}`
                        detail_button = `
                            <button postUrl="${new_url}" class="op_btn tracing_btn">
                                   分配详情
                            </button>
                        `
                    }
                    if (detail_button && exception_button){
                        row.innerHTML += `
                            <td>
                                <div class="opt-btn-group">
                                    ${detail_button}
                                    ${exception_button}
                                </div>
                            </td>
                        `
                    }else if (!detail_button && exception_button){
                        row.innerHTML += `
                            <td>
                                <div class="opt-btn-group">
                                    ${exception_button}
                                </div>
                            </td>
                        `
                    }
                
                    tableBody.appendChild(row);
                });
                
                $('.image-preview-show').on('click', function () {
                    const src = $(this).find('img')[0].src
                    console.log(src)
            
                    $('#preview-img').html(`
                        <img src="${src}"  alt=""/>
                    `)
                    previewModal.style.display = 'block';
                })
                
                hasMore = currentPage < data.next_page;
            } else {
                hasMore = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            currentPage--;  // 出错时回退页码
        })
        .finally(() => {
            isLoading = false;
            document.getElementById('loading').style.display = 'none';
        });
    }
</script>

{% endblock %}

</body>
</html>