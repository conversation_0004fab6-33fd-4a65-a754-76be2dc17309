import json
import logging

from app.models import CozeWorkflowResult, EnglishPaperExplain, EnglishPaperQuestionExplain

logger = logging.getLogger(__name__)


"""
解析完形填空
返回格式：
- 用户答题情况{{wx_user_situtation}}：
- 分析错误原因{{wx_cuowuyuanyin}}：
- 梳理文章结构{{wx_article_fenxi}}：
- 备考建议{{wx_suggestions}}：

解析完形填空
返回格式：
- 用户答题情况{{wx_user_situtation}}：
- 分析错误原因{{wx_cuowuyuanyin}}：
- 梳理文章结构{{wx_article_fenxi}}：
- 备考建议{{wx_suggestions}}：

    解析阅读理解
返回格式：
- 用户答题情况{{yd_user_situtation}}：
- 分析错误原因{{yd_cuowuyuanyin}}：
- 梳理文章结构{{yd_article_fenxi}}：
- 备考建议{{yd_suggestions}}：

    解析新题型
返回格式：
- 用户新题型答题情况{{xtx_user_situtation}}：
- 分析七选五错误原因{{qxw_cuowuyuanyin}}：
- 梳理七选五文章结构{{qxw_article_fenxi}}：
- 七选五备考建议{{qxw_suggestions}}：

- 分析排序题错误原因{{paixu_cuowuyuanyin}}：
- 梳理排序题文章结构{{paixu_article_fenxi}}：
- 排序题备考建议{{paixu_suggestions}}：

    解析翻译
返回格式：
- 用户英语一翻译情况{{fanyi_fenxi1}}：
- 用户英语二翻译情况{{fanyi_fenxi2}}：
- 用户翻译题答题情况{{fy_situtation}}

    解析小作文
返回格式：
- 用户答题情况(英语一){{ying1_xzw_fenxi}}：
- 用户答题情况(英语二){{ying2_xzw_fenxi}}：
- 用户小作文答题情况{{xzw_situtation}}：

    解析大作文
返回格式：
- 用户答题情况(英语一){{ying1_dzw_fenxi}}：
- 用户答题情况(英语二){{ying2_dzw_fenxi}}：
- 用户大作文答题情况{{dzw_situtation}}：
"""


class EnPaperExplainProcessService:

    @classmethod
    def get_question_explain(cls, workflow_result: CozeWorkflowResult) -> EnglishPaperQuestionExplain | None:
        ext_params = workflow_result.ext_params
        paper_explain_id = ext_params.get('paper_explain_id')
        question_id = ext_params.get('question_id')

        paper_explain: EnglishPaperExplain = EnglishPaperExplain.objects.filter(
            is_deleted=False, id=paper_explain_id).first()
        if not paper_explain:
            return None
        return paper_explain.englishpaperquestionexplain_set.filter(
            is_deleted=False, question_id=question_id).first()

    @classmethod
    def process_question_explain(cls, workflow_result: CozeWorkflowResult):
        question_explain = cls.get_question_explain(workflow_result)
        if not question_explain:
            return

        try:
            output_dict = json.loads(workflow_result.output_str)
            explain_info = json.loads(output_dict.get('Output'))

            question_explain.explain_content = explain_info
            question_explain.status = 'SUCCESS'
            question_explain.save()
        except Exception as e:
            logger.exception(e)
            question_explain.status = 'FAIL'
            question_explain.save()

    @classmethod
    def process_summary_explain(cls, workflow_result: CozeWorkflowResult):
        """
        解析总结
        返回格式：
        - {{conclusion}} 总结
        """

        ext_params = workflow_result.ext_params
        paper_explain_id = ext_params.get('paper_explain_id')

        paper_explain: EnglishPaperExplain = EnglishPaperExplain.objects.filter(
            is_deleted=False, id=paper_explain_id).first()
        if not paper_explain:
            return

        try:
            output_dict = json.loads(workflow_result.output_str)
            explain_info = json.loads(output_dict.get('Output'))

            paper_explain.summary_content = explain_info
            paper_explain.summary_status = 'SUCCESS'
            paper_explain.save()
        except Exception as e:
            logger.exception(e)
            paper_explain.summary_status = 'FAIL'
            paper_explain.save()
