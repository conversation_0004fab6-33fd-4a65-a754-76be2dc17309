<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>【{{ env }}环境】{{ prompt_name }}</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .help-text {
            display: block;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .page-title {
            margin: 20px auto;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }
        .container {
            padding: 20px 30px;
            width: 800px;
            min-height: 650px; /* 固定高度 */
            margin: 20px auto;
            background-color: #fff;
        }
        h2 {
            color: #333;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #666;
        }
        form {
            display: flex;
            flex-direction: column;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        input:focus, textarea:focus {
            border-color: #007bff;
            outline: none;
        }
        input {
            width: 200px;
        }
        textarea {
            resize: vertical;
            height: 400px; /* 设置一个初始高度 */
            margin-bottom: 0;
        }
        button {
            width: 150px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        /* 加载框样式 */
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>正在请求...</p>
        </div>
    </div>
    <div class="page-title">【{{ env }}环境】提示词管理：{{ prompt_name }}</div>
    <div class="container">
        <form id="myForm" action="{% url 'prompt_publish' app_no %}">
            <div class="form-group">
                <label for="max_tokens">max_tokens:</label>
                <input type="number" id="max_tokens" name="max_tokens" placeholder="Enter max_tokens here..."
                       value="{{ model_params.max_tokens }}">
            </div>
            <div class="form-group">
                <label for="temperature">temperature:</label>
                <input type="number" id="temperature" name="temperature" placeholder="Enter temperature here..."
                       value="{{ model_params.temperature }}" step="0.1" min="0" max="0.9">
            </div>

            <div class="form-group" style="margin-bottom: 10px">
                <label for="prompt">提示词:</label>
                <span class="help-text">两个大括号内的参数（例如query）为参数占位符</span>
                <textarea id="prompt" name="prompt" rows="3" required placeholder="Enter prompt here...">{{ prompt_content }}</textarea>
                <span id="queryCount">0</span> 字符
            </div>

            <button type="submit">提交</button>
        </form>
    </div>
    <script>
        $(function() {
            function updateCharCount() {
                $('#queryCount').text($('#prompt').val().length);
            }
            $('#prompt').on('input', updateCharCount);
            updateCharCount();

            $('.publish').on('click', () => {
                const url = $('.publish').data('url')
                const prompt = $('#prompt').val()

                if (!prompt) {
                    alert('提示词不能为空')
                    return
                }

                if (!confirm("确认要发布吗？")) {
                    return
                }

                const template = getTemplate()


                $.ajax({
                    type: 'POST',
                    url, // 替换为你的服务器端点
                    contentType: 'application/json',
                    data: JSON.stringify({
                        template_id: template.id,
                        prompt,
                    }),
                    success: function(response) {
                        // 处理响应数据
                        alert('请求成功')
                    },
                    error: function(xhr, status, error) {
                        // 处理错误情况
                        alert('请求失败')
                    }
                });

                console.log(url)

            })

            $('#myForm').submit(function(e) {
                e.preventDefault(); // 阻止表单的默认提交行为

                const formData = {
                    max_tokens: parseInt($('#max_tokens').val()),
                    temperature: parseFloat($('#temperature').val()),
                    prompt: $('#prompt').val(),
                };

                if (formData.max_tokens < 0) {
                    alert('max_tokens值错误')
                    return
                }
                if (formData.temperature < 0 || formData.temperature > 0.9) {
                    alert('temperature值错误【0-0.9】')
                    return
                }
                if (!formData.prompt) {
                    alert('提示词不能为空')
                    return
                }

                const password = prompt("请输入密码以确认提交:");
                if (!password) {
                    return
                }
                formData.password = password

                if (!confirm("确定要提交吗？")) {
                    return
                }

                $('#loadingOverlay').css('display', 'flex')
                $.ajax({
                    type: 'POST',
                    url: $('#myForm').attr('action'), // 替换为你的服务器端点
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        // 处理响应数据
                        $('#loadingOverlay').css('display', 'none')
                        if (response.code !== 0) {
                            alert(response.msg)
                        } else {
                            alert('提交成功')
                        }
                    },
                    error: function(xhr, status, error) {
                        // 处理错误情况
                        console.log(xhr, status, error)
                        $('#loadingOverlay').css('display', 'none')
                        alert('提交失败')
                    }
                });
            })

        })

    </script>
</body>
</html>