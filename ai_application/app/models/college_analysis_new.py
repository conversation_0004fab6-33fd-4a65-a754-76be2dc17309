from django.db import models
from ._base import BaseModel


class MajorInfo(BaseModel):
    # 专业代码
    major_code = models.CharField(max_length=10, verbose_name='专业代码', null=True, blank=True)

    # 专业评估
    major_evaluation = models.CharField(max_length=10, verbose_name='专业评估', null=True, blank=True)

    # 专业类型
    major_type = models.CharField(max_length=10, verbose_name='专业类型', null=True, blank=True)

    # 院校名称
    college_name = models.CharField(max_length=255, verbose_name='院校名称', null=True, blank=True)

    # 院校代码
    college_code = models.CharField(max_length=10, verbose_name='院校代码', null=True, blank=True)

    # 院校层次
    college_level = models.CharField(max_length=255, verbose_name='院校层次', null=True, blank=True)

    # 院校性质
    college_nature = models.CharField(max_length=255, verbose_name='院校性质', null=True, blank=True)

    # 院校排名
    college_rank = models.CharField(max_length=255, verbose_name='院校排名', null=True, blank=True)

    # 是否是博士点
    is_doctoral_point = models.BooleanField(verbose_name='是否是博士点', null=True, blank=True)

    # 是否有奖学金
    has_scholarship = models.BooleanField(verbose_name='是否有奖学金', null=True, blank=True)

    # 院校所在省份
    province = models.CharField(max_length=100, verbose_name='所在省份', null=True, blank=True)

    # 地区类别（例如 A区/B区）
    region_category = models.CharField(max_length=10, verbose_name='地区类别', null=True, blank=True)

    # 国家线分数
    national_line_score = models.FloatField(verbose_name='国家线分数', null=True, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '专业信息'
        db_table = 'ai_major_info'



class MajorRelation(BaseModel):
    # 本科一级学科
    undergraduate_first_discipline = models.CharField(max_length=255, verbose_name='本科一级学科', null=True,
                                                      blank=True)

    # 本科二级门类
    undergraduate_second_category = models.CharField(max_length=255, verbose_name='本科二级门类', null=True, blank=True)

    # 研究生一级学科
    graduate_first_discipline = models.CharField(max_length=255, verbose_name='研究生一级学科', null=True, blank=True)

    # 研究生二级门类代码
    graduate_second_category_code = models.CharField(max_length=10, verbose_name='研究生二级门类代码', null=True,
                                                     blank=True)

    # 研究生二级门类
    graduate_second_category = models.CharField(max_length=255, verbose_name='研究生二级门类', null=True, blank=True)

    # 研究生三级专业代码
    graduate_third_major_code = models.CharField(max_length=10, verbose_name='研究生三级专业代码', null=True,
                                                 blank=True)

    # 研究生三级专业
    graduate_third_major = models.CharField(max_length=255, verbose_name='研究生三级专业', null=True, blank=True)

    # 修改后
    relation_level = models.IntegerField(verbose_name='关联度等级', null=True, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '专业关联度'
        db_table = 'ai_major_relation'


class UndergraduateMajorCourse(BaseModel):
    # 一级学科
    first_discipline = models.CharField(max_length=100, verbose_name="一级学科",null=True, blank=True)

    # 二级门类
    second_category = models.CharField(max_length=100, verbose_name="二级门类编码",null=True, blank=True)

    # 主干课程
    core_courses = models.TextField(default='', verbose_name="主干课程",null=True, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = "本科专业核心课程"
        db_table = 'ai_undergraduate_major_course'


class CityGDP(BaseModel):
    # 城市名称
    city = models.CharField(max_length=100, verbose_name='城市', null=True, blank=True)

    # 城市GDP排名
    rank = models.IntegerField(verbose_name='GDP排名', null=True, blank=True)

    # 城市GDP得分
    score = models.FloatField(verbose_name='GDP得分', null=True, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '城市GDP信息'
        db_table = 'ai_city_gdp'


class UndergraduateCollegeInfo(BaseModel):
    """
    本科院校信息表
    """
    # 院校名称
    name = models.CharField(max_length=255, verbose_name='院校名称', null=True, blank=True)

    # 本科院校代码
    undergraduate_code = models.CharField(max_length=10, verbose_name='本科院校代码', null=True, blank=True)

    # 院校层次
    level_display = models.CharField(max_length=255, verbose_name='院校层次', null=True, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '本科院校信息'
        db_table = 'ai_undergraduate_college_info'
