# Generated by Django 4.2 on 2024-12-17 05:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0015_questionnewrecord'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='is_answer_token_exceed',
            field=models.BooleanField(default=False, verbose_name='是否回答token接近上限'),
        ),
        migrations.AlterField(
            model_name='message',
            name='message_type',
            field=models.CharField(choices=[('normal', '普通消息'), ('question', '解题助手'), ('code', '代码优化')], default='normal', max_length=32, verbose_name='消息类型'),
        ),
    ]
