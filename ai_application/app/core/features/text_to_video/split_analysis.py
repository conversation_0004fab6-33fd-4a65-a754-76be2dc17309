import json
from django.conf import settings
from langchain.chains.llm import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
def split_report(report_text):
    default_prompt = """
    # 角色
    你是一位专业且经验丰富的数据结构报告信息提取专家，能够从知识解析报告中准确提取出目标内容并且可以进行概括。

    ## 任务
    请从以下文本中提取出结构化信息，并按如下要求生成一个JSON对象：{knowledge_analysis}

    1. 提取“必要前置条件”部分的全部内容，作为 "prerequisites" 字段；
    2. 提取“分层讲解框架”中的“基础层”全部内容，作为 "basic_explanation" 字段；
    3. 提取“分层讲解框架”中的“应用层”全部内容，作为 "applied_explanation" 字段；
    4. 根据总结中的内容，对该知识点进行概括总结（不超过150字），作为 "summary" 字段；

    请保持每个字段内的内容为一段文字，避免列表嵌套。

    最终输出应为一个字典对象，包含以下四个键：
    - prerequisites
    - basic_explanation
    - applied_explanation
    - summary

    ## 限制
    - 仅围绕数据结构知识点进行信息提取；
    - 输出内容必须严格按照上述结构和格式进行组织。
    """
    prompt_obj = PromptTemplate(
        input_variables=["knowledge_analysis"],
        template=default_prompt
    )
    llm = ChatOpenAI(
        openai_api_key=settings.DOUBAO_API_KEY,
        openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
        model_name="doubao-1.5-pro-32k-250115",
    )
    chain = LLMChain(llm=llm, prompt=prompt_obj)
    result = chain.run(
            knowledge_analysis=report_text
        )
    return json.loads(result)