--
-- Add field antonym to englishwordlibrary
--
ALTER TABLE `ai_data_english_word_library` ADD COLUMN `antonym` json NULL;
--
-- Add field real_example_sentence to englishwordlibrary
--
ALTER TABLE `ai_data_english_word_library` ADD COLUMN `real_example_sentence` json NULL;
--
-- Add field synonym to englishwordlibrary
--
ALTER TABLE `ai_data_english_word_library` ADD COLUMN `synonym` json NULL;
--
-- Add field review_words to enwordrecitedayplan
--
ALTER TABLE `ai_english_word_recite_day_plan` ADD COLUMN `review_words` json NULL;
--
-- Add field is_manual to enwordreciteplanrecord
--
ALTER TABLE `ai_english_word_recite_plan_record` ADD COLUMN `is_manual` bool DEFAULT b'0' NOT NULL;
--
-- Add field example_sentence to enwordrecitequestion
--
AL<PERSON>R TABLE `ai_english_word_recite_question` ADD COLUMN `example_sentence` longtext NULL;
--
-- Create model EnWordReciteReviewRecord
--
CREATE TABLE `ai_english_word_recite_review_record` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `review_day` date NOT NULL, `user_answer` varchar(16) NOT NULL, `is_right` bool NOT NULL, `day_plan_id` bigint NULL, `plan_id` bigint NOT NULL, `plan_record_id` bigint NOT NULL, `question_id` bigint NOT NULL, `word_id` bigint NOT NULL);
--
-- Create model EnWordReciteReviewPlan
--
CREATE TABLE `ai_english_word_recite_review_plan` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `first_add_day` date NOT NULL, `review_num` integer NOT NULL, `is_review_continue` bool NOT NULL, `next_review_day` date NULL, `word_id` bigint NOT NULL);
CREATE INDEX `ai_english_word_recite_review_record_add_time_6036b1f5` ON `ai_english_word_recite_review_record` (`add_time`);
CREATE INDEX `ai_english_word_recite_review_record_day_plan_id_25c1952f` ON `ai_english_word_recite_review_record` (`day_plan_id`);
CREATE INDEX `ai_english_word_recite_review_record_plan_id_b3d47cb4` ON `ai_english_word_recite_review_record` (`plan_id`);
CREATE INDEX `ai_english_word_recite_review_record_plan_record_id_33fc6b98` ON `ai_english_word_recite_review_record` (`plan_record_id`);
CREATE INDEX `ai_english_word_recite_review_record_question_id_83409bb3` ON `ai_english_word_recite_review_record` (`question_id`);
CREATE INDEX `ai_english_word_recite_review_record_word_id_b132ff07` ON `ai_english_word_recite_review_record` (`word_id`);
CREATE INDEX `ai_english_word_recite_review_plan_add_time_976cdff5` ON `ai_english_word_recite_review_plan` (`add_time`);
CREATE INDEX `ai_english_word_recite_review_plan_word_id_859d9289` ON `ai_english_word_recite_review_plan` (`word_id`);