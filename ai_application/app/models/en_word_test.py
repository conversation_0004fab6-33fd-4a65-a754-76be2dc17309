from django.db import models

from ._base import BaseModel


class EnglishWordTestOrigin(BaseModel):
    year = models.IntegerField('年份', default=0)
    origin_content = models.TextField('出策略文章', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '英语单词测试真题来源'
        db_table = 'ai_data_english_word_test_origin'


class EnglishWordTestStrategy(BaseModel):
    test_origin = models.ForeignKey(EnglishWordTestOrigin, on_delete=models.CASCADE, db_constraint=False, null=True)
    strategy_content = models.TextField('策略', null=True)

    origin_content = models.TextField('出策略文章', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '英语单词测试策略'
        db_table = 'ai_data_english_word_test_strategy'


class EnglishWordTestQuestion(BaseModel):
    """
    0. 近义词题
    1. 形近词题
    2. 固定搭配题
    3. 多义词题
    4. 词性转换题
    5. 语法结构题
    6. 逻辑关系题
    7. 语境理解题
    """
    word = models.CharField('单词', max_length=100, blank=True, db_index=True)
    en_word = models.ForeignKey('EnglishWordLibrary', on_delete=models.CASCADE, db_constraint=False, null=True)
    question_type = models.CharField('问题类型', max_length=16, blank=True)
    question = models.TextField('题干', null=True)
    options = models.JSONField('选项', null=True)
    analysis = models.TextField('解析', null=True)
    answer = models.TextField('答案解析', null=True)
    # E简单题/M中等题/H难题
    level = models.CharField('难度', max_length=8, blank=True)

    strategy = models.ForeignKey(EnglishWordTestStrategy, on_delete=models.CASCADE, db_constraint=False, null=True)

    class Meta:
        verbose_name = verbose_name_plural = '英语单词测试题目'
        db_table = 'ai_data_english_word_test_question'


class EnglishWordTestRecord(BaseModel):
    user_id = models.CharField('用户id', max_length=100, blank=True, db_index=True)
    question_num = models.IntegerField('题目数量', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '英语单词测试出题记录'
        db_table = 'ai_data_english_word_test_record'


class EnglishWordTestRecordDetail(BaseModel):
    record = models.ForeignKey(EnglishWordTestRecord, on_delete=models.CASCADE, db_constraint=False)
    question = models.ForeignKey(EnglishWordTestQuestion, on_delete=models.CASCADE, db_constraint=False)

    class Meta:
        verbose_name = verbose_name_plural = '英语单词测试出题记录详情'
        db_table = 'ai_data_english_word_test_record_detail'


class EnglishWordTestAnswer(BaseModel):
    record = models.ForeignKey(EnglishWordTestRecord, on_delete=models.CASCADE, db_constraint=False)
    right_num = models.IntegerField('正确数量', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '英语单词测试结果'
        db_table = 'ai_data_english_word_test_answer'


class EnglishWordTestAnswerDetail(BaseModel):
    record = models.ForeignKey(EnglishWordTestRecord, on_delete=models.CASCADE, db_constraint=False)
    answer = models.ForeignKey(EnglishWordTestAnswer, on_delete=models.CASCADE, db_constraint=False)
    question = models.ForeignKey(EnglishWordTestQuestion, on_delete=models.CASCADE, db_constraint=False)
    user_answer = models.CharField('用户答案', max_length=16, blank=True)
    is_right = models.BooleanField('是否正确', default=False)
    is_answered = models.BooleanField('是否答题', default=False)
    is_exception = models.BooleanField(default=False, verbose_name='是否异常')
    exception_reason = models.TextField(null=True, verbose_name='异常原因')
    exception_image = models.TextField(null=True, verbose_name='异常图片')

    class Meta:
        verbose_name = verbose_name_plural = '英语单词测试结果详情'
        db_table = 'ai_data_english_word_test_answer_detail'


class EnglishWordWrongQuestion(BaseModel):
    user_id = models.CharField('用户id', max_length=100, blank=True, db_index=True)
    question = models.ForeignKey(EnglishWordTestQuestion, on_delete=models.CASCADE, db_constraint=False)
    answer_num = models.IntegerField('答题次数', default=0)
    is_right_again = models.BooleanField('是否重新答对', default=False)

    class Meta:
        verbose_name = verbose_name_plural = '英语单词错题集'
        db_table = 'ai_data_english_word_wrong_question'
