from app.core.features.supervise_init_status import Supervise_Init_Status_Feature
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow


class SuperviseInitStatusWorkflow(BaseWorkflow):

    def run(self):
        # 获取并处理数据
        data = self.application_generate_entity.inputs
        # 生成最终报告
        result = self._report_generator(data)
        self._publish_workflow_end_message()
        return result
    
    def _report_generator(self, data) -> LLMResult:
        feature = Supervise_Init_Status_Feature(app_no='supervise_init_undergraduated_st')
        query = str(data)

        result = feature.run(
            self.application_generate_entity,
            query=query,
            stream=False
        )
        tracing_info = {
            'tracing_type': 'supervise_init_undergraduated_st',
            'query': query
        }
        return self._handle_node_response(result, stream=False, tracing_info=tracing_info)
