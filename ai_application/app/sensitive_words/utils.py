import re
import jieba
from django.conf import settings
from app.models import SensitiveConfig
from app.sensitive_words.services import sensitive_word_service


def _is_english(sentence):
    # 正则表达式匹配英文字符、数字和一些特殊字符
    pattern = r'^[a-zA-Z0-9\s\.,!?\'"-]+$'
    return bool(re.match(pattern, sentence))


def _check_cn_contains_sensitive(input_text: str, sensitive_words: list, app_words: list, app_white_words: list):
    # 处理敏感词列表
    # 使用 jieba 进行分词
    sq_path = settings.BASE_DIR.joinpath('app/sensitive_words/stop_words/sq.txt')
    zj_path = settings.BASE_DIR.joinpath('app/sensitive_words/stop_words/zj.txt')
    zz_path = settings.BASE_DIR.joinpath('app/sensitive_words/stop_words/zz.txt')
    jieba.load_userdict(str(sq_path))
    jieba.load_userdict(str(zj_path))
    jieba.load_userdict(str(zz_path))
    words = jieba.lcut(input_text.strip(), cut_all=True)

    for word in app_words:
        if word in words:
            return True, settings.SENSITIVE_ANSWER, word

    for cat_words in sensitive_words:
        for word in cat_words['words']:
            if word in words and word not in app_white_words:
                return True, cat_words['tips'], word
    return False, '', ''


def _check_en_word_is_sensitive(en_word: str, sensitive_words: list, app_words: list, app_white_words: list):
    if en_word in app_words:
        return True, settings.SENSITIVE_ANSWER, en_word

    for cat_words in sensitive_words:
        if en_word in cat_words['words'] and en_word not in app_white_words:
            return True, cat_words['tips'], en_word
    return False, '', ''


def contains_sensitive_word(input_text: str, app_model_id: int = 0):
    """检查文本是否包含敏感词库中的任何词"""
    app_words = []
    app_white_words = []
    if app_model_id:
        sensitive_config: SensitiveConfig = SensitiveConfig.objects.filter(
            is_deleted=False, app_id=app_model_id).first()
        app_words = sensitive_config.words_list if sensitive_config else []
        app_white_words = sensitive_config.white_words_list if sensitive_config else []

    sensitive_words = sensitive_word_service.sensitive_words

    # 按照空格分割
    if _is_english(input_text):
        input_text = input_text.replace('\n', ' ')
        input_words = input_text.split(' ')
        for input_word in input_words:
            if not input_words:
                continue

            status, tips, sensitive_word = _check_en_word_is_sensitive(input_word, sensitive_words, app_words, app_white_words)
            if status:
                return status, tips, sensitive_word
        return False, '', ''
    else:
        return _check_cn_contains_sensitive(input_text, sensitive_words, app_words, app_white_words)
