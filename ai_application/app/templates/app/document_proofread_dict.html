<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校对词库管理</title>
    <!-- 添加图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 添加Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% load static %}
    <style>
        :root {
            /* 现代化色彩系统 */
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --success-color: #22c55e;
            --info-color: #3b82f6;
            
            /* 中性色彩 */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            
            /* 语义化颜色 */
            --bg-primary: #ffffff;
            --bg-secondary: var(--gray-50);
            --text-primary: var(--gray-900);
            --text-secondary: var(--gray-600);
            --border-color: var(--gray-200);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            
            /* 动画 */
            --transition-fast: 0.15s ease-in-out;
            --transition-normal: 0.3s ease-in-out;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--gray-50) 0%, #e0e7ff 100%);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgb(0 0 0 / 0.1);
        }

        .header .subtitle {
            font-size: 1.1rem;
            color: var(--text-secondary);
            font-weight: 400;
        }

        .stats-section {
            background: var(--bg-primary);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-card {
            text-align: center;
            padding: 1.5rem;
            background: var(--gray-50);
            border-radius: 0.75rem;
            border: 1px solid var(--border-color);
        }

        .stat-card .icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-card .label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .controls-section {
            background: var(--bg-primary);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
        }

        .controls-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .controls-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-color), #059669);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .search-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .search-input, .category-select {
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            font-size: 0.9rem;
            transition: all var(--transition-fast);
            background: var(--bg-primary);
        }

        .search-input {
            flex: 1;
            min-width: 200px;
        }

        .search-input:focus, .category-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .words-table {
            background: var(--bg-primary);
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .table-header {
            background: var(--gray-50);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 2fr 1fr 3fr 1fr 1.5fr 1fr;
            gap: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .word-item {
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: 2fr 1fr 3fr 1fr 1.5fr 1fr;
            gap: 1rem;
            align-items: center;
            transition: all var(--transition-fast);
        }

        .word-item:hover {
            background: var(--gray-50);
        }

        .word-item:last-child {
            border-bottom: none;
        }

        .word-text {
            font-weight: 600;
            color: var(--text-primary);
        }

        .category-tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            background: var(--primary-color);
            color: white;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .usage-count {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .date-text {
            color: var(--text-secondary);
            font-size: 0.85rem;
        }

        .actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-small {
            padding: 0.5rem;
            font-size: 0.8rem;
            border-radius: 0.375rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            background: var(--bg-primary);
            color: var(--text-primary);
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .pagination button:hover:not(:disabled) {
            background: var(--gray-50);
        }

        .pagination button.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            color: var(--gray-400);
            margin-bottom: 1rem;
            display: block;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .loading i {
            font-size: 2rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 添加词汇模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background-color: var(--bg-primary);
            margin: 10% auto;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 500px;
            box-shadow: var(--shadow-lg);
            animation: slideDown 0.3s ease-out;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .close {
            color: var(--text-secondary);
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: color var(--transition-fast);
        }

        .close:hover {
            color: var(--text-primary);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            font-size: 0.9rem;
            transition: all var(--transition-fast);
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-group textarea {
            min-height: 80px;
            resize: vertical;
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .search-controls {
                flex-direction: column;
            }

            .table-header, .word-item {
                grid-template-columns: 1fr;
                gap: 0.5rem;
                text-align: left;
            }

            .word-item {
                padding: 1rem;
            }

            .modal-content {
                margin: 5% auto;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-book"></i> 校对词库管理</h1>
            <p class="subtitle">
                <i class="fas fa-shield-alt"></i> 
                管理专有名词词库，确保这些词汇在校对过程中不被修改
            </p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="number" id="totalWords">{{ total_words }}</div>
                    <div class="label">总词汇数</div>
                </div>
                {% for category in categories %}
                <div class="stat-card">
                    <div class="icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <div class="number">{{ category.count }}</div>
                    <div class="label">{{ category.category }}</div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div class="controls-header">
                <h2>词库管理</h2>
                <button class="btn btn-primary" onclick="showAddWordModal()">
                    <i class="fas fa-plus"></i>
                    添加词汇
                </button>
            </div>

            <div class="search-controls">
                <input type="text" id="searchInput" class="search-input" placeholder="搜索词汇..." onchange="loadWords()">
                <select id="categoryFilter" class="category-select" onchange="loadWords()">
                    <option value="">所有分类</option>
                    {% for category in categories %}
                    <option value="{{ category.category }}">{{ category.category }}</option>
                    {% endfor %}
                </select>
                <button class="btn btn-secondary" onclick="loadWords()">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
            </div>
        </div>

        <!-- 词汇列表 -->
        <div class="words-table">
            <div class="table-header">
                <div>词汇</div>
                <div>分类</div>
                <div>描述</div>
                <div>使用次数</div>
                <div>添加时间</div>
                <div>操作</div>
            </div>
            <div id="wordsContainer">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>正在加载...</p>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="pagination" id="pagination"></div>
    </div>

    <!-- 添加词汇模态框 -->
    <div id="addWordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加词汇</h3>
                <span class="close" onclick="hideAddWordModal()">&times;</span>
            </div>
            <form id="addWordForm">
                <div class="form-group">
                    <label for="wordInput">词汇</label>
                    <input type="text" id="wordInput" required placeholder="请输入词汇">
                </div>
                <div class="form-group">
                    <label for="categoryInput">分类</label>
                    <select id="categoryInput">
                        <option value="专有名词">专有名词</option>
                        <option value="技术术语">技术术语</option>
                        <option value="品牌名">品牌名</option>
                        <option value="人名">人名</option>
                        <option value="地名">地名</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="descriptionInput">描述</label>
                    <textarea id="descriptionInput" placeholder="可选：添加词汇描述或使用说明"></textarea>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="hideAddWordModal()">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 20;

        // 获取CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
                <span>${message}</span>
            `;
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success-color)' : 
                           type === 'error' ? 'var(--danger-color)' : 'var(--info-color)'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 0.5rem;
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 500;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease-in-out;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 加载词汇列表
        function loadWords(page = 1) {
            currentPage = page;
            const search = document.getElementById('searchInput').value;
            const category = document.getElementById('categoryFilter').value;
            
            const params = new URLSearchParams({
                page: page,
                page_size: pageSize,
                search: search,
                category: category
            });

            fetch(`/console/app/proofreading_dictionary/?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        renderWords(data.data.words);
                        renderPagination(data.data.pagination);
                    } else {
                        showNotification('加载词汇列表失败: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showNotification('加载词汇列表失败: ' + error.message, 'error');
                });
        }

        // 渲染词汇列表
        function renderWords(words) {
            const container = document.getElementById('wordsContainer');
            
            if (words.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>暂无词汇</h3>
                        <p>没有找到符合条件的词汇</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = words.map(word => `
                <div class="word-item">
                    <div class="word-text">${word.word}</div>
                    <div><span class="category-tag">${word.category}</span></div>
                    <div>${word.description || '无描述'}</div>
                    <div class="usage-count">${word.usage_count}</div>
                    <div class="date-text">${word.add_time}</div>
                    <div class="actions">
                        <button class="btn btn-danger btn-small" onclick="deleteWord(${word.id}, '${word.word}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 渲染分页
        function renderPagination(pagination) {
            const container = document.getElementById('pagination');
            const { page, total_pages } = pagination;
            
            if (total_pages <= 1) {
                container.innerHTML = '';
                return;
            }

            let html = '';
            
            // 上一页
            html += `<button onclick="loadWords(${page - 1})" ${page <= 1 ? 'disabled' : ''}>上一页</button>`;
            
            // 页码
            for (let i = Math.max(1, page - 2); i <= Math.min(total_pages, page + 2); i++) {
                html += `<button onclick="loadWords(${i})" ${i === page ? 'class="active"' : ''}>${i}</button>`;
            }
            
            // 下一页
            html += `<button onclick="loadWords(${page + 1})" ${page >= total_pages ? 'disabled' : ''}>下一页</button>`;
            
            container.innerHTML = html;
        }

        // 显示添加词汇模态框
        function showAddWordModal() {
            document.getElementById('addWordModal').style.display = 'block';
            document.getElementById('wordInput').focus();
        }

        // 隐藏添加词汇模态框
        function hideAddWordModal() {
            document.getElementById('addWordModal').style.display = 'none';
            document.getElementById('addWordForm').reset();
        }

        // 添加词汇
        function addWord(event) {
            event.preventDefault();
            
            const word = document.getElementById('wordInput').value.trim();
            const category = document.getElementById('categoryInput').value;
            const description = document.getElementById('descriptionInput').value.trim();
            
            if (!word) {
                showNotification('请输入词汇', 'error');
                return;
            }

            fetch('/console/app/proofreading_dictionary/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    word: word,
                    category: category,
                    description: description
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification(data.message, 'success');
                    hideAddWordModal();
                    loadWords(currentPage);
                    // 更新统计
                    updateStats();
                } else {
                    showNotification('添加失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('添加失败: ' + error.message, 'error');
            });
        }

        // 删除词汇
        function deleteWord(wordId, wordText) {
            if (!confirm(`确定要删除词汇"${wordText}"吗？`)) {
                return;
            }

            fetch('/console/app/proofreading_dictionary/', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    word_id: wordId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification(data.message, 'success');
                    loadWords(currentPage);
                    // 更新统计
                    updateStats();
                } else {
                    showNotification('删除失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('删除失败: ' + error.message, 'error');
            });
        }

        // 更新统计信息
        function updateStats() {
            // 简单的重新加载页面统计
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载词汇列表
            loadWords();
            
            // 绑定表单提交事件
            document.getElementById('addWordForm').addEventListener('submit', addWord);
            
            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                const modal = document.getElementById('addWordModal');
                if (event.target === modal) {
                    hideAddWordModal();
                }
            });
            
            // 搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    loadWords();
                }
            });
        });
    </script>
</body>
</html>