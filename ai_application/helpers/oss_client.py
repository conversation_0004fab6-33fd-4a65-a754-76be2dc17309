import json
import logging
import os
import uuid

import oss2
from aliyunsdksts.request.v20150401 import AssumeRoleRequest
from aliyunsdkcore import client
from django.conf import settings

logger = logging.getLogger(__name__)


CONTENT_TYPE_2_EXT = {

}


class UploadResult:
    def __init__(self, path, response):
        self.path = path
        self.response = response


class OssClient:
    """
    oss 只负责将文件上传，并返回一个path（类似于ID）的信息给调用者，path无需具备良好的可读性
    文件上传client， 需要实现的功能：
        - 简单的文件上传
        - 大文件上传（断点续传）
        - 支持MD5校验
        - 重复文件名自动修改
    """

    def __init__(self, access_key_id, access_key_secret, endpoint, bucket, **kwargs):
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret

        self.bucket = oss2.Bucket(oss2.Auth(access_key_id, access_key_secret), endpoint, bucket)

        self.write_arn = kwargs.get('WRITE_ARN')

    def upload_object(self, obj, full_path, mode='default', headers=None):
        """

        :param filename: 文件名
        :param obj: 上传对象
        :param prj: 项目名称，如办公管理系统，则提供'oa'
        :param path: 指定路径
        :param full_path: 文件完整路径
        :param headers: 用户指定的HTTP头部。可以指定Content-Type、Content-MD5、x-oss-meta-开头的头部等
        :param content_type: 可选
        :param mode: acl权限
            mode参数
            r： public read
            rw/w： public read and write
            p： private
            default（默认）： 继承 bucket 权限
        :return:
        {
            'path': 短地址
            'status': 状态码, 200, 304, 403 ...
            'request_id': 业务流水
        }
        """
        assert mode in ('r', 'p', 'w', 'rw', 'default'), '请设置正确的ACL权限'
        acl_map = {
            'r': oss2.OBJECT_ACL_PUBLIC_READ,
            'p': oss2.OBJECT_ACL_PRIVATE,
            'w': oss2.OBJECT_ACL_PUBLIC_READ_WRITE,
            'rw': oss2.OBJECT_ACL_PUBLIC_READ_WRITE,
            'default': oss2.OBJECT_ACL_DEFAULT
        }
        acl_perm = acl_map[mode]
        try:
            self.bucket.put_object(full_path, obj, headers=headers)
        except oss2.exceptions.OssError as e:
            logger.error('上传文件{}失败: {}({})'.format(full_path, e.message, e.code))
            return None
        else:
            logger.debug('oss资源上传成功: {}'.format(full_path))
            if acl_perm != 'default':
                self.bucket.put_object_acl(full_path, acl_perm)
                logger.debug('oss资源权限设置成功')
            return full_path

    def delete_object(self, obj_path):
        try:
            self.bucket.delete_object(obj_path)
        except oss2.exceptions.OssError as e:
            logger.error('删除资源失败: {}/{}({})'.format(obj_path, e.message, e.code))

    def upload_image(self, filename, obj, prj, path, content_type=None):
        pass

    def get_temp_link(self, obj_path, expires_in=60):
        """
        生成资源的临时链接

        :param obj_path: str, 资源的短地址
        :param expires_in: int, 过去时间, 秒为单位, 默认60秒
        :return: str, 临时url
        """
        try:
            url = self.bucket.sign_url('GET', obj_path, expires_in)
        except oss2.exceptions.OssError as e:
            logger.error('生成临时链接操作异常：{}/{}({})'.format(obj_path, e.message, e.code))
            return None
        else:
            return url

    def get_temp_write_credential(self, session_name, duration=3600):
        """

        :param session_name: 用于区分请求，可使用app名称或者自定义信息
        :param duration: 可设置时间在 15 min - 60 min以内
        :return: 字典类型对象，包括AccessKeyID， AccessKeySecret, SecurityToken
        """
        assert self.write_arn
        clt = client.AcsClient(self.access_key_id, self.access_key_secret, 'cn-hangzhou')
        req = AssumeRoleRequest.AssumeRoleRequest()
        req.set_accept_format('json')
        req.set_RoleArn(self.write_arn)
        req.set_DurationSeconds(duration)
        req.set_RoleSessionName(session_name)
        try:
            body = clt.do_action_with_exception(req)
        except Exception as e:
            logger.exception(e)
        else:
            return json.loads(body.decode('utf-8')).get('Credentials')


def create_oss(config):
    return OssClient(
        config['ACCESS_KEY_ID'],
        config['ACCESS_KEY_SECRET'],
        config['ENDPOINT'],
        config['BUCKET'],
        **config['OPTIONALS']
    )


def get_oss_client():
    conf = {
        'ACCESS_KEY_ID': settings.OSS_ACCESS_KEY_ID,
        'ACCESS_KEY_SECRET': settings.OSS_ACCESS_KEY_SECRET,
        'ENDPOINT': 'oss-cn-shanghai.aliyuncs.com',
        'BUCKET': settings.OSS_ACCESS_KEY_BUCKET,
        "OPTIONALS": {  # 额外配置参数
            "WRITE_ARN": settings.OSS_ACCESS_ROLE
        }
    }
    return create_oss(config=conf)
