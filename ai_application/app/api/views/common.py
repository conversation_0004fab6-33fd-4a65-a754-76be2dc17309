import json
import logging
from collections import defaultdict

from django.conf import settings

from app.api.dto import (
    ChatMessageDto, ConversationAdd, PromptOptimizeDto, ContentExtractorDto, KnowledgeSearchDto,
    CourseNoteTaskCreateDto,
    MathProblemSolvingDto, ProblemSolvingOnlyDto, CodeOptimizationOnlyDto, DatasetAddKnowledgeDocumentDto
)
from app.api.serializers import AppListSerializer, AppInfoSerializer
from app.api.validators import (
    AppAddValidator, ChatMessageValidator, ConversationAddValidator, DatasetSetDocumentsValidator,
    DatasetCreateValidator, PromptOptimizeValidator, KnowledgeAddValidator, KnowledgeEditValidator,
    KnowledgeSearchValidator, ContentExtractorValidator,
    DatasetAddSubtitleDocumentValidator, CourseNoteTaskCreateValidator, MathProblemSolvingValidator,
    ProblemSolvingOnlyValidator, CodeOptimizationOnlyValidator, DatasetAddKnowledgeDocumentValidator
)
from app.core.app_parameter_manager import AppParameterManager
from app.core.rag.index_processor.constant.index_type import IndexType
from app.errors import ParameterError, DatasetNotFoundError, ConversationNotFoundError
from app.libs.baidu_ocr import BaiduOcr
from app.sensitive_words.utils import contains_sensitive_word
from app.services.app_generate_service import AppGenerateService
from app.services.app_service import AppService
from app.services.baowen_generator_service import ContentGeneratorService, RedContentGeneratorService
from app.models import (
    App, Dataset, PromptTemplate, MessageTask, Knowledge, DatasetDocument, CourseNoteTask,
    CourseVideoKeyword, Account, Conversation
)
from app.services.converation_service import ConversationService
from app.services.course_note.course_note_service import CourseNoteService
from app.services.course_note.utils import get_chapter_names, get_chapter_video_content_ids
from app.services.dataset_service import DocumentService
from app.services.document_summary_service import DocumentSummaryService
from app.services.knowledge_service import KnowledgeService
from app.services.math_problem_solving_service import MathProblemSolvingService
from app.services.message_service import MessageService
from app.services.prompt_optimize_service import PromptOptimizeService
from app.services.video_script_service import VideoScriptService
from django_ext import base_view
from django_ext.response import make_response, make_stream_response


logger = logging.getLogger(__name__)


class AppSupportParamsView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        app_parameter_manager = AppParameterManager()
        return make_response({
            'model_params': app_parameter_manager.get_model_params_dict(),
            'prompt_params': app_parameter_manager.get_prompt_params_dict(),
            'other_params': app_parameter_manager.get_other_params_dict(),
        })


class AppPromptTemplateListView(base_view.BaseView):
    def get(self, request, *args, **kwargs):
        templates = PromptTemplate.objects.filter(
            is_deleted=False,
            is_enabled=True,
            app_no__in=[
                'article_generation',
                'article_generation_title',
                'article_generation_body',
                'article_generation_tag',
            ]
        )
        data = [{
            'id': str(i.id),
            'name': i.name,
        } for i in templates]
        return make_response({'data': data})


class AppAddListView(base_view.BaseListView):
    validator_class = AppAddValidator
    queryset = App.objects.filter(is_deleted=False).order_by('-id')
    serializer_class = AppListSerializer

    def get_queryset(self):
        return self.queryset.filter(account=self.request.user)

    def post(self, request, *args, **kwargs):
        dto = self.validate_request(request.data)
        app = AppService.add_app(request.user, dto)
        return make_response({'app_id': app.app_no})


class SensitiveWordReviewView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        app_id = request.data.get('app_id')
        content = request.data.get('content')
        if not (app_id and content):
            raise ParameterError(detail_err='参数错误')

        app: App = App.objects.filter(app_no=app_id).first()
        app_model_id = app.id if app else 0
        is_contain, tips, _ = contains_sensitive_word(content, app_model_id)

        return make_response({
            'contain_sensitive_word': is_contain,
            'sensitive_tips': tips,
        })


class AppManageView(base_view.BaseSingleView):
    validator_class = AppAddValidator
    queryset = App.objects.filter(is_deleted=False).order_by('-id')
    serializer_class = AppInfoSerializer

    def get_object(self) -> App:
        return AppService.get_app_by_account(self.kwargs['app_id'], self.request.user)

    def perform_update(self, instance, dto):
        AppService.edit_app(instance, dto)

    def perform_destroy(self, app: App):
        app.soft_delete()


class ConversationAddView(base_view.BaseView):
    validator_class = ConversationAddValidator

    def post(self, request, *args, **kwargs):
        dto: ConversationAdd = self.validate_request(request.data)

        conversation = ConversationService.add_conversation(dto, request.user)
        return make_response({'conversation_id': conversation.conversation_no})


class ConversationTokenExceedView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        conversation_id = request.query_params.get('conversation_id')
        if not conversation_id:
            raise ParameterError(detail_err='会话不能为空')

        token_exceed = ConversationService.check_token_exceed(conversation_id)
        return make_response({
            'token_exceed': token_exceed,
        })


class PromptOptimizeView(base_view.BaseView):
    validator_class = PromptOptimizeValidator

    def post(self, request, *args, **kwargs):
        dto: PromptOptimizeDto = self.validate_request(request.data)
        result = PromptOptimizeService.get_prompt_dict(dto, request.user)
        return make_response({'prompt_result': result})


class PrologueGenerateView(base_view.BaseView):
    validator_class = PromptOptimizeValidator

    def post(self, request, *args, **kwargs):
        dto: PromptOptimizeDto = self.validate_request(request.data)
        prologue = PromptOptimizeService.get_prologue(dto, request.user)
        return make_response({
            'prologue': prologue,
        })


class DocumentExtractionView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        query = request.data.get('query')
        is_async = request.data.get('is_async', False)
        if not query:
            raise ParameterError(detail_err='参数错误')

        result = DocumentSummaryService.get_extraction(query, request.user, is_async=is_async)
        if is_async:
            return make_response({'task_id': result.get('task_id')})
        else:
            return make_response({'content': result.get('answer')})


class DocumentSummaryView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        query = request.data.get('query')
        style = request.data.get('style')
        is_async = request.data.get('is_async', False)
        if not (query and style):
            raise ParameterError(detail_err='参数错误')

        result = DocumentSummaryService.get_summary(query, style, request.user, is_async=is_async)
        if is_async:
            return make_response({'task_id': result.get('task_id')})
        else:
            return make_response({'content': result.get('answer')})


class VideoScriptView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        query_params = request.data.get('query_params', {})
        if 'subject' not in query_params:
            raise ParameterError(detail_err='参数错误')

        data = VideoScriptService.get_content(query_params, request.user)
        return make_response(data)


class ContentExtractorView(base_view.BaseView):
    validator_class = ContentExtractorValidator

    def post(self, request, *args, **kwargs):
        dto: ContentExtractorDto = self.validate_request(request.data)
        result = ContentGeneratorService.extract_content(dto.text,request.user)
        return make_response({'extracted_text': result['extracted_text']})


class RedGeneratorView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        # result = ContentGeneratorService.generate_content(request.user)
        result = RedContentGeneratorService.generate_content(request.user)
        return make_response({
            'covers': result.get('covers', []),
            'title': result.get('title', ''),  # 使用 title_options 更加语义化
            'body': result.get('body', ''),
            'tags': result.get('tags', [])
        })


class CodeOptimizationView(base_view.BaseView):
    validator_class = CodeOptimizationOnlyValidator

    def post(self, request, *args, **kwargs):
        dto: CodeOptimizationOnlyDto = self.validate_request(request.data)

        # TODO 临时处理，通过dto.conversation_id查询appid
        conversation = Conversation.objects.filter(
            is_deleted=False, conversation_no=dto.conversation_id).first()
        if not conversation:
            raise ConversationNotFoundError()

        chat_dto = ChatMessageDto(
            app_id=conversation.app.app_no,
            biz_id=dto.biz_id,
            userinfo=dto.userinfo,
            conversation_id=dto.conversation_id,
            inputs={
                'code_prompts': dto.code_prompts,
            },
            query=json.dumps({
                'lang_code': dto.lang_code,
                'content': dto.code_content,
            }, ensure_ascii=False),
            message_type='code',
            stream=True
        )
        response = AppGenerateService.generate(chat_dto, request.user)
        return make_stream_response(response)


class ProblemSolvingView(base_view.BaseView):
    validator_class = ProblemSolvingOnlyValidator

    def post(self, request, *args, **kwargs):
        dto: ProblemSolvingOnlyDto = self.validate_request(request.data)

        # TODO 临时处理，通过dto.conversation_id查询appid
        conversation = Conversation.objects.filter(
            is_deleted=False, conversation_no=dto.conversation_id).first()
        if not conversation:
            raise ConversationNotFoundError()

        chat_dto = ChatMessageDto(
            app_id=conversation.app.app_no,
            biz_id=dto.biz_id,
            userinfo=dto.userinfo,
            conversation_id=dto.conversation_id,
            inputs={
                'question_prompts': dto.question_prompts,
            },
            query=dto.user_question,
            message_type='question',
            file_objs=dto.file_objs,
            stream=True
        )
        response = AppGenerateService.generate(chat_dto, request.user)
        return make_stream_response(response)


class MathProblemSolvingView(base_view.BaseView):
    validator_class = MathProblemSolvingValidator

    def post(self, request, *args, **kwargs):
        dto: MathProblemSolvingDto = self.validate_request(request.data)

        # TODO 临时处理，通过dto.conversation_id查询appid
        app_no = 'math_problem_solving'
        if dto.conversation_id:
            conversation = Conversation.objects.filter(
                is_deleted=False, conversation_no=dto.conversation_id).first()
            if not conversation:
                raise ConversationNotFoundError()
            app_no = conversation.app.app_no

        chat_dto = ChatMessageDto(
            app_id=app_no,
            biz_id=dto.biz_id,
            userinfo=dto.userinfo,
            conversation_id=dto.conversation_id,
            file_objs=dto.image_url,
            query=dto.user_question,
            message_type='math_question',
            stream=True
        )
        response = AppGenerateService.generate(chat_dto, request.user)
        return make_stream_response(response)


class ChatMessageView(base_view.BaseView):
    validator_class = ChatMessageValidator

    def post(self, request, *args, **kwargs):
        dto: ChatMessageDto = self.validate_request(request.data)

        response = AppGenerateService.generate(dto, request.user)
        if isinstance(response, dict):
            return make_response(response)
        else:
            return make_stream_response(response)


class ChatMessageStopView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        AppGenerateService.stop_message(kwargs['message_id'], request.user)
        return make_response()


class ChatMessageRetryView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        response = AppGenerateService.retry_message(kwargs['message_id'], request.user)
        return make_stream_response(response)


class MessageRefSourceView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        source_list = MessageService.get_message_ref_source(kwargs['message_id'])
        return make_response({'source_list': source_list})


class MessageTaskStatusView(base_view.BaseView):
    def get_task_status(self, task: MessageTask) -> dict:
        if task.process_status == MessageTask.ProcessStatus.success:
            return {
                'status': MessageTask.ProcessStatus.success,
                'content': task.message.answer,
            }
        if task.process_status == MessageTask.ProcessStatus.fail and task.retry_times >= 3:
            return {
                'status': MessageTask.ProcessStatus.fail,
                'content': '',
            }
        return {
            'status': MessageTask.ProcessStatus.ing,
            'content': '',
        }

    def post(self, request, *args, **kwargs):
        data = []

        task_ids = request.data.get('task_ids')
        if task_ids:
            tasks = MessageTask.objects.filter(task_id__in=task_ids)
            for task in tasks:
                data.append({
                    'task_id': task.task_id,
                    'task_status': self.get_task_status(task)
                })

        return make_response({
            'tasks_status': data
        })


class DatasetAddView(base_view.BaseView):
    validator_class = DatasetCreateValidator

    def post(self, request, *args, **kwargs):
        dto = self.validate_request(request.data)
        name = request.data.get('name')
        if not name:
            raise ParameterError(detail_err='名称不能为空')
        dataset, documents = DocumentService.create_dataset(dto, request.user)
        return make_response({
            'dataset_no': dataset.dataset_no,
            'documents': documents,
        })


class DatasetMixin:
    def get_dataset(self, dataset_no) -> Dataset:
        ins = Dataset.objects.filter(
            is_deleted=False, dataset_no=dataset_no).first()
        if not ins:
            raise DatasetNotFoundError()
        return ins


class DatasetAddDocumentsView(DatasetMixin, base_view.BaseView):
    validator_class = DatasetSetDocumentsValidator

    def post(self, request, *args, **kwargs):
        dataset = self.get_dataset(kwargs['dataset_no'])
        dto = self.validate_request(request.data)
        documents = DocumentService.add_documents(dataset, dto.documents)
        return make_response({
            'documents': documents,
        })


class DatasetDelDocumentsView(DatasetMixin, base_view.BaseView):
    def post(self, request, *args, **kwargs):
        dataset = self.get_dataset(kwargs['dataset_no'])
        document_nos = request.data.get('document_nos', [])
        if not document_nos:
            return make_response()

        # 处理知识点相关文档
        del_document_nos = list(DatasetDocument.objects.filter(
            is_deleted=False,
            index_type=IndexType.KNOWLEDGE_INDEX.value,
            original_knowledge_document_no__in=document_nos
        ).values_list('document_no', flat=True))
        document_nos.extend(del_document_nos)

        DocumentService.delete_document(dataset, document_nos)
        return make_response()


class DatasetSetDocumentsView(base_view.BaseView):
    validator_class = DatasetSetDocumentsValidator

    def get_object(self):
        ins = Dataset.objects.filter(
            is_deleted=False, dataset_no=self.kwargs['dataset_no']).first()
        if not ins:
            raise DatasetNotFoundError()
        return ins

    def post(self, request, *args, **kwargs):
        dataset = self.get_object()
        dto = self.validate_request(request.data)
        DocumentService.save_document_with_dataset(dataset, dto, request.user)
        return make_response()


class DatasetDocumentsView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        dataset_nos = request.data.get('dataset_nos')
        dataset_nos = dataset_nos if dataset_nos else []
        data = DocumentService.get_datasets_documents(dataset_nos)
        return make_response(data)


class DatasetDeleteView(base_view.BaseDestroyView):
    queryset = Dataset.objects.filter(is_deleted=False).order_by('-id')

    def get_object(self) -> Dataset:
        ins = self.queryset.filter(dataset_no=self.kwargs['dataset_no']).first()
        if not ins:
            raise DatasetNotFoundError()
        return ins

    def perform_destroy(self, dataset: Dataset):
        DocumentService.delete_dataset(dataset)


class DatasetAddKnowledgeDocumentView(base_view.BaseView):
    validator_class = DatasetAddKnowledgeDocumentValidator

    def post(self, request, *args, **kwargs):
        dto: DatasetAddKnowledgeDocumentDto = self.validate_request(request.data)
        dataset = Dataset.objects.filter(
            is_deleted=False, dataset_no=dto.dataset_no).first()
        if not dataset:
            raise DatasetNotFoundError()

        document_no = DocumentService.add_knowledge_document(dataset, dto)
        return make_response({
            'document_no': document_no
        })


class DatasetAddSubtitleDocumentView(base_view.BaseView):
    validator_class = DatasetAddSubtitleDocumentValidator

    def post(self, request, *args, **kwargs):
        dataset = Dataset.objects.filter(
            is_deleted=False, dataset_no=settings.SUBTITLE_DATASET).first()
        if not dataset:
            raise DatasetNotFoundError()

        dto = self.validate_request(request.data)

        # 线上临时处理
        # document_no = DocumentService.add_subtitle_document(dataset, dto)
        document: DatasetDocument = DatasetDocument.objects.filter(
            is_deleted=False,
            dataset__dataset_no=settings.SUBTITLE_DATASET,
            name=dto.name,
        ).first()

        return make_response({
            'document_no': document.document_no if document else ''
        })


class \
        DocumentKnowledgeGenView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        document_no = request.data.get('document_no')
        if not document_no:
            raise ParameterError()

        data = KnowledgeService.generate_document_knowledge(document_no)
        return make_response({'data': data})


class DocumentKnowledgeAddView(base_view.BaseListView):
    validator_class = KnowledgeAddValidator

    def post(self, request, *args, **kwargs):
        dto = self.validate_request(request.data)
        knowledge = KnowledgeService.add_knowledge(dto, request.user)
        return make_response({
            'knowledge_id': str(knowledge.id),
        })


class DocumentKnowledgeEditView(base_view.BaseUpdateView):
    validator_class = KnowledgeEditValidator
    queryset = Knowledge.objects.filter(is_deleted=False)

    def perform_update(self, instance, dto):
        KnowledgeService.edit_knowledge(instance, dto)


class DocumentKnowledgeDeleteView(base_view.BaseDestroyView):
    queryset = Knowledge.objects.filter(is_deleted=False)

    def perform_destroy(self, instance: Knowledge):
        KnowledgeService.delete_knowledge(instance)


class DocumentKnowledgeSearchView(base_view.BaseView):
    validator_class = KnowledgeSearchValidator

    def post(self, request, *args, **kwargs):
        dto: KnowledgeSearchDto = self.validate_request(request.data)
        dto.search_mode = 'general'
        response = KnowledgeService.query_knowledge(
            dto=dto,
            account=request.user,
        )
        return make_stream_response(response)


class DocumentKnowledgeRegenerateView(base_view.BaseView):
    validator_class = KnowledgeSearchValidator

    def post(self, request, *args, **kwargs):
        dto: KnowledgeSearchDto = self.validate_request(request.data)
        dto.search_mode = 'general'
        response = KnowledgeService.regenerate_knowledge(
            dto=dto,
            account=request.user,
        )
        return make_stream_response(response)


class DocumentKnowledgeDeepSearchView(base_view.BaseView):
    validator_class = KnowledgeSearchValidator

    def post(self, request, *args, **kwargs):
        dto: KnowledgeSearchDto = self.validate_request(request.data)
        dto.search_mode = 'deep'
        response = KnowledgeService.query_knowledge(
            dto=dto,
            account=request.user,
        )
        return make_stream_response(response)


class DocumentKnowledgeSearchRetryView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        message_id = request.data.get('message_id')
        if not message_id:
            raise ParameterError()

        enable_recommend_video = request.data.get('enable_recommend_video',False)
        response = KnowledgeService.retry_query_knowledge(
            message_id=message_id,
            account=request.user,
            enable_recommend_video=enable_recommend_video,
        )
        return make_stream_response(response)


def baidu_ocr_get_text(image_url: str) -> str:
    res = BaiduOcr().basic_accurate(image_file_url=image_url)
    logger.info(f'get_baidu_result:{res}')
    words = [i['words'] for i in res]
    return ' '.join(words) or ''


class OcrResultView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        image_url = self.request.data.get('image_url')
        assert image_url, 'image url is required'

        # 英文句子使用空格拼接
        content = baidu_ocr_get_text(image_url) or '识别内容为空，请重新上传图片'
        return make_response({'text': content})


class ComplexSentenceAnalysisView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        complex_sentence_analysis = request.data['complex_sentence_analysis']
        user_question_image = request.data.get('image_url', '')
        user_question = request.data.get('text', '')
        biz_id = request.data.get('biz_id', '')
        conversation_id = request.data.get('conversation_id')
        assert user_question_image or user_question, 'image_url or text is required'

        # 优先使用图片
        if user_question_image:
            user_question = baidu_ocr_get_text(user_question_image) or user_question

        assert user_question, 'text is required'

        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            'complex_sentence_analysis': complex_sentence_analysis,
        }

        chat_dto = ChatMessageDto(
            app_id=request.data.get('app_id') or 'complex_sentence_analysis',
            query=user_question,
            conversation_id=conversation_id,
            stream=True,
            biz_id=biz_id,
            inputs=inputs,
        )
        # stream response
        response = AppGenerateService.generate(chat_dto, self.request.user, 'api')
        return make_stream_response(response)


class CourseNoteCreateTaskView(base_view.BaseView):
    validator_class = CourseNoteTaskCreateValidator

    def post(self, request, *args, **kwargs):
        dto: CourseNoteTaskCreateDto = self.validate_request(request.data)
        task = CourseNoteService.create_note_task(dto)
        return make_response({'task_id': str(task.id)})


class CourseNoteTaskStatusView(base_view.BaseView):
    def get(self, request, *args, **kwargs):
        task_id = request.query_params.get('task_id')
        if not task_id:
            raise ParameterError()
        task = CourseNoteTask.objects.filter(id=task_id).first()
        if not task:
            raise ParameterError()

        data = CourseNoteService.get_task_status(task)
        return make_response(data)


class CourseNoteVideoKeywordsView(base_view.BaseView):
    def first_chapter_keywords(self) -> list:
        return ["逻辑结构", "数组", "迪杰斯特拉算法", "二分递归", "操作系统", "空间复杂度", "计算机组成原理",
                "稀疏矩阵", "拓扑排序", "树形选择排序", "时间复杂度", "B+树", "链队", "循环链表", "顺序表",
                "语句频度", "特殊矩阵", "斐波那契数列", "广义表", "循环队列", "邻接多重表", "折半插入排序",
                "哈夫曼编码", "快速排序", "分治法", "堆排序", "计算机⽹络", "普里姆算法", "后缀表达式", "线性递归",
                "邻接矩阵", "队列", "数据结构", "KMP", "直接插入排序", "哈夫曼树", "最小生成树", "双向链表", "线索二叉树",
                "基本语句", "外部排序", "问题规模", "有序表", "平均时间复杂度", "链栈", "置换-选择排序", "归并排序", "递归",
                "广度优先搜索", "B-树", "简单选择排序", "对数阶", "邻接表", "基数排序", "关键路径", "克鲁斯卡尔算法",
                "线性表", "单链表", "408考研⼤纲", "红黑树", "十字链表", "抽象数据类型", "冒泡排序", "多路平衡归并",
                "最佳归并树", "平衡二叉树", "多项式时间复杂度", "基本操作", "希尔排序", "折半查找", "二叉排序树", "表达式树",
                "二叉树", "顺序存储结构"]

    def get_first_chapter_document_nos(self) -> list:
        chapter_names = get_chapter_names()
        video_content_ids = get_chapter_video_content_ids(chapter_names[0])
        docs = DatasetDocument.objects.filter(
            is_deleted=False, data_source_info__video_content_id__in=video_content_ids)
        return [d.document_no for d in docs]

    def post(self, request, *args, **kwargs):
        document_nos = request.data.get('document_nos')
        if not document_nos:
            return make_response([])

        kws = CourseVideoKeyword.objects.filter(
            dataset_document__document_no__in=document_nos, is_deleted=False
        ).select_related('dataset_document')
        document_no_map = defaultdict(list)
        for i in kws:
            document_no_map[i.dataset_document.document_no].append(i.keyword)

        first_chapter_document_nos = self.get_first_chapter_document_nos()

        data = []
        for document_no, keywords in document_no_map.items():
            if document_no in first_chapter_document_nos:
                keywords = self.first_chapter_keywords()
            data.append({
                'document_no': document_no,
                'keywords': keywords,
            })

        return make_response(data)
