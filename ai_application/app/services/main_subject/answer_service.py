import requests
from django.conf import settings

from api_client.account.client import account_client
from api_client.account.dto import UserDetailDto
from app.services.main_subject.test_paper_service import TestPaperService


class AnswerService:

    @classmethod
    def get_test_answer_detail(cls, answer_id):
        if settings.ENVIRONMENT == settings.ENV_PRODUCT:
            return TestPaperService.get_answer_detail(answer_id)
        else:
            return cls._get_test_answer_detail_by_api(answer_id)

    @classmethod
    def get_user_detail(cls, user_id):
        if settings.ENVIRONMENT == settings.ENV_PRODUCT:
            info: UserDetailDto = account_client.get_user_detail(user_id)
            return {
                'uuid': info.uuid,
                'nickname': info.nickname,
                'realname': info.realname,
                'sex': info.sex,
            } if info else {}
        else:
            return cls._get_user_detail_by_api(user_id)

    @classmethod
    def _get_test_answer_detail_by_api(cls, answer_id):
        url = 'https://ai.yantucs.com/api/v1/skip/paper_answer_detail'
        response = requests.get(url, params={'answer_id': answer_id})

        try:
            response.raise_for_status()
        except:
            raise ValueError('获取试卷失败')

        res = response.json()
        # 检查响应的 code 字段
        if res.get('code') != 0:
            raise ValueError('获取试卷失败')
        return res.get('data')

    @classmethod
    def _get_user_detail_by_api(cls, user_id):
        url = 'https://ai.yantucs.com/api/v1/skip/user_detail'
        response = requests.get(url, params={'user_id': user_id})

        try:
            response.raise_for_status()
        except:
            raise ValueError('获取试卷失败')

        res = response.json()
        # 检查响应的 code 字段
        if res.get('code') != 0:
            raise ValueError('获取用户信息失败')
        return res.get('data')
