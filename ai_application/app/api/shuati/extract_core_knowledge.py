import csv
import json
import ast
from typing import List, Dict, Any
from langchain.chains import <PERSON><PERSON>hain, Sequential<PERSON>hain
from langchain_core.prompts import PromptTemplate
from django.conf import settings
from langchain_openai import ChatOpenAI
from ai_application.settings import DAYI_TEXT_MODEL
from app.models import STQuestion, STKnowledge, STQuestionKnowledge
from app.services.shuati_app.utils import parse_objective_question


class ExtractCoreKnowledgeService:
    # 408知识点列表和考点分类列表
    KNOWLEDGE_LIST = [
        "时间复杂度", "空间复杂度", "线性表", "顺序表", "单链表", "循环链表", "双向链表",
        "栈", "顺序栈", "链栈", "递归", "队列", "顺序队", "链队", "串", "模式匹配",
        "BF 算法", "KMP 算法", "next 表", "数组", "特殊矩阵压缩", "树的概念", "二叉树的性质",
        "二叉树的存储结构", "二叉树的遍历", "线索二叉树", "树的存储结构", "树与二叉树的转换",
        "森林与二叉树的转换", "树的遍历", "哈夫曼树", "哈夫曼编码", "图的基本概念",
        "邻接矩阵", "邻接表", "十字链表", "邻接多重表", "深度优先搜索", "广度优先搜索",
        "最小生成树", "最短路径", "拓扑排序", "关键路径", "顺序查找", "折半查找", "分块查找",
        "二叉搜索树", "平衡二叉树", "红黑树", "B 树", "B + 树", "散列表", "直接插入排序",
        "折半插入排序", "起泡排序", "简单选择排序", "希尔排序", "快速排序", "树形选择排序",
        "堆排序", "归并排序", "基数排序", "多路平衡归并", "置换选择排序", "最佳归并树"
    ]
    @classmethod
    def get_knowledge_list_by_course_code(cls, core_course_code: str) -> list:
        """
        从数据库中获取指定核心课程的所有知识点名称列表

        Args:
            core_course_code (str): 核心课程代码

        Returns:
            list: 知识点名称列表
        """
        knowledges = STKnowledge.objects.filter(core_course_code=core_course_code).values_list('name', flat=True)
        return list(knowledges)

    @classmethod
    def check_in_knowledge_list(cls, knowledge: str, knowledge_list: List[str]) -> bool:
        return knowledge in knowledge_list

    @classmethod
    def create_extract_knowledge_chain(cls, llm):
        """第一阶段：提取核心知识点"""
        prompt_template = """
        # 角色
        你是一位资深的408计算机授课老师，拥有极为丰富的教学经验，对408计算机考试的各类知识点和题目类型了如指掌。你能够用通俗易懂且清晰准确的语言进行讲解。

        ## 技能
        ### 技能 1: 题目解析
        - 阅读并理解题目内容，提取关键数据和问题。

        ### 技能 2：考点分析
        - 分析题目考察的具体概念

        ### 技能 3：知识点标注
        - 必须从规定的{knowledge_list}中提取知识点

        # 题目内容: {question}
        # 题目解析: {analysis}

        # 返回格式限制：
        - 标准JSON列表，例如：["队列", "栈"]
        - 输出必须从规定的{knowledge_list}中提取知识点
        """
        prompt = PromptTemplate(
            input_variables=["question", "analysis", "knowledge_list"],
            template=prompt_template
        )
        return LLMChain(
            llm=llm,
            prompt=prompt,
            output_key="knowledge_response"
        )

    @classmethod
    def create_optimized_analysis_chain(cls, llm):
        """第三阶段：生成优化版解析的Chain"""
        prompt_template = """
        # 角色
        你是一位资深的408计算机授课老师，拥有极为丰富的教学经验，对408计算机考试的各类知识点和题目类型了如指掌。你能够用通俗易懂且清晰准确的语言进行讲解。

        ## 技能
        认真阅读题目，并结合题目的知识点，给出详细的解答，要求：
        1. 解答深入浅出，在必要的知识难点要进行通俗的解释。
        2. 结合题目所考查的知识内容，详细讲解题目的求解过程，特别注重将基础知识和题目的结合。
        2. 如果题目有选项，判断是否有必要对各个选型进行逐一解释，如果有必要则详细分析每个选项。
        3. 对于非选择题（没有选项的题目），要给出完整的解答过程。
        4. 在题目的最后，洞察题目本身的特点，给出由本题所获得的知识和能力。

        题目内容: {question}
        原始解析: {analysis}
        核心知识点: {valid_knowledge_list}

        """
        prompt = PromptTemplate(
            input_variables=["question", "analysis", "valid_knowledge_list"],
            template=prompt_template
        )
        return LLMChain(
            llm=llm,
            prompt=prompt,
            output_key="optimized_analysis"
        )

    @classmethod
    def extract_core_knowledge(cls, question_instance: STQuestion) -> dict:
        try:
            llm = ChatOpenAI(
                openai_api_key=settings.DOUBAO_API_KEY,
                openai_api_base=settings.DOUBAO_API_BASE,
                model_name='doubao-1.5-thinking-pro-250415',
                temperature=0.3,
            )
            llm2 = ChatOpenAI(
                openai_api_key=settings.DOUBAO_API_KEY,
                openai_api_base=settings.DOUBAO_API_BASE,
                model_name='doubao-1-5-pro-256k-250115',
                temperature=0.3,
            )

            # 解析题目内容，添加异常处理
            try:
                question_info = parse_objective_question(question_instance.question_content)
            except Exception as parse_error:
                print(f"解析题目内容时出错 (ID: {question_instance.id}): {str(parse_error)}")
                question_info = {
                    'title': question_instance.question_content or '',
                    'right_answer': '',
                    'choices': []
                }

            # 确保必要的字段存在
            question = question_info.get('title', '')
            answer = question_info.get('right_answer', '')
            choices = question_info.get('choices', [])

            # 处理选项格式，确保都是字符串
            if isinstance(choices, list):
                processed_choices = []
                for choice in choices:
                    if isinstance(choice, dict):
                        # 如果选项是字典格式，尝试提取文本内容
                        choice_text = choice.get('content', str(choice))
                        processed_choices.append(choice_text)
                    elif isinstance(choice, str):
                        processed_choices.append(choice)
                    else:
                        processed_choices.append(str(choice))
                choices = processed_choices
            else:
                choices = []

            analysis = ''
            if choices:
                # 确保选项是字符串列表后再进行join操作
                try:
                    question += "\n选项:\n" + "\n".join(str(c) for c in choices)
                except Exception as join_error:
                    print(f"处理选项时出错 (ID: {question_instance.id}): {str(join_error)}")
                    question += "\n选项:\n" + str(choices)

            if answer:
                analysis = f"答案:\n{answer}"

            # 根据题目所属的核心课程获取知识点列表
            knowledge_list = cls.get_knowledge_list_by_course_code(question_instance.core_course_code)

            extract_chain = cls.create_extract_knowledge_chain(llm)
            analysis_chain = cls.create_optimized_analysis_chain(llm2)
            print(f"题干🔥:{question}\n答案🚀:{analysis}")

            # 提取知识点
            knowledge_inputs = {
                "question": question,
                "analysis": analysis,
                "knowledge_list": knowledge_list
            }
            knowledge_response = extract_chain.run(knowledge_inputs)

            try:
                extracted_knowledge = json.loads(knowledge_response)
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试使用ast.literal_eval
                try:
                    extracted_knowledge = ast.literal_eval(knowledge_response)
                except:
                    print(f"无法解析知识点响应 (ID: {question_instance.id}): {knowledge_response}")
                    extracted_knowledge = []

            valid_knowledge_list = [k for k in extracted_knowledge
                                    if isinstance(k, str) and cls.check_in_knowledge_list(k, knowledge_list)]

            # 生成优化版解析
            analysis_inputs = {
                "question": question,
                "analysis": analysis,
                "valid_knowledge_list": valid_knowledge_list,
            }
            optimized_analysis = analysis_chain.run(analysis_inputs)

            # 存储优化版解析到数据库
            STQuestion.objects.filter(id=question_instance.id).update(analysis=optimized_analysis)
            print("解析存储完成✅")

            # 存储题目知识点关联
            for knowledge_name in valid_knowledge_list:
                try:
                    knowledge = STKnowledge.objects.get(
                        name=knowledge_name,
                    )
                    STQuestionKnowledge.objects.get_or_create(
                        question=question_instance,
                        knowledge=knowledge
                    )
                except STKnowledge.DoesNotExist:
                    print(f"知识点 '{knowledge_name}' 不存在于数据库中")
                except Exception as e:
                    print(f"存储知识点关联时出错 (ID: {question_instance.id}, 知识点: {knowledge_name}): {str(e)}")
            print("知识点存储完成✅")

            # 整理结果
            return {
                "题目id": question_instance.id,
                "题干": question,
                "原始解析": analysis,
                "优化版解析": optimized_analysis,
                "有效知识点列表": valid_knowledge_list,
            }

        except Exception as e:
            error_msg = f"处理题目 {question_instance.id} 时发生未预期的错误: {str(e)}"
            print(error_msg)
            import traceback
            print(f"完整堆栈信息:\n{traceback.format_exc()}")
            return {"error": error_msg, "题目id": question_instance.id}
