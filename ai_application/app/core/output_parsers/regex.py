import re


class RegexParser:
    """Parse the output of an LLM call using a regex."""

    def __init__(self, regex, output_keys: list[str], default_output_key: str | None = None):
        self.regex = regex
        self.output_keys = output_keys
        self.default_output_key = default_output_key

    def parse(self, text: str) -> dict[str, str]:
        """Parse the output of an LLM call."""
        match = re.search(self.regex, text)
        if match:
            return {key: match.group(i + 1) for i, key in enumerate(self.output_keys)}
        else:
            if self.default_output_key is None:
                raise ValueError(f"Could not parse output: {text}")
            else:
                return {
                    key: text if key == self.default_output_key else ""
                    for key in self.output_keys
                }
