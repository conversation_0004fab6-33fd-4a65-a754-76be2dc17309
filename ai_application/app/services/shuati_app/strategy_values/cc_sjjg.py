# 摸底策略
from app.services.shuati_app.constants import QuestionType
from app.services.shuati_app.dto import QuestionSelectStrategy, ModiUpRequired, UpgradeRequired, LearningStageRequired

modi_strategy = QuestionSelectStrategy(
    choice_question_num=10,
    subjective_question_num=0,
    total_knowledge_num=8,
    wrong_knowledge_percent=0,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=1,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 2, 2: 6, 3: 2, 4: 0, 5: 0},
    },
)

# 基础阶段-基础巩固 出题策略
basic_strategy = QuestionSelectStrategy(
    choice_question_num=10,
    subjective_question_num=0,
    total_knowledge_num=8,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 5, 2: 4, 3: 1, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
    },
)


# 基础阶段-基础提升初级 出题策略
basic_improve_l_strategy = QuestionSelectStrategy(
    choice_question_num=10,
    subjective_question_num=0,
    total_knowledge_num=8,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 2, 2: 6, 3: 2, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
    },
)

# 基础阶段-基础提升初级-强化 出题策略
basic_improve_l_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=10,
    subjective_question_num=0,
    total_knowledge_num=8,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 2, 2: 6, 3: 2, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
    },
)

# 基础阶段-基础提升中级 出题策略
basic_improve_m_strategy = QuestionSelectStrategy(
    choice_question_num=9,
    subjective_question_num=1,
    total_knowledge_num=8,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 1, 2: 3, 3: 4, 4: 1, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 1, 2: 0,3: 0, 4: 0, 5: 0},
    },
)

# 基础阶段-基础提升中级-强化 出题策略
basic_improve_m_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=9,
    subjective_question_num=1,
    total_knowledge_num=8,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 1, 2: 3, 3: 4, 4: 1, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 1, 2: 0, 3: 0, 4: 0, 5: 0},
    },
)

# 基础阶段-基础提升高级 出题策略
basic_improve_h_strategy = QuestionSelectStrategy(
    choice_question_num=5,
    subjective_question_num=3,
    total_knowledge_num=8,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 1, 2: 2, 3: 1, 4: 1, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 1, 4: 1, 5: 0},
    },
)

# 基础阶段-基础提升高级-强化 出题策略
basic_improve_h_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=5,
    subjective_question_num=3,
    total_knowledge_num=8,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 1, 2: 2, 3: 1, 4: 1, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 1, 4: 1, 5: 0},
    },
)

# 核心基础阶段 出题策略
core_basic_strategy = QuestionSelectStrategy(
    choice_question_num=9,
    subjective_question_num=1,
    total_knowledge_num=8,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 3, 2: 5, 3: 1, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 0, 4: 0, 5: 0},
    },
)

# 核心基础强化阶段 出题策略
core_basic_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=9,
    subjective_question_num=1,
    total_knowledge_num=8,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 3, 2: 5, 3: 1, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 0, 4: 0, 5: 0},
    },
)

# 核心提升_初级阶段 出题策略
core_improve_l_strategy = QuestionSelectStrategy(
    choice_question_num=9,
    subjective_question_num=1,
    total_knowledge_num=8,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 7, 3: 2, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 0, 4: 0, 5: 0},
    },
)

# 核心提升_初级强化阶段 出题策略
core_improve_l_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=9,
    subjective_question_num=1,
    total_knowledge_num=8,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 7, 3: 2, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 0, 4: 0, 5: 0},
    },
)

# 核心提升_中级阶段 出题策略
core_improve_m_strategy = QuestionSelectStrategy(
    choice_question_num=7,
    subjective_question_num=3,
    total_knowledge_num=8,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 4, 3: 3, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 1, 4: 1, 5: 0},
    },
)

# 核心提升_中级强化阶段 出题策略
core_improve_m_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=7,
    subjective_question_num=3,
    total_knowledge_num=8,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 4, 3: 3, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 1, 4: 1, 5: 0},
    },
)

# 核心提升_高级阶段 出题策略
core_improve_h_strategy = QuestionSelectStrategy(
    choice_question_num=6,
    subjective_question_num=4,
    total_knowledge_num=8,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 4, 3: 2, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 1, 4: 1, 5: 1},
    },
)

# 核心提升_高级强化阶段 出题策略
core_improve_h_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=6,
    subjective_question_num=4,
    total_knowledge_num=8,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    high_freq_knowledge_percent=0,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 4, 3: 2, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 1, 4: 1, 5: 1},
    },
)


# 摸底测升级要求
modi_up_required = ModiUpRequired(**dict(
    rounds_required=3,
    total_score_ratio=0.6,
    question_required=[
        {"question_type": 0, "difficulty": 3, "score_threshold": 4.5}
    ]
))


# 基础巩固阶段晋级规则
basic_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0.3},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0.3},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=3,
        question_required=[
            {"question_type": -1, "difficulty": -1, "accuracy": 0.70},
        ],
    )),
    strengthen_required=None,
    down_required=None,
)

# 【基础提升 - 初级】升降级规则
basic_improve_l_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.3},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.13, "accuracy": 0.35},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=5,
        question_required=[
            {"question_type": -1, "difficulty": -1, "accuracy": 0.70},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.13, "accuracy": (0.28, 0.35)},
        ],
    )),
    # 阶段强化轮校验
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.13, "accuracy": (0, 0.28)},
        ],
    )),
)

# 【基础提升 - 中级】升降级规则
basic_improve_m_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.2},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.13, "accuracy": 0.35},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=3,
        question_required=[
            {"question_type": -1, "difficulty": -1, "accuracy": 0},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.13, "accuracy": (0.28, 0.35)},
        ],
    )),
    # 阶段强化轮校验
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.13, "accuracy": (0, 0.28)},
        ],
    )),
)

# 【基础提升 - 高级】升降级规则
basic_improve_h_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.5},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.15, "accuracy": 0.4},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=5,
        question_required=[
            {"question_type": -1, "difficulty": -1, "accuracy": 0},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
            question_required=[
                {"question_type": 0, "difficulty": 3, "num_rate": 0.15, "accuracy": (0.3, 0.4)},
            ],
    )),
    # 阶段强化轮校验
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.15, "accuracy": (0, 0.3)},
        ],
    )),
)

# 核心基础阶段升降级规则
core_basic_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0.5},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0.8, "accuracy": 0.55},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=5,
        question_required=[
            {"question_type": 0, "difficulty": 2, "accuracy": 0.80},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0.8, "accuracy": (0.48, 0.55)},
        ],
    )),
    # 阶段强化轮校验
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0.8, "accuracy": (0, 0.48)},
        ],
    )),
)

# 核心-初级阶段升降级规则
core_improve_l_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.5},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.47, "accuracy": 0.55},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=5,
        question_required=[
            {"question_type": 0, "difficulty": 3, "accuracy": 0.80},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.47, "accuracy": (0.48, 0.55)},
        ],
    )),
    # 阶段强化轮校验
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.47, "accuracy": (0, 0.48)},
        ],
    )),
)

# 核心-中级阶段升降级规则
core_improve_m_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.5},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.40, "accuracy": 0.55},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=5,
        question_required=[
            {"question_type": 0, "difficulty": 3, "accuracy": 0.70},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.40, "accuracy": (0.48, 0.55)},
        ],
    )),
    # 阶段强化轮校验
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.40, "accuracy": (0, 0.48)},
        ],
    )),
)

# 核心-高级阶段升降级规则
core_improve_h_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 2, "difficulty": 2, "num_rate": 0.5},
        ],
        question_required=[
            {"question_type": 2, "difficulty": 2, "num_rate": 0.77, "accuracy": 0.55},
        ],
    )),
    up_again_required=None,
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 2, "difficulty": 2, "num_rate": 0.77, "accuracy": (0.48, 0.55)},
        ],
    )),
    # 阶段强化轮校验
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 2, "difficulty": 2, "num_rate": 0, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 2, "difficulty": 2, "num_rate": 0.77, "accuracy": (0, 0.48)},
        ],
    )),
)
