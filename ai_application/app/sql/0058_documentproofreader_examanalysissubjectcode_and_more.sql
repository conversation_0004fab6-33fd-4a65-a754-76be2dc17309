--
-- <PERSON>reate model DocumentProofreader
--
CREATE TABLE `ai_document_proofreader` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `file_name` varchar(255) NOT NULL, `original_content` longtext NOT NULL, `corrected_content` longtext NOT NULL, `labeled_content` longtext NOT NULL, `status` varchar(20) NOT NULL, `task_directory` varchar(255) NOT NULL);
--
-- Create model ExamAnalysisSubjectCode
--
CREATE TABLE `ai_exam_analysis_subject_code` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `subject_code` varchar(10) NOT NULL, `subject_name` varchar(20) NOT NULL, `enable` bool NOT NULL);
--
-- <PERSON>reate model GenerateQuestion
--
CREATE TABLE `ai_generate_question` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `subject_id` varchar(36) NOT NULL, `main_subject` varchar(36) NULL, `question` longtext NULL, `options` longtext NULL, `answer` varchar(36) NULL, `analysis` longtext NULL, `knowledge` varchar(36) NULL, `error_point` longtext NULL, `course_section_id` varchar(36) NULL, `chapter` varchar(36) NULL);
--
-- Create model keyword
--
CREATE TABLE `ai_dayiapp_keywords` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `keyword_no` varchar(255) NOT NULL UNIQUE, `keyword` longtext NOT NULL, `keyword_content` longtext NOT NULL, `created_at` datetime(6) NOT NULL);
--
-- Create model KnowledgePointPsychology
--
CREATE TABLE `ai_knowledge_psychology` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `lession_name` varchar(36) NOT NULL, `knowledge_point` json NOT NULL);
--
-- Create model KnowledgeSimple
--
CREATE TABLE `ai_knowledge_simple` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `course_id` varchar(40) NOT NULL, `name` varchar(100) NOT NULL, `definition` longtext NULL, `desc` longtext NULL, `tokens` integer NOT NULL, `is_usable` bool NOT NULL);
--
-- Create model ProofreadingDictionary
--
CREATE TABLE `ai_document_proofreading_dictionary` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `word` varchar(255) NOT NULL, `category` varchar(50) NOT NULL, `description` longtext NOT NULL, `usage_count` integer NOT NULL, `is_active` bool NOT NULL);
--
-- Create model SuperviseLearnStat
--
CREATE TABLE `ai_student_supervise_learn_stat` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_id` varchar(100) NOT NULL, `course_id` varchar(100) NULL, `subject_id` varchar(100) NULL, `start_date` date NULL, `end_date` date NULL, `task_id` varchar(36) NULL UNIQUE, `status` varchar(100) NULL, `fail_reason` longtext NULL, `query` longtext NULL, `analysis` longtext NULL);
--
-- Add field like_count to knowledgestore
--
ALTER TABLE `ai_knowledge_store` ADD COLUMN `like_count` integer DEFAULT 0 NOT NULL;
ALTER TABLE `ai_knowledge_store` ALTER COLUMN `like_count` DROP DEFAULT;
--
-- Add field search_count to knowledgestore
--
ALTER TABLE `ai_knowledge_store` ADD COLUMN `search_count` integer DEFAULT 0 NOT NULL;
ALTER TABLE `ai_knowledge_store` ALTER COLUMN `search_count` DROP DEFAULT;
--
-- Add field image_text to message
--
ALTER TABLE `ai_message` ADD COLUMN `image_text` longtext NULL;
--
-- Alter field model_id on appmodelconfig
--
ALTER TABLE `ai_app_model_config` MODIFY `model_id` varchar(64) NOT NULL;
--
-- Alter field level on categorycorrelation
--
-- (no-op)
--
-- Alter field model_id on conversation
--
ALTER TABLE `ai_conversation` MODIFY `model_id` varchar(64) NOT NULL;
--
-- Alter field status on hotarticlesource
--
-- (no-op)
--
-- Alter field level on majorcorrelation
--
-- (no-op)
--
-- Alter field model_id on message
--
ALTER TABLE `ai_message` MODIFY `model_id` varchar(64) NOT NULL;
--
-- Alter field model_id on messagetracing
--
ALTER TABLE `ai_message_tracing` MODIFY `model_id` varchar(64) NOT NULL;
--
-- Alter field model_id on prompttemplate
--
ALTER TABLE `ai_prompt_template` MODIFY `model_id` varchar(64) NOT NULL;
--
-- Create model ExamAnalysisPrompt
--
CREATE TABLE `ai_exam_analysis_prompt` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `prompt` longtext NULL, `subject_code_id` bigint NOT NULL);
--
-- Create model ExamAnalysisKnowledgePointAnalysis
--
CREATE TABLE `ai_exam_analysis_knowledge_point_analysis` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `year` varchar(4) NOT NULL, `subject` varchar(100) NOT NULL, `point_name` varchar(200) NOT NULL, `question_count` integer NOT NULL, `question_difficulty` json NOT NULL, `subject_code_id` bigint NULL);
--
-- Create model ExamAnalysisExamQuestion
--
CREATE TABLE `ai_exam_analysis_exam_question` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `year` varchar(4) NOT NULL, `question_number` integer NOT NULL, `content` longtext NULL, `score` numeric(3, 1) NOT NULL, `question_type` varchar(50) NOT NULL, `subject` varchar(50) NOT NULL, `knowledge_points` json NOT NULL, `difficulty` integer NOT NULL, `subject_code_id` bigint NOT NULL);
--
-- Create model DocumentProofreaderError
--
CREATE TABLE `ai_document_proofreader_error` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `error_id` varchar(50) NOT NULL, `error_text` longtext NOT NULL, `error_reason` longtext NOT NULL, `error_suggestion` longtext NOT NULL, `proofreader_id` bigint NOT NULL);
--
-- Create model DocumentProofreaderAsyncTask
--
CREATE TABLE `ai_document_proofreader_async_task` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `task_id` varchar(100) NOT NULL UNIQUE, `file_name` varchar(255) NOT NULL, `original_content` longtext NOT NULL, `status` varchar(20) NOT NULL, `fail_reason` longtext NOT NULL, `retry_times` integer NOT NULL, `user_session` varchar(100) NOT NULL, `notification_email` varchar(254) NOT NULL, `notification_sent` bool NOT NULL, `proofreader_id` bigint NULL);
--
-- Create model ExamAnalysisKnowledgePointWithStats
--
CREATE TABLE `ai_exam_analysis_knowledge_point_with_stats` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `subject` varchar(100) NOT NULL, `point_name` varchar(200) NOT NULL, `exam_count` integer NOT NULL, `avg_difficulty` double precision NOT NULL, `choice_count` integer NOT NULL, `choice_avg_difficulty` double precision NOT NULL, `comprehensive_count` integer NOT NULL, `comprehensive_avg_difficulty` double precision NOT NULL, `subject_code_id` bigint NULL);
--
-- Create model ExamAnalysisKnowledgePoint
--
CREATE TABLE `ai_exam_analysis_knowledge_point` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `subject` varchar(100) NOT NULL, `point_name` varchar(200) NOT NULL, `subject_code_id` bigint NULL);
CREATE INDEX `ai_document_proofreader_add_time_115f3c9b` ON `ai_document_proofreader` (`add_time`);
CREATE INDEX `ai_exam_analysis_subject_code_add_time_158fc2b6` ON `ai_exam_analysis_subject_code` (`add_time`);
CREATE INDEX `ai_generate_question_add_time_2380082c` ON `ai_generate_question` (`add_time`);
CREATE INDEX `ai_generate_question_subject_id_78af8c6c` ON `ai_generate_question` (`subject_id`);
CREATE INDEX `ai_dayiapp_keywords_add_time_b3c7d2e2` ON `ai_dayiapp_keywords` (`add_time`);
CREATE INDEX `ai_knowledge_psychology_add_time_14042796` ON `ai_knowledge_psychology` (`add_time`);
CREATE INDEX `ai_knowledge_psychology_lession_name_5873d0cd` ON `ai_knowledge_psychology` (`lession_name`);
CREATE INDEX `ai_knowledge_simple_add_time_fecbd175` ON `ai_knowledge_simple` (`add_time`);
CREATE INDEX `ai_knowledge_simple_course_id_6ba9609d` ON `ai_knowledge_simple` (`course_id`);
CREATE INDEX `ai_document_proofreading_dictionary_add_time_0f033993` ON `ai_document_proofreading_dictionary` (`add_time`);
CREATE INDEX `ai_document_proofreading_dictionary_word_ca6f2a26` ON `ai_document_proofreading_dictionary` (`word`);
CREATE INDEX `ai_student_supervise_learn_stat_add_time_8cd2ea0d` ON `ai_student_supervise_learn_stat` (`add_time`);
CREATE INDEX `ai_student_supervise_learn_stat_user_id_9db1f703` ON `ai_student_supervise_learn_stat` (`user_id`);
CREATE INDEX `ai_exam_analysis_prompt_add_time_6b598fc0` ON `ai_exam_analysis_prompt` (`add_time`);
CREATE INDEX `ai_exam_analysis_prompt_subject_code_id_1524eacc` ON `ai_exam_analysis_prompt` (`subject_code_id`);
CREATE INDEX `ai_exam_analysis_knowledge_point_analysis_add_time_d296dafe` ON `ai_exam_analysis_knowledge_point_analysis` (`add_time`);
CREATE INDEX `ai_exam_analysis_knowledge__subject_code_id_8c5d86b8` ON `ai_exam_analysis_knowledge_point_analysis` (`subject_code_id`);
CREATE INDEX `ai_exam_analysis_exam_question_add_time_09abce8c` ON `ai_exam_analysis_exam_question` (`add_time`);
CREATE INDEX `ai_exam_analysis_exam_question_subject_code_id_0cdcee43` ON `ai_exam_analysis_exam_question` (`subject_code_id`);
ALTER TABLE `ai_document_proofreader_error` ADD CONSTRAINT `ai_document_proofrea_proofreader_id_dc3d3ed7_fk_ai_docume` FOREIGN KEY (`proofreader_id`) REFERENCES `ai_document_proofreader` (`id`);
CREATE INDEX `ai_document_proofreader_error_add_time_44918e6e` ON `ai_document_proofreader_error` (`add_time`);
ALTER TABLE `ai_document_proofreader_async_task` ADD CONSTRAINT `ai_document_proofrea_proofreader_id_c9996a55_fk_ai_docume` FOREIGN KEY (`proofreader_id`) REFERENCES `ai_document_proofreader` (`id`);
CREATE INDEX `ai_document_proofreader_async_task_add_time_d64582bc` ON `ai_document_proofreader_async_task` (`add_time`);
ALTER TABLE `ai_exam_analysis_knowledge_point_with_stats` ADD CONSTRAINT `ai_exam_analysis_knowled_subject_point_name_b14c6cfa_uniq` UNIQUE (`subject`, `point_name`);
CREATE INDEX `ai_exam_analysis_knowledge_point_with_stats_add_time_89846dd5` ON `ai_exam_analysis_knowledge_point_with_stats` (`add_time`);
CREATE INDEX `ai_exam_analysis_knowledge__subject_code_id_559f0d1f` ON `ai_exam_analysis_knowledge_point_with_stats` (`subject_code_id`);
ALTER TABLE `ai_exam_analysis_knowledge_point` ADD CONSTRAINT `ai_exam_analysis_knowled_subject_point_name_c0b024a0_uniq` UNIQUE (`subject`, `point_name`);
CREATE INDEX `ai_exam_analysis_knowledge_point_add_time_4b900a62` ON `ai_exam_analysis_knowledge_point` (`add_time`);
CREATE INDEX `ai_exam_analysis_knowledge_point_subject_code_id_27693445` ON `ai_exam_analysis_knowledge_point` (`subject_code_id`);