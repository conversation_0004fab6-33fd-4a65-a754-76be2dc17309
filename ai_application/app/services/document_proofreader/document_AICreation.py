import json

from app.api.dto import ChatMessageDto
from app.models import Account, App
from app.services.app_generate_service import AppGenerateService
from app.services.document_proofreader.langchain_openai_thinking import ChatOpenAIThinking
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage
from typing import Generator, Dict, Any
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


class DocumentAICreationNewService:

    @classmethod
    def generate_content(cls, prompt: str, selected_text: str, account: Account):
        app = App.objects.filter(app_type='document_proofreader_ai_create').first()
        inputs = {
            'prompt': prompt,
            'selected_text': selected_text,
        }
        dto = ChatMessageDto(
            app_id=app.app_no,
            inputs=inputs,
            query=json.dumps(inputs, ensure_ascii=False),
            stream=True,
        )

        response = AppGenerateService.generate(dto, account, invoke_from='api')
        return response


class DocumentAICreationService:
    """AI内容生成服务"""
    
    def __init__(self, model_name: str = "doubao-1-5-pro-32k-250115"):
        self.model_name = model_name
        self.api_key = settings.DOUBAO_API_KEY
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3"
        self.is_cancelled = False
        
    def generate_content(self, prompt: str, selected_text: str = "") -> Generator[Dict[str, Any], None, None]:
        """
        生成AI内容
        
        Args:
            prompt: 用户提示词
            selected_text: 选中的文本内容（上下文）
            extra_instruction: 额外说明
            
        Yields:
            Dict: 包含生成内容的字典
        """
        try:
            # 初始化模型，如果是推理模型则使用ChatOpenAIThinking
            if self.model_name == "deepseek-r1-250528":
                llm = ChatOpenAIThinking(
                    base_url=self.base_url,
                    model=self.model_name,
                    api_key=self.api_key,
                    streaming=True,
                    temperature=0.7
                )
            else:
                llm = ChatOpenAI(
                    base_url=self.base_url,
                    model=self.model_name,
                    api_key=self.api_key,
                    streaming=True,
                    temperature=0.7
                )
            
            # 构建完整提示
            extra_prompt = f"遵循规则:1.模型回答时不需要额外说明，直接回答即可。2.保留原本格式"
            
            if selected_text:
                full_prompt = f"{prompt}\n\n原文内容：\n{selected_text}\n{extra_prompt}"
            else:
                full_prompt = f"{prompt}\n{extra_prompt}"
            
            logger.info(f"开始生成内容，模型：{self.model_name}")
            
            # 流式生成
            for chunk in llm.stream([HumanMessage(content=full_prompt)]):
                if self.is_cancelled:
                    yield {"type": "cancelled", "content": ""}
                    break
                
                # # 处理思考过程内容
                # if hasattr(chunk, 'additional_kwargs') and chunk.additional_kwargs.get('reasoning_content'):
                #     reasoning_content = chunk.additional_kwargs['reasoning_content']
                #     yield {"type": "thinking", "content": reasoning_content}
                
                # 处理正常内容
                if hasattr(chunk, 'content') and chunk.content:
                    yield {"type": "content", "content": chunk.content}
            
            if not self.is_cancelled:
                yield {"type": "finished", "content": ""}
                
        except Exception as e:
            logger.error(f"生成内容时发生错误：{str(e)}")
            yield {"type": "error", "content": str(e)}
    
    def cancel(self):
        """取消生成"""
        self.is_cancelled = True

