# Generated by Django 4.2 on 2025-03-25 05:03

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0029_knowledgelibrary_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubjectDomain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_code', models.CharField(blank=True, max_length=100, verbose_name='一级科目code')),
                ('subject_name', models.CharField(blank=True, max_length=100, verbose_name='一级科目名称')),
                ('main_subject_code', models.CharField(blank=True, max_length=100, verbose_name='主科目code')),
                ('main_subject_name', models.CharField(blank=True, max_length=100, verbose_name='主科目名称')),
                ('is_knowledge_search_enable', models.BooleanField(default=False, verbose_name='是否知识解析使用')),
            ],
            options={
                'verbose_name': '学科表',
                'verbose_name_plural': '学科表',
                'db_table': 'ai_data_subject_domain',
            },
        ),
        migrations.AddField(
            model_name='knowledgelibrary',
            name='subject_domain',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.subjectdomain'),
        ),
    ]
