import logging

from aip import AipOcr
from django.conf import settings

logger = logging.getLogger(__name__)


class BaiduOcr:

    def __init__(self):
        """ 你的 APPID AK SK """
        APP_ID = settings.BAIDU_OCR_API_ID
        API_KEY = settings.BAIDU_OCR_API_KEY
        SECRET_KEY = settings.BAIDU_OCR_SECRET_KEY

        self.client = AipOcr(APP_ID, API_KEY, SECRET_KEY)

    def basic_accurate(
            self,
            image_file=None,
            image_file_url=None,
            options=None
    ):
        """
        # 调用通用文字识别（高精度版）
        https://cloud.baidu.com/doc/OCR/s/7kibizyfm
        :return:
        """
        if options is None:
            options = {}
        # 是否检测图像朝向
        if 'detect_direction' not in options:
            options['detect_direction'] = 'true'
        # 是否返回识别结果中每一行的置信度
        if 'probability' not in options:
            options['probability'] = 'false'
        # 是否开启行级别的多方向文字识别
        if 'multidirectional_recognize' not in options:
            options['multidirectional_recognize'] = 'false'

        try:
            if image_file:
                res = self.client.basicAccurate(image_file, options=options)
            elif image_file_url:
                res = self.client.basicAccurateUrl(image_file_url, options=options)
            else:
                return []
        except Exception as e:
            logger.exception(e)
            return []

        if 'error_code' in res:
            logger.error(f'baidu_lexer_analysis_error:{res}')
            return []

        return res.get('words_result', [])
