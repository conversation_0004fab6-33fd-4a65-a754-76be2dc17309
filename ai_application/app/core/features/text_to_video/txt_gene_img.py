import os
# 通过 pip install 'volcengine-python-sdk[ark]' 安装方舟SDK
from volcenginesdkarkruntime import Ark
import requests
from PIL import Image
from io import BytesIO
from IPython.display import display

def geneImg(vscript,save_path,v_id):

    # 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
    # 初始化Ark客户端，从环境变量中读取您的API Key
    client = Ark(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 从环境变量中获取您的 API Key。此为默认方式，您可根据需要进行修改
        # api_key=os.environ.get("ARK_API_KEY"),
        api_key="4c88e68f-957e-463c-add6-7a08f2cfff4f",#这是公司的API_Key
    )

    imagesResponse = client.images.generate(
        model="doubao-seedream-3-0-t2i-250415",
        prompt=vscript,
        watermark=False,
        size="1280x720"
    )

    # 生成图片的URL
    image_url = imagesResponse.data[0].url

    # 下载并保存图片
    response = requests.get(image_url)
    if response.status_code == 200:
        # 保存到文件
        with open(f"{save_path}/scene{v_id}.jpeg", "wb") as f:
            f.write(response.content)

        # 用Pillow加载图片
        img = Image.open(BytesIO(response.content))

        # 在Jupyter中显示（可选）
        display(img)
        print("图片已保存🦖")
    else:
        print("下载失败！")
