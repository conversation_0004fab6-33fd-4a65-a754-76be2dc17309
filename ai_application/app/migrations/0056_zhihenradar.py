# Generated by Django 4.2 on 2025-05-28 09:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0055_studentlearnstat_rec_days'),
    ]

    operations = [
        migrations.CreateModel(
            name='zhihen<PERSON>adar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('assistant_id', models.CharField(blank=True, db_index=True, max_length=36, verbose_name='助教id')),
                ('subject_id', models.Char<PERSON>ield(blank=True, max_length=36, verbose_name='学科id')),
                ('question_id', models.CharField(blank=True, max_length=36, verbose_name='问题id')),
                ('question', models.TextField(null=True, verbose_name='问题')),
                ('answer_id', models.CharField(max_length=36, null=None, verbose_name='回答id')),
                ('answer', models.TextField(null=True, verbose_name='回答')),
                ('status', models.IntegerField(default=-1, verbose_name='是否为AI')),
                ('explain', models.TextField(null=True, verbose_name='解释')),
                ('analysis', models.TextField(blank=True, default='None', max_length=36, verbose_name='具体判断')),
            ],
            options={
                'verbose_name': '智痕雷达',
                'verbose_name_plural': '智痕雷达',
                'db_table': 'ai_zhihen_radar',
            },
        ),
    ]
