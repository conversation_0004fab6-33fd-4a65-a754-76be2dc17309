<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学科关联度统计系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .controls {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        input[type="number"] {
            width: 60px;
            padding: 5px;
        }
        .category-header {
            background-color: #d9edf7;
            font-weight: bold;
        }
        .scrollable-table {
            max-height: 600px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
            min-width: 200px;
        }
        .dropdown-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .stats-display {
            background-color: #e7f3fe;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>学科关联度统计系统</h1>

        <div class="controls">
            <div class="dropdown-container">
                <label for="major-select">选择本科二级门类:</label>
                <select id="major-select">
                    <!-- Options will be populated by JavaScript -->
                </select>
                <button id="save-btn">保存</button>
            </div>
            <button id="export-btn">导出Excel</button>
        </div>

        <!-- Stats display for current major -->
        <div id="current-major-stats" class="stats-display" style="display: none;">
            <span id="stats-text"></span>
        </div>

        <div class="scrollable-table">
            <table id="correlation-table">
                <thead>
                    <tr>
                        <th>本科一级学科</th>
                        <th>本科二级门类</th>
                        <th>研究生一级学科</th>
                        <th>研究生二级门类</th>
                        <th>关联度等级(0-10)</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                    <!-- 数据将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
       // 研究生学科数据
        const graduateData = {
            "01哲学": ["0101哲学", "0151应用伦理"],
            "02经济学": ["0201理论经济学", "0202应用经济学", "0270统计学", "0271区域国别学", "0251金融", "0252应用统计", "0253税务", "0254国际商务", "0255保险", "0256资产评估", "0258数字经济"],
            "03法学": ["0301法学", "0302政治学", "0303社会学", "0304民族学", "0305马克思主义理论", "0306公安学", "0307中共党史党建学", "0308纪检监察学", "0370国家安全学", "0371区域国别学", "0351法律", "0352社会工作", "0353警务", "0354知识产权", "0355国际事务"],
            "04教育学": ["0401教育学", "0402心理学", "0403体育学", "0471教育经济与管理", "0451教育", "0452体育", "0453国际中文教育", "0454应用心理"],
            "05文学": ["0501中国语言文学", "0502外国语言文学", "0503新闻传播学", "0551翻译", "0552新闻与传播", "0553出版"],
            "06历史学": ["0601考古学", "0602中国史", "0603世界史", "0670区域国别学", "0651博物馆"],
            "07理学": ["0701数学", "0702物理", "0703化学", "0704天文学", "0705地理学", "0706大气科学", "0707海洋科学", "0708地球物理学", "0709地质学", "0710生物学", "0711系统科学", "0712科学技术史", "0713生态学", "0714统计学", "0751气象"],
            "08工学": ["0801力学", "0802机械工程", "0803光学工程", "0804仪器科学与技术", "0805材料科学与工程", "0806冶金工程", "0807动力工程与工程热物理", "0808电气工程", "0809电子科学与技术", "0810信息与通信工程", "0811控制科学与工程", "0812计算机科学与技术", "0813建筑学", "0814土木工程", "0815水利工程", "0816测绘科学与技术", "0817化学工程与技术", "0818地质资源与地质工程", "0819矿业工程", "0820石油与天然气工程", "0821纺织科学与工程", "0822轻工技术与工程", "0823交通运输工程", "0824船舶与海洋工程", "0825航空宇航科学与技术", "0826兵器科学与技术", "0827核科学与技术", "0828农业工程", "0829林业工程", "0830环境科学与工程", "0831生物医学工程", "0832食品科学与工程", "0833城乡规划学", "0835软件工程", "0836生物工程", "0837安全科学与工程", "0838公安技术", "0839网络空间安全", "0851建筑", "0853城乡规划", "0854电子信息", "0855机械", "0856材料与化工", "0857资源与环境", "0858能源动力", "0859土木水利", "0860生物与医药", "0861交通运输", "0862风景园林"],
            "09农学": ["0901作物学", "0902园艺学", "0903农业资源与环境", "0904植物保护", "0905畜牧学", "0906兽医学", "0907林学", "0908水产", "0909草学", "0910水土保持与荒漠化防治学", "0951农业", "0952兽医", "0954林学", "0955食品与营养"],
            "10医学": ["1001基础医学", "1002临床医学", "1003口腔医学", "1004公共卫生与预防医学", "1005中医学", "1006中西医结合", "1007药学", "1008中药学", "1009特种医学", "1011护理学", "1012法医学", "1051临床医学", "1052口腔医学", "1053公共卫生", "1054护理", "1055药学", "1056中药学", "1057中医", "1058医学技术", "1059针灸"],
            "12管理学": ["1201管理科学与工程", "1202工商管理学", "1203农林经济管理", "1204公共管理学", "1205信息资源管理", "1251工商管理", "1252公共管理", "1253会计", "1254旅游管理", "1255图书情报", "1256工程管理", "1257审计"],
            "13艺术学": ["1301艺术学", "1352音乐", "1353舞蹈", "1354戏剧与影视", "1355戏曲与曲艺", "1356美术与书法", "1357设计"],
            "14交叉学科": ["1401集成电路科学与工程", "1402国家安全学", "1403设计学", "1404遥感科学与技术", "1405智能科学与技术", "1406纳米科学与技术", "1407区域国别学", "1451文物", "1452密码"]
        };

        // 本科学科数据
        const undergraduateData = {
            "01哲学": ["0101哲学类"],
            "02经济学": ["0201经济学类", "0202财政学类", "0203金融学类", "0204经济与贸易类"],
            "03法学": ["0301法学类", "0302政治学类", "0303社会学类", "0304民族学类", "0305马克思主义理论类", "0306公安学类"],
            "04教育学": ["0401教育学类", "0402体育学类"],
            "05文学": ["0501中国语言文学类", "0502外国语言文学类", "0503新闻传播类"],
            "06历史学": ["0601历史学类"],
            "07理学": ["0701数学类", "0702物理类", "0703化学类", "0704天文学类", "0705地理科学类", "0706大气科学类", "0708地球物理类", "0709地质学类", "0710生物科学类", "0711心理学类", "0712统计学类"],
            "08工学": ["0801力学类", "0802机械类", "0803仪器类", "0804材料类", "0805能源动力类", "0806电气类", "0807电子信息类", "0808自动化类", "0809计算机类", "0810土木类", "0811水利类", "0812测绘类", "0813化工与制药类", "0814地质类", "0815矿业类", "0816纺织类", "0817轻工类", "0818交通运输类", "0819海洋工程类", "0820航空航天类", "0821兵器类", "0822核工程类", "0823农业工程类", "0824林业工程类", "0825环境科学与工程类", "0826生物医学工程类", "0827食品科学与工程类", "0828建筑类", "0829安全科学与工程类", "0830生物工程类", "0831公安技术类", "0832交叉工程类"],
            "09农学": ["0901植物生产类", "0902自然保护与环境生态类", "0903动物生产类", "0904动物医学类", "0905林学类", "0906水产类", "0907草学类"],
            "10医学": ["1001基础医学类", "1002临床医学类", "1003口腔医学类", "1004公共卫生与预防医学类", "1005中医学类", "1006中西医结合类", "1007药学类", "1008中药学类", "1009法医学类", "1010医学技术类", "1011护理学类"],
            "12管理学": ["1201管理科学与工程", "1202工商管理类", "1203农业经济管理类", "1204公共管理类", "1205图书情报与档案管理类", "1206物流管理与工程类", "1207工业工程类", "1208电子商务类", "1209旅游管理类"],
            "13艺术学": ["1301艺术学理论类", "1302音乐与舞蹈学类", "1303戏剧与影视学类", "1304美术学类", "1305设计学类"]
        };

        let groupedData = {};
        let currentMajor = null;

        function groupDataByUgMajor(data) {
            return data.reduce((acc, item) => {
                if (!acc[item.ugMajor]) acc[item.ugMajor] = [];
                acc[item.ugMajor].push(item);
                return acc;
            }, {});
        }

        function populateMajorDropdown() {
            const select = document.getElementById('major-select');
            select.innerHTML = '';

            const majors = Object.keys(groupedData);
            if (majors.length === 0) return;

            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '-- 请选择本科二级门类 --';
            defaultOption.disabled = true;
            defaultOption.selected = true;
            select.appendChild(defaultOption);

            // Add major options
            majors.forEach(major => {
                const option = document.createElement('option');
                option.value = major;
                option.textContent = major;
                select.appendChild(option);
            });

            select.addEventListener('change', (e) => {
                currentMajor = e.target.value;
                if (currentMajor) {
                    renderMajorData(currentMajor);
                    document.getElementById('current-major-stats').style.display = 'block';
                } else {
                    document.getElementById('current-major-stats').style.display = 'none';
                }
            });
        }

        function renderMajorData(major) {
            const data = groupedData[major] || [];
            renderTable(data);
            updateStatsDisplay(major);
        }

        function updateStatsDisplay(major) {
            const data = groupedData[major] || [];
            const total = data.reduce((sum, item) => sum + item.level, 0);
            const avg = data.length > 0 ? (total / data.length).toFixed(2) : 0;

            document.getElementById('stats-text').textContent =
                `${major}: 总关联度 ${total}, 平均关联度 ${avg}`;
        }

        function generateTableData() {
            let correlationData = [];

            for (const [ugCategory, ugMajors] of Object.entries(undergraduateData)) {
                for (const ugMajor of ugMajors) {
                    for (const [gCategory, gMajors] of Object.entries(graduateData)) {
                        for (const gMajor of gMajors) {
                            correlationData.push({
                                ugCategory,
                                ugMajor,
                                gCategory,
                                gMajor,
                                level: 0
                            });
                        }
                    }
                }
            }

            localStorage.setItem('correlationData', JSON.stringify(correlationData));
            groupedData = groupDataByUgMajor(correlationData);
            populateMajorDropdown();
        }

        function renderTable(data) {
            const tableBody = document.getElementById('table-body');
            tableBody.innerHTML = '';

            data.forEach(item => {
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${item.ugCategory}</td>
                    <td>${item.ugMajor}</td>
                    <td>${item.gCategory}</td>
                    <td>${item.gMajor}</td>
                    <td><input type="number" min="0" max="10" value="${item.level}"
                         data-ug="${item.ugMajor}" data-g="${item.gMajor}"
                         class="correlation-input"></td>
                `;

                tableBody.appendChild(row);
            });

            document.querySelectorAll('.correlation-input').forEach(input => {
                input.addEventListener('change', updateCorrelation);
            });
        }

        function updateCorrelation(e) {
            const input = e.target;
            const ugMajor = input.getAttribute('data-ug');
            const gMajor = input.getAttribute('data-g');
            const level = parseInt(input.value) || 0;

            let correlationData = JSON.parse(localStorage.getItem('correlationData')) || [];

            correlationData = correlationData.map(item => {
                if (item.ugMajor === ugMajor && item.gMajor === gMajor) {
                    return {...item, level};
                }
                return item;
            });

            localStorage.setItem('correlationData', JSON.stringify(correlationData));
            groupedData = groupDataByUgMajor(correlationData);
        }

        function saveCurrentMajorData() {
            if (!currentMajor) {
                alert('请先选择本科二级门类');
                return;
            }

            // Update the stats display
            updateStatsDisplay(currentMajor);
            alert('保存成功！');
        }

        function exportToExcel() {
    const correlationData = JSON.parse(localStorage.getItem('correlationData')) || [];
    groupedData = groupDataByUgMajor(correlationData);

    let csvContent = "data:text/csv;charset=utf-8,";
    const headers = ["本科一级学科", "本科二级门类", "研究生一级学科", "研究生二级门类", "关联度等级"];
    csvContent += headers.join(",") + "\r\n";

    // Process each undergraduate major
    Object.keys(groupedData).forEach(ugMajor => {
        const majorData = groupedData[ugMajor];
        let total = 0;

        // Add all records for this major
        majorData.forEach(item => {
            csvContent += [
                item.ugCategory,
                item.ugMajor,
                item.gCategory,
                item.gMajor,
                item.level
            ].join(",") + "\r\n";
            total += item.level;
        });

        // Add summary row for this major
        const avg = majorData.length > 0 ? (total / majorData.length).toFixed(1) : 0;
        csvContent += `该二级门类总关联度,${total},平均关联度,${avg}\r\n\r\n`;
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "学科关联度统计.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
        document.addEventListener('DOMContentLoaded', () => {
            if (!localStorage.getItem('correlationData')) {
                generateTableData();
            } else {
                const correlationData = JSON.parse(localStorage.getItem('correlationData'));
                groupedData = groupDataByUgMajor(correlationData);
                populateMajorDropdown();
            }

            document.getElementById('save-btn').addEventListener('click', saveCurrentMajorData);
            document.getElementById('export-btn').addEventListener('click', exportToExcel);
        });
    </script>
</body>
</html>


