from celery import shared_task

from app.models import EnglishPaperExplain
from app.services.coze_workflow_service import CozeWorkflowService
from app.services.main_subject.en_paper_explain_service import EnPaperExplainService


@shared_task(queue='dataset')
def auto_check_english_paper_explain_gen():
    qs = EnglishPaperExplain.objects.filter(is_deleted=False, status='NOT_START')[:10]
    for record in qs:
        check_english_paper_explain_gen(record)


def check_english_paper_explain_gen(record: EnglishPaperExplain):
    record.status = 'ING'
    record.save(update_fields=['status'])

    try:
        EnPaperExplainService.submit_answer(record)
    except Exception as e:
        record.status = 'FAIL'
        record.fail_reason = str(e)
        record.save(update_fields=['status', 'fail_reason'])


@shared_task(queue='dataset')
def auto_check_english_paper_summary_explain_gen():
    qs = EnglishPaperExplain.objects.filter(is_deleted=False, summary_status='NOT_START')[:10]
    for record in qs:
        process_english_paper_summary_explain_gen(record)


def process_english_paper_summary_explain_gen(record: EnglishPaperExplain):
    subjective_qs = record.englishpaperquestionexplain_set.filter(is_deleted=False)
    if not subjective_qs:
        return

    is_all_subjective_success = True

    yd_total_question_num = 0
    yd_right_question_num = 0

    wx_user_situtation = ''
    yd_user_situtation = ''
    xtx_user_situtation = ''
    fy_situtation = ''
    xzw_situtation = ''
    dzw_situtation = ''

    for item in subjective_qs:
        if item.question_type in ['翻译', '小作文', '大作文']:
            if item.status == 'FAIL':
                record.summary_status = 'FAIL'
                record.save(update_fields=['summary_status'])
                return
            elif item.status == 'ING':
                is_all_subjective_success = False
                continue
            elif item.status == 'SUCCESS':
                if item.question_type == '翻译':
                    fy_situtation = item.explain_content.get('fy_situtation')
                elif item.question_type == '小作文':
                    xzw_situtation = item.explain_content.get('xzw_situtation')
                elif item.question_type == '大作文':
                    dzw_situtation = item.explain_content.get('dzw_situtation')
        else:
            if item.question_type == '完形填空':
                wx_user_rate = int(
                    100 * item.sub_right_question_num / item.sub_question_num) if item.sub_question_num else 0
                wx_user_situtation = f'{wx_user_rate}%'
            elif item.question_type == '阅读理解':
                yd_total_question_num += item.sub_question_num
                yd_right_question_num += item.sub_right_question_num
            elif item.question_type == '新题型':
                xtx_user_rate = int(
                    100 * item.sub_right_question_num / item.sub_question_num) if item.sub_question_num else 0
                xtx_user_situtation = f'{xtx_user_rate}%'

    yd_user_rate = int(100 * yd_right_question_num / yd_total_question_num) if yd_total_question_num else 0
    yd_user_situtation = f'{yd_user_rate}%'
    if not is_all_subjective_success:
        return

    summary_params = {
        'wx_user_situtation': wx_user_situtation,
        'yd_user_situtation': yd_user_situtation,
        'xtx_user_situtation': xtx_user_situtation,
        'fy_situtation': fy_situtation,
        'xzw_situtation': xzw_situtation,
        'dzw_situtation': dzw_situtation,
    }

    record.summary_status = 'ING'
    record.summary_req_params = summary_params
    record.save(update_fields=['summary_status', 'summary_req_params'])

    CozeWorkflowService.create_workflow_runs(
        'english_paper_explain_summary',
        summary_params,
        ext_params={'paper_explain_id': record.id}
    )


@shared_task(queue='dataset')
def auto_check_english_paper_status():
    qs = EnglishPaperExplain.objects.filter(is_deleted=False, status='ING')[:10]
    for record in qs:
        check_english_paper_status(record)


def check_english_paper_status(record: EnglishPaperExplain):
    subjective_qs = record.englishpaperquestionexplain_set.filter(is_deleted=False)
    if not subjective_qs:
        return

    has_fail = False
    for item in subjective_qs:
        # 有一个未完成则跳过
        if item.status == 'ING':
            return
        elif item.status == 'FAIL':
            has_fail = True

    # 总结未完成也跳过
    if record.summary_status in ['NOT_START', 'ING']:
        return

    elif record.summary_status == 'FAIL':
        has_fail = True

    if has_fail:
        record.status = 'FAIL'
    else:
        record.status = 'SUCCESS'
    record.save()
