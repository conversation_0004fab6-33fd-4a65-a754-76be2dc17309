from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.models import PromptTemplate


class MathQuestionKnowledgeExtractFeature:

    def __init__(self):
        self.prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no='math_question_knowledge_extract').first()
        if not self.prompt_template:
            raise Exception('prompt template not found')

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            knowledge_list_str: list,
            question_content: str
    ) -> LLMResult:
        user_inputs = application_generate_entity.inputs.copy()
        user_inputs['knowledge'] = knowledge_list_str

        prompt_template = self.prompt_template
        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=''
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=question_content,
            stream=False
        )
