from enum import Enum
from typing import Any

from app.core.model_runtime.entities.llm_entities import LLMResultChunk, LLMResult
from django_ext.base_dto_model import MyBaseModel


class QueueEvent(str, Enum):
    """
    QueueEvent enum
    """
    LLM_CHUNK = "llm_chunk"
    MESSAGE_END = "message_end"
    ERROR = "error"
    STOP = "stop_words"
    RAG_MESSAGE_END = "rag_message_end"
    RAG_DIRECT_CHUNK_BEGIN = "rag_direct_chunk_begin"


class AppQueueEvent(MyBaseModel):
    event: QueueEvent


class QueueLLMChunkEvent(AppQueueEvent):
    """
    QueueLLMChunkEvent entity
    """
    event: QueueEvent = QueueEvent.LLM_CHUNK
    chunk: LLMResultChunk


class QueueErrorEvent(AppQueueEvent):
    class ErrorType(Enum):
        CONVERSATION_TOKEN_EXCEED = "conversation_token_exceed"
        CONTAIN_SENSITIVE = "contain_sensitive"
        LLM_REQUEST_ERROR = "LLM_REQUEST_ERROR"
        SPECIAL_ERROR = "SPECIAL_ERROR"
        OTHER = "OTHER"

    event: QueueEvent = QueueEvent.ERROR
    error: Any = None
    error_type: ErrorType


class QueueStopEvent(AppQueueEvent):
    class StopBy(Enum):
        USER_MANUAL = "user-manual"
        SYSTEM_EXIT = "system_exit"

    event: QueueEvent = QueueEvent.STOP
    stopped_by: StopBy


class QueueMessageEndEvent(AppQueueEvent):
    """
    QueueMessageEndEvent entity
    """
    event: QueueEvent = QueueEvent.MESSAGE_END
    llm_result: LLMResult | None = None


class MessageQueueMessage(MyBaseModel):
    task_id: str
    app_mode: str
    event: AppQueueEvent
    message_id: str
    conversation_id: str
