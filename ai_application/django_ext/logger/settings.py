import os

from django.conf import settings

LOG_DIR = settings.LOG_DIR if hasattr(settings, 'LOG_DIR') else os.path.join(settings.BASE_DIR, 'log')
LOG_LEVEL = "DEBUG" if settings.DEBUG else 'INFO'

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "[%(asctime)s: %(levelname)s/%(threadName)s:%(thread)d] [%(name)s:%(lineno)d] "
                      "[%(module)s:%(funcName)s]: %(message)s"
        }
    },
    "filters": {
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    "handlers": {
        "default": {
            "level": "DEBUG",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "%s/all.log" % LOG_DIR,
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'formatter': 'standard'
        },
        "error": {
            "level": "ERROR",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "%s/error.log" % LOG_DIR,
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'formatter': 'standard'
        },
        "console": {
            "level": "DEBUG",
            'filters': ['require_debug_true'],
            "class": "logging.StreamHandler",
            'formatter': 'standard'
        },
        # "request_handler": {
        #     "level": "DEBUG",
        #     "class": "logging.handlers.RotatingFileHandler",
        #     "filename": "%s/request.log" % LOG_DIR,
        #     'maxBytes': 1024 * 1024 * 5,
        #     'backupCount': 5,
        #     'formatter': 'standard'
        # },
        "db_handler": {
            "level": "DEBUG",
            "class": "logging.handlers.RotatingFileHandler",
            'filters': ['require_debug_true'],
            "filename": "%s/db.log" % LOG_DIR,
            'maxBytes': 1024 * 1024 * 5,
            'backupCount': 5,
            'formatter': 'standard'
        }
    },
    "loggers": {
        '': {
            'handlers': ['default', 'error', 'console'],
            'level': LOG_LEVEL,
            'propagate': True
        },
        # 'django.server': {
        #     'handlers': ['default', 'request_handler', 'error'],
        #     'level': LOG_LEVEL,
        #     'propagate': False,
        # },
        # 'django.request': {
        #     'handlers': ['default', 'request_handler', 'error'],
        #     'level': LOG_LEVEL,
        #     'propagate': False,
        # },
        'django.db.backends': {
            'handlers': ['db_handler', 'error'],
            'level': LOG_LEVEL,
            'propagate': False
        }
    }
}
