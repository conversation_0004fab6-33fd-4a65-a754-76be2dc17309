import logging
import os

from app.core.helper.module_import_helper import load_single_subclass_from_source
from app.core.model_runtime.entities.provider_entities import ProviderEntity, ModelType
from app.core.model_runtime.model_providers._base.model_provider import ModelProvider

logger = logging.getLogger(__name__)


class ModelProviderFactory:
    model_providers: dict[str, ModelProvider] | None = None

    def __init__(self) -> None:
        # for cache in memory
        self.get_providers()

    def get_providers(self) -> list[ProviderEntity]:
        model_provider_map = self._get_model_provider_map()
        providers = []
        for model_provider_instance in model_provider_map.values():
            # get provider schema
            provider_schema = model_provider_instance.get_provider_schema()
            provider_schema.models = model_provider_instance.models(ModelType.LLM)

            providers.append(provider_schema)
        return providers

    def get_provider_instance(self, provider: str) -> ModelProvider:
        model_providers = self._get_model_provider_map()

        model_provider = model_providers.get(provider)
        if not model_providers:
            raise Exception(f"Invalid provider: {provider}")
        return model_provider

    def _get_model_provider_map(self) -> dict[str, ModelProvider]:
        if self.model_providers:
            return self.model_providers

        current_path = os.path.abspath(__file__)
        model_providers_path = os.path.dirname(current_path)

        model_provider_dir_paths = [
            os.path.join(model_providers_path, model_provider_dir)
            for model_provider_dir in os.listdir(model_providers_path)
            if not model_provider_dir.startswith("_")
               and os.path.isdir(os.path.join(model_providers_path, model_provider_dir))
        ]

        model_providers: dict[str, ModelProvider] = {}
        for model_provider_dir_path in model_provider_dir_paths:
            model_provider_name = os.path.basename(model_provider_dir_path)

            py_path = os.path.join(model_provider_dir_path, model_provider_name + ".py")

            parent_module = '.'.join(self.__class__.__module__.split('.')[:-1])
            model_provider_class = load_single_subclass_from_source(
                module_name=f"{parent_module}.{model_provider_name}.{model_provider_name}",
                script_path=py_path,
                parent_type=ModelProvider,
            )
            if not model_provider_class:
                logger.warning(f"Missing Model Provider Class that extends ModelProvider in {py_path}, Skip.")
                continue

            model_providers[model_provider_name] = model_provider_class()
        self.model_providers = model_providers
        return model_providers
