import os
import django
import re
import ast
import json
from datetime import datetime, timedelta
from typing import TypedDict, Dict, Any, List, Tuple, Optional

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()

from app.models import CoursePackageContent, SuperViseInitStudentStatus
from langchain.agents import initialize_agent, Tool, AgentType
from langchain_openai import ChatOpenAI
from django.conf import settings
from ai_application.settings import DAYI_TEXT_MODEL
from langchain_core.prompts import PromptTemplate
from langgraph.graph import StateGraph, END

# =====================================================================
# 状态定义
# =====================================================================
class GraphState(TypedDict):
    user_data: Dict[str, Any]
    macro_plan: Dict[str, Any]  # 存储宏观规划结果
    subject_plans: Dict[str, str]  # 存储每个科目的详细规划
    reviewer_feedback: str  # 审核反馈
    status: str  # 'pass', 'fail', 'in_progress'
    current_subject: str  # 当前正在处理的科目

# =====================================================================
# 智能体定义
# =====================================================================
class TestFoundationService:
    
    # 原有工具函数保持不变
    @staticmethod
    def get_course_package_content(subject):
        """Query CoursePackageContent by package name"""
        queryset = CoursePackageContent.objects.filter(subject=subject).all()
        return [{
            "unit_name": item.unit_name,
            "stage_name": item.stage_name,
            "study_target": item.study_target,
            "study_guidence": item.study_guidence,
            "important_sections": item.important_sections
        } for item in queryset]
    
    @staticmethod
    def get_student_analysis(user_id):
        """Retrieve analysis content from SuperViseInitStudentStatus"""
        student_data = SuperViseInitStudentStatus.objects.filter(user_id=user_id).first()
        if not student_data:
            return None
            
        query = student_data.query
        pattern = r"'考试范围':\s*(\{[^{}]*\})"
        match = re.search(pattern, query)
        subjects = match.group(1) if match else "{}"
        exam_year = student_data.exam_date[:4]
        end_date = f"{exam_year}-6-30"
        return {
            "初始画像": student_data.analysis,
            "结束时间": end_date,
            "开始时间": student_data.date,
            "用户状态": student_data.study_status,
            "考试科目": subjects,
            "学习阶段": student_data.study_stage if student_data.study_stage != "无" else None
        }

    # =================================================================
    # 宏观规划智能体 (MacroPlannerAgent)
    # =================================================================
    class MacroPlannerAgent:
        def generate_macro_plan(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
            """生成宏观规划，划分阶段并分配时间比例"""
            # 获取开始时间和考试日期
            start_date_str = user_data.get("开始时间")
            exam_date_str = user_data.get("结束日期")
            if not start_date_str or not exam_date_str:
                return {"error": "缺少开始时间或结束日期"}
            
            try:
                # 解析开始日期（支持两种格式）
                try:
                    start_date = datetime.strptime(start_date_str, "%Y年%m月%d日")
                except:
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
                
                # 解析考试日期获取年份，基础阶段结束日期为考试年份的6月30日
                try:
                    exam_date = datetime.strptime(exam_date_str, "%Y-%m-%d")
                except:
                    exam_date = datetime.strptime(exam_date_str, "%Y年%m月%d日")
                
                # 基础阶段结束日期为考试年份的6月30日
                base_end_date = datetime(exam_date.year, 6, 30)
                
                # 计算基础阶段总天数
                total_days = (base_end_date - start_date).days
                if total_days <= 0:
                    return {"error": "基础阶段结束日期早于开始日期"}
                    
                total_months = total_days / 30.44  # 平均每月天数
            except Exception as e:
                return {"error": f"日期解析失败: {str(e)}"}

            # 根据总月数确定阶段划分比例
            if total_months > 6:
                ratio = "3:2:4:1"
                stages = [
                    {"name": "早鸟先行", "duration_ratio": 3},
                    {"name": "基础入门", "duration_ratio": 2},
                    {"name": "基础进阶", "duration_ratio": 4},
                    {"name": "知识梳理", "duration_ratio": 1}
                ]
            elif 3 <= total_months <= 6:
                ratio = "3:5:2"
                stages = [
                    {"name": "基础入门", "duration_ratio": 3},
                    {"name": "基础进阶", "duration_ratio": 5},
                    {"name": "知识梳理", "duration_ratio": 2}
                ]
            else:
                ratio = "8:2"
                stages = [
                    {"name": "基础筑基", "duration_ratio": 8},
                    {"name": "知识梳理", "duration_ratio": 2}
                ]

            # 计算每个阶段的时间范围
            total_ratio = sum([stage["duration_ratio"] for stage in stages])
            current_date = start_date
            for stage in stages:
                # 计算该阶段的天数
                stage_days = int((stage["duration_ratio"] / total_ratio) * total_days)
                end_date = current_date + timedelta(days=stage_days)
                stage["start_date"] = current_date.strftime("%Y年%m月%d日")
                stage["end_date"] = end_date.strftime("%Y年%m月%d日")
                current_date = end_date

            return {
                "total_months": total_months,
                "ratio": ratio,
                "stages": stages
            }

    # =================================================================
    # 单科规划智能体 (SubjectPlannerAgent)
    # =================================================================
    class SubjectPlannerAgent:
        def generate_subject_plan(self, subject: str, user_data: Dict[str, Any], macro_plan: Dict[str, Any], feedback: str = "") -> str:
            """为单个科目生成详细学习计划"""
            # 初始化LLM
            llm = ChatOpenAI(
                openai_api_key=settings.DOUBAO_API_KEY,
                openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
                model_name="doubao-seed-1-6-thinking-250615"
            )
            
            # 创建工具
            tools = [
                Tool(
                    name="get_course_package_content",
                    func=TestFoundationService.get_course_package_content,
                    description=f"获取{subject}课程包内容。仅支持以下科目：数学、英语、政治、408计算机"
                )
            ]
            
            # 构建提示
            prompt_content = f"""
角色设定：
你是一位专业的考研备考规划师，负责为{subject}科目制定基础阶段学习计划。

任务：
根据宏观规划的阶段划分和课程包内容，为{subject}科目生成详细的学习计划。

宏观规划阶段划分：
{json.dumps(macro_plan['stages'], indent=2, ensure_ascii=False)}

学生画像：
{user_data.get("初始画像", "")}

要求：
1. 计划必须覆盖宏观规划的每个阶段。
2. 每个阶段内，根据课程包内容安排学习目标、关键动作和重点内容。
3. 针对学生基础薄弱的情况，注重基础概念的理解和巩固。
4. 输出Markdown格式，结构清晰。
5. 必须调用get_course_package_content工具获取课程包内容

反馈意见：
{feedback if feedback else "无"}
            """

            # 创建agent并运行
            agent = initialize_agent(
                tools,
                llm,
                agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION,
                verbose=True,
                handle_parsing_errors=True
            )
            return agent.run(prompt_content)

    # =================================================================
    # 审核智能体 (ReviewerAgent)
    # =================================================================
    class ReviewerAgent:
        def review_plan(self, subject: str, plan_content: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
            """审核单科规划报告"""
            feedback = []
            
            # 1. 检查是否包含基础阶段关键词
            if not any(keyword in plan_content for keyword in ["基础入门", "基础提高", "基础阶段"]):
                feedback.append(f"{subject}规划缺少基础阶段内容，请确保包含'基础入门'、'基础提高'或'基础阶段'等关键词")
            
            # 2. 检查是否分析学生画像
            student_profile = user_data.get("初始画像", "")
            if "基础薄弱" in student_profile and "基础薄弱" not in plan_content:
                feedback.append(f"{subject}规划未提及学生'基础薄弱'的问题")
            if "从零开始" in student_profile and "从零开始" not in plan_content:
                feedback.append(f"{subject}规划未提及学生'从零开始'的问题")
                
            # 3. 检查是否包含课程包内容
            course_content = TestFoundationService.get_course_package_content(subject)
            if course_content:
                for item in course_content:
                    if item["stage_name"] not in plan_content:
                        feedback.append(f"{subject}规划缺少课程包中的阶段: {item['stage_name']}")
            
            if feedback:
                return {
                    "status": "fail",
                    "feedback": "\n".join(feedback)
                }
            else:
                return {"status": "pass", "feedback": ""}

    # =================================================================
    # 统筹智能体 (ConsolidatorAgent)
    # =================================================================
    class ConsolidatorAgent:
        def consolidate_report(self, macro_plan: Dict[str, Any], subject_plans: Dict[str, str]) -> str:
            """整合所有规划为最终报告，按照阶段组织内容"""
            # 初始化LLM
            llm = ChatOpenAI(
                openai_api_key=settings.DOUBAO_API_KEY,
                openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
                model_name="doubao-seed-1-6-thinking-250615",
                temperature=0.3
            )
            
            # 构建提示
            prompt_content = f"""
角色设定：
你是一位考研规划报告总编，负责将各科目规划整合成一份排版优美、重点突出、语言流畅的单一 Markdown 文档。

任务：
根据提供的宏观规划和各科目规划内容，生成最终报告。

宏观规划阶段划分：
{json.dumps(macro_plan['stages'], indent=2, ensure_ascii=False)}

各科目规划内容：
{json.dumps(subject_plans, indent=2, ensure_ascii=False)}

要求：
1. 报告结构：
   - 标题：考研基础阶段学习规划报告
   - 宏观规划概览（简要描述总时长和阶段比例）
   - 按阶段组织的详细规划（核心部分）
   - 执行监控建议

2. 按阶段组织的详细规划格式要求：
   每个阶段按以下格式呈现：
   ```
   [阶段名称]（[开始日期] - [结束日期]）
   核心目标：
   ● [目标1]
   ● [目标2]
   ...

   阶段关键动作：
   ● [动作1]
   ● [动作2]
   ...

   科目 | 学习内容 | 提分重点 | 学习指导
   --- | --- | --- | ---
   [科目1] | [内容1] | [重点1] | [指导1]
   [科目1] | [内容2] | [重点2] | [指导2]
   [科目2] | [内容1] | [重点1] | [指导1]
   ...
   ```

3. 核心目标应综合各科目的核心目标
4. 阶段关键动作应突出该阶段最重要、最具代表性的学习动作
5. 科目表格应包含所有科目在该阶段的学习内容
6. 提分重点应明确该阶段需要掌握的关键知识点
7. 学习指导应提供具体、可操作的学习方法建议
8. 使用简洁专业的语言，避免冗余描述
9. 确保各阶段内容连贯，形成完整的学习路径

执行监控建议：
* 每周检查学习进度，确保各科目均衡发展
* 每月进行模拟测试，评估学习效果
* 根据测试结果动态调整后续学习计划
            """

            # 调用LLM生成报告
            response = llm.invoke([
                ("system", "你是一位专业的考研规划报告编辑专家。"),
                ("user", prompt_content)
            ])
            
            return response.content

    # =================================================================
    # 主工作流
    # =================================================================
    @staticmethod
    def test_llm(user_id):
        # 获取用户数据
        analysis = TestFoundationService.get_student_analysis(user_id)
        if not analysis:
            return "未找到用户分析数据"
        
        # 实例化智能体
        macro_planner = TestFoundationService.MacroPlannerAgent()
        subject_planner = TestFoundationService.SubjectPlannerAgent()
        reviewer = TestFoundationService.ReviewerAgent()
        consolidator = TestFoundationService.ConsolidatorAgent()
        
        # 生成宏观规划
        macro_plan = macro_planner.generate_macro_plan(analysis)
        if "error" in macro_plan:
            return f"宏观规划失败: {macro_plan['error']}"
        
        # 解析考试科目
        exam_subjects = TestFoundationService._parse_exam_subjects(analysis.get("考试科目", "{}"))
        if not exam_subjects:
            return "考试科目解析失败"
        
        # 初始化状态
        state = GraphState(
            user_data=analysis,
            macro_plan=macro_plan,
            subject_plans={},
            reviewer_feedback="",
            status="in_progress",
            current_subject=""
        )
        
        # 为每个科目生成规划并审核
        subject_review_attempts = {}
        for subject in exam_subjects:
            state["current_subject"] = subject
            subject_review_attempts[subject] = 0
            
            # 单科规划与审核循环（最多2次）
            while subject_review_attempts[subject] < 2:
                # 生成单科规划
                plan_content = subject_planner.generate_subject_plan(
                    subject,
                    state["user_data"],
                    state["macro_plan"],
                    state["reviewer_feedback"]
                )
                
                # 审核规划
                review_result = reviewer.review_plan(subject, plan_content, state["user_data"])
                
                if review_result["status"] == "pass":
                    # 审核通过，保存规划
                    state["subject_plans"][subject] = plan_content
                    state["reviewer_feedback"] = ""
                    break
                else:
                    # 审核未通过，记录反馈并重试
                    state["reviewer_feedback"] = review_result["feedback"]
                    subject_review_attempts[subject] += 1
            
            if subject_review_attempts[subject] >= 2:
                # 超过最大尝试次数，使用当前规划
                state["subject_plans"][subject] = plan_content
        
        # 整合最终报告
        final_report = consolidator.consolidate_report(
            state["macro_plan"],
            state["subject_plans"]
        )
        
        return final_report

    @staticmethod
    def _parse_exam_subjects(subjects_str: str) -> List[str]:
        """解析考试科目字符串"""
        try:
            exam_subjects = ast.literal_eval(subjects_str)
            return list(exam_subjects.keys())
        except:
            return []


# 测试代码
if __name__ == "__main__":
    user_id = "fwfafagaxz"
    print(f"开始为用户 {user_id} 生成考研规划报告...")
    result = TestFoundationService.test_llm(user_id=user_id)
    
    if result:
        print("报告生成成功！")
        with open("user_foundational.md", "w", encoding="utf-8") as f:
            f.write(result)
        print(f"报告已保存至: user_foundational.md")
        
        # 在控制台显示报告摘要
        print("\n===== 报告摘要 =====")
        print(result[:500] + "..." if len(result) > 500 else result)
    else:
        print("报告生成失败，请检查错误信息")