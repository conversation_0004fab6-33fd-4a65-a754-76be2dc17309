# Generated by Django 4.2 on 2025-04-23 10:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0049_merge_20250423_1808'),
    ]

    operations = [
        migrations.AddField(
            model_name='englishwaikantestrecord',
            name='exception_image',
            field=models.TextField(null=True, verbose_name='异常图片'),
        ),
        migrations.AddField(
            model_name='englishwaikantestrecord',
            name='exception_reason',
            field=models.TextField(null=True, verbose_name='异常原因'),
        ),
        migrations.AddField(
            model_name='englishwaikantestrecord',
            name='is_exception',
            field=models.BooleanField(default=False, verbose_name='是否异常'),
        ),
        migrations.AddField(
            model_name='englishwordtestanswerdetail',
            name='exception_image',
            field=models.TextField(null=True, verbose_name='异常图片'),
        ),
        migrations.AddField(
            model_name='englishwordtestanswerdetail',
            name='exception_reason',
            field=models.TextField(null=True, verbose_name='异常原因'),
        ),
        migrations.AddField(
            model_name='englishwordtestanswerdetail',
            name='is_exception',
            field=models.BooleanField(default=False, verbose_name='是否异常'),
        ),
        migrations.AddField(
            model_name='enwordrecitebasicanswerdetail',
            name='exception_image',
            field=models.TextField(null=True, verbose_name='异常图片'),
        ),
        migrations.AddField(
            model_name='enwordrecitebasicanswerdetail',
            name='exception_reason',
            field=models.TextField(null=True, verbose_name='异常原因'),
        ),
        migrations.AddField(
            model_name='enwordrecitebasicanswerdetail',
            name='is_exception',
            field=models.BooleanField(default=False, verbose_name='是否异常'),
        ),
    ]
