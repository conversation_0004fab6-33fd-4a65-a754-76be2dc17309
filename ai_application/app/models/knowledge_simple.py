from django.db import models
from ._base import BaseModel

class KnowledgeSimple(BaseModel):
    course_id=models.CharField('课程ID',max_length=40,db_index=True)
    name=models.CharField('名称',max_length=100,blank=True)
    definition=models.TextField('定义',null=True)
    desc = models.TextField('描述',null=True)
    tokens = models.IntegerField('token数',default=0)
    is_usable=models.BooleanField('是否可用',default=False)

    class Meta:
        verbose_name = verbose_name_plural = "知识解析"
        db_table = 'ai_knowledge_simple'