import http.client
import json

API_HOST = "api-dev.yantucs.com"
HEADERS = {
    'x-api-key': 'ak_bc1ea37f68a84d8b',
    'x-signature': '91e9b4e95b65e5fc14013baeb7e5bfe252b1db58071c01b5b19b889fe85ddfdb',
    'timestamp': '1720405843',
    'nonce': 'ld8h259m',
    'x-sign-debug': '1',
    'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Host': API_HOST,
    'Connection': 'keep-alive'
}

def make_request(endpoint, payload):
    conn = http.client.HTTPSConnection(API_HOST)
    conn.request("POST", endpoint, json.dumps(payload), HEADERS)
    res = conn.getresponse()
    data = res.read()
    return json.loads(data.decode("utf-8"))

def get_video_abstract(main_subject, course_section_id, video_content):
    payload = {
        "main_subject": main_subject,
        "course_section_id": course_section_id,
        "video_content": video_content
    }
    response = make_request("/api/zhizhou/v1/math/video_abstract", payload)
    return response["data"]['video_abstract']

def get_knowledge_extract(main_subject, course_section_id, video_abstract):
    payload = {
        "main_subject": main_subject,
        "course_section_id": course_section_id,
        "video_content": video_abstract
    }
    response = make_request("/api/zhizhou/v1/math/video_knowledge_extract", payload)
    return response["data"]['knowledge_list']

def load_json(file_path):
    """加载JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as file:
        return json.load(file)
def concatenate_subtitles(data):
    """将每个course_section_id下的所有subtitles的content字段拼接起来"""
    course_contents = {}
    for item in data:
        course_section_id = item["course_section_id"]
        full_content = ' '.join([subtitle['content'] for subtitle in item['subtitles']])
        course_contents[course_section_id] = full_content
    return course_contents

def get_knowledge_list(math_video_content,main_subject):

    # 拼接内容
    concatenated_contents = concatenate_subtitles(math_video_content)

    # 主题和课程部分ID
    main_subject = main_subject

    # 存储所有知识点的列表
    all_knowledge_lists = []

    # 更新数据
    for item in math_video_content:
        course_section_id = item["course_section_id"]
        video_content = concatenated_contents[course_section_id]
        item['video_content'] = video_content

        # 获取视频摘要
        video_abstract = get_video_abstract(main_subject, course_section_id, video_content)
        print(f"Course Section ID: {course_section_id}")
        print(f"Video Abstract: {video_abstract}")

        # 获取知识点提取
        knowledge_list = get_knowledge_extract(main_subject, course_section_id, video_abstract)
        print(f"Knowledge List: {knowledge_list}")
        all_knowledge_lists.append(knowledge_list)

    # 返回所有知识点列表
    return all_knowledge_lists

