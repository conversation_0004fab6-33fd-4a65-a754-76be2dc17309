import json
import time

from django.conf import settings
from django.db import transaction
from django.views.generic import TemplateView
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate

from app.api.api_dto.ai_practice import AIPracticePaperSubmitDto
from app.core.langchain_openai_thinking import Chat<PERSON><PERSON><PERSON><PERSON>hinking
from app.errors import ParameterError
from app.models import STUserPaper, PromptTemplate, PromptTemplateChangeLog, STUserPaperDistribution
from app.services.shuati_app.paper_detail import get_ai_practice_paper_detail
from app.services.shuati_app.paper_submit import PaperSubmitClient, format_user_desc_to_text
from app.services.shuati_app.user_knowledge_mastery import get_user_mastery_level
from django_ext.base_view import BaseView
from django_ext.response import make_response, make_stream_response


class STPaperIndexView(TemplateView):
    template_name = 'app/shuati/paper_index.html'

    def get_context_data(self, **kwargs):
        user_id = self.request.GET.get('user_id','')
        context = super().get_context_data(**kwargs)
        prompt = PromptTemplate.objects.get(app_no='first_shuati_paper_dist')
        context['first_dist_prompt'] = prompt.prompt_content
        context['user_id'] = user_id
        return context


class STPaperReportView(TemplateView):
    template_name = 'app/shuati/paper_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_id = self.request.GET.get('user_id', '')
        prompt = PromptTemplate.objects.get(app_no='shuati_answer_report')
        context['shuati_report_prompt'] = prompt.prompt_content
        context['user_id'] = user_id
        return context


class STPaperDetailView(BaseView):

    def get(self, request, *args, **kwargs):
        user_id = request.query_params.get('user_id')
        if not user_id:
            raise ParameterError(detail='用户ID不能为空')

        subject_id = 'TcT7x2dUZfG7BPyAeRNGFM'
        # core_course_code = 'CC_SJJG'
        # core_course_code = 'CC_JSXW'
        core_course_code = 'CC_CZXT'
        # core_course_code = 'CC_JSZZYL'
        paper_detail = get_ai_practice_paper_detail(subject_id, core_course_code, user_id)
        data = []
        choice_map = ["A", "B", "C", "D", "E", "F"]
        for q in paper_detail['question_list']:
            choices = [{
                'id': choice_map[c_idx],
                'body': c
            } for c_idx, c in enumerate(q['options'])]
            data.append({
                'id': q['question_id'],
                'question_type': q['question_type'],
                'difficulty': q['difficulty'],
                'title': q['question'],
                'choices': choices,
                'right_answer': ''.join(q.get('choice_answer', [])),
                'knowledge_list': [k['knowledge_name'] for k in q['knowledge_list']],
            })

        return make_response({
            'user_id': user_id,
            'paper_id': paper_detail['paper_id'],
            'stage_info': paper_detail['stage_info'],
            'questions': data
        })


class STPaperSubmitView(BaseView):
    def post(self, request, *args, **kwargs):
        paper_id = request.data.get('paper_id')
        paper = STUserPaper.objects.filter(id=paper_id).first()
        if not paper:
            raise ParameterError(detail_err='试卷不存在')

        answers = request.data.get('answers')
        answer_detail = []
        for a in answers:
            if a['question_type'] in [0, 1]:
                answer_detail.append({
                    'question_id': a['question_id'],
                    'choice_answer': [a['answer']]
                })
            else:
                answer_detail.append({
                    'question_id': a['question_id'],
                    'subjective_answer': {
                        'images': a['image_urls'],
                        'text': a['answer'],
                    }
                })

        dto = AIPracticePaperSubmitDto(**{
            'paper_id': paper.id,
            'user_id': paper.user_id,
            'answer_detail': answer_detail
        })

        client = PaperSubmitClient(paper, is_debug=True)
        paper_answer = client.submit(dto)

        user_desc = get_user_mastery_level(
            subject_id=paper.subject_id,
            core_course_code=paper.core_course_code,
            user_id=paper.user_id,
        )
        formatted_user_desc = format_user_desc_to_text(user_desc)

        paper_answer.refresh_from_db()
        return make_response({
            'report_result': paper_answer.report,
            'user_desc': formatted_user_desc
        })


class STPaperDistDebugView(TemplateView):
    template_name = 'app/shuati/st_paper_dist_debug.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        prompt1 = PromptTemplate.objects.get(app_no='shuati_paper_dist')
        prompt2 = PromptTemplate.objects.get(app_no='shuati_paper_dist_param')
        context['dist_prompt'] = prompt1.prompt_content
        context['dist_param_prompt'] = prompt2.prompt_content
        return context


class STPaperDistGenView(BaseView):
    def post(self, request, *args, **kwargs):
        model_id = request.data.get('model_id', 'doubao-1-5-pro-256k-250115')
        prompt_text = request.data.get('prompt')
        if not prompt_text:
            raise ParameterError(detail='请输入提示文本')
        input_text = request.data.get('input_text')
        if not input_text:
            raise ParameterError(detail='请输入知识点掌握情况')

        llm = ChatOpenAIThinking(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base=settings.DOUBAO_API_BASE,
            model_name=model_id,
            temperature=0.3,
            model_kwargs={
                'stream_options': {"include_usage": True}
            }
        )

        prompt_messages = [
            SystemMessage(content=prompt_text),
            HumanMessage(content=input_text)
        ]

        def _gen_resp(response):
            for chunk in response:
                if hasattr(chunk, "usage_metadata") and chunk.usage_metadata:
                    usage = chunk.usage_metadata
                    chunk_dict_str = json.dumps({
                        "event": "message_end",
                        "created_at": int(time.time()),
                        "metadata": {
                            "message_tokens": usage.get('input_tokens'),
                            "answer_tokens": usage.get('output_tokens'),
                            "total_tokens": usage.get('total_tokens'),
                            "characters": 0,
                            "response_latency": 0,
                        },
                    }, ensure_ascii=False)
                    yield f'data: {chunk_dict_str}\n\n'
                else:
                    chunk_dict_str = json.dumps({
                        "event": "message",
                        "created_at": int(time.time()),
                        "answer": chunk.content,
                        "reasoning_content": '',
                    }, ensure_ascii=False)
                    yield f'data: {chunk_dict_str}\n\n'

        response = llm.stream(prompt_messages)
        return make_stream_response(_gen_resp(response))


class STPaperDistParamGenView(BaseView):
    def post(self, request, *args, **kwargs):
        model_id = request.data.get('model_id', 'doubao-1-5-pro-32k-250115')
        prompt_text = request.data.get('prompt')
        user_id = request.data.get('user_id')
        print(f"user_id🔥:{user_id}")
        if not prompt_text:
            raise ParameterError(detail='请输入提示文本')
        input_text = request.data.get('input_text')
        if not input_text:
            raise ParameterError(detail='请输入出题策略')

        llm = ChatOpenAIThinking(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base=settings.DOUBAO_API_BASE,
            model_name=model_id,
            temperature=0.3,
        )

        prompt_messages = [
            SystemMessage(content=prompt_text),
            HumanMessage(content=input_text)
        ]
        prompt = ChatPromptTemplate.from_messages(prompt_messages)
        chain = prompt | llm
        res = chain.invoke({})
        paper_dist= res.content
        # 解析LLM返回的JSON字符串为Python对象再保存
        try:
            paper_dist_json = json.loads(paper_dist)
        except json.JSONDecodeError:
            raise ParameterError(detail='生成的试卷分布格式不正确')
        print(f"paper_dist在这边啊啊啊啊啊🔥:{paper_dist}")
        STUserPaperDistribution.objects.create(
            subject_id='TcT7x2dUZfG7BPyAeRNGFM',
            core_course_code='CC_SJJG',
            user_id=user_id,
            paper_distribution=paper_dist_json)

        return make_response({
            'content': res.content
        })


class STPaperDistPromptSaveView(BaseView):
    def post(self, request, *args, **kwargs):
        app_no = request.data.get('app_no')
        prompt_content = request.data.get('prompt_content')
        if not app_no or not prompt_content:
            raise ParameterError(detail='参数错误')

        prompt_template = PromptTemplate.objects.get(app_no=app_no)

        with transaction.atomic():
            prompt_template.prompt_content = prompt_content
            prompt_template.save(update_fields=['prompt_content'])
            PromptTemplateChangeLog.objects.create(
                prompt_template=prompt_template,
                is_debug=False,
                prompt_content=prompt_content
            )
