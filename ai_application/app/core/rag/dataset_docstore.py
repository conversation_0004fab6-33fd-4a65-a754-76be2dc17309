from typing import Sequence

from app.core.rag.models.document import Document
from app.models import Dataset, DatasetDocument, DocumentSegment


class DatasetDocumentStore:
    def __init__(
            self,
            dataset: Dataset,
            dataset_document: DatasetDocument,
    ):
        self._dataset = dataset
        self._dataset_document = dataset_document

    @classmethod
    def from_dict(cls, config_dict: dict[str, any]) -> "DatasetDocumentStore":
        return cls(**config_dict)

    def to_dict(self) -> dict[str, any]:
        """Serialize to dict."""
        return {
            "dataset_no": self._dataset.dataset_no,
        }

    @property
    def dateset_no(self) -> str:
        return self._dataset.dataset_no

    @property
    def docs(self) -> dict[str, Document]:
        document_segments = self._dataset.documentsegment_set.filter(is_deleted=False).all()

        output = {}
        for document_segment in document_segments:
            doc_id = document_segment.index_node_id
            output[doc_id] = Document(
                page_content=document_segment.content,
                metadata={
                    "doc_id": document_segment.index_node_id,
                    "doc_hash": document_segment.index_node_hash,
                    "document_no": document_segment.dataset_document.document_no,
                    "dataset_no": document_segment.dataset.dataset_no,
                }
            )

        return output

    def add_documents(
            self, docs: Sequence[Document], allow_update: bool = True
    ) -> None:
        max_position_segment: DocumentSegment = self._dataset_document.documentsegment_set.all().order_by('-position').first()
        max_position = max_position_segment.position if max_position_segment else 0

        exist_segments = DocumentSegment.objects.filter(
            dataset=self._dataset, index_node_id__in=[doc.metadata['doc_id'] for doc in docs]
        )
        exist_segments_map = {seg.index_node_id: seg for seg in exist_segments}

        add_segments = []
        for doc in docs:
            if not isinstance(doc, Document):
                raise ValueError("doc must be a Document")

            doc_id = doc.metadata['doc_id']
            segment_document = exist_segments_map.get(doc_id)

            # NOTE: doc could already exist in the store, but we overwrite it
            if not allow_update and segment_document:
                raise ValueError(
                    f"doc_id {doc.metadata['doc_id']} already exists. "
                    "Set allow_update to True to overwrite."
                )

            if not segment_document:
                max_position += 1
                add_segments.append(DocumentSegment(
                    dataset=self._dataset,
                    dataset_document=self._dataset_document,
                    index_node_id=doc.metadata['doc_id'],
                    index_node_hash=doc.metadata['doc_hash'],
                    position=max_position,
                    content=doc.page_content,
                    word_count=len(doc.page_content),
                ))
            else:
                segment_document.content = doc.page_content
                segment_document.index_node_hash = doc.metadata['doc_hash']
                segment_document.word_count = len(doc.page_content)
                segment_document.save()
        if add_segments:
            DocumentSegment.objects.bulk_create(add_segments)

    def document_exists(self, doc_id: str) -> bool:
        """Check if document exists."""
        result = self.get_document_segment(doc_id)
        return result is not None

    def get_document(
            self, doc_id: str, raise_error: bool = True
    ) -> Document | None:
        document_segment = self.get_document_segment(doc_id)

        if document_segment is None:
            if raise_error:
                raise ValueError(f"doc_id {doc_id} not found.")
            else:
                return None

        return Document(
            page_content=document_segment.content,
            metadata={
                "doc_id": document_segment.index_node_id,
                "doc_hash": document_segment.index_node_hash,
                "document_no": document_segment.dataset_document.document_no,
                "dataset_no": document_segment.dataset.dataset_no,
            }
        )

    def delete_document(self, doc_id: str, raise_error: bool = True) -> None:
        document_segment = self.get_document_segment(doc_id)

        if document_segment is None:
            if raise_error:
                raise ValueError(f"doc_id {doc_id} not found.")
            else:
                return None

        document_segment.soft_delete(is_save=True)

    def set_document_hash(self, doc_id: str, doc_hash: str) -> None:
        """Set the hash for a given doc_id."""
        document_segment = self.get_document_segment(doc_id)

        if document_segment is None:
            return None

        document_segment.index_node_hash = doc_hash
        document_segment.save(update_fields=['index_node_hash'])

    def get_document_hash(self, doc_id: str) -> str | None:
        """Get the stored hash for a document, if it exists."""
        document_segment = self.get_document_segment(doc_id)

        if document_segment is None:
            return None

        return document_segment.index_node_hash

    def get_document_segment(self, doc_id: str) -> DocumentSegment:
        document_segment = DocumentSegment.objects.filter(
            dataset=self._dataset, index_node_id=doc_id
        ).first()
        return document_segment
