import pandas as pd
import numpy as np
import os
import django


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()
from app.models import ExamAnalysisKnowledgePointWithStats

# --- 1. 数据模拟 ---
# 在实际使用中，请将这部分替换为您自己的数据加载过程
# 例如: knowledge_points_df = pd.read_csv('your_data.csv')
def load_sample_data():
    """
    创建一个模拟的知识点数据 DataFrame，结构与您的描述一致。
    """
    data = {
        'subject': [],
        'knowledge_point': [],
        'total_freq': [],
        'avg_difficulty': [],
        'mcq_freq': [],
        'mcq_difficulty': [],
        'subjective_freq': [],
        'subjective_difficulty': []
    }

    # 模拟数据
    subjects = ['计算机组成原理', '计算机网络', '操作系统', '数据结构']
    for subject in subjects:
        queryset = ExamAnalysisKnowledgePointWithStats.objects.filter(subject=subject).values()
        for item in queryset:
            data['subject'].append(item["subject"])
            data['knowledge_point'].append(item["point_name"])
            data['total_freq'].append(item["exam_count"])
            data['avg_difficulty'].append(item["avg_difficulty"])
            data['mcq_freq'].append(item["choice_count"])
            data['mcq_difficulty'].append(item["choice_avg_difficulty"])
            data['subjective_freq'].append(item["comprehensive_count"])
            data['subjective_difficulty'].append(item["comprehensive_avg_difficulty"])

    return pd.DataFrame(data)

# --- 2. 核心算法实现 ---
class ScoreAllocator:
    def __init__(self, knowledge_points_df, subject_max_scores, total_possible_score, alpha=0.5, beta=0.5):
        """
        初始化分数分配器
        :param knowledge_points_df: 包含知识点数据的DataFrame
        :param subject_max_scores: 包含各科目满分的字典
        :param total_possible_score: 考试总分
        :param alpha: 重要性系数中，分数占比的权重
        :param beta: 难度系数中，知识平均难度的权重
        """
        self.knowledge_points_df = knowledge_points_df
        self.subject_max_scores = subject_max_scores
        self.total_possible_score = total_possible_score
        self.alpha = alpha
        self.beta = beta
        self.subject_metrics = None

    def _aggregate_to_subjects(self):
        """
        [私有方法] 将知识点数据聚合到科目维度
        """
        # 定义加权平均函数
        def weighted_avg(group, avg_col, weight_col):
            d = group[avg_col]
            w = group[weight_col]
            # 处理分母为0的情况
            if w.sum() == 0:
                return 0
            return (d * w).sum() / w.sum()

        # 按科目分组
        grouped = self.knowledge_points_df.groupby('subject')

        # 计算每个科目的宏观指标
        aggregated_data = {
            'total_freq': grouped['total_freq'].sum(),
            'weighted_avg_difficulty': grouped.apply(weighted_avg, 'avg_difficulty', 'total_freq'),
            'subjective_freq_ratio': grouped['subjective_freq'].sum() / grouped['total_freq'].sum()
        }

        self.subject_metrics = pd.DataFrame(aggregated_data)
        # 补充科目满分信息
        self.subject_metrics['max_score'] = self.subject_metrics.index.map(self.subject_max_scores)
        
        # 处理NaN（如果某个科目没有主观题，ratio会是NaN，填充为0）
        self.subject_metrics.fillna(0, inplace=True)


    def _calculate_coefficients(self):
        """
        [私有方法] 计算每个科目的重要性、难度和优先度系数
        """
        if self.subject_metrics is None:
            self._aggregate_to_subjects()

        # 1. 计算重要性系数 (W)
        score_ratio = self.subject_metrics['max_score'] / self.total_possible_score
        freq_ratio = self.subject_metrics['total_freq'] / self.subject_metrics['total_freq'].sum()
        self.subject_metrics['importance_W'] = self.alpha * score_ratio + (1 - self.alpha) * freq_ratio

        # 2. 计算难度系数 (D)
        # 将难度归一化到0-1，避免量纲问题
        norm_difficulty = self.subject_metrics['weighted_avg_difficulty']
        norm_subjective_ratio = self.subject_metrics['subjective_freq_ratio']
        self.subject_metrics['difficulty_D'] = self.beta * norm_difficulty + (1 - self.beta) * norm_subjective_ratio
        
        # 3. 计算备考优先度系数 (P)
        # 为避免除以0，给难度系数D加上一个极小值
        self.subject_metrics['priority_P'] = self.subject_metrics['importance_W'] / (self.subject_metrics['difficulty_D'] + 1e-6)

    def allocate_scores(self, target_total_score):
        """
        根据计算出的系数，为给定的目标分数进行分配
        :param target_total_score: 你的目标总分
        :return: 一个包含最终分数分配的DataFrame
        """
        # 确保系数已经计算
        if self.subject_metrics is None or 'priority_P' not in self.subject_metrics.columns:
            self._calculate_coefficients()

        # 初始化分配
        df = self.subject_metrics.copy()
        df['allocated_score'] = 0.0

        # 开始迭代分配
        remaining_score = target_total_score
        
        while True:
            # 筛选出分数还未达到上限的科目
            eligible_subjects = df[df['allocated_score'] < df['max_score']]
            
            if eligible_subjects.empty or remaining_score <= 0:
                break

            # 计算当前合格科目的优先度总和
            current_priority_sum = eligible_subjects['priority_P'].sum()
            if current_priority_sum == 0:
                # 如果所有剩余科目优先度都为0，则平均分配
                avg_share = remaining_score / len(eligible_subjects)
                df.loc[eligible_subjects.index, 'allocated_score'] += avg_share
                break

            # 按优先度比例分配剩余分数
            df['score_to_add'] = 0.0
            df.loc[eligible_subjects.index, 'score_to_add'] = remaining_score * (df['priority_P'] / current_priority_sum)
            
            df['allocated_score'] += df['score_to_add']
            
            # 检查是否有科目分数溢出
            overflow_subjects = df[df['allocated_score'] > df['max_score']]
            if overflow_subjects.empty:
                # 如果没有溢出，则分配完成
                break
            else:
                # 如果有溢出，重置分数，将溢出的科目分数设为其满分，然后将超出的部分重新分配
                excess_score = (df['allocated_score'] - df['max_score'])[overflow_subjects.index].sum()
                df.loc[overflow_subjects.index, 'allocated_score'] = df['max_score']
                remaining_score = excess_score

        # 最后处理浮点数和取整，确保总分正确
        df['final_score'] = np.round(df['allocated_score'])
        score_diff = target_total_score - df['final_score'].sum()
        
        # 将四舍五入的误差加到优先度最高的科目上
        if score_diff != 0:
            highest_priority_subject = df['priority_P'].idxmax()
            df.loc[highest_priority_subject, 'final_score'] += score_diff

        return df[['max_score', 'importance_W', 'difficulty_D', 'priority_P', 'final_score']]

# --- 3. 执行与展示 ---
if __name__ == '__main__':
    # --- 用户配置 ---
    SUBJECT_MAX_SCORES = {
        '计算机组成原理': 45,
        '计算机网络': 25,
        '操作系统': 35,
        '数据结构': 45
    }
    TOTAL_POSSIBLE_SCORE = 150
    TARGET_SCORE = 114
    
    # 重要性(W)和难度(D)的内部权重，可以根据你的理解进行调整
    # alpha: 分数占比在“重要性”中的权重
    # beta: 知识难度在“难度”中的权重
    ALPHA_WEIGHT = 0.6  # 调高alpha意味着更看重官方分数安排
    BETA_WEIGHT = 0.7   # 调高beta意味着你认为知识点本身的难度比主观题更关键

    # --- 执行流程 ---
    # 1. 加载数据
    knowledge_data = load_sample_data()
    
    # 2. 初始化分配器
    allocator = ScoreAllocator(
        knowledge_points_df=knowledge_data,
        subject_max_scores=SUBJECT_MAX_SCORES,
        total_possible_score=TOTAL_POSSIBLE_SCORE,
        alpha=ALPHA_WEIGHT,
        beta=BETA_WEIGHT
    )
    
    # 3. 计算并分配分数
    final_allocation = allocator.allocate_scores(TARGET_SCORE)

    # --- 结果展示 ---
    print("="*50)
    print("各科目核心系数分析")
    print("="*50)
    print(final_allocation[['importance_W', 'difficulty_D', 'priority_P']].round(3))
    print("\n说明:")
    print(" - importance_W: 重要性系数。综合了分数占比和历史考频。")
    print(" - difficulty_D: 难度系数。综合了知识点平均难度和主观题比例。")
    print(" - priority_P: 备考优先度(性价比)。越高越值得优先投入精力。\n")
    
    print("="*50)
    print(f"目标总分 {TARGET_SCORE} 的最优分数分配建议")
    print("="*50)
    print(final_allocation[['max_score', 'final_score']].astype(int))
    print("-"*50)
    print(f"验证总分: {final_allocation['final_score'].sum()}")