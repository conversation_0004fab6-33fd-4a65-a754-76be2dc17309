# Generated by Django 4.2 on 2024-10-29 02:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0005_sensitiveconfig'),
    ]

    operations = [
        migrations.AddField(
            model_name='appmodelconfig',
            name='prompt_templates',
            field=models.JSONField(null=True, verbose_name='适用提示词模版'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='document_nos',
            field=models.JSONField(null=True, verbose_name='文档编号列表'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='from_biz_id',
            field=models.CharField(blank=True, max_length=32, verbose_name='来源业务id'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='invoke_from',
            field=models.CharField(choices=[('api', '接口调用'), ('console', '后台调用')], default='api', max_length=32, verbose_name='调用来源'),
        ),
        migrations.AddField(
            model_name='message',
            name='from_biz_id',
            field=models.CharField(blank=True, max_length=32, verbose_name='来源业务id'),
        ),
        migrations.AddField(
            model_name='message',
            name='invoke_from',
            field=models.CharField(choices=[('api', '接口调用'), ('console', '后台调用')], default='api', max_length=32, verbose_name='调用来源'),
        ),
        migrations.AddField(
            model_name='prompttemplate',
            name='custom_variables',
            field=models.JSONField(null=True, verbose_name='自定义变量'),
        ),
        migrations.AddField(
            model_name='prompttemplate',
            name='special_variables',
            field=models.JSONField(null=True, verbose_name='特殊变量'),
        ),
    ]
