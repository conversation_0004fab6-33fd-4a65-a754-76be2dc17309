import math
import re

from app.api.dto import ChatMessageDto
from app.models import InvokeFrom, Account, PromptTemplate
from app.services.app_generate_service import AppGenerateService


class DocumentSummaryService:

    @classmethod
    def get_extraction(
            cls,
            query: str,
            account: Account,
            invoke_from=InvokeFrom.api.value,
            is_async: bool = False,
    ):
        query = cls.remove_table_style(query)
        # query 去除图片和超链接
        query = cls.remove_hyperlinks(query)
        query = cls.remove_images(query)

        dto = ChatMessageDto(
            app_id='document_extraction',
            query=query,
            stream=False,
            is_async=is_async,
        )
        return AppGenerateService.generate(dto, account, invoke_from=invoke_from)

    @classmethod
    def get_summary(
            cls,
            query: str,
            style: str,
            account: Account,
            invoke_from=InvokeFrom.api.value,
            is_async: bool = False,
    ) -> dict:
        query = cls.remove_table_style(query)
        # query 去除图片和超链接
        query = cls.remove_hyperlinks(query)
        query = cls.remove_images(query)

        if style == 'edu':
            template = PromptTemplate.objects.filter(app_no='document_summary_edu').first()
        else:
            template = PromptTemplate.objects.filter(app_no='document_summary_sale').first()
        if not template:
            raise ValueError('summary template not found')

        dto = ChatMessageDto(
            app_id='document_summary',
            inputs={
                'prompt_template': template.id
            },
            query=query,
            stream=False,
            is_async=is_async,
        )
        return AppGenerateService.generate(dto, account, invoke_from=invoke_from)

    @classmethod
    def remove_table_style(cls, content: str) -> str:
        content = content.strip()
        table_pattern = re.compile(r'<table[^>]*?>(.*?)</table>', re.DOTALL)
        matches = table_pattern.findall(content)

        for match in matches:
            old_table_str = match.strip()
            td_content_pattern = re.compile(r'<td[^>]*>(.*?)</td>')
            td_matches = td_content_pattern.findall(old_table_str)
            new_table_str = ''
            for td_match in td_matches:
                new_table_str += td_match.strip() + '\n'
            content = content.replace(old_table_str, new_table_str)

        content = re.sub(r'<table[^>]*>', '', content)
        content = content.replace('</table>', '')

        return content

    @classmethod
    def remove_hyperlinks(cls, content: str) -> str:
        """Get a dictionary of a markdown file from its path."""
        pattern = r"\[(.*?)\]\((.*?)\)"
        content = re.sub(pattern, r"\1", content)
        return content

    @classmethod
    def remove_images(cls, content: str) -> str:
        """Get a dictionary of a markdown file from its path."""
        pattern = r"!{1}\[\[(.*)\]\]"
        content = re.sub(pattern, "", content)
        return content
