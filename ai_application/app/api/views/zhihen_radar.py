from app.api.validators import ZhihenRadarValidator
from app.services.zhihenRadar_service import zhihenRadarService
from django_ext import base_view
from app.api.dto import ZhihenRadarDto
from django_ext.response import make_response
from app.models import zhihenRadar


class ZhihenRadarView(base_view.BaseView):

    validator_class = ZhihenRadarValidator

    def post(self, request, *args, **kwargs):
        dto: ZhihenRadarDto = self.validate_request(request.data)

        # 保存信息
        new_message = zhihenRadar.objects.create(
            assistant_id=dto.assistant_id,
            subject_id = dto.subject_id,
            question_id = dto.question_id,
            question = dto.question,
            answer = dto.answer,
            answer_id = dto.answer_id,
        )

        # 计算一分钟多少字，判断是否超出限制
        answer = dto.answer
        len_answer = len(answer)
        speed = len_answer / (int(dto.answer_duration) / 60) if dto.answer_duration else 999
        if speed > 80:
            new_message.status = 1
            new_message.explain = '回复速度异常！'
            new_message.save()
            res = {
                "status": new_message.status,
                "explain": new_message.explain,
                "analysis": '回复速度异常'
            }
            return make_response(res)
        else:
            result = zhihenRadarService.generate(dto, request.user)
            # 切分，从分析文本中获得status
            lines = result['answer'].split('\n')
            class_value = " "
            for line in lines:
                if '**class**' in line:
                    class_value = line.split('：')[-1].strip()
                    break

            if class_value == '纯人工生成':
                new_message.status = 0
                new_message.analysis = "纯人工生成"
            elif class_value == '纯AI生成':
                new_message.status = 1
                new_message.analysis = "纯AI生成"
            elif class_value == 'AI+人工生成':
                new_message.status = 1
                new_message.analysis = "AI+人工生成"
            elif class_value == '课后习题答案':
                new_message.status = 1
                new_message.analysis = "课后习题答案"
            else:
                new_message.status = 2

            new_message.explain = result['answer']
            new_message.save()
            res = {
                "status": new_message.status,
                "explain": result['answer'],
                "analysis": new_message.analysis
            }

            return make_response(res)

