# https://console.volcengine.com/ark/region:ark+cn-beijing/model/detail?Id=doubao-1-5-pro-256k
model: doubao-1-5-pro-256k-250115
label: doubao-1-5-pro-256k-250115
model_type: llm
model_properties:
  context_size: 40000
  max_input_size: 40000
parameter_rules:
  - name: temperature
    label: 温度
    use_template: temperature
    type: float
    default: 0.3
    min: 0.0
    max: 1.0
    step: 0.1
    help: 用于控制随机性和多样性的程度。较高的值会使得生成结果更加多样化；而较低的值则会使得生成结果更加确定。
  - name: max_tokens
    label: 最大标记
    use_template: max_tokens
    type: int
    default: 4096
    min: 1
    max: 12000
    step: 1
    help: 用于指定模型在生成内容时token的最大数量，它定义了生成的上限，但不保证每次都会生成到这个数量。
  - name: top_p
    use_template: top_p
    type: float
    default: 0.7
    min: 0
    max: 1
    step: 0.1
    help: 用于
