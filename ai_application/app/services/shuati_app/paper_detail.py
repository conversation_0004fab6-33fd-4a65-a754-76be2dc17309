from collections import defaultdict

from app.models import <PERSON><PERSON><PERSON>ion, STQuestionKnowledge, STKnowledge, STUserPaper
from app.services.shuati_app.constants import get_paper_stage_by_learning_stage, LearningStage
from app.services.shuati_app.paper_composing import PaperComposingClient
from app.services.shuati_app.subject_strategy import get_subject_change_required


def get_ai_practice_paper_detail(subject_id: str, core_course_code: str, user_id: str):
    paper = PaperComposingClient(subject_id, core_course_code, user_id).get_paper()

    stage_required_map = get_subject_change_required(subject_id, core_course_code)
    modi_rounds = stage_required_map[LearningStage.MODI].rounds_required
    first_round_tips = (f'<div class="guide-text1">为了更精准地匹配适合你的练习难度，我们需要先进行【{modi_rounds} 轮摸底练习】，'
         '完成后将为你定制专属学习路径~</div><div class="guide-text2"> 现在就开始第一轮摸底吧！</div>')

    question_list = []
    if paper:
        question_list = get_paper_questions(paper)

    if not question_list:
        return {'has_paper': False, 'paper_id': None, 'stage_info': None, 'question_list': []}

    return {
        'has_paper': True,
        'first_round_tips': first_round_tips if paper.total_round <= 1 else '',
        'paper_id': paper.id,
        'stage_info': get_paper_stage_info(paper),
        'question_list': question_list,
    }


def get_paper_questions(paper: STUserPaper) -> list:
    question_ids = paper.get_question_ids()
    if not question_ids:
        return []

    q_kg_qs = STQuestionKnowledge.objects.filter(is_deleted=False, question_id__in=question_ids)
    q_kgs_map = defaultdict(list)
    q_kg_ids = []
    for i in q_kg_qs:
        q_kg_ids.append(i.knowledge_id)
        q_kgs_map[i.question_id].append(i.knowledge_id)
    kgs_map = {}
    if q_kg_ids:
        kgs = STKnowledge.objects.filter(id__in=q_kg_ids)
        kgs_map = {i.id: i for i in kgs}

    question_list = []
    q_qs = STQuestion.objects.filter(id__in=question_ids)
    q_map = {i.id: i for i in q_qs}

    # 按照question_ids顺序
    for question_id in question_ids:
        q = q_map.get(question_id)
        if not q:
            continue

        kg_list = []
        kg_ids = q_kgs_map.get(q.id, [])
        for kg_id in kg_ids:
            kg: STKnowledge = kgs_map.get(kg_id)
            kg_list.append({
                'knowledge_id': kg.id,
                'knowledge_name': kg.name,
                'related_video_url': kg.related_video_url,
            })

        q_dto = q.get_format_question_content()
        question_list.append({
            'question_id': q.id,
            'question_type': q.question_type,
            'difficulty': q.difficulty,
            'question': q_dto.title,
            'options': q_dto.choices,
            'choice_answer': q_dto.choices_answer,
            'analysis': q.analysis,
            'knowledge_list': kg_list
        })

    return question_list


def get_paper_stage_info(paper: STUserPaper) -> dict:
    stage = get_paper_stage_by_learning_stage(paper.learning_stage)

    # 计算总轮数
    if paper.learning_stage == LearningStage.MODI:
        # num_name = {1: '第一轮', 2: '第二轮', 3: '第三轮'}.get(paper.stage_round, '')
        stage_name = '摸底测试'
        full_stage_name = f'{stage_name} 第{paper.stage_round}轮, 共{paper.stage_total_rounds}轮'
    else:
        stage_name = paper.get_learning_stage_display()
        full_stage_name = stage_name
        if paper.is_stage_strengthen:
            full_stage_name = f'{stage_name}-阶段强化子阶段'
        full_stage_name = f'{full_stage_name}, 第{paper.stage_round}轮, 共{paper.stage_total_rounds}轮'

    stage_info = {
        'stage': stage,
        'round': paper.stage_round,
        'total_rounds': paper.stage_total_rounds,
        'stage_name': stage_name,
        'full_stage_name': full_stage_name,
    }
    return stage_info



