<!-- app/note_detail.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记详情</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/contrib/auto-render.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h1 {
            color: #333;
        }
        .note-content {
            background-color: #f9f9f9;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin-top: 20px;
            font-size: 16px;
            color: #fff;
            background-color: #007bff;
            text-decoration: none;
            border-radius: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>笔记详情</h1>
    <p><strong>ID:</strong> {{ note_detail.id }}</p>


    <h2>完整笔记内容</h2>
    <div id="rendered-note-content" class="note-content">{{ full_note_content | safe }}</div>

    <a href="{% url 'note_generator_tasks' %}" class="btn">返回任务列表</a>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const rawContent = "{{ full_note_content | escapejs }}";
            const renderedContent = marked.parse(rawContent);
            document.getElementById('rendered-note-content').innerHTML = renderedContent;

            // 渲染数学公式
            renderMathInElement(document.getElementById('rendered-note-content'), {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "$", right: "$", display: false},
                    {left: "\\(", right: "\\)", display: false},
                    {left: "\\[", right: "\\]", display: true}
                ],
                throwOnError : false
            });
        });
    </script>
</body>
</html>
