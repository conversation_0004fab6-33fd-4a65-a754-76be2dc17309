import datetime
import logging

from celery import shared_task
from django.conf import settings
from django.utils import timezone

from app.core.rag.index_processor.constant.index_type import IndexType
from app.models import DatasetDocument
from app.tasks.clean_document_task import clean_document_task
from app.tasks.document_indexing_task import document_indexing_task

logger = logging.getLogger(__name__)


@shared_task(queue='dataset')
def auto_check_document_overtime_task():
    logger.info('Start auto_check_document_overtime_task...')

    # 如果5分钟后仍未开始解析，则重试
    timeout = timezone.now() - datetime.timedelta(seconds=5*60)
    qs1 = DatasetDocument.objects.filter(
        is_deleted=False,
        processing_started_at__lt=timeout,
        indexing_status__in=['parsing']
    )
    for doc in qs1:
        document_indexing_task.delay(doc.dataset.dataset_no, [doc.document_no])

    # 30分钟后仍处理完成，则任务解析失败
    timeout = timezone.now() - datetime.timedelta(seconds=settings.RAG_DOCUMENT_OVERTIME)
    qs = DatasetDocument.objects.filter(
        is_deleted=False,
        processing_started_at__lt=timeout,
        indexing_status__in=['parsing', 'splitting', 'indexing']
    )
    for doc in qs:
        clean_document_task.delay(
            doc.dataset.dataset_no,
            [doc.document_no],
            index_type=IndexType.PARAGRAPH_INDEX.value
        )

        doc.indexing_status = 'error'
        doc.error = '文档处理超时'
        doc.save(update_fields=['indexing_status', 'error'])
