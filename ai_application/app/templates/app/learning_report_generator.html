<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学测试报告生成助手</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.css" rel="stylesheet">
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/contrib/auto-render.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background-color: #f6f8fa;
        }

        .container {
            max-width: 1500px;
            width: 100%;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        label {
            font-weight: bold;
            margin-top: 15px;
        }

        textarea, input {
            width: 80%;
            padding: 10px;
            margin-top: 5px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }

        button {
            padding: 10px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        button.save-button {
            margin-top: 10px;
            padding: 10px 20px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button.save-button:hover {
            background-color: #0056b3;
        }

        .modal {
            display: none;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ccc;
            z-index: 1000;
            width: 60%;
            max-width: 800px;
            border-radius: 8px;
        }

        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            color: #333333;
        }

        .results-container {
            display: grid;
            grid-template-columns: 70% 25%;
            gap: 20px;
            margin-top: 20px;
        }

        .result-view, .tracing-detail {
            background-color: #fff;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .button-group {
            display: flex;
            gap: 10px;
        }

        .section {
            margin-bottom: 20px;
        }

        .button-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            gap: 10px;
        }

        .file-row {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }

        .file-row label {
            margin-right: 10px;
        }

        .file-row input[type="radio"] {
            display: none;
        }

        .file-row input[type="radio"] + label {
            position: relative;
            padding-left: 30px;
            cursor: pointer;
            user-select: none;
        }

        .file-row input[type="radio"] + label::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 18px;
            height: 18px;
            border: 2px solid #ccc;
            border-radius: 4px;
            background-color: white;
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }

        .file-row input[type="radio"]:checked + label::before {
            background-color: #007BFF;
            border-color: #007BFF;
        }

        .file-row input[type="radio"]:checked + label::after {
            content: '';
            position: absolute;
            left: 5px;
            top: 2px;
            width: 8px;
            height: 14px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .textarea-container {
            width: 48%;
        }
        input[type="file"] {
            width: 75%;
            margin-right: 15px;
        }
    </style>
</head>
<body>
<div class="container">
    <h2>数学测试报告生成助手</h2>
    <!-- 选择生成方式 -->
    <div class="file-row">
        <label>选择生成方式：</label>
        <input type="radio" id="only_paper" name="generate_mode" value="only_paper" checked>
        <label for="only_paper">基于试卷</label>
        <input type="radio" id="paper_with_record" name="generate_mode" value="paper_with_record">
        <label for="paper_with_record">基于试卷+学习记录</label>
    </div>
    <!-- 学生ID和试卷ID输入框 -->
    <div class="file-row">
        <label for="student-id">学生ID：</label>
        <input type="text" id="student-id" value="2VspVAzUwtNhm29fgND7KE" readonly/>
    </div>
    <div class="file-row">
        <label for="paper-id">试卷ID：</label>
        <input type="text" id="paper-id"/>
    </div>
    <div class="file-row">
        <label>选择科目：</label>
        <input type="radio" id="high_math" name="subject" value="high_math">
        <label for="high_math">高等数学</label>
        <input type="radio" id="linear_algebra" name="subject" value="linear_algebra">
        <label for="linear_algebra">线性代数</label>
        <input type="radio" id="math_prob" name="subject" value="math_prob">
        <label for="math_prob">概率论</label>
    </div>
    <div class="file-row">
        <label for="learning_report_generator">提示词：</label>
        <textarea id="learning_report_generator" class="prompt_input" name="learning_report_generator"></textarea>
        <textarea id="learning_report_generator_single" class="hidden-prompt" style="display: none;">{{ learning_report_generator_single }}</textarea>
        <textarea id="learning_report_generator_mul" class="hidden-prompt" style="display: none;">{{ learning_report_generator_mul }}</textarea>
    </div>

    <button onclick="submitLearningReport()">生成学习报告</button>

    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>学习报告生成中...</p>
        </div>
    </div>

    <!-- 返回结果 -->
    <div class="section">
        <h3>学习报告返回结果:</h3>
        <div class="results-container">
            <div class="result-view" id="resultView">
                <!-- 搜索结果将在这里显示 -->
            </div>
            <div class="tracing-detail" id="tracingDetails">
                <!-- 搜索细节将在这里显示 -->
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const generateModeRadios = document.querySelectorAll('input[name="generate_mode"]');
        const learningReportGenerator = document.getElementById('learning_report_generator');
        const learningReportGeneratorSingle = document.getElementById('learning_report_generator_single');
        const learningReportGeneratorMul = document.getElementById('learning_report_generator_mul');

        generateModeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                 if (radio.value === 'only_paper') {
                    learningReportGenerator.value = learningReportGeneratorSingle.value;
                } else if (radio.value === 'paper_with_record') {
                    learningReportGenerator.value = learningReportGeneratorMul.value;
                }
                // No need to handle video subtitles upload row visibility
            });
        });
        const initialGenerateMode = document.querySelector('input[name="generate_mode"]:checked').value;
        if (initialGenerateMode === 'only_paper') {
            learningReportGenerator.value = learningReportGeneratorSingle.value;
        } else if (initialGenerateMode === 'paper_with_record') {
            learningReportGenerator.value = learningReportGeneratorMul.value;
        }
        // Initial state setup
        // No need to handle video subtitles upload row visibility
    });

    let message_id = null;

    const resultView = document.getElementById('resultView');
    const tracingDetails = document.getElementById('tracingDetails');
    const katexOptions = {
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        throwOnError: false
    }

    function submitLearningReport() {
        let promptContent = document.getElementById('learning_report_generator').value.trim();
        let selectedSubject = document.querySelector('input[name="subject"]:checked');

        let answerId = document.getElementById('paper-id').value;
        let generateMode = document.querySelector('input[name="generate_mode"]:checked').value; // 获取生成方式
        console.log("answer_id:", answerId);
        if (!selectedSubject) {
            alert('请选择一个科目！');
            return;
        }

        selectedSubject = selectedSubject.value;

        generateLearningReport(promptContent, selectedSubject, answerId, generateMode);
    }

    function generateLearningReport(promptContent, selectedSubject, answerId, generateMode) {
        // 清空返回结果的内容
        resultView.innerHTML = ''; // 清空之前的结果
        tracingDetails.innerHTML = ''; // 清空之前的细节

        $('#loadingOverlay').css('display', 'flex');

        message_id = null;
        const url = "{% url 'learning_report_generator' %}";
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                learning_report_generator: promptContent,
                subject: selectedSubject,
                answer_id: answerId,
                generate_mode: generateMode // 添加生成方式
            })
        })
        .then(response => {
            $('#loadingOverlay').css('display', 'none');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.body;
        })
        .then(stream => {
            $('#loadingOverlay').css('display', 'none');
            let streamComplete = false;
            const reader = stream.getReader();
            const decoder = new TextDecoder(); // 创建TextDecoder实例
            let rawResult = ''; // 清空存储的原始数据
            let result_text = '';
            function readChunk() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        streamComplete = true;
                        console.log('Stream complete, returning collected data');
                        console.log('find_tracing function is called');
                        find_tracing();
                        return;
                    }
                    const textChunkStr = decoder.decode(value, { stream: true });

                    const chunkArr = textChunkStr.split("\n\n");
                    for (let i = 0; i < chunkArr.length; i++) {
                        const textChunk = chunkArr[i];
                        if (textChunk.startsWith("data: ")) {
                            // 去除前缀"data: "
                            const json_str = textChunk.slice(6);
                            console.log('json_str', json_str);
                            try {
                                // 将匹配到的字符串转换为JSON对象
                                const jsonData = JSON.parse(json_str);
                                if (!message_id) {
                                    message_id = jsonData.message_id;
                                }
                                if (jsonData.answer) {
                                    rawResult += jsonData.answer; // 存储未渲染的内容
                                    result_text += jsonData.answer;
                                    resultView.innerHTML = marked.parse(result_text);
                                    renderMathInElement(resultView, katexOptions);
                                } else if (jsonData.err) {
                                    resultView.innerHTML = jsonData.err;
                                }
                            } catch (error) {
                                console.error('Error parsing JSON', json_str, error);
                            }
                        }
                    }

                    readChunk();
                }).catch(error => {
                    console.error('Error collecting stream data', error);
                });
            }
            readChunk();
        })
        .catch(error => {
            $('#loadingOverlay').css('display', 'none');
            console.error('Error:', error);
        });
    }

    function find_tracing() {
        if (!message_id) {
            console.warn('Message ID is not set, cannot fetch tracing information.');
            return;
        }
        const $el = $('#tracingDetails');  // 指定展示追踪信息的容器

        console.log('Preparing to send AJAX request with Message ID:', message_id);
        $.ajax({
            type: 'GET',
            url: "{% url 'message_tracing' %}",
            data: { message_id: message_id },
            success: function(response) {
                $('#loadingOverlay').css('display', 'none');

                const results = response.data;

                $el.empty(); // 确保清空之前的内容

                if (results && results.tracing) {
                    results.tracing.forEach(function(traceDetail) {
                        console.log('Adding tracing detail:', traceDetail);
                        const detailElement = document.createElement('p');
                        detailElement.textContent = traceDetail;
                        $el.append(detailElement);
                    });
                } else {
                    $el.append('<p>没有找到追踪信息。</p>');
                }
            },
            error: function(xhr, status, error) {
                alert('请求失败');
                $('#loadingOverlay').css('display', 'none');
                console.error('AJAX Error:', xhr.responseText);
            }
        });
    }
</script>

</body>
</html>
