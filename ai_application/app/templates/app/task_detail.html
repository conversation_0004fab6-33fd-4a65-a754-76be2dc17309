<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记生成任务查看</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h1 {
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            font-size: 14px;
            color: #fff;
            background-color: #007bff;
            text-decoration: none;
            border-radius: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
<h1>笔记生成任务详情</h1>
<table>
    <thead>
        <tr>
            <th>名称</th>
            <th>任务开始时间</th>
            <th>任务结束时间</th>
            <th>生成状态</th>
            <th>讲义字数</th>
            <th>笔记字数</th>
            <th>token</th>
            <th>LLM响应时间</th>
            <th>操作</th>
        </tr>
    </thead>
    <tbody>
        {% for task in tasks %}
        <tr>
            <td>{{ task.name }}</td>
            <td>{{ task.processing_started_at|date:"Y-m-d H:i:s" }}</td>
            <td>{{ task.completed_at|date:"Y-m-d H:i:s" }}</td>
            <td>{{ task.status }}</td>
            <td>{{ task.lecture_word_len }}</td>
            <td>{{ task.note_word_len }}</td>
            <td>
                <div>请求：{{ task.message.message_tokens }}</div>
                <div>响应：{{ task.message.answer_tokens }}</div>
            </td>
            <td>{{ task.response_latency }}秒</td>
            <td>
                {% if task.status == 'success' %}
                <a href="{% url 'single_note_detail' task.id %}" class="btn">查看笔记内容</a>
                {% endif %}
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
</body>
</html>