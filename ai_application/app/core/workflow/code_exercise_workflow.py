import json

from app.core.features.code_exercise import CodeExerciseFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError


class CodeExerciseWorkflow(BaseWorkflow):

    def run(self):
        inputs = self.application_generate_entity.inputs
        # 从 inputs 中获取 question_type、question 和 answer
        question_type = inputs.get('question_type')
        question = inputs.get('question')
        answer = inputs.get('answer', '')
        lang = inputs.get('lang', '')

        result = CodeExerciseFeature().run(
            application_generate_entity=self.application_generate_entity,
            question_type=question_type,
            question=question,
            answer=answer,
            lang=lang,
            stream=self.stream,
        )

        tracing_info = {
            'tracing_type': 'code_exercise', 'query': ''
        }
        self._handle_node_response(result, stream=self.stream, tracing_info=tracing_info)
        self._publish_workflow_end_message()
