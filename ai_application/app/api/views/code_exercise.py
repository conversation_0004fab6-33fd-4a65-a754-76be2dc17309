from django_ext import base_view
from app.errors import AppNotFoundError
from app.services.app_generate_service import AppGenerateService
from django_ext.response import make_stream_response, make_response
from app.api.dto import ChatMessageDto, CodeExerciseDto
from app.api.validators import CodeExerciseValidator
from app.models import App
import json


class CodeExerciseView(base_view.BaseView):
    """python代码练习解析查看视图"""
    validator_class = CodeExerciseValidator

    def post(self, request, *args, **kwargs):
        dto: CodeExerciseDto = self.validate_request(request.data)

        app: App = App.objects.filter(is_deleted=False, app_type='dsx_code_exercise').first()
        if not app:
            raise AppNotFoundError()

        inputs = {
            'question_type': dto.question_type,
            'question': dto.question,
            'answer': dto.answer,
            'lang': dto.lang,
        }
        chat_dto = ChatMessageDto(
            app_id=app.app_no,
            inputs=inputs,
            query=json.dumps(inputs, ensure_ascii=False),
            stream=dto.stream
        )
        response = AppGenerateService.generate(chat_dto, request.user)
        if isinstance(response, dict):
            return make_response({
                'content':response.get('answer', '')
            })
        return make_stream_response(response)
    

