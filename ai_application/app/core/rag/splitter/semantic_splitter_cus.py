import re
import numpy as np
from typing import List, Optional, Any

from app.core.model_manager import ModelInstance
from app.core.rag.splitter.text_splitter import RecursiveCharacterTextSplitter


import dashscope
from http import HTTPStatus


class SemanticSplitter(RecursiveCharacterTextSplitter):

    def __init__(
            self,
            embedding_model_instance: ModelInstance,
            buffer_size: int = 1,
            separators: Optional[list[str]] = None,
            keep_separator: bool = True,
            **kwargs: Any,
    ) -> None:
        """Create a new TextSplitter."""
        super().__init__(
            separators=separators,
            keep_separator=keep_separator,
            **kwargs
        )
        self._embedding_model_instance = embedding_model_instance
        self._buffer_size = buffer_size

    def split_text(self, text: str) -> List[str]:
        breakpoint_percentile_threshold = 95
        separators = ['。', '？', '！']

        re_separators = ''.join(separators)
        sentences = re.split(rf'(?<=[{re_separators}])\s*', text)
        sentences = [{'sentence': x, 'index': i} for i, x in enumerate(sentences) if x]

        self._combine_sentences(sentences)

        print(sentences)

        distances = self._calculate_cosine_distances(sentences)
        breakpoint_distance_threshold = np.percentile(distances, breakpoint_percentile_threshold)
        indices_above_thresh = [i for i, x in enumerate(distances) if x > breakpoint_distance_threshold]

        start_index = 0
        chunks = []
        for index in indices_above_thresh:
            # The end index is the current breakpoint
            end_index = index

            # Slice the sentence_dicts from the current start index to the end index
            group = sentences[start_index:end_index + 1]
            combined_text = ' '.join([d['sentence'] for d in group])
            chunks.append(combined_text)

            # Update the start index for the next group
            start_index = index + 1

        if start_index < len(sentences):
            combined_text = ' '.join([d['sentence'] for d in sentences[start_index:]])
            chunks.append(combined_text)

        return chunks

    def _combine_sentences(self, sentences):
        combined_sentences = [
            ' '.join(sentences[j]['sentence'] for j in
                     range(max(i - self._buffer_size, 0), min(i + self._buffer_size + 1, len(sentences))))
            for i in range(len(sentences))
        ]

        for i, combined_sentence in enumerate(combined_sentences):
            sentences[i]['combined_sentence'] = combined_sentence
            res = self._embed_with_str(combined_sentence)
            sentences[i]['combined_sentence_embedding'] = res

    def _embed_with_str(self, input_str) -> list[float]:
        result = self._embedding_model_instance.invoke_text_embedding([input_str])
        return result.embeddings[0]

    def _calculate_cosine_distances(self, sentences):
        distances = []
        for i in range(len(sentences) - 1):
            embedding_current = sentences[i]['combined_sentence_embedding']
            embedding_next = sentences[i + 1]['combined_sentence_embedding']
            # Calculate cosine similarity
            similarity = self._cosine_similarity(embedding_current, embedding_next)
            # Convert to cosine distance
            distance = 1 - similarity
            distances.append(distance)
            # Store distance in the dictionary
            sentences[i]['distance_to_next'] = distance
        return distances

    def _cosine_similarity(self, vec1, vec2):
        """Calculate the cosine similarity between two vectors."""
        dot_product = np.dot(vec1, vec2)
        norm_vec1 = np.linalg.norm(vec1)
        norm_vec2 = np.linalg.norm(vec2)
        return dot_product / (norm_vec1 * norm_vec2)
