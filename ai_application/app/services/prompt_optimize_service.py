import re

from app.api.dto import ChatMessageDto, PromptOptimizeDto
from app.core.prompt.params_prompt import comb_prompt_by_params
from app.models import Account, InvokeFrom, App, PromptTemplate
from app.services.app_generate_service import AppGenerateService


class PromptOptimizeService:

    @classmethod
    def get_prologue(cls, dto: PromptOptimizeDto, account: Account):
        invoke_from = InvokeFrom.api.value
        dto = ChatMessageDto(
            app_id='prologue_generator',
            inputs={"max_tokens": 500, "temperature": 0.8},
            query=dto.query_params.role,
            stream=False,
        )
        response = AppGenerateService.generate(dto, account, invoke_from=invoke_from)
        return response.get('answer', '')

    @classmethod
    def get_prompt_dict(cls, dto: PromptOptimizeDto, account: Account) -> dict:
        invoke_from = InvokeFrom.api.value
        query_params = dto.query_params.model_dump()
        response = cls.get_optimized_prompt(query_params, account, invoke_from=invoke_from)
        return cls.parse_instructions(response.get('answer', ''))

    @classmethod
    def get_optimized_prompt(cls, query_params: dict, account: Account, invoke_from: str = InvokeFrom.api.value) -> dict:
        query = comb_prompt_by_params(query_params)
        template_str = cls.comb_template_str(query_params)

        dto = ChatMessageDto(
            app_id='prompt_optimize',
            inputs={"max_tokens": 500, "temperature": 0.8, 'template': template_str},
            query=query,
            stream=False,
        )
        return AppGenerateService.generate(dto, account, invoke_from=invoke_from)

    @classmethod
    def get_prompt_template(cls, app: App) -> PromptTemplate:
        prompt_template_list = app.app_model_config.prompt_template_list
        if not prompt_template_list:
            raise Exception('prompt template not found')

        prompt_template_id = prompt_template_list[0]
        ins = PromptTemplate.objects.filter(id=prompt_template_id).first()
        if not ins:
            raise Exception('prompt template not found')
        return ins

    @classmethod
    def parse_instructions(cls, text) -> dict:
        # 定义一个字典来存储解析后的数据
        result = {
            "role": "",
            "tone": "",
            "task": "",
            "examples": "",
            "instructions": "",
            "note": "",
        }
        if not text:
            return result

        # 使用正则表达式来查找和提取信息
        role_pattern = r"## 角色\n([^#]*)"
        tone_pattern = r"## 情感基调\n([^#]*)"
        task_pattern = r"## 任务\n([^#]*)"
        examples_pattern = r"## 示例\n([^#]*)"
        instructions_pattern = r"## 说明\n([^#]*)"
        note_pattern = r"## 注意\n([^#]*)"

        # 搜索并提取角色定义
        role_match = re.search(role_pattern, text, re.DOTALL)
        if role_match:
            result["role"] = role_match.group(1).strip()

        # 搜索并提取情感基调
        tone_match = re.search(tone_pattern, text, re.DOTALL)
        if tone_match:
            result["tone"] = tone_match.group(1).strip()

        # 搜索并提取任务步骤
        task_match = re.search(task_pattern, text, re.DOTALL)
        if task_match:
            result["task"] = task_match.group(1).strip()

        # 搜索并提取示例
        examples_match = re.search(examples_pattern, text, re.DOTALL)
        if examples_match:
            result["examples"] = examples_match.group(1).strip()

        # 搜索并提取说明
        instructions_match = re.search(instructions_pattern, text, re.DOTALL)
        if instructions_match:
            result["instructions"] = instructions_match.group(1).strip()

        # 搜索并提取注意
        note_match = re.search(note_pattern, text, re.DOTALL)
        if note_match:
            result["note"] = note_match.group(1).strip()

        return result

    @classmethod
    def comb_template_str(cls, prompt_dict: dict) -> str:
        all_params = ['role', 'tone', 'task', 'examples', 'instructions', 'note']

        role = prompt_dict.get('role')
        tone = prompt_dict.get('tone')
        task = prompt_dict.get('task')
        examples = prompt_dict.get('examples')
        instructions = prompt_dict.get('instructions')
        note = prompt_dict.get('note')

        # 只有角色定义时，才需要拼接全部模板
        if role and not tone and not task and not examples and not instructions and not note:
            return cls._comb_template_str(all_params)

        has_content_params = [p for p in all_params if prompt_dict.get(p)]
        return cls._comb_template_str(has_content_params)

    @classmethod
    def _comb_template_str(cls, template_params: list) -> str:
        template_str_arr = []

        role_part = []
        if 'role' in template_params:
            role_part.append(f"## 角色\n请描述角色的身份、职责及其在场景中的作用,适当扩充，30个字左右。")
        if 'tone' in template_params:
            role_part.append(f"## 情感基调\n请用几个词语描述角色的情感状态和态度。")
        if role_part:
            role_part_str = '\n'.join(role_part)
            template_str_arr.append(f"# 角色定义\n{role_part_str}")

        task_part = []
        if 'task' in template_params:
            task_part.append(f"## 任务\n请根据用户输入的内容将任务分解为具体的步骤，必须分步1,2,3...列出，并描述每一步的操作细节，少于50个字。")
        if 'examples' in template_params:
            task_part.append(f"## 示例\n请根据用户输入的内容提供一个具体的示例来说明如何执行任务，30个字左右。")
        if task_part:
            task_part_str = '\n'.join(task_part)
            template_str_arr.append(f"# 任务步骤\n{task_part_str}")

        instructions_part = []
        if 'instructions' in template_params:
            instructions_part.append(f"## 说明\n请请根据用户输入的内容描述期望的输出形式和内容，少于50个字。")
        if 'note' in template_params:
            instructions_part.append(f"## 注意\n请根据用户输入的内容适当扩充，少于50个字。")
        if instructions_part:
            instructions_part_str = '\n'.join(instructions_part)
            template_str_arr.append(f"# 输出约束\n{instructions_part_str}")

        return '\n\n'.join(template_str_arr)
