# Generated by Django 4.2 on 2025-04-17 05:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0044_collegeinfo'),
    ]

    operations = [
        migrations.CreateModel(
            name='HollandTestResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('genre', models.CharField(max_length=50, verbose_name='测试类型')),
                ('result', models.TextField(verbose_name='测试结果')),
            ],
            options={
                'verbose_name': '霍兰德测试结果',
                'verbose_name_plural': '霍兰德测试结果',
                'db_table': 'ai_holland_test_result',
            },
        ),
    ]
