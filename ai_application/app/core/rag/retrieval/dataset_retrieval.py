import time
from typing import cast

from django.db.models import F

from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.callback_handler.index_tool_callback_handler import DatasetIndexToolCallbackHandler
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.rag.models.document import Document
from app.core.rag.retrieval_service import RetrievalService
from app.models import Dataset, DocumentSegment, DatasetDocument

default_retrieval_model = {
    'top_k': 2,
    'score_threshold_enabled': False
}


class DatasetRetrieval:
    def __init__(self, application_generate_entity: ChatAppGenerateEntity | None = None):
        self.application_generate_entity = application_generate_entity

    def retrieve(
            self,
            query: str,
            dataset: Dataset,
            query_documents: list[DatasetDocument],
            hit_callback: DatasetIndexToolCallbackHandler | None = None,
            tracing_log: MessageTracingLog | None = None,
            is_content_with_context: bool = True,
            return_str: bool = True,
            retrieval_model: dict | None = None
    ):
        started_at = time.perf_counter()

        all_documents = self.single_retrieve(
            dataset, query_documents, query,
            retrieval_model=retrieval_model
        )
        if not all_documents:
            return

        if hit_callback:
            hit_callback.on_query(query, dataset)

        document_score_list = {}
        for item in all_documents:
            if item.metadata.get('score'):
                document_score_list[item.metadata['doc_id']] = item.metadata['score']

        document_context_list = []
        index_node_ids = [document.metadata['doc_id'] for document in all_documents]
        segments = DocumentSegment.objects.filter(
            is_deleted=False, dataset=dataset, status='completed', index_node_id__in=index_node_ids
        )

        if segments:
            index_node_id_to_position = {idx: position for position, idx in enumerate(index_node_ids)}
            sorted_segments = sorted(
                segments, key=lambda segment_: index_node_id_to_position.get(segment_.index_node_id, float('inf'))
            )

            # 记录搜索来源
            context_list = []
            resource_number = 1

            for segment in sorted_segments:
                segment_content = segment.content

                segment = cast(DocumentSegment, segment)
                dataset_document = segment.dataset_document
                source = {
                    'position': resource_number,
                    'dataset': dataset,
                    'dataset_name': dataset.name,
                    'dataset_document': dataset_document,
                    'dataset_document_name': dataset_document.name,
                    'data_source_type': segment.dataset_document.data_source_type,
                    'segment': segment,
                    'score': document_score_list.get(segment.index_node_id, None),
                    'content': segment.content,
                }

                content_with_context = []
                # 获取文章的上下文
                # 获取前1段和后2段
                if is_content_with_context:
                    prev_parts = dataset_document.documentsegment_set.filter(
                        is_deleted=False, position__lt=segment.position
                    ).order_by('-position')[:1]
                    if prev_parts:
                        # content_with_context.append({'content': f'{prev_parts[0].content}\n', 'is_hit': False})
                        prev_content = ''.join([i.content for i in prev_parts])
                        segment_content = f'{prev_content}{segment_content}'

                content_with_context.append({'content': segment.content, 'is_hit': True})

                if is_content_with_context:
                    next_parts = dataset_document.documentsegment_set.filter(
                        is_deleted=False, position__gt=segment.position
                    ).order_by('position')[:2]
                    if next_parts:
                        # content_with_context.append({'content': f'\n{next_parts[0].content}', 'is_hit': False})
                        next_content = ''.join([i.content for i in next_parts])
                        segment_content = f'{segment_content}{next_content}'

                source['content_with_context'] = content_with_context

                document_context_list.append(segment_content)
                context_list.append(source)
                resource_number += 1

            if hit_callback:
                hit_callback.return_retriever_resource_info(context_list)

        context_str = str("\n".join(document_context_list))
        if tracing_log:
            tracing_log.add_tracing(
                tracing_type='query_retrieval',
                query=query,
                content='',
                answer=context_str,
                message_tokens=0,
                answer_tokens=0,
                total_tokens=0,
                latency=time.perf_counter() - started_at,
            )

        return context_str if return_str else document_context_list

    def single_retrieve(
            self,
            dataset: Dataset,
            query_documents: list[DatasetDocument],
            query: str,
            retrieval_model: dict | None = None
    ) -> list[Document]:
        # score越小相似度越高
        if not retrieval_model:
            retrieval_model = default_retrieval_model
        top_k = retrieval_model['top_k']
        # TODO 分数阈值
        score_threshold = None
        results = RetrievalService.retrieve(
            dataset=dataset,
            query_documents=query_documents,
            query=query,
            top_k=top_k,
            score_threshold=score_threshold,
        )
        if results:
            self._on_retrival_end(results, dataset)

        return results

    def _on_retrival_end(
            self, documents: list[Document], dataset: Dataset, message_id: str | None = None
    ):
        for document in documents:
            DocumentSegment.objects.filter(
                is_deleted=False, dataset=dataset, index_node_id=document.metadata['doc_id']
            ).update(hit_count=F('hit_count') + 1)
