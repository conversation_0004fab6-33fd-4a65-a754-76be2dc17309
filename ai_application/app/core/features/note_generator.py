
from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate


class NoteGeneratorFeature(BaseFeature):

    def __init__(self, app_no: str):
        self.prompt_template = self._get_prompt_template(app_no)
        if not self.prompt_template:
            raise Exception('Prompt template not found')

    def _get_prompt_template(self, app_no: str) -> PromptTemplate:
        return PromptTemplate.objects.filter(app_no=app_no).first()

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            lecture_slides: str,
            chapter_note:str,
            subtitle: str,
            stream: bool,
            prompt_content: str = '',
            subtitle_dict: str =''
    ):
        user_inputs = application_generate_entity.inputs.copy()
        user_inputs['lecture_slides'] = lecture_slides
        user_inputs['subtitle'] = subtitle
        user_inputs['chapter_note'] = chapter_note
        user_inputs['subtitle_dict'] = subtitle_dict
        pre_prompt = self.prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=prompt_content
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=self.prompt_template
        )
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=stream
        )
