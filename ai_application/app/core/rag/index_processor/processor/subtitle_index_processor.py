import uuid

from app.core.features.knowledge_keyword_extract import KnowledgeKeywordExtractFeature
from app.core.rag.extractor.entities.extract_setting import ExtractSetting
from app.core.rag.index_processor.constant.index_type import IndexType
from app.core.rag.index_processor.index_processor_base import BaseIndexProcessor
from app.core.rag.models.document import Document
from app.core.rag.vdb.vector_factory import Vector
from app.models import CourseVideoContent, Dataset, DatasetDocument, CourseVideoKeyword, DocumentSegment

from app.utils import generate_text_hash


class SubtitleIndexProcessor(BaseIndexProcessor):

    def extract(self, extract_setting: ExtractSetting, **kwargs) -> list[Document]:
        video_content_id = extract_setting.data_source_info.get('video_content_id')
        obj: CourseVideoContent = CourseVideoContent.objects.filter(id=video_content_id).first()
        if not obj:
            return []

        return [Document(
            page_content=obj.content,
            metadata={
                "video_content_id": video_content_id,
                "video_name": obj.name,
            }
        )]

    def transform_special(self, documents: list[Document], **kwargs) -> list[Document]:
        pass

    def transform(self, documents: list[Document], **kwargs) -> list[Document]:
        dataset_document: DatasetDocument = kwargs.get('dataset_document')

        kw_extract_feature = KnowledgeKeywordExtractFeature()
        tf_documents = []
        kw_objs = []
        for d in documents:
            video_content_id = d.metadata.get('video_content_id')
            video_content: CourseVideoContent = CourseVideoContent.objects.filter(id=video_content_id).first()
            if not video_content:
                continue

            if not video_content.is_optimized:
                # TODO 调用模型优化字幕
                pass

            keywords = kw_extract_feature.run(video_content.get_content())
            for keyword, weight in keywords:
                doc_id = str(uuid.uuid4())
                kw_objs.append(CourseVideoKeyword(
                    dataset=dataset_document.dataset,
                    dataset_document=dataset_document,
                    video_content=video_content,
                    keyword=keyword,
                    weight=weight
                ))
                # 校验keyword是否已经存在，入已存在，则不写入向量库
                if DocumentSegment.objects.filter(
                    dataset=dataset_document.dataset,
                    content=keyword
                ).exists():
                    continue

                tf_documents.append(Document(
                    page_content=keyword,
                    metadata={
                        'index_type': IndexType.SUBTITLE_INDEX.value,
                        'document_no': dataset_document.document_no,
                        'doc_id': doc_id,
                        'doc_hash': generate_text_hash(keyword),
                    }
                ))
        CourseVideoKeyword.objects.bulk_create(kw_objs)
        return tf_documents

    def load(self, dataset: Dataset, documents: list[Document]):
        vector = Vector(dataset)
        vector.create(documents)

    def clean(self, dataset: Dataset, node_ids: list[str] | None):
        vector = Vector(dataset)
        if node_ids:
            vector.delete_by_doc_ids(node_ids)
        else:
            vector.delete()
