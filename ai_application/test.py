import django
import os



os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_application.settings")
django.setup()

# from app.services.text_to_video import process_knowledge
import json
from app.core.utils.latex_utils import replace_latex
# process_knowledge({'name': '最佳归并树'})

s='''
[
    {
        "id": 1,
        "title": "二叉树的性质 - 定义",
        "text": "课程名称：《数据结构》。知识点：二叉树的性质。定义：二叉树的性质是描述二叉树结构特征的一系列规则，反映了二叉树节点数量、层次关系等方面的内在规律。",
        "audio": "同学们好，今天咱们要学习《数据结构》里的二叉树的性质。简单来说，这些性质就是描述二叉树结构特点的规则，能让我们了解二叉树节点数量和层次关系的规律。"
    },
    {
        "id": 2,
        "title": "二叉树的性质 - 前置知识点",
        "text": "要掌握二叉树的性质，需要了解二叉树的基本概念，如节点、根节点、子节点等。还需有一定的数学基础，能进行简单的数学运算和逻辑推理。",
        "audio": "在学习二叉树的性质之前，咱们得先了解一些前置知识。你得知道二叉树里的节点、根节点、子节点都是啥，还得有点数学基础，能做简单运算和逻辑推理，这样才能更好理解性质里的数量关系。"
    },
    {
        "id": 3,
        "title": "二叉树的性质 - 第i层节点数",
        "text": "性质一：在第 \(i\) 层上，最多有 \(2^{i - 1}\) 个节点。例如第1层，\(i = 1\)，\(2^{1 - 1}=1\) 个节点；第2层，\(i = 2\)，\(2^{2 - 1}=2\) 个节点。",
        "audio": "咱们先来看第一个性质，在二叉树的第i层上，最多有2的i - 1次方个节点。就像第1层，i是1，2的1 - 1次方就是1，所以第1层最多1个节点；第2层，i是2，2的2 - 1次方是2，那第2层最多2个节点。"
    },
    {
        "id": 4,
        "title": "二叉树的性质 - 深度为k的节点数",
        "text": "性质二：深度为 \(k\) 的二叉树，最多有 \(2^k - 1\) 个节点。比如深度 \(k = 3\) 时，\(2^3 - 1=7\) 个节点。",
        "audio": "再看第二个性质，深度为k的二叉树，最多有2的k次方减1个节点。要是深度k是3，2的3次方减1就是7，说明深度为3的二叉树最多有7个节点。"
    },
    {
        "id": 5,
        "title": "二叉树的性质 - 叶子节点与度为2节点关系",
        "text": "性质三：对于任何一棵二叉树，如果其叶子节点数为 \(n_0\)，度为 2 的节点数为 \(n_2\)，那么 \(n_0 = n_2 + 1\)。",
        "audio": "接着是第三个性质，对于任何一棵二叉树，要是叶子节点数是n0，度为2的节点数是n2，那n0就等于n2加1。"
    },
    {
        "id": 6,
        "title": "二叉树的性质 - 满二叉树与完全二叉树",
        "text": "满二叉树：每一层节点都达到最大值的二叉树。完全二叉树：除最后一层外，每一层都满，且最后一层节点从左到右排列。",
        "audio": "最后说说满二叉树和完全二叉树。满二叉树就是每一层的节点数都达到最大值；完全二叉树呢，除了最后一层，其他层节点数都满了，而且最后一层的节点是从左到右排列的。"
    },
    {
        "id": 7,
        "title": "二叉树的性质 - 深度为3的节点数案例",
        "text": "假如有一棵深度为 3 的二叉树，第 1 层最多 \(2^{1 - 1}=1\) 个节点，第 2 层最多 \(2^{2 - 1}=2\) 个节点，第 3 层最多 \(2^{3 - 1}=4\) 个节点，整棵树最多 \(1 + 2 + 4 = 7\) 个节点。就像盖房子，能提前知道每层能放多少东西。",
        "audio": "咱们来举个例子，有一棵深度为3的二叉树，按照前面说的性质，第1层最多1个节点，第2层最多2个节点，第3层最多4个节点，那整棵树最多就有7个节点。这就好比盖房子，我们能提前知道每层能放多少东西。"
    },
    {
        "id": 8,
        "title": "二叉树的性质 - 叶子节点与度为2节点案例",
        "text": "已知一棵二叉树的叶子节点有 5 个，根据 \(n_0 = n_2 + 1\)，可推出度为 2 的节点有 4 个。",
        "audio": "再看另一个例子，已知一棵二叉树的叶子节点有5个，根据n0等于n2加1这个公式，就能算出度为2的节点有4个。"
    }
]
'''
s1 = replace_latex(s)
print(s1)
json.loads(s1)


