import time

from langchain_core.prompts import PromptTemplate as LangChainPromptTemplate

from app.core.invoke_llm import LangChainLlmWrapper
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import STUserPaperAnswer, STQuestion, PromptTemplate
from django_ext.utils.string_utils import parse_int


class AnswerReportWorkflow(BaseWorkflow):

    def run(self):
        answer_id = parse_int(self.application_generate_entity.query)
        if not answer_id:
            raise ValueError('参数错误')
        paper_answer: STUserPaperAnswer = STUserPaperAnswer.objects.filter(id=answer_id).first()
        if not paper_answer:
            raise ValueError('答卷不存在')

        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no='shuati_answer_report').first()
        if not prompt_template:
            raise ValueError('没有找到对应的提示词')

        user_question_answers = paper_answer.stuserpaperquestionanswer_set.filter(is_deleted=False)

        # 获取题目信息
        question_ids = [qa.question_id for qa in user_question_answers]
        questions = STQuestion.objects.filter(id__in=question_ids)
        question_map = {q.id: q for q in questions}

        # 准备报告所需数据
        paper_content = []
        for qa_idx, qa in enumerate(user_question_answers, start=1):
            question: STQuestion = question_map.get(qa.question_id)
            if question.question_type in [0, 1]:
                user_answer_str = ','.join(qa.choice_answer)
            else:
                # 处理图片信息
                user_answer_str = qa.get_subjective_answer_str()
            question_content = {
                'question_id': qa_idx,
                'question_content': question.format_question_content,
                'difficulty': question.difficulty,
                'analysis': question.analysis,
                'user_answer': user_answer_str,
                'answer_status': qa.answer_status,
            }
            paper_content.append(question_content)

        paper_answer.report_id = str(self.message.id)
        paper_answer.report_params = paper_content
        paper_answer.save(update_fields=['report_id', 'report_params'])

        wrapper = LangChainLlmWrapper(
            model_provider=prompt_template.model_provider,
            model_id=prompt_template.model_id,
            model_params=prompt_template.model_params,
        )
        llm = wrapper.get_llm()

        prompt = LangChainPromptTemplate.from_template(prompt_template.prompt_content)

        chain = prompt | llm

        start_at = time.perf_counter()
        response = chain.invoke({
            "paper_content": paper_content
        })
        latency = time.perf_counter() - start_at

        llm_result = wrapper.handle_non_stream_response(response, latency)

        tracing_info = {'tracing_type': 'shuati_answer_report', 'query': answer_id}
        self._handle_node_direct_response(
            llm_result,
            tracing_info=tracing_info
        )
        self._publish_workflow_end_message()
