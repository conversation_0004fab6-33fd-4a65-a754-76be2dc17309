import logging
import math
import time
import uuid

from django.utils import timezone

from app.core.model_provider_manager import ModelProviderManager
from app.core.rag.dataset_docstore import DatasetDocumentStore
from app.core.rag.extractor.entities.datasource_type import DatasourceType
from app.core.rag.extractor.entities.extract_setting import ExtractSetting
from app.core.rag.index_processor.constant.index_type import IndexType
from app.core.rag.index_processor.index_processor_base import BaseIndexProcessor
from app.core.rag.index_processor.index_processor_factory import IndexProcessorFactory
from app.core.rag.knowledge_service import KnowledgeService
from app.core.rag.models.document import Document
from app.libs.redis import redis_default_client
from app.models import Dataset, DatasetDocument, DocumentSegment, Knowledge
from app.utils import generate_text_hash

logger = logging.getLogger(__name__)


class IndexingRunner:
    def __init__(self):
        self.model_manager = ModelProviderManager()

    def run(
            self,
            dataset: Dataset,
            dataset_documents: list[DatasetDocument]
    ):
        for dataset_document in dataset_documents:
            try:
                processing_rule_obj = dataset_document.dataset_process_rule
                processing_rule = processing_rule_obj.rules
                index_type = dataset_document.index_type
                index_processor = IndexProcessorFactory(index_type).init_index_processor()

                # extract
                text_docs = self._extract(index_processor, dataset_document)
                # transform
                documents = self._transform(
                    index_processor,
                    text_docs,
                    processing_rule,
                    dataset_document,
                )
                # save segment
                self._load_segments(dataset, dataset_document, documents)

                # 目前暂时只解析特定的markdown表格，预先处理一下embedding内容
                # TODO 该逻辑后期会清除
                # if (index_type == IndexType.PARAGRAPH_INDEX.value and
                #         dataset_document.file_extension in ['md', 'markdown']):
                #     knowledge_list = KnowledgeService.get_knowledge_from_document(text_docs)
                #     kg_docs = [Document(page_content=kg['name']) for kg in knowledge_list]
                #     index_processor.embedding_documents(dataset, kg_docs)

                self._load(
                    index_processor=index_processor,
                    dataset=dataset,
                    dataset_document=dataset_document,
                    documents=documents
                )
            except DocumentIsDeletedException:
                self.clean_index(dataset, dataset_document)
                DocumentSegment.objects.filter(
                    dataset_document=dataset_document,
                    is_deleted=False,
                ).update(is_deleted=True)
            except Exception as e:
                logger.exception("consume document failed")
                dataset_document.indexing_status = 'error'
                dataset_document.error = str(e)
                dataset_document.stopped_at = timezone.now()
                dataset_document.save(update_fields=['indexing_status', 'error', 'stopped_at'])

    def run_markdown_estimate(self, text, processing_rule) -> list[Document]:
        index_processor = IndexProcessorFactory(IndexType.PARAGRAPH_INDEX.value).init_index_processor()

        text_docs = [Document(page_content=text)]

        file_extension = 'md'
        document_no = 'markdown_estimate'

        documents = self._transform_special(
            index_processor,
            text_docs,
            document_no,
            processing_rule,
            'chinese',
            file_extension,
        )

        return documents

    def clean_index(self, dataset: Dataset, dataset_document: DatasetDocument):
        segments = DocumentSegment.objects.filter(dataset_document=dataset_document)
        index_node_ids = [segment.index_node_id for segment in segments]
        if not index_node_ids:
            return

        batch_size = 100
        batch = math.ceil(len(index_node_ids) / batch_size)
        for i in range(batch):
            _batch_index_node_ids = index_node_ids[i * batch_size: (i + 1) * batch_size]
            index_type = IndexType.PARAGRAPH_INDEX.value
            index_processor = IndexProcessorFactory(index_type).init_index_processor()
            index_processor.clean(dataset, _batch_index_node_ids)

    def _extract(
            self,
            index_processor: BaseIndexProcessor,
            dataset_document: DatasetDocument
    ) -> list[Document]:
        data_source_info = dataset_document.data_source_info

        remote_file_info = None
        if dataset_document.data_source_type == DatasourceType.REMOTE_FILE.value:
            if not data_source_info:
                raise ValueError("no upload file found")
            remote_file_info = data_source_info

        extract_setting = ExtractSetting(
            datasource_type=dataset_document.data_source_type,
            remote_file_info=remote_file_info,
            data_source_info=data_source_info,
            document_model=dataset_document.index_type
        )
        text_docs = index_processor.extract(extract_setting)

        logger.info(f'extract_document_success:[{dataset_document.document_no}]')

        self._update_document_index_status(
            dataset_document=dataset_document,
            after_indexing_status="splitting",
            extra_update_params={
                'word_count': sum(len(text_doc.page_content) for text_doc in text_docs),
                'parsing_completed_at': timezone.now()
            }
        )

        for text_doc in text_docs:
            text_doc.metadata['document_no'] = dataset_document.document_no
            text_doc.metadata['dataset_no'] = dataset_document.dataset.dataset_no

        return text_docs

    def _transform_special(
            self,
            index_processor: BaseIndexProcessor,
            text_docs: list[Document],
            document_no: str,
            process_rule: dict,
            doc_language: str,
            file_extension: str,
    ) -> list[Document]:
        return index_processor.transform_special(
            text_docs,
            document_no=document_no,
            process_rule=process_rule,
            doc_language=doc_language,
            file_extension=file_extension
        )

    def _transform(
            self,
            index_processor: BaseIndexProcessor,
            text_docs: list[Document],
            process_rule: dict,
            dataset_document: DatasetDocument,
    ) -> list[Document]:
        return index_processor.transform(
            text_docs,
            process_rule=process_rule,
            dataset_document=dataset_document
        )

    def _load_segments(
            self,
            dataset: Dataset,
            dataset_document: DatasetDocument,
            documents: list[Document]
    ):
        doc_store = DatasetDocumentStore(
            dataset=dataset,
            dataset_document=dataset_document
        )

        # add document segments
        doc_store.add_documents(documents)

        self._update_document_index_status(
            dataset_document=dataset_document,
            after_indexing_status="indexing",
            extra_update_params={
                'splitting_completed_at': timezone.now(),
            }
        )

        # update segment status to indexing
        self._update_segments_by_document(
            dataset_document=dataset_document,
            update_params={
                'status': "indexing",
                'indexing_at': timezone.now()
            }
        )

    def _load(
            self,
            index_processor: BaseIndexProcessor,
            dataset: Dataset,
            dataset_document: DatasetDocument,
            documents: list[Document]
    ) -> None:
        """
        insert index and update document/segment status to completed
        """
        # chunk nodes by chunk size
        indexing_start_at = time.perf_counter()
        chunk_size = 10

        for i in range(0, len(documents), chunk_size):
            chunk_documents = documents[i:i + chunk_size]
            self._process_chunk(
                index_processor=index_processor,
                chunk_documents=chunk_documents,
                dataset=dataset,
                dataset_document=dataset_document,
            )

        indexing_end_at = time.perf_counter()
        # update document status to completed
        self._update_document_index_status(
            dataset_document=dataset_document,
            after_indexing_status="completed",
            extra_update_params={
                'completed_at': timezone.now(),
                'indexing_latency': indexing_end_at - indexing_start_at,
            }
        )

    def _process_chunk(
            self,
            index_processor: BaseIndexProcessor,
            chunk_documents: list[Document],
            dataset: Dataset,
            dataset_document: DatasetDocument,
    ):
        # 批量处理问答，不集中处理
        batch_size = 100
        batch = math.ceil(len(chunk_documents) / batch_size)

        for i in range(batch):
            _batch_chunk_documents = chunk_documents[i * batch_size:(i + 1) * batch_size]
            # TODO 校验文档状态
            # check document is deleted
            self._check_document_is_deleted(dataset_document.document_no)

            # load index
            index_processor.load(dataset, _batch_chunk_documents)
            document_ids = [document.metadata['doc_id'] for document in _batch_chunk_documents]
            DocumentSegment.objects.filter(
                dataset_document=dataset_document,
                index_node_id__in=document_ids,
                status='indexing'
            ).update(
                status='completed',
                completed_at=timezone.now(),
            )

    def _check_document_is_deleted(self, document_no: str):
        indexing_cache_key = 'document_{}_is_deleted'.format(document_no)
        result = redis_default_client.get(indexing_cache_key)
        if result:
            raise DocumentIsDeletedException()

    def _update_document_index_status(
            self,
            dataset_document: DatasetDocument,
            after_indexing_status: str,
            extra_update_params: dict | None = None
    ) -> None:
        update_params = {
            'indexing_status': after_indexing_status
        }

        if extra_update_params:
            update_params.update(extra_update_params)

        DatasetDocument.objects.filter(id=dataset_document.id).update(
            **update_params,
            modified_time=timezone.now()
        )

    def _update_segments_by_document(
            self,
            dataset_document: DatasetDocument,
            update_params: dict
    ) -> None:
        DocumentSegment.objects.filter(
            dataset_document=dataset_document
        ).update(**update_params)


class DocumentIsDeletedException(Exception):
    pass
