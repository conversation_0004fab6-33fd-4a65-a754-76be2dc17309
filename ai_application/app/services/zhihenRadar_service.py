import time

from app.api.dto import ZhihenRadarDto
from app.models import Conversation, Account, App, Message
import uuid
import json
from app.errors import AppNotFoundError, ContainSensitiveError
from app.constants.app import AppMode
from django.conf import settings
from langchain_openai import ChatOpenAI
from app.models.model import PromptTemplate as PromptTemp
from langchain_core.prompts import PromptTemplate

from app.sensitive_words.utils import contains_sensitive_word


class zhihenRadarService:

    @classmethod
    def add_conversation(cls, app_model, from_account: Account,) -> Conversation:
        """创建新对话并保存到数据库"""
        return Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app=app_model,
            app_model_config=app_model.app_model_config,
            name='zhihenRadar_conversation',
            from_account=from_account,
        )

    @classmethod
    def generate(
            cls,
            dto: ZhihenRadarDto,
            from_account: Account,
    ) -> dict:
        """处理非流式消息生成并保存完整记录"""
        # 获取应用配置
        app_model = App.objects.filter(is_deleted=False, app_type="zhihenRadar").first()
        if not app_model:
            raise AppNotFoundError()

        # 创建对话
        conversation = cls.add_conversation(app_model, from_account)

        # 创建初始消息记录
        new_message = Message.objects.create(
            message_no=str(uuid.uuid4()),
            conversation=conversation,
            app=app_model,
            app_model_config=app_model.app_model_config,
            query=dto.model_dump_json(),  # 存储原始参数
            from_account=from_account,
            userinfo=json.dumps(dto.userinfo, ensure_ascii=False) if dto.userinfo else None,
        )

        is_contain, tips, sensitive_word = contains_sensitive_word(
            f'{dto.question}{dto.answer}',
            app_model.id
        )
        if is_contain:
            new_message.is_exception = True
            new_message.exception_reason = '包含敏感词'
            new_message.is_sensitive = True
            new_message.sensitive_content = sensitive_word
            new_message.save()
            raise ContainSensitiveError()

        try:
            # 验证应用模式
            if app_model.mode != AppMode.COMPLETION.value:
                raise ValueError(f'Invalid app mode {app_model.mode}')

            start_time = time.perf_counter()
            result = zhihenRadarService.getResponse(dto.question, dto.answer)

            if hasattr(result, 'content'):
                # 如果是直接的LLM响应
                new_message.answer = result.content
                new_message.message_tokens = result.usage_metadata.get('input_tokens',0)
                new_message.answer_tokens = result.usage_metadata.get('output_tokens', 0)
                new_message.total_tokens = result.usage_metadata.get('total_tokens', 0)
                new_message.response_latency = time.perf_counter() - start_time
            else:
                print("Not LLM Response")
            #
            # 更新消息状态和内容
            new_message.status = 'normal'
            new_message.save()

            return {'answer': result.content}

        except Exception as e:
            # 错误处理
            new_message.status = 'failed'
            new_message.error = str(e)
            new_message.save()
            raise  # 重新抛出异常以便上层处理

    @classmethod
    def getResponse(cls, question, answer):

        llm = ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="deepseek-r1-250120"
        )

        template_obj = PromptTemp.objects.get(app_no="zhihenRadar")

        template = template_obj.prompt_content

        prompt = PromptTemplate.from_template(template)

        input_data = {
            "input": answer,
            "question": question
        }

        llm_chain = prompt | llm

        result = llm_chain.invoke(input_data)

        return result