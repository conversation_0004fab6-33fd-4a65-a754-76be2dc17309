from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate


class AnalysisQuestionFeature(BaseFeature):

    def __init__(self):
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no='knowledge_deep_question').first()
        if not prompt_template:
            raise Exception('prompt template not found')

        self.prompt_template = prompt_template

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            question: dict,
            stream: bool,
            prompt_content: str = '',
    ):
        """
        :param application_generate_entity:
        :param question:
        :param stream:
        :param prompt_content:
        {"question": "", "analysis": ""}
        :return:
        """
        user_inputs = application_generate_entity.inputs.copy()
        user_inputs['problem'] = question["question"]
        user_inputs['answer'] = question["analysis"]

        prompt_template = self.prompt_template
        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=prompt_content
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        # 调用模型
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=stream
        )
