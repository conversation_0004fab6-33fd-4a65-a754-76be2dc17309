import re
import concurrent.futures
from decimal import Decimal

from django.utils import timezone

from app.api.dto import ChatMessageDto
from app.models import (
    STUserPaperAnswer, App, STUserPaperQuestionAnswer
)
from app.services.app_generate_service import AppGenerateService
from app.services.shuati_app.learn_stage import LearnStageClient
from app.services.shuati_app.paper_composing import PaperComposingClient
from app.services.shuati_app.user_knowledge_mastery import (
    update_user_knowledge_mastery_level
)
from django_ext.utils.string_utils import parse_int


def gen_subjective_answer_report(question_answer: STUserPaperQuestionAnswer):
    question_answer.report_status = 'ing'
    question_answer.save(update_fields=['report_status'])
    subjective_answer = question_answer.get_subjective_answer()
    if not subjective_answer:
        question_answer.report_status = 'fail'
        question_answer.save(update_fields=['report_status'])
        return

    app = App.objects.get(app_type='shuati_subjective_report')
    chat_dto = ChatMessageDto(
        app_id=app.app_no,
        inputs={
            'question_answer_id': question_answer.id
        },
        query=subjective_answer.text,
        file_objs=subjective_answer.images,
        stream=False
    )
    response = AppGenerateService.generate(chat_dto)
    # response = {'is_success': True, 'answer': '得分率：50%\n这是主观题报告'}

    question_answer.refresh_from_db()
    is_success = response.get('is_success', False)
    if not is_success:
        question_answer.report_status = 'fail'
        question_answer.save(update_fields=['report_status'])
        return

    report = response.get('answer', '')

    pattern = r"得分率[：:]?\s*(\d+)"
    result = re.search(pattern, report)
    if result:
        score_rate = parse_int(result.group(1))
    else:
        score_rate = 0

    question_score = Decimal(question_answer.question.get_question_score())
    answer_score = question_score if score_rate >= 80 else 0

    question_answer.report_status = 'success'
    question_answer.report = report
    question_answer.score_rate = score_rate
    question_answer.answer_score = answer_score
    question_answer.save(update_fields=['report_status', 'report', 'score_rate', 'answer_score'])

    paper_answer = question_answer.answer

    # 更新试卷分数和主观题分数
    answered_score = paper_answer.answered_score + answer_score
    subjective_score = paper_answer.subjective_score + answer_score
    paper_answer.answered_score = round(answered_score, 1)
    paper_answer.subjective_score = round(subjective_score, 1)
    paper_answer.save(update_fields=['answered_score', 'subjective_score'])

    # 判断试卷是不是都答完了
    is_finished = paper_answer.check_is_finished()
    if is_finished:
        update_answer_stat(paper_answer)
        batch_after_paper_submit(paper_answer)


def gen_answer_report(paper_answer: STUserPaperAnswer):
    paper_answer.report_status = 'ing'
    paper_answer.save(update_fields=['report_status'])

    app = App.objects.get(app_type='shuati_answer_report')
    chat_dto = ChatMessageDto(
        app_id=app.app_no,
        inputs={},
        query=str(paper_answer.id),
        stream=False
    )
    response = AppGenerateService.generate(chat_dto)
    # response = {'is_success': True, 'answer': '这是试卷报告'}

    paper_answer.refresh_from_db()
    is_success = response.get('is_success', False)
    if not is_success:
        paper_answer.report_status = 'fail'
    else:
        paper_answer.report_status = 'success'
        paper_answer.report = response.get('answer', '')
    paper_answer.save()


def update_answer_stat(paper_answer: STUserPaperAnswer):
    update_user_knowledge_mastery_level(paper_answer)
    LearnStageClient(paper_answer).update_round_analysis()


def get_next_paper(paper_answer: STUserPaperAnswer):
    PaperComposingClient(
        subject_id=paper_answer.paper.subject_id,
        core_course_code=paper_answer.paper.core_course_code,
        user_id=paper_answer.user_id
    ).get_paper()


def batch_after_paper_submit(paper_answer: STUserPaperAnswer):
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # 提交两个任务并发执行
        paper_future = executor.submit(get_next_paper, paper_answer=paper_answer)
        report_future = executor.submit(gen_answer_report, paper_answer=paper_answer)

        # 等待两个任务都完成
        paper_future.result()
        report_future.result()
