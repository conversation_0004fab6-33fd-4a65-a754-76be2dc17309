import base64
import hashlib
import hmac
import logging
import time
import urllib.parse

import requests
import json

from django.conf import settings

logger = logging.getLogger(__name__)


def send_dingtalk_message_by_env(message, at_all=False):
    logger.error(message)
    if settings.ENVIRONMENT == settings.ENV_PRODUCT:
        send_dingtalk_message(message, at_all)


def send_dingtalk_message_by_env2(message, at_all=False):
    logger.error(message)
    if settings.ENVIRONMENT in (settings.ENV_PRODUCT, settings.ENV_TEST):
        send_dingtalk_message(message, at_all)


def send_dingtalk_message(message, at_all=False):
    webhook_url = settings.DINGTALK_WEBHOOK
    # 构建钉钉消息
    data = {
        "msgtype": "text",
        "text": {"content": message},
        "at": {"isAtAll": at_all}
    }
    # 设置请求头
    headers = {
        "Content-Type": "application/json;charset=utf-8"
    }
    sign, timestamp = sign_message()
    # 发送POST请求
    try:
        response = requests.post(
            webhook_url,
            params={'timestamp': timestamp, 'sign': sign},
            headers=headers,
            data=json.dumps(data)
        )
        return response.json()
    except Exception as e:
        logger.error(f'send_dingtalk_message_error: {e}')
        pass


def sign_message():
    timestamp = str(round(time.time() * 1000))
    secret = settings.DINGTALK_SECRET
    secret_enc = secret.encode('utf-8')
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    string_to_sign_enc = string_to_sign.encode('utf-8')
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))

    return sign, timestamp
