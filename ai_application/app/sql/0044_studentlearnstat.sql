--
-- Create model StudentLearnStat
--
CREATE TABLE `ai_student_learn_stat` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_id` varchar(100) NOT NULL, `task_id` varchar(36) NOT NULL UNIQUE, `query` longtext NULL, `analysis` longtext NULL, `status` varchar(100) NOT NULL, `fail_reason` longtext NULL);
CREATE INDEX `ai_student_learn_stat_add_time_c6967a5c` ON `ai_student_learn_stat` (`add_time`);
CREATE INDEX `ai_student_learn_stat_user_id_707b594d` ON `ai_student_learn_stat` (`user_id`);