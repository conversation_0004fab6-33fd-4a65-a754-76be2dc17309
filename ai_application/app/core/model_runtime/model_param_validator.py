from app.core.app_parameter_manager import AppParameterManager
from app.core.entities.app_entities import AppParameterRule
from app.models import AppModelConfig


class ModelParamValidator:

    def validate(
            self,
            user_inputs: dict,
            app_model_config: AppModelConfig
    ) -> dict:
        model_params = AppParameterManager().get_model_params()

        # TODO 临时处理
        support_params = app_model_config.support_params
        support_params.extend(['temperature', 'top_p', 'max_tokens'])
        filtered_user_inputs = {k: v for k, v in user_inputs.items() if k in support_params and v is not None}

        model_conf = {}
        for rule in model_params:
            model_conf[rule.code] = rule.default
            if rule.code in filtered_user_inputs:
                input_value = filtered_user_inputs[rule.code]
                model_conf[rule.code] = AppParameterRule.validate_field(input_value, rule)
        return model_conf
