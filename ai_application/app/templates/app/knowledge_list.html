<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App List</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        table {
            border-collapse: collapse; /* 合并边框 */
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd; /* 单元格边框 */
            text-align: left; /* 文本左对齐 */
            padding: 8px; /* 单元格内边距 */
        }
        th {
            background-color: #f2f2f2; /* 表头背景颜色 */
        }
        tr:nth-child(even) {
            background-color: #f9f9f9; /* 偶数行背景颜色 */
        }
        tr:hover {
            background-color: #d3d3d3; /* 鼠标悬停行背景颜色 */
        }
        th, td {
            vertical-align: middle; /* 文本垂直居中 */
        }
    </style>
</head>
<body>
    <h1>知识点列表</h1>
    <p><a href="{% url 'knowledge_search' %}">去搜索</a></p>
    <table>
        <tr>
            <th class="title" style="width: 200px;">名称</th>
            <th>定义</th>
        </tr>
        {% for item in knowledge_list %}
        <tr>
            <td>{{ item.name }}</td>
            <td>{{ item.definition }}</td>
        </tr>
        {% endfor %}
    </table>

</body>
</html>
