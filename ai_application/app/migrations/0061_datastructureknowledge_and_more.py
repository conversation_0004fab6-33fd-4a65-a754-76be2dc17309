# Generated by Django 4.2 on 2025-08-12 15:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0060_subjecttoknowledge_texttovideo_knowledgevideo'),
    ]

    operations = [
        migrations.CreateModel(
            name='DataStructureKnowledge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_1', models.CharField(max_length=255, verbose_name='一级学科')),
                ('subject_2', models.Char<PERSON><PERSON>(max_length=255, verbose_name='二级学科')),
                ('categories', models.Char<PERSON>ield(max_length=255, verbose_name='章节')),
                ('knowledge_points', models.CharField(max_length=100, verbose_name='知识点')),
            ],
            options={
                'verbose_name': '数据结构知识点',
                'verbose_name_plural': '数据结构知识点',
                'db_table': 'ai_data_structure_knowledge',
            },
        ),
        migrations.AddField(
            model_name='knowledgevideo',
            name='core_course_name',
            field=models.CharField(blank=True, max_length=20, verbose_name='二级学科名称'),
        ),
        migrations.AddField(
            model_name='knowledgevideo',
            name='fail_reason',
            field=models.CharField(blank=True, max_length=255, verbose_name='失败原因'),
        ),
        migrations.AddField(
            model_name='knowledgevideo',
            name='knowledge_name',
            field=models.CharField(blank=True, max_length=20, verbose_name='知识点名称'),
        ),
        migrations.AddField(
            model_name='knowledgevideo',
            name='status',
            field=models.CharField(choices=[('not_start', '未开始'), ('processing', '校对中'), ('success', '成功'), ('fail', '失败')], default='not_start', max_length=20, verbose_name='任务状态'),
        ),
        migrations.AlterField(
            model_name='knowledgevideo',
            name='name',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='app.knowledgesimple', verbose_name='对应知识点'),
        ),
    ]
