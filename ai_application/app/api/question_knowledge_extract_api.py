# 阿纪
import http.client
import json
def question_knowledge_extract(question_id,question_content):
    conn = http.client.HTTPSConnection("api-dev.yantucs.com")
    payload = json.dumps({
       "subject": "math",
       "question_id": question_id,
       "question_content": question_content
    })
    headers = {
       'x-api-key': 'ak_bc1ea37f68a84d8b',
        'x-signature': '91e9b4e95b65e5fc14013baeb7e5bfe252b1db58071c01b5b19b889fe85ddfdb',
        'timestamp': '1720405843',
        'nonce': 'ld8h259m',
        'x-sign-debug': '1',
       'Content-Type': 'application/json'
    }
    conn.request("POST", "/api/zhizhou/v1/question_knowledge_extract", payload, headers)
    res = conn.getresponse()
    data = res.read()
    response =  json.loads(data.decode("utf-8"))
    # 检查响应状态码和是否成功
    if response.get('code') == 200 and response.get('data', {}).get('is_success'):
        return response.get('data', {}).get('knowledge_list', [])
    else:
        # 处理错误情况
        print(f"Error: {response.get('message', 'Unknown error')}")
        return []