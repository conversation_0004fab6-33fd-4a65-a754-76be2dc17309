import numpy as np
import pandas as pd
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()
from app.models import ExamAnalysisKnowledgePointWithStats

def compute_subject_coefficients(
    df,
    subject_col="科目",
    kp_col="知识点",
    features={
        "freq_total": "考频",
        "freq_mc": "选择题考频",
        "freq_subj": "主观题考频",
        "diff_total": "平均难度",
        "diff_mc": "选择题难度",
        "diff_subj": "主观题难度",
    },
    freq_gate=1.0,   # 频率类门控系数 λ_freq
    ease_gate=1.0,   # 易度类门控系数 λ_ease
    eps=1e-6
):
    """
    输入 df：每行是一个知识点，包含所属科目与6个特征。
    返回：
      - subject_coef: 每科系数 α_c（和为1）
      - feature_weights: 熵权法得到的6个特征权重
      - kp_scores: 每个知识点的贡献度 s_i
    """
    # 1) 构造正向指标
    work = df.copy()
    work["总体易度"] = 1.0 / (work[features["diff_total"]] + eps)
    work["选择题易度"] = 1.0 / (work[features["diff_mc"]] + eps)
    work["主观题易度"] = 1.0 / (work[features["diff_subj"]] + eps)

    pos_cols = [
        features["freq_total"],
        features["freq_mc"],
        features["freq_subj"],
        "总体易度", "选择题易度", "主观题易度"
    ]

    # 2) Min-Max 归一化
    X = work[pos_cols].astype(float).copy()
    for c in X.columns:
        col = X[c].values
        mn, mx = np.nanmin(col), np.nanmax(col)
        if np.isclose(mx - mn, 0.0):
            X[c] = 0.0
        else:
            X[c] = (col - mn) / (mx - mn)

    # 3) 熵权法
    X_mat = X.values
    # 避免全零列
    col_sums = X_mat.sum(axis=0, keepdims=True)
    col_sums[col_sums == 0] = 1.0
    P = X_mat / col_sums
    n = X_mat.shape[0]
    tiny = 1e-12
    E = -(1/np.log(n)) * np.sum(P * np.log(P + tiny), axis=0)  # 熵
    D = 1 - E  # 差异系数
    # 门控：强化频率或易度
    gates = np.array([freq_gate, freq_gate, freq_gate, ease_gate, ease_gate, ease_gate])
    D = D * gates
    D_sum = D.sum()
    if np.isclose(D_sum, 0.0):
        w = np.ones_like(D) / len(D)
    else:
        w = D / D_sum
    feature_weights = pd.Series(w, index=pos_cols, name="权重")

    # 4) 知识点贡献度
    s = (X.values * w).sum(axis=1)
    kp_scores = work[[subject_col, kp_col]].copy()
    kp_scores["贡献度"] = s

    # 5) 聚合到学科
    subj_sum = kp_scores.groupby(subject_col)["贡献度"].sum().rename("学科原始分")
    total = subj_sum.sum()
    if np.isclose(total, 0.0):
        subject_coef = (subj_sum * 0 + 1.0 / len(subj_sum)).rename("学科系数")
    else:
        subject_coef = (subj_sum / total).rename("学科系数")

    return subject_coef.sort_values(ascending=False), feature_weights, kp_scores

def allocate_targets(subject_coef, target_total=114, subject_max=None):
    """
    根据学科系数分配目标分，并考虑满分上界。
    subject_coef: pd.Series(index=科目, values=α_c，和为1)
    subject_max:  dict 或 Series，给出各科满分（可选）
    """
    alloc = subject_coef * target_total
    if subject_max is None:
        return alloc.rename("目标分").round(2)

    alloc = alloc.copy()
    subject_max = pd.Series(subject_max)
    # 先截断
    capped = np.minimum(alloc, subject_max)
    overflow = alloc.sum() - capped.sum()
    # 循环把溢出部分按剩余空间与系数再分配
    while overflow > 1e-9:
        room = subject_max - capped
        positive_room = room.clip(lower=0)
        weights = (positive_room > 1e-9) * subject_coef
        if weights.sum() <= 1e-12:
            break
        delta = overflow * (weights / weights.sum())
        delta = np.minimum(delta, positive_room)
        capped = capped + delta
        overflow = alloc.sum() - capped.sum()
    return capped.rename("目标分").round(2)

if __name__ == "__main__":


    knowledges = {
        "科目":[],
        "知识点":[],
        "考频":[],
        "平均难度":[],
        "选择题考频":[],
        "选择题难度":[],
        "主观题考频":[],
        "主观题难度":[],
    }

    subjects = ["计算机组成原理","计算机网络","操作系统","数据结构"]

    for subject in subjects:
        queryset = ExamAnalysisKnowledgePointWithStats.objects.filter(subject=subject).values()
        for item in queryset:
            knowledges["科目"].append(item["subject"])
            knowledges["知识点"].append(item["point_name"])
            knowledges["考频"].append(item["exam_count"])
            knowledges["平均难度"].append(item["avg_difficulty"])
            knowledges["选择题考频"].append(item["choice_count"])
            knowledges["选择题难度"].append(item["choice_avg_difficulty"])
            knowledges["主观题考频"].append(item["comprehensive_count"])
            knowledges["主观题难度"].append(item["comprehensive_avg_difficulty"])

    example = pd.DataFrame(knowledges)

    subject_coef, feat_w, kp_scores = compute_subject_coefficients(example)

    print("=== 特征权重（熵权法） ===")
    print(feat_w.to_string())
    print("\n=== 学科系数 α_c（和为1） ===")
    print(subject_coef.to_string())

    subject_max = {"计算机组成原理":45, "计算机网络":25, "操作系统":35, "数据结构":45}
    targets = allocate_targets(subject_coef, target_total=90, subject_max=subject_max)
    print("\n=== 建议目标分配（考虑满分上限） ===")
    print(targets.to_string())