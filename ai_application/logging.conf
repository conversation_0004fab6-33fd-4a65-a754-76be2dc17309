[loggers]
keys=root, gunicorn.error

[handlers]
keys=console, error_file

[formatters]
keys=generic

[logger_root]
handlers=console

[logger_gunicorn.error]
handlers=error_file
propagate=1
qualname=gunicorn.error

[handler_console]
class=StreamHandler
formatter=generic
args=(sys.stdout, )

[handler_error_file]
class=logging.FileHandler
formatter=generic
args=('./log/error.log',)

[handler_access_file]
class=logging.FileHandler
formatter=access
args=('./log/all.log',)

[formatter_generic]
format=[%(asctime)s: %(levelname)s/%(threadName)s:%(thread)d] [%(name)s:%(lineno)d] [%(module)s:%(funcName)s]: %(message)s
datefmt=%Y-%m-%d %H:%M:%S
class=logging.Formatter
