import logging
from functools import wraps

from django_ext.base_dto_model import DtoBuildException

logger = logging.getLogger(__name__)


def catch_error(default_valve: any = None):
    def dec(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                response = func(*args, **kwargs)
                return response
            except Exception as e:
                logger.exception(e)
                return default_valve
        return wrapper
    return dec


def catch_res_error(default_valve: any = None):
    def dec(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                response = func(*args, **kwargs)
                return response
            except DtoBuildException as e:
                logger.exception(e)
                return default_valve
        return wrapper
    return dec
