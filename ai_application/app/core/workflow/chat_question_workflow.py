import re
import time
from pathlib import Path

from ai_application import settings
from app.constants.app import AppMode
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.features.analysis_question import AnalysisQuestionFeature
from app.core.features.chat_question_judge import ChatQuestionJudgeFeature
from app.core.invoke_llm import query_llm_by_prompt, get_llm_model_config
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError, QuestionMessageNotQuestionError
from app.libs.baidu_ocr import BaiduOcr
from app.models import Message, QuestionNewRecord


class ChatQuestionWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

        self.question_prompts = self.inputs.get('question_prompts') or {}
        is_debug = self.inputs.get('is_debug', False)
        if is_debug:
            self.question_prompts = self.inputs.get('debug_question_prompts')

        self.analysis_question_feature = AnalysisQuestionFeature()
        self.default_query = '请解答这个题目。'
        self.not_question_err = '看起来您上传的图片内容可能与具体的学科问题不完全匹配。为了确保我们能够准确地为您提供帮助，请重新上传相关的图片或详细描述您的问题。谢谢！'

    def run(self):
        query = self.application_generate_entity.query
        file_objs = self.application_generate_entity.file_objs
        file_url = self._check_pic_file(file_objs)

        original_question_query = ''
        if file_url:
            original_question_query = self._get_pic_content(file_url)
            if not original_question_query:
                raise QuestionMessageNotQuestionError(detail=self.not_question_err)

        # 处理demo数据
        if self.is_debug:
            # demo数据处理trace
            original_question_query = self.inputs.get('debug_user_question')
            self.tracing_log.add_tracing(
                tracing_type='question_ocr_result',
                query=file_url,
                answer=original_question_query,
                latency=0,
            )

        if not query and not original_question_query:
            raise ParameterError(detail=self.not_question_err)

        extra_query = ''
        if original_question_query:
            # 判读是否是完整题目，符合标准
            self._check_pic_content_is_question(original_question_query)
            # 如果既有图片，又有用户输入，则当作两条对话输出
            if query:
                extra_query = query
        else:
            # 如果仅有用户输入，则将其当作问题去搜索
            original_question_query = query

        question_info = ChatQuestionJudgeFeature().run(
            self.application_generate_entity,
            original_question_query,
            tracing_log=self.tracing_log
        )
        if not question_info:
            self._save_not_found_question(file_url, original_question_query)
            self._raise_chat_question_no_result()

        self._publish_workflow_custom_message(f'找到了与您的输入相似度最高的题目↓：\n\n{question_info["question"]}\n\n---\n\n')
        self._publish_workflow_custom_message('针对上面的题目，为您提供如下解题参考↓：\n\n')
        # 输出问题解析
        result = self._analysis_question(question_info)

        # TODO 第一期忽略用户额外输入
        # if extra_query:
        #     self._answer_extra_question(result, extra_query)
        self._publish_workflow_end_message()

    def _analysis_question(self, question_info: dict) -> LLMResult:
        question_found_prompt = self.question_prompts.get('question_found_prompt', '')
        result = self.analysis_question_feature.run(
            self.application_generate_entity,
            question_info,
            stream=True,
            prompt_content=question_found_prompt
        )
        tracing_info = {
            'tracing_type': 'analysis_question', 'query': question_info.get('question')
        }
        return self._handle_node_generate_response(result, tracing_info=tracing_info)

    def _answer_extra_question(
            self,
            analysis_question_result: LLMResult,
            extra_query: str,
    ) -> LLMResult:
        history_messages = [*analysis_question_result.prompt_messages, analysis_question_result.message]

        # 额外回答的模型配置，与问题解析的一致
        prompt_template = self.analysis_question_feature.prompt_template
        model_conf = get_llm_model_config(
            app_model_conf=self.application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        result = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.CHAT,
            pre_prompt=self.message.conversation.pre_prompt,
            query=extra_query,
            history_messages=history_messages,
            stream=True,
        )

        tracing_info = {
            'tracing_type': 'analysis_question_extra', 'query': extra_query,
        }
        return self._handle_node_generate_response(result, tracing_info=tracing_info)

    def _check_pic_file(self, file_objs: list) -> str:
        if file_objs:
            # 目前只获取第一个文件，格式只支持jpg和jpeg
            file_url = file_objs[0]
            suffix = Path(file_url).suffix
            file_extension = suffix.lower()
            if file_extension in ['.jpg', '.jpeg', '.png']:
                return file_url
        return ''

    def _get_pic_content(self, file_url) -> str:
        start_time = time.perf_counter()

        res = BaiduOcr().basic_accurate(image_file_url=file_url)
        content = '\n'.join([i['words'] for i in res])

        latency = time.perf_counter() - start_time

        self.tracing_log.add_tracing(
            tracing_type='question_ocr_result',
            query=file_url,
            answer=content,
            latency=latency,
        )
        return content

    def _check_pic_content_is_question(self, content: str):
        """
        判断图片内容是否是题目
        """
        if not content:
            self._raise_chat_question_no_result()

        cleaned_content = re.sub(r'[^\w\s]', '', content)
        if len(cleaned_content) <= settings.JUDGE_QUESTION_MIN_LEN:
            self._raise_chat_question_no_result()

    def _save_not_found_question(self, question_pic_url, question_content):
        QuestionNewRecord.objects.create(
            message=self.message,
            question_pic_url=question_pic_url,
            question_content=question_content
        )

    def _raise_chat_question_no_result(self):
        # 接受配置的错误输出
        not_found_err = self.question_prompts.get('question_not_found_prompt', '')
        if not not_found_err:
            not_found_err = self.not_question_err

        raise QuestionMessageNotQuestionError(detail=not_found_err)
