--
-- Add field prompt_templates to appmodelconfig
--
ALTER TABLE `ai_app_model_config` ADD COLUMN `prompt_templates` json NULL;
--
-- Add field document_nos to conversation
--
ALTER TABLE `ai_conversation` ADD COLUMN `document_nos` json NULL;
--
-- Add field from_biz_id to conversation
--
ALTER TABLE `ai_conversation` ADD COLUMN `from_biz_id` varchar(32) DEFAULT '' NOT NULL;
--
-- Add field invoke_from to conversation
--
ALTER TABLE `ai_conversation` ADD COLUMN `invoke_from` varchar(32) DEFAULT 'api' NOT NULL;
--
-- Add field from_biz_id to message
--
ALTER TABLE `ai_message` ADD COLUMN `from_biz_id` varchar(32) DEFAULT '' NOT NULL;
--
-- Add field invoke_from to message
--
ALTER TABLE `ai_message` ADD COLUMN `invoke_from` varchar(32) DEFAULT 'api' NOT NULL;
--
-- Add field custom_variables to prompttemplate
--
<PERSON>TER TABLE `ai_prompt_template` ADD COLUMN `custom_variables` json NULL;
--
-- Add field special_variables to prompttemplate
--
ALTER TABLE `ai_prompt_template` ADD COLUMN `special_variables` json NULL;