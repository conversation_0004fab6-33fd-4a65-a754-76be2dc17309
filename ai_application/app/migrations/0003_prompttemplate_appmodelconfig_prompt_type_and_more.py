# Generated by Django 4.2 on 2024-09-21 02:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0002_dataset_datasetdocument_embedding_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PromptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(blank=True, max_length=32, verbose_name='标题')),
                ('app_no', models.CharField(blank=True, db_index=True, max_length=32, verbose_name='应用id')),
                ('prompt_content', models.TextField(verbose_name='提示词内容')),
                ('debug_prompt_content', models.TextField(null=True, verbose_name='调试提示词内容')),
                ('is_enabled', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '提示词模版配置',
                'verbose_name_plural': '提示词模版配置',
                'db_table': 'ai_prompt_template',
            },
        ),
        migrations.AddField(
            model_name='appmodelconfig',
            name='prompt_type',
            field=models.CharField(choices=[('for_params', '支持参数'), ('for_template', '支持模板')], default='for_params', max_length=32, verbose_name='提示词类型'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='conversation_hash',
            field=models.CharField(blank=True, max_length=255, verbose_name='会话Hash'),
        ),
        migrations.AddField(
            model_name='message',
            name='conversation_hash',
            field=models.CharField(blank=True, max_length=255, verbose_name='会话Hash'),
        ),
        migrations.AddField(
            model_name='message',
            name='inputs',
            field=models.JSONField(null=True, verbose_name='表单参数'),
        ),
    ]
