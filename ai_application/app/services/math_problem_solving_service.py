
from app.api.dto import ChatMessageDto
from app.models import Account, PromptTemplate, InvokeFrom
from app.services.app_generate_service import AppGenerateService


class MathProblemSolvingService:

    @classmethod
    def get_img_result(cls, image_url: list ,account: Account) -> dict:
        template = PromptTemplate.objects.filter(app_no='math_problem_recognition').first()

        dto = ChatMessageDto(
            app_id='math_problem_recognition',
            inputs={
                "max_tokens": 1000,
                "temperature": 0.3,
                'top_p': 0.5,
                'prompt_template': template.id,

            },
            file_objs=image_url,
            stream=False,
        )
        print(f"打印出的files：{dto}")
        response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)

        img_result = response.get('answer', '')
        return {
            "img_result": img_result,
        }

    @classmethod
    def get_math_result(cls, question: str, account: Account) -> dict:
        template = PromptTemplate.objects.filter(app_no='math_problem_solving').first()
        dto = ChatMessageDto(
            app_id='math_problem_solving',
            inputs={
                "max_tokens": 1500,
                "temperature": 0.3,
                'top_p': 0.5,
                'prompt_template': template.id
            },
            query=question,
            stream=False,
        )
        response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)
        response_content = response.get('answer', '')

        return {
            "result": response_content,
        }
