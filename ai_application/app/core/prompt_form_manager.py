from app.core.entities.app_entities import AppParameterRule
from app.core.prompt.prompt_form import prompt_form_instance


class PromptFormManager:

    def get_prompt_params(self) -> list[AppParameterRule]:
        prompt_parameter_rules = prompt_form_instance.get_form_schema().parameter_rules
        prompt_params = []
        for rule in prompt_parameter_rules:
            prompt_params.append(AppParameterRule(
                code=rule.name,
                label=rule.label,
                type=rule.type,
                desc=rule.help,
                placeholder=rule.placeholder,
                required=rule.required,
                default=rule.default,
                max_length=rule.max_length,
                options=rule.options,
            ))
        return prompt_params
