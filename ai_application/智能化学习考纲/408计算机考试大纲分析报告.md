## 考研408计算机考试大纲分析报告


#### **一、各科目分值分布及重要程度分析**  
考研408试卷总分150分，科目分值分布及重要程度如下：  
- **数据结构**：45分（核心科目，算法设计与实现为综合题重点）  
- **计算机组成原理**：45分（核心科目，硬件底层原理与计算为难点）  
- **操作系统**：35分（重要科目，进程管理与内存管理为高频考点）  
- **计算机网络**：25分（次重要科目，TCP/IP协议栈为核心）  


#### **二、各科目考点分析**  

### **（一）数据结构**  

| **章节**               | **考点内容**                                                                 | **标签** | **考试题型**       | **难度等级** |  
|------------------------|------------------------------------------------------------------------------|--------|--------------------|----------|  
| 一、基本概念           | 数据结构的基本概念                                                           | 基础     | 单选               | ★        |  
|                        | 算法的基本概念（时间/空间复杂度分析）                                       | 重要     | 单选、综合题（算法分析） | ★★★★★    |  
| 二、线性表             | 线性表的基本概念                                                             | 核心     | 单选               | ★★★★★    |  
|                        | 线性表的实现（顺序存储、链式存储）                                           | 重要     | 单选、综合题（代码实现） | ★★★★     |  
|                        | 线性表的应用                                                                 | 了解     | 单选               | ★★★★     |  
| 三、栈、队列和数组     | 栈和队列的基本概念、顺序/链式存储结构                                         | 重要     | 单选               | ★★★      |  
|                        | 多维数组的存储、特殊矩阵的压缩存储                                           | 重要     | 单选（地址计算）   | ★★       |  
|                        | 栈、队列和数组的应用（表达式求值、缓冲队列）                                 | 重要     | 单选、综合题       | ★★★      |  
| 四、树与二叉树         | 树的基本概念                                                                 | 基础     | 单选               | ★★★      |  
|                        | 二叉树的定义、特征、顺序/链式存储结构                                         | 重要     | 单选               | ★★★      |  
|                        | 二叉树的遍历（前中后序、层次遍历）                                           | 核心     | 单选、综合题（算法设计） | ★★★      |  
|                        | 线索二叉树的基本概念和构造                                                   | 了解     | 单选               | ★★★      |  
|                        | 树/森林的存储结构、与二叉树的转换、遍历                                     | 重要     | 单选               | ★★★      |  
|                        | 哈夫曼树和哈夫曼编码                                                         | 核心     | 单选、综合题（编码构造） | ★★★      |  
|                        | 并查集及其应用                                                               | 重要     | 单选、综合题（操作实现） | ★★★      |  
|                        | 堆及其应用（堆排序、优先级队列）                                             | 核心     | 单选、综合题（排序实现） | ★★★      |  
| 五、图                 | 图的基本概念（顶点、边、度、路径等）                                         | 基础     | 单选               | ★★★      |  
|                        | 图的存储（邻接矩阵、邻接表）                                                 | 核心     | 单选、综合题（存储实现） | ★★★      |  
|                        | 邻接多重表、十字链表                                                         | 了解     | 单选               | ★★★★     |  
|                        | 图的遍历（深度优先DFS、广度优先BFS）                                         | 核心     | 单选、综合题（遍历应用） | ★★★      |  
|                        | 最小（代价）生成树（Prim、Kruskal算法）                                      | 核心     | 单选、综合题（算法实现） | ★★★★     |  
|                        | 最短路径（Dijkstra、Floyd算法）                                              | 核心     | 单选、综合题（路径计算） | ★★★★     |  
|                        | 拓扑排序                                                                     | 重要     | 单选、综合题（排序过程） | ★★★      |  
|                        | 关键路径                                                                     | 重要     | 综合题             | ★★★★     |  
| 六、查找               | 查找的基本概念                                                               | 基础     | 单选               | ★★★★     |  
|                        | 顺序查找、分块查找                                                           | 了解     | 单选               | ★★★★★    |  
|                        | 折半查找（算法及性能分析）                                                   | 重要     | 单选               | ★★★      |  
|                        | 二叉排序树（插入、删除、查找）                                               | 重要     | 单选、综合题       | ★★★★     |  
|                        | 平衡二叉树（AVL树旋转操作）                                                 | 重要     | 单选               | ★★★      |  
|                        | 红黑树的基本概念                                                             | 了解     | 单选               | ★        |  
|                        | B树及其基本操作、B+树的基本概念                                             | 重要     | 单选               | ★★       |  
|                        | 散列（Hash）表（构造方法、冲突处理、性能分析）                               | 核心     | 单选、综合题       | ★★★      |  
|                        | 字符串模式匹配（KMP算法）                                                   | 重要     | 单选               | ★★★      |  
|                        | 查找算法的分析及应用                                                       | 了解     | 单选               | ★        |  
| 七、排序               | 排序的基本概念（稳定性、时间/空间复杂度）                                   | 基础     | 单选               | ★★★      |  
|                        | 直接插入、折半插入、起泡、简单选择排序                                       | 了解     | 单选               | ★★★      |  
|                        | 希尔排序                                                                     | 重要     | 单选               | ★★★      |  
|                        | 快速排序（算法、性能分析）                                                   | 核心     | 单选、综合题（排序实现） | ★★★      |  
|                        | 堆排序（建堆、排序过程）                                                     | 核心     | 单选、综合题       | ★★★      |  
|                        | 二路归并排序                                                               | 核心     | 单选、综合题       | ★★★      |  
|                        | 基数排序                                                                     | 了解     | 单选               | ★★★      |  
|                        | 外部排序                                                                     | 了解     | 单选               | ★★★★     |  
|                        | 排序算法的分析和应用（适用场景对比）                                         | 重要     | 单选、综合题       | ★★★★     |  


### **（二）计算机组成原理**  

| **章节**               | **考点内容**                                                                 | **标签** | **考试题型**       | **难度等级** |  
|------------------------|------------------------------------------------------------------------------|--------|--------------------|----------|  
| 一、计算机系统概述     | 计算机系统层次结构（基本组成、软硬件关系、“存储程序”工作方式）               | 基础     | 单选               | ★★★★★    |  
|                        | 计算机性能指标（吞吐量、响应时间、CPU时钟周期、CPI、MIPS、MFLOPS等）         | 重要     | 单选、计算         | ★★★★     |  
| 二、数据的表示和运算   | 数制与编码（进位计数制转换、定点数编码）                                     | 基础     | 单选               | ★★★      |  
|                        | 运算方法和运算电路（加法器、ALU、补码加减运算、乘除法基本原理）               | 重要     | 单选、计算         | ★★★★     |  
|                        | 整数的表示和运算                                                             | 了解     | 单选               | ★        |  
|                        | 浮点数的表示（IEEE 754标准）、浮点数的加/减运算                             | 核心     | 单选、计算（格式/运算步骤） | ★★★★     |  
| 三、存储器层次结构     | 存储器的分类、层次化存储器的基本结构                                         | 基础     | 单选               | ★★★      |  
|                        | SRAM、DRAM、Flash存储器的基本原理                                           | 基础     | 单选               | ★★★★★    |  
|                        | 主存储器（DRAM芯片、多模块存储器、CPU与主存的连接）                           | 重要     | 单选               | ★★★★     |  
|                        | 高速缓冲存储器（Cache）的基本原理、映射方式（直接/全相联/组相联）、替换算法（LRU/FIFO）、写策略 | 核心     | 单选、综合题（地址映射/命中率计算） | ★★★★★    |  
|                        | 虚拟存储器的基本概念、页式虚拟存储器（页表、地址转换、TLB）                   | 核心     | 单选、综合题（地址转换） | ★★★★★    |  
|                        | 段式、段页式虚拟存储器                                                       | 了解     | 单选               | ★        |  
| 四、指令系统           | 指令格式（操作码、地址码）                                                   | 重要     | 单选（字段含义分析） | ★★★★     |  
|                        | 寻址方式（立即数、直接、间接、变址、基址寻址等）                             | 核心     | 单选（有效地址计算） | ★★★★     |  
|                        | 数据的对齐和大/小端存放方式                                                 | 了解     | 单选               | ★        |  
|                        | CISC和RISC的基本概念                                                       | 了解     | 单选               | ★★★★     |  
|                        | 高级语言程序与机器级代码的对应（选择/循环/过程调用的机器表示、栈帧结构）     | 重要     | 单选、综合题       | ★★★★★    |  
| 五、中央处理器（CPU）  | CPU的功能和基本结构                                                         | 基础     | 单选               | ★★★★★    |  
|                        | 指令执行过程（取指、译码、执行、访存、写回）                                 | 重要     | 单选               | ★★★★     |  
|                        | 数据通路的功能和基本结构（数据通路图分析）                                   | 核心     | 综合题             | ★★★★     |  
|                        | 控制器的功能和工作原理（硬布线/微程序控制）                                 | 了解     | 单选               | ★★★      |  
|                        | 异常和中断机制（基本概念、分类、检测与响应）                                 | 重要     | 单选               | ★★       |  
|                        | 指令流水线（基本概念、结构冒险/数据冒险/控制冒险的处理、吞吐率计算）         | 核心     | 单选、综合题       | ★★★★     |  
|                        | 多处理器基本概念（SISD、SIMD、MIMD、多核、SMP）                             | 了解     | 单选               | ★        |  
| 六、总线和输入/输出系统 | 总线的基本概念、组成及性能指标（带宽、位宽）                                 | 重要     | 单选               | ★★★★     |  
|                        | 总线事务和定时（同步/异步定时）                                             | 了解     | 单选               | ★★       |  
|                        | I/O接口的功能、基本结构、I/O端口编址                                         | 基础     | 单选               | ★★       |  
|                        | I/O方式（程序查询、程序中断、DMA方式）                                       | 核心     | 单选、综合题（中断处理/DMA过程） | ★★★★★    |  


### **（三）操作系统**  

| **章节**               | **考点内容**                                | **标签** | **考试题型**       | **难度等级** |  
|------------------------|-----------------------------------------|--------|--------------------|----------|  
| 一、操作系统概述       | 操作系统的基本概念、发展历程                          | 基础     | 单选               | ★        |  
|                        | 程序运行环境（CPU运行模式、中断/异常处理、系统调用、程序的链接与装入）   | 了解     | 单选               | ★        |  
|                        | 操作系统结构（分层、模块化、宏内核、微内核）                  | 了解     | 单选               | ★★★      |  
|                        | 操作系统引导、虚拟机                              | 了解     | 单选               | ★★       |  
| 二、进程管理           | 进程与线程的基本概念、状态转换、组织与控制                   | 重要     | 单选               | ★★       |  
|                        | 进程间通信（共享内存、消息传递、管道、信号）                  | 重要     | 单选               | ★★★★     |  
|                        | CPU调度算法（FCFS、SJF、优先级、时间片轮转、多级反馈队列）      | 重要     | 单选、综合题       | ★★       |  
|                        | 同步与互斥的基本概念、实现方法（信号量、PV操作、条件变量）          | 核心     | 综合题（经典同步问题） | ★★★★     |  
|                        | 经典同步问题（生产者-消费者、读者-写者、哲学家进餐）             | 核心     | 综合题             | ★★★★★    |  
|                        | 死锁的基本概念、预防、避免（银行家算法）、检测与解除              | 核心     | 单选、综合题       | ★★★★     |  
| 三、内存管理           | 内存管理基础（逻辑/物理地址空间、地址变换、连续分配、页式/段式/段页式管理） | 重要     | 单选               | ★★★      |  
|                        | 虚拟内存管理的基本概念、请求页式管理                      | 核心     | 单选               | ★★★      |  
|                        | 页置换算法（OPT、FIFO、LRU、CLOCK）               | 核心     | 单选、综合题（缺页率计算） | ★★★      |  
|                        | 页框分配与回收、内存映射文件                          | 了解     | 单选               | ★        |  
| 四、文件管理           | 文件的基本概念、元数据（inode）、操作（建立/删除/打开/关闭/读写）   | 重要     | 单选               | ★★★      |  
|                        | 文件的逻辑结构、物理结构（连续、链接、索引）                  | 重要     | 单选               | ★★★      |  
|                        | 树形目录、硬链接与软链接                            | 了解     | 单选               | ★        |  
|                        | 文件系统的全局结构、外存空闲空间管理（位示图、空闲链表）            | 了解     | 单选               | ★★★      |  
| 五、输入输出（I/O）管理 | I/O设备的分类、I/O控制方式（查询、中断、DMA）             | 基础     | 单选               | ★★       |  
|                        | I/O软件层次结构（中断处理程序、驱动程序、设备独立软件）           | 了解     | 单选               | ★        |  
|                        | 缓冲区管理、SPOOLing技术                        | 重要     | 单选               | ★★★      |  
|                        | 磁盘调度算法（FCFS、SSTF、SCAN、CSCAN）            | 核心     | 单选               | ★★★      |  


### **（四）计算机网络**  

| **章节**    | **考点内容**                                                       | **标签** | **考试题型**       | **难度等级** |  
|-----------|--------------------------------------------------------------------|--------|--------------------|----------|  
| 一、计算机网络概述 | 计算机网络的基本概念（定义、组成、功能、分类）                        | 基础     | 单选               | ★★★      |  
|           | 计算机网络性能指标（带宽、时延、吞吐量）                             | 重要     | 单选               | ★★★      |  
|           | 网络体系结构（分层结构、协议/接口/服务、OSI与TCP/IP模型）             | 重要     | 单选               | ★★★★     |  
| 二、物理层     | 通信基础（信道、信号、码元、波特率、速率；奈奎斯特定理、香农定理）         | 核心     | 单选、计算（速率计算） | ★★★      |  
|           | 编码与调制（数字编码、模拟调制）                                   | 重要     | 单选               | ★★       |  
|           | 电路交换、报文交换、分组交换                                       | 重要     | 单选               | ★★★★     |  
|           | 传输介质、物理层设备（中继器、集线器）                              | 了解     | 单选               | ★★       |  
| 三、数据链路层   | 数据链路层功能、组帧（PPP帧格式）                                 | 基础     | 单选               | ★        |  
|           | 差错控制（检错编码：CRC；纠错编码）                               | 重要     | 单选               | ★        |  
|           | 流量控制与可靠传输机制（滑动窗口、停止-等待、GBN、SR协议）            | 核心     | 单选、综合题（协议流程） |