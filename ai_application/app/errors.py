from django_ext.exceptions import InternalException


class AppSystemError(InternalException):
    code = '-1'


class LLMRequestError(InternalException):
    code = '30001'


class SignatureInvalidError(InternalException):
    code = '40001'


class SignatureExpiredError(InternalException):
    code = '40002'


class ParameterError(InternalException):
    code = '41001'


class AppNotFoundError(InternalException):
    code = '42001'


class ConversationNotFoundError(InternalException):
    code = '43001'


class ConversationFinishedError(InternalException):
    code = '43002'


class ConversationTokenExceedError(InternalException):
    code = '43003'


class MessageNotFoundError(InternalException):
    code = '44001'


class DatasetNotFoundError(InternalException):
    code = '45001'


class DatasetNotSupportError(InternalException):
    code = '45002'


class DatasetFileNumExceedError(InternalException):
    code = '45003'


class DatasetFileTypeNotAllowError(InternalException):
    code = '45004'


class DatasetDocumentNotFoundError(InternalException):
    code = '45005'


class MessageTaskNotFoundError(InternalException):
    code = '46001'


class DocumentExtractionError(InternalException):
    code = '80001'


class ContainSensitiveError(InternalException):
    code = '90001'


class SpecialMessageError(InternalException):
    pass


class KnowledgeNotResultError(SpecialMessageError):
    code = '45006'


class QuestionMessageNotQuestionError(SpecialMessageError):
    code = '45007'


class GrammarMessageNotQuestionError(SpecialMessageError):
    code = '45008'


class QuestionNotFoundError(InternalException):
    code = '81001'


class PlanNotFoundError(InternalException):
    code = '81002'
