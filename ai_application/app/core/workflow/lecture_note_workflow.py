import re

from app.core.features.course_note.lecture_fix import LectureFixFeature
from app.core.features.course_note.lecture_note_generation import LectureNoteGenerationFeature
from app.core.features.course_note.subtitle_fix import SubtitleFixFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import PromptTemplate, CourseVideoContent


class LectureNoteWorkFlow(BaseWorkflow):

    def run(self):
        user_inputs = self.application_generate_entity.inputs.copy()

        fixed_subtitle = self.fix_subtitle(user_inputs)

        lecture = user_inputs.get('chapter_note')
        # 如果lecture不为空，则使用lecture_fix_feature进行修正笔记
        if lecture:
            fixed_cleaned_subtitle = self.replace_code_blocks(fixed_subtitle)
            fixed_lecture = self.fix_lecture(user_inputs)
            note_result = self.gen_note(user_inputs, fixed_cleaned_subtitle, fixed_lecture)
            note = note_result.message.content
        else:
            note = fixed_subtitle
        note = self.precess_note(note)

        self._publish_workflow_custom_message(note)
        self._publish_workflow_end_message()
        return note

    def fix_subtitle(self, user_inputs):
        video_content_id = user_inputs.get('video_content_id')
        video_content: CourseVideoContent | None = None
        if video_content_id:
            video_content: CourseVideoContent = CourseVideoContent.objects.filter(id=video_content_id).first()

        if video_content and video_content.subtitle_note and video_content.is_subtitle_note_enabled:
            return video_content.subtitle_note

        subtitle_fix_feature = SubtitleFixFeature()

        result = subtitle_fix_feature.run(
            application_generate_entity=self.application_generate_entity,
            subtitle=user_inputs.get('subtitle'),
            stream=False,
            prompt_content=''
        )
        tracing_info = {
            'tracing_type': 'lecture_note_subtitle_fix',
            'query': f"{user_inputs.get('chapter_name')}|{user_inputs.get('section_name')}"
        }
        self.tracing_log.add_tracing_by_llm_result(
            tracing_type=tracing_info.get('tracing_type'),
            query=tracing_info.get('query'),
            llm_result=result
        )

        subtitle_note = result.message.content
        if video_content:
            video_content.subtitle_note = subtitle_note
            video_content.is_subtitle_note_enabled = True
            video_content.save()

        return subtitle_note

    def fix_lecture(self, user_inputs):
        lecture_fix_feature = LectureFixFeature()

        lecture = user_inputs.get('chapter_note')
        if lecture:
            lecture = self.trim_lecture_content(lecture)

        result = lecture_fix_feature.run(
            application_generate_entity=self.application_generate_entity,
            lecture=lecture,
            stream=False,
            prompt_content=''
        )
        tracing_info = {
            'tracing_type': 'lecture_note_lecture_fix',
            'query': f"{user_inputs.get('chapter_name')}|{user_inputs.get('section_name')}"
        }
        self.tracing_log.add_tracing_by_llm_result(
            tracing_type=tracing_info.get('tracing_type'),
            query=tracing_info.get('query'),
            llm_result=result
        )
        return result.message.content

    def gen_note(self, user_inputs, subtitle, lecture):
        note_gen_feature = LectureNoteGenerationFeature()
        result = note_gen_feature.run(
            application_generate_entity=self.application_generate_entity,
            subtitle=subtitle,
            lecture=lecture,
            stream=False,
            prompt_content=''
        )
        tracing_info = {
            'tracing_type': 'lecture_note_generation',
            'query': f"{user_inputs.get('chapter_name')}|{user_inputs.get('section_name')}"
        }
        self.tracing_log.add_tracing_by_llm_result(
            tracing_type=tracing_info.get('tracing_type'),
            query=tracing_info.get('query'),
            llm_result=result
        )
        return result

    def replace_code_blocks(self, markdown_text, replacement=""):
        # 正则表达式匹配Markdown中的代码块
        pattern = r'```.*?\n.*?```'
        # 使用re.DOTALL标志使.匹配包括换行符在内的所有字符
        return re.sub(pattern, replacement, markdown_text, flags=re.DOTALL)

    def trim_lecture_content(self, lecture):
        # 使用re.DOTALL标志使.匹配包括换行符在内的所有字符
        # 正则表达式匹配Markdown中的代码块
        lecture = re.sub(r'```.*?\n.*?```', '', lecture, flags=re.DOTALL)
        lecture = re.sub(r'如图.*?所示', '', lecture, flags=re.DOTALL)
        lecture = re.sub(r'图\s*(\d+(\.\d+)+)($\w+$)?', '', lecture, flags=re.DOTALL)
        lecture = re.sub(r'<center>.*?</center>', '', lecture, flags=re.DOTALL)
        return lecture

    def precess_note(self, note):
        # 整理note，去除首尾的无效内容
        note_arr = note.split('\n')
        new_note_arr = []
        for idx, row in enumerate(note_arr):
            if idx == 0 and '综合性课程讲义' in row:
                continue
            if idx == len(note_arr) - 1 and '的学习' in row:
                continue
            if row.startswith('#') and re.search(r'第[一二三四五六七八九十\d]+章', row):
                continue
            if row.startswith('#') and '课程笔记' in row:
                continue

            # 整理笔记内容，标题样式修改
            row = re.sub(r'^# ', '#### ', row)
            row = re.sub(r'^## ', '#### ', row)
            row = re.sub(r'^### ', '#### ', row)
            new_note_arr.append(row)

        new_note = '\n'.join(new_note_arr)
        return new_note
