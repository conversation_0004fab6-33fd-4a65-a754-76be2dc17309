from celery import shared_task


from app.services.coze_workflow_service import create_recite_question, CozeWorkflowService


@shared_task(queue='message_task')
def auto_gen_en_word_recite_question():
    create_recite_question('high', 10)
    create_recite_question('middle', 10)
    create_recite_question('low', 10)


@shared_task(queue='message_task')
def auto_check_en_word_recite_question():
    CozeWorkflowService.check_running_workflow()
