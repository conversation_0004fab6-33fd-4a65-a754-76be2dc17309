import logging
import time

from celery import shared_task
from django.utils import timezone

from app.core.indexing_runner import IndexingRunner
from app.models import Dataset, DatasetDocument

logger = logging.getLogger(__name__)


@shared_task(queue='dataset')
def document_indexing_task(dataset_no: str, document_nos: list[str]):
    logger.info('Start indexing document: {}'.format(document_nos))
    start_at = time.perf_counter()

    dataset: Dataset = Dataset.objects.filter(
        is_deleted=False, dataset_no=dataset_no).first()
    # 资料库不存在，则不处理
    if not dataset:
        return

    # 修改文档解析状态
    DatasetDocument.objects.filter(
        is_deleted=False, document_no__in=document_nos
    ).update(
        indexing_status='parsing',
        processing_started_at=timezone.now()
    )

    dataset_documents = list(DatasetDocument.objects.filter(
        is_deleted=False, document_no__in=document_nos
    ))
    try:
        indexing_runner = IndexingRunner()
        indexing_runner.run(dataset, dataset_documents)
        end_at = time.perf_counter()
        logger.info('Processed dataset: {} latency: {}'.format(1, end_at - start_at))
    except Exception as e:
        logger.exception(e)
