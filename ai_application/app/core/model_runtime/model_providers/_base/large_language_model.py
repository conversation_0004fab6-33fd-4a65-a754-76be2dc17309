import time
from abc import abstractmethod
from typing import Generator

from app.core.model_runtime.entities.llm_entities import LLMUsage, LLMResult
from app.core.model_runtime.entities.provider_entities import ModelType
from app.core.model_runtime.model_providers._base.ai_model import AIModel
from app.core.prompt.entities import PromptMessage


class LargeLanguageModel(AIModel):
    model_provider = ''
    model_type: ModelType = ModelType.LLM

    def invoke(
            self,
            model: str,
            prompt_messages: list[PromptMessage],
            model_parameters: dict | None = None,
            tools: list[str] | None = None,
            stream: bool = True
    ) -> LLMResult | Generator:
        self.started_at = time.perf_counter()
        return self._invoke(model, prompt_messages, model_parameters, tools, stream)

    @abstractmethod
    def _invoke(
            self,
            model: str,
            prompt_messages: list[PromptMessage],
            model_parameters: dict,
            tools: list[str] | None = None,
            stream: bool = True
    ) -> LLMResult | Generator:
        raise NotImplementedError

    @abstractmethod
    def get_num_tokens(self, model: str, prompt_messages: list[PromptMessage]) -> int:
        """
        Get number of tokens for given prompt messages
        """
        raise NotImplementedError

    def _calc_response_usage(self, prompt_tokens: int, completion_tokens: int) -> LLMUsage:
        return LLMUsage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=prompt_tokens + completion_tokens,
            latency=time.perf_counter() - self.started_at
        )
