import uuid

from django.conf import settings
from django.db import transaction

from app.api.dto import AppAddDto
from app.constants.app import AppRunType
from app.core.app_parameter_manager import AppParameterManager
from app.errors import ParameterError, AppNotFoundError
from app.models import Account, App, AppModelConfig


class AppService:

    @classmethod
    def create_app(cls, name: str, app_no: str, mode: str) -> App:
        account = Account.objects.first()

        with transaction.atomic():
            app_model_config = AppModelConfig.objects.create(
                app_no=app_no,
                mode=mode,
                run_type=AppRunType.NATIVE.value,
                third_app_key='',
                support_params=[],
                model_provider=settings.MODEL_PROVIDER_NAME,
                model_id=settings.MODEL_NAME,
            )

            app = App.objects.create(
                account=account,
                app_no=app_no,
                name=name,
                mode=mode,
                app_model_config=app_model_config
            )
        return app

    @classmethod
    def get_app_by_account(cls, app_no: str, account: Account) -> App:
        app = App.objects.filter(is_deleted=False, app_no=app_no, account=account).first()
        if not app:
            raise AppNotFoundError()
        return app

    @classmethod
    def add_app(cls, account: Account, dto: AppAddDto) -> App:
        cls._check_params(dto.support_params)

        app_no = cls._gen_app_no()

        with transaction.atomic():
            app_model_config = AppModelConfig.objects.create(
                app_no=app_no,
                mode=dto.mode,
                run_type=dto.run_type,
                third_app_key=dto.app_key or '',
                support_params=dto.support_params,
                model_provider=settings.MODEL_PROVIDER_NAME,
                model_id=settings.MODEL_NAME,
            )

            app = App.objects.create(
                account=account,
                app_no=app_no,
                name=dto.name,
                mode=dto.mode,
                app_model_config=app_model_config
            )
        return app

    @classmethod
    def edit_app(cls, app: App, dto: AppAddDto):
        cls._check_params(dto.support_params)

        with transaction.atomic():
            app_model_config = AppModelConfig.objects.create(
                app_no=app.app_no,
                mode=dto.mode,
                run_type=dto.run_type,
                third_app_key=dto.app_key or '',
                support_params=dto.support_params,
                model_provider=settings.MODEL_PROVIDER_NAME,
                model_id=settings.MODEL_NAME,
            )

            app.name = dto.name
            app.app_model_config = app_model_config
            app.save()

    @classmethod
    def _gen_app_no(cls):
        uuid_str = uuid.uuid4().hex[:16]
        return f'app_{uuid_str}'

    @classmethod
    def _check_params(cls, support_params):
        all_param_codes = AppParameterManager().get_param_code_list()
        not_exist_code = [p for p in support_params if p not in all_param_codes]
        if not_exist_code:
            raise ParameterError(detail_err=f'参数{not_exist_code}不支持')
