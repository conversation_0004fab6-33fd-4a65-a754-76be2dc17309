from pathlib import Path

from app.constants.app import AppRunType, AppMode
from django_ext.base_dto_model import MyBaseModel
from pydantic import Field
from typing import List, Dict
from pydantic import BaseModel, Field
from typing import List, Optional
from typing import Literal

class AppAddDto(MyBaseModel):
    name: str
    app_key: str | None = None
    support_params: list = Field(default=[])
    # 默认参数
    mode: AppMode = AppMode.CHAT.value
    run_type: AppRunType = AppRunType.NATIVE.value


class PromptOptimizeParamDto(MyBaseModel):
    role: str
    tone: str = ''
    task: str = ''
    examples: str = ''
    instructions: str = ''
    note: str = ''


class PromptOptimizeDto(MyBaseModel):
    query_params: PromptOptimizeParamDto


class ContentExtractorDto(MyBaseModel):
    text: str


class ProblemSolvingDto(MyBaseModel):
    user_question: str
    user_requirement: str = ''  # user_requirement 可以为空


class ProblemSolvingOnlyDto(MyBaseModel):
    conversation_id: str = ''
    user_question: str = ''
    question_prompts: dict
    file_objs: List[str] = Field(default=[])
    biz_id: str = ''
    userinfo: dict | None = None
    app_id: str = ''


class CodeOptimizationOnlyDto(MyBaseModel):
    lang_code: str
    code_content: str
    conversation_id: str = ''
    code_prompts: dict
    biz_id: str = ''
    userinfo: dict | None = None
    app_id: str = ''


class MathProblemSolvingDto(MyBaseModel):
    app_id: str = ''
    user_question: str = ''
    image_url: List[str] = Field(default=[])
    conversation_id: str = ''
    biz_id: str = ''
    userinfo: dict | None = None


class ComplexSentenceAnalysisDto(MyBaseModel):
    sentence: str


class ChapterNoteGeneratorDto(MyBaseModel):
    lecture_slides: str


class LectureNoteGeneratorDto(MyBaseModel):
    subtitle:  List[Dict[str, str]]



class ConversationAdd(MyBaseModel):
    app_id: str
    biz_id: str = ''
    inputs: dict = Field(default={})
    pre_prompt: str = ''
    dataset_no: str = ''
    document_nos: list = Field(default=[])



class CozeConversationAdd(MyBaseModel):
    app_id: str
    biz_id: str = ''


class ChatMessageDto(MyBaseModel):
    app_id: str = ''
    biz_id: str = ''
    userinfo: dict | None = None
    inputs: dict | None = None
    prompt_params: dict | None = None
    pre_prompt: str = ''
    query: str = ''
    conversation_id: str = ''
    original_message_id: str = ''
    is_override: bool = False
    stream: bool = True
    message_type: str = 'normal'
    file_objs: list = Field(default=[])
    is_async: bool = False


class CozeChatMessageDto(MyBaseModel):
    app_id: str = ''
    biz_id: str = ''
    userinfo: dict | None = None
    query: str = ''
    image: str = ''
    conversation_id: str = ''
    stream: bool = True
    is_async: bool = False


class DatasetSetDocumentDto(MyBaseModel):
    document_no: str = ''
    name: str = ''
    url: str = ''

    @property
    def file_extension(self):
        input_file = Path(self.url)
        return input_file.suffix.lower()[1:]


class DatasetCreateDto(MyBaseModel):
    name: str
    documents: list[DatasetSetDocumentDto] = Field(default=[])


class DatasetSetDocumentsDto(MyBaseModel):
    documents: list[DatasetSetDocumentDto]


class DatasetAddKnowledgeDocumentDto(MyBaseModel):
    dataset_no: str
    name: str
    url: str


class DatasetAddSubtitleDocumentDto(MyBaseModel):
    name: str
    content: str


class KnowledgeAddDto(MyBaseModel):
    document_no: str
    name: str
    definition: str


class KnowledgeEditDto(MyBaseModel):
    name: str
    definition: str


class KnowledgeSearchDto(MyBaseModel):
    query: str
    biz_id: str = ''
    course_id: str
    document_no: str = ''
    search_mode: str = 'general'
    search_type: str
    split_prompt: str = ''
    local_prompt: str = ''
    llm_prompt: str = ''
    deep_local_prompt: str = ''
    deep_llm_prompt: str = ''
    deep_question_prompt: str = ''
    deep_no_question_prompt: str = ''
    is_recommend: bool = False
    enable_recommend_video: bool = False
    userinfo: dict = {}


class CourseNoteTaskCreateDto(MyBaseModel):
    course_id: str
    course_lecture_id: str
    chapter_id: str
    chapter_name: str
    chapter_lecture: str
    document_nos: list[str] = Field(default=[])



class SubQuestionDto(MyBaseModel):
    sub_question_id: str
    user_answer: str
    is_right: bool
    question_type: List[str]


class EnglishReaderReportDto(MyBaseModel):
    question_id: str
    answer_detail: List[SubQuestionDto]
    userinfo: Dict[str, str]   # userinfo 包含 user_id


class EnglishReaderQuestionDetailDto(MyBaseModel):
    userinfo: Dict[str, str]   # userinfo 包含 user_id


class EnglishReaderAgainReportDto(MyBaseModel):
    question_id: int
    wrong_sub_question_ids: List[int] = Field(default=[])  # 添加新的字段
    userinfo: Dict[str, str]   # userinfo 包含 user_id


class DayiApptDto(MyBaseModel):
    query: str = ''
    app_id: str = ''
    biz_id: str = ''
    conversation_id: str = ''
    stream: bool = True
    images: list[str] = Field(default=[])
    userinfo: dict = Field(default={})
    # {"scene": "", "subject_id": ""}
    scene_info: dict = Field(default={})


class CSVideoKnowledgeExtractDto(MyBaseModel):
    main_subject: str  # high_math, linear_algebra, math_prob
    course_section_id: str
    video_content: str


class GaoshuScreenshotQuestionDto(MyBaseModel):
    pic: str
    question: str
    # keywords: list[str] = Field(default=[])
    userinfo: Dict[str, str]
    course_id: str


class QuestionKnowledgeExtractDto(MyBaseModel):
    subject: str = ''  # math
    question_id: str


class QuestionAnswerDto(MyBaseModel):
    question_id: int
    user_answer: str


class TestPaperSubmitDto(MyBaseModel):
    record_id: int
    answer_detail: QuestionAnswerDto


class WordReciteBasicPaperSubmitDto(MyBaseModel):
    paper_id: int
    answer_detail: list[QuestionAnswerDto] = []


class WordRecitePlanManualChangeDto(MyBaseModel):
    # "low": 9, "high": 13, "middle": 10,
    low: int
    middle: int
    high: int


class WordRecitePostQuestionDto(MyBaseModel):
    question_id: int
    user_answer: str


class WordRecitePlanUseDto(MyBaseModel):
    plan_record_id: int
    use_status: str


class HuolandeTestDto(MyBaseModel):
    part1_realistic: List[int]
    part1_investigative: List[int]
    part1_artistic: List[int]
    part1_social: List[int]
    part1_enterprising: List[int]
    part1_conventional: List[int]

    part2_realistic: List[int]
    part2_investigative: List[int]
    part2_artistic: List[int]
    part2_social: List[int]
    part2_enterprising: List[int]
    part2_conventional: List[int]

    part3_realistic: List[int]
    part3_investigative: List[int]
    part3_artistic: List[int]
    part3_social: List[int]
    part3_enterprising: List[int]
    part3_conventional: List[int]

    part4_realistic: int
    part4_investigative: int
    part4_artistic: int
    part4_social: int
    part4_enterprising: int
    part4_conventional: int

    part5_realistic: int
    part5_investigative: int
    part5_artistic: int
    part5_social: int
    part5_enterprising: int
    part5_conventional: int


class TargetCollegeMajorDto(BaseModel):
    college_name: str
    college_code: str
    major_name: str
    major_code: str


class TargetMajorDirectionDto(BaseModel):
    name: str
    code: str


class CollegeAnalysisDto(BaseModel):
    report_id: str
    bachelor_level: Optional[str] = Field(None, description="Bachelor's degree level")
    bachelor_major: Optional[str] = Field(None, description="Bachelor's degree major")
    gpa_range: Optional[str] = Field(None, description="GPA range")
    major_ranking: Optional[str] = Field(None, description="Major ranking")
    research_experience: Optional[str] = Field(None, description="Research experience")
    competition_experience: Optional[str] = Field(None, description="Competition experience")
    english_ability: Optional[str] = Field(None, description="English ability")
    math_basis_select: Optional[str] = Field(None, description="Math basis selection")
    candidate_status: Optional[str] = Field(None, description="Candidate status")
    intern_experience: Optional[str] = Field(None, description="Internship experience")
    job_experience: Optional[str] = Field(None, description="Job experience")
    internship_accommodation_needs: Optional[str] = Field(None, description="Internship accommodation needs")
    concurrent_preparation: Optional[List[str]] = Field(None, description="Concurrent preparation")
    regions: Optional[List[str]] = Field(None, description="Regions")
    tuition_sensitivity: Optional[str] = Field(None, description="Tuition sensitivity")
    priority_order: Optional[str] = Field(None, description="Priority order")
    preparation_pain_points: Optional[str] = Field(None, description="Preparation pain points")
    preferred_services: Optional[str] = Field(None, description="Preferred services")
    admission_baseline: Optional[str] = Field(None, description="Admission baseline")
    contact_info: Optional[str] = Field(None, description="Contact information")
    holland_test_result: Optional[str] = Field(None, description="Holland test result")
    daily_learning_time: Optional[str] = Field(None, description="Daily learning time")
    stream: Optional[bool] = Field(True, description="Stream flag")
    is_cross_exam: Optional[str] = Field(None, description="Whether the candidate is cross-examining")
    target_major_direction: Optional[List[TargetMajorDirectionDto]] = Field(None, description="Target major direction")
    personal_needs: Optional[str] = Field(None, description="Personal needs")
    master_type: Optional[str] = Field(None, description="Master type (学硕/专硕)")
    master_degree_type: Optional[str] = Field(None, description="Master degree type")
    exam_year: Optional[str] = Field(None, description="Year of the exam")
    target_college_major: Optional[List[TargetCollegeMajorDto]] = Field(None, description="Target college major")


class CollegeAnalysisNewDto(BaseModel):

    bachelor_college_name: str = Field(None, description="本科学校名称")
    bachelor_college_code: str = Field(None, description="本科学校代码")
    bachelor_major: str = Field(None, description="Bachelor's degree major")

    gpa_range: Optional[str] = Field(None, description="GPA range")
    major_ranking: Optional[str] = Field(None, description="Major ranking")
    research_experience: Optional[str] = Field(None, description="Research experience")
    competition_experience: Optional[str] = Field(None, description="Competition experience")

    english_ability: Optional[str] = Field(None, description="English ability")
    math_basis_select: Optional[str] = Field(None, description="Math basis selection")


    regions: Optional[List[str]] = Field(None, description="Regions")
    priority_order: Optional[str] = Field(None, description="Priority order")
    master_type: Optional[str] = Field(None, description="Master type (学硕/专硕)")

    personal_needs: Optional[List[str]] = Field(None, description="主观诉求")
    cross_exam: dict
    target_college_code: str = Field(None, description="目标院校代码")
    target_major_code: str = Field(None, description="目标专业代码")
    target_college_name: str = Field(None, description="目标院校名称")
    target_major_name: str = Field(None, description="目标专业名称")
    # holland_test_result: Optional[str] = Field(None, description="Holland test result")

    stream: Optional[bool] = Field(True, description="Stream flag")


class KaoYanReviewPlanDto(BaseModel):
    report_id:str
    target_college_level: str
    stream: Optional[bool] = Field(True, description="Stream flag")


class StudyGuidesDto(BaseModel):
    app_id: str = ''
    biz_id: str = ''
    userinfo: dict | None = None
    conversation_id: str = ''
    weakness_knowledge : List[str]
    section_learn_stat : List[Dict]
    subject_id: str | None = None


class StudyGuidesAppDto(BaseModel):
    app_id: str = ''
    biz_id: str = ''
    user_id: str = ''
    subject_id: str = ''
    conversation_id: str = ''
    userinfo: dict | None = None
    # my_delivery_id: str = ''
    # outline_number: str = ''
    course_section_ids: List[str]
    # stage_name: str = ''


class ExercisesLearnStatusDto(MyBaseModel):
    subject_id: str | None = None
    section_name: str = ''  
    section_kgs: List[str]
    video_duration: str
    learn_duration: str
    video_kg_distribute: List[Dict]
    class_exercise: Dict[str, list] | None = None
    class_test: Dict[str, list]
    userinfo: dict


class CodeExerciseDto(MyBaseModel):
    question_type: Literal['concept', 'programming', 'only_code']
    question: str
    answer: str = ''  # only_code 可为空
    lang: str = ''  # only_code 不可为空
    stream: bool = True


class LanguageStyleDto(MyBaseModel):
    app_id: str = ''
    biz_id: str = ''


class ZhihenRadarDto(MyBaseModel):
    # app_id: str = ''
    # biz_id: str = ''
    # conversation_id: str = ''
    assistant_id: str = ''
    subject_id : str = ''
    question_id: str = ''
    answer_id: str = ''
    question: str = ''
    answer: str = ''
    answer_duration: int = 0
    userinfo: dict | None = None


class ModelManagementMessageDto(BaseModel):
    app_id: str
    user_id: int
    query: Optional[str] = Field(None, description="Input Text")
    image: Optional[List[str]] = Field(default=[], description="Image URLs")
    stream: bool = True
    is_async: Optional[bool] = Field(False, description="Async flag, defaults to False")
    inputs: dict | None = None

class SuperviseLearnStatusDto(MyBaseModel):
    user_id: str = ''
    course_id: str = ''
    subject_id: str = ''
    start_date: str = ''
    end_date: str = ''
    userinfo: dict | None = None


class GeneQuestionDto(MyBaseModel):
    app_id: str = ''
    biz_id: str = ''
    conversation_id: str = ''
    subject_id: str = ''
    main_subject: str = ''
    knowledge: str = ''
    chapter: str = ''


class KnowledgeAnalysisDto(MyBaseModel):
    biz_id: str = ''
    userinfo: dict | None = None
    query: str = ''
    course_id: str = ''
    document_no: str = ''
    split_prompt: str = ''
    recommend_prompt: str = ''
    local_prompt: str = ''
    llm_prompt: str = ''
    search_type: Literal['llm', 'local']
    is_stream: bool = True

class SupervisedLearnUserInitStatus(MyBaseModel):
    user_id: str = ''
    start_date: str = ''
    exam_date: str = ''
    graduation_info: dict | None = None
    target: dict | None = None
    education_level: int
    study_status: int
    graduation_years: int | None = None  # 改为可选
    study_stage: int | None = None  # 改为可选
    academic_performance: int | None = None  # 改为可选
    english_level: int = 5  # 改为可选
    math_subjects: list[int] = []
    math_mastery: int | None = None  # 改为可选
    
class SupervisedLearnUserStageStatus(MyBaseModel):
    user_id: str = ''
    course_id: str = ''

class PersonalizedExamSyllabus(MyBaseModel):
    user_id: str = ''