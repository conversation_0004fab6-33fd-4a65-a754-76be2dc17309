<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>院系分析助手（迭代版）</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
    <!-- Select2 CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/css/select2.min.css" rel="stylesheet" />
    <!-- Select2 JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/select2/4.1.0-rc.0/js/select2.min.js"></script>


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <style>        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
        }
        .left-section, .right-section {
            padding: 20px;
            box-sizing: border-box;
        }
        .left-section {
            width: 30%;
            background-color: #f9f9f9;
            border-right: 1px solid #ddd;
        }
        .right-section {
            width: 70%;
            background-color: #fff;
        }
        h2 {
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], select, textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .checkbox-group label {
            display: inline-block;
            margin-right: 10px;
        }
        .hidden {
            display: none;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result-box {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #loadingOverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        #loadingOverlay div {
            color: white;
            font-size: 24px;
        }
        /* 新增模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1001;
        }
        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            max-width: 400px;
            text-align: center;
        }
        .modal.hidden {
            display: none;
        }
        /* 院校目标样式 */
        .college-major-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .college-major-group input {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="left-section">
        <h2>填写个人信息</h2>
        <form id="college-analysis-form">

            <!-- 本科院校层次 -->
            <div class="form-group">
                <label for="bachelor-level-name">本科院校名称</label>
                <div class="relative">
                    <input type="text" id="bachelor-level-name" maxlength="15" placeholder="输入学校名称搜索..." required>
                    <div id="college-search-results" class="absolute left-0 right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 hidden"></div>
                </div>
            </div>
            <div class="form-group">
                <label for="bachelor-level">本科院校代码</label>
                <input type="text" id="bachelor-level" maxlength="15" value="" required readonly>
            </div>

        <div class="form-group">
            <label for="bachelor-major">本科专业</label>
            <div class="dropdown-container" style="display: flex; gap: 10px;">
                <select id="undergraduate-category" style="flex: 1;">
                    <option value="">请选择一级学科</option>
                    <!-- 动态填充一级学科 -->
                </select>
                <select id="undergraduate-major" style="flex: 1;" disabled>
                    <option value="">请选择二级门类</option>
                    <!-- 动态填充二级门类 -->
                </select>
            </div>
            <input type="text" id="bachelor-major" maxlength="15" value="" required readonly style="margin-top: 10px;">
        </div>
        <!-- 是否跨考 -->
        <div class="form-group">
            <label for="is-cross-exam">是否跨考</label>
            <select id="is-cross-exam" name="is-cross-exam">
                <option value="false">否</option>
                <option value="true">是</option>
            </select>
        </div>

        <!-- 跨考方向（初始状态为隐藏） -->
        <div class="form-group" id="cross-direction-container" style="display: none;">
            <label for="cross-direction">跨考方向</label>
            <select id="cross-direction" name="cross-direction">
                <option value="">请选择研究生二级门类</option>
                <!-- 动态填充研究生二级门类 -->
            </select>
        </div>

            <!-- 修改后的主观诉求 -->
            <div class="form-group">
                <label>主观诉求</label>
                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="need-math" value="不考数学"> 不考数学
                    </label>
                    <label>
                        <input type="checkbox" id="need-english" value="英语不好"> 英语不好
                    </label>
                </div>
            </div>
            <!-- 意向地区 -->
            <!-- 意向地区 -->
            <div class="form-group">
                <label for="preferred-regions">意向地区</label>
                <div class="checkbox-group">
                <label><input type="checkbox" class="region-option" value="超一线城市"> 超一线城市</label>
                <label><input type="checkbox" class="region-option" value="一线城市"> 一线城市</label>
                <label><input type="checkbox" class="region-option" value="新一线城市"> 新一线城市</label>
                <label><input type="checkbox" class="region-option" value="二线城市"> 二线城市</label>
                <label><input type="checkbox" class="region-option" id="region-other-checkbox" value="其他"> 其他</label>
                </div>
                <!-- 其他城市输入框，默认隐藏 -->
                <input type="text" id="region-other-input" class="hidden" placeholder="请输入具体城市名">
            </div>
            <!-- 修改后的院校目标部分 -->
             <div class="form-group">
                <label>目标院校</label>
                <div class="college-major-group">
                    <input type="text" id="target-college-1" placeholder="院校名称" required>
                    <input type="text" id="target-college-code-1" placeholder="院校代码" required>
                    <input type="text" id="target-major-1" placeholder="对应专业" required>
                    <input type="text" id="target-major-code-1" placeholder="专业代码" required>
                </div>
            </div>

<!--            <div class="form-group">-->
<!--                <label>目标院校2</label>-->
<!--                <div class="college-major-group">-->
<!--                    <input type="text" id="target-college-2" placeholder="院校名称">-->
<!--                    <input type="text" id="target-college-code-2" placeholder="院校代码">-->
<!--                    <input type="text" id="target-major-2" placeholder="对应专业">-->
<!--                    <input type="text" id="target-major-code-2" placeholder="专业代码">-->
<!--                </div>-->
<!--            </div>-->

<!--            <div class="form-group">-->
<!--                <label>目标院校3</label>-->
<!--                <div class="college-major-group">-->
<!--                    <input type="text" id="target-college-3" placeholder="院校名称">-->
<!--                    <input type="text" id="target-college-code-3" placeholder="院校代码">-->
<!--                    <input type="text" id="target-major-3" placeholder="对应专业">-->
<!--                    <input type="text" id="target-major-code-3" placeholder="专业代码">-->
<!--                </div>-->
<!--            </div>-->

            <!-- 总GPA区间 -->
            <div class="form-group">
                <label for="gpa-range">总GPA区间</label>
                <select id="gpa-range">
                     <option value="">-- 请选择 --</option>
                    <option value="3.8+">3.8+</option>
                    <option value="3.5-3.7">3.5-3.7</option>
                    <option value="3.0-3.4">3.0-3.4</option>
                    <option value="3.0">3.0</option>
                </select>
            </div>

            <!-- 专业排名 -->
            <div class="form-group">
                <label for="major-ranking">专业排名</label>
                <select id="major-ranking">
                    <option value="">-- 请选择 --</option>
                    <option value="前10%">前10%</option>
                    <option value="11%-30%">11%-30%</option>
                    <option value="其他">其他</option>
                </select>
            </div>

            <!-- 科研经历 -->
            <div class="form-group">
                <label for="research-experience">科研经历</label>
                <input type="text" id="research-experience" value="" required>
            </div>

            <!-- 竞赛经历 -->
            <div class="form-group">
                <label for="competition-experience">竞赛经历</label>
                <textarea id="competition-experience" maxlength="50" required></textarea>
            </div>

             <!-- 修改后的英语能力 -->
            <div class="form-group">
                <label for="english-ability">英语能力</label>
                <div style="display: flex; gap: 10px;">
                    <select id="english-ability" style="flex: 1;">
                        <option value="">-- 请选择 --</option>
                        <option value="CET-6">六级</option>
                        <option value="雅思">雅思</option>
                        <option value="托福">托福</option>
                        <option value="其他">其他</option>
                    </select>
                    <input type="text" id="english-score" style="flex: 1;" placeholder="分数/详情">
                </div>
            </div>

            <!-- 修改后的数学基础 -->
            <div class="form-group">
                <label for="math-basis">数学基础</label>
                <div style="display: flex; gap: 10px;">
                    <select id="math-basis-select" style="flex: 1;">
                        <option value="">-- 请选择 --</option>
                        <option value="基础薄弱">基础薄弱</option>
                        <option value="系统学习过高数线代概率">系统学习过高数线代概率</option>
                    </select>
                    <input type="text" id="math-basis-text" style="flex: 1;" placeholder="补充说明">
                </div>
            </div>


            <!-- 院校专业优先级排序 -->
            <div class="form-group">
                <label for="priority-order">院校及专业优先级排序</label>
                <select id="priority-order">
                    <option value="">-- 请选择 --</option>
                    <option value="major,college_level">专业优先</option>
                    <option value="college_level,major">院校优先</option>
                </select>
            </div>

<!--            &lt;!&ndash; 目标专业方向 &ndash;&gt;-->
<!--            <div class="form-group">-->
<!--                <label>目标专业方向</label>-->
<!--                <div style="display: flex; gap: 10px;">-->
<!--                    <input type="text" id="target_major_direction_1" placeholder="目标专业方向1" required>-->
<!--                    <input type="text" id="target_major_direction_code_1" placeholder="专业代码1" required>-->
<!--                    <input type="text" id="target_major_direction_2" placeholder="目标专业方向2">-->
<!--                    <input type="text" id="target_major_direction_code_2" placeholder="专业代码2">-->
<!--                    <input type="text" id="target_major_direction_3" placeholder="目标专业方向3">-->
<!--                    <input type="text" id="target_major_direction_code_3" placeholder="专业代码3">-->
<!--                </div>-->
<!--            </div>-->

             <!--想要考学硕/专硕-->
            <div class="form-group">
                <label for="master_type">想要考学硕/专硕</label>
                <div style="display: flex; gap: 10px;">
                    <select id="master_type" style="flex: 1;">
                        <option value="">-- 请选择 --</option>
                        <option value="学硕">学硕</option>
                        <option value="专硕">专硕</option>
                    </select>
                </div>
            </div>

            <!-- 霍兰德职业兴趣测试 -->
            <div class="form-group">
                <label for="holland-test">霍兰德职业兴趣测试</label>
                <select id="holland-test">
                    <option value="">-- 请选择 --</option>
                    <option value="没有做过测试">没有做过测试</option>
                    <option value="做过测试">做过测试</option>
                </select>
                <input type="text" id="holland-result" class="hidden" placeholder="请输入测试结果" required>
            </div>

<!--             &lt;!&ndash; 个人需求 &ndash;&gt;-->
<!--            <div class="form-group">-->
<!--                <label for="personal_needs">主观避开条件</label>-->
<!--                <input type="text" id="personal_needs" placeholder="不考数学/不接受二战" required>-->
<!--            </div>-->


            <!-- 提交按钮 -->
            <button type="button" id="generateAdviceBtn">生成报告</button>
            <!-- 新增按钮 -->
            <button type="button" id="generateReportWithGoalBtn">生成报告(基于目标院校和专业版)</button>

        </form>
    </div>

    <div class="right-section">
        <h2>模型返回结果</h2>
        <div class="result-box" id="result-box" aria-live="polite" aria-atomic="true" aria-busy="false"></div>
        <div class="tracing-detail" id="tracingDetails"></div>

        <!-- 提示词编辑窗口 -->
        <div class="form-group">
            <label for="college-analysis-prompt">提示词编辑</label>
            <textarea id="college-analysis-prompt" rows="4" placeholder="请输入提示词">{{ college_analysis_new }}</textarea>
        </div>
        <!-- 新增提示词编辑窗口 -->
        <div class="form-group">
            <label for="college-analysis-with-goal-prompt">目标院校报告提示词编辑</label>
            <textarea id="college-analysis-with-goal-prompt" rows="4" placeholder="请输入基于目标院校报告提示词">{{ college_analysis_new_with_goal }}</textarea>
        </div>
    </div>


    <div id="loadingOverlay">
        <div>加载中...</div>
    </div>


    <!-- 霍兰德测试弹窗 -->
    <div id="holland-modal" class="modal hidden">
        <div class="modal-content">
            <p>您还没有做过霍兰德职业兴趣测试，是否现在开始测试？</p>
            <div style="display: flex; justify-content: center; gap: 20px; margin-top: 20px;">
                <button id="holland-test-yes">是</button>
                <button id="holland-test-no">否</button>
            </div>
        </div>
    </div>


     <script>
document.addEventListener("DOMContentLoaded", function () {

    // 获取DOM元素
    const generateAdviceBtn = document.getElementById('generateAdviceBtn');
    const resultBox = document.getElementById('result-box');
    const tracingDetails = document.getElementById('tracingDetails');
    const loadingOverlay = document.getElementById('loadingOverlay');

    // 研究生二级门类数据
    const graduateMajors = [
        "0101哲学",
        "0151应用伦理",
        "0201理论经济学",
        "0202应用经济学",
        "0270统计学",
        "0271区域国别学",
        "0251金融",
        "0252应用统计",
        "0253税务",
        "0254国际商务",
        "0255保险",
        "0256资产评估",
        "0258数字经济",
        "0301法学",
        "0302政治学",
        "0303社会学",
        "0304民族学",
        "0305马克思主义理论",
        "0306公安学",
        "0307中共党史党建学",
        "0308纪检监察学",
        "0370国家安全学",
        "0371区域国别学",
        "0351法律",
        "0352社会工作",
        "0353警务",
        "0354知识产权",
        "0355国际事务",
        "0401教育学",
        "0402心理学",
        "0403体育学",
        "0471教育经济与管理",
        "0451教育",
        "0452体育",
        "0453国际中文教育",
        "0454应用心理",
        "0501中国语言文学",
        "0502外国语言文学",
        "0503新闻传播学",
        "0551翻译",
        "0552新闻与传播",
        "0553出版",
        "0601考古学",
        "0602中国史",
        "0603世界史",
        "0670区域国别学",
        "0651博物馆",
        "0701数学",
        "0702物理",
        "0703化学",
        "0704天文学",
        "0705地理学",
        "0706大气科学",
        "0707海洋科学",
        "0708地球物理学",
        "0709地质学",
        "0710生物学",
        "0711系统科学",
        "0712科学技术史",
        "0713生态学",
        "0714统计学",
        "0751气象",
        "0801力学",
        "0802机械工程",
        "0803光学工程",
        "0804仪器科学与技术",
        "0805材料科学与工程",
        "0806冶金工程",
        "0807动力工程与工程热物理",
        "0808电气工程",
        "0809电子科学与技术",
        "0810信息与通信工程",
        "0811控制科学与工程",
        "0812计算机科学与技术",
        "0813建筑学",
        "0814土木工程",
        "0815水利工程",
        "0816测绘科学与技术",
        "0817化学工程与技术",
        "0818地质资源与地质工程",
        "0819矿业工程",
        "0820石油与天然气工程",
        "0821纺织科学与工程",
        "0822轻工技术与工程",
        "0823交通运输工程",
        "0824船舶与海洋工程",
        "0825航空宇航科学与技术",
        "0826兵器科学与技术",
        "0827核科学与技术",
        "0828农业工程",
        "0829林业工程",
        "0830环境科学与工程",
        "0831生物医学工程",
        "0832食品科学与工程",
        "0833城乡规划学",
        "0835软件工程",
        "0836生物工程",
        "0837安全科学与工程",
        "0838公安技术",
        "0839网络空间安全",
        "0851建筑",
        "0853城乡规划",
        "0854电子信息",
        "0855机械",
        "0856材料与化工",
        "0857资源与环境",
        "0858能源动力",
        "0859土木水利",
        "0860生物与医药",
        "0861交通运输",
        "0862风景园林",
        "0901作物学",
        "0902园艺学",
        "0903农业资源与环境",
        "0904植物保护",
        "0905畜牧学",
        "0906兽医学",
        "0907林学",
        "0908水产",
        "0909草学",
        "0910水土保持与荒漠化防治学",
        "0951农业",
        "0952兽医",
        "0954林学",
        "0955食品与营养",
        "1001基础医学",
        "1002临床医学",
        "1003口腔医学",
        "1004公共卫生与预防医学",
        "1005中医学",
        "1006中西医结合",
        "1007药学",
        "1008中药学",
        "1009特种医学",
        "1011护理学",
        "1012法医学",
        "1051临床医学",
        "1052口腔医学",
        "1053公共卫生",
        "1054护理",
        "1055药学",
        "1056中药学",
        "1057中医",
        "1058医学技术",
        "1059针灸",
        "1201管理科学与工程",
        "1202工商管理学",
        "1203农林经济管理",
        "1204公共管理学",
        "1205信息资源管理",
        "1251工商管理",
        "1252公共管理",
        "1253会计",
        "1254旅游管理",
        "1255图书情报",
        "1256工程管理",
        "1257审计",
        "1301艺术学",
        "1352音乐",
        "1353舞蹈",
        "1354戏剧与影视",
        "1355戏曲与曲艺",
        "1356美术与书法",
        "1357设计",
        "1401集成电路科学与工程",
        "1402国家安全学",
        "1403设计学",
        "1404遥感科学与技术",
        "1405智能科学与技术",
        "1406纳米科学与技术",
        "1407区域国别学",
        "1451文物",
        "1452密码"
    ];
    const undergraduateData = {
        "01哲学": ["0101哲学类"],
        "02经济学": ["0201经济学类", "0202财政学类", "0203金融学类", "0204经济与贸易类"],
        "03法学": ["0301法学类", "0302政治学类", "0303社会学类", "0304民族学类", "0305马克思主义理论类", "0306公安学类"],
        "04教育学": ["0401教育学类", "0402体育学类"],
        "05文学": ["0501中国语言文学类", "0502外国语言文学类", "0503新闻传播类"],
        "06历史学": ["0601历史学类"],
        "07理学": ["0701数学类", "0702物理类", "0703化学类", "0704天文学类", "0705地理科学类", "0706大气科学类", "0708地球物理类", "0709地质学类", "0710生物科学类", "0711心理学类", "0712统计学类"],
        "08工学": ["0801力学类", "0802机械类", "0803仪器类", "0804材料类", "0805能源动力类", "0806电气类", "0807电子信息类", "0808自动化类", "0809计算机类", "0810土木类", "0811水利类", "0812测绘类", "0813化工与制药类", "0814地质类", "0815矿业类", "0816纺织类", "0817轻工类", "0818交通运输类", "0819海洋工程类", "0820航空航天类", "0821兵器类", "0822核工程类", "0823农业工程类", "0824林业工程类", "0825环境科学与工程类", "0826生物医学工程类", "0827食品科学与工程类", "0828建筑类", "0829安全科学与工程类", "0830生物工程类", "0831公安技术类", "0832交叉工程类"],
        "09农学": ["0901植物生产类", "0902自然保护与环境生态类", "0903动物生产类", "0904动物医学类", "0905林学类", "0906水产类", "0907草学类"],
        "10医学": ["1001基础医学类", "1002临床医学类", "1003口腔医学类", "1004公共卫生与预防医学类", "1005中医学类", "1006中西医结合类", "1007药学类", "1008中药学类", "1009法医学类", "1010医学技术类", "1011护理学类"],
        "12管理学": ["1201管理科学与工程", "1202工商管理类", "1203农业经济管理类", "1204公共管理类", "1205图书情报与档案管理类", "1206物流管理与工程类", "1207工业工程类", "1208电子商务类", "1209旅游管理类"],
        "13艺术学": ["1301艺术学理论类", "1302音乐与舞蹈学类", "1303戏剧与影视学类", "1304美术学类", "1305设计学类"]
    };

    const categorySelect = document.getElementById('undergraduate-category');
    const majorSelect = document.getElementById('undergraduate-major');
    const bachelorMajorInput = document.getElementById('bachelor-major');

    // 填充一级学科
    Object.keys(undergraduateData).forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categorySelect.appendChild(option);
    });

    // 根据一级学科更新二级门类
    categorySelect.addEventListener('change', function () {
        const selectedCategory = this.value;
        majorSelect.innerHTML = '<option value="">请选择二级门类</option>';
        if (selectedCategory && undergraduateData[selectedCategory]) {
            undergraduateData[selectedCategory].forEach(major => {
                const option = document.createElement('option');
                option.value = major;
                option.textContent = major;
                majorSelect.appendChild(option);
            });
            majorSelect.disabled = false;
        } else {
            majorSelect.disabled = true;
        }
    });

    // 填充最终值到输入框
    majorSelect.addEventListener('change', function () {
        bachelorMajorInput.value = this.value;
    });

    // 院校数据存储
    let collegeData = [];
    const searchInput = document.getElementById('bachelor-level-name');
    const codeInput = document.getElementById('bachelor-level');
    const resultsContainer = document.getElementById('college-search-results');

    // 从后端获取院校数据
    const fetchColleges = async () => {
        try {
            const response = await fetch("{% url 'get_colleges' %}");
            if (!response.ok) throw new Error('获取院校数据失败');

            const data = await response.json();
            if (data.status === 'success' && data.data) {
                collegeData = data.data;
                console.log(`成功加载 ${collegeData.length} 所院校数据`);
            } else {
                throw new Error(data.message || '院校数据格式错误');
            }
        } catch (error) {
            console.error('加载院校数据出错:', error);
            alert('加载院校数据失败，请刷新页面重试');
        }
    };

    // 搜索院校
    const searchColleges = (query) => {
        if (!query || query.length < 1) return [];

        query = query.toLowerCase();
        return collegeData.filter(college =>
            college.name.toLowerCase().includes(query)
        );
    };

    // 显示搜索结果
    const showSearchResults = (results) => {
        resultsContainer.innerHTML = '';

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="p-2 text-gray-500">未找到匹配的院校</div>';
            resultsContainer.classList.remove('hidden');
            return;
        }

        results.forEach(college => {
            const resultItem = document.createElement('div');
            resultItem.className = 'p-2 hover:bg-gray-100 cursor-pointer';
            resultItem.innerHTML = `
                <div class="font-medium">${college.name}</div>
                <div class="text-xs text-gray-500">${college.code}</div>
            `;

            resultItem.addEventListener('click', () => {
                searchInput.value = college.name;
                codeInput.value = college.code;
                resultsContainer.classList.add('hidden');
            });

            resultsContainer.appendChild(resultItem);
        });

        resultsContainer.classList.remove('hidden');
    };

    // 隐藏搜索结果
    const hideSearchResults = () => {
        setTimeout(() => {
            resultsContainer.classList.add('hidden');
        }, 200);
    };

    // 初始化搜索功能
    const initSearch = () => {
        // 输入事件监听
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length >= 1) {
                const results = searchColleges(query);
                showSearchResults(results);
            } else {
                resultsContainer.classList.add('hidden');
            }
        });

        // 点击外部隐藏结果
        document.addEventListener('click', (e) => {
            if (!searchInput.contains(e.target) && !resultsContainer.contains(e.target)) {
                hideSearchResults();
            }
        });
        document.getElementById('region-other-checkbox').addEventListener('change', function () {
        const otherInput = document.getElementById('region-other-input');
        if (this.checked) {
            otherInput.classList.remove('hidden');
        } else {
            otherInput.classList.add('hidden');
            otherInput.value = ''; // 清空输入内容
        }
    });
        // 键盘导航
        searchInput.addEventListener('keydown', (e) => {
            const resultItems = resultsContainer.querySelectorAll('.p-2');
            const activeItem = resultsContainer.querySelector('.bg-gray-100');
            let index = Array.from(resultItems).indexOf(activeItem);

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                if (resultItems.length > 0) {
                    if (activeItem) activeItem.classList.remove('bg-gray-100');
                    index = (index + 1) % resultItems.length;
                    resultItems[index].classList.add('bg-gray-100');
                    resultItems[index].scrollIntoView({ block: 'nearest' });
                }
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                if (resultItems.length > 0) {
                    if (activeItem) activeItem.classList.remove('bg-gray-100');
                    index = (index - 1 + resultItems.length) % resultItems.length;
                    resultItems[index].classList.add('bg-gray-100');
                    resultItems[index].scrollIntoView({ block: 'nearest' });
                }
            } else if (e.key === 'Enter' && activeItem) {
                e.preventDefault();
                activeItem.click();
            }
        });
    };

    // 初始化页面
    const init = async () => {
        await fetchColleges();
        initSearch();
    };

    // 启动应用
    init();


    // 获取新按钮和新提示词编辑框
    const generateReportWithGoalBtn = document.getElementById('generateReportWithGoalBtn');
    const collegeAnalysisWithGoalPrompt = document.getElementById('college-analysis-with-goal-prompt');
    let message_id = null ;

    // 动态显示/隐藏字段的函数
    function toggleOtherProgrammingInput() {
        const otherCheckbox = document.getElementById('programming-other');
        const otherInput = document.getElementById('programming-other-input');
        if (otherCheckbox.checked) {
            otherInput.classList.remove('hidden');
        } else {
            otherInput.classList.add('hidden');
        }
    }


    function toggleHollandResult() {
        const hollandTest = document.getElementById('holland-test').value;
        const hollandResult = document.getElementById('holland-result');
        const hollandModal = document.getElementById('holland-modal');

        if (hollandTest === '做过测试') {
            hollandResult.classList.remove('hidden');
            hollandModal.classList.add('hidden');
        } else {
            hollandResult.classList.add('hidden');
            hollandModal.classList.remove('hidden');
        }
    }

    // 绑定事件监听器
    document.getElementById('holland-test').addEventListener('change', toggleHollandResult);

    // 霍兰德测试弹窗按钮事件
    document.getElementById('holland-test-yes').addEventListener('click', () => {
        window.location.href = 'https://ai-dev.yantucs.com/console/app/huolande_test_index';
    });

    document.getElementById('holland-test-no').addEventListener('click', () => {
        document.getElementById('holland-modal').classList.add('hidden');
    });
    // 填充跨考方向下拉菜单
    function populateCrossDirectionSelect() {
        const crossDirectionSelect = document.getElementById('cross-direction');
        graduateMajors.forEach(major => {
            const option = document.createElement('option');
            option.value = major;
            option.textContent = major;
            crossDirectionSelect.appendChild(option);
        });
    }

    // 初始化时填充跨考方向下拉菜单
    populateCrossDirectionSelect();

    // 控制“跨考方向”下拉菜单的显示与隐藏
    const isCrossExamSelect = document.getElementById('is-cross-exam');
    const crossDirectionContainer = document.getElementById('cross-direction-container');

    isCrossExamSelect.addEventListener('change', function () {
        if (this.value === 'true') {
            crossDirectionContainer.style.display = 'block';
        } else {
            crossDirectionContainer.style.display = 'none';
        }
    });

    // 生成建议按钮点击事件

    generateAdviceBtn.addEventListener('click', () => {
        // 创建包含所有字段的对象（英文变量名）
        const allFields = {};
        const englishAbility = document.getElementById('english-ability').value;
        const englishScore = document.getElementById('english-score').value;
        const mathBasisSelect = document.getElementById('math-basis-select').value;
        const mathBasisText = document.getElementById('math-basis-text').value;
        console.log('english-ability:', document.getElementById('english-ability').value);
        console.log('math-basis-select:', document.getElementById('math-basis-select').value);
        // 收集是否跨考及跨考方向数据
        const isCrossExam = document.getElementById('is-cross-exam').value === 'true';
        let crossExamData = { is_cross_exam: isCrossExam };

        if (isCrossExam) {
            const crossDirection = document.getElementById('cross-direction').value;
            if (crossDirection) {
                crossExamData.cross_direction = crossDirection;
            }
        }

        // 将 crossExamData 添加到 allFields 中
        allFields.cross_exam = crossExamData;
        // 收集主观诉求字段
        const personalNeeds = [];
        if (document.getElementById('need-math').checked) {
            personalNeeds.push("不考数学");
        }
        if (document.getElementById('need-english').checked) {
            personalNeeds.push("英语不好");
        }
        allFields['personal_needs'] = personalNeeds;
        allFields['bachelor_college'] = searchInput;
        allFields['english_ability'] = englishAbility;
        allFields['english_score'] = englishScore;
        allFields['math_basis_select'] = mathBasisSelect;
        allFields['math_basis_text'] = mathBasisText;
        // 收集所有表单字段
        document.querySelectorAll('#college-analysis-form input, #college-analysis-form select, #college-analysis-form textarea').forEach(element => {
            const labelElement = element.closest('.form-group')?.querySelector('label');
            let label = labelElement?.innerText.replace(':', '').trim() || element.id;

            // 转换为英文变量名
            const englishLabel = chineseToEnglishLabel(label);
            const skipFields = new Set([
                'english_ability',
                'math_basis_select',
                'english_score',
                'math_basis_text'
            ]);

            if (skipFields.has(englishLabel)) {
                return; // 跳过这四个字段的自动赋值
            }
            // 处理不同类型的输入元素
            if (element.tagName === 'SELECT') {
                allFields[englishLabel] = element.value;
            } else if (element.type === 'checkbox') {
                if (!allFields[englishLabel]) allFields[englishLabel] = [];
<!--                if (element.checked) allFields[englishLabel].push(element.value);-->
            } else if (element.type === 'text' || element.type === 'textarea') {
                allFields[englishLabel] = element.value;
            }
        });

        // 单独处理院校目标字段
        const targetCollegeMajor = [];
<!--        for (let i = 1; i <= 3; i++) {-->
<!--            const collegeName = document.getElementById(`target-college-${i}`).value;-->
<!--            const collegeCode = document.getElementById(`target-college-code-${i}`).value;-->
<!--            const majorName = document.getElementById(`target-major-${i}`).value;-->
<!--            const majorCode = document.getElementById(`target-major-code-${i}`).value;-->

<!--            targetCollegeMajor.push({-->
<!--                college_name: collegeName,-->
<!--                college_code: collegeCode,-->
<!--                major_name: majorName,-->
<!--                major_code: majorCode-->
<!--            });-->
<!--        }-->

    // 单独处理目标专业方向字段
    const targetMajorDirections = [];
<!--    for (let i = 1; i <= 3; i++) {-->
<!--        const majorName = document.getElementById(`target_major_direction_${i}`).value;-->
<!--        const majorCode = document.getElementById(`target_major_direction_code_${i}`).value;-->

<!--        if (majorName || majorCode) { // 确保不添加空值-->
<!--            targetMajorDirections.push({-->
<!--                name: majorName,-->
<!--                code: majorCode-->
<!--            });-->
<!--        }-->
<!--    }-->

    allFields['target_major_direction'] = targetMajorDirections;

        // 收集意向地区字段
        const selectedRegions = [];
        // 打印选中的复选框数量（关键调试）
        const checkedBoxes = document.querySelectorAll('.region-option:checked');
        console.log('选中的地区复选框数量：', checkedBoxes.length); // 正常应≥1

        checkedBoxes.forEach(checkbox => {
            console.log('当前选中的复选框value：', checkbox.value); // 检查是否包含"二线城市"和"其他"
            if (checkbox.value === '其他') {
                const otherInput = document.getElementById('region-other-input');
                const otherInputValue = otherInput.value.trim();
                console.log('"其他"输入框的值：', otherInputValue); // 应显示"连云港市"
                if (otherInputValue) {
                    selectedRegions.push(otherInputValue);
                } else {
                    alert('请在"其他"中输入具体城市名');
                }
            } else {
                selectedRegions.push(checkbox.value);
            }
        });

        console.log('最终收集的地区列表：', selectedRegions); // 应显示["二线城市", "连云港市"]
        allFields['preferred_regions'] = selectedRegions;
        // 处理霍兰德测试结果
        const hollandTest = document.getElementById('holland-test').value;
        if (hollandTest === '做过测试') {
            allFields['holland_test_result'] = document.getElementById('holland-result').value;
        } else {
            allFields['holland_test_result'] = '未做测试';
        }

        // 获取提示词编辑窗口的内容
        const collegeAnalysisPrompt = document.getElementById('college-analysis-prompt').value;

        // 创建整合后的studentInfo对象（中文变量名）
        const studentInfo = {
            // 基本信息
            '基本信息': {
                '本科院校代码': allFields['bachelor_level'],
                '本科院校名称': allFields['bachelor_college'],
                '本科专业': allFields['bachelor_major'],
                '是否跨考': allFields['is_cross_exam'],
                '考研年份': allFields['exam_year'],
            },
            '成绩背景': {
                '总GPA区间': allFields['gpa_range'],
                '专业排名': allFields['major_ranking'],
                '科研经历': allFields['research_experience'],
                '竞赛经历': allFields['competition_experience'],
            },
            '能力评估': {
                '英语能力': allFields['english_ability'],
                '分数/详情': allFields['english_score'],
                '数学基础': allFields['math_basis_select'],
                '补充说明':allFields['math_basis_text'],
            },
            '院校需求': {
                '意向地区': allFields['preferred_regions'] ,
                '院校及专业优先级排序': allFields['priority_order'],
<!--                '目标专业方向': allFields['target_major_direction'],-->
                '想要考学硕/专硕': allFields['master_type'],
                '目标院校和专业': targetCollegeMajor
            },
            '个性需求': {
<!--                '个人需求': allFields['personal_needs'],-->
                '硕士类型': allFields['master_degree_type'],
                '霍兰德职业兴趣测试': allFields['holland_test'],
                '霍兰德测试结果': allFields['holland_test_result'],
            },
        };
        tracingDetails.innerHTML = ''; // 清空之前的细节
        // 新增 user_info 变量
        const report_id = "01";

        // 显示加载动画
        loadingOverlay.style.display = 'flex';

        // 发送请求到后端
        fetch("{% url 'college_analysis_new' %}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                all_fields: allFields,  // 所有字段的独立变量（英文）
                student_info: studentInfo, // 整合后的studentInfo对象（中文）
                college_analysis_new: collegeAnalysisPrompt,
                report_id: report_id
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.body;
        })
        .then(stream => {
            const reader = stream.getReader();
            const decoder = new TextDecoder(); // 创建TextDecoder实例
            let result_text = '';

            function readChunk() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        console.log('Stream complete, returning collected data');
                        find_tracing();
                        return;
                    }
                    const textChunkStr = decoder.decode(value, { stream: true });

                    // 根据 \n\n 对流数据分块
                    const chunkArr = textChunkStr.split("\n\n");
                    for (let i = 0; i < chunkArr.length; i++) {
                        const textChunk = chunkArr[i];
                        if (textChunk.startsWith("data: ")) {
                            // 去除前缀"data: "
                            const json_str = textChunk.slice(6);
                            console.log('json_str', json_str);
                            try {
                                // 将匹配到的字符串转换为JSON对象
                                const jsonData = JSON.parse(json_str);
                                if (!message_id) {
                                    message_id = jsonData.message_id;
                                }
                                if (jsonData.answer) {
                                    result_text += jsonData.answer;
                                    resultBox.innerHTML = marked.parse(result_text);
                                } else if (jsonData.err) {
                                    resultBox.innerHTML = jsonData.err;
                                }
                            } catch (error) {
                                console.error('Error parsing JSON', json_str, error);
                            }
                        }
                    }
                    readChunk();
                }).catch(error => {
                    console.error('Error collecting stream data', error);
                });
            }
            readChunk();
        })
        .catch(error => {
            console.error('Error:', error);
            resultBox.innerHTML = '<p>生成建议失败，请重试。</p>';
        })
        .finally(() => {
            // 隐藏加载动画
            loadingOverlay.style.display = 'none';
        });
    });

    generateReportWithGoalBtn.addEventListener('click', () => {
        // 创建包含所有字段的对象（英文变量名）
        const allFields = {};
        const targetCollegeCode = document.getElementById(`target-college-code-1`).value;
        const targetMajorCode = document.getElementById(`target-major-code-1`).value;
        const targetCollegeName = document.getElementById(`target-college-1`).value;
        const targetMajorName = document.getElementById(`target-major-1`).value;
        const bachelorMajorName = document.getElementById(`bachelor-major`).value;
        const bachelorCollegeName = document.getElementById(`bachelor-level-name`).value;
        // 收集主观诉求字段
        const personalNeeds = [];
        if (document.getElementById('need-math').checked) {
            personalNeeds.push("不考数学");
        }
        if (document.getElementById('need-english').checked) {
            personalNeeds.push("英语不好");
        }
        allFields['personal_needs'] = personalNeeds;

        // 收集所有表单字段
        document.querySelectorAll('#college-analysis-form input, #college-analysis-form select, #college-analysis-form textarea').forEach(element => {
            const labelElement = element.closest('.form-group')?.querySelector('label');
            let label = labelElement?.innerText.replace(':', '').trim() || element.id;

            // 转换为英文变量名
            const englishLabel = chineseToEnglishLabel(label);

            // 处理不同类型的输入元素
            if (element.tagName === 'SELECT') {
                allFields[englishLabel] = element.value;
            } else if (element.type === 'checkbox') {
                if (!allFields[englishLabel]) allFields[englishLabel] = [];
<!--                if (element.checked) allFields[englishLabel].push(element.value);-->
            } else if (element.type === 'text' || element.type === 'textarea') {
                allFields[englishLabel] = element.value;
            }
        });

        // 单独处理院校目标字段
        const targetCollegeMajor = [];
<!--        for (let i = 1; i <= 3; i++) {-->
<!--            const collegeName = document.getElementById(`target-college-${i}`).value;-->
<!--            const collegeCode = document.getElementById(`target-college-code-${i}`).value;-->
<!--            const majorName = document.getElementById(`target-major-${i}`).value;-->
<!--            const majorCode = document.getElementById(`target-major-code-${i}`).value;-->

<!--            targetCollegeMajor.push({-->
<!--                college_name: collegeName,-->
<!--                college_code: collegeCode,-->
<!--                major_name: majorName,-->
<!--                major_code: majorCode-->
<!--            });-->
<!--        }-->

        // 新建 studentInfoForGoal 变量，只包含目标院校相关字段
        const studentInfoForGoal = {
            '院校需求': {
                '本科院校':bachelorCollegeName,
                '本科专业':bachelorMajorName,
                '考研目标院校':targetCollegeName,
                '考研目标专业':targetMajorName,
            }
        };

        const report_id = "02";

        // 获取提示词编辑窗口的内容
        const collegeAnalysisWithGoalPromptValue = collegeAnalysisWithGoalPrompt.value;

        // 显示加载动画
        loadingOverlay.style.display = 'flex';

        fetch("{% url 'college_analysis_new_with_goal' %}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                all_fields: allFields,
                student_info_for_goal: studentInfoForGoal, // 使用新的中文结构体
                college_analysis_new_with_goal: collegeAnalysisWithGoalPromptValue,
                report_id: report_id,
                target_college_code :targetCollegeCode,
                target_major_code :targetMajorCode,
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.body;
        })
        .then(stream => {
            const reader = stream.getReader();
            const decoder = new TextDecoder(); // 创建TextDecoder实例
            let result_text = '';

            function readChunk() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        console.log('Stream complete, returning collected data');
                        find_tracing();
                        return;
                    }
                    const textChunkStr = decoder.decode(value, { stream: true });

                    // 根据 \n\n 对流数据分块
                    const chunkArr = textChunkStr.split("\n\n");
                    for (let i = 0; i < chunkArr.length; i++) {
                        const textChunk = chunkArr[i];
                        if (textChunk.startsWith("data: ")) {
                            // 去除前缀"data: "
                            const json_str = textChunk.slice(6);
                            console.log('json_str', json_str);
                            try {
                                // 将匹配到的字符串转换为JSON对象
                                const jsonData = JSON.parse(json_str);
                                if (!message_id) {
                                    message_id = jsonData.message_id;
                                }
                                if (jsonData.answer) {
                                    result_text += jsonData.answer;
                                    resultBox.innerHTML = marked.parse(result_text);
                                } else if (jsonData.err) {
                                    resultBox.innerHTML = jsonData.err;
                                }
                            } catch (error) {
                                console.error('Error parsing JSON', json_str, error);
                            }
                        }
                    }
                    readChunk();
                }).catch(error => {
                    console.error('Error collecting stream data', error);
                });
            }
            readChunk();
        })
        .catch(error => {
            console.error('Error:', error);
            resultBox.innerHTML = '<p>生成报告失败，请重试。</p>';
        })
        .finally(() => {
            // 隐藏加载动画
            loadingOverlay.style.display = 'none';
        });
    });

    // 辅助函数：中文标签转英文变量名
    function chineseToEnglishLabel(chineseLabel) {
        const mapping = {
            '本科院校代码': 'bachelor_level',
            '本科院校名称': 'bachelor_college',
            '本科专业': 'bachelor_major',
            '总GPA区间': 'gpa_range',
            '专业排名': 'major_ranking',
            '科研经历': 'research_experience',
            '竞赛经历': 'competition_experience',
            '英语能力': 'english_ability',
            '分数/详情': 'english_score',
            '数学基础': 'math_basis_select',
            '补充说明': 'math_basis_text',
            '意向地区': 'preferred_regions',
            '院校及专业优先级排序': 'priority_order',
            '目标专业方向': 'target_major_direction',
            '想要考学硕/专硕': 'master_type',
            '霍兰德职业兴趣测试': 'holland_test',
            '霍兰德测试结果': 'holland_result',
            '主管避开条件': 'personal_needs',
            '硕士类型': 'master_degree_type',
        };
        return mapping[chineseLabel] || chineseLabel.toLowerCase().replace(/[^a-z0-9]+/g, '_');
    }

    // 辅助函数：获取考生身份（拼接详情）

    function find_tracing() {
    if (!message_id) return;
    const $el = $('#tracingDetails');  // 指定展示追踪信息的容器
    $.ajax({
        type: 'GET',
        url: "{% url 'message_tracing' %}",
        data: { message_id: message_id },
        success: function(response) {
            $('#loadingOverlay').css('display', 'none');
            const results = response.data;

            $el.empty(); // 确保清空之前的内容

            if (results && results.tracing) {
                results.tracing.forEach(function(traceDetail) {
                    const detailElement = document.createElement('p');
                    detailElement.textContent = traceDetail;
                    $el.append(detailElement);
                });
            } else {
                $el.append('<p>没有找到追踪信息。</p>');
            }
        },
        error: function(xhr, status, error) {
            alert('请求失败');
            $('#loadingOverlay').css('display', 'none');
            console.error('AJAX Error:', xhr.responseText);
        }
    });
}
});

    </script>
</body>
</html>