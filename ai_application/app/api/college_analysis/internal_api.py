import requests

def get_college_code(school_name, major_name):
    url = f"http://query2.kaoyan-vip.cn/internal_api/v1/school/?name={school_name}"

    payload = {}
    headers = {}

    response = requests.request("GET", url, headers=headers, data=payload)

    # 解析 JSON 响应
    response_json = response.json()

    # 提取 data 字段
    if response_json.get("code") == 20000:
        data = response_json.get("data", [])
        if data:
            college_code = data[0].get("code")
            print(f"College Code: {college_code}")
            catalog_data = get_catalog(college_code, major_name)
            return catalog_data
        else:
            print("No data found.")
            return []
    else:
        print(f"Error: {response_json.get('msg')}")
        return []

def get_catalog(school_code, major_name):
    url = f"http://query2.kaoyan-vip.cn/internal_api/v1/catalog/?&school_code={school_code}&major_name={major_name}"

    payload = {}
    headers = {}

    response = requests.request("GET", url, headers=headers, data=payload)

    # 解析 JSON 响应
    response_json = response.json()

    # 提取 data 字段
    if response_json.get("code") == 20000:
        data = response_json.get("data", [])
        if data:
            print(f"Catalog Data: {data}")
            return data
        else:
            print("No data found.")
            return []
    else:
        print(f"Error: {response_json.get('msg')}")
        return []
