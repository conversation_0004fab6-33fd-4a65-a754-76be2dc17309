from celery import shared_task
from django.conf import settings
from django.utils import timezone

from app.models import CourseNoteTask, CourseNoteTaskChangeDetail, MessageTask, CourseNoteTaskDebug
from app.services.course_note.course_note_task_service import CourseNoteTaskService


@shared_task(queue='message_task')
def auto_check_course_note_task():
    # 获取每个章最近一次需要更新的内容
    qs = CourseNoteTask.objects.filter(status='not_start', task_debug__isnull=True).order_by('id')
    chapter_task_map = {}
    for i in qs:
        course_chapter_id = f'{i.course_id}_{i.chapter_id}'
        if course_chapter_id not in chapter_task_map:
            chapter_task_map[course_chapter_id] = i

    # 处理每个章的内容
    for task in chapter_task_map.values():
        CourseNoteTaskService.process_task(task)

    # 处理debug数据
    if settings.ENVIRONMENT == settings.ENV_TEST:
        debug_qs = CourseNoteTask.objects.filter(status='not_start', task_debug__isnull=False).order_by('id')
        for debug_task in debug_qs:
            CourseNoteTaskService.process_task(debug_task)


@shared_task(queue='message_task')
def auto_check_course_video_note_task():
    qs = CourseNoteTaskChangeDetail.objects.filter(status='not_start', is_deleted=False)[:10]
    for i in qs:
        CourseNoteTaskService.process_course_video_note_task(i)


@shared_task(queue='message_task')
def auto_check_course_video_note_gen_task():
    qs = CourseNoteTaskChangeDetail.objects.filter(status='processing')
    for i in qs:
        message_task: MessageTask = i.message.messagetask_set.first()
        if message_task.process_status == MessageTask.ProcessStatus.success:
            i.status = 'success'
            i.video_note = message_task.message.answer
            i.completed_at = timezone.now()
            i.save()

            note_task = i.task
            # 测试环境下，debug不保存note信息
            if settings.ENVIRONMENT == settings.ENV_TEST and note_task.task_debug:
                continue

            CourseNoteTaskService.save_video_note(i)
        elif message_task.process_status == MessageTask.ProcessStatus.fail:
            if message_task.retry_times >= 3:
                i.status = 'fail'
                i.save()


@shared_task(queue='message_task')
def auto_check_course_note_status_task():
    qs = CourseNoteTask.objects.filter(status='processing')
    for task in qs:
        task_detail = task.coursenotetaskchangedetail_set.filter(is_deleted=False)

        is_task_finished = True
        is_task_fail = False
        for d in task_detail:
            if d.status in ('not_start', 'processing'):
                is_task_finished = False
                break
            elif d.status == 'fail':
                is_task_fail = True
                break

        if is_task_finished:
            task.status = 'fail' if is_task_fail else 'success'
            if task.status == 'success':
                task.completed_at = timezone.now()
            task.save()


@shared_task(queue='message_task')
def auto_check_course_note_task_debug():
    debug_qs = CourseNoteTaskDebug.objects.filter(status='processing')

    for task_debug in debug_qs:
        debug_relation_tasks = task_debug.coursenotetask_set.filter(is_deleted=False)

        is_task_finished = True
        is_task_fail = False

        for r_task in debug_relation_tasks:
            if r_task.status in ('not_start', 'processing'):
                is_task_finished = False
                break
            elif r_task.status == 'fail':
                is_task_fail = True
                break

        if is_task_finished:
            task_debug.status = 'fail' if is_task_fail else 'success'
            if task_debug.status == 'success':
                task_debug.completed_at = timezone.now()
            task_debug.save()
