# Generated by Django 4.2 on 2025-07-17 09:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0060_message_malicious_attack_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CityGDP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='城市')),
                ('rank', models.IntegerField(blank=True, null=True, verbose_name='GDP排名')),
                ('score', models.FloatField(blank=True, null=True, verbose_name='GDP得分')),
            ],
            options={
                'verbose_name': '城市GDP信息',
                'verbose_name_plural': '城市GDP信息',
                'db_table': 'ai_city_gdp',
            },
        ),
        migrations.CreateModel(
            name='KaoYanQueryResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('undergraduate_secondary_major', models.CharField(blank=True, max_length=255, null=True, verbose_name='本科二级专业名')),
                ('related_college_majors', models.JSONField(blank=True, null=True, verbose_name='关联度高的院校专业信息')),
            ],
            options={
                'verbose_name': '关联度高的院校专业信息查询结果',
                'db_table': 'ai_kao_yan_query_result',
            },
        ),
        migrations.CreateModel(
            name='MajorInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('major_code', models.CharField(blank=True, max_length=10, null=True, verbose_name='专业代码')),
                ('major_evaluation', models.CharField(blank=True, max_length=10, null=True, verbose_name='专业评估')),
                ('major_type', models.CharField(blank=True, max_length=10, null=True, verbose_name='专业类型')),
                ('college_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='院校名称')),
                ('college_code', models.CharField(blank=True, max_length=10, null=True, verbose_name='院校代码')),
                ('college_level', models.CharField(blank=True, max_length=255, null=True, verbose_name='院校层次')),
                ('college_nature', models.CharField(blank=True, max_length=255, null=True, verbose_name='院校性质')),
                ('college_rank', models.CharField(blank=True, max_length=255, null=True, verbose_name='院校排名')),
                ('is_doctoral_point', models.BooleanField(blank=True, null=True, verbose_name='是否是博士点')),
                ('has_scholarship', models.BooleanField(blank=True, null=True, verbose_name='是否有奖学金')),
                ('province', models.CharField(blank=True, max_length=100, null=True, verbose_name='所在省份')),
                ('region_category', models.CharField(blank=True, max_length=10, null=True, verbose_name='地区类别')),
                ('national_line_score', models.FloatField(blank=True, null=True, verbose_name='国家线分数')),
            ],
            options={
                'verbose_name': '专业信息',
                'verbose_name_plural': '专业信息',
                'db_table': 'ai_major_info',
            },
        ),
        migrations.CreateModel(
            name='MajorRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('undergraduate_first_discipline', models.CharField(blank=True, max_length=255, null=True, verbose_name='本科一级学科')),
                ('undergraduate_second_category', models.CharField(blank=True, max_length=255, null=True, verbose_name='本科二级门类')),
                ('graduate_first_discipline', models.CharField(blank=True, max_length=255, null=True, verbose_name='研究生一级学科')),
                ('graduate_second_category_code', models.CharField(blank=True, max_length=10, null=True, verbose_name='研究生二级门类代码')),
                ('graduate_second_category', models.CharField(blank=True, max_length=255, null=True, verbose_name='研究生二级门类')),
                ('graduate_third_major_code', models.CharField(blank=True, max_length=10, null=True, verbose_name='研究生三级专业代码')),
                ('graduate_third_major', models.CharField(blank=True, max_length=255, null=True, verbose_name='研究生三级专业')),
                ('relation_level', models.IntegerField(blank=True, null=True, verbose_name='关联度等级')),
            ],
            options={
                'verbose_name': '专业关联度',
                'verbose_name_plural': '专业关联度',
                'db_table': 'ai_major_relation',
            },
        ),
        migrations.CreateModel(
            name='UndergraduateCollegeInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(blank=True, max_length=255, null=True, verbose_name='院校名称')),
                ('undergraduate_code', models.CharField(blank=True, max_length=10, null=True, verbose_name='本科院校代码')),
                ('level_display', models.CharField(blank=True, max_length=255, null=True, verbose_name='院校层次')),
            ],
            options={
                'verbose_name': '本科院校信息',
                'verbose_name_plural': '本科院校信息',
                'db_table': 'ai_undergraduate_college_info',
            },
        ),
        migrations.CreateModel(
            name='UndergraduateMajorCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('first_discipline', models.CharField(blank=True, max_length=100, null=True, verbose_name='一级学科')),
                ('second_category', models.CharField(blank=True, max_length=100, null=True, verbose_name='二级门类编码')),
                ('core_courses', models.TextField(blank=True, default='', null=True, verbose_name='主干课程')),
            ],
            options={
                'verbose_name': '本科专业核心课程',
                'verbose_name_plural': '本科专业核心课程',
                'db_table': 'ai_undergraduate_major_course',
            },
        ),
    ]
