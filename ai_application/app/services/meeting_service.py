import logging
from re import M

from django.conf import settings

import requests
import json 
import time
from django.db import transaction 
from app.api.dto import CozeChatMessageDto
from app.libs.coze_api import CozeApiClient
from app.models import App, Account
from app.services.coze_app_generate_service import coze_chat_block
from app.libs.coze_api import CozeApiClient
from app.models.meeting_call import MeetingVoiceRecord, MeetingQuestion

logger = logging.getLogger(__name__)


def get_debug_coze_api_client():
    COZE_JWT_OAUTH_CLIENT_ID = '*************'
    COZE_JWT_OAUTH_PRIVATE_KEY = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    COZE_JWT_OAUTH_PUBLIC_KEY_ID = 'N8B5Ptu6XjQKWv7AFw8Qazd8WPoTgFW7NGLZEwNihGw'

    return CozeApiClient(
        coze_api_base=settings.COZE_API_BASE,
        jwt_oauth_client_id=COZE_JWT_OAUTH_CLIENT_ID,
        jwt_oauth_private_key=COZE_JWT_OAUTH_PRIVATE_KEY,
        jwt_oauth_public_key_id=COZE_JWT_OAUTH_PUBLIC_KEY_ID,
        expire_time=settings.COZE_JWT_TOKEN_EXPIRE_TIME,
    ).coze

# coze_api_client = get_debug_coze_api_client()


class MeetingService:

    @classmethod
    def process_unconverted_records(cls, batch_size=1):
        """
        选未转换的音频文件并进行转换
        :param batch_size: 每次处理的记录数量
        :return: (成功数量, 失败数量)
        """
        unconverted = MeetingVoiceRecord.objects.filter(
            is_deleted=False,
            voice_file__isnull=False,
            is_convert_voice=False
        ).order_by('id')[:batch_size]
        
        success, fail = 0, 0
        for record in unconverted:
            try:
                if cls.convert_voice_file(record):
                    success += 1
                else:
                    fail += 1
                    record.is_convert_voice = True
                    record.save()
            except Exception as e:
                print(f"处理记录ID {record.id} 时出错: {str(e)}")
                fail += 1
        return success, fail

    @classmethod
    def convert_voice_file(cls, record):
        """
        将音频文件转换为文字内容并保存到数据库
        :param record: MeetingVoiceRecord实例
        :return: bool 是否成功
        """
        if not record.voice_file:
            return False
            
        try:
            # 使用字节跳动的语音识别API
            appid = settings.BYTE_SPEECH_APP_ID
            token = settings.BYTE_SPEECH_TOKEN
            cluster = settings.BYTE_SPEECH_CLUSTER
            service_url = 'https://openspeech.bytedance.com/api/v1/auc'
            headers = {'Authorization': f'Bearer; {token}'}
            
            # 提交任务
            request = {
                "app": {"appid": appid, "token": token, "cluster": cluster},
                "user": {"uid": "388808087185088_demo"},
                "audio": {"format": "wav", "url": record.voice_file},
                "additions": {'with_speaker_info': 'False'}
            }
            
            response = requests.post(
                f"{service_url}/submit",
                data=json.dumps(request),
                headers=headers
            )
            try:
                response.raise_for_status()
            except Exception as ee:
                logger.error(response.text)
                raise ee

            resp_data = response.json()
            task_id = resp_data['resp']['id']
            
            # 查询任务状态
            start_time = time.time()
            while True:
                time.sleep(1)
                query_data = {
                    'appid': appid,
                    'token': token,
                    'id': task_id,
                    'cluster': cluster
                }
                response = requests.post(
                    f"{service_url}/query",
                    data=json.dumps(query_data),
                    headers=headers
                )
                resp_data = response.json()
                
                if resp_data['resp']['code'] == 1000:  # 成功
                    # 提取完整的对话文本
                    full_text = resp_data['resp']['text']  # 直接获取顶层text字段
                    utterances = resp_data['resp'].get('utterances', [])
                    last_utterance = utterances[-1]
                    total_duration = last_utterance['end_time'] / 1000  # 转换为秒
                    with transaction.atomic():
                        record.voice_content = full_text
                        record.is_convert_voice = True
                        record.voice_duration = total_duration
                        record.save()
                    return True
                elif resp_data['resp']['code'] < 2000:  # 失败
                    return False
                if time.time() - start_time > 300:  # 超时5分钟
                    return False
                    
        except Exception as e:
            logger.error(f"音频转文字失败: {str(e)}")
            logger.exception(e)
            return False

    @classmethod
    def process_not_extracted_records(cls, batch_size=1):
        """
        选未提取的音频文件并进行转换
        :param batch_size: 每次处理的记录数量
        :return: (成功数量, 失败数量)
        """
        not_extracted_ids = list(MeetingVoiceRecord.objects.filter(
            is_deleted=False,
            voice_content__isnull=False,
            is_convert_voice=True,
            extract_question_status='NOT_START',
            is_extract_question=False
        ).order_by('id').values_list('id', flat=True)[:batch_size])

        MeetingVoiceRecord.objects.filter(id__in=not_extracted_ids).update(extract_question_status='ING')

        id_list = MeetingVoiceRecord.objects.filter(
            is_deleted=False,
            id__in=not_extracted_ids
        ).order_by('id')
        success, fail = 0, 0
        for record in id_list:
            print(record.id)
            try:
                if cls.extract_question(record):
                    success += 1
                else:
                    fail += 1
            except Exception as e:
                print(f"处理记录ID {record.id} 时出错: {str(e)}")
                fail += 1
        return success, fail

    @classmethod
    def extract_question(cls, meeting_record: MeetingVoiceRecord):
        app_model = App.objects.filter(app_type='meeting_extract_question').first()
        dto = CozeChatMessageDto(
            app_id=app_model.app_no,
            query=meeting_record.voice_content,
            stream=False
        )

        try:
            answer = coze_chat_block(
                app_model=app_model,
                dto=dto,
                from_account=Account.objects.first(),
            )
            # 更新记录状态
            meeting_record.extract_question_content = answer
            meeting_record.is_extract_question = True
            meeting_record.save()

            answer_data = json.loads(answer)
            ## answer不是json格式
            if not isinstance(answer_data, dict):

                logger.error(f"返回的答案不是有效的JSON格式: {answer}")
                meeting_record.extract_question_status = 'FAIL'
                meeting_record.save()
                return False
            if answer_data.get('consultation_summary', {}).get('total_questions', 0) > 0:
                with transaction.atomic():
                    for category in answer_data['consultation_summary']['question_categories']:
                        
                        for qa_pair in category.get('qa_pairs', []):
                            MeetingQuestion.objects.create(
                                stage=answer_data['consultation_summary']['student_stage'],
                                purpose=answer_data['consultation_summary']['purpose'],
                                meeting_record_id=meeting_record.id,
                                question_type=category['category'],
                                question_content=qa_pair['student_question'],  
                                student_features=category['student_features'],
                                teacher_answer=qa_pair['teacher_answer'],
                                analyse=category.get('analysis_notes', '')
                            )
            # 更新记录状态
            meeting_record.extract_question_status = 'SUCCESS'
            meeting_record.save()
        except Exception as e:
            logger.exception(e)
            logger.error(f"保存问题到数据库失败: {str(e)}")
            meeting_record.extract_question_status = 'FAIL'
            meeting_record.save()