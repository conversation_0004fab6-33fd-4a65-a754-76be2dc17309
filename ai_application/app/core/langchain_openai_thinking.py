from typing import Union, Optional

import openai
from django.conf import settings
from langchain_core.messages import AIMessageChunk
from langchain_core.outputs import ChatR<PERSON>ult, ChatGenerationChunk
from langchain_openai import Chat<PERSON>penA<PERSON>

from app.models import PromptTemplate


class ChatOpenAIThinking(ChatOpenAI):

    def _create_chat_result(
        self,
        response: Union[dict, openai.BaseModel],
        generation_info: Optional[dict] = None,
    ) -> ChatResult:
        rtn = super()._create_chat_result(response, generation_info)

        if not isinstance(response, openai.BaseModel):
            return rtn

        if hasattr(response.choices[0].message, "reasoning_content"):  # type: ignore
            rtn.generations[0].message.additional_kwargs["reasoning_content"] = (
                response.choices[0].message.reasoning_content  # type: ignore
            )
        # Handle use via OpenRouter
        elif hasattr(response.choices[0].message, "model_extra"):  # type: ignore
            model_extra = response.choices[0].message.model_extra  # type: ignore
            if isinstance(model_extra, dict) and (
                reasoning := model_extra.get("reasoning")
            ):
                rtn.generations[0].message.additional_kwargs["reasoning_content"] = (
                    reasoning
                )

        return rtn

    def _convert_chunk_to_generation_chunk(
        self,
        chunk: dict,
        default_chunk_class: type,
        base_generation_info: Optional[dict],
    ) -> Optional[ChatGenerationChunk]:
        generation_chunk = super()._convert_chunk_to_generation_chunk(
            chunk,
            default_chunk_class,
            base_generation_info,
        )

        if (choices := chunk.get("choices")) and generation_chunk:
            top = choices[0]
            if isinstance(generation_chunk.message, AIMessageChunk):
                if reasoning_content := top.get("delta", {}).get("reasoning_content"):
                    generation_chunk.message.additional_kwargs["reasoning_content"] = (
                        reasoning_content
                    )
                # Handle use via OpenRouter
                elif reasoning := top.get("delta", {}).get("reasoning"):
                    generation_chunk.message.additional_kwargs["reasoning_content"] = (
                        reasoning
                    )

        return generation_chunk


def get_llm_by_prompt_template(prompt_template: PromptTemplate) -> ChatOpenAI:
    model_name = prompt_template.model_id
    # 设置默认模型
    if not model_name:
        model_name = 'doubao-1-5-pro-32k-250115'
    model_params = prompt_template.model_params_dict
    temperature = model_params.get('temperature')
    max_tokens = model_params.get('max_tokens')

    return ChatOpenAIThinking(
        openai_api_key=settings.DOUBAO_API_KEY,
        openai_api_base=settings.DOUBAO_API_BASE,
        model_name=model_name,
        temperature=temperature,
        max_tokens=max_tokens,
        model_kwargs={
            'stream_options': {"include_usage": True}
        },
    )
