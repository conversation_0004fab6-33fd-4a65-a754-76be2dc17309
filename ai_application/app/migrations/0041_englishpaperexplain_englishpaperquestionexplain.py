# Generated by Django 4.2 on 2025-04-11 09:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0040_merge_20250410_2159'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnglishPaperExplain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(db_index=True, max_length=100, verbose_name='用户id')),
                ('answer_id', models.Char<PERSON><PERSON>(db_index=True, max_length=100, verbose_name='答卷id')),
                ('task_id', models.CharField(db_index=True, max_length=100, verbose_name='任务id')),
                ('summary_req_params', models.JSONField(null=True, verbose_name='总结请求参数')),
                ('summary_status', models.CharField(blank=True, max_length=100, verbose_name='总结生成状态')),
                ('summary_content', models.JSONField(null=True, verbose_name='总结内容')),
                ('status', models.CharField(blank=True, max_length=100, verbose_name='解析状态')),
                ('fail_reason', models.TextField(null=True, verbose_name='失败原因')),
            ],
            options={
                'verbose_name': '英语试卷解读',
                'verbose_name_plural': '英语试卷解读',
                'db_table': 'ai_english_paper_explain',
            },
        ),
        migrations.CreateModel(
            name='EnglishPaperQuestionExplain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_id', models.CharField(db_index=True, max_length=100, verbose_name='题目id')),
                ('question_type', models.CharField(max_length=100, verbose_name='题型')),
                ('req_params', models.JSONField(null=True, verbose_name='请求参数')),
                ('sub_question_num', models.IntegerField(default=0, verbose_name='小题数量')),
                ('sub_right_question_num', models.IntegerField(default=0, verbose_name='小题正确数量')),
                ('status', models.CharField(blank=True, max_length=100, verbose_name='解析状态')),
                ('explain_content', models.JSONField(null=True, verbose_name='解析内容')),
                ('paper_explain', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishpaperexplain')),
            ],
            options={
                'verbose_name': '英语试卷题目解读',
                'verbose_name_plural': '英语试卷题目解读',
                'db_table': 'ai_english_paper_question_explain',
            },
        ),
    ]
