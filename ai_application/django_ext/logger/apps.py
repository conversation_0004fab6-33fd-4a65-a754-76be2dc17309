from django.apps import AppConfig
from django.conf import settings
from django.utils.log import configure_logging
from django.utils.translation import gettext_lazy as _

from django_ext.logger.settings import LOGGING


class LoggerConfig(AppConfig):
    name = 'django_ext.logger'
    verbose_name = _("Logger使用")

    def ready(self):
        if getattr(settings, 'LOGGING', {}):
            return

        new_loggers = LOGGING
        if hasattr(settings, 'CUS_LOGGING'):
            old_loggers = settings.YT_LOGGING
            handlers = old_loggers.pop('handlers') if 'handlers' in old_loggers else {}
            loggers = old_loggers.pop('loggers') if 'loggers' in old_loggers else {}
            new_loggers.update(loggers)
            new_loggers['handlers'].update(handlers)
            new_loggers['loggers'].update(loggers)

        settings.LOGGING = new_loggers
        configure_logging(settings.LOGGING_CONFIG, settings.LOGGING)
