import secrets
import string
import uuid

from django.core.cache import caches

from app.models import Account


def gen_api_key():
    uuid_str = uuid.uuid4().hex[:16]
    return f'ak_{uuid_str}'


def gen_secret_key():
    alphabet = string.ascii_letters + string.digits
    secret_key = ''.join(secrets.choice(alphabet) for _ in range(32))
    return secret_key.lower()


def create_account(name):
    return Account.objects.create(
        name=name,
        api_key=gen_api_key(),
        secret_key=gen_secret_key()
    )


def get_all_account() -> dict:
    local_cache = caches['local']
    accounts_map = local_cache.get('accounts')
    if accounts_map is None:
        accounts = Account.objects.all()
        accounts_map = {account.api_key: account for account in accounts}
        local_cache.set('accounts', accounts_map, timeout=3600)
    return accounts_map
