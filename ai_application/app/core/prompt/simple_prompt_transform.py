from app.constants.app import AppMode
from app.core.file import file_manager
from app.core.prompt.conversation_buffer_memory import Conversation<PERSON>ufferMemory
from app.core.prompt.entities import UserPromptMessage, SystemPromptMessage, PromptMessage, PromptMessageContent, \
    TextPromptMessageContent
from app.core.prompt.prompt_transform import PromptTransform


class SimplePromptTransform(PromptTransform):
    def get_prompt(
            self,
            app_mode: AppMode,
            pre_prompt: str,
            query: str,
            files: list | None = None,
            memory: ConversationBufferMemory | None = None,
            query_in_prompt: bool = False,
            history_messages: list[PromptMessage] | None = None
    ):
        if app_mode == app_mode.CHAT:
            return self._get_chat_model_prompt_messages(
                pre_prompt, query, files, memory, history_messages
            )
        else:
            return self._get_completion_model_prompt_messages(
                pre_prompt, query, query_in_prompt=query_in_prompt,
                files=files
            )

    def _get_chat_model_prompt_messages(
            self,
            pre_prompt: str,
            query: str,
            files: list | None = None,
            memory: ConversationBufferMemory | None = None,
            history_messages: list[PromptMessage] | None = None
    ):
        if files is None:
            files = []

        if history_messages is None:
            history_messages = []

        prompt_messages = []
        if pre_prompt and query:
            prompt_messages.append(SystemPromptMessage(content=pre_prompt))

        if memory:
            prompt_messages = self._append_chat_histories(memory, prompt_messages)

        if history_messages:
            prompt_messages.extend(history_messages)

        if query:
            user_text = query
        else:
            user_text = pre_prompt

        if files:
            prompt_message_contents: list[PromptMessageContent] = [TextPromptMessageContent(data=user_text)]
            for file in files:
                prompt_message_contents.append(file_manager.to_prompt_message_content(file))
            prompt_messages.append(UserPromptMessage(content=prompt_message_contents))
        else:
            prompt_messages.append(UserPromptMessage(content=user_text))

        return prompt_messages

    def _get_completion_model_prompt_messages(
            self,
            pre_prompt: str,
            query: str,
            query_in_prompt: bool = False,
            files: list | None = None,
    ):
        if not pre_prompt:
            raise ValueError('prompt is empty')

        if files is None:
            files = []

        prompt_messages = []
        if query_in_prompt:
            real_query = pre_prompt.replace('{{query}}', query)
        else:
            if query:
                prompt_messages.append(SystemPromptMessage(content=pre_prompt))
                real_query = query
            else:
                real_query = pre_prompt

        if files:
            prompt_message_contents: list[PromptMessageContent] = [TextPromptMessageContent(data=real_query)]
            for file in files:
                prompt_message_contents.append(file_manager.to_prompt_message_content(file))
            prompt_messages.append(UserPromptMessage(content=prompt_message_contents))
        else:
            prompt_messages.append(UserPromptMessage(content=real_query))

        return prompt_messages
