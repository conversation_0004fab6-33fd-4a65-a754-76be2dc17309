from app.api.serializers import KnowledgeListSerializer
from app.errors import Parameter<PERSON>rror, KnowledgeNotResultError
from app.models import SubjectDomain, KnowledgeLibrary
from django_ext import base_view
from django_ext.response import make_response


class KnowledgeSubjectListView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        subject_data = {}
        qs = SubjectDomain.objects.filter(
            is_deleted=False, is_knowledge_search_enable=True).order_by('sort', 'id')
        for q in qs:
            if q.subject_code in subject_data:
                subject_data[q.subject_code]['details'].append({
                    'subject_id': q.main_subject_code, 'subject_name': q.main_subject_name
                })
            else:
                subject_data[q.subject_code] = {
                    'subject_id': q.subject_code,
                    'subject_name': q.subject_name,
                    'details': [{'subject_id': q.main_subject_code, 'subject_name': q.main_subject_name}]
                }

        data = list(subject_data.values())
        return make_response(data)
        # data = [{
        #     'code': i.main_subject_code,
        #     'name': i.main_subject_name,
        # } for i in qs]
        # return make_response(data)


class SubjectKnowledgeListView(base_view.BaseListView):
    serializer_class = KnowledgeListSerializer
    page = False

    # def get_page(self):
    #     is_page = self.request.query_params.get('is_page')
    #     return False if is_page == '0' else True

    def get_queryset(self):
        subject_code = self.request.query_params.get('subject_code')
        if not subject_code:
            return KnowledgeLibrary.objects.none()

        subject_domain = SubjectDomain.objects.filter(
            is_deleted=False, is_knowledge_search_enable=True,
            main_subject_code=subject_code
        ).first()
        if not subject_domain:
            return KnowledgeLibrary.objects.none()

        return KnowledgeLibrary.objects.filter(
            is_deleted=False, nature=KnowledgeLibrary.Nature.major,
            subject_domain=subject_domain
        ).order_by('first_letter', 'sort', 'id')

    def fill_data(self, data):
        new_data = []
        special_data = []
        for i in data:
            if i['first_letter'] == '#':
                special_data.append(i)
            else:
                new_data.append(i)
        new_data.extend(special_data)
        return new_data


class SubjectKnowledgeDetailView(base_view.BaseListView):

    def get(self, request, *args, **kwargs):
        knowledge_id = request.query_params.get('knowledge_id')
        if not knowledge_id:
            raise ParameterError()

        knowledge: KnowledgeLibrary = KnowledgeLibrary.objects.filter(
            is_deleted=False, nature=KnowledgeLibrary.Nature.major, id=knowledge_id
        ).first()
        if not knowledge:
            raise KnowledgeNotResultError()

        if knowledge.content:
            knowledge_content = knowledge.content
        else:
            knowledge_content = '知识点详情暂未获取，请稍后'

        return make_response({
            'id': knowledge.id,
            'name': knowledge.name,
            'desc': knowledge.desc,
            'content': knowledge_content
        })
