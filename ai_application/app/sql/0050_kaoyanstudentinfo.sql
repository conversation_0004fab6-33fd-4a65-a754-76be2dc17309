--
-- Create model Ka<PERSON><PERSON>anStudentInfo
--
CREATE TABLE `ai_kao_yan_student_info` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `student_info` json NULL, `college_analysis` json NULL, `intensive_choice` json NULL, `steady_choice` json NULL, `safety_choice` json NULL);
CREATE INDEX `ai_kao_yan_student_info_add_time_0fff48e0` ON `ai_kao_yan_student_info` (`add_time`);