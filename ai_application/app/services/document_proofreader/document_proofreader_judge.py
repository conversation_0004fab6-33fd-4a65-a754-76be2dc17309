"""
AI 智能文档校对器 - 异步版本
=============================

这是一个基于大语言模型的智能文档校对系统, 支持异步并发处理, 
能够自动识别和修正中文文档中的语法错误、错别字等问题。

主要功能：
- 🚀 异步并发处理, 大幅提升校对速度
- 🧠 智能错误识别和修正建议
- 📝 支持 Markdown、.docx、.doc多种格式文档
- 🔢 自动错误ID管理和累加

"""

import re
import os
import django
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import List, Tuple, Dict, Any, Optional, Callable
from pathlib import Path
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from django.conf import settings

# 设置Django环境
import sys
# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()



class ChunkProofreadResult:
    """
    单个文本块的校对结果
    
    在异步处理时, 每个文本块都会生成一个这样的结果, 
    最后再合并成最终的校对结果。
    """
    def __init__(self, chunk_index: int, labeled_text: str, corrected_text: str, error_count: int):
        self.chunk_index = chunk_index        # 文本块索引（用于排序）
        self.labeled_text = labeled_text      # 带错误标记的文本
        self.corrected_text = corrected_text  # 校正后的文本
        self.error_count = error_count        # 该块中的错误数量


# ============================================================================
# 核心异步校对器
# ============================================================================

class AsyncDocumentProofreader:
    """
    异步文档校对器
    
    这是核心的校对类, 支持异步并发处理多个文本块, 
    大幅提升大文档的校对速度。
    """
    
    def __init__(self, max_workers: int = 10, dictionary: List[str] = None, conversion_rules: Dict[str, str] = None, 
                 professional_terms: List[str] = None, target_category: str = None):
        """
        初始化异步文档校对器
        
        Args:
            max_workers (int): 最大并发工作线程数
            dictionary (List[str]): 词库列表
            conversion_rules (Dict[str, str]): 词汇转换规则
            professional_terms (List[str]): 专业词汇字典["词汇1", "词汇2"] 
            target_category (str): 目标审校类别
        """
        self.max_workers = max_workers
        self.dictionary = dictionary or []
        self.conversion_rules = conversion_rules or {}
        self.professional_terms = professional_terms or []
        self.target_category = target_category
        self.model = self._init_model()
        self.error_type_general = ["语法/句法错误类", "错别字类", "常识错误类", "专业术语错误类"]
        self.error_type_professional = ["专业术语错误类", "语法/句法错误类", "错别字类", "常识错误类"]
        # 智能预处理专业词汇
        self.relevant_terms = self._get_category_terms()
        # 专业密度阈值（可调节）
        self.professional_density_threshold = 0.15  # 15%的密度阈值
        
        print(f"🚀 异步校对器已初始化, 最大并发数: {max_workers}")
        print(f"📚 专有名词保护: {len(self.dictionary)} 个")
        print(f"🔄 词汇转换规则: {len(self.conversion_rules)} 条")
        print(f"🎯 专业词汇审校: {self.target_category or '未启用'} ({len(self.relevant_terms)} 个术语)")
        print(f"📊 专业密度阈值: {self.professional_density_threshold * 100}%")

    def _get_category_terms(self) -> List[str]:
        """获取指定类别的专业词汇"""
        if not self.target_category or not self.professional_terms:
            return []
        return self.professional_terms

    def _select_relevant_terms(self, content: str, max_terms: int = 8) -> List[str]:
        """
        智能筛选与内容相关的专业词汇
        
        使用三层匹配策略：精确匹配 → 部分匹配 → 字符重叠
        """
        if not self.relevant_terms:
            return []
            
        # 第一层：精确匹配（直接包含的术语）
        exact_matches = [term for term in self.relevant_terms if term in content]
        # 第二层：部分匹配（术语的关键词出现在内容中）
        partial_matches = []
        for term in self.relevant_terms:
            if term not in exact_matches:
                # 检查术语的关键字符是否在内容中
                if len(term) >= 3 and any(char in content for char in term[:3]):
                    partial_matches.append(term)
        # 第三层：按匹配度排序并限制数量
        all_matches = exact_matches + partial_matches[:max_terms-len(exact_matches)]
        return all_matches

    def _init_model(self):
        """初始化语言模型"""
        return ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="deepseek-v3-250324",
            temperature=0.3,
        )
    
    async def proofread_document_async(
        self, 
        content: str, 
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> Tuple[str, str, Dict[str, Any]]:
        """
        异步校对文档内容（主要入口函数）
        
        这是最重要的函数, 新手主要需要理解这个流程：
        1. 分割文档 → 2. 异步处理各块 → 3. 合并结果 → 4. 错误类型裁定 → 5. 返回最终结果
        
        Args:
            content (str): 文档内容
            progress_callback (function, optional): 进度回调函数, 参数为(当前进度, 总进度)
        
        Returns:
            tuple: (校正后的文本, 带标记的文本, 包含详细信息的字典)
        """
        print(f"📄 开始异步文档校对...")
        
        # 步骤1: 分割文本块
        chunks = self._split_content(content)
        print(f"✂️  文档已分割为 {len(chunks)} 个文本块")
        
        # 步骤2: 异步处理所有文本块（核心步骤）
        chunk_results = await self._process_chunks_async(chunks, progress_callback)
        
        # 步骤3: 调整错误ID并合并结果
        corrected_text, labeled_text = self._merge_results_with_id_adjustment(chunk_results)
        
        print("裁定前文本:\n", labeled_text)
        # 步骤4: 错误类型裁定（新增）
        labeled_text = await self._refine_error_types_async(labeled_text, content)
        
        # 步骤5: 重新生成校正后的文本（因为可能有标记变化）
        corrected_text = _generate_corrected_text_from_labeled(labeled_text)
        
        # 步骤6: 提取错误信息
        error_list = extract_errors_from_labeled_text(labeled_text)
        
        # 步骤7: 构建结果字典
        result_dict = {
            "corrected_text": corrected_text,
            "labeled_text": labeled_text,
            "error_list": error_list
        }
        
        print(f"✅ 异步校对完成！共发现 {len(error_list)} 个错误")
        return corrected_text, labeled_text, result_dict
    
    async def _process_chunks_async(
        self, 
        chunks: List[str], 
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[ChunkProofreadResult]:
        """
        异步处理所有文本块（核心异步逻辑）
        
        这里是实现并发处理的关键部分：
        - 创建多个异步任务
        - 使用线程池处理LLM调用
        - 保持原始顺序返回结果
        
        Args:
            chunks (List[str]): 文本块列表
            progress_callback (function, optional): 进度回调函数
        
        Returns:
            List[ChunkProofreadResult]: 按顺序排列的处理结果
        """
        print(f"⚡ 开始异步处理 {len(chunks)} 个文本块...")
        
        # 使用线程池执行器来处理同步的LLM调用
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 创建异步任务列表, 每个任务处理一个chunk
            tasks = []
            for i, chunk in enumerate(chunks):
                task = asyncio.create_task(
                    self._process_single_chunk_async(executor, i, chunk)
                )
                tasks.append(task)
            
            # 收集完成的结果, 但需要保持原始顺序
            completed_results = {}
            completed_count = 0
            
            # 等待所有任务完成
            for task in asyncio.as_completed(tasks):
                try:
                    result = await task
                    completed_results[result.chunk_index] = result
                    completed_count += 1
                    
                    print(f"📝 文本块 {result.chunk_index + 1} 处理完成 ({completed_count}/{len(chunks)})")
                    
                    # 调用进度回调
                    if progress_callback:
                        progress_callback(completed_count, len(chunks))
                        
                except Exception as e:
                    print(f"❌ 处理文本块时发生错误: {e}")
                    raise
        
        # 按原始顺序重新排列结果
        ordered_results = []
        for i in range(len(chunks)):
            if i in completed_results:
                ordered_results.append(completed_results[i])
            else:
                print(f"⚠️  警告: 文本块 {i} 处理失败, 使用原文本")
                ordered_results.append(ChunkProofreadResult(i, chunks[i], chunks[i], 0))
        
        return ordered_results
    
    async def _process_single_chunk_async(
        self, 
        executor: ThreadPoolExecutor, 
        chunk_index: int, 
        chunk_content: str
    ) -> ChunkProofreadResult:
        """
        异步处理单个文本块
        
        将同步的LLM调用放在线程池中执行, 避免阻塞异步循环
        
        Args:
            executor (ThreadPoolExecutor): 线程池执行器
            chunk_index (int): 文本块索引
            chunk_content (str): 文本块内容
        
        Returns:
            ChunkProofreadResult: 单个块的处理结果
        """
        print(f"🔄 开始处理文本块 {chunk_index + 1}...")
        
        # 在线程池中执行同步的校对操作
        loop = asyncio.get_event_loop()
        
        try:
            # 使用初始ID 1, 稍后会进行调整
            labeled_text, corrected_text, error_count = await loop.run_in_executor(
                executor, 
                self._proofread_document_chunk_sync, 
                chunk_content, 
                1  # 初始current_count为1, 稍后调整
            )
            
            return ChunkProofreadResult(
                chunk_index=chunk_index,
                labeled_text=labeled_text,
                corrected_text=corrected_text,
                error_count=error_count
            )
            
        except Exception as e:
            print(f"❌ 处理文本块 {chunk_index + 1} 时发生错误: {e}")
            # 返回原始内容作为后备方案
            return ChunkProofreadResult(
                chunk_index=chunk_index,
                labeled_text=chunk_content,
                corrected_text=chunk_content,
                error_count=0
            )
    
    def _merge_results_with_id_adjustment(
        self, 
        chunk_results: List[ChunkProofreadResult]
    ) -> Tuple[str, str]:
        """
        合并处理结果并调整错误ID（解决并发ID冲突问题）
        
        这是解决您提出的current_count问题的核心函数：
        - 按顺序处理每个块的结果
        - 累加错误ID, 确保不重复
        - 合并所有文本块
        
        Args:
            chunk_results (List[ChunkProofreadResult]): 按顺序排列的块处理结果
        
        Returns:
            Tuple[str, str]: (校正后的文本, 带标记的文本)
        """
        print("🔗 开始合并结果并调整错误ID...")
        
        corrected_parts = []
        labeled_parts = []
        cumulative_error_count = 0
        
        for i, result in enumerate(chunk_results):
            # 调整当前块的错误ID（关键步骤）
            adjusted_labeled_text = self._adjust_error_ids(
                result.labeled_text, 
                cumulative_error_count
            )
            
            # 添加到结果列表
            corrected_parts.append(result.corrected_text)
            labeled_parts.append(adjusted_labeled_text)
            
            # 更新累计错误数量
            actual_error_count = self._count_errors_in_text(adjusted_labeled_text)
            cumulative_error_count += actual_error_count
        # 合并所有部分
        corrected_text = self._merge_text_parts(corrected_parts)
        labeled_text = self._merge_text_parts(labeled_parts)
        return corrected_text, labeled_text
    
    def _adjust_error_ids(self, labeled_text: str, id_offset: int) -> str:
        """
        调整文本中的错误ID（解决并发ID重复问题）
        
        Args:
            labeled_text (str): 带标记的文本
            id_offset (int): ID偏移量
        
        Returns:
            str: 调整后的带标记文本
        """
        if id_offset == 0:
            return labeled_text
        
        # 正则表达式匹配错误标记中的ID
        pattern = r'👉id:\s*(\d+),'
        
        def replace_id(match):
            original_id = int(match.group(1))
            new_id = original_id + id_offset
            return f'👉id: {new_id},'
        
        adjusted_text = re.sub(pattern, replace_id, labeled_text)
        return adjusted_text
    
    def _count_errors_in_text(self, labeled_text: str) -> int:
        """
        统计文本中的错误数量
        
        Args:
            labeled_text (str): 带标记的文本
        
        Returns:
            int: 错误数量
        """
        pattern = r'~~.*?~~👉id:\s*\d+,.*?👈'
        matches = re.findall(pattern, labeled_text, re.DOTALL)
        return len(matches)
    
    def _merge_text_parts(self, text_parts: List[str]) -> str:
        if not text_parts:
            return ""
        return '\n'.join(part for part in text_parts if part.strip())
    
    def _clean_protected_words(self, labeled_text: str) -> str:
        """
        清理被误标记的专有名词并重新编号ID
        """
        if not self.dictionary:
            return labeled_text
            
        cleaned_text = labeled_text
        
        # 遍历专有名词词库，清理误标记
        for word in self.dictionary:
            if not word.strip():
                continue
                
            # 查找包含该专有名词的错误标记模式
            pattern = rf'~~([^~]*?{re.escape(word)}[^~]*?)~~👉id:[^👈]*?👈'
            matches = re.findall(pattern, cleaned_text, re.DOTALL)
            
            for match in matches:
                # 如果错误标记中的文本包含专有名词，则移除标记
                if word in match:
                    full_match = re.search(rf'~~{re.escape(match)}~~👉id:[^👈]*?👈', cleaned_text, re.DOTALL)
                    if full_match:
                        cleaned_text = cleaned_text.replace(full_match.group(), match)
                        print(f"🧹 清理专有名词误标记: '{word}' 在 '{match}' 中")
        
        # 重新编号所有剩余的错误ID，确保连续
        return self._reorder_error_ids(cleaned_text)
    
    def _reorder_error_ids(self, labeled_text: str) -> str:
        """
        重新编号错误ID，确保连续（只需8行代码）
        """
        # 找到所有错误标记
        pattern = r'(~~[^~]+?~~👉id:\s*)(\d+)(,.*?👈)'
        matches = list(re.finditer(pattern, labeled_text))
        
        # 重新编号
        result = labeled_text
        for i, match in enumerate(matches, 1):
            old_mark = match.group()
            new_mark = f"{match.group(1)}{i}{match.group(3)}"
            result = result.replace(old_mark, new_mark, 1)  # 只替换第一个匹配
        
        return result

    def _split_content(self, content: str) -> List[str]:
        """
        将内容分割成固定大小的块
        
        Args:
            content (str): 原始内容
        
        Returns:
            List[str]: 文本块列表
        """
        try:
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=200,  # 每块200字符, 可根据需要调整
                chunk_overlap=0,  # 不重叠, 避免重复处理
                length_function=len,
            )
            chunks = text_splitter.split_text(content)
            return chunks
        except Exception as e:
            raise Exception(f"分割文本时出错: {str(e)}")
    
    def _proofread_document_chunk_sync(
        self, 
        chunk_content: str, 
        current_count: int = 1
    ) -> Tuple[str, str, int]:
        """
        同步校对单个文档块
        
        根据专业术语密度自动选择校对模式：
        - 高密度 → 专业模型
        - 低密度 → 通用模型
        
        Args:
            chunk_content (str): 文本块内容
            current_count (int): 当前错误计数起始值
        Returns:
            Tuple[str, str, int]: (带标记的文本, 校正后的文本, 错误数量)
        """
        try:
            # 🎯 根据专业密度选择校对模式
            proofread_mode = self._select_proofread_mode(chunk_content)
            
            # 构建词汇转换规则字符串
            conversion_rules_str = ""
            if self.conversion_rules:
                conversion_rules_list = [f"'{source}' → '{target}'" for source, target in self.conversion_rules.items()]
                conversion_rules_str = f"""
                词汇转换要求：请将以下词汇进行统一转换, 确保用词一致性：
                {', '.join(conversion_rules_list)}
                转换规则说明：
                - 字典中需要转化的词汇直接放到错误标记中, 不要进行强制转换。
                - 如果是词汇转换, conversion_rules_str, 那么直接判断为错误, 然后标记出来。例如, conversion_rules = ("天真": "可爱"), content是"天真无邪的小孩", 那么content直接给出建议即可, 变为"~~天真~~👉id: 3, 修改建议: 可爱, 错误理由: 根据词汇转换要求👈无邪的小孩", 而不是直接进行强制转换。
                """

            # 根据模式选择相应的校对逻辑
            if proofread_mode == 'professional':
                return self._proofread_with_professional_mode(chunk_content, conversion_rules_str)
            else:
                return self._proofread_with_general_mode(chunk_content, conversion_rules_str)
        
        except Exception as e:
            print(f"❌ 校对文档块时出错: {str(e)}")
            return chunk_content, chunk_content, 0

    def _proofread_with_professional_mode(self, chunk_content: str, conversion_rules_str: str) -> Tuple[str, str, int]:
        """专业模型校对模式"""
        print("🎯 使用专业模型校对...")
        
        proofread_template_professional = f"""
            你是一位专业的文档校对专家, 擅长发现和纠正中文文档中的错误, 并且会对输入文本进行细致的校对。
            请对以下文本进行仔细校对, 找出其中的专业术语错误,并直接在原文中标记出来。专业术语有错误才会标记出来，没有错误不要标记出来。
            顺便找出其中的语法错误、标点错误、错别字、重复内容、逻辑不通顺的地方等问题,并直接在原文中标记出来。这些可以和专业术语错误一起标记出来。并且可以归类于基础错误类。
            
            根据{self.target_category}领域的专业特点和术语习惯进行校对。
            专业词汇审校要求：请特别注意以下{self.target_category}专业术语的准确性：
            {conversion_rules_str}

            并且请按照以下要求进行处理：
            要求：
            1. 【重点】重点检查专业术语错误，不要遗漏。顺便检查语法错误、标点错误、错别字、重复内容、逻辑不通顺的地方等问题。
            2. 对于发现的错误, 严格使用以下格式直接在原文中标记：
            - 对错误的文本,使用'~~错误文本~~'来标记错误
            - 格式为：~~错误文本~~👉id: 1, 修改建议: 正确文本, 错误理由: ..., 错误类型: {self.error_type_professional}👈
            - 每发现一个错误, id序号加1
            - 例如："数据结构是一门研究非数值计算的~~程式设计~~👉id: 1, 修改建议: 程序设计, 错误理由: ..., 错误类型: {self.error_type_professional}👈问题中的操作对象"
            - 专业错误类：不符合{self.target_category}领域的专业特点和术语习惯
            - 错误类型：语法/句法错误类、错别字类、常识错误类
            - 语法句法错误类：用词不当/语法错误、搭配不当错误、常见句法错误
            - 常识错误类：常识错误、常见地名错误、句子重复错误、句子不通顺、重复内容、逻辑不通顺
            - 错别字类：音/形相似错误、标点符号错误、的地得错误、错别字
            2. 错误类型中的错误，可以存在多个，专业错误类和其他错误类可以同时存在。如果这个错误属于多个错误类，可以输出多个错误类型。
            - 例如，"数据结构是一门研究非数值计算的~~程式设计~~👉id: 1, 修改建议: 程序设计, 错误理由: ..., 错误类型: 专业术语错误类, 错别字类👈问题中的操作对象"。
            3. 输出格式要求：
            corrected_label_text:
                包含标记的完整原文

            限制：
            1. 【重点】重点检查专业术语错误，不要遗漏。顺便检查语法错误、标点错误、错别字、重复内容、逻辑不通顺的地方等问题。
            2. 错误类型格式不需要使用['专业术语错误类'],直接输出,专业术语错误类,即可。
            3. 请严格遵守{{conversion_rules_str}}所有要求。
            4. 如果修改建议是是'删除', 那么修改建议直接采用置为空。例如, ~~错误文本~~👉id: 1, 修改建议: , 错误理由: ..., 错误类型: ...👈
            5. 不需要添加任何说明！直接返回结果即可！
            6. 标记为错误时，将原文本使用'~~原文本~~'来标记错误。而不是在后面一个'~~错误文本~~'。
            -例如，”zongshang所述，~~zongshang~~👉id: 13, 修改建议: 综上, 错误理由: 错别字, 错误类型: 错别字类👈法律体系的完善“是错误的，直接”~~zongshang~~👉id: 13, 修改建议: 综上, 错误理由: 错别字, 错误类型: 错别字类👈所述，法律体系的完善“
            7. 错误类型只能在{self.error_type_professional}中选择，可以选择多个，但是不要输出其他错误类型。
            8. 如果这个错误属于多个错误类，可以输出多个错误类型。

            文本内容:{{text_chunk}} 
            """

        prompt = PromptTemplate(template=proofread_template_professional, input_variables=["text_chunk", "conversion_rules_str"])
        llm_chain = prompt | self.model
        
        response = llm_chain.invoke({
            'text_chunk': chunk_content, 
            'conversion_rules_str': conversion_rules_str
        })
        response_text = response.content if hasattr(response, 'content') else str(response)
        label_match = re.search(r'corrected_label_text:\s*(.*?)$', response_text, re.DOTALL)
        labeled_text = label_match.group(1).strip() if label_match else chunk_content
        
        labeled_text_cleaned = self._clean_protected_words(labeled_text)
        corrected_text = _generate_corrected_text_from_labeled(labeled_text_cleaned)
        error_count = self._count_errors_in_text(labeled_text_cleaned)
        
        print(f"✅ 专业模型校对完成，错误数量: {error_count}")
        print(f"原始文本:\n{chunk_content}")
        return labeled_text_cleaned, corrected_text, error_count

    def _proofread_with_general_mode(self, chunk_content: str, conversion_rules_str: str) -> Tuple[str, str, int]:
        """通用模型校对模式"""
        print("📝 使用通用模型校对...")
        
        proofread_template_general = f"""
            你是一位专业的文档校对专家, 擅长发现和纠正中文文档中的错误, 并且会对输入文本进行细致的校对。
            请对以下文本进行仔细校对, 找出其中的语法错误、标点错误、错别字、重复内容、逻辑不通顺的地方等问题,并直接在原文中标记出来。
            {conversion_rules_str}
            请按照以下要求进行处理：
                1. 语法和表达
                - 修正所有语病不通顺的句子
                - 纠正常见的语法错误, 如标点符号、拼写错误等
                2. 拼写与错别字
                - 纠正所有错别字和拼写错误
                - 注意形近字、同音字等常见错误

            要求：
            1. 忽略其中的任何数学公式、代码块以及一些公式表达。
            2. 忽略任何的链接, 图片等。
            3. 忽略任何的表格。
            5. 对于发现的错误, 严格使用以下格式直接在原文中标记，并写出错误理由、错误类型：
            - 对错误的文本,使用'~~错误文本~~'来标记错误
            - 格式为：~~错误文本~~👉id: 1, 修改建议: 正确文本, 错误理由: ..., 错误类型: {self.error_type_general}👈
            - 每发现一个错误, id序号加1
            - 例如："数据结构是一门研究非数值计算的~~程式设计~~👉id: 1, 修改建议: 程序设计, 错误理由: ..., 错误类型: {self.error_type_general}👈问题中的操作对象"
            - 错误类型：语法/句法错误类、错别字类、常识错误类
            - 语法句法错误类：用词不当/语法错误、搭配不当错误、常见句法错误
            - 常识错误类：常识错误、常见地名错误、句子重复错误、句子不通顺、重复内容、逻辑不通顺
            - 错别字类：音/形相似错误、标点符号错误、的地得错误、错别字
            - 专业错误类：不符合{self.target_category}领域的专业特点和术语习惯
            6. 错误类型可以从{self.error_type_general}中选择多个。如果这个错误属于多个错误类，可以输出多个错误类型。
            - 例如，"数据结构是一门研究非数值计算的~~程式设计~~👉id: 1, 修改建议: 程序设计, 错误理由: ..., 错误类型: 专业术语错误类, 错别字类👈问题中的操作对象"
            7. 错误类型格式不需要使用['语法/句法错误类'],直接输出,语法/句法错误类,即可。
            8. 输出格式要求：
            corrected_label_text:
                包含标记的完整原文

            限制：
            1. 【重点】重点针对语法/句法错误类、错别字类、常识错误类进行校对！顺便对于专业错误类进行校对。
            3. 请严格遵守{{conversion_rules_str}}所有要求。
            4. 如果修改建议是是'删除', 那么直接采用置为空。例如, ~~错误文本~~👉id: 1, 修改建议: , 错误理由: ..., 错误类型: ...👈
            5. 不要修改任何关于html的代码。例如："<td style="text-align:center">","</td>","<table border="1" style="border-collapse: collapse;">", 不要修改！不要修改！
            6. 不需要添加任何说明！直接返回结果即可！
            7. 标记为错误时，将原文本使用'~~原文本~~'来标记错误。而不是在后面一个'~~错误文本~~'。
            -例如，”zongshang所述，~~zongshang~~👉id: 13, 修改建议: 综上, 错误理由: 错别字, 错误类型: 错别字类👈法律体系的完善“是错误的，直接”~~zongshang~~👉id: 13, 修改建议: 综上, 错误理由: 错别字, 错误类型: 错别字类👈所述，法律体系的完善“
            8. 错误类型只能在{self.error_type_general}中选择，可以选择多个，但是不要输出其他错误类型。
            9. 如果这个错误属于多个错误类，可以输出多个错误类型。
            
            文本内容:{{text_chunk}} 
            """

        prompt = PromptTemplate(template=proofread_template_general, input_variables=["text_chunk", "conversion_rules_str"])
        llm_chain = prompt | self.model
        
        response = llm_chain.invoke({
            'text_chunk': chunk_content, 
            'conversion_rules_str': conversion_rules_str
        })
        response_text = response.content if hasattr(response, 'content') else str(response)
        label_match = re.search(r'corrected_label_text:\s*(.*?)$', response_text, re.DOTALL)
        labeled_text = label_match.group(1).strip() if label_match else chunk_content
        
        labeled_text_cleaned = self._clean_protected_words(labeled_text)
        corrected_text = _generate_corrected_text_from_labeled(labeled_text_cleaned)
        error_count = self._count_errors_in_text(labeled_text_cleaned)
        
        print(f"✅ 通用模型校对完成，错误数量: {error_count}")
        print(f"原始文本:\n{chunk_content}")
        return labeled_text_cleaned, corrected_text, error_count

    def _calculate_professional_density(self, chunk_content: str) -> float:
        """
        使用LLM计算文本块的专业术语密度
        
        Args:
            chunk_content (str): 文本块内容
        
        Returns:
            float: 专业术语密度 (0-1)
        """
        if not self.target_category or not chunk_content.strip():
            return 0.0
        
        density_prompt = f"""
        你是一位{self.target_category}领域的专业分析师。请分析以下文本中专业术语的密度。

        分析要求：
        1. 识别文本中所有与{self.target_category}相关的专业术语
        2. 计算专业术语在文本中的密度比例
        3. 专业术语包括但不限于：专业概念、专业表述、学术用语、行业术语、专业术语等

        计算方法：
        密度 = (专业术语字符数 / 文本总字符数) × 100%

        请分析以下文本：
        "{chunk_content}"

        请只返回一个0-100之间的数字，表示专业术语密度的百分比。
        例如：如果密度是15%，只返回"15"。
        """

        response = self.model.invoke(density_prompt)
        result_text = response.content if hasattr(response, 'content') else str(response)
        
        # 提取数字
        import re
        numbers = re.findall(r'\d+\.?\d*', result_text.strip())
        if numbers:
            density_percentage = float(numbers[0])
            density = min(density_percentage / 100.0, 1.0)  # 转换为0-1之间的值
            print(f"📊 文本专业密度: {density_percentage:.1f}% ({'专业模型' if density > self.professional_density_threshold else '通用模型'})")
            print("chunk_content:\n",chunk_content)
            return density
        else:
            print("⚠️ 无法解析密度结果，使用默认值0.0")
            return 0.0


    def _select_proofread_mode(self, chunk_content: str) -> str:
        """
        根据专业密度选择校对模式
        
        Args:
            chunk_content (str): 文本块内容
        
        Returns:
            str: 'professional' 或 'general'
        """
        density = self._calculate_professional_density(chunk_content)
        
        if density > self.professional_density_threshold:
            return 'professional'
        else:
            return 'general'

    async def _refine_error_types_async(self, labeled_text: str, original_content: str) -> str:
        """
        异步裁定多重错误类型，确定每个错误的准确分类
        
        Args:
            labeled_text (str): 带标记的文本
            original_content (str): 原始文档内容
        
        Returns:
            str: 裁定后的带标记文本
        """
        print("🎯 开始错误类型裁定...")
        
        # 提取多重错误类型的标记
        multi_type_errors = self._extract_multi_type_errors(labeled_text)
        
        if not multi_type_errors:
            print("✅ 无需裁定，所有错误类型已明确")
            return labeled_text
        
        print(f"📋 发现 {len(multi_type_errors)} 个需要裁定的多重错误类型")
        
        # 异步处理每个需要裁定的错误
        with ThreadPoolExecutor(max_workers=5) as executor:
            tasks = []
            for error_info in multi_type_errors:
                task = asyncio.create_task(
                    self._judge_single_error_type_async(executor, error_info, original_content)
                )
                tasks.append(task)
            
            # 等待所有裁定完成
            refinement_results = await asyncio.gather(*tasks)
        
        # 应用裁定结果到文本中
        refined_text = labeled_text
        for original_mark, refined_mark in refinement_results:
            refined_text = refined_text.replace(original_mark, refined_mark, 1)
        
        print(f"✅ 错误类型裁定完成！共裁定 {len(refinement_results)} 个错误")
        return refined_text

    def _extract_multi_type_errors(self, labeled_text: str) -> List[Dict[str, str]]:
        """
        提取包含多个错误类型的标记，并获取上下文
        
        Args:
            labeled_text (str): 带标记的文本
        
        Returns:
            List[Dict]: 包含错误信息和上下文的列表
        """
        multi_type_errors = []
        
        # 匹配错误标记模式
        pattern = r'~~([^~]+?)~~👉id:\s*(\d+)\s*,\s*修改建议:\s*([^,]+?),\s*错误理由:\s*([^,👈]+?),\s*错误类型:\s*([^👈]+?)👈'
        
        for match in re.finditer(pattern, labeled_text, re.DOTALL):
            error_text = match.group(1).strip()
            error_id = match.group(2).strip()
            error_suggestion = match.group(3).strip()
            error_reason = match.group(4).strip()
            error_types = match.group(5).strip()
            
            # 检查是否包含多个错误类型（含有逗号分隔）
            if ',' in error_types:
                # 获取标记在文本中的位置
                start_pos = match.start()
                end_pos = match.end()
                
                # 提取前后10个字符作为上下文（最多不超过文本边界）
                context_start = max(0, start_pos - 10)
                context_end = min(len(labeled_text), end_pos + 10)
                context = labeled_text[context_start:context_end]
                
                multi_type_errors.append({
                    'error_text': error_text,
                    'error_id': error_id,
                    'error_suggestion': error_suggestion,
                    'error_reason': error_reason,
                    'error_types': error_types,
                    'full_mark': match.group(0),
                    'context': context
                })
        
        return multi_type_errors

    async def _judge_single_error_type_async(self, executor: ThreadPoolExecutor, error_info: Dict[str, str], original_content: str) -> Tuple[str, str]:
        """
        异步裁定单个错误的准确类型
        
        Args:
            executor (ThreadPoolExecutor): 线程池执行器
            error_info (Dict): 错误信息字典
            original_content (str): 原始文档内容
        
        Returns:
            Tuple[str, str]: (原标记, 裁定后标记)
        """
        loop = asyncio.get_event_loop()
        
        try:
            refined_mark = await loop.run_in_executor(
                executor,
                self._judge_error_type_sync,
                error_info,
                original_content
            )
            return error_info['full_mark'], refined_mark
        except Exception as e:
            print(f"⚠️  裁定错误类型失败，使用原标记: {e}")
            return error_info['full_mark'], error_info['full_mark']

    def _judge_error_type_sync(self, error_info: Dict[str, str], original_content: str) -> str:
        """
        同步裁定错误类型
        
        Args:
            error_info (Dict): 错误信息
            original_content (str): 原始内容
        
        Returns:
            str: 裁定后的错误标记
        """
        judgment_prompt = f"""
        你是一位专业的语言文字审校专家，擅长精确判断文字错误的类型。

        现在需要你对一个包含多种错误类型的标记进行准确分类，请从给出的候选类型中选择最主要、最准确的一个。

        错误信息：
        - 错误文本："{error_info['error_text']}"
        - 修改建议："{error_info['error_suggestion']}"
        - 错误理由："{error_info['error_reason']}"
        - 候选错误类型：{error_info['error_types']}
        - 上下文："{error_info['context']}"

        错误类型定义：
        1. 专业术语错误类：专业领域术语使用不当、专业表述不规范
        2. 语法/句法错误类：语法错误、句式错误、搭配不当
        3. 错别字类：音/形相似错误、标点符号错误、的地得错误、拼写错误
        4. 常识错误类：常识性错误、逻辑错误、事实性错误

        分析要求：
        1. 仔细分析错误文本的具体问题
        2. 结合上下文判断错误的主要性质
        3. 从候选类型中选择最符合的一个
        4. 只返回一个最准确的错误类型

        请直接返回最准确的错误类型，不要添加任何说明文字。
        例如：专业术语错误类
        """

        try:
            response = self.model.invoke(judgment_prompt)
            result_text = response.content if hasattr(response, 'content') else str(response)
            
            # 提取模型返回的错误类型
            refined_type = result_text.strip()
            
            # 验证返回的类型是否在候选类型中
            candidate_types = [t.strip() for t in error_info['error_types'].split(',')]
            if refined_type not in candidate_types:
                # 如果返回的类型不在候选中，选择第一个候选类型
                refined_type = candidate_types[0]
            
            # 构建新的错误标记
            refined_mark = f"~~{error_info['error_text']}~~👉id: {error_info['error_id']}, 修改建议: {error_info['error_suggestion']}, 错误理由: {error_info['error_reason']}, 错误类型: {refined_type}👈"
            
            print(f"🎯 裁定完成: '{error_info['error_text']}' → {refined_type}")
            return refined_mark
            
        except Exception as e:
            print(f"❌ 裁定过程出错: {e}")
            return error_info['full_mark']


# ============================================================================
# 公共接口函数
# ============================================================================

def proofread_document_file(
        content: str, 
        progress_callback=None, 
        dictionary: List[str] = None,
        conversion_rules: Dict[str, str] = None,
        professional_terms: List[str] = None,
        target_category: str = None
        ) -> Tuple[str, str, Dict[str, Any]]:
    """
    校对文档文件内容（同步函数）
    使用异步处理来提高性能
    
    Args:
        content (str): 文档内容
        progress_callback (function, optional): 进度回调函数, 参数为(当前进度, 总进度)
        dictionary (List[str], optional): 词库列表
        conversion_rules (Dict[str, str], optional): 词汇转换规则字典
        professional_terms (List[str], optional): 专业词汇列表
        target_category (str, optional): 目标审校类别
    
    Returns:
        tuple: (校正后的文本, 带标记的文本, 包含详细信息的字典)
    """
    print(f"📄 开始文档校对...")
    
    async def _async_proofread():
        """内部异步处理函数"""
        proofreader = AsyncDocumentProofreader(
            max_workers=10, 
            dictionary=dictionary, 
            conversion_rules=conversion_rules,
            professional_terms=professional_terms,
            target_category=target_category
            )
        
        return await proofreader.proofread_document_async(content, progress_callback)
    
    try:
        # 获取当前事件循环, 如果不存在则创建新的
        loop = asyncio.get_event_loop()
        if loop.is_running():
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, _async_proofread())
                corrected_text, labeled_text, result_dict = future.result()
        else:
            corrected_text, labeled_text, result_dict = loop.run_until_complete(_async_proofread())
    except RuntimeError:
        # 事件循环问题, 直接运行
        corrected_text, labeled_text, result_dict = asyncio.run(_async_proofread())
    
    print(f"✅ 文档校对完成！")
    return corrected_text, labeled_text, result_dict


def extract_errors_from_labeled_text(labeled_text: str) -> List[Dict[str, str]]:
    """
    从带标记的文本中提取错误信息，支持新版格式：
    ~~错误文本~~👉id: {{current_count}}, 修改建议: 正确文本, 错误理由: ..., 错误类型: ...👈
    """
    error_list = []
    # 匹配完整的错误标记模式: ~~...~~👉...👈
    full_pattern = r'~~([^~]+?)~~👉id:\s*(\d+)\s*,\s*修改建议:\s*([^,]+?),\s*错误理由:\s*([^,👈]+?),\s*错误类型:\s*([^👈]+?)👈'
    error_marks = re.findall(full_pattern, labeled_text, re.DOTALL)
    for match in error_marks:
        error_text, error_id, error_suggestion, error_reason, error_type = [m.strip() for m in match]
        error_list.append({
            "id": error_id,
            "error_text": error_text,
            "error_suggestion": error_suggestion,
            "error_reason": error_reason,
            "error_type": error_type
        })
    return error_list


def _generate_corrected_text_from_labeled(labeled_text: str) -> str:
    """根据新版带标记的文本生成校正后的文本"""
    def replace_suggestion(match):
        suggestion = match.group(1).strip()
        return suggestion
    # 匹配新版错误标记格式
    pattern = r"~~.*?~~👉id:\s*\d+,\s*修改建议:\s*(.*?),\s*错误理由:.*?,\s*错误类型:.*?👈"
    corrected_text = re.sub(pattern, replace_suggestion, labeled_text, flags=re.DOTALL)
    return corrected_text


# ============================================================================
# 示例和测试代码
# ============================================================================

if __name__ == "__main__":
    """
    示例用法和测试代码
    
    新手可以通过这个示例了解如何使用校对器
    """

    # 🧪 测试样本：包含不同专业密度的文本
    high_density_sample = '''
    根据《宪法》第35条规定，选举权和被选举权是公民的基本政治权利。在法治国家构建中，宪法限制与法律限制共同构成法律体系框架。明确性原则与比例原则作为权利行使的双重约束，确保选举公正有序进行。民族平等、性别平等原则要求立法机关避免特权和差别对待，防止民族歧视、宗教歧视现象。
    '''
    
    low_density_sample = '''
    我喜欢次饭。每天吃法真开心。小明跑得很快，但是他今天迟到了。老师说学生需要好好学习，不然就是违返规定。打羽毛球是一个健康的运洞，大家都喜欢参加体育活动。
    '''
    
    mixed_sample = '''
    在现代法治国家的构建中，宪法作为根本大法，其限制作用体现在多个层面。宪法限制与法律限制共同构成法律体系的框架，例如《宪法》第35条明确规定了公民的选举权和被选举权，但这一权利的行使需遵循明确形原则与比利原则的双重约束。我想次饭。
关于公民基本权利的保护，出版自由与宗教信仰自由是核心内容。然而，当言论涉及淫秽言论或煽动仇恨时，国家可依据预防制或追惩罚制进行干预。值得注意的是，民族平等与性别平等原则要求立法机关避免特权和差别对待的出现。例如，某些地方政策若对特定民族实施户口宪制或学位宪制，则可能被认定为民族歧视或宗教歧视，违背平等保护的宪法精神。以及次饭精神。
在权利限制的正当性分析中，合力差别理论具有重要意义。确实确实！还是敲代码比较重要一电。
在司法实践中，证明责任的分配直接影响案件结果。同时，保障义务要求国家通过立法和行政手段消除权利行使的障碍。例如，针对残障人士的家庭生活权力保障，需通过经济权力与社会权力的综合施策实现。
zongshang所述，法律体系的完善不仅依赖于对民族政策、宗教政策的精准设计，还需通过公共利益的衡量平衡个体权利与社会秩序。未来立法应进一步细化差别对待的界限，强化法律审查机制，以实现宪法对“人权保障”的庄严陈诺。
我喜欢次饭。每天吃法真开心。需要特别注意的是，在法律条文中有明确规定，学生需要好好学习，不然就是违返法律。打羽毛球是一个健康的运洞，并且被打了之后，肇事者不会被判形。
    '''

#     sample_content = '''
#     随着计算机硬件性能的不断提升和大数据技术的广泛应用，人工智能（AI）已成为现代计算机科学的重要研究方向。与此同时，数据结构作为计算机科学的基础，也在不断演进以适应新的需求。人工智能的核心目标是使计算机系统能够执行通常需要人类智能才能完成的复杂任务，例如自然语言处理（NLP）、图像识别、语音识别以及决策制定。
# 深度学习作为人工智能的一个子领域，依赖于神经网络模型，尤其是卷和神经网络（CNN）和循环神经网络（RNN），这些模型被广泛应用于图像分类和时间序列预测中。为了提高训练效率，通常使用图形处理器（GPU）来加速计算过程。在这个过程中，数据通过各种算法进行处理，其中包括了对数据的操作如进栈（push）。进站是指将元素添加到栈顶的过程，栈是一种后进先出（LIFO, Last In First Out）的数据结构，在程序设计中用于存储临时信息或回溯操作等场景。
# 在软件层面，开发人员使用高级编程语言如Python，并借助机器学习框架如TensorFlow和PyTorch来构建和训练模型。同时，云计算平台为AI模型提供了强大的计算资源和存储能力，使得分布式训练成为可能。
# 此外，人工智能也推动了边缘计算的发展，即将数据处理从中心服务器转移到靠近数据源的设备端，以减少延迟并提高响应速度。这种架构常用于自动驾驶汽车和智能互联网（IoT）设备中。
# 尽管人工智能带来了许多技术突破，但同时也引发了关于算法偏见、隐私保护和伦理问题的讨论。因此，在设计AI系统时，必须考虑可解释性和公平性原则。
# 总之，人工智能正以前所未有的速度改变着我们的生活和工作方式，未来它将在医疗、金融、教育等多个领域发挥更大的作用。而数据结构如栈，则为这些先进的算法和技术提供了必要的支持，确保了数据的有效管理和高效处理。
#     '''
    dictionary = [
        # "系痛", "哈哈哈你好的是吗", "无向图", "操作", "ABC", "天真", "明显的", "错误", "阿斯顿", "进站"
        ]
    conversion_rules = {
        # "树形结构": "树形解构", 
        # "天真": "可爱",
        # "数组": "宿主",
        # "搜索树": "搜索🌲",
        # "系痛": "系统",
        # "无向图": "无向兔",
        # "CPG": "CPU"
        }
    
    # 专业词汇数据库
    professional_terms = [
            # "宪法限制", "法律限制", "明确性原则", "比例原则", "选举权和被选举权",
            # "出版自由", "集会、游行、示威自由", "宗教信仰自由", "民族平等", "性别平等",
            # "特权", "差别对待", "淫秽言论", "煽动仇恨", "预防制", "追惩制",
            # "年龄差异", "生理差异", "民族差异", "宗教社团", "国际人权公约",
            # "法律保留", "年龄限制", "学位限制", "户口限制", "保障义务", "理解偏差",
            # "证明责任", "年龄差异的合理差别", "生理差异的合理差别", "民族差异的合理差别",
            # "消极影响", "备案制", "许可制", "民族歧视", "性别歧视", "宗教歧视",
            # "文化权利", "经济权利", "社会权利", "家庭生活权利", "公共利益",
            # "宪法原则", "民主社会", "宪法审查", "民族政策", "宗教政策", "平等保护", "合理差别"
        ]
    target_category = "法律"
    
    def test_intelligent_routing():
        """测试智能路由功能"""
        print("🧪 开始测试智能路由文档校对器...")
        
        test_cases = [
            # ("高专业密度文本", high_density_sample),
            # ("低专业密度文本", low_density_sample), 
            ("混合密度文本", mixed_sample)
        ]
        
        for test_name, test_content in test_cases:
            print(f"\n{'=='*50}")
            print(f"🔍 测试案例: {test_name}")
            print(f"{'=='*50}")
            
            try:
                corrected, labeled, result = proofread_document_file(
                    content=test_content,
                    dictionary=dictionary,
                    conversion_rules=conversion_rules,
                    professional_terms=professional_terms,
                    target_category=target_category
                )
                
                # print(f"📄 校正后的文本:\n{corrected}")
                print(f"\n🏷️ 裁定后的带标记的文本:\n{labeled}")
                
                # 统计错误类型
                if "error_list" in result and result["error_list"]:
                    professional_errors = [e for e in result["error_list"] if "专业术语" in e["error_type"]]
                    general_errors = [e for e in result["error_list"] if "专业术语" not in e["error_type"]]
                    
                    print(f"\n📊 错误统计:")
                    print(f"  🎯 专业术语错误: {len(professional_errors)} 个")
                    print(f"  📝 通用错误: {len(general_errors)} 个")
                    print(f"  📈 总错误数: {len(result['error_list'])} 个")
                else:
                    print("\n✅ 未发现任何错误, 文档质量很好！")
                    
            except Exception as e:
                print(f"❌ 测试 {test_name} 时出错: {str(e)}")
    
    # 执行智能路由测试
    test_intelligent_routing()
    


