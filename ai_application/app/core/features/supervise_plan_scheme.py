import json
import logging
from typing import Dict, Any

from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.models import PromptTemplate

logger = logging.getLogger(__name__)


class SupervisePlanSchemeStageFeature(BaseFeature):
    """第一阶段：处理目标信息和数据总结"""

    def __init__(self):
        self.app_no = 'supervise_plan_scheme_stage'

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            target_info: list,
            data_summary_comparison: str,
            start_date: str,
            exam_date: str,
            entrance_foundation_positioning: str = "",
            stream: bool = False
    ) -> LLMResult:
        prompt_template = self._get_prompt_template(self.app_no)

        inputs = {
            "目标院校信息": target_info,
            "数据总结与对比": data_summary_comparison,
            "入学基础定位": entrance_foundation_positioning,
            "开始时间": start_date,
            "考试时间": exam_date
        }

        query = json.dumps(inputs, ensure_ascii=False)

        pre_prompt = prompt_template.assemble_prompt(
            query=query,
            inputs=inputs
        )

        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=query,
            stream=stream
        )

    def _get_prompt_template(self, app_no: str) -> PromptTemplate:
        prompt_template = PromptTemplate.objects.filter(app_no=app_no, is_enabled=True, is_deleted=False).first()
        if not prompt_template:
            raise Exception(f'Prompt template not found for app_no: {app_no}')
        return prompt_template


class SupervisePlanSchemeMapperFeature(BaseFeature):
    """第二阶段：映射第一阶段输出和课包信息"""

    def __init__(self):
        self.app_no = 'supervise_plan_scheme_mapper'

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            stage_one_output: str,
            course_packages: list,
            stream: bool = False
    ) -> LLMResult:
        prompt_template = self._get_prompt_template(self.app_no)

        inputs = {
            "第一阶段输出": stage_one_output,
            "课包信息": course_packages
        }

        query = json.dumps(inputs, ensure_ascii=False)

        pre_prompt = prompt_template.assemble_prompt(
            query=query,
            inputs=inputs
        )

        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=query,
            stream=stream
        )

    def _get_prompt_template(self, app_no: str) -> PromptTemplate:
        prompt_template = PromptTemplate.objects.filter(app_no=app_no, is_enabled=True, is_deleted=False).first()
        if not prompt_template:
            raise Exception(f'Prompt template not found for app_no: {app_no}')
        return prompt_template


class SupervisePlanSchemePersonalizFeature(BaseFeature):
    """第三阶段：个性化处理"""

    def __init__(self):
        self.app_no = 'supervise_plan_scheme_personaliz'

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            stage_two_output: str,
            study_stage: str,
            stream: bool = False
    ) -> LLMResult:
        prompt_template = self._get_prompt_template(self.app_no)

        inputs = {
            "第二阶段输出": stage_two_output,
            "学习阶段": study_stage
        }

        query = json.dumps(inputs, ensure_ascii=False)

        pre_prompt = prompt_template.assemble_prompt(
            query=query,
            inputs=inputs
        )

        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=query,
            stream=stream
        )

    def _get_prompt_template(self, app_no: str) -> PromptTemplate:
        prompt_template = PromptTemplate.objects.filter(app_no=app_no, is_enabled=True, is_deleted=False).first()
        if not prompt_template:
            raise Exception(f'Prompt template not found for app_no: {app_no}')
        return prompt_template


class SupervisePrePromptFeature(BaseFeature):
    """Pre-prompt 阶段：处理学生分析"""

    def __init__(self):
        self.app_no = 'supervise_plan_scheme_pre'

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            student_analysis: str,
            start_date: str,
            exam_date: str,
            stream: bool = False
    ) -> LLMResult:
        prompt_template = self._get_prompt_template(self.app_no)

        inputs = {
            "学生分析": student_analysis,
            "开始时间": start_date,
            "考试时间": exam_date
        }

        query = json.dumps(inputs, ensure_ascii=False)

        pre_prompt = prompt_template.assemble_prompt(
            query=query,
            inputs=inputs
        )

        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=query,
            stream=stream
        )

    def _get_prompt_template(self, app_no: str) -> PromptTemplate:
        prompt_template = PromptTemplate.objects.filter(app_no=app_no, is_enabled=True, is_deleted=False).first()
        if not prompt_template:
            raise Exception(f'Prompt template not found for app_no: {app_no}')
        return prompt_template


# 保持向后兼容性的原始类
class Supervise_Learn_Stage_Feature(BaseFeature):
    """向后兼容的原始类，现在作为工厂类使用"""

    def __init__(self, app_no: str):
        self.app_no = app_no

    def _get_prompt_template(self, app_no: str) -> PromptTemplate:
        prompt_template = PromptTemplate.objects.filter(app_no=app_no, is_enabled=True, is_deleted=False).first()
        if not prompt_template:
            raise Exception(f'Prompt template not found for app_no: {app_no}')
        return prompt_template

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            query: str,
            stream: bool,
            prompt_content: str = '',
    ):
        user_inputs = application_generate_entity.inputs.copy()
        prompt_template = self._get_prompt_template(self.app_no)

        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query=query,
            inputs=user_inputs,
            prompt_content=prompt_content
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        # 调用模型
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=query,
            stream=stream
        )
