# Generated by Django 4.2 on 2025-09-01 07:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0067_stuserpaperanswer_report_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='CoursePackageContentByChapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('chapter_name', models.CharField(db_index=True, max_length=36, verbose_name='章节名称')),
                ('unit_name', models.CharField(db_index=True, max_length=36, verbose_name='单元名称')),
                ('subject', models.CharField(max_length=36, null=True, verbose_name='学科')),
                ('course_package_name', models.CharField(max_length=36, null=True, verbose_name='课包名称')),
                ('stage_name', models.CharField(max_length=36, null=True, verbose_name='阶段名称')),
                ('combine_unit_name', models.CharField(max_length=36, null=True, verbose_name='组合单元名称')),
                ('study_target', models.TextField(null=True, verbose_name='学习目标')),
                ('study_guidence', models.TextField(null=True, verbose_name='学习指导')),
                ('chapter_task', models.TextField(null=True, verbose_name='章节任务')),
                ('chapter_date', models.CharField(max_length=36, null=True, verbose_name='章节跨度时间')),
                ('spend_time', models.CharField(max_length=36, null=True, verbose_name='学习时长')),
                ('major_or_minor', models.CharField(max_length=36, null=True, verbose_name='重要程度')),
                ('knowledge_exam_count_and_avg_difficulity', models.JSONField(max_length=108, null=True, verbose_name='对应章节考察知识点考频及平均难度')),
                ('important_degree', models.CharField(max_length=36, null=True, verbose_name='考频重要程度')),
            ],
            options={
                'verbose_name': '往年课包内容',
                'verbose_name_plural': '往年课包内容',
                'db_table': 'ai_course_package_content_by_chapter',
            },
        ),
        migrations.CreateModel(
            name='CoursePackageWithTime',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject', models.CharField(max_length=36, null=True, verbose_name='学科')),
                ('course_date', models.CharField(max_length=36, null=True, verbose_name='课程时间')),
                ('course_name', models.TextField(null=True, verbose_name='课程名称')),
            ],
            options={
                'verbose_name': '往年课包课程添加时间',
                'verbose_name_plural': '往年课包课程添加时间',
                'db_table': 'ai_course_package_time',
            },
        ),
        migrations.CreateModel(
            name='KaoGangAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject', models.CharField(max_length=36, null=True, verbose_name='学科')),
                ('kaogang_content', models.TextField(null=True, verbose_name='考纲内容')),
                ('kaogang_logic', models.TextField(null=True, verbose_name='考纲科目逻辑')),
            ],
            options={
                'verbose_name': '考纲分析',
                'verbose_name_plural': '考纲分析',
                'db_table': 'ai_kaogang_analysis',
            },
        ),
        migrations.CreateModel(
            name='PersonalizedExamSyllabus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('student_score', models.IntegerField(null=True, verbose_name='学生总分')),
                ('exam_syllabus', models.TextField(null=True, verbose_name='个性化考纲')),
                ('subject', models.CharField(max_length=36, null=True, verbose_name='学科')),
            ],
            options={
                'verbose_name': '个性化考纲',
                'verbose_name_plural': '个性化考纲',
                'db_table': 'ai_personalized_exam_syllabus',
            },
        ),
        migrations.AddField(
            model_name='coursepackagecontent',
            name='important_sections',
            field=models.TextField(null=True, verbose_name='重点章节'),
        ),
        migrations.AddField(
            model_name='coursepackagecontent',
            name='unit_date',
            field=models.CharField(max_length=36, null=True, verbose_name='单元跨度时间'),
        ),
        migrations.AddField(
            model_name='examanalysisknowledgepointwithstats',
            name='priority',
            field=models.CharField(max_length=100, null=True, verbose_name='优先级'),
        ),
        migrations.AlterField(
            model_name='superviseinitstudentstatus',
            name='target',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='目标院校基本信息'),
        ),
    ]
