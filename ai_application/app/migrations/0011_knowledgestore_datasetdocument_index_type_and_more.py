# Generated by Django 4.2 on 2024-11-27 02:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0010_prompttemplatechangelog'),
    ]

    operations = [
        migrations.CreateModel(
            name='KnowledgeStore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('course_id', models.CharField(db_index=True, max_length=40, verbose_name='课程ID')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='名称')),
                ('definition', models.TextField(null=True, verbose_name='定义')),
                ('desc', models.TextField(null=True, verbose_name='描述')),
                ('is_usable', models.BooleanField(default=True, verbose_name='是否可用')),
            ],
            options={
                'verbose_name': '知识点内容存储',
                'verbose_name_plural': '知识点内容存储',
                'db_table': 'ai_knowledge_store',
            },
        ),
        migrations.AddField(
            model_name='datasetdocument',
            name='index_type',
            field=models.CharField(default='text_model', max_length=40, verbose_name='索引类型'),
        ),
        migrations.AddField(
            model_name='datasetdocument',
            name='original_knowledge_document_no',
            field=models.CharField(blank=True, max_length=36, verbose_name='原始文档编号'),
        ),
        migrations.AddField(
            model_name='messagetracing',
            name='model_id',
            field=models.CharField(blank=True, max_length=32, verbose_name='模型名称'),
        ),
        migrations.AddField(
            model_name='messagetracing',
            name='model_provider',
            field=models.CharField(blank=True, max_length=32, verbose_name='模型提供方'),
        ),
        migrations.AddField(
            model_name='prompttemplate',
            name='model_id',
            field=models.CharField(blank=True, max_length=32, verbose_name='模型名称'),
        ),
        migrations.AddField(
            model_name='prompttemplate',
            name='model_provider',
            field=models.CharField(blank=True, max_length=32, verbose_name='模型提供方'),
        ),
        migrations.AlterField(
            model_name='appmodelconfig',
            name='prompt_type',
            field=models.CharField(choices=[('for_params', '支持参数'), ('for_template', '支持模板'), ('for_workflow', '支持工作流')], default='for_params', max_length=32, verbose_name='提示词类型'),
        ),
        migrations.AlterField(
            model_name='datasetdocument',
            name='dataset_process_rule',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.datasetprocessrule'),
        ),
        migrations.CreateModel(
            name='Knowledge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='名称')),
                ('definition', models.TextField(null=True, verbose_name='定义')),
                ('index_node_id', models.CharField(blank=True, max_length=255, verbose_name='索引节点ID')),
                ('dataset', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.dataset')),
                ('dataset_document', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.datasetdocument')),
            ],
            options={
                'verbose_name': '知识点',
                'verbose_name_plural': '知识点',
                'db_table': 'ai_knowledge',
            },
        ),
    ]
