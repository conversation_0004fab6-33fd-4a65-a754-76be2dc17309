import datetime

from celery import shared_task
from django.conf import settings
from django.utils import timezone

from app.models import StudentLearnStat
from app.services import learn_status_service


@shared_task(queue='learn_status_check')
def auto_check_student_learn_stat_gen():
    # 预发布环境压测先不处理
    # if settings.ENVIRONMENT == settings.ENV_PREVIEW:
    #     return

    qs = StudentLearnStat.objects.filter(is_deleted=False, status='NOT_START')[:10]
    for record in qs:
        check_student_learn_stat_gen(record)

    # 10分钟后未处理则修改为失败
    time_point = timezone.now() - datetime.timedelta(minutes=10)
    StudentLearnStat.objects.filter(
        is_deleted=False, status='ING', add_time__lt=time_point
    ).update(status='FAIL', fail_reason='超时')


@shared_task(queue='learn_status_check')
def delay_student_learn_stat_gen(record_id):
    record = StudentLearnStat.objects.get(id=record_id)
    learn_status_service.submit_learn_status(record)


def check_student_learn_stat_gen(record: StudentLearnStat):
    record.status = 'ING'
    record.save(update_fields=['status'])
    delay_student_learn_stat_gen.delay(record.id)
