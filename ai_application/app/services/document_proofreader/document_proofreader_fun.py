import logging
import re
import json
import asyncio
import uuid
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import List, Tuple, Dict, Any, Optional, Callable
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from django.conf import settings

from app.models import App, Conversation, Account, Message

logger = logging.getLogger(__name__)


class ChunkProofreadResult:
    """
    单个文本块的校对结果
    """
    def __init__(self, chunk_index: int, labeled_text: str, corrected_text: str, error_count: int):
        self.chunk_index = chunk_index        
        self.labeled_text = labeled_text      
        self.corrected_text = corrected_text  
        self.error_count = error_count        


class AsyncDocumentProofreader:
    """
    异步文档校对器
    
    这是核心的校对类, 支持异步并发处理多个文本块, 
    大幅提升大文档的校对速度。
    """
    
    def __init__(
            self,
            max_workers: int = 10,
            dictionary: List[str] = None,
            conversion_rules: Dict[str, str] = None,
            professional_terms: List[str] = None,
            subject_category: str = None,
            use_two_stage: bool = True,
            proofreader_id: str = ''
    ):
        """
        初始化异步文档校对器
        
        Args:
            max_workers (int): 最大并发工作线程数
            dictionary (List[str]): 词库列表
            conversion_rules (Dict[str, str]): 词汇转换规则
            professional_terms (List[str]): 专业词汇字典["词汇1", "词汇2"] 
            target_category (str): 目标审校类别
            use_two_stage (bool): 是否使用两轮校对模式，默认True
        """
        self.max_workers = max_workers
        self.dictionary = dictionary or []
        self.conversion_rules = conversion_rules or {}
        self.professional_terms = professional_terms or []
        self.subject_category = subject_category
        self.use_two_stage = use_two_stage
        self.model = self._init_model()
        self.error_type_professional = ["专业术语错误类", "语法/句法错误类", "错别字类", "常识错误类"]
        # 智能预处理专业词汇
        self.relevant_terms = self._get_category_terms()
        # 专业密度阈值（可调节）
        self.professional_density_threshold = 0.15  # 15%的密度阈值
        self.proofreader_id = proofreader_id
        
        print(f"🚀 异步校对器已初始化, 最大并发数: {max_workers}")
        print(f"📚 专有名词保护: {len(self.dictionary)} 个")
        print(f"🔄 词汇转换规则: {len(self.conversion_rules)} 条")
        print(f"🎯 专业词汇审校: {self.subject_category or '未启用'} ({len(self.relevant_terms)} 个术语)")
        print(f"📊 专业密度阈值: {self.professional_density_threshold * 100}%")
        print(f"🔄 校对模式: {'两轮模式' if self.use_two_stage else '单轮模式'}")

    def _get_category_terms(self) -> List[str]:
        """获取指定类别的专业词汇"""
        if not self.subject_category or not self.professional_terms:
            return []
        return self.professional_terms

    def _select_relevant_terms(self, content: str, max_terms: int = 8) -> List[str]:
        """
        智能筛选与内容相关的专业词汇

        使用三层匹配策略：精确匹配 → 部分匹配 → 字符重叠
        """
        if not self.relevant_terms:
            return []
            
        exact_matches = [term for term in self.relevant_terms if term in content]
        # 第二层：部分匹配（术语的关键词出现在内容中）
        partial_matches = []
        for term in self.relevant_terms:
            if term not in exact_matches:
                # 检查术语的关键字符是否在内容中
                if len(term) >= 3 and any(char in content for char in term[:3]):
                    partial_matches.append(term)
        # 第三层：按匹配度排序并限制数量
        all_matches = exact_matches + partial_matches[:max_terms-len(exact_matches)]
        return all_matches

    def _init_model(self):
        """初始化语言模型"""
        return ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="doubao-1-5-pro-256k-250115",
            temperature=0,
            top_p=0,
        )
    
    async def proofread_document_async(
        self, 
        content: str, 
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> Tuple[str, str, Dict[str, Any]]:
        """
        异步校对文档内容

        1. 分割文档 → 2. 异步处理各块 → 3. 合并结果 → 4. 返回最终结果
        
        Args:
            content (str): 文档内容
            progress_callback (function, optional): 进度回调函数, 参数为(当前进度, 总进度)
        
        Returns:
            tuple: (校正后的文本, 带标记的文本, 包含详细信息的字典)
        """
        print(f"📄 开始异步文档校对...")
        
        # 步骤1: 分割文本块
        chunks = self._split_content(content)
        print(f"✂️  文档已分割为 {len(chunks)} 个文本块")
        
        # 步骤2: 异步处理所有文本块（核心步骤）
        chunk_results = await self._process_chunks_async(chunks, progress_callback)
        logger.info(f"chunk处理结果\n: {chunk_results}")

        # 步骤3: 调整错误ID并合并结果
        corrected_text, labeled_text = self._merge_results_with_id_adjustment(chunk_results)
        logger.info(f"📚 带标记文本\n: {labeled_text}")
        
        labeled_text = self._reorder_error_ids(labeled_text)
        # 步骤4: 提取错误信息
        error_list = extract_errors_from_labeled_text(labeled_text)
        logger.info(f"错误列表:\n{error_list}")

        # 错误类型分类净化，确保错误类型符合错误类型分类
        for error in error_list:
            if error['error_type'] == '音近字':
                error['error_type'] = '错别字类'
            elif error['error_type'] == '形近字':
                error['error_type'] = '错别字类'
            elif error['error_type'] == '错别字':
                error['error_type'] = '错别字类'
            elif error['error_type'] == '的地得错误':
                error['error_type'] = '错别字类'
            elif error['error_type'] == '标点符号错误':
                error['error_type'] = '错别字类'
            elif error['error_type'] == '错别字类':
                error['error_type'] = '错别字类'

            elif error['error_type'] == '用词不当':
                error['error_type'] = '语法/句法错误类'
            elif error['error_type'] == '语法错误':
                error['error_type'] = '语法/句法错误类'
            elif error['error_type'] == '搭配不当':
                error['error_type'] = '语法/句法错误类'
            elif error['error_type'] == '逻辑不通顺':
                error['error_type'] = '语法/句法错误类'
            elif error['error_type'] == '重复内容':
                error['error_type'] = '语法/句法错误类'
            elif error['error_type'] == '句子不通顺':
                error['error_type'] = '语法/句法错误类'
            elif error['error_type'] == '语法/句法错误类':
                error['error_type'] = '语法/句法错误类'

            elif error['error_type'] == '常识性错误':
                error['error_type'] = '常识错误类'
            elif error['error_type'] == '常见地名错误':
                error['error_type'] = '常识错误类'
            elif error['error_type'] == '常识错误类':
                error['error_type'] = '常识错误类'
            else:
                error['error_type'] = '专业术语错误类'
        
        # 步骤5: 构建结果字典
        result_dict = {
            "corrected_text": corrected_text,
            "labeled_text": labeled_text,
            "error_list": error_list
        }
        
        print(f"✅ 异步校对完成！共发现 {len(error_list)} 个错误")
        return corrected_text, labeled_text, result_dict
    
    async def _process_chunks_async(
        self, 
        chunks: List[str], 
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[ChunkProofreadResult]:
        """
        异步处理所有文本块
        
        Args:
            chunks (List[str]): 文本块列表
            progress_callback (function, optional): 进度回调函数
        
        Returns:
            List[ChunkProofreadResult]: 按顺序排列的处理结果
        """
        print(f"⚡ 开始异步处理 {len(chunks)} 个文本块...")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            tasks = []
            for i, chunk in enumerate(chunks):
                task = asyncio.create_task(
                    self._process_single_chunk_async(executor, i, chunk)
                )
                tasks.append(task)
            
            completed_results = {}
            completed_count = 0
            
            for task in asyncio.as_completed(tasks):
                result = await task
                completed_results[result.chunk_index] = result
                completed_count += 1
                
                print(f"📝 文本块 {result.chunk_index + 1} 处理完成 ({completed_count}/{len(chunks)})")
                
                if progress_callback:
                    progress_callback(completed_count, len(chunks))

        # 按原始顺序重新排列结果
        ordered_results = []
        for i in range(len(chunks)):
            if i in completed_results:
                ordered_results.append(completed_results[i])
            else:
                print(f"⚠️  警告: 文本块 {i} 处理失败, 使用原文本")
                ordered_results.append(ChunkProofreadResult(i, chunks[i], chunks[i], 0))
        
        return ordered_results
    
    async def _process_single_chunk_async(
        self, 
        executor: ThreadPoolExecutor, 
        chunk_index: int, 
        chunk_content: str
    ) -> ChunkProofreadResult:
        """
        异步处理单个文本块
        
        Args:
            executor (ThreadPoolExecutor): 线程池执行器
            chunk_index (int): 文本块索引
            chunk_content (str): 文本块内容
        
        Returns:
            ChunkProofreadResult: 单个块的处理结果
        """
        print(f"🔄 开始处理文本块 {chunk_index + 1}...")
        
        loop = asyncio.get_event_loop()

        labeled_text, corrected_text, error_count = await loop.run_in_executor(
            executor, 
            self._proofread_document_chunk_sync, 
            chunk_content, 
            1  
        )
        
        return ChunkProofreadResult(
            chunk_index=chunk_index,
            labeled_text=labeled_text,
            corrected_text=corrected_text,
            error_count=error_count
        )

    def _merge_results_with_id_adjustment(
        self, 
        chunk_results: List[ChunkProofreadResult]
    ) -> Tuple[str, str]:
        """
        合并处理结果并调整错误ID（解决并发ID冲突问题）
        
        这是解决您提出的current_count问题的核心函数：
        - 按顺序处理每个块的结果
        - 累加错误ID, 确保不重复
        - 合并所有文本块
        
        Args:
            chunk_results (List[ChunkProofreadResult]): 按顺序排列的块处理结果
        
        Returns:
            Tuple[str, str]: (校正后的文本, 带标记的文本)
        """
        print("🔗 开始合并结果并调整错误ID...")
        
        corrected_parts = []
        labeled_parts = []
        cumulative_error_count = 0
        
        for i, result in enumerate(chunk_results):
            # 调整当前块的错误ID（关键步骤）
            adjusted_labeled_text = self._adjust_error_ids(
                result.labeled_text, 
                cumulative_error_count
            )
            
            # 添加到结果列表
            corrected_parts.append(result.corrected_text)
            labeled_parts.append(adjusted_labeled_text)
            
            # 更新累计错误数量
            actual_error_count = self._count_errors_in_text(adjusted_labeled_text)
            cumulative_error_count += actual_error_count
        # 合并所有部分
        corrected_text = self._merge_text_parts(corrected_parts)
        labeled_text = self._merge_text_parts(labeled_parts)
        return corrected_text, labeled_text
    
    def _adjust_error_ids(self, labeled_text: str, id_offset: int) -> str:
        """
        调整文本中的错误ID（解决并发ID重复问题）
        
        Args:
            labeled_text (str): 带标记的文本
            id_offset (int): ID偏移量
        
        Returns:
            str: 调整后的带标记文本
        """
        if id_offset == 0:
            return labeled_text
        
        # 正则表达式匹配错误标记中的ID
        pattern = r'👉id:\s*(\d+)'
        
        def replace_id(match):
            original_id = int(match.group(1))
            new_id = original_id + id_offset
            return f'👉id: {new_id}'
        
        adjusted_text = re.sub(pattern, replace_id, labeled_text)
        return adjusted_text
    
    def _count_errors_in_text(self, labeled_text: str) -> int:
        """
        统计文本中的错误数量
        
        Args:
            labeled_text (str): 带标记的文本
        
        Returns:
            int: 错误数量
        """
        pattern = r'~~.*?~~👉id:\s*\d+.*?👈'
        matches = re.findall(pattern, labeled_text, re.DOTALL)
        return len(matches)
    
    def _merge_text_parts(self, text_parts: List[str]) -> str:
        if not text_parts:
            return ""
        # 两个换行拼接片段，兼容markdown异常格式
        return '\n'.join(part for part in text_parts if part.strip())
    
    def _clean_protected_words(self, labeled_text: str) -> str:
        """
        清理被误标记的专有名词并重新编号ID
        """
        ignore_words = [
            "style=", 
            "text-align:", 
            "text-align:left", 
            "text-align:center", 
            "text-align:right", 
            "text-align:justify",
            "td",
            "tr",
            "table",
            "br",
            "colspan",
            "rowspan",
            "border",
            ]
        self.dictionary += ignore_words
        if not self.dictionary:
            return labeled_text
        
        self.dictionary += ignore_words
        cleaned_text = labeled_text
        
        # 遍历专有名词词库，清理误标记
        for word in self.dictionary:
            if not word.strip():
                continue
                
            # 查找包含该专有名词的错误标记模式
            pattern = rf'~~([^~👉👈]*?{re.escape(word)}[^~👉👈]*?)~~[^~👉👈]*👉id:[^👈]*?👈'
            matches = re.findall(pattern, cleaned_text, re.DOTALL)
            
            for match in matches:
                # 如果错误标记中的文本包含专有名词，则移除标记
                if word in match:
                    full_match = re.search(rf'~~{re.escape(match)}~~[^~👉👈]*👉id:[^👈]*?👈', cleaned_text, re.DOTALL)
                    if full_match:
                        cleaned_text = cleaned_text.replace(full_match.group(), match)
                        print(f"🧹 清理专有名词误标记: '{word}' 在 '{match}' 中")
        
        # 重新编号所有剩余的错误ID，确保连续
        return self._reorder_error_ids(cleaned_text)
    
    def _reorder_error_ids(self, labeled_text: str) -> str:
        """
        重新编号错误ID，确保连续（只需8行代码）
        """
        # 找到所有错误标记
        # pattern = r'(~~[^~]+?~~👉id:\s*)(\d+)(.*?👈)'
        pattern = r'(~~[^~👉👈]+?~~[^~👉👈]*👉id:\s*)(\d+)(.*?👈)'
        matches = list(re.finditer(pattern, labeled_text))
        
        # 重新编号
        result = labeled_text
        for i, match in enumerate(matches, 1):
            old_mark = match.group()
            new_mark = f"{match.group(1)}{i}{match.group(3)}"
            result = result.replace(old_mark, new_mark, 1)  # 只替换第一个匹配
        
        return result
    
    def _normalize_error_types(self, labeled_text: str) -> str:
        """
        将labeled_text中每个错误标记的错误类型强制转换为四种标准类型之一
        """
        
        # 更精确的正则表达式，支持可选的错误文本
        norm_pattern = (
            r'~~([^~👉👈]*?)~~([^~👉👈]*?)👉id:\s*(\d+)\s*'
            r'修改建议:\s*([\s\S]*?)\s*'
            r'错误理由:\s*([\s\S]*?)\s*'
            r'错误类型:\s*([^👈]*?)👈'
        )

        def _normalize_type(error_type: str) -> str:
            """错误类型标准化映射"""
            et = error_type.strip()
            
            # 错别字类
            if et in {"音近字", "形近字", "错别字", "的地得错误", "标点符号错误", "错别字类"}:
                return "错别字类"
            
            # 语法/句法错误类
            elif et in {"用词不当", "语法错误", "搭配不当", "逻辑不通顺", "重复内容", 
                    "句子不通顺", "语法/句法错误类"}:
                return "语法/句法错误类"
            
            # 常识错误类
            elif et in {"常识性错误", "常见地名错误", "常识错误类"}:
                return "常识错误类"
            
            # 其他类型统一归为专业术语错误类
            else:
                return "专业术语错误类"

        def _replace_error_type(match: re.Match) -> str:
            """替换错误类型的回调函数"""
            error_text = match.group(1)
            text_before = match.group(2)
            error_id = match.group(3)
            suggestion = match.group(4).strip()
            reason = match.group(5).strip()
            old_type = match.group(6)
            
            # 标准化错误类型
            new_type = _normalize_type(old_type) if old_type.strip() else "专业术语错误类"
            
            # 重新拼装错误标记（保持原格式）
            return (
                f"~~{error_text}~~{text_before}👉id: {error_id} "
                f"修改建议: {suggestion} "
                f"错误理由: {reason} "
                f"错误类型: {new_type}👈"
            )

        # 执行替换
        result = re.sub(norm_pattern, _replace_error_type, labeled_text, flags=re.DOTALL)
        return result

    def _split_content(self, content: str) -> List[str]:
        """
        将内容分割成固定大小的块
        
        Args:
            content (str): 原始内容
        
        Returns:
            List[str]: 文本块列表
        """
        try:
            text_splitter = RecursiveCharacterTextSplitter(
                separators=["\n\n", "\n"],
                # separators=['。', '？', '！', "\n"],
                chunk_size=200,  # 每块200字符, 可根据需要调整
                chunk_overlap=0,  # 不重叠, 避免重复处理
                length_function=len,
            )

            pattern = r'(<table.*?</table>)'
            parts = re.split(pattern, content, flags=re.DOTALL)

            # 过滤空字符串（split可能会产生空元素）
            parts = [p.strip() for p in parts if p.strip()]

            chunks = []
            for part in parts:
                # 目前表格元素不分割，直接保留
                if part.startswith('<table'):
                    chunks.append(part)
                    continue

                # 如果该行是代码块，则不处理
                code_pattern = r'(```.*?```)'
                code_parts = re.split(code_pattern, part, flags=re.DOTALL)
                for code_part in code_parts:
                    if code_part.startswith('```'):
                        chunks.append(code_part)
                        continue
                    part_chunks = text_splitter.split_text(code_part)
                    chunks.extend(part_chunks)
            return chunks
        except Exception as e:
            raise Exception(f"分割文本时出错: {str(e)}")
    
    def _id_reset(self, labeled_text: str) -> str:
        """
        重置错误标记的ID，从1开始累加
        """
        id_counter = 1
        
        def reset_error_id(match):
            nonlocal id_counter
            error_text = match.group(1)
            text_before = match.group(2)
            old_id = match.group(3)
            suggestion = match.group(4)
            reason = match.group(5)
            error_type = match.group(6)
            
            # 重新组装错误标记，使用新的ID
            new_error_mark = (
                f"~~{error_text}~~{text_before}👉id: {id_counter} "
                f"修改建议: {suggestion} "
                f"错误理由: {reason} "
                f"错误类型: {error_type}👈"
            )
            
            id_counter += 1
            return new_error_mark
        
        # 使用正则表达式重置所有错误标记的ID
        labeled_text = re.sub(
            r'~~([^~👉👈]*?)~~([^~👉👈]*?)👉id:\s*(\d+)\s*修改建议:\s*([^👈]*)\s*错误理由:\s*([^👈]*?)\s*错误类型:\s*([^👈]*?)👈',
            reset_error_id,
            labeled_text
        )
        
        print(f"🔄 已重置错误ID，共 {id_counter - 1} 个错误")
        return labeled_text
    
    def _proofread_document_chunk_sync(
        self, 
        chunk_content: str, 
        current_count: int = 1
    ) -> Tuple[str, str, int]:
        """
        同步校对单个文档块 - 使用两轮大模型策略
        
        第一轮：检测错误，记录位置和上下文
        第二轮：分类错误，给出修改建议和理由
        
        Args:
            chunk_content (str): 文本块内容
            current_count (int): 当前错误计数起始值
        Returns:
            Tuple[str, str, int]: (带标记的文本, 校正后的文本, 错误数量)
        """
        # 表格和代码块元素不处理
        if chunk_content.startswith('<table') or chunk_content.startswith('```'):
            return chunk_content, chunk_content, 0

        # 构建词汇转换规则字符串
        # conversion_rules_str = ""
        # if self.conversion_rules:
        #     conversion_rules_list = [f"'{source}' → '{target}'" for source, target in self.conversion_rules.items()]
        #     conversion_rules_str = f"""
        #     词汇转换要求：请将以下词汇进行统一转换, 确保用词一致性：
        #     {', '.join(conversion_rules_list)}
        #     转换规则说明：
        #         1. 字典中需要转化的词汇直接放到错误标记中, 不要进行强制转换。
        #         2. 如果是词汇转换, conversion_rules_str, 那么直接判断为错误, 然后标记出来。例如, conversion_rules = ("天真": "可爱"), content是"天真无邪的小孩", 那么content直接给出建议即可, 变为"~~天真~~👉id: 3, 修改建议: 可爱, 错误理由: 根据词汇转换要求👈无邪的小孩", 而不是直接进行强制转换。
        #     """

        if self.use_two_stage:
            return self._proofread_with_two_stage_mode(chunk_content, current_count)
        else:
            return self._proofread_with_professional_mode(chunk_content)

    def _proofread_with_two_stage_mode(self, chunk_content: str, current_count: int = 1) -> Tuple[str, str, int]:
        """
        两阶段校对模式
        
        第一阶段：错误检测 - 识别可能的错误及其位置和上下文
        第二阶段：错误分类标记 - 对检测出的错误进行分类并给出修改建议
        """
        print(f"🔍 开始第一轮错误检测...")
        
        # 第一轮：错误检测
        detected_errors = self._detect_errors_stage_one(chunk_content)
        logger.info(f"第一阶段处理结果:\n {detected_errors}")

        if not detected_errors:
            print(f"✅ 第一轮检测: 未发现错误")
            return chunk_content, chunk_content, 0
        print("detected_errors\n", detected_errors)
        print(f"🎯 第一轮检测: 发现 {len(detected_errors)} 个可能的错误")
        # 第二轮：错误分类和标记
        print(f"📝 开始第二轮错误分类标记...")
        labeled_text = self._classify_and_mark_errors_stage_two(chunk_content, detected_errors, current_count)

        # 重置错误标记的ID
        labeled_text = self._id_reset(labeled_text)

        # 去除异常标记
        temp_text = labeled_text
        temp_text = re.sub(
            r'~~([^~👉👈]+?)~~[^~👉👈]*👉id:\s*(\d+)\s*修改建议:\s*([^👈]*)\s*错误理由:\s*([^👈]*?)\s*错误类型:\s*([^👈]*?)👈', 
            r'\1', 
            temp_text
        )
        # 获取完整的错误标记文本
        error_marks = re.finditer(
            r'~~[^~👉👈]*👉id:\s*(\d+)\s*修改建议:\s*([^👈]+?)\s*错误理由:\s*([^👈]*?)\s*错误类型:\s*([^👈]*?)👈',
            temp_text
        )
        # 提取完整的错误标记并从labeled_text中清理
        for match in error_marks:
            error_mark = match.group(0)  # 完整的错误标记
            # 从labeled_text中移除这个错误标记
            labeled_text = labeled_text.replace(error_mark, '')
        
        # 标准化错误类型
        labeled_text = self._normalize_error_types(labeled_text)
        
        # 净化labeled_text中的</td>标签错误标记
        # 将错误标记的</td>恢复为正常的</td>
        labeled_text = re.sub(r'</td[^>]*>', '</td>', labeled_text)
        labeled_text = re.sub(r'</tr[^>]*>', '</tr>', labeled_text)
        labeled_text = re.sub(r'</table[^>]*>', '</table>', labeled_text)
        
        # 最终统一重排ID，确保连续且不重复
        labeled_text = self._reorder_error_ids(labeled_text)
        labeled_text_cleaned = self._clean_protected_words(labeled_text)
        corrected_text = _generate_corrected_text_from_labeled(labeled_text_cleaned)
        error_count = self._count_errors_in_text(labeled_text_cleaned)

        logger.info(f"第二阶段处理结果:\n {labeled_text_cleaned}")
        return labeled_text_cleaned, corrected_text, error_count

    def _detect_errors_stage_one(self, chunk_content: str) -> List[Dict[str, str]]:
        """
        第一阶段：错误检测
        
        Args:
            chunk_content (str): 文本块内容
            
        Returns:
            List[Dict[str, str]]: 检测到的错误列表，包含错误文本、位置和上下文
        """
        
        # 第一轮检测的prompt
        detection_template = """你是一位专业的中文文档校对专家，擅长发现中文文档中的各种错误。

请仔细检测以下文本中可能存在的错误，包括"语法/句法错误类、错别字类、常识错误类、专业错误类"：
    - 专业错误类：不符合{subject_category}领域的专业特点和术语习惯
    - 语法句法错误类：用词不当/语法错误、搭配不当错误、常见句法错误
    - 常识错误类：常识错误、常见地名错误、句子重复错误、句子不通顺、重复内容、逻辑不通顺
    - 错别字类：音/形相似错误、标点符号错误、的地得错误、错别字

**重要任务说明：**
- 你的任务是检测和定位错误，不要进行修正
- 对于每个发现的错误，需要记录：错误文本内容、错误在原文中的精确位置、包含该错误的前后10个字符的上下文

**输出格式：**
请严格按照以下JSON格式输出检测结果，每个错误一个条目：

```
[
  {{{{
    "error_text": "错误的文本内容",
    "context": "包含错误文本的前后10个字符上下文",
    "position_hint": "错误在原文中的大致位置描述"
  }}}}
]
```

**输出要求：**
1. 如果没有发现错误，请输出空数组：[]
2. 确保context字段包含完整的上下文，有助于准确定位
3. error_text必须是原文中的确切文本
4. 只输出标准JSON格式，不要添加任何解释文字、注释或其他内容
5. JSON必须是完整有效的，不能有语法错误

**严格限制：**
1. 不能有任何非JSON的文字说明
2. 不能有注释或解释，不要有任何解释文字！！！
3. 不要检测任何链接，类似于[链接](https://www.baidu.com)等任何链接
4. 不要检测图片链接，类似于![](/Users/<USER>/PycharmProjects/备份/第1章test_副本_images/image_e100e0edaabf.png)等任何图片链接
5. 不要检测公式，类似于$公式$等任何公式
6. 不要检测和表格相关的错误，例如表格的格式、表格的样式、表格的布局等
7. 不要检测和html标签相关的错误，例如<br>、<p>、<span>、<div>、<img>、<a>、<table>、<tr>、<td>等任何html标签

待检测文本：
{chunk_content}
"""

        prompt = PromptTemplate(
            template=detection_template, 
            input_variables=["chunk_content", "subject_category"]
        )
        llm_chain = prompt | self.model

        query_dict = {
            'chunk_content': chunk_content,
            'subject_category': self.subject_category or "通用",
        }
        response = llm_chain.invoke(query_dict)
        response_text = response.content if hasattr(response, 'content') else str(response)

        # 记录转换结果
        query = json.dumps(query_dict, ensure_ascii=False)
        prompt_info = {'sys_prompt': detection_template}
        save_llm_message(self.proofreader_id, query, response_text, prompt_info)
        
        # 安全的JSON解析
        return self._safe_parse_json_response(response_text)

    def _safe_parse_json_response(self, response_text: str) -> List[Dict[str, str]]:
        """安全解析JSON响应，处理各种格式问题"""
        try:
            # 方法1: 查找```json```代码块
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(1).strip()
            else:
                # 方法2: 查找JSON数组
                array_match = re.search(r'(\[.*?\])', response_text, re.DOTALL)
                if array_match:
                    json_text = array_match.group(1).strip()
                else:
                    # 方法3: 直接使用响应
                    json_text = response_text.strip()
            
            # 清理和修复JSON
            json_text = self._clean_and_fix_json(json_text)
            
            # 解析JSON
            detected_errors = json.loads(json_text)
            
            # 验证并返回有效错误
            if isinstance(detected_errors, list):
                return [error for error in detected_errors 
                       if isinstance(error, dict) and 'error_text' in error and 'context' in error]
            
            print(f"⚠️ 响应不是列表格式: {type(detected_errors)}")
            return []
                
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON解析失败: {e}")
            print(f"原始响应: {response_text}")
            # 尝试修复常见JSON错误
            return self._try_repair_json(response_text)
        except Exception as e:
            print(f"⚠️ 解析出现未知错误: {e}")
            return []

    def _clean_and_fix_json(self, json_text: str) -> str:
        """清理并修复JSON文本中的格式问题"""
        # 1. 基本清理
        json_text = self._clean_json_text(json_text)
        
        # 2. 简单而有效的修复策略：逐行处理
        lines = json_text.split('\n')
        fixed_lines = []
        
        for line in lines:
            # 检测包含字段值的行
            for field in ['error_text', 'context', 'position_hint']:
                pattern = f'"{field}":\\s*"'
                if re.search(pattern, line):
                    # 找到字段名和值的分界点
                    match = re.search(f'("{field}":\\s*")(.*)', line)
                    if match:
                        prefix = match.group(1)
                        value_part = match.group(2)
                        
                        # 处理值部分：找到真正的结束引号
                        # 从后往前找，跳过可能的尾部符号
                        value_end = len(value_part)
                        for i in range(len(value_part) - 1, -1, -1):
                            if value_part[i] == '"' and (i == 0 or value_part[i-1] != '\\'):
                                value_end = i
                                break
                        
                        # 提取值和后缀
                        field_value = value_part[:value_end]
                        suffix = value_part[value_end:]
                        
                        # 转义字段值中的特殊字符
                        field_value = field_value.replace('\\', '\\\\')
                        field_value = field_value.replace('"', '\\"')
                        
                        # 重建行
                        line = prefix + field_value + suffix
                        break
            
            fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)

    def _clean_json_text(self, json_text: str) -> str:
        """清理JSON文本中的注释和多余内容"""
        lines = json_text.split('\n')
        clean_lines = []
        
        for line in lines:
            stripped = line.strip()
            # 跳过注释行和空行
            if stripped and not stripped.startswith('//') and not stripped.startswith('#'):
                clean_lines.append(line)
        
        return '\n'.join(clean_lines)

    def _try_repair_json(self, response_text: str) -> List[Dict[str, str]]:
        """尝试修复常见的JSON格式问题"""
        try:
            # 策略1: 提取JSON数组部分
            start_idx = response_text.find('[')
            end_idx = response_text.rfind(']')
            
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_part = response_text[start_idx:end_idx+1]
                
                # 尝试使用强化的清理和修复
                try:
                    cleaned_json = self._clean_and_fix_json(json_part)
                    detected_errors = json.loads(cleaned_json)
                    if isinstance(detected_errors, list):
                        return [error for error in detected_errors 
                               if isinstance(error, dict) and 'error_text' in error and 'context' in error]
                except Exception as e:
                    print(f"🔧 强化清理失败: {e}")
                
                # 策略2: 逐个提取对象并单独解析
                return self._parse_json_objects_individually(json_part)
            
            # 策略3: 如果找不到完整数组，尝试文本解析
            return self._extract_errors_from_text(response_text)
            
        except Exception as e:
            print(f"⚠️ JSON修复过程出错: {e}")
            return []

    def _extract_errors_from_text(self, text: str) -> List[Dict[str, str]]:
        """从文本中提取错误信息作为最后的解析策略"""
        print("🆘 使用文本解析作为最后策略")
        results = []
        
        # 查找可能的错误文本模式
        error_patterns = [
            r'"error_text":\s*"([^"]+)"',
            r'"context":\s*"([^"]*)"',
            r'"position_hint":\s*"([^"]*)"'
        ]
        
        # 分块查找可能的错误条目
        chunks = text.split('{')
        for i, chunk in enumerate(chunks[1:], 1):  # 跳过第一个空块
            error_data = {}
            
            # 在每个块中查找字段
            for field, pattern in zip(['error_text', 'context', 'position_hint'], error_patterns):
                match = re.search(pattern, chunk)
                if match:
                    error_data[field] = match.group(1)
            
            # 如果找到了错误文本，就添加到结果中
            if 'error_text' in error_data:
                if 'context' not in error_data:
                    error_data['context'] = ''
                if 'position_hint' not in error_data:
                    error_data['position_hint'] = '文本解析'
                
                results.append(error_data)
                print(f"📝 文本解析提取错误 {i}: {error_data['error_text']}")
        
        return results
    
    def _parse_json_objects_individually(self, json_array_text: str) -> List[Dict[str, str]]:
        """逐个解析JSON对象，跳过有问题的对象"""
        results = []
        
        # 使用正则表达式提取每个JSON对象
        # 匹配 { ... } 格式的对象
        object_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        objects = re.findall(object_pattern, json_array_text, re.DOTALL)
        
        for i, obj_text in enumerate(objects):
            try:
                # 清理并修复单个对象
                cleaned_obj = self._clean_and_fix_json(obj_text)
                obj = json.loads(cleaned_obj)
                
                # 验证对象结构
                if (isinstance(obj, dict) and 
                    'error_text' in obj and 
                    'context' in obj):
                    results.append(obj)
                    print(f"✅ 成功解析第 {i+1} 个JSON对象")
                else:
                    print(f"⚠️ 跳过格式不正确的第 {i+1} 个对象: {obj}")
                    
            except Exception as e:
                print(f"⚠️ 跳过无法解析的第 {i+1} 个对象: {e}")
                continue
        
        print(f"📊 成功解析 {len(results)}/{len(objects)} 个JSON对象")
        return results

    
    def _classify_and_mark_errors_stage_two(self, original_text: str, detected_errors: List[Dict[str, str]], start_id: int = 1) -> str:
        """
        第二阶段：错误分类和标记
        
        Args:
            original_text (str): 原始文本
            detected_errors (List[Dict]): 第一阶段检测到的错误列表
            start_id (int): 起始错误ID
            
        Returns:
            str: 标记后的文本
        """
        
        # 构建第二轮的prompt
        errors_info = []
        for i, error in enumerate(detected_errors):
            error_info = f"""
                        错误 {i+1}:
                        - 错误文本: "{error.get('error_text', '')}"
                        - 上下文: "{error.get('context', '')}"
                        - 位置提示: {error.get('position_hint', '未知')}
                        """
            errors_info.append(error_info)
        
        classification_template = """
# 角色
你是一位专业的文档校对专家，负责对文档中的错误进行精准分类、给出修正建议并进行标记。

## 技能
### 技能 1: 错误分类与建议
1. 接收原始文本以及检测到的错误信息。
2. 针对每个检测到的错误，准确判断其错误类型（错误类型分类如下），给出合理的修改建议并阐述错误理由。
    - 专业术语错误类：不符合{subject_category}领域的专业术语规范。
    - 错别字类：包含音近字、形近字、错别字、“的地得”错误、标点符号错误。
    - 语法/句法错误类：涉及用词不当、语法错误、搭配不当、逻辑不通顺、重复内容、句子不通顺。
    - 常识错误类：涵盖常识性错误、常见地名错误。

### 技能 2: 错误标记
1. 在原始文本中找到对应的错误位置，进行精确标记。
2. 使用指定的标记格式“~~错误文本~~👉id: {{{{错误ID号}}}} 修改建议: {{{{修正建议}}}} 错误理由: {{{{错误原因}}}} 错误类型: {{{{错误类型}}}}👈”对原文进行标记。例如，~~错误文本~~👉id: 1 修改建议: 正确文本 错误理由:... 错误类型:专业术语错误类👈
3. 错误ID从{start_id}开始，依次递增

### 技能 3: 输出处理
1. 直接输出标记后的完整原文，不要添加任何说明。
2. 确保标记的错误文本与原文完全一致。
3. 如果修改建议是“删除”，那么修改建议直接采用置为空，例如, ~~错误文本~~👉id: 1 修改建议:  错误理由: ... 错误类型: ...👈

## 任务要求：
1. 对每个检测到的错误，判断其错误类型并给出修改建议和错误理由
2. 在原始文本中找到对应的错误位置，进行精确标记
3. 使用指定的标记格式对原文进行标记
4. 如果错误类型有多个，请使用其中最适合的那一个。

## 限制:
1. 标记中的错误类型必须符合给定的错误类型分类，即必须是专业术语错误类、错别字类、语法/句法错误类、常识错误类这四种类型之一。
2. 输出格式必须严格遵循"~~错误文本~~👉id: 1 修改建议: ... 错误理由:... 错误类型:...👈"，不要使用其他格式。

### 原始文本
{original_text}

### 检测到的错误信息
{errors_info}

请输出标记后的文本：
"""

        prompt = PromptTemplate(
            template=classification_template, 
            input_variables=["original_text", "errors_info", "subject_category", "start_id"]
        )
        llm_chain = prompt | self.model

        query_dict = {
            "original_text": original_text,
            "errors_info": ''.join(errors_info),
            "subject_category": self.subject_category or "通用",
            "start_id": start_id
        }
        response = llm_chain.invoke(query_dict)
        response_text = response.content if hasattr(response, 'content') else str(response)

        # 记录转换结果
        query = json.dumps(query_dict, ensure_ascii=False)
        prompt_info = {'sys_prompt': classification_template}
        save_llm_message(self.proofreader_id, query, response_text, prompt_info)

        return response_text.strip()

    def _proofread_with_professional_mode(self, chunk_content: str) -> Tuple[str, str, int]:
        
        proofread_template_professional = f"""
            你是一位专业的文档校对专家, 擅长发现和纠正中文文档中的错误, 并且会对输入文本进行细致的校对。
            请对以下文本进行仔细校对, 找出其中的专业术语错误,并直接在原文中标记出来。
            顺便找出其中的语法错误、标点错误、错别字、重复内容、逻辑不通顺的地方等问题,并直接在原文中标记出来。
            这些可以和专业术语错误一起标记出来。并且可以归类于基础错误类。
            
            根据{self.subject_category}领域的专业特点和术语习惯进行校对。
            专业词汇审校要求：请特别注意以下{self.subject_category}专业术语的准确性

            并且请按照以下要求进行处理：
            要求：
            1. 【重点】重点检查专业术语错误，不要遗漏。然后检查语法错误、标点错误、错别字、重复内容、逻辑不通顺的地方等问题。
            2. 对于发现的错误, 严格使用以下格式直接在原文中标记：
            - 对错误的文本,使用'~~错误文本~~'来标记错误
            - 格式为：~~错误文本~~👉id: 1 修改建议: 正确文本 错误理由: ... 错误类型: {self.error_type_professional}👈
            - 每发现一个错误, id序号加1
            - 例如："数据结构是一门研究非数值计算的~~程式设计~~👉id: 1 修改建议: 程序设计 错误理由: ... 错误类型: {self.error_type_professional}👈问题中的操作对象"
            - 错误类包含：语法/句法错误类、错别字类、常识错误类、专业错误类
            - 专业错误类：不符合{self.subject_category}领域的专业特点和术语习惯
            - 语法句法错误类：用词不当/语法错误、搭配不当错误、常见句法错误
            - 常识错误类：常识错误、常见地名错误、句子重复错误、句子不通顺、重复内容、逻辑不通顺
            - 错别字类：音/形相似错误、标点符号错误、的地得错误、错别字
            2. 错误类型中的错误，可以存在多个，专业错误类和其他错误类可以同时存在。如果这个错误属于多个错误类，可以输出多个错误类型。
            - 例如，"数据结构是一门研究非数值计算的~~程式设计~~👉id: 1 修改建议: 程序设计 错误理由: ... 错误类型: 专业术语错误类, 错别字类👈问题中的操作对象"。
            3. 输出格式要求：
            corrected_label_text:
                包含标记的完整原文

            限制：
            1. 错误类型格式不需要使用['专业术语错误类'],直接输出,专业术语错误类,即可。
            3. 如果修改建议是是'删除', 那么修改建议直接采用置为空。例如, ~~错误文本~~👉id: 1 修改建议: ... 错误理由: ... 错误类型: ...👈
            4. 不需要添加任何说明！直接返回结果即可！
            5. 标记为错误时，将原文本使用'~~原文本~~'来标记错误。而不是在后面一个'~~错误文本~~'。
            -例如，”zongshang所述，~~zongshang~~👉id: 13 修改建议: 综上 错误理由: 错别字 错误类型: 错别字类👈法律体系的完善“是错误的，直接”~~zongshang~~👉id: 13 修改建议: 综上 错误理由: 错别字 错误类型: 错别字类👈所述，法律体系的完善“
            6. 错误类型只能在{self.error_type_professional}中选择，可以选择多个，但是不要输出其他错误类型。
            7. 如果这个错误属于多个错误类，可以输出多个错误类型。

            文本内容:{{text_chunk}} 
            """

        prompt = PromptTemplate(template=proofread_template_professional, input_variables=["text_chunk"])
        llm_chain = prompt | self.model
        
        response = llm_chain.invoke({
            'text_chunk': chunk_content
        })
        response_text = response.content if hasattr(response, 'content') else str(response)
        label_match = re.search(r'corrected_label_text:\s*(.*?)$', response_text, re.DOTALL)
        labeled_text = label_match.group(1).strip() if label_match else chunk_content
        
        labeled_text_cleaned = self._clean_protected_words(labeled_text)
        corrected_text = _generate_corrected_text_from_labeled(labeled_text_cleaned)
        error_count = self._count_errors_in_text(labeled_text_cleaned)
        
        print(f"✅ 专业模型校对完成，错误数量: {error_count}")
        return labeled_text_cleaned, corrected_text, error_count

# ============================================================================
# 公共接口函数
# ============================================================================


def proofread_document_file(
        content: str, 
        progress_callback=None, 
        dictionary: List[str] = None,
        conversion_rules: Dict[str, str] = None,
        professional_terms: List[str] = None,
        subject_category: str = '通用',
        use_two_stage: bool = True,
        proofreader_id: str = ''
) -> Tuple[str, str, Dict[str, Any]]:
    """
    校对文档文件内容
    使用异步处理来提高性能
    
    Args:
        content (str): 文档内容
        progress_callback (function, optional): 进度回调函数, 参数为(当前进度, 总进度)
        dictionary (List[str], optional): 词库列表
        conversion_rules (Dict[str, str], optional): 词汇转换规则字典，# 模型自身的判断以及分词功能
        professional_terms (List[str], optional): 专业词汇列表
        subject_category (str, optional): 目标审校类别
        use_two_stage (bool, optional): 是否使用两轮校对模式，默认True
    
    Returns:
        tuple: (校正后的文本, 带标记的文本, 包含详细信息的字典)
    """
    print(f"📄 开始文档校对...")
    
    async def _async_proofread():
        """内部异步处理函数"""
        proofreader = AsyncDocumentProofreader(
            max_workers=10, 
            dictionary=dictionary, 
            conversion_rules=conversion_rules,
            professional_terms=professional_terms,
            subject_category=subject_category,
            use_two_stage=use_two_stage,
            proofreader_id=proofreader_id
        )
        
        return await proofreader.proofread_document_async(content, progress_callback)
    
    try:
        # 获取当前事件循环, 如果不存在则创建新的
        loop = asyncio.get_event_loop()
        if loop.is_running():
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, _async_proofread())
                corrected_text, labeled_text, result_dict = future.result()
        else:
            corrected_text, labeled_text, result_dict = loop.run_until_complete(_async_proofread())
    except RuntimeError:
        # 事件循环问题, 直接运行
        corrected_text, labeled_text, result_dict = asyncio.run(_async_proofread())
    
    print(f"✅ 文档校对完成！")
    return corrected_text, labeled_text, result_dict


def extract_errors_from_labeled_text(labeled_text: str) -> List[Dict[str, str]]:
    """
    从带标记的文本中提取错误信息，支持新版格式：
    ~~错误文本~~👉id: {{current_count}}, 修改建议: 正确文本, 错误理由: ..., 错误类型: ...👈
    """
    error_list = []
    # 匹配完整的错误标记模式: ~~...~~👉...👈 (使用贪婪匹配避免逗号问题)
    full_pattern = r'~~([^~👉👈]+?)~~[^~👉👈]*👉id:\s*(\d+)\s*修改建议:\s*([^👈]*)\s*错误理由:\s*([^👈]*?)\s*错误类型:\s*([^👈]*?)👈'
    

    error_marks = re.findall(full_pattern, labeled_text, re.DOTALL)
    for match in error_marks:
        error_text, error_id, error_suggestion, error_reason, error_type = [m.strip() for m in match]
        error_list.append({
            "id": error_id,
            "error_text": error_text,
            "error_suggestion": error_suggestion,
            "error_reason": error_reason,
            "error_type": error_type
        })
    return error_list


def _generate_corrected_text_from_labeled(labeled_text: str) -> str:
    """根据新版带标记的文本生成校正后的文本"""
    def replace_suggestion(match):
        suggestion = match.group(3).strip()
        return suggestion
    # 匹配新版错误标记格式
    pattern = r"~~([^~👉👈]+?)~~[^~👉👈]*👉id:\s*(\d+)\s*修改建议:\s*([^👈]*)\s*错误理由:\s*([^👈]*?)\s*错误类型:\s*([^👈]*?)👈"


    corrected_text = re.sub(pattern, replace_suggestion, labeled_text, flags=re.DOTALL)
    return corrected_text


def save_llm_message(proofreader_id, query, answer, prompt_info):
    # TODO 暂时不记录更新记录
    return

    app_model: App = App.objects.filter(app_type='document_proofreader').first()
    if not app_model:
        return

    proofreader_id = proofreader_id or ''
    user = Account.objects.first()
    conversation = Conversation.objects.create(
        conversation_no=str(uuid.uuid4()),
        app=app_model,
        app_model_config=app_model.app_model_config,
        name='文稿助手会话',
        from_account=user,
    )
    Message.objects.create(
        message_no=str(uuid.uuid4()),
        conversation=conversation,
        app=app_model,
        app_model_config=app_model.app_model_config,
        query=query,  # 存储用户原始问题
        answer=answer,
        message=prompt_info,
        status='normal',
        from_account=user,
        from_biz_id=proofreader_id
    )


# ============================================================================
# 示例和测试代码
# ============================================================================

if __name__ == "__main__":
    """
    示例用法和测试代码
    
    新手可以通过这个示例了解如何使用校对器
    """

    # 🧪 测试样本：包含不同专业密度的文本
    high_density_sample = '''
    根据《宪法》第35条规定，选举权和被选举权是公民的基本政治权利。在法治国家构建中，宪法限制与法律限制共同构成法律体系框架。明确性原则与比例原则作为权利行使的双重约束，确保选举公正有序进行。民族平等、性别平等原则要求立法机关避免特权和差别对待，防止民族歧视、宗教歧视现象。
    '''
    
    low_density_sample = '''
    我喜欢次饭。每天吃法真开心。小明跑得很快，但是他今天迟到了。老师说学生需要好好学习，不然就是违返规定。打羽毛球是一个健康的运洞，大家都喜欢参加体育活动。
    '''
    
    mixed_sample1 = '''
    在现代法治国家的构建中，宪法作为根本大法，其限制作用体现在多个层面。宪法限制与法律限制共同构成法律体系的框架，例如《宪法》第35条明确规定了公民的选举权和被选举权，但这一权利的行使需遵循明确形原则与比利原则的双重约束。我想次饭。
关于公民基本权利的保护，出版自由与宗教信仰自由是核心内容。然而，当言论涉及淫秽言论或煽动仇恨时，国家可依据预防制或追惩罚制进行干预。值得注意的是，民族平等与性别平等原则要求立法机关避免特权和差别对待的出现。例如，某些地方政策若对特定民族实施户口宪制或学位宪制，则可能被认定为民族歧视或宗教歧视，违背平等保护的宪法精神。以及次饭精神。
在权利限制的正当性分析中，合力差别理论具有重要意义。确实确实！还是敲代码比较重要一电。
在司法实践中，证明责任的分配直接影响案件结果。同时，保障义务要求国家通过立法和行政手段消除权利行使的障碍。例如，针对残障人士的家庭生活权力保障，需通过经济权力与社会权力的综合施策实现。
zongshang所述，法律体系的完善不仅依赖于对民族政策、宗教政策的精准设计，还需通过公共利益的衡量平衡个体权利与社会秩序。未来立法应进一步细化差别对待的界限，强化法律审查机制，以实现宪法对“人权保障”的庄严陈诺。
我喜欢次饭。每天吃法真开心。需要特别注意的是，在法律条文中有明确规定，学生需要好好学习，不然就是违返法律。打羽毛球是一个健康的运洞，并且被打了之后，肇事者不会被判形。
    '''

    mixed_sample = '''
    随着计算机硬件性能的不断提升和大数据技撒打算术的广泛应用，人工智能（AI）已成为现代计算机科学的重要研究方向。与此同时，数据结构作为计算机科学的基础，也在不断演进以适应新的需求。人工智能的核心目标是使计算机系统能够执行通常需要人类智能才能完成的复杂任务，例如自然语言处理（NLP）、图像识别、语音识别以及决策制定。
深度学习作为人工智能的一个子领域，依赖于神经网络模型，尤其是卷和神经网络（CNN）和循环神经网络（RNN），这些模型被广泛应用于图像分类和时间序列预测中。为了提高训练效率，通常使用图形处理器（GPU）来加速计算过程。在这个过程中，数据通过各种算法进行处理，其中包括了对数据的操作如进栈（push）。进站是指将元素添加到栈顶的过程，栈是一种后进先出（LIFO, Last In First Out）的数据结构，在程序设计中用于存储临时信息或回溯操作等场景。
在软件层面，开发人员使用高级编程语言如Python，并借助机器学习框架如TensorFlow和PyTorch来构建和训练模型。同时，云计算平台为AI模型提供了强大的计算资源和存储能力，使得分布式训练成为可能。
此外，人工智能也推动了边缘计算的发展，即将数据处理从中心服务器转移到靠近数据源的设备端，以减少延迟并提高响应速度。这种架构常用于自动驾驶汽车和智能互联网（IoT）设备中。
尽管人工智能撒打算带来了许多技术突破，但同时也引发了关于算法篇简、隐私保护和伦理问题的讨论。因此，在设计AI系统时，必须考虑可解释性和公平性原则。阿斯顿啥的
总之，人工智能正以前所未有的速度改变着我们的生活和工作方式，未来它将在医疗、金融、教育等多个撒打算领域发挥更大的作用。而数据结构如栈，则为这些先进的算法和技术提供了必要撒打算大的支持，确保了数据的有效管理和高效处理。

26.汉初，叔孙通制定《傍章律》十八篇，“与律令同录，藏于理官”，作为《九章律》的重要补充。《傍章律》的主要内容是（    ）（2022-非法学-35）
A.朝廷礼仪B.宫廷警卫C.朝贺制度 D.朝议制度

27.韩非提出：“明主之国，无书简之文，以法为教；无先王之语，以吏为师。”中国古代以此为治国指导思想的朝代是（ ）。（2023-非法学-28）

A.秦朝 B.隋朝C.宋朝 D.元朝

28.明初洪武年间“定南北更调之制”，即南方人调北方任官，北方人调南方任官。后来虽不限南北，但“不得官本省”。这种任官回避制度古已有之。下列选项中，规定任官回避制度的是（ ）。（2023-非法学-31）

A.汉朝的三互法 B.曹魏的九品官人法

C.宋朝的差遣制 D.元朝的行省制

29.汉朝颁布《上计律》，其适用的主要事项是（ ）。（2023-非法学-33）

A.地方官吏考核 B.田租赋税征收

C.盗贼缉捕审判 D.强宗豪右监察

30.程树德在《九朝律考》中指出：“自晋氏失驭，海内分裂，江左以清谈相尚，不崇名法。故其时中原律学，衰于南而盛于北。”这一时期中原律学的发展推动了北朝立法的创新。下列选项中，代表了北朝立法最高成就的法典是（ ）。（2023-非法学-34）

A.北魏律 B.麟趾格

C.北齐律 D.大统式

31.据《汉书·陈平传》载，西汉开国功臣陈平年少时与其兄陈伯同住，“伯常耕田，纵平使游学”。其嫂“疾平之不亲家生产”，曰：“有叔如此，不如无有！”陈伯听闻后，“逐其妇弃之”。陈伯“逐其妇”在中国古代离婚制度中属于（ ）（2024-非法学-29）

A.七出B.三不去C.义绝D.和离

32.中国古代政治体制中，凡官必有监察，至汉代监察制度臻于完善。据《后汉书·百官志》，汉代设立的“掌察举百官以下，及京师近郡犯法者”的监察机构是（ ）（2024-非法学-34）

A.御史中丞B.司隶校尉C.大司空D.刺史
    '''
    
    dictionary = [
        # "系痛", "哈哈哈你好的是吗", "无向图", "操作", "ABC", "天真", "明显的", "错误", "阿斯顿", "进站"
        ]
    conversion_rules = {
        # "树形结构": "树形解构", 
        # "天真": "可爱",
        # "数组": "宿主",
        # "搜索树": "搜索🌲",
        # "系痛": "系统",
        # "无向图": "无向兔",
        # "淫秽言论": "淫秽信息",
        # "层面": "方面",
        # "第35条": "第34条",
        }
    
    # 专业词汇数据库
    professional_terms = [
            # "宪法限制", "法律限制", "明确性原则", "比例原则", "选举权和被选举权",
            # "出版自由", "集会、游行、示威自由", "宗教信仰自由", "民族平等", "性别平等",
            # "特权", "差别对待", "淫秽言论", "煽动仇恨", "预防制", "追惩制",
            # "年龄差异", "生理差异", "民族差异", "宗教社团", "国际人权公约",
            # "法律保留", "年龄限制", "学位限制", "户口限制", "保障义务", "理解偏差",
            # "证明责任", "年龄差异的合理差别", "生理差异的合理差别", "民族差异的合理差别",
            # "消极影响", "备案制", "许可制", "民族歧视", "性别歧视", "宗教歧视",
            # "文化权利", "经济权利", "社会权利", "家庭生活权利", "公共利益",
            # "宪法原则", "民主社会", "宪法审查", "民族政策", "宗教政策", "平等保护", "合理差别"
        ]
    subject_category = "法律"
    
    def test_intelligent_routing():
        """测试智能路由功能"""
        print("🧪 开始测试智能路由文档校对器...")
        
        test_cases = [
            # ("高专业密度文本", high_density_sample),
            # ("低专业密度文本", low_density_sample), 
            ("混合密度文本", mixed_sample)
        ]
        
        for test_name, test_content in test_cases:
            print(f"\n{'=='*50}")
            print(f"🔍 测试案例: {test_name}")
            print(f"{'=='*50}")
            
            try:
                corrected, labeled, result = proofread_document_file(
                    content=test_content,
                    dictionary=dictionary,
                    conversion_rules=conversion_rules,
                    professional_terms=professional_terms,
                    subject_category=subject_category
                )
                
                # print(f"📄 校正后的文本:\n{corrected}")
                print(f"\n🏷️ 带标记的文本:\n{labeled}")
                
                # 统计错误类型
                if "error_list" in result and result["error_list"]:
                    professional_errors = [e for e in result["error_list"] if "专业术语" in e["error_type"]]
                    general_errors = [e for e in result["error_list"] if "专业术语" not in e["error_type"]]
                    
                    print(f"\n📊 错误统计:")
                    print(f"  🎯 专业术语错误: {len(professional_errors)} 个")
                    print(f"  📝 通用错误: {len(general_errors)} 个")
                    print(f"  📈 总错误数: {len(result['error_list'])} 个")
                else:
                    print("\n✅ 未发现任何错误, 文档质量很好！")
                    
            except Exception as e:
                print(f"❌ 测试 {test_name} 时出错: {str(e)}")
    
    from datetime import datetime

    start_time = datetime.now()

    # 执行测试
    test_intelligent_routing()

    end_time = datetime.now()
    execution_time = (end_time - start_time).total_seconds()
    print(f"\n⏱️ 程序执行时间: {execution_time:.2f} 秒")


