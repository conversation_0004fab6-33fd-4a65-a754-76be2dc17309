from django.http import JsonResponse, HttpResponse
from django.views import View
from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import pandas as pd
from io import BytesIO
from django.views.generic import TemplateView
from app.console.serializers import UndergraduateCategorySerializer, GraduateCategorySerializer, UndergraduateMajorSerializer, \
    GraduateMajorSerializer, CategoryCorrelationSerializer, MajorCorrelationSerializer
from app.models import UndergraduateCategory, GraduateCategory, UndergraduateMajor, GraduateMajor, CategoryCorrelation, \
    MajorCorrelation

# views.py
from django.http import JsonResponse, HttpResponse
from django.views import View
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import pandas as pd
from io import BytesIO
from django.views.generic import TemplateView


class CategoryListView(APIView):
    """获取所有一级学科"""

    def get(self, request):
        undergrad = UndergraduateCategory.objects.all()
        grad = GraduateCategory.objects.all()

        return Response({
            'undergraduate': UndergraduateCategorySerializer(undergrad, many=True).data,
            'graduate': GraduateCategorySerializer(grad, many=True).data
        })


class MajorListView(APIView):
    """获取所有二级门类"""

    def get(self, request):
        undergrad = UndergraduateMajor.objects.all()
        grad = GraduateMajor.objects.all()

        return Response({
            'undergraduate': UndergraduateMajorSerializer(undergrad, many=True).data,
            'graduate': GraduateMajorSerializer(grad, many=True).data
        })


class CategoryCorrelationView(APIView):
    """一级学科关联度操作"""

    def get(self, request):
        correlations = CategoryCorrelation.objects.all()
        return Response(CategoryCorrelationSerializer(correlations, many=True).data)

    def post(self, request):
        serializer = CategoryCorrelationSerializer(data=request.data)
        if serializer.is_valid():
            # 检查是否已存在关联
            correlation, created = CategoryCorrelation.objects.update_or_create(
                undergrad_category=serializer.validated_data['undergrad_category'],
                grad_category=serializer.validated_data['grad_category'],
                defaults={'level': serializer.validated_data['level']}
            )
            return Response(
                CategoryCorrelationSerializer(correlation).data,
                status=status.HTTP_201_CREATED if created else status.HTTP_200_OK
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MajorCorrelationView(APIView):
    """二级门类关联度操作"""

    def get(self, request):
        correlations = MajorCorrelation.objects.all()
        return Response(MajorCorrelationSerializer(correlations, many=True).data)

    def post(self, request):
        serializer = MajorCorrelationSerializer(data=request.data)
        if serializer.is_valid():
            correlation, created = MajorCorrelation.objects.update_or_create(
                undergrad_major=serializer.validated_data['undergrad_major'],
                grad_major=serializer.validated_data['grad_major'],
                defaults={'level': serializer.validated_data['level']}
            )
            return Response(
                MajorCorrelationSerializer(correlation).data,
                status=status.HTTP_201_CREATED if created else status.HTTP_200_OK
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ExportExcelView(View):
    """导出Excel"""

    def get(self, request):
        output = BytesIO()

        # 一级学科数据
        category_data = []
        for corr in CategoryCorrelation.objects.select_related('undergrad_category', 'grad_category'):
            category_data.append({
                '本科一级学科': corr.undergrad_category.name,
                '研究生一级学科': corr.grad_category.name,
                '关联度等级': corr.level
            })

        # 二级门类数据
        major_data = []
        for corr in MajorCorrelation.objects.select_related(
                'undergrad_major__category', 'grad_major__category'
        ):
            major_data.append({
                '本科一级学科': corr.undergrad_major.category.name,
                '本科二级门类': corr.undergrad_major.name,
                '研究生一级学科': corr.grad_major.category.name,
                '研究生二级门类': corr.grad_major.name,
                '关联度等级': corr.level
            })

        # 创建Excel
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            pd.DataFrame(category_data).to_excel(writer, sheet_name='一级学科关联度', index=False)
            pd.DataFrame(major_data).to_excel(writer, sheet_name='二级门类关联度', index=False)

        output.seek(0)
        response = HttpResponse(
            output,
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=专业关联度统计.xlsx'
        return response




class CategoryCorrelationsIndexView(TemplateView):
    template_name = 'app/category_correlations.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)


        return context