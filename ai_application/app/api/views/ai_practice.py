from app.api.api_dto.ai_practice import AIPracticePaperReqDto
from app.api.api_validators.ai_practice import (
    AIPracticePaperReqValidator, AIPracticePaperSubmitValidator, AIPracticePaperQuestionSubmitValidator
)
from app.errors import ParameterError
from app.models import STUserPaper, STUserPaperAnswer, STUserPaperQuestionAnswer
from app.services.shuati_app.answer_detail import get_ai_practice_answer_detail
from app.services.shuati_app.constants import StageChangeType, get_paper_stage_by_learning_stage, LEARNING_STAGE_LABELS
from app.services.shuati_app.paper_detail import get_ai_practice_paper_detail
from app.services.shuati_app.paper_submit import PaperSubmitClient
from app.services.shuati_app.stage_report_templates import answer_fail_report_template, answer_pass_report_template
from django_ext import base_view
from django_ext.response import make_response


class AIPracticePaperDetailView(base_view.BaseView):
    validator_class = AIPracticePaperReqValidator

    def post(self, request, *args, **kwargs):
        dto: AIPracticePaperReqDto = self.validate_request(request.data)
        data = get_ai_practice_paper_detail(dto.subject_id, dto.core_course_code, dto.user_id)
        return make_response(data)


class AIPracticeQuestionPaperSubmitView(base_view.BaseView):
    validator_class = AIPracticePaperQuestionSubmitValidator

    def post(self, request, *args, **kwargs):
        dto = self.validate_request(request.data)

        paper = STUserPaper.objects.filter(id=dto.paper_id, user_id=dto.user_id).first()
        if not paper:
            raise ParameterError(detail='试卷不存在')

        question_answer = PaperSubmitClient(paper).submit_question(dto)
        return make_response({
            'answer_id': question_answer.answer_id,
            'is_finished': question_answer.answer.is_finished,
            'answer_status': question_answer.answer_status,
        })


class AIPracticeSubjectiveReportView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        answer_id = request.data.get('answer_id')
        question_id = request.data.get('question_id')
        if not answer_id or not question_id:
            raise ParameterError(detail='参数错误')

        answer: STUserPaperQuestionAnswer = STUserPaperQuestionAnswer.objects.filter(
            is_deleted=False, answer_id=answer_id, question_id=question_id).first()
        if not answer:
            raise ParameterError(detail='答题记录不存在')

        status = 'ing'
        if answer.report_status == 'success':
            status = 'success'
        elif answer.report_status == 'fail' and answer.report_retry_count >= 3:
            status = 'fail'

        return make_response({
            'status': status,
            'report': answer.report,
            'score': answer.score_rate,
            'answer_status': 'right' if answer.score_rate >= 80 else 'wrong'
        })


class AIPracticePaperSubmitView(base_view.BaseView):
    validator_class = AIPracticePaperSubmitValidator

    def post(self, request, *args, **kwargs):
        dto = self.validate_request(request.data)

        paper = STUserPaper.objects.filter(id=dto.paper_id, user_id=dto.user_id).first()
        if not paper:
            raise ParameterError(detail='试卷不存在')

        paper_answer = PaperSubmitClient(paper).submit(dto)
        return make_response({'answer_id': paper_answer.id})


class AIPracticePaperAnswerView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        answer_id = request.data.get('answer_id')
        if not answer_id:
            raise ParameterError(detail='答题记录ID不能为空')

        data = get_ai_practice_answer_detail(answer_id)
        return make_response(data)


class AIPracticePaperReportView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        answer_id = request.data.get('answer_id')
        if not answer_id:
            raise ParameterError(detail='答题记录ID不能为空')

        answer: STUserPaperAnswer = STUserPaperAnswer.objects.filter(id=answer_id).first()
        if not answer:
            raise ParameterError(detail='答题记录不存在')

        status = 'ing'
        if answer.report_status == 'success':
            status = 'success'
        elif answer.report_status == 'fail' and answer.report_retry_count >= 3:
            status = 'fail'

        return make_response({
            'status': status,
            'content': answer.report,
        })


class AIPracticeStageChangeInfoView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        answer_id = request.data.get('answer_id')
        if not answer_id:
            raise ParameterError(detail='答题记录ID不能为空')

        answer: STUserPaperAnswer = STUserPaperAnswer.objects.filter(id=answer_id).first()
        if not answer:
            raise ParameterError(detail='答题记录不存在')

        user_round_analysis, _ = STUserRoundAnalysis.objects.get_or_create(
            subject_id=answer.paper.subject_id,
            core_course_code=answer.paper.core_course_code,
            user_id=answer.user_id
        )

        record = answer.stuserroundchangerecord_set.filter(
            is_deleted=False,
        ).first()
        if not record:
            return make_response({
                'is_test_stop': user_round_analysis.is_test_stop,
                'stop_reason': user_round_analysis.stop_reason,
                'stop_content': '',
                'is_stage_change': False,
                'stage_info': None,
            })

        change_type = record.change_type
        if user_round_analysis.is_test_stop:
            confirm_btn = '确认'
            if user_round_analysis.stop_reason == 2:
                # 获取用户薄弱知识点
                week_kg_names = list(STUserKnowledgeMasteryLevel.objects.filter(
                    user_id=answer.user_id,
                    subject_id=answer.paper.subject_id,
                    core_course_code=answer.paper.core_course_code
                ).order_by('accuracy').values_list('knowledge__name', flat=True)[:3])
                weak_points_display = '\n'.join([f'- {k}' for k in week_kg_names])
                stop_content = answer_fail_report_template.replace('{weak_points}', weak_points_display)
            else:
                stop_content = answer_pass_report_template
            stage_report = stop_content
        else:
            stop_content = ''
            stage_report = record.change_report or ''

            # 下一个周期刷题策略处理
            if record.change_type == StageChangeType.FAIL_NEXT_CYCLE:
                confirm_btn = '继续刷题'
                change_type = StageChangeType.DOWN
            elif record.change_type == StageChangeType.PASS_NEXT_CYCLE:
                confirm_btn = '重新刷题'
                change_type = StageChangeType.UP
            else:
                # current_stage = LEARNING_STAGE_LABELS.get(record.new_stage, '')
                # current_stage_str = str(current_stage)
                # if record.change_type == StageChangeType.STRENGTHEN:
                #     current_stage_str += '-强化'
                # confirm_btn = '开始' + current_stage_str
                confirm_btn = '确定'

        # 首次进该科目的学习，该参数为0，其余都唯一
        learn_stage_display = 1 if user_round_analysis.examined_rounds else 0
        return make_response({
            'is_test_stop': user_round_analysis.is_test_stop,
            'stop_reason': user_round_analysis.stop_reason,
            'stop_content': stop_content,
            'is_stage_change': True,
            'stage_info': {
                'change_type': change_type,
                'stage': learn_stage_display,
                'stage_report': stage_report,
                'confirm_btn': confirm_btn,
            }
        })


from app.api.shuati.extract_core_knowledge import ExtractCoreKnowledgeService
from app.models.shuati import STQuestion, STUserRoundAnalysis, STUserKnowledgeMasteryLevel
from django_ext import base_view
from django_ext.response import make_stream_response, make_response
from concurrent.futures import ThreadPoolExecutor
import threading


class ExtractKnowledgeView(base_view.BaseView):
    # validator_class = ExtractKnowledgeValidator

    def post(self, request, *args, **kwargs):
        # dto: ExtractKnowledgeDto = self.validate_request(request.data)

        questions = STQuestion.objects.filter(id__gte=254, id__lte=875)

        # 将查询结果转换为列表
        questions_list = list(questions)

        # 定义处理单个问题的函数
        def process_question(question_instance):
            try:
                # 调用 extract_core_knowledge 方法处理每个问题实例
                result = ExtractCoreKnowledgeService.extract_core_knowledge(question_instance)
                print(f"成功处理问题 ID: {question_instance.id}")
                return result
            except Exception as e:
                # 记录处理异常的问题
                print(f"处理问题 ID {question_instance.id} 时出错: {str(e)}")
                return None

        # 使用5个线程并发处理
        with ThreadPoolExecutor(max_workers=5) as executor:
            # 批量提交任务并执行
            executor.map(process_question, questions_list)

        return make_response()