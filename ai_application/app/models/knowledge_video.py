from django.db import models
from ._base import BaseModel
from .knowledge_simple import KnowledgeSimple

class KnowledgeVideo(BaseModel):
    name=models.ForeignKey(KnowledgeSimple,on_delete=models.CASCADE,verbose_name='对应知识点', null=True)
    knowledge_name=models.Char<PERSON>ield('知识点名称',max_length=20,blank=True)
    video=models.CharField('视频链接',max_length=200,blank=True)
    image=models.CharField('图片链接',max_length=200,blank=True)
    status = models.CharField('任务状态', max_length=20, default='not_start', 
                        choices=[
                            ('not_start', '未开始'),
                            ('processing', '校对中'),
                            ('success', '成功'),
                            ('fail', '失败')
                        ])
    fail_reason=models.CharField('失败原因',max_length=255,blank=True)
    core_course_name=models.CharField('二级学科名称',max_length=20,blank=True)

    def save(self, *args, **kwargs):
        # 自动从关联的KnowledgeSimple获取name字段值
        if self.name and self.name.name:
            self.knowledge_name = self.name.name
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = verbose_name_plural = "知识解析视频"
        db_table = 'ai_knowledge_video'