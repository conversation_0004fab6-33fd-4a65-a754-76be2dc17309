<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语阅读理解</title>
    {#<link href="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.16.9/katex.min.css" rel="stylesheet">#}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    {#<script src="https://cdn.bootcdn.net/ajax/libs/marked/13.0.2/marked.min.js"></script>#}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.6/marked.min.js"></script>
    {#<script src="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.16.9/katex.min.js"></script>#}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.js"></script>
    {#<script src="https://cdn.bootcdn.net/ajax/libs/KaTeX/0.16.9/contrib/auto-render.min.js"></script>#}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/contrib/auto-render.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 0 20px;
        }
        h1, h2, h3, h4, p {
            margin: 0;
            padding: 0;
        }
        ul, ol {
            margin: 0;
        }
        .container {
            max-width: 1500px;
            width: 100%;
        }
<!--        .main-container {-->
<!--            display: flex;-->
<!--        }-->
<!--        .left-container {-->
<!--            width: 25%;-->
<!--        }-->
<!--        .right-container {-->
<!--            width: 70%;-->
<!--        }-->

    /* 设置整个页面为 Flex 布局，并将内容垂直和水平居中 */
    .main-container {
        display: flex;
        justify-content: center; /* 水平居中 */
        align-items: center; /* 垂直居中 */
        height: 100vh; /* 使容器占满整个视口高度 */
        margin: 0; /* 去掉默认的外边距 */
    }

    /* 右侧容器样式 */
    .right-container {
        width: 1500px; /* 设置宽度 */
        padding: 20px; /* 内边距 */
        background-color: #f9f9f9; /* 背景颜色 */
        border: 1px solid #ddd; /* 边框 */
        border-radius: 8px; /* 圆角 */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 阴影效果 */
    }

    /* 预览调试框样式 */
    .preview-container {
        height: 550px; /* 设置固定高度 */
        overflow-y: auto; /* 如果内容超出高度，显示滚动条 */
        border: 1px solid #ccc; /* 边框 */
        margin-bottom: 15px; /* 底部间距 */
        padding: 10px; /* 内边距 */
        background-color: #fff; /* 背景颜色 */
        border-radius: 4px; /* 圆角 */
    }

        .prompt-button {
            max-width: 185px;
        }
        .custom-file-upload {
            display: inline-block;
            padding: 6px 12px;
            cursor: pointer;
            background: #4CAF50; /* 按钮背景颜色 */
            border: none;
            color: white; /* 文字颜色 */
            text-align: center;
            font-size: 14px; /* 字体大小 */
            margin: 4px 2px;
            opacity: 0.6;
            transition: 0.3s; /* 过渡效果 */
            border-radius: 4px; /* 圆角 */
        }
        .custom-file-upload:hover {
            opacity: 1; /* 鼠标悬停时的透明度 */
        }
        .custom-file-upload:active {
            transform: translateY(4px); /* 点击时的下压效果 */
        }
<!--        .preview-container {-->
<!--            height: 525px;-->
<!--            overflow-y: auto; /* 允许Y轴滚动 */-->
<!--            border: 1px solid #000; /* 可选，为了更清楚地看到div的边界 */-->
<!--            margin-bottom: 10px;-->
<!--        }-->
        .message-item {
            display: flex;
            border-bottom: 1px solid red;
            position: relative;
        }
        .message-user {
            padding: 5px;
            border-right: 1px solid red;
        }
        .message-content {
            padding-left: 5px;
            padding-top: 5px;
            width: 80%;
        }
        .message-tracing {
            position: absolute;
            border: 1px solid #333;
            right: 5px;
            top: 5px;
            font-size: 14px;
            cursor: pointer;
        }
        .message-image img{
            width: 150px;
        }
        .message-answer{
            white-space: pre-line;
        }
        label {
            font-weight: bold;
            margin-top: 15px;
        }
        textarea {
            padding: 10px;
            margin-top: 5px;
            margin-bottom: 5px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        #user-query {
            width: 95%;
            height: 50px;
            resize: none;
        }
        .code_content {
            margin-top: 15px;
            width: 95%;
            height: 280px;
            resize: none;
        }
        button {
            max-width: 180px;
            padding: 10px 20px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.cancel {
            background-color: #fff;
            border: 1px solid #333;
            color: #000;
        }
        button.success {
            background-color: green;
            border: 1px solid green;
        }
        button.cancel:hover {
            background-color: #eee;
        }
        .prompt_input {
            resize: vertical;
            height: 300px; /* 设置一个初始高度 */
            width: 600px;
        }
        /* 新增弹出层样式 */
        .modal {
            display: none;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ccc;
            z-index: 1000;
            width: 650px;
            height: 450px;
            overflow: hidden;
        }
        .modal_content {
            overflow-y: auto; /* 允许Y轴滚动 */
            height: 375px;
            box-sizing: border-box;
            padding: 10px 0;
        }
        pre {
            background-color: #eee;
            padding: 5px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        /* 遮罩层样式 */
        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
        .tracing_container p{
            white-space: pre-line;
            border-bottom: 1px solid red;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>

<h2 style="margin-top: 10px">英语阅读理解</h2><hr>
<div class="container">
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>奋力处理中...</p>
        </div>
    </div>

    <!-- 新增遮罩层 -->
    <div id="overlay"></div>

    <div id="message_tracing_modal" class="modal">
        <h2>消息日志</h2>
        <div class="modal_content tracing_container"></div>
        <button id="confirm_message_tracing_modal" type="button">确定</button>
    </div>

    <div id="app_optimization_modal" class="modal">
        <h2>代码优化</h2>
        <div class="modal_content app_optimization_container">
            <select name="code_lang">
                <option value="">选择语言</option>
                <option value="Python">Python</option>
                <option value="Java">Java</option>
                <option value="Javascript">Javascript</option>
                <option value="C">C</option>
                <option value="C++">C++</option>
            </select>
            <textarea class="code_content" name="code_content"></textarea><br>
        </div>
        <button id="confirm_app_optimization_modal" class="" type="button">确定</button>
        <button id="cancel_app_optimization_modal" class="cancel" type="button">取消</button>
    </div>

    <div class="main-container">
        <div class="right-container">
            <h4 style="margin-bottom: 10px">预览调试</h4>

            <div class="preview-container">
                <div class="message-list">
                </div>
            </div>

            <form id="chatForm" action="{% url 'english_reading_comprehension' %}">

                <label for="chat_waikan" style="margin-bottom: 5px">
                    <input type="checkbox" id="chat_waikan" name="chat_waikan" value="1">
                    外刊出题
                </label>

                 <label for="chat_waikan_again" style="margin-bottom: 5px">
                    <input type="checkbox" id="chat_waikan_again" name="chat_waikan_again" value="1" disabled>
                    外刊薄弱题专项练习
                </label>
                <textarea id="user-query" name="user_query" placeholder="请输入你的回答"></textarea>
                <button class="submit-btn">提交</button>
            </form>

        </div>
    </div>

</div>

<script>
    let temp_prompt = '';
    let conversation_id = '';
    let questionImage = null;
    const $chatForm = $('#chatForm');
    const $userQuery = $('#user-query');
    const $messageList = $('.message-list');
    const $chatWeakPointsCheckbox = $('#chat_waikan_again'); // 获取薄弱题复选框
    let suggestion_initial = ''; // 存储第一次提交的提升建议
    let suggestion_again = ''; // 存储第二次提交的提升建议
    const katexOptions = {
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        throwOnError: false
    }
<!--    // 初始化时禁用薄弱题复选框-->
<!--    $chatWeakPointsCheckbox.prop('disabled', true)-->
    $(document).ready(function () {
    // 初始化时禁用薄弱题复选框
    $chatWeakPointsCheckbox.prop('disabled', true);

    // 确保最多只能同时勾选一个复选框
    function ensureSingleCheckboxSelection() {
        const checkboxes = ['#chat_waikan', '#chat_waikan_again']; // 需要限制的复选框
        checkboxes.forEach((checkboxId) => {
            $(checkboxId).change(function () {
                if ($(this).is(':checked')) {
                    // 如果当前复选框被选中，则取消其他复选框的选中状态
                    checkboxes
                        .filter((id) => id !== checkboxId) // 排除当前复选框
                        .forEach((id) => $(id).prop('checked', false));
                }
            });
        });
    }

    // 调用函数以应用逻辑
    ensureSingleCheckboxSelection();
    // 处理外刊薄弱题专项练习复选框的逻辑
    $('#chat_waikan_again').change(function () {
        if ($(this).is(':checked')) {
            const confirmResult = confirm("是否开始进行外刊薄弱题型专项练习？");
            if (confirmResult) {
                // 用户点击了“是”，触发表单提交
                submit_chat();
            } else {
                // 用户点击了“否”，取消复选框的选中状态
                $(this).prop('checked', false);
            }
        }
    });
});

    $('.prompt-button').click(function () {
        const $modal = $(this).parent().find('.promptManagementModal')
        const $promptInput = $modal.find('textarea.prompt_input')
        if ($promptInput.length <= 0) {
            return
        }
        temp_prompt = $promptInput.val()
        $modal.show()
        $('#overlay').show();
    })
    $('.prompt-confirm').click(function() {
        const $modal = $(this).parent()
        temp_prompt = ''
        $modal.hide();
        $('#overlay').hide();
    });
    $('.prompt-cancel').click(function() {
        const $modal = $(this).parent()
        const $promptInput = $modal.find('textarea.prompt_input')
        $promptInput.val(temp_prompt)
        temp_prompt = ''
        $modal.hide();
        $('#overlay').hide();
    });
    $('.prompt-publish').click(function() {
        const $modal = $(this).parent()
        const $promptInput = $modal.find('textarea.prompt_input')
        const promptContent = $promptInput.val()
        const url = $(this).data('url')

        if (!promptContent) {
            alert('提示词不能为空')
            return
        }

        if (!confirm("确认要发布吗？")) {
            return
        }

        $.ajax({
            type: 'POST',
            url,
            contentType: 'application/json',
            data: JSON.stringify({
                prompt: promptContent,
            }),
            success: function(response) {
                alert('请求成功')
            },
            error: function(xhr, status, error) {
                alert('请求失败')
            }
        });
    })

    $messageList.on('click', '.message-tracing', function() {
        const messageId = $(this).parent().data('message-id')
        find_tracing(messageId, $(this))
        $('#message_tracing_modal').show();
        $('#overlay').show();
    })
    $('#confirm_message_tracing_modal').click(function() {
        $('#message_tracing_modal').hide();
        $('#overlay').hide();
    });

    function hide_chat_question() {
        $('.question-upload').hide()
        questionImage = null;
        $('#image').val('')
        $('#image-name').html('');
    }
    $('#chat_question').change(function () {
        $('#chat_code').prop('checked', false);
        $('#chat_waikan').prop('checked', false); // 确保其他选项被取消选中
        if ($(this).is(':checked')) {
            $('.question-upload').show()
        } else {
            hide_chat_question()
        }
    })
    $('#chat_code').change(function () {
        $('#chat_question').prop('checked', false);
        $('#chat_waikan').prop('checked', false); // 确保其他选项被取消选中
        hide_chat_question()
        if ($(this).is(':checked')) {
            $('#app_optimization_modal').show();
            $('#overlay').show();
        }
    })

    $('#chat_waikan_again').change(function () {
        $('#chat_question').prop('checked', false);
        $('#chat_waikan').prop('checked', false); // 确保其他选项被取消选中
        $('#chat_code').prop('disabled', true)
        hide_chat_question()
    })
$('#chat_waikan').change(function () {
    $('#chat_question').prop('checked', false);
    $('#chat_code').prop('checked', false); // 确保其他选项被取消选中
    hide_chat_question()
    if ($(this).is(':checked')) {
        if (confirm("是否开始进行外刊出题？")) {
            const headers = {
                "x-api-key": "ak_f288a20832ab417d",
                "x-signature": "91e9b4e95b65e5fc14013baeb7e5bfe252b1db58071c01b5b19b889fe85ddfdb",
                "timestamp": "**********",
                "nonce": "ld8h259m",
                "x-sign-debug": "1"
            };

            fetch("{% url 'english_question_bank' %}", {
                method: 'GET',
                headers: headers
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                    return;
                }
                const waikanDetail = data.data;
                console.log(waikanDetail); // 添加调试信息
                if (!waikanDetail || typeof waikanDetail !== 'object') {
                    alert('获取到的外刊出题详情格式不正确');
                    return;
                }
                const content = `${waikanDetail.article}\n${waikanDetail.option}\n\n**外刊来源**\n${waikanDetail.publications_source}`;


                if (typeof content !== 'string') {
                    alert('获取到的内容不是字符串');
                    return;
                }
                const messageItem = `
                    <div class="message-item">
                        <div class="message-user">系统 </div>
                        <div class="message-content">
                            <div class="message-answer">${marked.parse(content)}</div>
                        </div>
                    </div>
                `;
                $messageList.append(messageItem);
                $messageList.find('.message-answer').each(function() {
                    renderMathInElement(this, katexOptions);
                });

                // 将 waikanDetail 添加到全局变量中，以便在提交时使用
                window.waikanDetail = waikanDetail;
            })
            .catch(error => {
                console.error('Error fetching waikan question detail:', error);
                alert('获取外刊出题详情失败');
            });
        } else {
            $(this).prop('checked', false); // 如果用户点击“否”，取消选中复选框
        }
    }
});

$chatForm.submit(function(e) {
    e.preventDefault(); // 阻止表单的默认提交行为
    submit_chat();
})
// 定义全局变量，用于存储历史系统消息
window.systemMessages = [];

// 更新全局变量的函数
function updateSystemMessages() {
    const systemMessages = [];
    // 遍历 messageList，提取所有系统回复的消息内容
    $('.message-item').each(function () {
        const $message = $(this);
        const user = $message.find('.message-user').text().trim();
        if (user === '系统') {
            const content = $message.find('.message-answer').text().trim();
            systemMessages.push(content);
        }
    });

    // 根据复选框状态决定保留的消息数量
    if ($('#chat_waikan_again').is(':checked')) {
        // 薄弱题专项练习：保留两条历史系统消息
        window.systemMessages = systemMessages.slice(-3);
    } else if ($('#chat_waikan').is(':checked')) {
        // 外刊出题：只保留一条历史系统消息
        window.systemMessages = systemMessages.slice(-3);
    }

    console.log('Updated system messages:', window.systemMessages);
}

function submit_chat() {
    const formData = new FormData($chatForm[0]);
    let query = $userQuery.val();

    formData.append('conversation_id', conversation_id);

    const isQuestion = $('#chat_question').is(':checked')
    const isCode = $('#chat_code').is(':checked')
    const isWaikan = $('#chat_waikan').is(':checked') // 检查 chat_waikan 是否被选中
    const isWaikanAgain = $('#chat_waikan_again').is(':checked')

    formData.append('chat_pre_prompt', $('textarea[name="chat_pre_prompt"]').val());
    if (isQuestion) {
        formData.append('evaluate_ocr_text_completeness', $('textarea[name="evaluate_ocr_text_completeness"]').val());
        formData.append('compare_three_inputs', $('textarea[name="compare_three_inputs"]').val());
        formData.append('compare_user_input', $('textarea[name="compare_user_input"]').val());
        formData.append('process_comparison', $('textarea[name="process_comparison"]').val());
    }

    if (isCode) {
        const code_lang = $('select[name="code_lang"]').val()
        if (!code_lang) {
            alert('请选择代码语言')
            return
        }
        if (!query) {
            alert('请输入代码内容')
            return
        }
        formData.append('code_lang', code_lang);
        formData.append('code_optimization', $('textarea[name="code_optimization"]').val());

        $('#app_optimization_modal').hide();
        $('#overlay').hide();
        $('#chat_code').prop('checked', false);
        $('textarea[name="code_content"]').val('')

        query = `<pre>${query}</pre>`;
    }

    if (isWaikan) {
        formData.append('chat_waikan', '1'); // 添加 chat_waikan 到 formData
        // 添加用户输入的答案
        formData.append('user_answer', query);
        // 添加 waikanDetail.answer 和 waikanDetail.analysis
        if (window.waikanDetail) {
            formData.append('waikan_answer', window.waikanDetail.answer);
            formData.append('waikan_analysis', window.waikanDetail.analysis);
            formData.append('waikan_article', window.waikanDetail.article);
        }
    }

    if (isWaikanAgain) {
    formData.append('chat_waikan_again', '1');
    // 添加用户输入的答案
    formData.append('user_answer', query);
    // 添加 waikanDetail.answer 和 waikanDetail.analysis
    if (window.waikanDetail) {
        formData.append('waikan_answer', window.waikanDetail.answer);
        formData.append('waikan_analysis', window.waikanDetail.analysis);
        formData.append('waikan_article', window.waikanDetail.article);
        formData.append('waikan_option', window.waikanDetail.option);
        formData.append('suggestion', window.suggestion);
         if (window.systemMessages && window.systemMessages.length > 0) {
                formData.append('system_messages', JSON.stringify(window.systemMessages));
            }

    }
}
<!--    console.log("sssssss")-->
<!--    return-->

    $userQuery.val('')
    $('#image').val('')
    $('#image-name').html('');
    if (questionImage) {
        $messageList.append(
            `<div class="message-item">
                <div class="message-user">用户 </div>
                <div class="message-content">
                    <div class="message-image"><img src="${questionImage}"  alt=""/></div>
                    <div class="message-query">${query}</div>
                </div>
            </div>`
        )
    } else {
        $messageList.append(
            `<div class="message-item">
                <div class="message-user">用户 </div>
                <div class="message-content">
                    <div class="message-query">${query}</div>
                </div>
            </div>`
        )
    }

    $('#loadingOverlay').css('display', 'flex');
    let message_id = null;
    let $messageView = null;
    const url = "{% url 'english_reading_comprehension' %}";
    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        $('#loadingOverlay').css('display', 'none');
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.body;
    })
    .then(stream => {
        $('#loadingOverlay').css('display', 'none');
        let streamComplete = false;
        const reader = stream.getReader();
        const decoder = new TextDecoder(); // 创建TextDecoder实例
        let result_text = '';
        let suggestion = '';
        function readChunk() {
            reader.read().then(({ done, value }) => {
                if (done) {
                    streamComplete = true;

                    updateSystemMessages();
                    console.log('Stream complete, returning collected data');
                    // 在流结束时，将 suggestion 存储为全局变量
                    window.suggestion = suggestion;
                    // 启用薄弱题复选框
                    $chatWeakPointsCheckbox.prop('disabled', false);
                    return;
                }
                const textChunkStr = decoder.decode(value, { stream: true });

                // 根据 \n\n 对流数据分块
                const chunkArr = textChunkStr.split("\n\n");
                for (let i = 0; i < chunkArr.length; i++) {
                    const textChunk = chunkArr[i];

                    if (textChunk.startsWith("data: ")) {
                        // 去除前缀"data: "
                        const json_str = textChunk.slice(6);
                        console.log('json_str', json_str)
                        try {
                            // 将匹配到的字符串转换为JSON对象
                            const jsonData = JSON.parse(json_str);
                            conversation_id = jsonData.conversation_id
                            if (!message_id) {
                                message_id = jsonData.message_id
                                $messageList.append(
                                    `<div id="message-item-${message_id}" class="message-item" data-message-id="${message_id}">
                                        <div class="message-user">系统 </div>
                                        <div class="message-content">
                                            <div class="message-answer"></div>
                                        </div>
                                        <div class="message-tracing">查看日志</div>
                                    </div>`
                                )
                                $messageView = $(`#message-item-${message_id}`);
                            }
                            if (jsonData.answer && $messageView) {
                                result_text += jsonData.answer;
                                $messageView.find('.message-answer').html(marked.parse(result_text));
                                // 在流结束时，将 suggestion 存储为全局变量
                                if (isWaikanAgain) {
                                    suggestion = result_text; // 存储第二次提交的提升建议
                                } else {
                                    // 解析提升建议部分的内容
                                    const suggestionStart = result_text.indexOf('## 提升建议');
                                    if (suggestionStart !== -1) {
                                        suggestion = result_text.substring(suggestionStart).trim();
                                    }
                                }
<!--                                // 解析提升建议部分的内容-->
<!--                                const suggestionStart = result_text.indexOf('## 提升建议');-->
<!--                                if (suggestionStart !== -1) {-->
<!--                                    suggestion = result_text.substring(suggestionStart).trim();-->
<!--                                }-->

                                $messageView.find('.message-answer').each(function() {
                                    renderMathInElement(this, katexOptions)
                                })
                            } else if (jsonData.err) {
                                alert(jsonData.err)
                            }
                        } catch (error) {
                            console.error('Error parsing JSON', json_str, error);
                        }
                    }
                }
                readChunk();
            }).catch(error => {
                console.error('Error collecting stream data', error);
            });
        }
        readChunk();
    })
    .catch(error => {
        $('#loadingOverlay').css('display', 'none');
        console.error('Error:', error);
    });


}
$userQuery.keydown(function(event) {
    if (event.key === 'Enter' && !event.shiftKey) { // 检测是否按下了回车键且不是Shift+Enter
        event.preventDefault(); // 阻止默认的换行行为
        submit_chat(); // 调用提交函数
    }
});

    function find_tracing(message_id) {
        const $el = $('.tracing_container')
        $el.html('')
        $.ajax({
            type: 'get',
            url: "{% url 'message_tracing' %}",
            data: {message_id},
            success: function(response) {
                $('#loadingOverlay').css('display', 'none');
                const results = response.data;

                results.tracing.forEach(function(traceDetail) {
                    const detailElement = document.createElement('p');
                    detailElement.textContent = traceDetail;
                    $el.append(detailElement);
                });
            },
            error: function(xhr, status, error) {
                alert('请求失败');
                $('#loadingOverlay').css('display', 'none');
            }
        });
    }

</script>




</body>
</html>


