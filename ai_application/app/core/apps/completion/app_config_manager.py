from app.constants.app import AppMode
from app.models import AppModelConfig, App
from django_ext.base_dto_model import MyBaseModel


class CompletionAppConfig(MyBaseModel):
    pass


class CompletionAppConfigManager:
    @classmethod
    def get_app_config(
            cls,
            app_model: App,
            app_model_config: AppModelConfig,
            override_config_dict: dict | None = None
    ) -> CompletionAppConfig:
        app_mode = AppMode.value_of(app_model.mode)


        app_config = CompletionAppConfig(
            app_id=app_model.id,
            app_mode=app_mode,
            app_model_config_id=app_model_config.id,
            model=ModelConfigManager.convert(config=config_dict),
            prompt_template=PromptTemplateConfigManager.convert(config=config_dict),
            sensitive_word_avoidance=SensitiveWordAvoidanceConfigManager.convert(config=config_dict),
            dataset=DatasetConfigManager.convert(config=config_dict),
            additional_features=cls.convert_features(config_dict, app_mode),
        )
