import json
import re


def extract_code_from_markdown(markdown_text):
    # 定义正则表达式模式，匹配 ``` 或 ```json 开头和结尾的代码块
    pattern = r"```(?:json)?(.*?)```"
    # 使用 re.DOTALL 参数，使 . 匹配包括换行符在内的所有字符
    matches = re.findall(pattern, markdown_text, re.DOTALL)
    if not matches:
        return None

    code_str = matches[0]
    # 去除无效的转义符号
    code_str = code_str.replace('\_', '_').replace("\"[", "[").replace("]\"", "]")
    return code_str


def safe_parse_json(json_string):
    try:
        # 先尝试解析 JSON，如果失败，则进行转义处理
        return json.loads(json_string)
    except json.JSONDecodeError:
        # 处理内部未转义的引号 "（替换为 `\"`）
        fixed_json_string = json_string.replace('"', '\\"')
        print(fixed_json_string)
        return json.loads(fixed_json_string)
