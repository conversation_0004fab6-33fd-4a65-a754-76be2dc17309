import re

from app.core.features.base_feature import BaseFeature
from app.core.features.knowledge_keyword_extract import KnowledgeKeywordExtractFeature
from app.core.rag.splitter.markdown_splitter import MarkdownHeaderTextSplitter
from app.models import CourseVideoContent


class CourseNoteSplitFeature(BaseFeature):

    def __init__(self, lecture_content):
        # 去除讲义内的图片，超链接标签内容
        doc_content = self.clean_course_notes(lecture_content)
        md_documents = MarkdownHeaderTextSplitter(
            headers_to_split_on=[
                ("##", "h2"),
            ],
            strip_headers=False
        ).split_text(doc_content)

        self.kw_extract_feature = KnowledgeKeywordExtractFeature()

        doc_with_kw = []
        for idx, doc in enumerate(md_documents):
            title = doc.metadata.get('h2')

            # 过滤 考纲要求,知识导图,习题讲解
            if '考纲要求' in title or '知识导图' in title or '习题详解' in title:
                continue

            # 如果该段落只有标题，则忽略之
            if len(doc.page_content) - len(title) < 10:
                continue

            # if doc.metadata.get('h3'):
            #     title = doc.metadata.get('h3')
            doc_keywords = self.kw_extract_feature.run(doc.page_content)
            doc_with_kw.append({
                'title': title,
                'content': doc.page_content,
                'keywords': doc_keywords,
            })
        self.doc_with_kw = doc_with_kw
        self.default_return = md_documents[0].page_content if md_documents else ''

    def run(self, video_content: CourseVideoContent):
        if not self.doc_with_kw:
            return ''

        # 判断视频课节标题是否与讲义分块的内容一致
        for doc_info in self.doc_with_kw:
            doc_title = doc_info['title']
            if doc_title == video_content.name:
                return doc_info['content']

        video_keywords_map = {}
        title_keywords = self.kw_extract_feature.run(video_content.name)
        title_keywords = [i[0] for i in title_keywords]
        # 初始化title权重
        title_weight = 1 / len(title_keywords) if title_keywords else 0
        for tk in title_keywords:
            video_keywords_map[tk] = title_weight

        video_keywords = video_content.coursevideokeyword_set.all()
        for vk in video_keywords:
            if vk.keyword in video_keywords_map:
                video_keywords_map[vk.keyword] += vk.weight
            else:
                video_keywords_map[vk.keyword] = vk.weight

        if not video_keywords_map:
            return self.default_return

        score_list = []
        for idx, doc_info in enumerate(self.doc_with_kw):
            doc_keywords = doc_info['keywords']

            doc_score = 0
            for keyword, w in doc_keywords:
                if keyword not in video_keywords_map:
                    continue
                doc_score += video_keywords_map[keyword] * w
            score_list.append(doc_score)

        max_val = max(score_list)
        if max_val == 0:
            return self.default_return

        max_index = score_list.index(max_val)

        return self.doc_with_kw[max_index]['content']

    def clean_course_notes(self, notes):
        # 使用正则表达式去除图片标签及其内容
        notes = re.sub(r'<img[^>]*>', '', notes)
        # 使用正则表达式去除超链接标签及其内容
        notes = re.sub(r'<a[^>]*>.*?</a>', '', notes)
        # 替换 Markdown 中的图片
        notes = re.sub(r'!\[.*?\]\(.*?\)', '', notes)
        # 替换 Markdown 中的超链接
        notes = re.sub(r'\[.*?\]\(.*?\)', '', notes)
        # 替换br为换行符
        notes = re.sub(r'<br\s*/?>', '\n', notes)
        return notes
