from rest_framework import serializers

from django_ext.base_serializer import MyBaseSerializer


class BaseValidator(MyBaseSerializer):
    dto_class = None

    @classmethod
    def _get_validated_data(cls, req_data):
        ser = cls(data=req_data)
        ser.is_valid(raise_exception=True)
        return ser.validated_data

    @classmethod
    def build_dict(cls, req_data):
        return cls._get_validated_data(req_data)

    @classmethod
    def build_dto(cls, req_data: dict):
        if not cls.dto_class:
            raise serializers.ValidationError('dto_class cannot be null')
        return cls.dto_class(**cls.build_dict(req_data))
