<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 智能讲义审校工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.6/marked.min.js"></script>
    <!-- 添加图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 添加Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% load static %}
    <!-- <link rel="stylesheet" href="{% static 'css/document_proofreader.css' %}"> -->
     <style>
        :root {
    /* 现代化色彩系统 */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --success-color: #22c55e;
    --info-color: #3b82f6;
    
    /* 中性色彩 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 语义化颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --border-color: var(--gray-200);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* 动画 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--gray-50) 0%, #e0e7ff 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem; /* 进一步减少容器内边距 */
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    animation: fadeInUp 0.8s ease-out;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 1rem; /* 进一步减少头部下边距 */
}

.header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgb(0 0 0 / 0.1);
}

.header .subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* 上传区域优化 */
.upload-section {
    background: var(--bg-primary);
    border-radius: 0.75rem;
    padding: 1rem; /* 进一步减少上传区域内边距 */
    margin-bottom: 1rem; /* 减少下边距 */
    box-shadow: var(--shadow-md);
    border: 2px dashed var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.upload-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.upload-section:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.upload-section.dragover {
    border-color: var(--primary-color);
    background: #f0f4ff;
}

.upload-section.dragover::after {
    content: '松开鼠标完成上传';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    animation: pulse 1s infinite;
}

.upload-content {
    text-align: center;
}

.upload-icon {
    font-size: 1.5rem; /* 减小图标大小 */
    color: var(--primary-color);
    margin-bottom: 0.5rem; /* 减少下边距 */
}

.upload-section h2 {
    font-size: 1.1rem; /* 减小标题字体大小 */
    font-weight: 600;
    margin-bottom: 0.5rem; /* 减少下边距 */
    color: var(--text-primary);
}

.upload-description {
    color: var(--text-secondary);
    margin-bottom: 1rem; /* 减少下边距 */
    font-size: 0.85rem; /* 减小描述文字大小 */
    line-height: 1.4;
}

.file-input {
    display: none;
}

.upload-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.upload-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.upload-btn:active {
    transform: translateY(0);
}

.start-btn {
    background: linear-gradient(135deg, var(--secondary-color), #059669);
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    margin-left: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.start-btn:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.start-btn:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 按钮组样式 */
.button-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.upload-actions {
    text-align: center;
}

/* 异步处理选项样式 */
.async-options {
    background: var(--gray-50);
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    margin-top: 1rem;
}

.async-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.async-checkbox input[type="checkbox"] {
    display: none;
}

.async-checkbox .checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-right: 0.5rem;
    position: relative;
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.async-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.async-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.async-checkbox .label-text {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 500;
    color: var(--text-primary);
}

.async-checkbox .label-text i {
    color: var(--primary-color);
}

.async-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

.async-description a {
    color: var(--primary-color);
    text-decoration: none;
}

.async-description a:hover {
    text-decoration: underline;
}

/* 邮箱通知样式 */
.email-notification {
    margin-top: 0.8rem;
    padding: 0.8rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
}

.email-input-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.email-input-group i {
    color: var(--primary-color);
    font-size: 0.9rem;
    min-width: 16px;
}

.email-input {
    flex: 1;
    padding: 0.6rem 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 0.4rem;
    font-size: 0.9rem;
    transition: all var(--transition-fast);
    background: white;
}

.email-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.email-input:invalid {
    border-color: var(--danger-color);
}

.email-required-tip {
    color: var(--danger-color);
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

.email-description {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.email-description i {
    color: var(--info-color);
    font-size: 0.7rem;
}

/* 当未启用异步时隐藏邮箱输入框 */
.email-notification.hidden {
    display: none;
}

.file-info {
    margin-top: 1rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: 0.5rem;
    display: none;
}

.file-info.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.file-name {
    font-size: 0.875rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 加载动画优化 */
.loading {
    display: none;
    text-align: center;
    margin: 3rem 0;
    padding: 2rem;
    background: var(--bg-primary);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    animation: fadeInUp 0.5s ease-out;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite, glow 2s ease-in-out infinite;
    margin: 0 auto 1rem;
}

.loading p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* 结果区域优化 */
.result-section {
    flex-grow: 1;
    background: var(--bg-primary);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-top: 0.5rem; /* 减少上边距 */
    animation: fadeInUp 0.8s ease-out;
    /* 确保结果区域能够充满剩余空间 */
    height: calc(100vh - 200px); /* 进一步优化高度计算，增加预览高度 */
}

.result-tabs {
    display: flex;
    background: var(--gray-50);
    border-bottom: 1px solid var(--border-color);
    position: relative;
    align-items: center;
    justify-content: space-between;
}

.tabs-container {
    display: flex;
}

.controls-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-left: auto;
    padding-right: 1rem;
}

.edit-controls {
    display: flex;
    gap: 0.5rem;
    margin-right: 1rem;
}

.edit-btn, .accept-all-btn, .save-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: var(--shadow-sm);
}

.edit-btn {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: white;
}

.accept-all-btn {
    background: linear-gradient(135deg, var(--success-color), #16a34a);
    color: white;
    display: none;
}

.save-btn {
    background: linear-gradient(135deg, var(--info-color), #2563eb);
    color: white;
    display: none;
}

.edit-btn:hover, .accept-all-btn:hover, .save-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.tab {
    padding: 1rem 1.5rem;
    cursor: pointer;
    background: transparent;
    border: none;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform var(--transition-fast);
}

.tab.active {
    color: var(--primary-color);
    background: var(--bg-primary);
}

.tab.active::after {
    transform: scaleX(1);
}

.tab.disabled {
    color: var(--gray-400);
    cursor: not-allowed;
    opacity: 0.6;
}

.tab:not(.disabled):hover {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.tab-content {
    display: none;
    padding: 0;
    height: calc(100% - 60px); /* 减去标签栏高度，增加内容高度 */
    overflow: hidden;
}

.tab-content.active {
    display: block;
}

.result-container {
    display: flex;
    gap: 1.5rem;
    height: 100%;
}

/* 文本区域优化 */
.labeled-text, .markdown-preview {
    flex: 3;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    overflow-y: auto;
    white-space: pre-wrap;
    line-height: 1.8;
    font-size: 1rem;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    /* 修复滚轮问题 - 阻止事件冒泡 */
    overscroll-behavior: contain;
}

.labeled-text:hover, .markdown-preview:hover {
    box-shadow: var(--shadow-md);
}

.labeled-text.edit-mode {
    background: #fffbf0;
    border-color: var(--warning-color);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/* 错误列表优化 */
.error-list {
    flex: 1;
    background: var(--gray-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
    overflow-y: auto;
    max-height: 100%;
    /* 修复滚轮问题 - 阻止事件冒泡 */
    overscroll-behavior: contain;
}

.error-list h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 错误列表默认状态 */
.error-list-empty {
    text-align: center;
    padding: 2rem 1rem;
    color: var(--text-secondary);
    animation: fadeInUp 0.6s ease-out;
}

.error-list-empty i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
    display: block;
}

.error-list-empty h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.error-list-empty p {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.error-list-empty .tip {
    font-size: 0.8rem;
    color: var(--gray-500);
    font-style: italic;
}

.error-item {
    background: var(--bg-primary);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    cursor: pointer; /* 添加手型光标，表示可点击 */
}

.error-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--danger-color);
}

.error-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color); /* 悬停时边框变色 */
}

.error-item.highlight {
    background: #fff7ed;
    border-color: var(--warning-color);
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* 错误项点击提示 */
.error-item::after {
    content: '点击跳转到文档位置';
    position: absolute;
    bottom: 0.5rem;
    right: 1rem;
    font-size: 0.7rem;
    color: var(--gray-500);
    opacity: 0;
    transition: opacity var(--transition-fast);
    pointer-events: none;
}

.error-item:hover::after {
    opacity: 1;
}

.delete-suggestion-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    cursor: pointer;
    opacity: 0;
    transition: all var(--transition-fast);
}

.error-item:hover .delete-suggestion-btn {
    opacity: 1;
}

.delete-suggestion-btn:hover {
    background: #dc2626;
}

/* 非编辑模式下隐藏删除建议按钮 */
.error-list:not(.edit-mode) .delete-suggestion-btn {
    display: none !important;
}

/* 编辑模式下显示删除建议按钮 */
.error-list.edit-mode .delete-suggestion-btn {
    display: block;
}

/* 加入词库按钮样式 */
.add-to-dictionary-btn {
    position: absolute;
    top: 0.5rem;
    right: 4rem; /* 放在删除按钮左边 */
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    cursor: pointer;
    opacity: 0;
    transition: all var(--transition-fast);
}

.error-item:hover .add-to-dictionary-btn {
    opacity: 1;
}

.add-to-dictionary-btn:hover {
    background: #059669;
}

/* 在编辑模式下显示词库按钮 */
.error-list.edit-mode .add-to-dictionary-btn {
    display: block;
}

/* 非编辑模式下隐藏词库按钮 */
.error-list:not(.edit-mode) .add-to-dictionary-btn {
    display: none !important;
}

.error-id {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.error-text {
    color: var(--danger-color);
    text-decoration: line-through;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.error-suggestion {
    color: var(--success-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.error-reason {
    color: var(--text-secondary);
    font-style: italic;
    font-size: 0.85rem;
}

/* 高亮内容优化 */
.highlighted-content {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: var(--primary-dark);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid transparent;
}

.highlighted-content:hover {
    background: linear-gradient(135deg, #bfdbfe, #93c5fd);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.non-editable-suggestion {
    background: linear-gradient(135deg, #f0f8ff, #e0f2fe);
    border: 1px dashed var(--info-color);
    padding: 0.25rem 0.5rem;
    margin: 0 0.125rem;
    border-radius: 0.375rem;
    transition: all var(--transition-fast);
}

.non-editable-suggestion:hover {
    background: linear-gradient(135deg, #e0f2fe, #bae6fd);
    border-color: var(--primary-color);
}

/* 建议选择浮窗优化 */
.suggestion-popup {
    display: none;
    position: absolute;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    min-width: 180px;
    overflow: hidden;
    animation: popupSlide 0.2s ease-out;
}

.suggestion-popup button {
    display: block;
    width: 100%;
    padding: 0.875rem 1rem;
    border: none;
    background: transparent;
    cursor: pointer;
    text-align: left;
    transition: all var(--transition-fast);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.suggestion-popup button:first-child {
    color: var(--success-color);
}

.suggestion-popup button:last-child {
    color: var(--danger-color);
}

.suggestion-popup button:hover {
    background: var(--gray-50);
}

/* 下载按钮优化 - 移到标签区域 */
.download-btn {
    background: linear-gradient(135deg, var(--secondary-color), #059669);
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    order: -1;
    margin-right: 0.5rem;
}

.download-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
    color: white;
}

.download-btn.show {
    display: flex;
    animation: fadeInUp 0.5s ease-out;
}

/* 动画定义 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes popupSlide {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.6);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .upload-section {
        padding: 2rem 1rem;
    }
    
    .result-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .error-list {
        flex: none;
        max-height: 300px;
    }
    
    .edit-controls {
        position: static;
        transform: none;
        justify-content: center;
        margin-top: 1rem;
        padding: 1rem;
        background: var(--gray-50);
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 提示工具样式 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-800);
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-fast);
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 成功状态样式 */
.success-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
    animation: fadeInUp 0.6s ease-out;
}

.success-state i {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 1rem;
    display: block;
    animation: pulse 2s infinite;
}

.success-state p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* 处理中状态 */
.processing {
    pointer-events: none;
    opacity: 0.7;
}

/* 错误状态样式 */
.error-state {
    color: var(--danger-color);
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
}

/* 页面加载动画 */
.container {
    animation: fadeInUp 0.8s ease-out;
}

/* 结果区域进入动画 */
.result-section {
    animation: fadeInUp 0.8s ease-out;
}

/* 移除下载引导提示 */
.download-guide {
    display: none;
}

/* 审校后的讲义页面优化 - 占满整个页面 */
.tab-content {
    display: none;
    padding: 0;
    height: calc(100% - 60px); /* 减去标签栏高度，增加内容高度 */
    overflow: hidden;
}

.tab-content.active {
    display: block;
}

/* 修改建议页面保持原布局 */
#labeledTab {
    padding: 2rem;
    height: calc(100% - 60px); /* 减去标签栏高度，增加内容高度 */
    overflow: hidden;
}

/* 审校后的讲义全屏显示 */
#correctedTab {
    padding: 0;
    height: calc(100% - 60px); /* 减去标签栏高度，增加内容高度 */
    overflow: auto;
}

#correctedTab .markdown-preview {
    padding: 2rem;
    height: 100%;
    overflow-y: auto;
    border: none;
    border-radius: 0;
    box-shadow: none;
    background: var(--bg-primary);
    font-size: 1.1rem;
    line-height: 1.8;
    max-width: none;
}

/* 下载按钮组样式 */
.download-controls {
    display: none;
    align-items: center;
    gap: 0.5rem;
}

.download-controls.show {
    display: flex;
    animation: fadeInUp 0.5s ease-out;
}

/* 词库管理按钮样式 */
.dictionary-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dictionary-controls .download-btn {
    background: linear-gradient(135deg, var(--secondary-color), #059669);
}

.dictionary-controls .download-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
}

.download-btn-word {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

.download-btn-word:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

/* 编辑模式下禁用下载按钮的样式 */
.download-controls.disabled .download-btn {
    background: var(--gray-300);
    cursor: not-allowed;
    opacity: 0.6;
    pointer-events: none;
}

/* 流式处理相关样式 */
.streaming-progress {
    display: none;
    background: var(--bg-primary);
    border-radius: 1rem;
    padding: 2rem;
    margin: 1rem 0;
    box-shadow: var(--shadow-md);
    animation: fadeInUp 0.5s ease-out;
}

.streaming-progress.show {
    display: block;
}

.progress-bar-container {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.streaming-status {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 1rem;
    animation: pulse 2s infinite;
}

.batch-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.batch-count {
    font-weight: 600;
}

.eta-display {
    font-style: italic;
}

/* 实时更新动画 */
.content-updating {
    position: relative;
    overflow: hidden;
}

.content-updating::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 流式模式下的结果区域 */
.streaming-mode .result-section {
    opacity: 1;
}

.streaming-mode .tab.disabled {
    pointer-events: none;
    opacity: 0.6;
}

.streaming-mode .edit-controls {
    pointer-events: none;
    opacity: 0.6;
}

.streaming-mode .download-controls {
    pointer-events: none;
    opacity: 0.6;
}

/* 底部状态提示 */
.bottom-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    display: none;
    animation: slideInUp 0.3s ease-out;
}

.bottom-status.show {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bottom-status i {
    animation: spin 1s linear infinite;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 控件禁用状态样式 */
.upload-btn:disabled,
.start-btn:disabled {
    background: var(--gray-300) !important;
    color: var(--gray-500) !important;
    cursor: not-allowed !important;
    opacity: 0.6;
    transform: none !important;
    box-shadow: none !important;
}

.upload-section.disabled {
    pointer-events: none;
    opacity: 0.6;
    background: var(--gray-100);
    border-color: var(--gray-300);
}

.upload-section.disabled::after {
    content: '校对进行中，请稍候...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-500);
    z-index: 10;
}

.select-wrapper {
    position: relative;
}

.select-wrapper::after {
    content: '\25BC';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    pointer-events: none;
    color: #909090;
    font-size: 0.8em;
}

.subject-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 0.5rem 2rem 0.5rem 1rem;
    font-size: 1rem;
    color: #333;
    cursor: pointer;
}

.subject-select:focus {
    border-color: #80bfff;
    outline: none;
}

.subject-hint {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: #666;
}

/* 学科选择框样式优化 */
.subject-selection {
    margin-top: 1rem;
}

.subject-selection label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.select-wrapper {
    position: relative;
    width: 100%;
}

.subject-select {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    background: var(--bg-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    box-shadow: var(--shadow-sm);
}

.subject-select:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.subject-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.subject-select:disabled {
    background: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
    border-color: var(--gray-300);
    box-shadow: none;
}

.select-arrow {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
    transition: all var(--transition-fast);
    font-size: 0.75rem;
}

.subject-select:focus + .select-arrow {
    color: var(--primary-color);
    transform: translateY(-50%) rotate(180deg);
}

.subject-hint {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
    line-height: 1.4;
}

.subject-hint i {
    color: var(--warning-color);
    font-size: 0.7rem;
}

/* 历史记录样式 */
.main-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 1.5rem;
    align-items: start;
}

.history-section {
    background: var(--bg-primary);
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    height: fit-content;
    max-height: none; /* 移除最大高度限制 */
    display: flex;
    flex-direction: column;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.history-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.history-header h3 i {
    color: var(--primary-color);
}

.refresh-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
    transform: rotate(180deg);
}

.history-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 200px; /* 设置最小高度 */
    max-height: 300px; /* 设置最大高度，避免过高 */
}

.history-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--text-secondary);
    gap: 0.5rem;
}

.history-loading i {
    font-size: 1.5rem;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

.history-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    color: var(--text-secondary);
    text-align: center;
}

.history-empty i {
    font-size: 2rem;
    color: var(--gray-400);
    margin-bottom: 0.5rem;
}

.history-empty p {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.history-empty small {
    font-size: 0.8rem;
    opacity: 0.8;
}

.history-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 0.25rem;
}

.history-item {
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-primary);
}

.history-item:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-item-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.history-item-icon {
    color: var(--primary-color);
    font-size: 0.9rem;
    flex-shrink: 0;
}

.history-item-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.85rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

.history-item-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.history-item-time i {
    font-size: 0.7rem;
}

.history-pagination {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    gap: 0.25rem;
    flex-shrink: 0; /* 防止分页区域被压缩 */
}

.history-pagination button {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all var(--transition-fast);
}

.history-pagination button:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.history-pagination button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.history-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-content-wrapper {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .history-section {
        order: -1;
        max-height: 300px;
    }
    
    .history-header h3 {
        font-size: 0.9rem;
    }
    
    .history-item {
        padding: 0.5rem;
    }
    
    .history-item-name {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .main-content-wrapper {
        gap: 0.75rem;
    }
    
    .history-section {
        padding: 0.75rem;
        max-height: 250px;
    }
    
    .history-header {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
    }
    
    .history-item {
        padding: 0.5rem 0.75rem;
        margin-bottom: 0.375rem;
    }
}

/* 历史记录滚动条样式 */
.history-list::-webkit-scrollbar {
    width: 4px;
}

.history-list::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 2px;
}

.history-list::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 2px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}


     </style>
    <!-- <script src="{% static 'js/document_proofreader.js' %}"></script> -->
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-magic"></i> AI 智能讲义审校工具</h1>
            <p class="subtitle">
                <i class="fas fa-robot"></i> 
                基于人工智能的智能校对系统，帮您快速发现并修正文档中的语法错误、错别字和表达问题
            </p>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content-wrapper">
            <!-- 左侧上传区域 -->
        <div class="upload-section" id="uploadSection">
            <div class="upload-content">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <h2>上传讲义进行智能审校</h2>
                <p class="upload-description">
                    支持 Markdown(.md)、Word(.docx)<br>
                    <span style="color: #aaa; font-size: 0.8rem;">推荐使用标准 Markdown 语法或 Word 文档格式</span>
                </p>
                <div class="upload-actions">
                    <input type="file" id="fileInput" class="file-input" accept=".md,.docx,.doc">
                    <div class="button-group">
                        <button class="upload-btn tooltip" onclick="document.getElementById('fileInput').click()" data-tooltip="选择 .md、.docx 或 .doc 格式文件">
                            <i class="fas fa-folder-open"></i>
                            选择文件
                        </button>
                        <button id="startBtn" class="start-btn tooltip" disabled data-tooltip="开始AI智能审校">
                            <i class="fas fa-play"></i>
                            开始审校
                        </button>
                    </div>
                        
                        <!-- 异步处理选项 -->
                        <div class="async-options" style="margin-top: 1rem;">
                            <label class="async-checkbox">
                                <input type="checkbox" id="enableAsync" checked>
                                <span class="checkmark"></span>
                                <span class="label-text">
                                    <i class="fas fa-background"></i>
                                    启用后台处理（关闭页面也会继续校对）
                                </span>
                            </label>
                            
                            <!-- 邮箱通知输入框 -->
                            <div class="email-notification" id="emailNotificationContainer" style="margin-top: 0.8rem;">
                                <div class="email-input-group">
                                    <i class="fas fa-envelope"></i>
                                    <input type="email" id="notificationEmail" class="email-input" 
                                           placeholder="请输入邮箱地址以接收完成通知" 
                                           title="输入邮箱后将在任务完成时收到邮件通知">
                                    <span class="email-required-tip">*必填</span>
                                </div>
                                <p class="email-description">
                                    <i class="fas fa-info-circle"></i>
                                    任务完成后将向您的邮箱发送通知，包含校对结果链接
                                </p>
                            </div>
                            
                            <p class="async-description">
                                启用后台处理后，即使关闭浏览器，校对任务也会在服务器上继续运行。
                                完成后会发送邮件通知，您也可以在<a href="/console/app/document_proofreader_async_manage/" target="_blank">任务管理页面</a>查看所有任务状态。
                            </p>
                        </div>
                </div>
                <div id="fileInfo" class="file-info">
                    <div id="fileName" class="file-name">
                        <i class="fas fa-file-alt"></i>
                        <span>未选择文件</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧历史记录区域 -->
            <div class="history-section" id="historySection">
                <div class="history-header">
                    <h3><i class="fas fa-history"></i> 历史记录</h3>
                    <button class="refresh-btn" onclick="refreshHistoryRecords()" title="刷新历史记录">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                
                <div class="history-content">
                    <div id="historyLoading" class="history-loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>加载中...</span>
                    </div>
                    
                    <div id="historyList" class="history-list" style="display: none;">
                        <!-- 历史记录项目将在这里动态加载 -->
                    </div>
                    
                    <div id="historyEmpty" class="history-empty" style="display: none;">
                        <i class="fas fa-folder-open"></i>
                        <p>暂无历史记录</p>
                        <small>完成校对后记录将出现在这里</small>
                    </div>
                    
                    <div class="history-pagination" id="historyPagination" style="display: none;">
                        <!-- 分页按钮将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <p><i class="fas fa-brain"></i> AI 正在智能分析您的讲义，请稍候...</p>
        </div>

        <!-- 普通审校进度显示 -->
        <div id="normalProgress" class="streaming-progress">
            <div class="batch-indicator">
                <span class="batch-count">文本块 <span id="normalCurrentChunk">1</span> / <span id="normalTotalChunks">1</span></span>
                <span class="eta-display" id="normalEta">总时间：。。。</span>
            </div>
            <div class="progress-text">
                <span id="normalProgressPercentage">0%</span> 完成
            </div>
            <div class="progress-bar-container">
                <div id="normalProgressBar" class="progress-bar"></div>
            </div>
            <div class="streaming-status">
                <i class="fas fa-cog fa-spin"></i>
                <span id="normalStatusText">正在智能校对中...</span>
            </div>
        </div>

        <div id="resultSection" class="result-section">
            <div class="result-tabs">
                <div class="tabs-container">
                    <div class="tab active" data-tab="labeledTab">
                        <i class="fas fa-edit"></i>
                        讲义：修改建议
                    </div>
                    <div class="tab" data-tab="correctedTab">
                        <i class="fas fa-check-circle"></i>
                        审校后的讲义
                    </div>
                </div>
                
                <div class="controls-container">
                    <!-- 下载按钮组 -->
                    <div class="download-controls" id="downloadControls">
                        <button class="download-btn" onclick="downloadFile('md')" data-tooltip="下载 Markdown 文件">
                            <i class="fas fa-download"></i>
                            下载MD
                        </button>
                        <button class="download-btn download-btn-word" onclick="downloadFile('docx')" data-tooltip="下载 Word 文件">
                            <i class="fas fa-file-word"></i>
                            下载Word
                        </button>
                    </div>
                    <div class="edit-controls">
                        <button id="editBtn" class="edit-btn tooltip" data-tooltip="进入编辑模式">
                            <i class="fas fa-pen"></i>
                            编辑
                        </button>
                        <button id="acceptAllBtn" class="accept-all-btn tooltip" data-tooltip="一键采纳所有建议">
                            <i class="fas fa-check-double"></i>
                            一键采纳
                        </button>
                        <button id="saveBtn" class="save-btn tooltip" data-tooltip="保存编辑结果">
                            <i class="fas fa-save"></i>
                            保存
                        </button>
                    </div>
                    <!-- 词库管理按钮 -->
                    <div class="dictionary-controls">
                        <button class="download-btn" onclick="openDictionaryManagement()" data-tooltip="管理专有名词词库" style="background: linear-gradient(135deg, var(--secondary-color), #059669);">
                            <i class="fas fa-book"></i>
                            词库管理
                        </button>
                        <button class="download-btn" onclick="openAsyncTaskManagement()" data-tooltip="管理异步校对任务" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-tasks"></i>
                            任务管理
                        </button>
                    </div>
                </div>
            </div>

            <div id="labeledTab" class="tab-content active">
                <div class="result-container">
                    <div id="labeledText" class="labeled-text"></div>
                    <div id="errorList" class="error-list">
                        <h3>
                            <i class="fas fa-exclamation-triangle"></i>
                            修改建议列表
                        </h3>
                        <div id="errorItems">
                            <!-- 默认空状态 -->
                            <div class="error-list-empty" id="errorListEmpty">
                                <i class="fas fa-clipboard-check"></i>
                                <h4>暂无修改建议</h4>
                                <p>上传文档并开始审校后，</p>
                                <p>AI发现的问题将在这里显示</p>
                                <div class="tip">支持点击建议进行快速处理</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="correctedTab" class="tab-content">
                <div id="correctedText" class="markdown-preview"></div>
            </div>
        </div>
    </div>

    <!-- 建议选择浮窗 -->
    <div id="suggestionPopup" class="suggestion-popup">
        <button id="acceptSuggestion">
            <i class="fas fa-check"></i>
            采纳修改建议
        </button>
        <button id="rejectSuggestion">
            <i class="fas fa-times"></i>
            放弃修改建议
        </button>
    </div>

    <!-- 底部状态提示 -->
    <div id="bottomStatus" class="bottom-status">
        <i class="fas fa-spinner"></i>
        <span id="bottomStatusText">正在努力校对中...</span>
    </div>

    <!-- 传递Django模板数据到JavaScript -->
    {% if is_result_page %}
    <div id="resultPageData" style="display: none;"
         data-is-result-page="true"
         {% if error_message %}data-error-message="{{ error_message|escape }}"{% endif %}
         {% if proofreader_id %}data-proofreader-id="{{ proofreader_id }}"{% endif %}
         {% if labeled_text %}data-labeled-text="{{ labeled_text|escape }}"{% endif %}
         {% if corrected_text %}data-corrected-text="{{ corrected_text|escape }}"{% endif %}
         {% if error_list_json %}data-error-list="{{ error_list_json|escape }}"{% endif %}
         {% if download_url_md %}data-download-url-md="{{ download_url_md }}"{% endif %}
         {% if download_url_docx %}data-download-url-docx="{{ download_url_docx }}"{% endif %}
         {% if file_name %}data-file-name="{{ file_name|escape }}"{% endif %}
         {% if creation_time %}data-creation-time="{{ creation_time }}"{% endif %}>
    </div>
    {% endif %}

    
    <script>
        // 词库管理
        window.openDictionaryManagement = function() {
            window.open('/console/app/proofreading_dictionary_manage/', '_blank');
        };
        
        // 任务管理
        window.openAsyncTaskManagement = function() {
            window.open('/console/app/document_proofreader_async_manage/', '_blank');
        };
        // 全局变量
let isEditMode = false;
let currentProofreaderId = null;
let originalLabeledText = '';
let currentSuggestionElement = null;
let downloadUrls = {}; // 存储下载链接
let streamingEventSource = null; // EventSource对象
let streamingStartTime = null; // 开始时间
let normalProofreadStartTime = null; // 普通审校开始时间

// ========================================
// 邮箱通知控制函数
// ========================================

// 初始化邮箱通知控制
function initializeEmailNotificationControl() {
    const enableAsyncCheckbox = document.getElementById('enableAsync');
    const emailNotificationContainer = document.getElementById('emailNotificationContainer');
    const notificationEmail = document.getElementById('notificationEmail');
    
    if (!enableAsyncCheckbox || !emailNotificationContainer) {
        return;
    }
    
    // 控制邮箱输入框的显示/隐藏
    function toggleEmailInput() {
        if (enableAsyncCheckbox.checked) {
            emailNotificationContainer.classList.remove('hidden');
            notificationEmail.setAttribute('required', 'required');
        } else {
            emailNotificationContainer.classList.add('hidden');
            notificationEmail.removeAttribute('required');
        }
    }
    
    // 初始状态
    toggleEmailInput();
    
    // 监听异步选择框变化
    enableAsyncCheckbox.addEventListener('change', toggleEmailInput);
    
    // 邮箱输入框验证
    if (notificationEmail) {
        notificationEmail.addEventListener('blur', function() {
            const email = this.value.trim();
            if (email && enableAsyncCheckbox.checked) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    this.style.borderColor = 'var(--danger-color)';
                    showNotification('请输入有效的邮箱地址', 'warning');
                } else {
                    this.style.borderColor = 'var(--success-color)';
                }
            } else {
                this.style.borderColor = 'var(--border-color)';
            }
        });
        
        // 输入时重置边框颜色
        notificationEmail.addEventListener('input', function() {
            this.style.borderColor = 'var(--border-color)';
        });
    }
}

// ========================================
// 普通审校进度处理函数
// ========================================

// 显示普通审校进度
function showNormalProgress() {
    const normalProgress = document.getElementById('normalProgress');
    normalProgress.style.display = 'block';
    setTimeout(() => {
        normalProgress.classList.add('show');
    }, 100);
    normalProofreadStartTime = Date.now();
}

// 隐藏普通审校进度
function hideNormalProgress() {
    const normalProgress = document.getElementById('normalProgress');
    normalProgress.classList.remove('show');
    setTimeout(() => {
        normalProgress.style.display = 'none';
    }, 300);
}

// 更新普通审校进度
function updateNormalProgress(current, total) {
    const normalCurrentChunk = document.getElementById('normalCurrentChunk');
    const normalTotalChunks = document.getElementById('normalTotalChunks');
    const normalProgressPercentage = document.getElementById('normalProgressPercentage');
    const normalProgressBar = document.getElementById('normalProgressBar');
    const normalStatusText = document.getElementById('normalStatusText');
    const normalEta = document.getElementById('normalEta');
    
    // 更新数值
    normalCurrentChunk.textContent = current;
    normalTotalChunks.textContent = total;
    
    // 计算进度百分比
    const progress = Math.round((current / total) * 100);
    normalProgressPercentage.textContent = `${progress}%`;
    normalProgressBar.style.width = `${progress}%`;
    
    // 更新状态文本
    if (current < total) {
        normalStatusText.textContent = `正在处理第 ${current} 个文本块...`;
        
        // 计算时间
        const elapsed = Date.now() - normalProofreadStartTime;
        const etaSeconds = Math.round(elapsed / 1000);
        
        normalEta.textContent = `总时间: ${etaSeconds}秒`;

    } else {
        normalStatusText.textContent = '校对即将完成...';
        normalEta.textContent = '即将完成';
    }
    
    // 更新底部状态
    updateBottomStatus(`正在处理文本块 ${current}/${total} (${progress}%)`);
}

// ========================================
// 结果页面初始化函数
// ========================================

// 初始化结果页面
function initializeResultPage() {
    console.log('📄 初始化结果页面...');
    
    // 隐藏历史记录区域（结果页面不需要显示历史记录）
    const historySection = document.getElementById('historySection');
    if (historySection) {
        historySection.style.display = 'none';
    }
    
    // 调整主内容区域布局（移除网格布局，改为单列）
    const mainContentWrapper = document.querySelector('.main-content-wrapper');
    if (mainContentWrapper) {
        mainContentWrapper.style.gridTemplateColumns = '1fr';
        mainContentWrapper.style.gap = '0';
    }
    
    // 从DOM属性获取结果页面数据
    const dataElement = document.getElementById('resultPageData');
    if (!dataElement) {
        console.log('⚠️ 未找到结果页面数据元素');
        return;
    }
    
    const errorMessage = dataElement.getAttribute('data-error-message');
    if (errorMessage) {
        // 显示错误信息
        showErrorPage(errorMessage);
        return;
    }
    
    const proofreaderId = dataElement.getAttribute('data-proofreader-id');
    const labeledText = dataElement.getAttribute('data-labeled-text');
    const correctedText = dataElement.getAttribute('data-corrected-text');
    
    if (proofreaderId && labeledText && correctedText) {
        // 设置全局变量
        currentProofreaderId = parseInt(proofreaderId);
        originalLabeledText = labeledText;
        
        // 设置下载链接
        const downloadUrlMd = dataElement.getAttribute('data-download-url-md');
        const downloadUrlDocx = dataElement.getAttribute('data-download-url-docx');
        if (downloadUrlMd && downloadUrlDocx) {
            downloadUrls.md = downloadUrlMd;
            downloadUrls.docx = downloadUrlDocx;
        }
        
        // 显示结果区域
        document.getElementById('resultSection').style.display = 'block';
        
        // 隐藏上传区域
        document.getElementById('uploadSection').style.display = 'none';
        
        // 填充内容
        renderMarkdown(labeledText, 'labeledText');
        renderMarkdown(correctedText, 'correctedText');
        
        // 填充错误列表
        const errorListJson = dataElement.getAttribute('data-error-list');
        let errorList = [];
        try {
            errorList = errorListJson ? JSON.parse(errorListJson) : [];
        } catch (e) {
            console.warn('解析错误列表失败:', e);
            errorList = [];
        }
        renderErrorList(errorList);
        
        // 切换到"审校后的讲义"标签
        switchToCorrectTab();
        
        // 更新下载按钮状态
        updateDownloadButtonVisibility();
        
        // 显示页面标题信息
        const fileName = dataElement.getAttribute('data-file-name');
        const creationTime = dataElement.getAttribute('data-creation-time');
        showResultPageInfo(fileName, creationTime);
        
        console.log('✅ 结果页面初始化完成');
    }
}

// 显示错误页面
function showErrorPage(errorMessage) {
    document.getElementById('uploadSection').style.display = 'none';
    document.getElementById('resultSection').style.display = 'block';
    
    const labeledText = document.getElementById('labeledText');
    labeledText.innerHTML = `
        <div class="error-state">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>访问失败</h3>
            <p>${errorMessage}</p>
            <a href="/console/app/document_proofreader/" class="btn btn-primary" style="margin-top: 1rem; text-decoration: none;">
                <i class="fas fa-home"></i> 返回首页
            </a>
        </div>
    `;
}

// 渲染错误列表
function renderErrorList(errorList) {
    const errorItems = document.getElementById('errorItems');
    const errorListEmpty = document.getElementById('errorListEmpty');
    
    if (errorList.length === 0) {
        errorItems.innerHTML = `
            <div class="success-state">
                <i class="fas fa-check-circle"></i>
                <p>恭喜！未发现任何错误</p>
                <small style="color: var(--text-secondary); display: block; margin-top: 0.5rem;">
                    您的文档质量很高！
                </small>
            </div>
        `;
    } else {
        // 隐藏默认空状态
        if (errorListEmpty) {
            errorListEmpty.style.display = 'none';
        }
        
        errorItems.innerHTML = '';
        errorList.forEach(error => {
            const errorItem = document.createElement('div');
            errorItem.className = 'error-item';
            errorItem.id = `error-${error.id}`;
            errorItem.setAttribute('onclick', `jumpToErrorPosition('${error.id}')`);
            errorItem.innerHTML = `
                <button class="add-to-dictionary-btn" onclick="event.stopPropagation(); addToDictionary('${error.id}', '${error.error_text.replace(/'/g, '\\\'')}')" title="加入词库">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="delete-suggestion-btn" onclick="event.stopPropagation(); deleteSuggestionFromList('${error.id}')">
                    <i class="fas fa-times"></i>
                </button>
                <div class="error-id">错误ID: ${error.id}</div>
                <div class="error-text">错误文本: ${error.error_text}</div>
                <div class="error-suggestion">修改建议: ${error.error_suggestion}</div>
                <div class="error-reason">错误理由: ${error.error_reason}</div>
            `;
            errorItems.appendChild(errorItem);
        });
    }
}

// 切换到审校后的讲义标签
function switchToCorrectTab() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    const correctedTabElement = document.querySelector('[data-tab="correctedTab"]');
    
    if (correctedTabElement) {
        tabs.forEach(t => t.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));
        correctedTabElement.classList.add('active');
        document.getElementById('correctedTab').classList.add('active');
    }
}

// 显示结果页面信息
function showResultPageInfo(fileName, creationTime) {
    if (fileName && creationTime) {
        // 在页面顶部显示文件信息
        const header = document.querySelector('.header');
        if (header) {
            const infoDiv = document.createElement('div');
            infoDiv.style.cssText = `
                background: var(--bg-primary);
                border-radius: 0.5rem;
                padding: 1rem;
                margin-top: 1rem;
                border: 1px solid var(--border-color);
                text-align: left;
            `;
            infoDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 1rem; font-size: 0.9rem; color: var(--text-secondary);">
                    <span><i class="fas fa-file-alt"></i> 文件：${fileName}</span>
                    <span><i class="fas fa-clock"></i> 校对时间：${creationTime}</span>
                    <span><i class="fas fa-link"></i> 结果页面</span>
                </div>
            `;
            header.appendChild(infoDiv);
        }
    }
}

// 将需要在HTML中调用的函数声明为全局函数
window.highlightError = function(id) {
    const errorItem = document.getElementById(`error-${id}`);
    if (errorItem) {
        // 移除其他高亮
        document.querySelectorAll('.error-item').forEach(item => item.classList.remove('highlight'));
        // 添加高亮
        errorItem.classList.add('highlight');
        // 滚动到错误项
        errorItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 添加轻微震动效果
        errorItem.style.animation = 'none';
    }
};

// 新增：点击错误列表项跳转到左侧文档位置的函数
window.jumpToErrorPosition = function(id) {
    // 首先高亮右侧的错误项
    highlightError(id);
    
    // 然后定位到左侧文档中的错误位置
    const errorPosition = document.getElementById(`error-position-${id}`);
    if (errorPosition) {
        // 确保左侧预览区域是可见的（切换到"讲义：修改建议"标签）
        const labeledTab = document.querySelector('[data-tab="labeledTab"]');
        if (labeledTab && !labeledTab.classList.contains('active')) {
            // 切换到修改建议标签
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            labeledTab.classList.add('active');
            document.getElementById('labeledTab').classList.add('active');
        }
        
        // 滚动到错误位置
        const labeledTextContainer = document.getElementById('labeledText');
        if (labeledTextContainer) {
            // 保存原始样式，以便稍后恢复
            const originalBackground = errorPosition.style.background;
            const originalBorderRadius = errorPosition.style.borderRadius;
            const originalTransition = errorPosition.style.transition;
            
            // 暂时高亮错误位置
            errorPosition.style.background = 'rgba(255, 193, 7, 0.3)';
            errorPosition.style.borderRadius = '0.25rem';
            errorPosition.style.transition = 'all 0.3s ease';
            
            // 滚动到错误位置
            errorPosition.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center',
                inline: 'nearest'
            });
            
            // 2秒后恢复原始样式
            setTimeout(() => {
                errorPosition.style.background = originalBackground;
                errorPosition.style.borderRadius = originalBorderRadius;
                errorPosition.style.transition = originalTransition;
            }, 2000);
            
            // 显示成功提示
            showNotification('已定位到错误位置', 'info');
        }
    } else {
        // 如果找不到具体的错误位置元素，尝试通过data-id查找
        const labeledTextContainer = document.getElementById('labeledText');
        if (labeledTextContainer && isEditMode) {
            const suggestionElement = labeledTextContainer.querySelector(`[data-id="${id}"]`);
            if (suggestionElement) {
                // 确保左侧预览区域是可见的
                const labeledTab = document.querySelector('[data-tab="labeledTab"]');
                if (labeledTab && !labeledTab.classList.contains('active')) {
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                    labeledTab.classList.add('active');
                    document.getElementById('labeledTab').classList.add('active');
                }
                
                // 保存原始样式
                const originalBackground = suggestionElement.style.background;
                const originalBorderRadius = suggestionElement.style.borderRadius;
                const originalTransition = suggestionElement.style.transition;
                
                // 高亮元素
                suggestionElement.style.background = 'rgba(255, 193, 7, 0.3)';
                suggestionElement.style.borderRadius = '0.25rem';
                suggestionElement.style.transition = 'all 0.3s ease';
                
                // 滚动到元素位置
                suggestionElement.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center',
                    inline: 'nearest'
                });
                
                // 2秒后恢复原始样式
                setTimeout(() => {
                    suggestionElement.style.background = originalBackground;
                    suggestionElement.style.borderRadius = originalBorderRadius;
                    suggestionElement.style.transition = originalTransition;
                }, 2000);
                
                showNotification('已定位到错误位置', 'info');
                return;
            }
        }
        
        showNotification('无法找到对应的错误位置', 'warning');
    }
};

// 检查文件格式是否支持
function isSupportedFileFormat(fileName) {
    const supportedExtensions = ['.md', '.docx', '.doc'];
    const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return supportedExtensions.includes(fileExtension);
}

// 获取文件格式描述
function getFileFormatDescription(fileName) {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    switch(extension) {
        case '.md':
            return 'Markdown 文件';
        case '.docx':
            return 'Word 文档';
        case '.doc':
            return 'Word 旧版文档';
        default:
            return '未知格式';
    }
}

window.deleteSuggestionFromList = function(errorId) {
    // 检查是否在编辑模式下
    if (!isEditMode) {
        showNotification('请先进入编辑模式才能删除建议', 'warning');
        return;
    }
    
    // 显示确认对话框
    if (!confirm('确定要放弃这条修改建议吗？')) {
        return;
    }

    // 从文本中移除对应的建议
    const labeledTextContainer = document.getElementById('labeledText');
    
    if (isEditMode) {
        // 编辑模式下，查找并替换不可编辑的建议元素
        const suggestionElements = labeledTextContainer.querySelectorAll(`[data-id="${errorId}"]`);
        suggestionElements.forEach(element => {
            const suggestionText = element.innerHTML;
            const match = suggestionText.match(/~~(.*?)~~/);
            if (match) {
                const originalText = match[1];
                const textNode = document.createTextNode(originalText);
                element.parentNode.replaceChild(textNode, element);
            }
        });
    } else {
        // 非编辑模式下，更新原始文本并重新渲染
        originalLabeledText = originalLabeledText.replace(
            new RegExp(`(\\**)~~.*?~~(\\**)\\s*\\(👉id:\\s*${errorId}\\s*,\\s*修改建议:.*?👈\\)`, 'g'),
            function(match) {
                const textMatch = match.match(/(\**)~~(.*?)~~(\**)/);
                return textMatch ? textMatch[1] + textMatch[2] + textMatch[3] : match;
            }
        );
        renderMarkdown(originalLabeledText, 'labeledText');
    }
    
    // 从错误列表中移除
    removeErrorFromList(errorId);
    
    // 显示成功提示
    showNotification('已放弃该修改建议', 'success');
};

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation-triangle' : 'info'}"></i>
        <span>${message}</span>
    `;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'var(--success-color)' : 
                    type === 'error' ? 'var(--danger-color)' : 
                    type === 'warning' ? 'var(--warning-color)' : 'var(--info-color)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease-in-out;
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 渲染Markdown
function renderMarkdown(text, elementId) {
    const container = document.getElementById(elementId);
    
    if (isEditMode && elementId === 'labeledText') {
        // 编辑模式下显示可编辑的内容
        renderEditableText(text, container);
    } else {
        // 非编辑模式或审校后的讲义，正常渲染
        let processedText = text;
        
        // 如果是审校后的讲义，确保移除所有错误标记
        if (elementId === 'correctedText') {
            // 更新正则表达式以支持markdown标记
            processedText = text.replace(/(\**)~~(.*?)~~(\**)\s*\(👉id:\s*\d+,\s*修改建议:\s*(.*?),\s*错误理由:.*?👈\)/g, function(match, preMarkdown, errorText, postMarkdown, suggestion) {
                return preMarkdown + suggestion.trim() + postMarkdown; // 保留markdown标记，使用建议内容
            });
        } else {
            // 对于"讲义：修改建议"页面，转换错误标记为可点击链接
            processedText = text.replace(/\(👉id:\s*(\d+),\s*修改建议:\s*(.*?)(?:,\s*错误理由:\s*.*?)?\👈\)/g, (match, id, suggestion) => {
                return `<span id="error-position-${id}" class="highlighted-content" data-id="${id}" onclick="highlightError(${id})">(👉修改建议: ${suggestion})</span>`;
            });
        }
        
        container.innerHTML = marked.parse(processedText);
    }
}

// 渲染可编辑的文本（编辑模式专用）
function renderEditableText(text, container) {
    container.innerHTML = '';
    container.setAttribute('contenteditable', 'true');
    container.classList.add('edit-mode');
    
    // 更强大的建议匹配正则表达式，支持markdown格式分割的情况
    const suggestionRegex = /(\**)~~(.*?)~~(\**)\s*\(👉id:\s*(\d+),\s*修改建议:\s*(.*?),\s*错误理由:\s*(.*?)👈\)/g;
    let lastIndex = 0;
    let match;
    
    while ((match = suggestionRegex.exec(text)) !== null) {
        // 添加普通文本
        if (match.index > lastIndex) {
            const normalText = text.substring(lastIndex, match.index);
            if (normalText) {
                // 保持换行格式
                const lines = normalText.split('\n');
                for (let i = 0; i < lines.length; i++) {
                    if (i > 0) {
                        container.appendChild(document.createElement('br'));
                    }
                    if (lines[i]) {
                        container.appendChild(document.createTextNode(lines[i]));
                    }
                }
            }
        }
        
        // 添加不可编辑的建议部分
        const suggestionSpan = document.createElement('span');
        suggestionSpan.className = 'non-editable-suggestion highlighted-content';
        suggestionSpan.setAttribute('id', `error-position-${match[4]}`); // ID现在在match[4]
        suggestionSpan.setAttribute('data-id', match[4]); // ID现在在match[4]
        suggestionSpan.setAttribute('contenteditable', 'false');
        suggestionSpan.setAttribute('unselectable', 'on'); // 增加不可选择属性
        suggestionSpan.style.userSelect = 'none'; // CSS样式防止选择
        // 保持原有的格式显示，包含markdown标记
        suggestionSpan.innerHTML = `${match[1]}~~${match[2]}~~${match[3]}(👉id: ${match[4]}, 修改建议: ${match[5]}, 错误理由: ${match[6]}👈)`;
        
        // 防止建议被编辑的多重保护
        suggestionSpan.addEventListener('keydown', function(e) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        });
        
        suggestionSpan.addEventListener('input', function(e) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        });
        
        suggestionSpan.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showSuggestionPopup(e, this);
        });
        
        // 添加拖拽保护
        suggestionSpan.addEventListener('dragstart', function(e) {
            e.preventDefault();
            return false;
        });
        
        container.appendChild(suggestionSpan);
        
        lastIndex = suggestionRegex.lastIndex;
    }
    
    // 添加剩余文本
    if (lastIndex < text.length) {
        const remainingText = text.substring(lastIndex);
        if (remainingText) {
            // 保持换行格式
            const lines = remainingText.split('\n');
            for (let i = 0; i < lines.length; i++) {
                if (i > 0) {
                    container.appendChild(document.createElement('br'));
                }
                if (lines[i]) {
                    container.appendChild(document.createTextNode(lines[i]));
                }
            }
        }
    }
    
    // 监听容器的输入事件，确保不可编辑元素保持不变
    container.addEventListener('input', function(e) {
        // 重新确保所有建议元素的不可编辑属性
        const suggestions = container.querySelectorAll('.non-editable-suggestion');
        suggestions.forEach(suggestion => {
            if (!suggestion.getAttribute('contenteditable') || suggestion.getAttribute('contenteditable') !== 'false') {
                suggestion.setAttribute('contenteditable', 'false');
                suggestion.setAttribute('unselectable', 'on');
                suggestion.style.userSelect = 'none';
            }
        });
    });
}

// 显示建议选择浮窗
function showSuggestionPopup(event, suggestionElement) {
    if (!isEditMode) return;
    
    const popup = document.getElementById('suggestionPopup');
    currentSuggestionElement = suggestionElement;
    
    // 设置浮窗位置
    popup.style.left = event.pageX + 'px';
    popup.style.top = event.pageY + 'px';
    popup.style.display = 'block';
    
    // 点击其他地方隐藏浮窗
    setTimeout(() => {
        document.addEventListener('click', hideSuggestionPopup);
    }, 0);
}

// 隐藏建议选择浮窗
function hideSuggestionPopup() {
    const popup = document.getElementById('suggestionPopup');
    popup.style.display = 'none';
    document.removeEventListener('click', hideSuggestionPopup);
}

// 采纳修改建议
function acceptSuggestion() {
    if (!currentSuggestionElement) return;
    
    const suggestionText = currentSuggestionElement.innerHTML;
    const match = suggestionText.match(/修改建议:\s*(.*?),\s*错误理由:/);
    if (match) {
        const newText = match[1];
        const textNode = document.createTextNode(newText);
        currentSuggestionElement.parentNode.replaceChild(textNode, currentSuggestionElement);
        
        // 从错误列表中移除对应项
        const errorId = currentSuggestionElement.getAttribute('data-id');
        removeErrorFromList(errorId);
        
        showNotification('已采纳修改建议', 'success');
    }
    
    hideSuggestionPopup();
}

// 放弃修改建议
function rejectSuggestion() {
    if (!currentSuggestionElement) return;
    
    const suggestionText = currentSuggestionElement.innerHTML;
    const match = suggestionText.match(/~~(.*?)~~/);
    if (match) {
        const originalText = match[1];
        const textNode = document.createTextNode(originalText);
        currentSuggestionElement.parentNode.replaceChild(textNode, currentSuggestionElement);
        
        // 从错误列表中移除对应项
        const errorId = currentSuggestionElement.getAttribute('data-id');
        removeErrorFromList(errorId);
        
        showNotification('已放弃修改建议', 'info');
    }
    
    hideSuggestionPopup();
}

// 从错误列表中移除错误项
function removeErrorFromList(errorId) {
    const errorItem = document.getElementById(`error-${errorId}`);
    if (errorItem) {
        // 添加移除动画
        errorItem.style.transition = 'all 0.3s ease-out';
        errorItem.style.transform = 'translateX(100%)';
        errorItem.style.opacity = '0';
        
        setTimeout(() => {
            errorItem.remove();
            
            // 检查是否还有错误项，如果没有则显示空状态
            const remainingErrors = document.querySelectorAll('.error-item');
            if (remainingErrors.length === 0) {
                const errorItemsContainer = document.getElementById('errorItems');
                const errorListEmpty = document.getElementById('errorListEmpty');
                if (errorListEmpty) {
                    errorListEmpty.style.display = 'block';
                } else {
                    // 确保 errorItemsContainer 存在
                    if (errorItemsContainer) {
                        errorItemsContainer.innerHTML = `
                            <div class="error-list-empty">
                                <i class="fas fa-clipboard-check"></i>
                                <h4>所有建议已处理完成</h4>
                                <p>您已处理了所有修改建议</p>
                                <div class="tip">可以切换到"审校后的讲义"查看最终结果</div>
                            </div>
                        `;
                    }
                }
            }
        }, 300);
        
        // 调用后端API删除数据库中的记录
        if (currentProofreaderId) {
            fetch('/console/app/document_proofreader/delete_suggestion/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    proofreader_id: currentProofreaderId,
                    error_id: errorId
                })
            });
        }
    }
}

// 更新下载按钮可见性与状态
function updateDownloadButtonVisibility() {
    const correctedTabActive = document.querySelector('[data-tab="correctedTab"].active');
    const downloadControls = document.getElementById('downloadControls');
    
    if (isEditMode) {
        downloadControls.classList.remove('show');
        downloadControls.classList.add('disabled'); // 添加禁用样式
        // 确保按钮本身也被禁用
        downloadControls.querySelectorAll('.download-btn').forEach(btn => btn.disabled = true);
        console.log('编辑模式：隐藏并禁用下载按钮');
    } else {
        downloadControls.classList.remove('disabled'); // 移除禁用样式
        downloadControls.querySelectorAll('.download-btn').forEach(btn => btn.disabled = false);
        if (correctedTabActive && (downloadUrls.md || downloadUrls.docx)) {
            downloadControls.style.display = 'flex'; // 确保 flex 生效
            downloadControls.classList.add('show');
            console.log('非编辑模式，审校后Tab激活，有链接：显示下载按钮');
        } else {
            downloadControls.classList.remove('show');
            // downloadControls.style.display = 'none'; // 确保在不满足条件时隐藏
            console.log('非编辑模式或无链接或非审校后Tab：隐藏下载按钮');
        }
    }
}

// 获取CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 进入编辑模式
function enterEditMode() {
    if (!originalLabeledText) {
        showNotification('没有可编辑的内容', 'error');
        return;
    }
    
    isEditMode = true;
    document.getElementById('editBtn').style.display = 'none';
    document.getElementById('acceptAllBtn').style.display = 'inline-flex';
    document.getElementById('saveBtn').style.display = 'inline-flex';
    
    // 禁用"审校后的讲义"标签
    const correctedTab = document.querySelector('[data-tab="correctedTab"]');
    correctedTab.classList.add('disabled');
    correctedTab.style.pointerEvents = 'none';
    
    // 给错误列表添加编辑模式类，显示删除建议按钮
    const errorList = document.getElementById('errorList');
    if (errorList) {
        errorList.classList.add('edit-mode');
    }
    
    const labeledTextContainer = document.getElementById('labeledText');
    
    // 使用保存的原始带标记文本进行编辑
    renderEditableText(originalLabeledText, labeledTextContainer);
    
    // 更新下载按钮状态
    updateDownloadButtonVisibility();
    showNotification('已进入编辑模式', 'info');
}

// 一键采纳所有建议
function acceptAllSuggestions() {
    if (!isEditMode) return;
    
    const labeledTextContainer = document.getElementById('labeledText');
    const suggestionElements = labeledTextContainer.querySelectorAll('.non-editable-suggestion');
    
    if (suggestionElements.length === 0) {
        showNotification('没有可采纳的建议', 'info');
        return;
    }

    if (!confirm(`确定要采纳所有 ${suggestionElements.length} 条建议吗？`)) {
        return;
    }
    
    // 收集所有需要删除的错误ID
    const errorIdsToRemove = [];
    
    suggestionElements.forEach(element => {
        const suggestionText = element.innerHTML;
        const match = suggestionText.match(/修改建议:\s*(.*?),\s*错误理由:/);
        if (match) {
            const newText = match[1];
            const textNode = document.createTextNode(newText);
            element.parentNode.replaceChild(textNode, element);
            
            // 收集错误ID
            const errorId = element.getAttribute('data-id');
            errorIdsToRemove.push(errorId);
        }
    });
    
    // 批量删除右侧错误列表项
    errorIdsToRemove.forEach(errorId => {
        removeErrorFromList(errorId);
    });
    
    showNotification(`已采纳 ${errorIdsToRemove.length} 条建议`, 'success');
}

// 保存编辑（新逻辑：未处理的建议默认放弃）
function saveEdit() {
    if (!currentProofreaderId) {
        showNotification('无法保存：缺少校对ID', 'error');
        return;
    }
    
    const labeledTextContainer = document.getElementById('labeledText');
    // 获取HTML内容，保持表格等HTML格式
    let editedContent = '';
    
    // 遍历所有子节点来获取内容（保持HTML格式）
    function getHTMLContent(node) {
        let htmlContent = '';
        for (let child of node.childNodes) {
            if (child.nodeType === Node.TEXT_NODE) {
                htmlContent += child.textContent;
            } else if (child.tagName === 'BR') {
                htmlContent += '\n';
            } else if (child.classList && child.classList.contains('non-editable-suggestion')) {
                // 对于未处理的修改建议，提取原始错误文本（放弃建议）
                const suggestionMatch = child.innerHTML.match(/~~(.*?)~~/);
                if (suggestionMatch) {
                    htmlContent += suggestionMatch[1]; // 使用原始文本
                    
                    // 删除右侧对应的错误列表项
                    const errorId = child.getAttribute('data-id');
                    removeErrorFromList(errorId);
                }
            } else if (child.tagName === 'TABLE') {
                // 保持表格的HTML格式
                htmlContent += child.outerHTML;
            } else if (child.tagName === 'DIV') {
                // 特殊处理DIV标签：contenteditable会自动插入DIV作为段落分隔
                // 在DIV前添加换行符（如果不是第一个元素）
                if (htmlContent && !htmlContent.endsWith('\n')) {
                    htmlContent += '\n';
                }
                // 递归处理DIV内容
                const divContent = getHTMLContent(child);
                htmlContent += divContent;
                // 在DIV后添加换行符（如果内容不为空且不以换行符结尾）
                if (divContent && !divContent.endsWith('\n')) {
                    htmlContent += '\n';
                }
            } else if (child.tagName === 'P') {
                // 处理P标签：在前后添加换行符
                if (htmlContent && !htmlContent.endsWith('\n')) {
                    htmlContent += '\n\n';
                }
                const pContent = getHTMLContent(child);
                htmlContent += pContent;
                if (pContent && !pContent.endsWith('\n')) {
                    htmlContent += '\n\n';
                }
            } else if (child.tagName === 'H1' || child.tagName === 'H2' || child.tagName === 'H3' || 
                        child.tagName === 'H4' || child.tagName === 'H5' || child.tagName === 'H6') {
                // 处理标题标签：保持Markdown格式
                if (htmlContent && !htmlContent.endsWith('\n')) {
                    htmlContent += '\n\n';
                }
                const level = parseInt(child.tagName.substring(1));
                const prefix = '#'.repeat(level) + ' ';
                const headingContent = getHTMLContent(child);
                htmlContent += prefix + headingContent;
                if (headingContent && !headingContent.endsWith('\n')) {
                    htmlContent += '\n\n';
                }
            } else if (child.tagName === 'UL' || child.tagName === 'OL' || child.tagName === 'LI' || 
                        child.tagName === 'BLOCKQUOTE' || child.tagName === 'CODE' || child.tagName === 'PRE') {
                // 对于列表、引用、代码等元素，保持其HTML格式
                if (child.innerHTML.trim()) {
                    htmlContent += child.outerHTML;
                }
            } else {
                // 递归处理其他元素
                htmlContent += getHTMLContent(child);
            }
        }
        return htmlContent;
    }
    
    // 先尝试获取HTML内容（保持表格格式）
    try {
        editedContent = getHTMLContent(labeledTextContainer);
        
        // 如果获取的内容为空或只有空白字符，回退到innerHTML方式
        if (!editedContent.trim()) {
            // 克隆容器并移除修改建议元素，保持其他HTML格式
            const clonedContainer = labeledTextContainer.cloneNode(true);
            const suggestions = clonedContainer.querySelectorAll('.non-editable-suggestion');
            
            suggestions.forEach(suggestion => {
                const suggestionMatch = suggestion.innerHTML.match(/~~(.*?)~~/);
                if (suggestionMatch) {
                    const textNode = document.createTextNode(suggestionMatch[1]);
                    suggestion.parentNode.replaceChild(textNode, suggestion);
                    
                    // 删除右侧对应的错误列表项
                    const errorId = suggestion.getAttribute('data-id');
                    removeErrorFromList(errorId);
                }
            });
            
            // 对克隆容器应用相同的HTML清理逻辑
            editedContent = cleanContentEditableHTML(clonedContainer.innerHTML);
        }
        
        // 清理多余的换行符和空白字符
        editedContent = cleanupContent(editedContent);
        
    } catch (error) {
        console.warn('获取HTML内容失败，回退到纯文本模式:', error);
        // 如果出错，回退到原来的纯文本提取方式
        function getTextContent(node) {
            let text = '';
            for (let child of node.childNodes) {
                if (child.nodeType === Node.TEXT_NODE) {
                    text += child.textContent;
                } else if (child.tagName === 'BR') {
                    text += '\n';
                } else if (child.classList && child.classList.contains('non-editable-suggestion')) {
                    const suggestionMatch = child.innerHTML.match(/~~(.*?)~~/);
                    if (suggestionMatch) {
                        text += suggestionMatch[1];
                        const errorId = child.getAttribute('data-id');
                        removeErrorFromList(errorId);
                    }
                } else {
                    text += getTextContent(child);
                }
            }
            return text;
        }
        editedContent = getTextContent(labeledTextContainer);
    }
    
    // 显示保存中状态
    const saveBtn = document.getElementById('saveBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
    saveBtn.disabled = true;
    
    // 发送到后端保存
    fetch('/console/app/document_proofreader/save_edit/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            proofreader_id: currentProofreaderId,
            corrected_content: editedContent
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // 退出编辑模式
            isEditMode = false;
            document.getElementById('editBtn').style.display = 'inline-flex';
            document.getElementById('acceptAllBtn').style.display = 'none';
            document.getElementById('saveBtn').style.display = 'none';
            
            // 重新启用"审校后的讲义"标签
            const correctedTab = document.querySelector('[data-tab="correctedTab"]');
            correctedTab.classList.remove('disabled');
            correctedTab.style.pointerEvents = 'auto';
            
            // 移除错误列表的编辑模式类，隐藏删除建议按钮
            const errorList = document.getElementById('errorList');
            if (errorList) {
                errorList.classList.remove('edit-mode');
            }
            
            // 更新审校后的讲义内容
            document.getElementById('correctedText').innerHTML = marked.parse(editedContent);
            
            // 重新渲染带标记的文本（非编辑模式）
            const labeledTextContainer = document.getElementById('labeledText');
            labeledTextContainer.setAttribute('contenteditable', 'false');
            labeledTextContainer.classList.remove('edit-mode');
            labeledTextContainer.innerHTML = marked.parse(editedContent);
            
            // 更新原始带标记文本，移除已处理的建议
            originalLabeledText = editedContent;
            
            // 更新下载按钮状态
            updateDownloadButtonVisibility();
            showNotification('保存成功！', 'success');
        } else {
            showNotification('保存失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('保存失败：' + error, 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// 清理contenteditable产生的HTML标签
function cleanContentEditableHTML(html) {
    // 将<br>标签转换为换行符
    html = html.replace(/<br\s*\/?>/gi, '\n');
    
    // 将<div>标签转换为换行符（保持段落结构）
    html = html.replace(/<div[^>]*>/gi, '\n');
    html = html.replace(/<\/div>/gi, '');
    
    // 将<p>标签转换为双换行符（保持段落间距）
    html = html.replace(/<p[^>]*>/gi, '\n\n');
    html = html.replace(/<\/p>/gi, '');
    
    // 移除其他不需要的HTML标签（但保留表格、列表等重要标签）
    const preservedTags = ['table', 'tr', 'td', 'th', 'thead', 'tbody', 'tfoot', 'ul', 'ol', 'li', 'blockquote', 'pre', 'code', 'strong', 'em', 'b', 'i', 'a', 'img'];
    const preservedPattern = preservedTags.join('|');
    const unwantedTagsPattern = new RegExp(`<(?!/?(?:${preservedPattern})\\b)[^>]*>`, 'gi');
    html = html.replace(unwantedTagsPattern, '');
    
    return html;
}

// 清理内容中的多余空白字符
function cleanupContent(content) {
    // 清理多余的换行符
    content = content.replace(/\n{4,}/g, '\n\n\n'); // 最多保留3个连续换行
    
    // 清理行首行尾的空白字符
    const lines = content.split('\n');
    const cleanedLines = lines.map(line => {
        // 保留缩进，但清理行尾空白
        return line.replace(/\s+$/, '');
    });
    
    content = cleanedLines.join('\n');
    
    // 清理开头和结尾的多余空白
    content = content.trim();
    
    return content;
}

// 拖拽上传功能
function setupDragAndDrop() {
    const uploadSection = document.getElementById('uploadSection');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadSection.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadSection.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadSection.addEventListener(eventName, unhighlight, false);
    });

    uploadSection.addEventListener('drop', handleDrop, false);

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        uploadSection.classList.add('dragover');
    }

    function unhighlight(e) {
        uploadSection.classList.remove('dragover');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            const file = files[0];
            if (isSupportedFileFormat(file.name)) {
                handleFileSelect(file);
            } else {
                showNotification('仅支持 .md、.docx 和 .doc 格式的文件', 'error');
            }
        }
    }
}

// 处理文件选择
function handleFileSelect(file) {
    const fileInput = document.getElementById('fileInput');
    const fileName = document.getElementById('fileName');
    const fileInfo = document.getElementById('fileInfo');
    const startBtn = document.getElementById('startBtn');
    
    // 模拟文件输入
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(file);
    fileInput.files = dataTransfer.files;
    
    // 更新UI
    const formatDesc = getFileFormatDescription(file.name);
    fileName.innerHTML = `
        <i class="fas fa-file-alt"></i>
        <span>${file.name}</span>
        <small>(${formatDesc}, ${formatFileSize(file.size)})</small>
    `;
    fileInfo.classList.add('show');
    startBtn.disabled = false;
    
    showNotification(`${formatDesc}上传成功`, 'success');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 下载文件功能
function downloadFile(format) {
    if (format === 'md' && downloadUrls.md) {
        window.location.href = downloadUrls.md;
    } else if (format === 'docx' && downloadUrls.docx) {
        window.location.href = downloadUrls.docx;
    } else {
        showNotification('下载链接不可用', 'error');
    }
}

// ============================================================================
// 流式处理相关函数
// ============================================================================

// 禁用所有控件（开始校对时调用）
function disableAllControls() {
    // 禁用文件选择
    document.getElementById('fileInput').disabled = true;
    
    // 禁用上传按钮
    document.querySelector('.upload-btn').disabled = true;
    
    // 禁用开始按钮
    document.getElementById('startBtn').disabled = true;
    
    // 禁用拖拽功能并添加禁用样式
    const uploadSection = document.getElementById('uploadSection');
    uploadSection.style.pointerEvents = 'none';
    uploadSection.classList.add('disabled');
    
    console.log('📵 所有控件已禁用');
}

// 启用所有控件（校对完成或出错时调用）
function enableAllControls() {
    // 启用文件选择
    document.getElementById('fileInput').disabled = false;
    
    // 启用上传按钮
    document.querySelector('.upload-btn').disabled = false;
    
    // 根据文件状态启用开始按钮
    const fileInput = document.getElementById('fileInput');
    if (fileInput.files.length > 0) {
        document.getElementById('startBtn').disabled = false;
    }
    
    // 启用拖拽功能并移除禁用样式
    const uploadSection = document.getElementById('uploadSection');
    uploadSection.style.pointerEvents = 'auto';
    uploadSection.classList.remove('disabled');
    
    console.log('✅ 所有控件已启用');
}

// 显示底部状态
function showBottomStatus(text) {
    const bottomStatus = document.getElementById('bottomStatus');
    const bottomStatusText = document.getElementById('bottomStatusText');
    bottomStatusText.textContent = text;
    bottomStatus.classList.add('show');
}

// 更新底部状态
function updateBottomStatus(text) {
    const bottomStatusText = document.getElementById('bottomStatusText');
    bottomStatusText.textContent = text;
}

// 隐藏底部状态
function hideBottomStatus() {
    document.getElementById('bottomStatus').classList.remove('show');
}

document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    const fileName = document.getElementById('fileName');
    const fileInfo = document.getElementById('fileInfo');
    const startBtn = document.getElementById('startBtn');
    const loading = document.getElementById('loading');
    const resultSection = document.getElementById('resultSection');
    const labeledText = document.getElementById('labeledText');
    const correctedText = document.getElementById('correctedText');
    const errorItems = document.getElementById('errorItems');
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    const downloadControls = document.getElementById('downloadControls');
    const editBtn = document.getElementById('editBtn');
    const acceptAllBtn = document.getElementById('acceptAllBtn');
    const saveBtn = document.getElementById('saveBtn');
    
    // 检查是否是结果页面（从DOM元素获取数据）
    const resultPageDataElement = document.getElementById('resultPageData');
    if (resultPageDataElement && resultPageDataElement.getAttribute('data-is-result-page') === 'true') {
        initializeResultPage();
    }

    // 设置拖拽上传
    setupDragAndDrop();
    
    // 初始化邮箱输入框控制
    initializeEmailNotificationControl();

    // 修复滚轮事件 - 防止内部滚动影响外部页面
    function setupScrollFix() {
        const scrollableElements = [
            document.getElementById('labeledText'),
            document.getElementById('correctedText'),
            document.getElementById('errorList')
        ];

        scrollableElements.forEach(element => {
            if (element) {
                element.addEventListener('wheel', function(e) {
                    const { scrollTop, scrollHeight, clientHeight } = this;
                    const isScrollingUp = e.deltaY < 0;
                    const isScrollingDown = e.deltaY > 0;
                    
                    // 如果已经滚动到顶部且继续向上滚动，或者滚动到底部且继续向下滚动
                    if ((isScrollingUp && scrollTop === 0) || 
                        (isScrollingDown && scrollTop + clientHeight >= scrollHeight)) {
                        // 阻止事件冒泡到父元素
                        e.preventDefault();
                        e.stopPropagation();
                    }
                }, { passive: false });
            }
        });
    }

    // 初始化滚轮修复
    setupScrollFix();

    // 编辑按钮事件
    editBtn.addEventListener('click', enterEditMode);
    acceptAllBtn.addEventListener('click', acceptAllSuggestions);
    saveBtn.addEventListener('click', saveEdit);

    // 建议选择浮窗事件
    document.getElementById('acceptSuggestion').addEventListener('click', acceptSuggestion);
    document.getElementById('rejectSuggestion').addEventListener('click', rejectSuggestion);

    // 文件选择事件
    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            handleFileSelect(this.files[0]);
        } else {
            fileName.innerHTML = '<i class="fas fa-file-alt"></i><span>未选择文件</span>';
            fileInfo.classList.remove('show');
            startBtn.disabled = true;
        }
    });


    // 开始校对按钮点击事件
    startBtn.addEventListener('click', function() {
        if (fileInput.files.length === 0) {
            showNotification('请先选择文件', 'error');
            return;
        }

        const file = fileInput.files[0];
        if (!isSupportedFileFormat(file.name)) {
            showNotification('仅支持 .md、.docx 和 .doc 格式的文件', 'error');
            return;
        }

        // 开始普通审校（带进度条）
        startNormalProofreadingWithProgress(file);
    });
    
    // 普通审校函数（带进度条）
    function startNormalProofreadingWithProgress(file) {
        // 检查是否启用异步处理
        const enableAsyncCheckbox = document.getElementById('enableAsync');
        const enableAsync = enableAsyncCheckbox && enableAsyncCheckbox.checked;
        
        // 如果启用异步处理
        if (enableAsync) {
            return startAsyncProofreading(file);
        }
        
        // 同步处理（原有逻辑）
        // 禁用所有控件
        disableAllControls();

        // 隐藏结果区域和下载按钮
        resultSection.style.display = 'none';
        downloadControls.classList.remove('show');
        
        // 显示进度条
        showNormalProgress();
        showBottomStatus('正在准备校对...');
        
        // 生成会话ID
        const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        // 禁用开始按钮并显示处理状态
        const originalText = startBtn.innerHTML;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 校对中...';
        startBtn.disabled = true;

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('session_id', sessionId);

        // 启动校对
        fetch('/console/app/document_proofreader_progress/', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'started') {
                // 开始轮询进度
                pollNormalProgress(data.session_id, originalText);
            } else {
                throw new Error(data.message || '启动校对失败');
            }
        })
        .catch(error => {
            console.error('启动校对失败:', error);
            hideNormalProgress();
            hideBottomStatus();
            showNotification('启动校对失败: ' + error.message, 'error');
            
            // 恢复按钮状态
            startBtn.innerHTML = originalText;
            startBtn.disabled = false;
            enableAllControls();
        });
    }
    
    // 异步校对函数
    function startAsyncProofreading(file) {
        console.log('🚀 启动异步校对...');
        
        // 验证邮箱
        const emailInput = document.getElementById('notificationEmail');
        const email = emailInput ? emailInput.value.trim() : '';
        
        if (!email) {
            showNotification('启用后台处理时，必须输入邮箱地址以接收完成通知', 'error');
            emailInput.focus();
            return;
        }
        
        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showNotification('请输入有效的邮箱地址', 'error');
            emailInput.focus();
            return;
        }
        
        
        // 生成用户会话ID
        const userSession = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        // 禁用开始按钮并显示处理状态
        const originalText = startBtn.innerHTML;
        startBtn.innerHTML = '<i class="fas fa-cloud-upload-alt fa-spin"></i> 提交中...';
        startBtn.disabled = true;
        
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('enable_async', 'true');
        formData.append('user_session', userSession);
        formData.append('notification_email', email);

        // 提交异步任务
        fetch('/console/app/document_proofreader/', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'async_started') {
                // 异步任务已启动
                console.log('✅ 异步任务已启动:', data.data.task_id);
                
                // 显示成功提示
                showAsyncStartedNotification(data.data);
                
                // 恢复按钮状态
                startBtn.innerHTML = originalText;
                startBtn.disabled = false;
                
                // 清空文件选择
                const fileInput = document.getElementById('fileInput');
                if (fileInput) {
                    fileInput.value = '';
                }
                
                // 隐藏文件信息
                const fileInfo = document.getElementById('fileInfo');
                if (fileInfo) {
                    fileInfo.classList.remove('show');
                }
                
            } else {
                throw new Error(data.message || '启动异步任务失败');
            }
        })
        .catch(error => {
            console.error('启动异步校对失败:', error);
            showNotification('启动异步校对失败: ' + error.message, 'error');
            
            // 恢复按钮状态
            startBtn.innerHTML = originalText;
            startBtn.disabled = false;
        });
    }
    
    // 显示异步任务启动成功的通知
    function showAsyncStartedNotification(data) {
        const notification = `
            <div class="async-notification" style="
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 1.5rem;
                border-radius: 0.75rem;
                margin: 1rem 0;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            ">
                <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 0.75rem;">
                    <i class="fas fa-rocket" style="font-size: 1.5rem;"></i>
                    <h3 style="margin: 0; font-size: 1.2rem;">异步校对任务已启动</h3>
                </div>
                <p style="margin: 0.5rem 0; opacity: 0.9;">
                    <i class="fas fa-info-circle"></i>
                    任务ID: <strong>${data.task_id}</strong>
                </p>
                <p style="margin: 0.5rem 0; opacity: 0.9;">
                    <i class="fas fa-background"></i>
                    校对任务已在后台开始处理，即使关闭浏览器也会继续运行
                </p>
                <p style="margin: 0.5rem 0; opacity: 0.9;">
                    <i class="fas fa-bell"></i>
                    完成后会发送桌面通知，点击通知可查看结果
                </p>
                <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                    <a href="/console/app/document_proofreader_async_manage/" target="_blank" 
                       style="background: rgba(255,255,255,0.2); color: white; padding: 0.5rem 1rem; 
                              border-radius: 0.5rem; text-decoration: none; font-size: 0.9rem;
                              border: 1px solid rgba(255,255,255,0.3); transition: all 0.2s;">
                        <i class="fas fa-tasks"></i> 查看任务管理
                    </a>
                    <button onclick="this.parentElement.parentElement.remove()" 
                            style="background: rgba(255,255,255,0.2); color: white; padding: 0.5rem 1rem; 
                                   border-radius: 0.5rem; border: 1px solid rgba(255,255,255,0.3); 
                                   cursor: pointer; font-size: 0.9rem; transition: all 0.2s;">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </div>
        `;
        
        // 在上传区域后显示通知
        const uploadSection = document.getElementById('uploadSection');
        if (uploadSection) {
            uploadSection.insertAdjacentHTML('afterend', notification);
        }
        
        // 显示浏览器桌面通知
        if ('Notification' in window && Notification.permission === 'granted') {
            const browserNotification = new Notification('异步校对任务已启动', {
                body: `任务 ${data.task_id} 已在后台开始处理，完成后将通知您`,
                icon: '/static/favicon.ico',
                tag: 'proofreader-async-started'
            });
            
            browserNotification.onclick = function() {
                window.open('/console/app/document_proofreader_async_manage/', '_blank');
                browserNotification.close();
            };
            
            setTimeout(() => {
                browserNotification.close();
            }, 5000);
        }
    }
    
    // 轮询普通审校进度
    function pollNormalProgress(sessionId, originalButtonText) {
        const pollInterval = setInterval(() => {
            fetch(`/console/app/document_proofreader_progress_status/?session_id=${sessionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'processing') {
                        // 更新进度
                        const progressData = data.data;
                        updateNormalProgress(progressData.current, progressData.total);
                    } else if (data.status === 'completed') {
                        // 校对完成
                        clearInterval(pollInterval);
                        handleNormalProofreadingComplete(data.data, originalButtonText);
                    } else if (data.status === 'error') {
                        // 处理错误
                        clearInterval(pollInterval);
                        handleNormalProofreadingError(data.message, originalButtonText);
                    }
                })
                .catch(error => {
                    console.error('轮询进度失败:', error);
                    clearInterval(pollInterval);
                    handleNormalProofreadingError('轮询进度失败: ' + error.message, originalButtonText);
                });
        }, 1000); // 每秒轮询一次
        
        // 设置超时保护（10*6分钟）
        setTimeout(() => {
            clearInterval(pollInterval);
            handleNormalProofreadingError('校对超时，请重试', originalButtonText);
        }, 600000*6);
    }
    
    // 处理普通审校完成
    function handleNormalProofreadingComplete(data, originalButtonText) {
        // 隐藏进度条
        hideNormalProgress();
        hideBottomStatus();
        
        // 计算并显示总耗时
        const totalTime = ((Date.now() - normalProofreadStartTime) / 1000).toFixed(1);
        console.log(`📊 普通审校完成，总耗时: ${totalTime}秒`);
        
        // 保存校对ID和原始带标记文本
        currentProofreaderId = data.id;
        originalLabeledText = data.labeled_text;
        
        // 显示结果区域
        resultSection.style.display = 'block';

        // 填充内容
        renderMarkdown(data.labeled_text, 'labeledText');
        renderMarkdown(data.corrected_text, 'correctedText');

        // 填充建议列表
        const errorListEmpty = document.getElementById('errorListEmpty');
        errorItems.innerHTML = '';
        
        if (data.error_list.length === 0) {
            errorItems.innerHTML = `
                <div class="success-state">
                    <i class="fas fa-check-circle"></i>
                    <p>恭喜！未发现任何错误</p>
                    <small style="color: var(--text-secondary); display: block; margin-top: 0.5rem;">
                        您的文档质量很高！（耗时: ${totalTime}秒）
                    </small>
                </div>
            `;
        } else {
            // 隐藏默认空状态
            if (errorListEmpty) {
                errorListEmpty.style.display = 'none';
            }
            
            data.error_list.forEach(error => {
                const errorItem = document.createElement('div');
                errorItem.className = 'error-item';
                errorItem.id = `error-${error.id}`;
                errorItem.setAttribute('onclick', `jumpToErrorPosition('${error.id}')`); // 添加点击跳转功能
                errorItem.innerHTML = `
                    <button class="add-to-dictionary-btn" onclick="event.stopPropagation(); addToDictionary('${error.id}', '${error.error_text.replace(/'/g, '\\\'')}')" title="加入词库">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="delete-suggestion-btn" onclick="event.stopPropagation(); deleteSuggestionFromList('${error.id}')">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="error-id">错误ID: ${error.id}</div>
                    <div class="error-text">错误文本: ${error.error_text}</div>
                    <div class="error-suggestion">修改建议: ${error.error_suggestion}</div>
                    <div class="error-reason">错误理由: ${error.error_reason}</div>
                `;
                errorItems.appendChild(errorItem);
            });
        }

        // 设置下载链接
        downloadUrls.md = data.download_url_md;
        downloadUrls.docx = data.download_url_docx;
        
        // 切换到"审校后的讲义"标签
        const correctedTabElement = document.querySelector('[data-tab="correctedTab"]');
        if (correctedTabElement) {
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            correctedTabElement.classList.add('active');
            document.getElementById('correctedTab').classList.add('active');
        }
        
        // 更新下载按钮状态
        updateDownloadButtonVisibility();

        // 更新浏览器URL到结果页面
        updateResultPageURL(data.id);

        // 显示成功消息
        showNotification(`文档校对完成！(耗时: ${totalTime}秒)`, 'success');
        
        // 刷新历史记录
        refreshHistoryRecords();
        
        // 恢复按钮状态
        startBtn.innerHTML = originalButtonText;
        startBtn.disabled = false;
        enableAllControls();
    }
    
    // 更新浏览器URL到结果页面
    function updateResultPageURL(proofreaderId) {
        const newUrl = `/console/app/document_proofreader/result/${proofreaderId}/`;
        try {
            // 使用 history.pushState 更新URL而不刷新页面
            window.history.pushState(
                { proofreaderId: proofreaderId }, 
                `校对结果 - ${proofreaderId}`, 
                newUrl
            );
            console.log(`📍 URL已更新到结果页面: ${newUrl}`);
            
            // 显示URL变更提示
            setTimeout(() => {
                showNotification('页面URL已更新，您可以收藏此链接以便再次访问', 'info');
            }, 2000);
        } catch (error) {
            console.warn('⚠️ 更新URL失败:', error);
        }
    }
    
    // 处理普通审校错误
    function handleNormalProofreadingError(errorMessage, originalButtonText) {
        hideNormalProgress();
        hideBottomStatus();
        showNotification('校对失败: ' + errorMessage, 'error');
        
        // 恢复按钮状态
        startBtn.innerHTML = originalButtonText;
        startBtn.disabled = false;
        enableAllControls();
    }

    // 标签切换
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            if (this.classList.contains('disabled')) {
                return;
            }
            
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            tabContents.forEach(content => content.classList.remove('active'));
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
            
            // 切换标签页时更新下载按钮可见性
            updateDownloadButtonVisibility();
        });
    });
    
    // 初始加载时更新下载按钮状态 (确保在DOM加载后，并且在校对结果返回前)
    updateDownloadButtonVisibility();

    // 测试新的正则表达式（开发用，可在生产环境中删除）
    function testMarkdownRegex() {
        const testCases = [
            'normal ~~error~~ (👉id: 1, 修改建议: correct, 错误理由: test👈) text',
            '**树的基本** **~~界沟~~**(👉id: 2, 修改建议: 结构, 错误理由: "界沟"为明显错别字👈) **：打好地基是关键**',
            '~~**周**~~（👉id: 6，修改建议：骤，错误理由："周"是错别字，应为"骤"👈）'
        ];
        
        const regex = /(\**)~~(.*?)~~(\**)\s*\(👉id:\s*(\d+),\s*修改建议:\s*(.*?),\s*错误理由:\s*(.*?)👈\)/g;
        
        testCases.forEach((testCase, index) => {
            const matches = [...testCase.matchAll(regex)];
            console.log(`测试用例 ${index + 1}:`, testCase);
            console.log(`匹配结果:`, matches.length > 0 ? '✅ 成功' : '❌ 失败', matches);
        });
    }
    
    // 运行测试（仅在开发环境）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        testMarkdownRegex();
    }
});

// ========================================
// 词库管理函数
// ========================================

// 添加词到词库
window.addToDictionary = function(errorId, errorText) {
    // 检查是否在编辑模式下
    if (!isEditMode) {
        showNotification('请先进入编辑模式才能添加词库', 'warning');
        return;
    }
    
    // 显示确认对话框
    const confirmMessage = `确定要将"${errorText}"添加到专有名词词库吗？\n\n添加后，该词将在以后的校对中被忽略。`;
    if (!confirm(confirmMessage)) {
        return;
    }

    // 发送请求添加到词库
    fetch('/console/app/proofreading_dictionary/add_from_error/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            proofreader_id: currentProofreaderId,
            error_id: errorId,
            category: '专有名词',
            description: `从校对建议添加: ${errorText}`
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showNotification(data.message, 'success');
            
            // 从界面中移除这个错误项
            const errorItem = document.getElementById(`error-${errorId}`);
            if (errorItem) {
                errorItem.style.transition = 'all 0.3s ease-out';
                errorItem.style.transform = 'translateX(-100%)';
                errorItem.style.opacity = '0';
                
                setTimeout(() => {
                    errorItem.remove();
                    
                    // 检查是否还有错误项
                    const remainingErrors = document.querySelectorAll('.error-item');
                    if (remainingErrors.length === 0) {
                        const errorItemsContainer = document.getElementById('errorItems');
                        errorItemsContainer.innerHTML = `
                            <div class="error-list-empty">
                                <i class="fas fa-clipboard-check"></i>
                                <h4>所有建议已处理完成</h4>
                                <p>您已处理了所有修改建议</p>
                                <div class="tip">可以切换到"审校后的讲义"查看最终结果</div>
                            </div>
                        `;
                    }
                }, 300);
            }
            
            // 从编辑区域中移除对应的建议标记
            if (isEditMode) {
                const labeledTextContainer = document.getElementById('labeledText');
                const suggestionElements = labeledTextContainer.querySelectorAll(`[data-id="${errorId}"]`);
                suggestionElements.forEach(element => {
                    const suggestionText = element.innerHTML;
                    const match = suggestionText.match(/~~(.*?)~~/);
                    if (match) {
                        const originalText = match[1];
                        const textNode = document.createTextNode(originalText);
                        element.parentNode.replaceChild(textNode, element);
                    }
                });
            }
        } else {
            showNotification('添加到词库失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('添加到词库失败: ' + error.message, 'error');
    });
};




// ========================================
// 历史记录功能
// ========================================

// 历史记录全局变量
let historyCurrentPage = 1;
let historyPageSize = 10;

// 加载历史记录
async function loadHistoryRecords(page = 1) {
    try {
        showHistoryLoading();
        
        const response = await fetch(`/console/app/document_proofreader/history/?page=${page}&page_size=${historyPageSize}`);
        const data = await response.json();
        
        if (data.status === 'success') {
            historyCurrentPage = page;
            renderHistoryList(data.data.records);
            renderHistoryPagination(data.data.pagination);
            hideHistoryLoading();
        } else {
            console.error('加载历史记录失败:', data.message);
            showHistoryError('加载历史记录失败');
        }
    } catch (error) {
        console.error('加载历史记录时发生错误:', error);
        showHistoryError('网络错误，请稍后重试');
    }
}

// 显示历史记录加载状态
function showHistoryLoading() {
    document.getElementById('historyLoading').style.display = 'flex';
    document.getElementById('historyList').style.display = 'none';
    document.getElementById('historyEmpty').style.display = 'none';
    document.getElementById('historyPagination').style.display = 'none';
}

// 隐藏历史记录加载状态
function hideHistoryLoading() {
    document.getElementById('historyLoading').style.display = 'none';
}

// 显示历史记录错误
function showHistoryError(message) {
    hideHistoryLoading();
    const historyEmpty = document.getElementById('historyEmpty');
    historyEmpty.style.display = 'flex';
    historyEmpty.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        <p>加载失败</p>
        <small>${message}</small>
    `;
}

// 渲染历史记录列表
function renderHistoryList(records) {
    const historyList = document.getElementById('historyList');
    const historyEmpty = document.getElementById('historyEmpty');
    
    if (!records || records.length === 0) {
        historyList.style.display = 'none';
        historyEmpty.style.display = 'flex';
        historyEmpty.innerHTML = `
            <i class="fas fa-folder-open"></i>
            <p>暂无历史记录</p>
            <small>完成校对后记录将出现在这里</small>
        `;
        return;
    }
    
    historyEmpty.style.display = 'none';
    historyList.style.display = 'block';
    
    historyList.innerHTML = records.map(record => `
        <div class="history-item" onclick="openHistoryRecord(${record.id})" title="点击查看校对结果">
            <div class="history-item-header">
                <i class="history-item-icon fas fa-file-alt"></i>
                <div class="history-item-name" title="${record.file_name}">${record.display_name}</div>
            </div>
            <div class="history-item-time">
                <i class="fas fa-clock"></i>
                ${record.add_time}
            </div>
        </div>
    `).join('');
}

// 渲染历史记录分页
function renderHistoryPagination(pagination) {
    const paginationContainer = document.getElementById('historyPagination');
    const { page, total_pages } = pagination;
    
    if (total_pages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }
    
    paginationContainer.style.display = 'flex';
    
    let html = '';
    
    // 上一页
    if (page > 1) {
        html += `<button onclick="loadHistoryRecords(${page - 1})" title="上一页">
            <i class="fas fa-chevron-left"></i>
        </button>`;
    }
    
    // 页码
    const startPage = Math.max(1, page - 1);
    const endPage = Math.min(total_pages, page + 1);
    
    for (let i = startPage; i <= endPage; i++) {
        html += `<button onclick="loadHistoryRecords(${i})" ${i === page ? 'class="active"' : ''} title="第${i}页">
            ${i}
        </button>`;
    }
    
    // 下一页
    if (page < total_pages) {
        html += `<button onclick="loadHistoryRecords(${page + 1})" title="下一页">
            <i class="fas fa-chevron-right"></i>
        </button>`;
    }
    
    paginationContainer.innerHTML = html;
}

// 打开历史记录
function openHistoryRecord(proofreaderId) {
    // 在新窗口打开结果页面
    const url = `/console/app/document_proofreader/result/${proofreaderId}/`;
    window.open(url, '_blank');
}

// 刷新历史记录
function refreshHistoryRecords() {
    loadHistoryRecords(historyCurrentPage);
}

// 初始化历史记录功能
function initializeHistorySection() {
    // 检查是否是结果页面
    const resultPageData = document.getElementById('resultPageData');
    const isResultPage = resultPageData && resultPageData.getAttribute('data-is-result-page') === 'true';
    
    if (!isResultPage) {
        // 非结果页面才显示历史记录
        const historySection = document.getElementById('historySection');
        if (historySection) {
            historySection.style.display = 'flex';
        }
        
        // 设置正确的网格布局
        const mainContentWrapper = document.querySelector('.main-content-wrapper');
        if (mainContentWrapper) {
            mainContentWrapper.style.gridTemplateColumns = '1fr 320px';
            mainContentWrapper.style.gap = '1.5rem';
        }
        
        // 页面加载时自动加载历史记录
        loadHistoryRecords();
        
        console.log('📚 历史记录功能初始化完成');
    } else {
        console.log('📄 结果页面，跳过历史记录初始化');
    }
}

// ========================================
// 页面初始化时调用历史记录功能
// ========================================

// 在原有的页面加载事件中添加历史记录初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 页面加载完成，初始化功能...');
    
    // 原有的初始化函数
    initializeEmailNotificationControl();
    setupDragAndDrop();
    
    // 检查是否是结果页面
    const resultPageData = document.getElementById('resultPageData');
    if (resultPageData && resultPageData.getAttribute('data-is-result-page') === 'true') {
        initializeResultPage();
    }
    
    // 新增：初始化历史记录功能
    initializeHistorySection();
    
    // 文件选择事件
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });
    }

    // 开始按钮事件
    const startBtn = document.getElementById('startBtn');
    if (startBtn) {
        startBtn.addEventListener('click', function() {
            const fileInput = document.getElementById('fileInput');
            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                
                // 检查是否启用异步处理
                const enableAsync = document.getElementById('enableAsync').checked;
                const notificationEmail = document.getElementById('notificationEmail').value.trim();
                
                if (enableAsync) {
                    if (!notificationEmail) {
                        showNotification('启用后台处理时必须提供邮箱地址', 'warning');
                        document.getElementById('notificationEmail').focus();
                        return;
                    }
                    startAsyncProofreading(file);
                } else {
                    startNormalProofreadingWithProgress(file);
                }
            } else {
                showNotification('请先选择文件', 'warning');
            }
        });
    }

    console.log('✅ 所有功能初始化完成');
});


    </script>
</body>
</html>