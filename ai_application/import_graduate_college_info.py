import os
import django
import csv

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()

from app.models import GraduateCollegeInfo


def import_graduate_college_info():
    """
    导入研究生院校信息数据
    """
    csv_file_path = '新college_info(包含city版).csv'
    
    # 用于去重的集合，存储院校代码
    processed_codes = set()
    
    created_count = 0
    skipped_count = 0
    
    with open(csv_file_path, mode='r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        for row in reader:
            # 获取各字段数据
            code = row.get('code', '').strip()
            name = row.get('name', '').strip()
            department = row.get('主管部门', '').strip()
            province = row.get('所在省', '').strip()
            city = row.get('所在城市', '').strip()
            level = row.get('院校层次', '').strip()
            college_type = row.get('院校性质', '').strip()
            
            # 跳过没有院校代码或名称的记录
            if not code or not name:
                print(f"⚠️  跳过无效记录: code={code}, name={name}")
                skipped_count += 1
                continue
            
            # 检查是否已经处理过该院校代码
            if code in processed_codes:
                print(f"⚠️  跳过重复记录: {code} - {name}")
                skipped_count += 1
                continue
            
            # 检查数据库中是否已存在该院校代码
            if GraduateCollegeInfo.objects.filter(code=code).exists():
                print(f"⚠️  数据库中已存在: {code} - {name}")
                skipped_count += 1
                continue
            
            # 创建新记录
            try:
                GraduateCollegeInfo.objects.create(
                    code=code,
                    name=name,
                    department=department if department else None,
                    province=province if province else None,
                    city=city if city else None,
                    level=level if level else None,
                    type=college_type if college_type else None
                )
                processed_codes.add(code)
                created_count += 1
                print(f"✅ 创建院校记录: {code} - {name} ({province} {city})")
                
            except Exception as e:
                print(f"❌ 创建记录失败: {code} - {name}, 错误: {str(e)}")
                skipped_count += 1
    
    print(f"\n🎉 导入完成！")
    print(f"✅ 成功创建: {created_count} 条记录")
    print(f"⚠️  跳过记录: {skipped_count} 条")
    print(f"📊 数据库中总记录数: {GraduateCollegeInfo.objects.count()}")


def show_imported_data():
    """
    显示导入的数据概览
    """
    print("\n📋 导入的研究生院校信息概览:")
    print("-" * 80)
    
    # 按省份分组显示
    provinces = GraduateCollegeInfo.objects.values_list('province', flat=True).distinct().order_by('province')
    
    for province in provinces:
        if not province:
            continue
            
        print(f"\n🏛️  {province}")
        
        # 获取该省份下的院校（按层次分组）
        levels = GraduateCollegeInfo.objects.filter(
            province=province
        ).values_list('level', flat=True).distinct()
        
        for level in levels:
            if not level:
                continue
                
            colleges = GraduateCollegeInfo.objects.filter(
                province=province,
                level=level
            ).values_list('code', 'name', 'city')[:5]  # 最多显示5个
            
            print(f"  📚 {level}:")
            for code, name, city in colleges:
                city_info = f"({city})" if city and city != province else ""
                print(f"    🎓 {code} - {name} {city_info}")
            
            # 如果有更多记录，显示省略号
            total_count = GraduateCollegeInfo.objects.filter(
                province=province,
                level=level
            ).count()
            
            if total_count > 5:
                print(f"    ... 还有 {total_count - 5} 所院校")
    
    # 显示统计信息
    print(f"\n📊 统计信息:")
    print(f"总院校数: {GraduateCollegeInfo.objects.count()}")
    
    # 按层次统计
    level_stats = GraduateCollegeInfo.objects.values('level').distinct()
    for level_info in level_stats:
        level = level_info['level']
        if level:
            count = GraduateCollegeInfo.objects.filter(level=level).count()
            print(f"{level}: {count} 所")
    
    # 按性质统计
    type_stats = GraduateCollegeInfo.objects.values('type').distinct()
    print(f"\n按院校性质统计:")
    for type_info in type_stats:
        college_type = type_info['type']
        if college_type:
            count = GraduateCollegeInfo.objects.filter(type=college_type).count()
            print(f"{college_type}: {count} 所")


if __name__ == '__main__':
    print("🚀 开始导入研究生院校信息数据...")
    print("📝 数据来源: 新college_info(包含city版).csv")
    print("=" * 60)
    
    # 清空现有数据（可选）
    existing_count = GraduateCollegeInfo.objects.count()
    if existing_count > 0:
        confirm = input(f"⚠️  数据库中已有 {existing_count} 条记录，是否清空后重新导入？(y/N): ")
        if confirm.lower() == 'y':
            GraduateCollegeInfo.objects.all().delete()
            print("🗑️  已清空现有数据")
    
    # 导入数据
    import_graduate_college_info()
    
    # 显示导入结果
    show_imported_data()
