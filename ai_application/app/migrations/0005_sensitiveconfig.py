# Generated by Django 4.2 on 2024-10-15 07:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0004_message_is_sensitive'),
    ]

    operations = [
        migrations.CreateModel(
            name='SensitiveConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('words', models.JSONField(null=True, verbose_name='敏感词列表')),
                ('white_words', models.J<PERSON>NField(null=True, verbose_name='白名单列表')),
                ('app', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.app')),
            ],
            options={
                'verbose_name': '敏感词配置',
                'verbose_name_plural': '敏感词配置',
                'db_table': 'ai_sensitive_config',
            },
        ),
    ]
