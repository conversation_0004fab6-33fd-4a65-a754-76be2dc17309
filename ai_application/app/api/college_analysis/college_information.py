import requests
import json

from django.conf import settings


def regionFilter(rg: list, res: list):
    return [school for school in res if school["region"] in rg]

def levelFilter(lv: int, res: list):
    # 根据数字编码映射到院校级别
    level_mapping = {
        0: ["985工程"],
        1: ["985工程", "211工程"],
        2: ["985工程", "211工程", "双一流大学"],
        6: ["双非院校", "985工程", "211工程", "双一流大学"]
    }
    allowed_levels = level_mapping.get(lv, [])
    return [school for school in res if school["level"] in allowed_levels]

def map_college_level(college_level_str):
    # 将字符串形式的院校级别映射到数字编码
    level_map = {
        "985工程": 0,
        "211工程": 1,
        "双一流大学": 2,
        "双非院校": 6
    }
    return level_map.get(college_level_str, None)

def get_college_and_major_info(major=None, college_level=None, master_degree_type=None, region=None, priority_order=None):
    # 初始化硕士类型编码
    master_degree_type_code = ""
    if master_degree_type == '全日制':
        master_degree_type_code = 1
    elif master_degree_type == '非全日制':
        master_degree_type_code = 2

    # 获取所有专业代码
    major_codes = [m['code'] for m in major] if major else []
    major = major
    # 如果没有找到任何专业代码，直接返回空结果
    if not major_codes:
        return []

    # 构建请求 payload
    payload = {
        "major_code": major_codes,
        "major":major,
        "way": master_degree_type_code or ""
    }
    print(f"payload在这边啊啊啊啊啊啊啊:{payload}")
    if settings.ENVIRONMENT == settings.ENV_PRODUCT:
        host = 'http://yantucs-data-svc:8000'
    else:
        host = 'https://yantucs-data.yantucs.com'
    url = f"{host}/internal_api/v1/college/school_major_query/"
    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    if response.status_code == 200:
        data = response.json().get('data', [])

        # 根据 priority_order 进行筛选
        if priority_order:
            print(f"priority_order:{priority_order}")
            priority_list = priority_order.split(',')
            for priority in priority_list:
                if priority == "region" and region:
                    data = regionFilter(region, data)
                    print(f"根据region进行筛选之后的数据为：{data},\n\n长度为{len(data)}")
                    if len(data) > 30:
                        continue  # 如果长度大于30，继续筛选下一个优先级
                    else:
                        return data  # 如果长度小于等于30，直接返回结果
                elif priority == "college_level" and college_level:
                    # 将字符串形式的院校级别映射为数字编码
                    college_level_code = map_college_level(college_level)
                    if college_level_code is not None:
                        data = levelFilter(college_level_code, data)
                        print(f"根据院校层级进行筛选之后的数据为：{data},\n\n长度为{len(data)}")
                        if len(data) > 30:
                            continue  # 如果长度大于30，继续筛选下一个优先级
                        else:
                            return data  # 如果长度小于等于30，直接返回结果

        return data
    else:
        response.raise_for_status()

