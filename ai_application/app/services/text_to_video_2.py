import os
import subprocess
import shutil

# from app.core.features.text_to_video import *
from app.models.text_to_video import TexttoVideo
from helpers.upload_helper import upload_file


def add_subtitle_to_video(input_video, ass_file, output_video):
    command = [
        'ffmpeg',
        '-i', input_video,
        '-vf', f"ass='{ass_file}'",
        '-c:a', 'copy',
        output_video
    ]
    try:
        subprocess.run(command, check=True)
        print(f"✅ ASS Subtitle added to {output_video}")
    except subprocess.CalledProcessError as e:
        print("❌ Error adding subtitles:", e)

def get_query_knowledge(query):
    knowledge_list = get_knowledge_list(query)
    if not knowledge_list:
        return {'error': True, 'message': '未检索到相关知识点'}
    elif len(knowledge_list) > 1:
        return {'error': True, 'message': '仅支持输入一个知识点，请更精确地输入'}
    
    kp = gene_analysis_report(knowledge_list[0])

    script = gene_sc(
        f'课程名：《数据结构》 知识点名：{kp["name"]} 知识点定义：{kp["definition"]} '
        f'前置知识点:{kp["pre"]} '
        f'知识点讲解：{kp["basic"]} '
        f'案例说明：{kp["case"]} '
        f'总结：{kp["summary"]}'
    )
    return {'error': False, 'script': script, 'kp':kp}


def gene_knowledge_video(kp, script):
    try:
        save_path = f'app/core/features/text_to_video/output_files/{kp["name"]}'
        os.makedirs(save_path, exist_ok=True)

        firstPage('《数据结构》 - ' + kp['name'], kp['definition'], save_path)

        ans = process_ppt_script_list(script)
        for sc in ans:
            if sc['generate_figure'] == True:
                render_mermaid_to_png(sc['code'], f"{save_path}/{sc['id']}.png")

        v_list = [f'{save_path}/scene1.jpeg']
        for idx, sp in enumerate(script[1:], start=1):
            geneContent(sp['title'], sp['text'], save_path, idx + 1)
            v_list.append(f'{save_path}/scene{idx + 1}.jpeg')

        a_list = []
        for s in script:
            geneAudio(s['audio'], save_path, str(s['id']))
            a_list.append(f'{save_path}/scene{s["id"]}.mp3')

        geneVideo(a_list, v_list, save_path)

        GetSubtitle(save_path)
        correct_srt_file(f"{save_path}/subtitle.srt", f"{save_path}/subtitle_corrected.srt")
        srt_to_ass(f"{save_path}/subtitle_corrected.srt", f"{save_path}/subtitle.ass", font_name="思源黑体", font_size=34, spacing=2)
        final_video_path = f'{save_path}/{kp["name"]}.mp4'
        add_subtitle_to_video(f'{save_path}/final_video.mp4', f'{save_path}/subtitle.ass', final_video_path)

        with open(final_video_path, 'rb') as f:
            url = upload_file(f, f'{kp["name"]}.mp4', sub_path='videos')

        TexttoVideo.objects.create(
            name=kp['name'],
            video=url,
            user_info={
                "id":"1",
                "name":"test"
            },
            script=script
        )
        
        shutil.rmtree(save_path)

        print(f"🎉 Done processing: {kp['name']}")
        return {'error': False, 'video_url': url}
        
    except Exception as e:
        print(f"❌ Error processing {kp['name']}: {e}")
        return {'error': True, 'message': str(e)}

