--
-- Create model EnglishWordLibrary
--
CREATE TABLE `ai_data_english_word_library`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `word`          varchar(100) NOT NULL,
    `word_freq`     varchar(32)  NOT NULL,
    `part`          varchar(100) NOT NULL,
    `title1`        varchar(100) NOT NULL,
    `title2`        varchar(100) NOT NULL,
    `title3`        varchar(100) NOT NULL
);
CREATE INDEX `ai_data_english_word_library_add_time_66179b83` ON `ai_data_english_word_library` (`add_time`);
CREATE INDEX `ai_data_english_word_library_word_e69ae059` ON `ai_data_english_word_library` (`word`);