# 摸底 → 基础提升-初级
modi_up_basic_improve_l_report_template = """## 🎉 恭喜您！已成功晋级【基础提升-初级】阶段！
·已完成测试题量：{total_q_num}题
·平均每轮正确率：{q_accuracy}% <span style="color: rgba(0, 0, 0, 0.6);">(目标60%)</span>"""


next_shuati_for_fail = """## ⏸️ 建议暂停刷题，前去巩固基础知识~
练习错误率有些高哦，好好把基础再巩固一下吧！
{weak_points}

调整好状态，满血重启学习之旅 💪"""


next_shuati_for_pass = """## 🎉 恭喜你，完成当前所有题目啦！
太棒了！您已经把目前阶段的所有题目练习完成呢~"""


# 暂停刷题文案，取三个知识点
answer_fail_report_template = """## ⛔暂停刷题，先来巩固基础吧~
练习错误率有些高哦，好好把基础再巩固一下吧！
{weak_points}

暂时的放缓不是落后，而是为了更稳健地成长！💪"""

# 通关文案
answer_pass_report_template = """## 🎉 恭喜你，完成当前所有题目啦！
太棒了！您已经把目前阶段的所有题目练习完成呢~
我们会持续更新更多优质题目，敬请期待！📚"""

# 摸底 → 基础巩固
modi_up_basic_report_template = """## ❌ 很遗憾，您暂未达标，需进入【基础巩固】阶段继续学习。
·已完成测试题量：{total_q_num}题
·平均每轮正确率：{q_accuracy}% <span style="color: rgba(0, 0, 0, 0.6);">(目标60%)</span>

看起来基础部分掌握得还不太扎实哦~ 别急，先把基础巩固好，后面提升起来就会轻松多啦！加油！💪"""

# 基础巩固 → 基础提升-初级
basic_up_improve_l_report_template = """## 🎉 恭喜您！已成功晋级【基础提升-初级】阶段！

·累计已完成测试题量:{total_q_num}题

巩固基础知识的目标已达成， 趁着好状态接着解锁更多知识点，相信你会有更多突破！✨"""

# 其他阶段晋级文案
up_level_report_template = """## 🎉 恭喜您！已成功晋级【{new_stage}】阶段！

·知识点覆盖: {q_knowledge} / {target_q_knowledge}个
·综合题量：{q_num}题 
·核心题型正确率：{core_q_accuracy}%<span style="color: rgba(0, 0, 0, 0.6);">（目标{target_core_q_accuracy}%）</span>

哇，解题越来越灵活了呢！理解得更深，思路也更稳了~ 现在这样就超棒的，保持住这个节奏稳稳前进吧🌟"""

# 其他阶段降级文案
down_level_report_template = """## ❌ 很遗憾，您暂未达标，需重新练习【{new_stage}】阶段！

·知识点覆盖: {q_knowledge} / {target_q_knowledge}个
·综合题量：{q_num}题 
·核心题型正确率：{core_q_accuracy}%<span style="color: rgba(0, 0, 0, 0.6);">（目标{target_core_q_accuracy}%）</span>

基础细节还需要再稳稳打磨呢~正好把核心模块炼得更扎实 💪"""

# 进入强化阶段的文案
strengthen_level_report_template = """## ⚠️ 很遗憾，您暂未达标！

·知识点覆盖: {q_knowledge} / {target_q_knowledge}个
·综合题量：{q_num}题 
·核心题型正确率：{core_q_accuracy}%<span style="color: rgba(0, 0, 0, 0.6);">（目标{target_core_q_accuracy}%）</span>

接下来需进入【阶段强化】连续{next_stage_rounds}轮达标，方可晋级~"""

# # 基础提升-初级 → 基础提升-中级
# improve_l_up_improve_m_report_template = """## 🎉 基础提升-初级已达标！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 哇，应用能力提升得真快，解题越来越灵活了呢！理解得更深，思路也更稳了~ 现在这样就超棒的，保持住这个节奏稳稳前进吧，相信你很快就是解题小能手了！🌟
#
# 推荐进入：【基础提升-中级】模式
# → 深化思维+复杂情境，锤炼综合解题能力"""
#
# # 基础提升-中级 → 基础提升-高级
# improve_m_up_improve_h_report_template = """## 🎉 基础提升-中级已达标！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 哇，解题思路越来越清晰，进步特别稳当呢！分析更准了~ 现在这样就超棒的，保持这个节奏继续突破，准备好挑战更复杂的题目吧！🌟
#
# 推荐进入：【基础提升-高级】模式
# → 策略性拆解+举一反三，制霸高阶复杂问题"""
#
# # 基础提升-高级 → 核心基础
# improve_h_up_core_basic_report_template = """## 🎉 基础提升-高级圆满达成！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 高级基础提升阶段的思维训练让基础功底更犀利了呢！✨ 现在咱们用新视角回头梳理核心模块，查漏补缺效率翻倍~ 保持这个状态夯实升级版地基，解题高手之路走得更稳啦！🌟
#
# 推荐进入：【核心基础】优化模式
# → 全覆盖深度梳理 + 核心模块加固，构建无短板能力体系"""
#
# # 核心基础 → 核心提升-初级
# core_basic_up_core_improve_l_report_template = """## 🎉 核心基础已达标！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 核心基础巩固得真不错，进步特别明显呢！掌握的知识也更扎实了~ 现在这样就超棒的，保持住这个节奏稳稳前进吧，相信你还能更厉害！🌟
#
# 推荐进入：【核心提升-初级】模式
# → 综合应用+拓展题，提升解题力"""
#
# # 核心提升-初级 → 核心提升-中级
# core_improve_l_up_core_improve_m_report_template = """## 🎉 核心提升-初级模式已达标！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 太棒了！初级核心提升模式顺利搞定啦~ 基础技能用得越来越熟练，接下来怎么学也更清楚了呢！现在咱们继续稳稳深化理解、巩固成果，你肯定会越来越强的！🚀
#
# 推荐进入：【核心提升-中级】模式
# → 复杂场景应用题+跨知识点综合题，强化系统解题思维"""
#
# # 核心提升-中级 → 核心提升-高级
# core_improve_m_up_core_improve_h_report_template = """##🎉 核心提升-中级模式已达标！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 太赞了！中级核心提升模式稳稳拿下啦~ 核心技能都掌握得超熟练了，接下来提升的方向也特别清晰！咱们继续这样稳稳地突破自己，你肯定会越来越厉害的！⚡
#
# 推荐进入：【核心提升-高级】模式
# → 高难度创新题+真题压轴题，突破解题能力上限"""
#
# # 降级
# # 基础提升_初级 → 基础巩固
# improve_l_down_basic_report_template = """## 📊 基础提升_初级模式调整通知！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 发现某些基础细节还需要再稳稳打磨呢~正好把核心模块炼得更扎实，等重新挑战提升内容时，你的解题会明显更顺畅！ 💪
#
# 推荐重新进入：🔁 【基础巩固】模式
# → 核心概念闭环梳理 + 高频基础题变式精练"""
#
# # 基础提升_中级 → 基础提升_初级
# improve_m_down_improve_l_report_template = """## 📊 基础提升_中级模式调整通知！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 感觉部分核心模块需要重点强化一下呢~正好回头把应用根基打牢，等重新挑战中级内容时，解题会更轻松更自信！ 💪
#
# 推荐重新进入：🔁【基础提升_初级】模式
# → 关键知识点应用深化 + 综合基础题阶梯训练"""
#
# # 基础提升_高级 → 基础提升_中级
# improve_h_down_improve_m_report_template = """## 📊 基础提升_高级模式调整通知！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 为高级挑战储备更稳的解题体系，现在需要聚焦中级核心模块深度训练哦~重启提升时会事半功倍呢！ 💪
#
# 推荐重新进入：🔁【基础提升_中级】模式
# → 主干思维强化训练 + 典型题型变式精练"""
#
# # 核心基础 → 基础提升_高级
# core_basic_down_improve_h_report_template = """## 📊 基础提升_高级模式调整通知！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 关键模块的掌握度需要重点巩固一下哦~ 正好系统回炉核心地基，等重启提升训练时，解题会少卡点更流畅！ 💪
#
# 推荐重新入：🔁【基础提升_高级】模式
# → 高频考点三遍闭环 + 隐性漏洞靶向突破"""
#
# # 核心提升-初级 → 核心基础
# core_improve_l_down_core_basic_report_template = """## 📊 能力提升-初级模式调整通知！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 看起来咱们要把基础细节再细细打磨一下哦~正好稳稳炼透关键知识点，等重新挑战初级内容时，你解题会比现在更稳更快！ 💪
#
# 推荐重新进入：🔁 【核心基础】模式
# → 核心概念精讲 + 高频典型题变式训练"""
#
# # 核心提升-中级 → 核心提升-初级
# core_improve_m_down_core_improve_l_report_template = """## 📊 核心提升-中级模式调整通知！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 咱们需要把常考重点再强化一遍~正好扎实吃透核心解法，等重返中级内容时，你应对复杂题会更得心应手！💡
#
# 推荐重新进入：🔁【核心提升-初级】模式
# → 针对性补强基础漏洞 + 高频核心题特训"""
#
# # 核心提升-高级 → 核心提升-中级
# core_improve_h_down_core_improve_m_report_template = """## 📊 核心提升-高级模式调整通知！
# ·综合题量：{q_num}题
# ·平均正确率： {accuracy}%
#
# 我们把高阶思维模块再系统整合一遍~正好稳固跨领域解题链路，等再攀高级阶段时，你拆解创新题的灵活度会明显提升！ 🧩
#
# 推荐重新进入：🔁【核心提升-中级】模式
# → 典型综合题深度闯关训练 + 稳步过渡至高阶思维阶段"""
