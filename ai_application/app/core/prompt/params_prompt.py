def comb_prompt_by_params(prompt_dict: dict) -> str:
    prompt_str_arr = []

    role_part = []
    if prompt_dict.get('role'):
        role_part.append(f"## 角色\n{prompt_dict.get('role')}")
    if prompt_dict.get('tone'):
        role_part.append(f"## 情感基调\n{prompt_dict.get('tone')}")
    if role_part:
        role_part_str = '\n'.join(role_part)
        prompt_str_arr.append(f"# 角色定义\n{role_part_str}")

    task_part = []
    if prompt_dict.get('task'):
        task_part.append(f"## 任务\n{prompt_dict.get('task')}")
    if prompt_dict.get('examples'):
        task_part.append(f"## 示例\n{prompt_dict.get('examples')}")
    if task_part:
        task_part_str = '\n'.join(task_part)
        prompt_str_arr.append(f"# 任务步骤\n{task_part_str}")

    instructions_part = []
    if prompt_dict.get('instructions'):
        instructions_part.append(f"## 说明\n{prompt_dict.get('instructions')}")
    if prompt_dict.get('note'):
        instructions_part.append(f"## 注意\n{prompt_dict.get('note')}")
    if instructions_part:
        instructions_part_str = '\n'.join(instructions_part)
        prompt_str_arr.append(f"# 输出约束\n{instructions_part_str}")

    return '\n\n'.join(prompt_str_arr)
