import json
from django.shortcuts import render
from django.http import JsonResponse
from django_ext import base_view
from django_ext.response import make_response, make_stream_response
from app.services.document_proofreader.document_proofreader_traditional import proofread_document_file


class DocumentProofreadTraditionalIndexView(base_view.BaseView):
    """讲义审校主页视图"""
    
    def get(self, request, *args, **kwargs):
        """显示讲义审校页面"""
        context = {
            'page_title': '智能讲义审校工具',
            'default_prompt': self._get_default_prompt(),
            'default_text': self._get_default_text()
        }
        return render(request, 'app/document_proofread_traditional.html', context)
    
    def _get_default_prompt(self):
        """获取默认的审校提示词"""
        return """你是一位专业的文档校对专家，请对以下文本进行仔细校对，检测以下类型的错误：
1. 错别字/别字校对：检测常见的同音字、形近字错误
2. 标点符号校对：检查标点符号的误用、缺失、多余
3. 数字用法校对：检查数字表示法是否符合规范
4. 量和单位表示法：检查物理量、计量单位使用是否标准

请使用以下格式标记错误：
~~错误文本~~👉id: 1, 修改建议: 正确文本, 错误理由: 详细说明, 错误类型: 错误分类👈"""

    def _get_default_text(self):
        """获取默认的测试文本"""
        return """数据结构是一门研究非数值计算的程式设计问题中的操作对象。算法是对特定问题求解步骤的一种描述，它是指令的有限序列。数据结构与算法密不可分，数据结构为算法的实现提供操作对象，而算法的选择依赖于作为基础的数据结构。

在计算机科学中，栈是一种后进先出（LIFO）的数据结构。进站是指将元素添加到栈顶的过程。与之相对的是出栈操作，将栈顶元素移除。"""


class DocumentProofreadTraditionalSubmitView(base_view.BaseView):
    """讲义审校提交处理视图"""
    
    def post(self, request, *args, **kwargs):
        """处理审校请求"""
        try:
            # 获取前端提交的数据
            content = request.data.get('content', '').strip()
            custom_prompt = request.data.get('custom_prompt', '').strip()
            use_dictionary = request.data.get('use_dictionary', False)
            use_conversion_rules = request.data.get('use_conversion_rules', False)
            model_name = request.data.get('model_name') or "deepseek-v3-250324"
            try:
                temperature = float(request.data.get('temperature', 0.1))
            except Exception:
                temperature = 0.1
            
            if not content:
                return make_response({'error': '请输入要审校的文本内容'}, status_code=400)
            
            # 准备校对参数
            dictionary = self._get_dictionary() if use_dictionary else []
            conversion_rules = self._get_conversion_rules() if use_conversion_rules else {}
            
            # 调用审校服务 (添加进度回调支持)
            def progress_callback(current, total):
                print(f"审校进度: {current}/{total}")
            
            labeled_text, result_dict = proofread_document_file(
                content=content,
                progress_callback=progress_callback,
                dictionary=dictionary,
                conversion_rules=conversion_rules,
                model_name=model_name,
                temperature=temperature,
                custom_prompt=custom_prompt
            )
            
            # 构建返回结果
            response_data = {
                'success': True,
                'labeled_text': labeled_text,
                'error_list': result_dict.get('error_list', []),
                'error_count': len(result_dict.get('error_list', [])),
                'original_length': len(content),
                'processed_length': len(labeled_text)
            }
            
            return make_response(response_data)
            
        except Exception as e:
            print(f"审校处理错误: {str(e)}")
            return make_response({
                'success': False,
                'error': f'审校处理失败: {str(e)}'
            }, status_code=500)
    
    def _get_dictionary(self):
        """获取专有名词词库"""
        return [
            "数据结构", "算法", "栈", "队列", "链表", "二叉树", "哈希表",
            "图论", "动态规划", "贪心算法", "分治算法", "回溯算法"
        ]
    
    def _get_conversion_rules(self):
        """获取词汇转换规则"""
        return {
            "程式设计": "程序设计",
            "进站": "入栈",
            "出站": "出栈"
        } 