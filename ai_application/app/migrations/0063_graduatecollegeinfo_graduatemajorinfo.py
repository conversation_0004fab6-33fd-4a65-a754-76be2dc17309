# Generated by Django 4.2 on 2025-07-31 07:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0062_coursepackagecontent_superviseinitstudentstatus_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="GraduateCollegeInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "add_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "modified_time",
                    models.DateTimeField(auto_now=True, verbose_name="修改时间"),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="删除状态"),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True, max_length=10, null=True, verbose_name="院校代码"
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON><PERSON>(
                        blank=True, max_length=255, null=True, verbose_name="院校名称"
                    ),
                ),
                (
                    "department",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="主管部门"
                    ),
                ),
                (
                    "province",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="院校省份"
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="院校城市"
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="院校层次"
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="院校性质"
                    ),
                ),
            ],
            options={
                "verbose_name": "研究生院校信息",
                "verbose_name_plural": "研究生院校信息",
                "db_table": "ai_graduate_college_info",
            },
        ),
        migrations.CreateModel(
            name="GraduateMajorInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "add_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "modified_time",
                    models.DateTimeField(auto_now=True, verbose_name="修改时间"),
                ),
                (
                    "is_deleted",
                    models.BooleanField(default=False, verbose_name="删除状态"),
                ),
                (
                    "graduate_first_discipline",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="研究生一级学科",
                    ),
                ),
                (
                    "graduate_second_category_code",
                    models.CharField(
                        blank=True,
                        max_length=10,
                        null=True,
                        verbose_name="研究生二级门类代码",
                    ),
                ),
                (
                    "graduate_second_category",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="研究生二级门类",
                    ),
                ),
                (
                    "graduate_third_major_code",
                    models.CharField(
                        blank=True,
                        max_length=10,
                        null=True,
                        verbose_name="研究生三级专业代码",
                    ),
                ),
                (
                    "graduate_third_major",
                    models.CharField(
                        blank=True,
                        max_length=255,
                        null=True,
                        verbose_name="研究生三级专业",
                    ),
                ),
            ],
            options={
                "verbose_name": "研究生专业信息",
                "verbose_name_plural": "研究生专业信息",
                "db_table": "ai_graduate_major_info",
            },
        ),
    ]
