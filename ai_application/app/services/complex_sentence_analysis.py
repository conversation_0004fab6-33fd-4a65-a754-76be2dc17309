import pandas as pd


class ComplexSentenceAnalysis:
    def __init__(self, file_path='sentence.csv'):
        self.file_path = file_path
        self.sentence_data = self._load_csv()

    def _load_csv(self):
        """
        加载CSV文件，并尝试使用不同的编码方式。
        """
        try:
            return pd.read_csv(self.file_path, encoding='gbk')  # 尝试使用gbk编码读取
        except UnicodeDecodeError:
            try:
                return pd.read_csv(self.file_path, encoding='gb2312')  # 如果gbk不行，尝试gb2312
            except UnicodeDecodeError:
                return pd.read_csv(self.file_path, encoding='utf-8')  # 最后尝试utf-8

    def _find_sentence_components_row(self, sentence):
        """
        查找句子对应的语法成分，支持完全相同或部分相同匹配，并返回匹配行及成分描述
        """
        if self.sentence_data.empty:
            return None, ""

        matched_rows = self.sentence_data[self.sentence_data['句子'].str.contains(sentence, na=False)]

        if matched_rows.empty:
            return None, ""

        matched_row = matched_rows.iloc[0]
        components = [column for column in matched_row.index if matched_row[column] == '√']
        components_description = ", ".join(components)

        return matched_row, components_description

    def analyze_sentence(self, user_question, client, model, system_message_content):
        """
        分析句子并返回结果。
        """
        # 查找匹配的句子和其语法成分
        matched_row, components_description = self._find_sentence_components_row(user_question)
        if matched_row is not None:
            user_question = matched_row['句子']


        if components_description:
            system_message_content += f"\n以下是该句子检测到的语法成分：{components_description}"
        print(system_message_content)
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_message_content},
                {"role": "user", "content": user_question},
            ],
        )

        answer = response.choices[0].message.content if response.choices else "无法获取分析结果"
        return answer