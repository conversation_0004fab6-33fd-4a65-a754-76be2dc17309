import uuid

from app.core.rag.cleaner.clean_processor import CleanProcessor
from app.core.rag.extractor.entities.extract_setting import ExtractSetting
from app.core.rag.extractor.extract_processor import ExtractProcessor
from app.core.rag.index_processor.index_processor_base import BaseIndexProcessor
from app.core.rag.models.document import Document
from app.core.rag.splitter.markdown_splitter import MarkdownHeaderTextSplitter
from app.core.rag.vdb.vector_factory import Vector
from app.models import Dataset, DatasetDocument
from app.utils import generate_text_hash


class ChatDocIndexProcessor(BaseIndexProcessor):

    def extract(self, extract_setting: ExtractSetting, **kwargs) -> list[Document]:
        text_docs = ExtractProcessor.extract(extract_setting)
        return text_docs

    def transform_special(self, documents: list[Document], **kwargs) -> list[Document]:
        pass

    def transform(self, documents: list[Document], **kwargs) -> list[Document]:
        # TODO pdf提取比较粗糙，分块时需要特殊处理
        processing_rule = kwargs.get('process_rule')
        dataset_document: DatasetDocument = kwargs.get('dataset_document')

        # Split the text documents into nodes.
        document_no = dataset_document.document_no
        file_extension = dataset_document.file_extension

        # markdown文件预处理，按照标题分割文件
        if file_extension in ('md', 'markdown'):
            headers_to_split_on = processing_rule.get('headers_to_split_on')
            if not headers_to_split_on:
                headers_to_split_on = [
                    ("#", "h1"),
                ]
            md_documents = []
            for d in documents:
                md_res = MarkdownHeaderTextSplitter(
                    headers_to_split_on=[
                        ("#", "h1")
                    ],
                    strip_headers=False,
                    remove_images=processing_rule.get('remove_images', False),
                    remove_hyperlinks=processing_rule.get('remove_hyperlinks', False),
                ).split_text(d.page_content)
                # print(len(md_res))
                # raise
                md_documents.extend(md_res)
            documents = md_documents

        all_documents = []
        for document in documents:
            doc_id = str(uuid.uuid4())

            all_documents.append(Document(
                page_content=document.page_content,
                metadata={
                    'document_no': dataset_document.document_no,
                    'doc_id': doc_id,
                    'doc_hash': generate_text_hash(document.page_content),
                }
            ))
        return all_documents

    def load(self, dataset: Dataset, documents: list[Document]):
        vector = Vector(dataset)
        vector.create(documents)

    def clean(self, dataset: Dataset, node_ids: list[str] | None):
        vector = Vector(dataset)
        if node_ids:
            vector.delete_by_doc_ids(node_ids)
        else:
            vector.delete()
