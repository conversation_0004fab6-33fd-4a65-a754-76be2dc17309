import re
import jieba.analyse
from django.conf import settings

from app.core.features.base_feature import BaseFeature


class KnowledgeKeywordExtractFeature(BaseFeature):

    def __init__(self):
        stopwords_path = settings.BASE_DIR.joinpath('app/subtitle_stopwords.txt')
        subtitle_dict_path = str(settings.BASE_DIR.joinpath('app/subtitle_dict.txt'))
        with open(subtitle_dict_path, 'r') as f:
            res = f.read()
        res_arr = res.split('\n')
        custom_kw_list = []
        for line in res_arr:
            custom_kw_list.append(line.split(' ')[0])
        self.custom_kw_list = custom_kw_list

        jieba.analyse.set_stop_words(stopwords_path)
        jieba.load_userdict(subtitle_dict_path)

    def run(self, text: str):
        top_k = 20
        # 去除换行符和多余的空格
        cleaned_text = re.sub(r'\n+', ' ', text)
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

        # 使用 TF-IDF 提取关键词
        keywords = jieba.analyse.extract_tags(cleaned_text, topK=top_k, withWeight=True)

        data = []
        for keyword, weight in keywords:
            # 纯数字忽略
            if keyword.isdigit():
                continue
            if weight < 0.1:
                continue
            if keyword not in self.custom_kw_list:
                continue
            data.append((keyword, weight))
        return data
