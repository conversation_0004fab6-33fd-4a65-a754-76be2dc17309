import json
import os
import uuid
import tempfile
import pypandoc

from django.core.cache import cache
from django.conf import settings
from django.db.models import Count
from django.http import JsonResponse, FileResponse 
from django.shortcuts import render
from django.views.generic import TemplateView, View

from app.models.document_proofreader import Document<PERSON>roofreader, DocumentProofreaderError, ProofreadingDictionary, DocumentProofreaderAsyncTask
from app.services.document_proofreader.document_proofreader_fun import proofread_document_file
from app.services.document_proofreader.parsers.markdown_to_docx import convert_markdown_to_docx
from app.services.document_proofreader.parsers.word_parser import WordParser
from app.services.document_proofreader.celery_tasks.document_proofreader_async_task import async_document_proofreader_task
from django_ext.base_view import BaseView


class DocumentProofreaderView(View):
    """文档校对视图"""
    def get(self, request, task_id=None, filename=None, format_type=None):
        """处理文件下载请求"""
        if task_id and filename:
            parts = task_id.split('_')
            if len(parts) >= 3:
                proofreader_id = parts[2]  
                proofreader = DocumentProofreader.objects.get(id=proofreader_id)

                content = proofreader.corrected_content

                if format_type == 'docx':
                    docx_buffer = convert_markdown_to_docx(content)
                    
                    # 更改文件扩展名为.docx
                    docx_filename = filename.replace('.md', '.docx')
                    
                    response = FileResponse(
                        docx_buffer,
                        as_attachment=True,
                        filename=docx_filename,
                        content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    )
                    return response
                else:
                    from io import BytesIO
                    content_io = BytesIO(content.encode('utf-8'))
                    # 返回文件响应
                    response = FileResponse(
                        content_io,
                        as_attachment=True,
                        filename=filename
                    )
                    return response
            else:
                return JsonResponse({'status': 'error', 'message': '无效的任务ID格式'}, status=400)
        return render(request, 'app/document_proofreader.html')
    
    def post(self, request):
        """处理文档校对请求"""
        try:
            if 'file' not in request.FILES:
                return JsonResponse({'status': 'error', 'message': '请上传文件'})
            
            file = request.FILES['file']
            file_extension = os.path.splitext(file.name)[1].lower()

            if file_extension not in ['.md', '.docx', '.doc']:
                return JsonResponse({'status': 'error', 'message': '仅支持.md、.docx和.doc格式的文件'})

            # 检查是否启用异步处理
            enable_async = request.POST.get('enable_async', 'false').lower() == 'true'
            user_session = request.POST.get('user_session', str(uuid.uuid4()))
            notification_email = request.POST.get('notification_email', '').strip()

            print(f"📚 收到校对请求，文件格式: {file_extension}, 异步处理: {enable_async}")
            
            # 处理文件内容
            original_content = ""
            converted_from_doc = False
            converted_from_docx = False
            temp_files_to_cleanup = []
            try:
                if file_extension == '.md':
                    # 直接读取markdown文件内容
                    original_content = file.read().decode('utf-8')
                
                elif file_extension == '.doc':
                    # 处理doc文件：doc -> docx -> markdown
                    converted_from_doc = True
                    
                    temp_docx_path = None  # 初始化变量
                    with tempfile.NamedTemporaryFile(suffix='.doc', delete=False) as temp_doc:
                        for chunk in file.chunks():
                            temp_doc.write(chunk)
                        temp_doc_path = temp_doc.name
                    temp_files_to_cleanup.append(temp_doc_path)
                    
                    try:
                        # 转换doc到docx
                        temp_docx_path = self._convert_doc_to_docx(temp_doc_path)
                        temp_files_to_cleanup.append(temp_docx_path)
                        # 转换docx到markdown
                        parser = WordParser(temp_docx_path)
                        parser.parse()
                        content_list = parser.get_content()
                        original_content = '\n'.join(content_list)
                    except Exception as e:
                        raise Exception(f"doc文件转换失败: {str(e)}")

                elif file_extension == '.docx':
                    # 处理docx文件，先转换为markdown
                    converted_from_docx = True
                    
                    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                        for chunk in file.chunks():
                            temp_file.write(chunk)
                        temp_file_path = temp_file.name
                    temp_files_to_cleanup.append(temp_file_path)

                    parser = WordParser(temp_file_path)
                    parser.parse()
                    content_list = parser.get_content()
                    original_content = '\n'.join(content_list)
                    print(f"✅ docx文件转换完成，内容长度: {len(original_content)} 字符")
                
                # 如果启用异步处理
                if enable_async:
                    # 验证邮箱
                    if not notification_email:
                        return JsonResponse({'status': 'error', 'message': '启用后台处理时必须提供邮箱地址'})
                    
                    # 简单的邮箱格式验证
                    import re
                    email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
                    if not re.match(email_pattern, notification_email):
                        return JsonResponse({'status': 'error', 'message': '邮箱地址格式不正确'})
                    
                    # 创建异步任务记录
                    task_id = f"proofreader_async_{uuid.uuid4().hex}"
                    async_task = DocumentProofreaderAsyncTask.objects.create(
                        task_id=task_id,
                        file_name=file.name,
                        original_content=original_content,
                        user_session=user_session,
                        notification_email=notification_email,
                        status='pending'
                    )
                    
                    # 提交到Celery队列
                    async_document_proofreader_task.delay(
                        task_id=task_id,
                        file_name=file.name,
                        original_content=original_content,
                        file_extension=file_extension
                    )
                    
                    return JsonResponse({
                        'status': 'async_started',
                        'data': {
                            'task_id': task_id,
                            'async_task_id': async_task.id,
                            'message': '异步校对任务已启动，关闭页面也会继续处理',
                            'enable_async': True
                        }
                    })
                
                # 同步处理（原有逻辑）
                # 创建校对记录
                proofreader = DocumentProofreader.objects.create(
                    file_name=file.name,
                    original_content=original_content,
                    status='processing'
                )
                
                # 定义进度回调函数
                def progress_callback(current, total):
                    print(f"📊 进度更新: {current}/{total} ({(current/total)*100:.1f}%)")
                
                # 调用校对函数
                corrected_text, labeled_text, result_dict = proofread_document_file(
                    original_content, 
                    progress_callback=progress_callback
                )
                
                # 更新校对记录
                proofreader.corrected_content = corrected_text
                proofreader.labeled_content = labeled_text
                proofreader.status = 'completed'
                proofreader.save()

                # 保存错误列表
                error_list = result_dict.get('error_list', [])
                for error in error_list:
                    DocumentProofreaderError.objects.create(
                        proofreader=proofreader,
                        error_id=error.get('id', ''),
                        error_text=error.get('error_text', ''),
                        error_reason=error.get('error_reason', ''),
                        error_suggestion=error.get('error_suggestion', '')
                    )

                # 创建任务目录并保存文件
                original_filename_without_ext = os.path.splitext(file.name)[0]
                download_filename_md = f"{original_filename_without_ext}_corrected.md"
                download_filename_docx = f"{original_filename_without_ext}_corrected.docx"
                
                # 生成安全的文件名
                original_filename_safe = "".join([c for c in file.name if c.isalnum() or c in (' ', '.', '_')]).rstrip()
                task_dir_name = f'proofreader_task_{proofreader.id}_{original_filename_safe}'
                
                task_directory = os.path.join(settings.MEDIA_ROOT, 'document_proofreader', task_dir_name)
                os.makedirs(task_directory, exist_ok=True)
                
                # 保存MD文件
                md_file_path = os.path.join(task_directory, download_filename_md)
                with open(md_file_path, 'w', encoding='utf-8') as f:
                    f.write(corrected_text)
                
                # 转换并保存Word文档
                docx_file_path = os.path.join(task_directory, download_filename_docx)
                convert_markdown_to_docx(corrected_text, docx_file_path)
                
                # 更新任务目录
                proofreader.task_directory = task_dir_name
                proofreader.save()
                
                # 构建响应数据
                response_data = {
                    'status': 'success',
                    'data': {
                        'id': proofreader.id,
                        'labeled_text': labeled_text,
                        'corrected_text': corrected_text,
                        'error_list': error_list,
                        'converted_from_doc': converted_from_doc,  # 标记是否从doc转换而来
                        'converted_from_docx': converted_from_docx,  # 标记是否从docx转换而来
                        'original_format': file_extension,  # 原始文件格式
                        'download_url_md': f'/console/app/document_proofreader/{task_dir_name}/{download_filename_md}/',
                        'download_url_docx': f'/console/app/document_proofreader/{task_dir_name}/{download_filename_docx}/docx/'
                    }
                }
                
                return JsonResponse(response_data)
            finally:
                # 清理临时文件
                for temp_file in temp_files_to_cleanup:
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
                        print(f"🗑️ 清理临时文件: {temp_file}")
            
        except Exception as e:
            print(f"❌ 校对过程中发生错误: {str(e)}")
            return JsonResponse({'status': 'error', 'message': f'校对失败: {str(e)}'})

    def _convert_doc_to_docx(self, doc_file_path):
        """将doc文件转换为docx文件"""
        # 创建临时的docx文件
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_docx:
            temp_docx_path = temp_docx.name
        
        try:
            # 使用pypandoc转换doc到docx
            pypandoc.convert_file(
                doc_file_path, 
                'docx', 
                outputfile=temp_docx_path,
                extra_args=['--extract-media=temp_media']
            )
            return temp_docx_path
        except Exception as e:
            if os.path.exists(temp_docx_path):
                os.unlink(temp_docx_path)
            raise Exception(f"doc到docx转换失败: {str(e)}")


class DocumentProofreaderDeleteSuggestionView(View):
    """删除修改建议"""
    def post(self, request):
        try:
            data = json.loads(request.body)
            proofreader_id = data.get('proofreader_id')
            error_id = data.get('error_id')
            
            if not proofreader_id or not error_id:
                return JsonResponse({'status': 'error', 'message': '缺少必要参数'})
            
            # 查找并软删除错误记录
            error_record = DocumentProofreaderError.objects.filter(
                proofreader_id=proofreader_id,
                error_id=error_id,
                is_deleted=False
            ).first()
            
            if error_record:
                error_record.soft_delete()
                
                # 清理对应的缓存，确保下次访问时能获取最新数据
                cache_key = f"proofreader_result_{proofreader_id}"
                cache.delete(cache_key)
                print(f"🗑️ 已清理校对结果缓存，ID: {proofreader_id}")
                
                return JsonResponse({'status': 'success', 'message': '建议已删除'})
            else:
                return JsonResponse({'status': 'error', 'message': '未找到对应的建议记录'})
                
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'删除建议失败: {str(e)}'}, status=500)


class DocumentProofreaderSaveEditView(View):
    """保存编辑后的内容"""
    def post(self, request):
        try:
            data = json.loads(request.body)
            proofreader_id = data.get('proofreader_id')
            corrected_content = data.get('corrected_content')
            
            if not proofreader_id or not corrected_content:
                return JsonResponse({'status': 'error', 'message': '缺少必要参数'})
            
            # 更新校对记录
            proofreader = DocumentProofreader.objects.filter(id=proofreader_id).first()
            if not proofreader:
                return JsonResponse({'status': 'error', 'message': '未找到校对记录'})
            
            # 智能清理HTML标签，专门处理富文本编辑器产生的HTML
            import re
            import html
            
            # 先处理HTML实体
            clean_content = html.unescape(corrected_content)
            
            # 第一步：处理常见的富文本编辑器HTML标签
            # 将<br>标签转换为换行符
            clean_content = re.sub(r'<br\s*/?>', '\n', clean_content)
            
            # 特殊处理contenteditable产生的嵌套DIV结构
            # 先处理嵌套的<div><br></div>模式（空行）
            clean_content = re.sub(r'<div[^>]*>\s*<br\s*/?>\s*</div>', '\n', clean_content)
            # 处理空的<div></div>
            clean_content = re.sub(r'<div[^>]*>\s*</div>', '\n', clean_content)
            # 将<div>标签转换为换行符（保持段落结构）
            clean_content = re.sub(r'<div[^>]*>', '\n', clean_content)
            clean_content = re.sub(r'</div>', '', clean_content)
            
            # 将<p>标签转换为双换行符（保持段落间距）
            clean_content = re.sub(r'<p[^>]*>', '\n\n', clean_content)
            clean_content = re.sub(r'</p>', '', clean_content)
            
            # 第二步：保留重要的Markdown相关HTML标签，移除其他HTML标签
            # 定义需要保留的HTML标签（主要是Markdown中可能用到的）
            preserved_tags = [
                'table', 'tr', 'td', 'th', 'thead', 'tbody', 'tfoot',  # 表格相关
                'ul', 'ol', 'li',  # 列表（虽然Markdown有自己的列表语法）
                'blockquote',  # 引用
                'pre', 'code',  # 代码块
                'strong', 'em', 'b', 'i',  # 基本格式化（对应**和*）
                'a',  # 链接
                'img'  # 图片
            ]
            
            # 构建保留标签的正则表达式
            preserved_pattern = '|'.join(preserved_tags)
            
            # 移除不在保留列表中的HTML标签，但保留其内容
            unwanted_tags_pattern = r'<(?!/?(?:' + preserved_pattern + r')\b)[^>]*>'
            clean_content = re.sub(unwanted_tags_pattern, '', clean_content)
            
            # 第三步：清理多余的空白字符，但保持Markdown结构
            # 处理多余的换行符，但保持Markdown的结构需求
            clean_content = re.sub(r'\n{4,}', '\n\n\n', clean_content)  # 最多保留3个连续换行
            
            # 逐行处理，保持缩进和格式
            lines = clean_content.split('\n')
            cleaned_lines = []
            for line in lines:
                # 对于非空行，保留开头的空格（可能是缩进）
                if line.strip():
                    # 检测行首是否有缩进空格
                    leading_spaces = len(line) - len(line.lstrip())
                    content = line.strip()
                    # 合并行内多个空格为单个空格，但保留Markdown语法需要的空格
                    content = re.sub(r'[ \t]+', ' ', content)
                    # 如果原来有缩进，保留适当的缩进
                    if leading_spaces > 0 and (content.startswith('-') or content.startswith('*') or re.match(r'^\d+\.', content)):
                        # 对于列表项，使用标准的2个空格缩进
                        indent_level = min(leading_spaces // 2, 4)  # 最多4级缩进
                        cleaned_lines.append('  ' * indent_level + content)
                    else:
                        cleaned_lines.append(content)
                else:
                    # 空行保持为空行
                    cleaned_lines.append('')
            
            clean_content = '\n'.join(cleaned_lines)
            
            # 第四步：修复常见的Markdown格式问题
            # 确保标题前后有适当的空行
            clean_content = re.sub(r'\n(#{1,6}\s)', r'\n\n\1', clean_content)
            clean_content = re.sub(r'(#{1,6}.*)\n([^#\n])', r'\1\n\n\2', clean_content)
            
            # 确保列表项格式正确，但不要添加过多空行
            clean_content = re.sub(r'\n\n+(-\s)', r'\n\n\1', clean_content)  # 列表前最多一个空行
            clean_content = re.sub(r'\n\n+(\d+\.\s)', r'\n\n\1', clean_content)  # 数字列表前最多一个空行
            
            # 清理过多的连续空行
            clean_content = re.sub(r'\n{3,}', '\n\n', clean_content)
            
            # 第五步：特殊处理contenteditable可能产生的问题
            # 清理可能残留的HTML实体
            clean_content = re.sub(r'&nbsp;', ' ', clean_content)
            clean_content = re.sub(r'&lt;', '<', clean_content)
            clean_content = re.sub(r'&gt;', '>', clean_content)
            clean_content = re.sub(r'&amp;', '&', clean_content)
            
            # 清理可能的零宽字符
            clean_content = re.sub(r'[\u200b-\u200d\ufeff]', '', clean_content)
            
            # 最终清理：移除开头和结尾的多余空白
            clean_content = clean_content.strip()
            
            # 确保内容不为空
            if not clean_content:
                return JsonResponse({'status': 'error', 'message': '清理后的内容为空，请检查输入内容'})
            
            proofreader.corrected_content = clean_content
            proofreader.save()
            
            return JsonResponse({'status': 'success', 'message': '保存成功'})
                
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'保存失败: {str(e)}'}, status=500)


class DocumentProofreaderProgressView(View):
    """支持实时进度的文档校对视图"""
    
    def _convert_doc_to_docx(self, doc_file_path):
        """将doc文件转换为docx文件"""
        # 创建临时的docx文件
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_docx:
            temp_docx_path = temp_docx.name
        
        try:
            # 使用pypandoc转换doc到docx
            pypandoc.convert_file(
                doc_file_path, 
                'docx', 
                outputfile=temp_docx_path,
                extra_args=['--extract-media=temp_media']
            )
            return temp_docx_path
        except Exception as e:
            if os.path.exists(temp_docx_path):
                os.unlink(temp_docx_path)
            raise Exception(f"doc到docx转换失败: {str(e)}")
    
    def post(self, request):
        """处理带进度回调的文档校对请求"""
        try:
            # 检查是否有文件上传
            if 'file' not in request.FILES:
                return JsonResponse({'status': 'error', 'message': '请上传文件'})
            
            file = request.FILES['file']
            file_extension = os.path.splitext(file.name)[1].lower()
            
            # 扩展支持的文件格式，加入.doc
            if file_extension not in ['.md', '.docx', '.doc']:
                return JsonResponse({'status': 'error', 'message': '仅支持.md、.docx和.doc格式的文件'})

            # 获取会话ID参数
            session_id = request.POST.get('session_id', str(uuid.uuid4()))
            print(f"📚 收到带进度的校对请求，会话ID: {session_id}")
            
            # 处理文件内容
            original_content = ""
            converted_from_doc = False
            converted_from_docx = False
            
            if file_extension == '.md':
                original_content = file.read().decode('utf-8')
                
            elif file_extension == '.doc':
                converted_from_doc = True
                print("🔄 开始转换doc文件...")
                
                temp_docx_path = None  # 初始化变量
                with tempfile.NamedTemporaryFile(suffix='.doc', delete=False) as temp_doc:
                    for chunk in file.chunks():
                        temp_doc.write(chunk)
                    temp_doc_path = temp_doc.name
                
                try:
                    # 转换doc到docx
                    temp_docx_path = self._convert_doc_to_docx(temp_doc_path)
                    print("✅ doc转换为docx完成")
                    
                    # 转换docx到markdown
                    parser = WordParser(temp_docx_path)
                    parser.parse()
                    content_list = parser.get_content()
                    original_content = '\n'.join(content_list)
                    print(f"✅ 文件转换完成，内容长度: {len(original_content)} 字符")
                    
                finally:
                    # 清理临时文件
                    temp_files_to_cleanup = [temp_doc_path]
                    if temp_docx_path:  # 只有在成功创建时才清理
                        temp_files_to_cleanup.append(temp_docx_path)
                    for temp_file in temp_files_to_cleanup:
                        if os.path.exists(temp_file):
                            os.unlink(temp_file)
                            
            elif file_extension == '.docx':
                converted_from_docx = True
                print("🔄 开始转换docx文件为markdown格式...")
                
                with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                    for chunk in file.chunks():
                        temp_file.write(chunk)
                    temp_file_path = temp_file.name
                
                try:
                    parser = WordParser(temp_file_path)
                    parser.parse()
                    content_list = parser.get_content()
                    original_content = '\n'.join(content_list)
                    print(f"✅ docx文件转换完成，内容长度: {len(original_content)} 字符")
                finally:
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
            
            # 创建校对记录
            proofreader = DocumentProofreader.objects.create(
                file_name=file.name,
                original_content=original_content,
                status='processing'
            )
            
            # 使用缓存存储进度信息
            progress_key = f"proofreader_progress_{session_id}"
            cache.set(progress_key, {
                'current': 0, 
                'total': 1, 
                'status': 'starting',
                'proofreader_id': proofreader.id
            }, timeout=3600)
            
            # 在后台异步执行校对任务
            import threading
            
            def async_proofread():
                try:
                    # 定义进度回调函数
                    def progress_callback(current, total):
                        progress_data = {
                            'current': current,
                            'total': total,
                            'progress': (current / total) * 100 if total > 0 else 0,
                            'status': 'processing' if current < total else 'completing',
                            'proofreader_id': proofreader.id
                        }
                        cache.set(progress_key, progress_data, timeout=3600)
                        print(f"📊 进度缓存更新: {current}/{total} ({progress_data['progress']:.1f}%)")
                    
                    # 调用校对函数
                    corrected_text, labeled_text, result_dict = proofread_document_file(
                        original_content, 
                        progress_callback=progress_callback
                    )
                    
                    # 更新校对记录
                    proofreader.corrected_content = corrected_text
                    proofreader.labeled_content = labeled_text
                    proofreader.status = 'completed'
                    proofreader.save()

                    # 保存错误列表
                    error_list = result_dict.get('error_list', [])
                    for error in error_list:
                        DocumentProofreaderError.objects.create(
                            proofreader=proofreader,
                            error_id=error.get('id', ''),
                            error_text=error.get('error_text', ''),
                            error_reason=error.get('error_reason', ''),
                            error_suggestion=error.get('error_suggestion', '')
                        )

                    # 创建任务目录并保存文件
                    original_filename_without_ext = os.path.splitext(file.name)[0]
                    download_filename_md = f"{original_filename_without_ext}_corrected.md"
                    download_filename_docx = f"{original_filename_without_ext}_corrected.docx"
                    
                    original_filename_safe = "".join([c for c in file.name if c.isalnum() or c in (' ', '.', '_')]).rstrip()
                    task_dir_name = f'proofreader_task_{proofreader.id}_{original_filename_safe}'
                    
                    task_directory = os.path.join(settings.MEDIA_ROOT, 'document_proofreader', task_dir_name)
                    os.makedirs(task_directory, exist_ok=True)
                    
                    # 保存MD文件
                    md_file_path = os.path.join(task_directory, download_filename_md)
                    with open(md_file_path, 'w', encoding='utf-8') as f:
                        f.write(corrected_text)
                    
                    # 转换并保存Word文档
                    docx_file_path = os.path.join(task_directory, download_filename_docx)
                    convert_markdown_to_docx(corrected_text, docx_file_path)
                    
                    # 更新任务目录
                    proofreader.task_directory = task_dir_name
                    proofreader.save()
                    
                    # 更新进度缓存为完成状态
                    cache.set(progress_key, {
                        'current': error_list.__len__() if error_list else 0,
                        'total': error_list.__len__() if error_list else 1,
                        'progress': 100,
                        'status': 'completed',
                        'proofreader_id': proofreader.id
                    }, timeout=3600)
                    
                    print(f"✅ 带进度的校对完成！校对ID: {proofreader.id}")
                    
                except Exception as e:
                    print(f"❌ 后台校对失败: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    
                    # 更新为失败状态
                    proofreader.status = 'failed'
                    proofreader.save()
                    
                    cache.set(progress_key, {
                        'status': 'error',
                        'error': str(e),
                        'proofreader_id': proofreader.id
                    }, timeout=3600)
            
            # 启动后台线程
            thread = threading.Thread(target=async_proofread)
            thread.daemon = True
            thread.start()
            
            # 返回会话ID，让前端开始轮询进度
            return JsonResponse({
                'status': 'started',
                'session_id': session_id,
                'proofreader_id': proofreader.id,
                'message': '校对已开始，请通过session_id查询进度'
            })
            
        except Exception as e:
            print(f"❌ 启动校对时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': f'启动校对失败: {str(e)}'})


class DocumentProofreaderProgressStatusView(View):
    """查询文档校对进度状态"""
    def get(self, request):
        session_id = request.GET.get('session_id')
        if not session_id:
            return JsonResponse({'status': 'error', 'message': '缺少session_id参数'})
        
        progress_key = f"proofreader_progress_{session_id}"
        progress_data = cache.get(progress_key)
        
        if not progress_data:
            return JsonResponse({'status': 'error', 'message': '未找到对应的校对任务'})
        
        # 检查是否有错误
        if progress_data.get('status') == 'error':
            return JsonResponse({
                'status': 'error',
                'message': progress_data.get('error', '校对过程中发生未知错误')
            })
        
        # 检查是否完成
        if progress_data.get('status') == 'completed':
            # 获取完成的校对结果
            proofreader_id = progress_data.get('proofreader_id')
            try:
                proofreader = DocumentProofreader.objects.get(id=proofreader_id)
                if proofreader.status == 'completed':
                    # 返回完成的结果
                    error_list = list(proofreader.documentproofreadererror_set.filter(is_deleted=False).values(
                        'error_id', 'error_text', 'error_reason', 'error_suggestion'
                    ))
                    # 重命名字段以匹配前端期望
                    for error in error_list:
                        error['id'] = error.pop('error_id')
                    
                    # 创建下载文件路径
                    original_filename_without_ext = os.path.splitext(proofreader.file_name)[0]
                    download_filename_md = f"{original_filename_without_ext}_corrected.md"
                    download_filename_docx = f"{original_filename_without_ext}_corrected.docx"
                    
                    # 使用保存的task_directory或重建
                    if proofreader.task_directory:
                        task_dir_name = proofreader.task_directory
                    else:
                        original_filename_safe = "".join([c for c in proofreader.file_name if c.isalnum() or c in (' ', '.', '_')]).rstrip()
                        task_dir_name = f'proofreader_task_{proofreader.id}_{original_filename_safe}'
                    
                    return JsonResponse({
                        'status': 'completed',
                        'data': {
                            'id': proofreader.id,
                            'labeled_text': proofreader.labeled_content,
                            'corrected_text': proofreader.corrected_content,
                            'error_list': error_list,
                            'download_url_md': f'/console/app/document_proofreader/{task_dir_name}/{download_filename_md}/',
                            'download_url_docx': f'/console/app/document_proofreader/{task_dir_name}/{download_filename_docx}/docx/'
                        }
                    })
            except DocumentProofreader.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': '校对记录不存在'})
        
        # 返回进行中的状态
        return JsonResponse({
            'status': 'processing',
            'data': progress_data
        })


# =============================================================================
# 词库管理相关视图
# =============================================================================

class ProofreadingDictionaryView(BaseView):
    """词库管理视图"""
    permission_classes = []
    
    def get(self, request):
        """获取词库列表"""
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            category = request.GET.get('category', '')
            search = request.GET.get('search', '')
            
            # 构建查询条件
            queryset = ProofreadingDictionary.objects.filter(is_deleted=False, is_active=True)
            
            if category:
                queryset = queryset.filter(category=category)
            
            if search:
                queryset = queryset.filter(word__icontains=search)
            
            # 分页
            total = queryset.count()
            start = (page - 1) * page_size
            end = start + page_size
            
            words = list(queryset[start:end].values(
                'id', 'word', 'category', 'description', 'usage_count', 'add_time'
            ))
            
            # 格式化时间
            for word in words:
                word['add_time'] = word['add_time'].strftime('%Y-%m-%d %H:%M:%S')
            
            # 获取分类统计
            categories = ProofreadingDictionary.objects.filter(
                is_deleted=False, is_active=True
            ).values('category').annotate(count=Count('id')).order_by('category')
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'words': words,
                    'categories': list(categories),
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total,
                        'total_pages': (total + page_size - 1) // page_size
                    }
                }
            })
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})
    
    def post(self, request):
        """添加词到词库"""
        try:
            data = json.loads(request.body)
            word = data.get('word', '').strip()
            category = data.get('category', '专有名词').strip()
            description = data.get('description', '').strip()
            
            if not word:
                return JsonResponse({'status': 'error', 'message': '词语不能为空'})
            
            # 检查词是否已存在
            existing_word = ProofreadingDictionary.objects.filter(
                word=word, is_deleted=False
            ).first()
            
            if existing_word:
                # 如果词已存在，增加使用次数
                existing_word.increment_usage()
                return JsonResponse({
                    'status': 'success', 
                    'message': f'词语"{word}"已存在于词库中，使用次数已更新',
                    'data': {
                        'id': existing_word.id,
                        'word': existing_word.word,
                        'category': existing_word.category,
                        'usage_count': existing_word.usage_count
                    }
                })
            
            # 创建新词
            new_word = ProofreadingDictionary.objects.create(
                word=word,
                category=category,
                description=description,
                usage_count=1
            )
            
            return JsonResponse({
                'status': 'success',
                'message': f'词语"{word}"已添加到词库',
                'data': {
                    'id': new_word.id,
                    'word': new_word.word,
                    'category': new_word.category,
                    'description': new_word.description,
                    'usage_count': new_word.usage_count
                }
            })
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})
    
    def delete(self, request):
        """删除词库中的词"""
        try:
            data = json.loads(request.body)
            word_id = data.get('word_id')
            
            if not word_id:
                return JsonResponse({'status': 'error', 'message': '缺少词语ID'})
            
            word = ProofreadingDictionary.objects.filter(id=word_id, is_deleted=False).first()
            if not word:
                return JsonResponse({'status': 'error', 'message': '词语不存在'})
            
            # 软删除
            word.soft_delete()
            
            return JsonResponse({
                'status': 'success',
                'message': f'词语"{word.word}"已从词库中删除'
            })
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})


class ProofreadingDictionaryAddFromErrorView(BaseView):
    """从错误建议添加词到词库"""
    permission_classes = []
    
    def post(self, request):
        """从错误建议添加词到词库"""
        try:
            data = json.loads(request.body)
            
            proofreader_id = data.get('proofreader_id')
            error_id = data.get('error_id')
            category = data.get('category', '专有名词')
            description = data.get('description', '')
            
            if not proofreader_id or not error_id:
                return JsonResponse({'status': 'error', 'message': '缺少必要参数'})
            
            # 查找错误记录
            error = DocumentProofreaderError.objects.filter(
                proofreader_id=proofreader_id,
                error_id=error_id,
                is_deleted=False
            ).first()
            
            if not error:
                return JsonResponse({'status': 'error', 'message': '错误记录不存在'})
            
            # 提取原始错误文本作为要保护的词
            word = error.error_text.strip()
            
            if not word:
                return JsonResponse({'status': 'error', 'message': '无法提取有效的词语'})
            
            # 检查词是否已存在
            existing_word = ProofreadingDictionary.objects.filter(
                word=word, is_deleted=False
            ).first()
            
            if existing_word:
                existing_word.increment_usage()
                
                # 删除这个错误建议
                error.soft_delete()
                
                # 清理对应的缓存，确保下次访问时能获取最新数据
                cache_key = f"proofreader_result_{proofreader_id}"
                cache.delete(cache_key)
                print(f"🗑️ 已清理校对结果缓存（已存在词汇），ID: {proofreader_id}")
                
                return JsonResponse({
                    'status': 'success',
                    'message': f'词语"{word}"已存在于词库中，使用次数已更新，该建议已移除',
                    'data': {
                        'id': existing_word.id,
                        'word': existing_word.word,
                        'category': existing_word.category,
                        'usage_count': existing_word.usage_count
                    }
                })
            
            # 创建新词
            new_word = ProofreadingDictionary.objects.create(
                word=word,
                category=category,
                description=description or f'从错误建议添加: {error.error_reason}',
                usage_count=1
            )
            
            # 删除这个错误建议
            error.soft_delete()
            
            # 清理对应的缓存，确保下次访问时能获取最新数据
            cache_key = f"proofreader_result_{proofreader_id}"
            cache.delete(cache_key)
            print(f"🗑️ 已清理校对结果缓存（词库添加），ID: {proofreader_id}")
            
            return JsonResponse({
                'status': 'success',
                'message': f'词语"{word}"已添加到词库，该建议已移除',
                'data': {
                    'id': new_word.id,
                    'word': new_word.word,
                    'category': new_word.category,
                    'description': new_word.description,
                    'usage_count': new_word.usage_count
                }
            })
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})


class ProofreadingDictionaryManageView(TemplateView):
    """词库管理页面"""
    template_name = 'app/document_proofread_dict.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取词库统计信息
        total_words = ProofreadingDictionary.objects.filter(is_deleted=False, is_active=True).count()
        categories = ProofreadingDictionary.objects.filter(
            is_deleted=False, is_active=True
        ).values('category').annotate(count=Count('id')).order_by('category')
        
        context.update({
            'total_words': total_words,
            'categories': list(categories),
        })
        
        return context


class DocumentProofreaderResultView(TemplateView):
    """文档校对结果页面视图"""
    template_name = 'app/document_proofreader.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        proofreader_id = kwargs.get('proofreader_id')
        
        try:
            proofreader = DocumentProofreader.objects.get(id=proofreader_id, is_deleted=False)
            
            error_list = list(proofreader.documentproofreadererror_set.filter(is_deleted=False).values(
                'error_id', 'error_text', 'error_reason', 'error_suggestion'
            ))

            for error in error_list:
                error['id'] = error.pop('error_id') # 重命名字段以匹配前端期望
            
            # 构建下载URL
            original_filename_without_ext = os.path.splitext(proofreader.file_name)[0]
            download_filename_md = f"{original_filename_without_ext}_corrected.md"
            download_filename_docx = f"{original_filename_without_ext}_corrected.docx"
            
            if proofreader.task_directory: # 使用保存的task_directory或重建
                task_dir_name = proofreader.task_directory
            else:
                original_filename_safe = "".join([c for c in proofreader.file_name if c.isalnum() or c in (' ', '.', '_')]).rstrip()
                task_dir_name = f'proofreader_task_{proofreader.id}_{original_filename_safe}'
            
            # 构建结果数据
            result_data = {
                'proofreader_id': proofreader.id,
                'file_name': proofreader.file_name,
                'labeled_text': proofreader.labeled_content,
                'corrected_text': proofreader.corrected_content,
                'error_list_json': json.dumps(error_list, ensure_ascii=False),  # 转换为JSON字符串
                'download_url_md': f'/console/app/document_proofreader/{task_dir_name}/{download_filename_md}/',
                'download_url_docx': f'/console/app/document_proofreader/{task_dir_name}/{download_filename_docx}/docx/',
                'creation_time': proofreader.add_time.strftime('%Y-%m-%d %H:%M:%S'),
                'is_result_page': True,  # 标记这是结果页面
            }
            
            context.update(result_data)
            
            # 更新缓存，使用最新的数据
            cache_key = f"proofreader_result_{proofreader_id}"
            cache.set(cache_key, result_data, timeout=7200)
            print(f"💾 校对结果缓存已更新，ID: {proofreader_id}")
            
        except DocumentProofreader.DoesNotExist:
            context.update({
                'error_message': '校对记录不存在或已被删除',
                'proofreader_id': proofreader_id,
                'is_result_page': True,
            })
        except Exception as e:
            context.update({
                'error_message': f'获取校对结果失败: {str(e)}',
                'proofreader_id': proofreader_id,
                'is_result_page': True,
            })
        
        return context


class DocumentProofreaderCacheManageView(View):
    """校对结果缓存管理视图"""
    
    def delete(self, request, proofreader_id):
        """清理指定校对结果的缓存"""
        try:
            cache_key = f"proofreader_result_{proofreader_id}"
            cache.delete(cache_key)
            return JsonResponse({
                'status': 'success',
                'message': f'校对结果 {proofreader_id} 的缓存已清理'
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'清理缓存失败: {str(e)}'
                })


# =============================================================================
# 异步任务管理相关视图
# =============================================================================

class DocumentProofreaderAsyncManageView(TemplateView):
    """异步任务管理页面"""
    template_name = 'app/document_proofreader_async_manage.html'


class DocumentProofreaderAsyncManageApiView(BaseView):
    """异步任务管理API视图"""
    permission_classes = []
    
    def get(self, request):
        """获取异步任务列表"""
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            status_filter = request.GET.get('status', '')
            
            # 构建查询条件
            queryset = DocumentProofreaderAsyncTask.objects.filter(is_deleted=False)
            
            if status_filter:
                queryset = queryset.filter(status=status_filter)
            
            # 获取统计数据
            stats = {
                'pending': DocumentProofreaderAsyncTask.objects.filter(is_deleted=False, status='pending').count(),
                'processing': DocumentProofreaderAsyncTask.objects.filter(is_deleted=False, status='processing').count(),
                'success': DocumentProofreaderAsyncTask.objects.filter(is_deleted=False, status='success').count(),
                'failed': DocumentProofreaderAsyncTask.objects.filter(is_deleted=False, status='failed').count(),
            }
            
            # 分页
            total = queryset.count()
            start = (page - 1) * page_size
            end = start + page_size
            
            tasks = list(queryset.order_by('-add_time')[start:end].values(
                'id', 'task_id', 'file_name', 'status', 'fail_reason', 'retry_times',
                'add_time', 'modified_time', 'proofreader_id', 'notification_sent', 'notification_email'
            ))
            
            # 格式化时间
            for task in tasks:
                task['add_time'] = task['add_time'].strftime('%Y-%m-%d %H:%M:%S')
                task['update_time'] = task['modified_time'].strftime('%Y-%m-%d %H:%M:%S')
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'tasks': tasks,
                    'stats': stats,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total,
                        'total_pages': (total + page_size - 1) // page_size
                    }
                }
            })
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})


class DocumentProofreaderAsyncRetryView(BaseView):
    """重试异步任务"""
    permission_classes = []
    
    def post(self, request):
        """重试失败的异步任务"""
        try:
            data = json.loads(request.body)
            task_id = data.get('task_id')
            
            if not task_id:
                return JsonResponse({'status': 'error', 'message': '缺少任务ID'})
            
            # 查找任务
            async_task = DocumentProofreaderAsyncTask.objects.filter(
                task_id=task_id, is_deleted=False
            ).first()
            
            if not async_task:
                return JsonResponse({'status': 'error', 'message': '任务不存在'})
            
            if async_task.status not in ['failed']:
                return JsonResponse({'status': 'error', 'message': '只能重试失败的任务'})
            
            if not async_task.can_retry():
                return JsonResponse({'status': 'error', 'message': '任务重试次数已达上限'})
            
            # 重置任务状态
            async_task.status = 'pending'
            async_task.fail_reason = ''
            async_task.notification_sent = False
            async_task.save(update_fields=['status', 'fail_reason', 'notification_sent', 'modified_time'])
            
            # 重新提交到Celery队列
            async_document_proofreader_task.delay(
                task_id=async_task.task_id,
                file_name=async_task.file_name,
                original_content=async_task.original_content,
                file_extension='.md'
            )
            
            return JsonResponse({
                'status': 'success',
                'message': '任务已重新提交到队列'
            })
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})


class DocumentProofreaderAsyncDeleteView(BaseView):
    """删除异步任务"""
    permission_classes = []
    
    def post(self, request):
        """删除异步任务"""
        try:
            data = json.loads(request.body)
            task_id = data.get('task_id')
            
            if not task_id:
                return JsonResponse({'status': 'error', 'message': '缺少任务ID'})
            
            # 查找并删除任务
            async_task = DocumentProofreaderAsyncTask.objects.filter(
                task_id=task_id, is_deleted=False
            ).first()
            
            if not async_task:
                return JsonResponse({'status': 'error', 'message': '任务不存在'})
            
            # 软删除
            async_task.soft_delete()
            
            return JsonResponse({
                'status': 'success',
                'message': '任务已删除'
            })
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})


# =============================================================================
# 历史记录相关视图
# =============================================================================

class DocumentProofreaderHistoryView(BaseView):
    """历史记录视图"""
    permission_classes = []
    
    def get(self, request):
        """获取用户历史记录"""
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 10))
            
            # 获取用户的历史记录（这里简化处理，实际项目中可能需要用户身份识别）
            # 获取最近的已完成校对记录
            queryset = DocumentProofreader.objects.filter(
                is_deleted=False,
                status='completed'
            ).order_by('-add_time')
            
            # 分页
            total = queryset.count()
            start = (page - 1) * page_size
            end = start + page_size
            
            records = list(queryset[start:end].values(
                'id', 'file_name', 'add_time', 'modified_time'
            ))
            
            # 格式化时间和文件名
            for record in records:
                record['add_time'] = record['add_time'].strftime('%Y-%m-%d %H:%M')
                record['update_time'] = record['modified_time'].strftime('%Y-%m-%d %H:%M')
                # 限制文件名长度
                if len(record['file_name']) > 20:
                    record['display_name'] = record['file_name'][:17] + '...'
                else:
                    record['display_name'] = record['file_name']
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'records': records,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total,
                        'total_pages': (total + page_size - 1) // page_size
                    }
                }
            })
            
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)}) 