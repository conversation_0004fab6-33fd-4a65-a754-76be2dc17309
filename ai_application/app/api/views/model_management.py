from datetime import datetime
from app.api.dto import KaoYanReviewPlanDto
from app.api.validators import KaoYanReviewPlanValidator
from app.errors import ParameterError
from app.models import App
from app.services.kaoyan_review_report import KaoYanReviewReportService
from django_ext import base_view
from django_ext.response import make_response, make_stream_response



class KaoYanReviewPlanNewView(base_view.BaseView):
    validator_class = KaoYanReviewPlanValidator

    def post(self, request, *args, **kwargs):
        dto: KaoYanReviewPlanDto = self.validate_request(request.data)

        # 获取当前时间
        start_review_time = datetime.now().strftime('%Y-%m-%d')

        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            "report_id": dto.report_id,
            "start_review_time":start_review_time,
            'target_college_level':dto.target_college_level,
            'stream':dto.stream
           }
        app_model: App = App.objects.filter(app_type="kaoyan_review_plan").first()

        # 业务逻辑调service
        response = KaoYanReviewReportService.run(inputs, app_model,request.user)

        if dto.stream:
            return make_stream_response(response)
        else:
            return make_response(response)

