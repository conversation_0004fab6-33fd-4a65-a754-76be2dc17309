--
-- <PERSON>reate model SubjectDomain
--
CREATE TABLE `ai_data_subject_domain`
(
    `id`                         bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`                   datetime(6) NOT NULL,
    `modified_time`              datetime(6) NOT NULL,
    `is_deleted`                 bool         NOT NULL,
    `subject_code`               varchar(100) NOT NULL,
    `subject_name`               varchar(100) NOT NULL,
    `main_subject_code`          varchar(100) NOT NULL,
    `main_subject_name`          varchar(100) NOT NULL,
    `is_knowledge_search_enable` bool         NOT NULL
);
--
-- Add field subject_domain to knowledgelibrary
--
ALTER TABLE `ai_data_knowledge_library`
    ADD COLUMN `subject_domain_id` bigint NULL;
CREATE INDEX `ai_data_subject_domain_add_time_1d442665` ON `ai_data_subject_domain` (`add_time`);
CREATE INDEX `ai_data_knowledge_library_subject_domain_id_1d5131f3` ON `ai_data_knowledge_library` (`subject_domain_id`);