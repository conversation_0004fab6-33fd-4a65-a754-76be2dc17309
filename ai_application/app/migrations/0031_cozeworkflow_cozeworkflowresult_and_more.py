# Generated by Django 4.2 on 2025-03-25 14:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0030_englishwordlibrary'),
    ]

    operations = [
        migrations.CreateModel(
            name='CozeWorkflow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('workflow_id', models.CharField(blank=True, db_index=True, max_length=100, verbose_name='流水线id')),
                ('code', models.Char<PERSON>ield(blank=True, max_length=100, verbose_name='流水线code')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='流水线名称')),
            ],
            options={
                'verbose_name': 'coze流水线',
                'verbose_name_plural': 'coze流水线',
                'db_table': 'ai_coze_workflow',
            },
        ),
        migrations.CreateModel(
            name='CozeWorkflowResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('workflow_id', models.CharField(blank=True, db_index=True, max_length=100, verbose_name='流水线id')),
                ('parameters', models.JSONField(null=True, verbose_name='流水线参数')),
                ('execute_id', models.CharField(blank=True, db_index=True, max_length=100, verbose_name='流水线异步id')),
                ('err_log_id', models.CharField(blank=True, max_length=100, verbose_name='错误log_id')),
                ('output_str', models.TextField(null=True, verbose_name='输出内容字符串')),
                ('status', models.CharField(choices=[('RUNNING', '执行中'), ('SUCCESS', '成功'), ('FAIL', '失败')], default='RUNNING', max_length=16, verbose_name='执行状态')),
                ('ext_params', models.JSONField(null=True, verbose_name='额外参数')),
            ],
            options={
                'verbose_name': 'coze流水线执行结果',
                'verbose_name_plural': 'coze流水线执行结果',
                'db_table': 'ai_coze_workflow_result',
            },
        ),
        migrations.CreateModel(
            name='EnglishWordTestStrategy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('origin_content', models.TextField(null=True, verbose_name='出策略文章')),
                ('strategy_content', models.TextField(null=True, verbose_name='策略')),
            ],
            options={
                'verbose_name': '英语单词测试出题策略',
                'verbose_name_plural': '英语单词测试出题策略',
                'db_table': 'ai_data_english_word_test_strategy',
            },
        ),
        migrations.CreateModel(
            name='EnglishWordTestQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question', models.TextField(null=True, verbose_name='题干')),
                ('answer', models.TextField(null=True, verbose_name='答案解析')),
                ('strategy', models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordteststrategy')),
            ],
            options={
                'verbose_name': '英语单词测试出题策略',
                'verbose_name_plural': '英语单词测试出题策略',
                'db_table': 'ai_data_english_word_test_question',
            },
        ),
    ]
