import logging
import threading
import uuid
from typing import Generator

from app.api.dto import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.app_response.chat_response import ChatResponse
from app.core.apps.base_app_generator import BaseAppGenerator
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.apps.completion.app_runner import Co<PERSON><PERSON><PERSON>ppRunner
from app.core.apps.message_based_app_queue_manage import MessageBasedAppQueueManager
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.entities.queue_entities import QueueErrorEvent
from app.errors import (
    ConversationTokenExceedError, ParameterError, MessageNotFoundError, ContainSensitiveError,
    SpecialMessageError, LLMRequestError
)
from app.models import Account, App, Conversation, InvokeFrom, MessageTask, Message
from app.tasks.process_message_task import process_message_task

logger = logging.getLogger(__name__)


class CompletionAppGenerator(BaseAppGenerator):
    def generate(
            self,
            app_model: App,
            dto: ChatMessageDto,
            from_account: Account | None = None,
            invoke_from: str = InvokeFrom.api.value,
    ) -> Generator | dict:
        conversation: Conversation | None = None

        query = dto.query.replace('\x00', '')
        user_inputs = dto.inputs if isinstance(dto.inputs, dict) else {}

        # 处理消息异常重试
        original_message: Message | None = None

        if user_inputs.get('is_exception_retry'):
            original_message_id = user_inputs.get('original_message_id')
            if not original_message_id:
                raise ParameterError('original_message_id is required')

            original_message: Message = Message.objects.filter(message_no=original_message_id).first()
            if not original_message:
                raise MessageNotFoundError()

            conversation = original_message.conversation
            user_inputs.update(original_message.message_inputs)
            query = original_message.query

        app_model_config = self._get_app_model_config(app_model, conversation)

        prompt_template = self._get_prompt_template(user_inputs=user_inputs, app_model_config=app_model_config)
        model_config = self._get_model_config(
            user_inputs=user_inputs,
            app_model_config=app_model_config,
            conversation=conversation,
            prompt_template=prompt_template,
        )
        pre_prompt = self._get_pre_prompt(
            pre_prompt=dto.pre_prompt,
            user_inputs=user_inputs,
            app_model_config=app_model_config,
            conversation=conversation,
            query=query,
            prompt_template=prompt_template,
        )

        if conversation:
            conversation_hash = conversation.conversation_hash
        else:
            conversation_hash = self._generate_conversation_hash()

        application_generate_entity = CompletionAppGenerateEntity(
            app_no=app_model.app_no,
            app_type=app_model.app_type,
            app_model_id=app_model.id,
            app_model_config_id=app_model_config.id,
            model_conf=model_config,
            pre_prompt=pre_prompt,
            query=query,
            inputs=user_inputs,
            conversation_hash=conversation_hash,
            context_limit=0,
            file_objs=dto.file_objs,
            stream=dto.stream,
            invoke_from=invoke_from,
            query_in_prompt=prompt_template.query_in_prompt if prompt_template else False,
        )

        # 创建conversion 和 message
        if original_message:
            message = original_message
        else:
            # 特殊处理知识解析
            biz_id = dto.biz_id
            if application_generate_entity.app_type == 'knowledge_query' and dto.inputs.get('course_id'):
                biz_id = dto.inputs.get('course_id')
            if application_generate_entity.app_type == 'knowledge_analysis_simple' and dto.inputs.get('course_id'):
                biz_id = dto.inputs.get('course_id')

            (
                conversation,
                message
            ) = self._init_generate_records(
                application_generate_entity=application_generate_entity,
                from_account=from_account,
                from_biz_id=biz_id,
                userinfo=dto.userinfo,
                conversation=conversation,
            )

        if dto.is_async:
            task = MessageTask.objects.create(
                message=message,
                task_id=str(uuid.uuid4()),
                process_status=MessageTask.ProcessStatus.waiting,
            )
            process_message_task.delay(task.task_id)
            return {
                'task_id': task.task_id,
                'message_id': message.message_no,
                'message_int_id': message.id,
            }

        queue_manager = MessageBasedAppQueueManager(
            task_id=message.message_no,
            conversation_id=conversation.conversation_no,
            message_id=message.message_no,
            app_mode='completion'
        )

        # new thread
        worker_thread = threading.Thread(target=self._generate_worker, kwargs={
            'application_generate_entity': application_generate_entity,
            'queue_manager': queue_manager,
            'message_id': message.id,
        })
        worker_thread.start()

        # return response or stream generator
        response = ChatResponse(
            application_generate_entity=application_generate_entity,
            queue_manager=queue_manager,
            conversation=conversation,
            message=message,
            stream=dto.stream,
        ).process()
        return self._convert_generate_response(response)

    def generate_for_task(self, task: MessageTask):
        message = task.message
        user_inputs = message.inputs
        conversation = task.message.conversation
        app_model = task.message.app
        stream = False

        app_model_config = self._get_app_model_config(app_model, conversation)
        prompt_template = self._get_prompt_template(user_inputs=user_inputs, app_model_config=app_model_config)

        model_config = self._get_model_config(
            user_inputs=message.inputs,
            app_model_config=app_model_config,
            conversation=conversation,
            prompt_template=prompt_template
        )
        pre_prompt = conversation.pre_prompt
        conversation_hash = conversation.conversation_hash

        application_generate_entity = CompletionAppGenerateEntity(
            app_no=app_model.app_no,
            app_model_id=app_model.id,
            app_model_config_id=app_model_config.id,
            model_conf=model_config,
            pre_prompt=pre_prompt,
            query=message.query,
            inputs=message.inputs,
            conversation_hash=conversation_hash,
            context_limit=0,
            stream=stream,
            invoke_from=message.invoke_from,
            query_in_prompt=prompt_template.query_in_prompt if prompt_template else False,
        )
        queue_manager = MessageBasedAppQueueManager(
            task_id=message.message_no,
            conversation_id=conversation.conversation_no,
            message_id=message.message_no,
            app_mode='completion'
        )

        # new thread
        worker_thread = threading.Thread(target=self._generate_worker, kwargs={
            'application_generate_entity': application_generate_entity,
            'queue_manager': queue_manager,
            'message_id': message.id,
        })
        worker_thread.start()

        # return response or stream generator
        response = ChatResponse(
            application_generate_entity=application_generate_entity,
            queue_manager=queue_manager,
            conversation=conversation,
            message=message,
            stream=stream,
        ).process()
        return self._convert_generate_response(response)

    def _generate_worker(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            queue_manager: AppQueueManager,
            message_id: int,
    ) -> None:
        try:
            message = Message.objects.get(id=message_id)
            CompletionAppRunner().run(
                application_generate_entity=application_generate_entity,
                queue_manager=queue_manager,
                message=message,
            )
        except ConversationTokenExceedError as ee:
            logger.exception(ee)
            queue_manager.publish_error(e_type=QueueErrorEvent.ErrorType.CONVERSATION_TOKEN_EXCEED, e=ee.detail)
        except ContainSensitiveError as se:
            logger.exception(se)
            queue_manager.publish_error(e_type=QueueErrorEvent.ErrorType.CONTAIN_SENSITIVE, e=se.detail)
        except SpecialMessageError as sp_e:
            queue_manager.publish_error(e_type=QueueErrorEvent.ErrorType.SPECIAL_ERROR, e=sp_e.detail)
        except LLMRequestError as llm_e:
            queue_manager.publish_error(e_type=QueueErrorEvent.ErrorType.LLM_REQUEST_ERROR, e=llm_e.detail_err)
        except Exception as e:
            logger.exception(e)
            queue_manager.publish_error(str(e))
