import math

from django.db.models import QuerySet

from django_ext.utils.string_utils import parse_int


class Paginator:
    default_page_size = 15

    curr_page = None
    page_size = None
    num_pages = None
    total = None
    offset = None

    def __init__(self, query_params: dict):
        curr_page = parse_int(query_params.get('page'), 1)
        page_size = parse_int(query_params.get('size'), self.default_page_size)

        self.curr_page = curr_page if curr_page > 0 else 1
        self.page_size = page_size if page_size > 0 else self.default_page_size

    def paginate(self, total: int):
        self.total = total
        self.num_pages = math.ceil(self.total / self.page_size)
        self.offset = (self.curr_page - 1) * self.page_size

    def get_paginated_response(self, data_list: list):
        return {
            'data': data_list,
            'page': {
                'count': self.total,
                'size': self.page_size,
                'total_page': self.num_pages,
                'current_page': self.curr_page,
            }
        }


def queryset_paginate(qs: QuerySet, serializer_class, query_params: dict):
    paginator = Paginator(query_params)
    paginator.paginate(qs.count())
    page_qs = qs[paginator.offset: paginator.offset + paginator.page_size]

    data = serializer_class(page_qs, many=True).data
    return paginator.get_paginated_response(data)
