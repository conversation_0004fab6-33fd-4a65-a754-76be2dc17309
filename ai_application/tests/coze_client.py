import logging

from cozepy import setup_logging, Message, ChatEventType

from app.libs.coze_api import coze_api_client


def test_create_conversation() -> str:
    bot_id = 'x'
    conversation = coze_api_client.conversations.create(bot_id=bot_id)
    return conversation.id


def test_stop_message():
    chat_id = ''
    conversation_id = ''
    coze_api_client.chat.cancel(conversation_id=conversation_id, chat_id=chat_id)


def test_coze_chat_stream():
    bot_id = 'x'
    user_id = "x"
    is_debug = False

    if is_debug:
        setup_logging(logging.DEBUG)

    # Call the coze.chat.stream method to create a chat. The create method is a streaming
    # chat and will return a Chat Iterator. Developers should iterate the iterator to get
    # chat event and handle them.
    for event in coze_api_client.chat.stream(
            bot_id=bot_id,
            user_id=user_id,
            additional_messages=[
                Message.build_user_question_text("Tell a 500-word story."),
            ],
            conversation_id=None,   # 某个会话中的唯一标识，如果为空，则创建一个新的会话
    ):
        if event.event == ChatEventType.CONVERSATION_MESSAGE_DELTA:
            if event.message.content:
                # event.message.conversation_id
                # event.chat.id
                print(event.message.content, end="", flush=True)

        if event.event == ChatEventType.CONVERSATION_CHAT_COMPLETED:
            print('')
            print("token usage:", event.chat.usage.token_count)
