from django.db import models

from app.models import BaseModel


class EnglishPaperExplain(BaseModel):
    user_id = models.CharField('用户id', max_length=100, db_index=True)
    answer_id = models.Char<PERSON>ield('答卷id', max_length=100, db_index=True)
    task_id = models.Char<PERSON>ield('任务id', max_length=100, db_index=True)

    # NOT_START ING SUCCESS FAIL
    summary_req_params = models.JSONField('总结请求参数', null=True)
    summary_status = models.CharField('总结生成状态', max_length=100, blank=True)
    summary_content = models.J<PERSON><PERSON>ield("总结内容", null=True)

    # NOT_START ING SUCCESS FAIL
    status = models.Char<PERSON><PERSON>('解析状态', max_length=100, blank=True)
    fail_reason = models.TextField('失败原因', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '英语试卷解读'
        db_table = 'ai_english_paper_explain'


class EnglishPaperQuestionExplain(BaseModel):
    paper_explain = models.ForeignKey(
        EnglishPaperExplain, on_delete=models.CASCADE, db_constraint=False)
    question_id = models.Char<PERSON>ield('题目id', max_length=100, db_index=True)
    # 完形填空 阅读理解 新题型 翻译 大作文 小作文
    question_type = models.CharField('题型', max_length=100)
    req_params = models.JSONField('请求参数', null=True)
    sub_question_num = models.IntegerField('小题数量', default=0)
    sub_right_question_num = models.IntegerField('小题正确数量', default=0)
    # ING SUCCESS FAIL
    status = models.CharField('解析状态', max_length=100, blank=True)
    explain_content = models.JSONField("解析内容", null=True)

    class Meta:
        verbose_name = verbose_name_plural = '英语试卷题目解读'
        db_table = 'ai_english_paper_question_explain'
