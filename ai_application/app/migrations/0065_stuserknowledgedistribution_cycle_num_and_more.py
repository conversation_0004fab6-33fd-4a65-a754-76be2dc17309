# Generated by Django 4.2 on 2025-08-14 09:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0064_merge_20250814_1759'),
    ]

    operations = [
        migrations.AddField(
            model_name='stuserknowledgedistribution',
            name='cycle_num',
            field=models.IntegerField(default=1, verbose_name='周期数'),
        ),
        migrations.AddField(
            model_name='stuserpaper',
            name='cycle_num',
            field=models.IntegerField(default=1, verbose_name='周期数'),
        ),
        migrations.AddField(
            model_name='stuserpaperanswer',
            name='cycle_num',
            field=models.IntegerField(default=1, verbose_name='周期数'),
        ),
        migrations.AddField(
            model_name='stuserpaperquestion',
            name='cycle_num',
            field=models.IntegerField(default=1, verbose_name='周期数'),
        ),
        migrations.AddField(
            model_name='stuserpaperquestionanswer',
            name='cycle_num',
            field=models.IntegerField(default=1, verbose_name='周期数'),
        ),
        migrations.AddField(
            model_name='stuserroundanalysis',
            name='cycle_num',
            field=models.IntegerField(default=1, verbose_name='周期数'),
        ),
        migrations.AddField(
            model_name='stuserroundchangerecord',
            name='cycle_num',
            field=models.IntegerField(default=1, verbose_name='周期数'),
        ),
        migrations.AlterField(
            model_name='stsubjectstagestrategy',
            name='learning_stage',
            field=models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提高')], default='modi', max_length=32, verbose_name='阶段'),
        ),
        migrations.AlterField(
            model_name='stuserpaper',
            name='learning_stage',
            field=models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提高')], default='modi', max_length=32, verbose_name='阶段'),
        ),
        migrations.AlterField(
            model_name='stuserroundanalysis',
            name='learning_stage',
            field=models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提高')], default='modi', max_length=32, verbose_name='答题阶段'),
        ),
        migrations.AlterField(
            model_name='stuserroundanalysis',
            name='stage_change_type',
            field=models.CharField(choices=[('up', '升级'), ('down', '降级'), ('strengthen', '阶段强化'), ('pass_next_cycle', '通过后进入下一周期'), ('fail_next_cycle', '失败后进入下一周期')], default='up', max_length=16, verbose_name='阶段变更类型'),
        ),
        migrations.AlterField(
            model_name='stuserroundchangerecord',
            name='change_type',
            field=models.CharField(choices=[('up', '升级'), ('down', '降级'), ('strengthen', '阶段强化'), ('pass_next_cycle', '通过后进入下一周期'), ('fail_next_cycle', '失败后进入下一周期')], max_length=16, verbose_name='变更类型'),
        ),
        migrations.AlterField(
            model_name='stuserroundchangerecord',
            name='new_stage',
            field=models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提高')], max_length=32, verbose_name='新阶段'),
        ),
        migrations.AlterField(
            model_name='stuserroundchangerecord',
            name='old_stage',
            field=models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提高')], max_length=32, verbose_name='旧阶段'),
        ),
    ]
