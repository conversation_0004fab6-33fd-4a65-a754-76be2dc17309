from django.conf import settings
from django.db import transaction
from django.views.generic import TemplateView

from app.errors import AppSystemError
from app.models import PromptTemplate, PromptTemplateChangeLog
from django_ext.base_view import BaseView
from django_ext.response import make_response


class PromptListView(TemplateView):
    template_name = 'app/prompt_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'env': '生产' if settings.ENVIRONMENT == settings.ENV_PRODUCT else '测试',
        })
        return context


class PromptDetailView(TemplateView):
    template_name = 'app/prompt_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        app_no = kwargs.get('app_no')
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no=app_no).first()
        if not prompt_template:
            raise ValueError('No prompt template found')

        model_params = prompt_template.model_params_dict
        if not model_params:
            model_params = {"max_tokens": 500, "temperature": 0.3}
        context.update({
            'env': '生产' if settings.ENVIRONMENT == settings.ENV_PRODUCT else '测试',
            'app_no': app_no,
            'prompt_name': prompt_template.name,
            'model_params': model_params,
            'prompt_content': prompt_template.prompt_content
        })
        return context


class PromptPublishView(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        # password = request.data.get('password')
        # if password != settings.PROMPT_PASSWORD:
        #     raise AppSystemError(detail='密码错误')

        app_no = kwargs.get('app_no')
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no=app_no).first()
        if not prompt_template:
            raise AppSystemError(detail='提示词模板不存在')

        prompt = request.data.get('prompt')
        if not prompt:
            raise AppSystemError(detail='提示词不能为空')
        prompt_template.prompt_content = prompt

        max_tokens = request.data.get('max_tokens')
        temperature = request.data.get('temperature')
        model_params = prompt_template.model_params_dict
        if max_tokens and temperature:
            model_params = {'max_tokens': max_tokens, 'temperature': temperature}
            prompt_template.model_params = model_params

        query_in_prompt = '{{query}}' in prompt
        special_variables = prompt_template.special_variable_list
        special_variables_without_query = [i for i in special_variables if i != 'query']
        if query_in_prompt:
            special_variables_without_query.append(['query'])
        prompt_template.special_variables = special_variables

        with transaction.atomic():
            prompt_template.save(update_fields=['prompt_content', 'model_params', 'special_variables', 'modified_time'])
            PromptTemplateChangeLog.objects.create(
                prompt_template=prompt_template,
                prompt_content=prompt,
                model_params=model_params
            )

        return make_response()
