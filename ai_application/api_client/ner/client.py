import logging

from api_client.base_client import BaseClient
from api_client.decorators import catch_error

logger = logging.getLogger(__name__)

__all__ = ['ner_client']


class NerClient(BaseClient):
    host = 'http://yantucs-ner-svc:8000'
    host_local = 'https://ner.kaoyan-vip.cn'

    @property
    def base_url(self):
        return f'{self.host}/internal_api'

    @catch_error([])
    def get_en_sentence_dependency(self, query: str) -> list:
        url = '/app/en_sentence_dependency'
        return self._post(url, data={"query": query}, timeout=10)


ner_client = NerClient()
