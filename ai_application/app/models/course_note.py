from django.db import models

from ._base import BaseModel


class CourseNoteContent(BaseModel):
    course_id = models.CharField('课程ID', max_length=40, db_index=True)
    course_lecture_id = models.Char<PERSON>ield('课程讲义文档ID', max_length=40, blank=True, db_index=True)
    chapter_id = models.CharField('章ID', max_length=40, db_index=True)
    chapter_name = models.CharField('章名称', max_length=100, blank=True)
    video_content = models.ForeignKey('CourseVideoContent', on_delete=models.CASCADE, db_constraint=False)
    document_no = models.Char<PERSON><PERSON>('文档号', max_length=40, db_index=True)
    video_lecture = models.TextField('讲义', null=True)
    video_note = models.TextField('笔记', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '课程笔记内容'
        db_table = 'ai_course_note_content'


# demo使用，生产环境忽略
class CourseNoteTaskDebug(BaseModel):
    course_id = models.Char<PERSON>ield('课程ID', max_length=40, db_index=True)
    # not_start, processing, success, fail
    status = models.CharField('状态', max_length=20, default='not_start')
    name = models.CharField('名称', max_length=100, blank=True)
    processing_started_at = models.DateTimeField('处理时间', null=True)
    completed_at = models.DateTimeField('完成时间', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '课程笔记任务debug'
        db_table = 'ai_course_note_task_debug'


class CourseNoteTask(BaseModel):
    course_id = models.CharField('课程ID', max_length=40, db_index=True)
    course_lecture_id = models.CharField('课程讲义文档ID', max_length=40, blank=True, db_index=True)
    chapter_id = models.CharField('章ID', max_length=40, db_index=True)
    chapter_name = models.CharField('章名称', max_length=100, blank=True)
    chapter_lecture = models.TextField('章讲义', null=True)
    document_nos = models.JSONField('document_nos', null=True)
    # not_start, processing, success, fail
    status = models.CharField('状态', max_length=20, default='not_start')
    processing_started_at = models.DateTimeField('处理时间', null=True)
    completed_at = models.DateTimeField('完成时间', null=True)
    task_debug = models.ForeignKey(
        'CourseNoteTaskDebug', on_delete=models.CASCADE, db_constraint=False, null=True)

    class Meta:
        verbose_name = verbose_name_plural = '课程笔记任务'
        db_table = 'ai_course_note_task'

    @property
    def document_nos_list(self) -> list:
        return self.document_nos or []


class CourseNoteTaskChange(BaseModel):
    task = models.ForeignKey('CourseNoteTask', on_delete=models.CASCADE, db_constraint=False)
    change_content = models.TextField('变更内容', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '课程笔记任务变更'
        db_table = 'ai_course_note_task_change'


class CourseNoteTaskChangeDetail(BaseModel):
    task = models.ForeignKey('CourseNoteTask', on_delete=models.CASCADE, db_constraint=False)
    video_content = models.ForeignKey('CourseVideoContent', on_delete=models.CASCADE, db_constraint=False)
    document_no = models.CharField('文档号', max_length=40, db_index=True)
    video_lecture = models.TextField('讲义', null=True)
    # 该字段用于判断一章内提取的讲义是否重复，如重复，则不使用讲义生成
    is_use_lecture = models.BooleanField('是否使用讲义生成', default=False)
    video_note = models.TextField('笔记', null=True)
    message = models.ForeignKey('app.Message', on_delete=models.CASCADE, db_constraint=False, null=True)
    # not_start, processing, success, fail
    status = models.CharField('状态', max_length=20, default='not_start')
    processing_started_at = models.DateTimeField('处理时间', null=True)
    completed_at = models.DateTimeField('完成时间', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '课程笔记任务变更详情'
        db_table = 'ai_course_note_task_change_detail'
