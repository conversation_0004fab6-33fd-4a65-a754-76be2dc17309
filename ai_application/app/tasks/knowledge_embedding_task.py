from app.core.rag.extractor.entities.datasource_type import DatasourceType
from app.core.rag.extractor.entities.extract_setting import ExtractSetting, RemoteFileInfo
from app.core.rag.index_processor.constant.index_type import IndexType
from app.core.rag.index_processor.index_processor_factory import IndexProcessorFactory
from app.core.rag.knowledge_service import KnowledgeService


def knowledge_embedding_task(knowledge_files: list[str]):
    index_processor = IndexProcessorFactory(IndexType.KNOWLEDGE_INDEX.value).init_index_processor()
    for file_url in knowledge_files:
        extract_setting = ExtractSetting(
            datasource_type=DatasourceType.REMOTE_FILE.value,
            remote_file_info=RemoteFileInfo(url=file_url),
            document_model=IndexType.KNOWLEDGE_INDEX.value
        )
        text_docs = index_processor.extract(extract_setting)

        knowledge_list = KnowledgeService.get_knowledge_from_document(text_docs)
        print(knowledge_list)

