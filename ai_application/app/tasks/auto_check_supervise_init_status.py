import datetime
import logging

from celery import shared_task
from django.utils import timezone

from app.models import SuperViseInitStudentStatus
from app.services.supervise_init_status_service import SuperviseInitStatusService

logger = logging.getLogger(__name__)


@shared_task(queue='learn_status_check')
def auto_check_supervise_init_stat_gen():
    # 预发布环境压测先不处理
    # if settings.ENVIRONMENT == settings.ENV_PREVIEW:
    #     return

    qs = SuperViseInitStudentStatus.objects.filter(is_deleted=False, status='NOT_START')[:10]
    for record in qs:
        check_supervise_init_stat_gen(record)

    # 10分钟后未处理则修改为失败
    time_point = timezone.now() - datetime.timedelta(minutes=10)
    SuperViseInitStudentStatus.objects.filter(
        is_deleted=False, status='ING', add_time__lt=time_point
    ).update(status='FAIL', fail_reason='超时')


@shared_task(queue='learn_status_check')
def delay_supervise_init_stat_gen(record_id):
    logger.info(f"start_delay_supervise_learn_stat_gen: {record_id}")
    try:
        record = SuperViseInitStudentStatus.objects.get(id=record_id)
        print("record啊啊啊啊")
        logger.info(f"Processing record: {record.id}")
        result = SuperviseInitStatusService.get_report(record)
        print("result啊啊啊啊🚀",result)
        logger.info(f"Successfully processed record: {record.id}")
        return result

    except Exception as e:
        # 记录完整的异常堆栈
        logger.error(f"Error processing record_id {record_id}", exc_info=True)
        logger.error(f"Record details: {record.__dict__ if 'record' in locals() else 'Record not loaded'}")
        # 更新任务状态为失败
        if 'record' in locals():
            record.status = 'FAIL'
            record.fail_reason = str(e)
            record.save()
        raise


def check_supervise_init_stat_gen(record: SuperViseInitStudentStatus):
    logger.info(f"pre_start_delay_supervise_init_stat_gen: {record.id}")
    record.status = 'ING'
    record.save(update_fields=['status'])
    print(f"开始处理ID啊啊啊啊啊啊", record.id)
    delay_supervise_init_stat_gen.delay(record.id)
    print("处理了啊啊啊啊异步")