## 考研408计算机考试大纲分析报告


#### **一、各科目分值分布及重要程度分析**  
考研408试卷总分150分，科目分值分布及重要程度如下：  
- **数据结构**：45分（核心科目，算法设计与实现为综合题重点）  
- **计算机组成原理**：45分（核心科目，硬件底层原理与计算为难点）  
- **操作系统**：35分（重要科目，进程管理与内存管理为高频考点）  
- **计算机网络**：25分（次重要科目，TCP/IP协议栈为核心）  


#### **二、各科目考点分析**  
注：重要程度和难度等级按照从小到大的顺序往上递增
### **（一）数据结构**  

| **章节**               | **考点内容**                  | **重要程度** | **考试题型**       | **难度等级** |  
|------------------------|---------------------------|---------|--------------------|----------|  
| 一、基本概念           | 数据结构的基本概念                 | ★★      | 单选               | 1        |  
|                        | 算法的基本概念                   | ★★★     | 单选、综合题（算法分析） | 3        |  
| 二、线性表             | 线性表的基本概念                  | ★★★★    | 单选               | 9        |  
|                        | 线性表的实现（顺序存储、链式存储）         | ★★★★★   | 单选、综合题（代码实现） | 7        |  
|                        | 线性表的应用                    | ★       | 单选               | 7        |  
| 三、栈、队列和数组     | 栈和队列的基本概念、顺序/链式存储结构       | ★★★★    | 单选               | 5        |  
|                        | 多维数组的存储、特殊矩阵的压缩存储         | ★★★★    | 单选（地址计算）   | 3        |  
|                        | 栈、队列和数组的应用（表达式求值、缓冲队列）    | ★★★★    | 单选、综合题       | 5        |  
| 四、树与二叉树         | 树的基本概念                    | ★★      | 单选               | 5        |  
|                        | 二叉树的定义、特征、顺序/链式存储结构       | ★★★★    | 单选               | 5        |  
|                        | 二叉树的遍历（前中后序、层次遍历）         | ★★★     | 单选、综合题（算法设计） | 5        |  
|                        | 线索二叉树的基本概念和构造             | ★       | 单选               | 5        |  
|                        | 树/森林的存储结构、与二叉树的转换、遍历      | ★★★★    | 单选               | 5        |  
|                        | 哈夫曼树和哈夫曼编码                | ★★★     | 单选、综合题（编码构造） | 5        |  
|                        | 并查集及其应用                   | ★★★★    | 单选、综合题（操作实现） | 5        |  
|                        | 堆及其应用（堆排序、优先级队列）          | ★★★     | 单选、综合题（排序实现） | 5        |  
| 五、图                 | 图的基本概念（顶点、边、度、路径等）        | ★★      | 单选               | 5        |  
|                        | 图的存储（邻接矩阵、邻接表）            | ★★★     | 单选、综合题（存储实现） | 5        |  
|                        | 邻接多重表、十字链表                | ★       | 单选               | 7        |  
|                        | 图的遍历（深度优先DFS、广度优先BFS）     | ★★★     | 单选、综合题（遍历应用） | 5        |  
|                        | 最小（代价）生成树（Prim、Kruskal算法） | ★★★     | 单选、综合题（算法实现） | 7        |  
|                        | 最短路径（Dijkstra、Floyd算法）    | ★★★     | 单选、综合题（路径计算） | 7        |  
|                        | 拓扑排序                      | ★★★★★   | 单选、综合题（排序过程） | 5        |  
|                        | 关键路径                      | ★★★★    | 综合题             | 7        |  
| 六、查找               | 查找的基本概念                   | ★★      | 单选               | 7        |  
|                        | 顺序查找、分块查找                 | ★★      | 单选               | 9        |  
|                        | 折半查找（算法及性能分析）             | ★★★★    | 单选               | 5        |  
|                        | 二叉排序树（插入、删除、查找）           | ★★★★★   | 单选、综合题       | 7        |  
|                        | 平衡二叉树（AVL树旋转操作）           | ★★★★    | 单选               | 5        |  
|                        | 红黑树的基本概念                  | ★       | 单选               | 1        |  
|                        | B树及其基本操作、B+树的基本概念         | ★★★★    | 单选               | 3        |  
|                        | 散列（Hash）表（构造方法、冲突处理、性能分析） | ★★★     | 单选、综合题       | 5        |  
|                        | 字符串模式匹配（KMP算法）            | ★★★★    | 单选               | 5        |  
|                        | 查找算法的分析及应用                | ★       | 单选               | 1        |  
| 七、排序               | 排序的基本概念（稳定性、时间/空间复杂度）     | ★★      | 单选               | 5        |  
|                        | 直接插入、折半插入、起泡、简单选择排序       | ★       | 单选               | 5        |  
|                        | 希尔排序                      | ★★★★    | 单选               | 5        |  
|                        | 快速排序（算法、性能分析）             | ★★★★★   | 单选、综合题（排序实现） | 5        |  
|                        | 堆排序（建堆、排序过程）              | ★★★     | 单选、综合题       | 5        |  
|                        | 二路归并排序                    | ★★★     | 单选、综合题       | 5        |  
|                        | 基数排序                      | ★       | 单选               | 5        |  
|                        | 外部排序                      | ★       | 单选               | 7        |  
|                        | 排序算法的分析和应用（适用场景对比）        | ★★★★    | 单选、综合题       | 7        |  


### **（二）计算机组成原理**  

| **章节**               | **考点内容**                                                                 | **重要程度** | **考试题型**       | **难度等级** |  
|------------------------|------------------------------------------------------------------------------|----------|--------------------|----------|  
| 一、计算机系统概述     | 计算机系统层次结构（基本组成、软硬件关系、“存储程序”工作方式）               | ★★       | 单选               | 9        |  
|                        | 计算机性能指标（吞吐量、响应时间、CPU时钟周期、CPI、MIPS、MFLOPS等）         | ★★★★     | 单选、计算         | 7        |  
| 二、数据的表示和运算   | 数制与编码（进位计数制转换、定点数编码）                                     | ★★       | 单选               | 5        |  
|                        | 运算方法和运算电路（加法器、ALU、补码加减运算、乘除法基本原理）               | ★★★★     | 单选、计算         | 7        |  
|                        | 整数的表示和运算                                                             | ★        | 单选               | 1        |  
|                        | 浮点数的表示（IEEE 754标准）、浮点数的加/减运算                             | ★★★      | 单选、计算（格式/运算步骤） | 7        |  
| 三、存储器层次结构     | 存储器的分类、层次化存储器的基本结构                                         | ★★       | 单选               | 5        |  
|                        | SRAM、DRAM、Flash存储器的基本原理                                           | ★★       | 单选               | 9        |  
|                        | 主存储器（DRAM芯片、多模块存储器、CPU与主存的连接）                           | ★★★★     | 单选               | 7        |  
|                        | 高速缓冲存储器（Cache）的基本原理、映射方式（直接/全相联/组相联）、替换算法（LRU/FIFO）、写策略 | ★★★      | 单选、综合题（地址映射/命中率计算） | 9        |  
|                        | 虚拟存储器的基本概念、页式虚拟存储器（页表、地址转换、TLB）                   | ★★★      | 单选、综合题（地址转换） | 9        |  
|                        | 段式、段页式虚拟存储器                                                       | ★        | 单选               | 1        |  
| 四、指令系统           | 指令格式（操作码、地址码）                                                   | ★★★★     | 单选（字段含义分析） | 7        |  
|                        | 寻址方式（立即数、直接、间接、变址、基址寻址等）                             | ★★★      | 单选（有效地址计算） | 7        |  
|                        | 数据的对齐和大/小端存放方式                                                 | ★        | 单选               | 1        |  
|                        | CISC和RISC的基本概念                                                       | ★        | 单选               | 7        |  
|                        | 高级语言程序与机器级代码的对应（选择/循环/过程调用的机器表示、栈帧结构）     | ★★★★★    | 单选、综合题       | 9        |  
| 五、中央处理器（CPU）  | CPU的功能和基本结构                                                         | ★★       | 单选               | 9        |  
|                        | 指令执行过程（取指、译码、执行、访存、写回）                                 | ★★★★     | 单选               | 7        |  
|                        | 数据通路的功能和基本结构（数据通路图分析）                                   | ★★★      | 综合题             | 7        |  
|                        | 控制器的功能和工作原理（硬布线/微程序控制）                                 | ★        | 单选               | 5        |  
|                        | 异常和中断机制（基本概念、分类、检测与响应）                                 | ★★★★     | 单选               | 3        |  
|                        | 指令流水线（基本概念、结构冒险/数据冒险/控制冒险的处理、吞吐率计算）         | ★★★      | 单选、综合题       | 7        |  
|                        | 多处理器基本概念（SISD、SIMD、MIMD、多核、SMP）                             | ★        | 单选               | 1        |  
| 六、总线和输入/输出系统 | 总线的基本概念、组成及性能指标（带宽、位宽）                                 | ★★★★     | 单选               | 7        |  
|                        | 总线事务和定时（同步/异步定时）                                             | ★        | 单选               | 3        |  
|                        | I/O接口的功能、基本结构、I/O端口编址                                         | ★★       | 单选               | 3        |  
|                        | I/O方式（程序查询、程序中断、DMA方式）                                       | ★★★      | 单选、综合题（中断处理/DMA过程） | 9        |  


### **（三）操作系统**  

| **章节**               | **考点内容**                                | **重要程度** | **考试题型**       | **难度等级** |  
|------------------------|-----------------------------------------|----------|--------------------|----------|  
| 一、操作系统概述       | 操作系统的基本概念、发展历程                          | ★★       | 单选               | 1        |  
|                        | 程序运行环境（CPU运行模式、中断/异常处理、系统调用、程序的链接与装入）   | ★        | 单选               | 1        |  
|                        | 操作系统结构（分层、模块化、宏内核、微内核）                  | ★        | 单选               | 5        |  
|                        | 操作系统引导、虚拟机                              | ★        | 单选               | 3        |  
| 二、进程管理           | 进程与线程的基本概念、状态转换、组织与控制                   | ★★★★     | 单选               | 3        |  
|                        | 进程间通信（共享内存、消息传递、管道、信号）                  | ★★★★     | 单选               | 7        |  
|                        | CPU调度算法（FCFS、SJF、优先级、时间片轮转、多级反馈队列）      | ★★★★     | 单选、综合题       | 3        |  
|                        | 同步与互斥的基本概念、实现方法（信号量、PV操作、条件变量）          | ★★★★★    | 综合题（经典同步问题） | 7        |  
|                        | 经典同步问题（生产者-消费者、读者-写者、哲学家进餐）             | ★★★      | 综合题             | 9        |  
|                        | 死锁的基本概念、预防、避免（银行家算法）、检测与解除              | ★★★      | 单选、综合题       | 7        |  
| 三、内存管理           | 内存管理基础（逻辑/物理地址空间、地址变换、连续分配、页式/段式/段页式管理） | ★★★★     | 单选               | 5        |  
|                        | 虚拟内存管理的基本概念、请求页式管理                      | ★★★      | 单选               | 5        |  
|                        | 页置换算法（OPT、FIFO、LRU、CLOCK）               | ★★★      | 单选、综合题（缺页率计算） | 5        |  
|                        | 页框分配与回收、内存映射文件                          | ★        | 单选               | 1        |  
| 四、文件管理           | 文件的基本概念、元数据（inode）、操作（建立/删除/打开/关闭/读写）   | ★★★★★    | 单选               | 5        |  
|                        | 文件的逻辑结构、物理结构（连续、链接、索引）                  | ★★★★★    | 单选               | 5        |  
|                        | 树形目录、硬链接与软链接                            | ★        | 单选               | 1        |  
|                        | 文件系统的全局结构、外存空闲空间管理（位示图、空闲链表）            | ★        | 单选               | 5        |  
| 五、输入输出（I/O）管理 | I/O设备的分类、I/O控制方式（查询、中断、DMA）             | ★★       | 单选               | 3        |  
|                        | I/O软件层次结构（中断处理程序、驱动程序、设备独立软件）           | ★        | 单选               | 1        |  
|                        | 缓冲区管理、SPOOLing技术                        | ★★★★     | 单选               | 5        |  
|                        | 磁盘调度算法（FCFS、SSTF、SCAN、CSCAN）            | ★★★      | 单选               | 5        |  


### **（四）计算机网络**  

| **章节**    | **考点内容**                                         | **重要程度** | **考试题型**       | **难度等级** |  
|-----------|--------------------------------------------------|-----|--------------------|----------|  
| 一、计算机网络概述 | 计算机网络的基本概念（定义、组成、功能、分类）                          | ★★  | 单选               | 5        |  
|           | 计算机网络性能指标（带宽、时延、吞吐量）                             | ★★★★ | 单选               | 5        |  
|           | 网络体系结构（分层结构、协议/接口/服务、OSI与TCP/IP模型）               | ★★★★★ | 单选               | 7        |  
| 二、物理层     | 通信基础（信道、信号、码元、波特率、速率；奈奎斯特定理、香农定理）                | ★★★ | 单选、计算（速率计算） | 5        |  
|           | 编码与调制（数字编码、模拟调制）                                 | ★★★★ | 单选               | 3        |  
|           | 电路交换、报文交换、分组交换                                   | ★★★★ | 单选               | 7        |  
|           | 传输介质、物理层设备（中继器、集线器）                              | ★   | 单选               | 3        |  
| 三、数据链路层   | 数据链路层功能、组帧（PPP帧格式）                               | ★★  | 单选               | 1        |  
|           | 差错控制（检错编码：CRC；纠错编码）                              | ★★★★ | 单选               | 1        |  
|           | 流量控制与可靠传输机制（滑动窗口、停止-等待、GBN、SR协议）                 | ★★★★ | 单选、综合题（协议流程） | 4        |
|           | 介质访问控制：信道划分（FDM/TDM/WDM/CDM概念）;随机访问              | ★★  | 选择       | 2        |
|           | 局域网：以太网（IEEE 802.3帧格式、MAC地址）VLAN基本原理（隔离广播域）      | ★★★★★ | 选择       | 3        |
|           | 广域网：PPP协议（功能、帧格式）                                | ★   | 选择       | 1        |  1   |
|           | 数据链路层设备：以太网交换机（工作原理、MAC地址表、转发方式）                 |  ★★★★ | 选择       | 2        |
| 四、网络层     | 网络层功能：路由与转发（路由表与转发表区别）                           | ★★  | 选择       | 1        |  
|           | 路由算法：距离-向量路由算法（RIP协议原理、计数到无穷问题）;链路状态路由算法         | ★★★★ | 选择/综合 | 4        |
|           | IPv4：IPv4分组格式;IPv4地址与NAT;子网划分与CIDR ;ARP协议;ICMP协议 | ★★★★ | 选择       | 4        |
|           | IPv6：主要特点（128位地址、无校验和、简化首部）                      | ★★  | 选择       | 2        |  
|           | 路由协议：BGP协议（域间路由，路径向量算法）                          | ★★★★ | 选择       | 2        |  
|           | 网络层设备：路由器组成（路由选择、分组转发流程）                         | ★★★★★ | 综合题     | 3        |
| 五、传输层     | 传输层服务：端口号概念（熟知端口、登记端口、动态端口）                      | ★★  | 选择       | 2        |  
|           | UDP：UDP数据报格式（首部字段）、校验（伪首部）                       | ★★★★ | 选择       | 2        |  
|           | TCP：TCP段格式（序号、确认号、标志位SYN/ACK/FIN/RST、窗口大小）       | ★★★★ | 选择/综合 | 3        |  
|           | TCP：连接管理（三次握手过程及原因、四次挥手过程）                       | ★★★ | 选择/综合 | 4        |  
|           | TCP：可靠传输机制（超时重传、快速重传、确认机制）                       | ★★★ | 综合题     | 4        |  
|           | TCP：流量控制（滑动窗口协议，接收窗口rwnd）                        | ★★★ | 综合题     | 4        |  
|           | TCP：拥塞控制（慢开始、拥塞避免、快重传、快恢复算法流程）                   | ★★★★ | 综合题     | 4        |
| 六、应用层     | 网络应用模型：C/S模型与P2P模型对比                             | ★★  | 选择     | 1        |  
|           | DNS：层次域名空间（根域、顶级域、权威域名服务器）；域名解析过程                | ★★★★ | 选择     | 4        |
|           | FTP：控制连接（21端口）与数据连接（20端口，主动/被动模式）                | ★★  | 选择     | 2        |  
|           | 电子邮件：SMTP协议（发送邮件，25端口）与POP3协议（接收邮件，110端口）        | ★★  | 选择     | 2        |  
|           | WWW：HTTP协议（请求方法GET/POST、状态码200/404/500）；HTTP无状态特性与Cookie机制          | ★★  | 选择/简答 | 1        |  

