import datetime
import json
import math
import random
from collections import defaultdict

from django.db import transaction
from django.db.models import Count

from app.models import (
    EnWordReciteBasicAnswer, EnglishWordLibrary, EnWordRecitePlanRecord, CozeWorkflowResult,
    EnWordRecitePlan, EnWordReciteDayRecord, EnWordReciteQuestion, EnWordReciteBasicPaper, EnWordReciteBasicPaperDetail
)
from app.services.coze_workflow_service import CozeWorkflowService
from app.services.main_subject.en_word_service import EnWordService
from django_ext.exceptions import InternalException


class EnWordReciteService:

    # 总单词数
    TOTAL_WORD_COUNT = 5769

    @classmethod
    def create_basic_paper(cls, user_id):
        word_freq_group = EnWordService.get_word_freq_group_for_recite()

        # 高中低频（6:3:1）
        high_words = random.sample(word_freq_group.get('high'), 12)
        middle_words = random.sample(word_freq_group.get('middle'), 6)
        low_words = random.sample(word_freq_group.get('low'), 2)
        all_words = [*high_words, *middle_words, *low_words]

        question_list = list(EnWordReciteQuestion.objects.filter(
            is_deleted=False, word_id__in=all_words,
        ))
        # 摸底测随机题型取20道
        question_list = random.sample(question_list, min(20, len(question_list)))
        return cls._create_paper(user_id, question_list, EnWordReciteBasicPaper.PaperType.basic_test)

    @classmethod
    def create_week_paper(cls, user_id):
        """
        题目类别权重：
            词义匹配50% 15
            语境选择30% 9
            同义词/反义词辨析20% 6
        :param user_id:
        :return:
        """
        # 出30个单词
        today = datetime.date.today()
        last_7_days = [(today - datetime.timedelta(days=i)) for i in range(7)]
        day_qs = EnWordReciteDayRecord.objects.filter(
            day__in=last_7_days
        ).values('word').annotate(total=Count('id'))
        word_ids = [i['word'] for i in day_qs]
        if not word_ids:
            raise InternalException(code=50000, detail='近一周未背诵单词')

        if len(word_ids) > 30:
            word_freq_group = EnWordService.get_word_freq_group_for_recite()
            # 6: 2: 2 / 18: 6: 6
            high_words = list(set(word_freq_group.get('high')) & set(word_ids))
            middle_words = list(set(word_freq_group.get('middle')) & set(word_ids))
            low_words = list(set(word_freq_group.get('low')) & set(word_ids))

            all_words = []
            low_num = 6
            middle_num = 6
            high_num = 18
            if len(low_words) <= low_num:
                middle_num += (low_num - len(low_words))
                all_words.extend(low_words)
            else:
                low_words = random.sample(low_words, low_num)
                all_words.extend(low_words)

            if len(middle_words) <= middle_num:
                high_num += (middle_num - len(middle_words))
                all_words.extend(middle_words)
            else:
                middle_words = random.sample(middle_words, middle_num)
                all_words.extend(middle_words)

            if len(high_words) <= high_num:
                all_words.extend(high_words)
            else:
                high_words = random.sample(high_words, high_num)
                all_words.extend(high_words)
        else:
            all_words = word_ids

        question_qs = EnWordReciteQuestion.objects.filter(
            is_deleted=False, word_id__in=all_words,
        )

        question_type_map = defaultdict(list)
        for q in question_qs:
            question_type_map[q.question_type].append(q)

        en2ch_questions = question_type_map.get(EnWordReciteQuestion.QuestionType.en2ch, [])
        multi_define_questions = question_type_map.get(EnWordReciteQuestion.QuestionType.multi_define, [])
        relate_define_questions = question_type_map.get(EnWordReciteQuestion.QuestionType.relate_define, [])

        all_questions = []
        en2ch_num = 15
        multi_define_num = 9
        relate_define_num = 6
        if len(relate_define_questions) <= relate_define_num:
            multi_define_num += (relate_define_num - len(relate_define_questions))
            all_questions.extend(relate_define_questions)
        else:
            relate_define_questions = random.sample(relate_define_questions, relate_define_num)
            all_questions.extend(relate_define_questions)

        if len(multi_define_questions) <= multi_define_num:
            en2ch_num += (multi_define_num - len(multi_define_questions))
            all_questions.extend(multi_define_questions)
        else:
            multi_define_questions = random.sample(multi_define_questions, multi_define_num)
            all_questions.extend(multi_define_questions)

        if len(en2ch_questions) <= en2ch_num:
            all_questions.extend(en2ch_questions)
        else:
            en2ch_questions = random.sample(en2ch_questions, en2ch_num)
            all_questions.extend(en2ch_questions)

        return cls._create_paper(user_id, all_questions, EnWordReciteBasicPaper.PaperType.week_test)

    @classmethod
    def _create_paper(
            cls,
            user_id,
            question_list: list[EnWordReciteQuestion],
            paper_type
    ):
        with transaction.atomic():
            paper = EnWordReciteBasicPaper.objects.create(
                user_id=user_id,
                paper_type=paper_type,
                question_num=len(question_list)
            )

            q_objs = []
            for q in question_list:
                q_objs.append(EnWordReciteBasicPaperDetail(
                    basic_paper=paper,
                    question=q
                ))
            EnWordReciteBasicPaperDetail.objects.bulk_create(q_objs)
        return paper

    @classmethod
    def create_plan(cls, answer: EnWordReciteBasicAnswer) -> EnWordRecitePlanRecord:
        """
        本次测试已全部完成，你一共回答了 20 道题目，答对了 18 道，正确率为 90%。高频词汇 12 题，答对 12 题，正确率 100%；中频词汇 6 题，答对 5 题，正确率约 83.33%；低频词汇 2 题，答对 2 题，正确率 100%。
        输出参数：
        {{study_plan}}:(学习计划)
        {{total}}:（每天背的单词总数）
        {{high}}:（每天背的高频单词数目）
        {{middle}}:（每天背的中频单词数目）
        {{low}}:（每天背的低频单词数目）
        :return:
        """
        paper_questions = answer.basic_paper.enwordrecitebasicpaperdetail_set.filter(is_deleted=False)
        paper_word_ids = [i.question.word_id for i in paper_questions]
        if not paper_word_ids:
            raise Exception('摸底试卷没有题目')

        answer_details = answer.enwordrecitebasicanswerdetail_set.filter(is_deleted=False)
        right_words = []
        for i in answer_details:
            if i.is_right:
                right_words.append(i.word_id)

        words = EnglishWordLibrary.objects.filter(is_deleted=False, id__in=paper_word_ids)
        word_freq_map = defaultdict(list)
        for w in words:
            word_freq_map[w.word_freq].append(w.id)

        high_words = word_freq_map.get(EnglishWordLibrary.Freq.high, [])
        middle_words = word_freq_map.get(EnglishWordLibrary.Freq.middle, [])
        low_words = word_freq_map.get(EnglishWordLibrary.Freq.low, [])

        high_right_words = list(set(high_words) & set(right_words))
        middle_right_words = list(set(middle_words) & set(right_words))
        low_right_words = list(set(low_words) & set(right_words))

        high_num = len(high_words)
        high_right_num = len(high_right_words)
        high_right_rate = int(100 * high_right_num / high_num) if high_num else 0

        middle_num = len(middle_words)
        middle_right_num = len(middle_right_words)
        middle_right_rate = int(100 * middle_right_num / middle_num) if middle_num else 0

        low_num = len(low_words)
        low_right_num = len(low_right_words)
        low_right_rate = int(100 * low_right_num / low_num) if low_num else 0

        total_num = high_num + middle_num + low_num
        total_right_num = high_right_num + middle_right_num + low_right_num
        total_right_rate = int(100 * total_right_num / total_num) if total_num else 0

        content = (f'本次测试已全部完成，你一共回答了 {total_num} 道题目，答对了 {total_right_num} 道，正确率为 {total_right_rate}%。'
                   f'高频词汇 {high_num} 题，答对 {high_right_num} 题，正确率 {high_right_rate}%；'
                   f'中频词汇 {middle_num} 题，答对 {middle_right_num} 题，正确率约 {middle_right_rate}%；'
                   f'低频词汇 {low_num} 题，答对 {low_right_num} 题，正确率 {low_right_rate}%。')

        plan: EnWordRecitePlan = EnWordRecitePlan.objects.filter(
            is_deleted=False, basic_answer=answer).first()
        if plan:
            plan_record = plan.enwordreciteplanrecord_set.filter(is_deleted=False).last()
        else:
            with transaction.atomic():
                plan = EnWordRecitePlan.objects.create(
                    user_id=answer.user_id,
                    basic_answer=answer,
                    gen_status='ING',
                )
                plan_record = EnWordRecitePlanRecord.objects.create(
                    plan=plan,
                    gen_plan_content=content,
                    gen_status='ING',
                )

        code = 'english_word_recite_plan'
        params = {'dancijichu': content}
        ext_params = {'answer_id': answer.id}

        result: CozeWorkflowResult = CozeWorkflowService.create_workflow_runs_sync(code, params, ext_params)
        try:
            res = json.loads(json.loads(result.output_str).get('Output'))
            # 校验计划是否正确
            if not ('high' in res and 'middle' in res and 'low' in res and 'total' in res):
                raise Exception(f'计划数据结构错误:{res}')

            if res['high'] == 0 and res['middle'] == 0 and res['low'] == 0:
                raise Exception(f'计划数据内容无效:{res}')
        except Exception as e:
            result.process_status = 'FAIL'
            result.process_fail_reason = str(e)
            result.save()

            # 如果解析内容失败，或者里面内容有误，则用默认值
            res = {"low": 6, "high": 15, "total": 30, "middle": 9,
                   "new_study_plan": "每天学习30个单词，其中高频词汇15个，中频词汇9个，低频词汇6个"}

        # 计算背诵单词需要的天数
        res.update({'days': math.ceil(cls.TOTAL_WORD_COUNT / res['total'])})

        plan.gen_status = 'SUCCESS'
        plan.plan_content = res
        plan.last_plan_gen_date = datetime.date.today()
        plan.save()

        plan_record.gen_status = 'SUCCESS'
        plan_record.plan_content = res
        plan_record.use_status = EnWordRecitePlanRecord.UseStatus.used
        plan_record.save()

        return plan_record

    @classmethod
    def change_plan(cls, plan: EnWordRecitePlan) -> EnWordRecitePlanRecord:
        """
        调整计划输入参数：
        上周情况完成度
        第一天：目标60个，学习了60个；
        第二天：目标60个，学习了50个；
        第三天：目标60个，学习了60个；
        第四天：目标60个，学习了20个；
        第五天：目标60个，学习了50个；
        第六天：目标60个，学习了30个；
        第七天：目标60个，学习了60个；
        输出参数：
        {{new_study_plan}}:(学习计划)
        {{total}}:（每天背的单词总数）
        {{high}}:（每天背的高频单词数目）
        {{middle}}:（每天背的中频单词数目）
        {{low}}:（每天背的低频单词数目）
        :return:
        """
        total_num = plan.plan_content.get('total')
        # 获取当前日期
        today = datetime.date.today()
        last_7_days = [(today - datetime.timedelta(days=i)) for i in range(1, 8)]

        last_plan_record = plan.enwordreciteplanrecord_set.filter(is_deleted=False).last()
        day_qs = EnWordReciteDayRecord.objects.filter(plan=plan, plan_record=last_plan_record, day__in=last_7_days)
        day_map = defaultdict(list)
        for i in day_qs:
            day_map[i.day.strftime("%Y-%m-%d")].append(i)

        last_7_days_data = []
        for d in last_7_days:
            day_records = day_map.get(d.strftime("%Y-%m-%d"), [])
            last_7_days_data.append(len(day_records))

        content = f"""上周情况完成度
第一天：目标{total_num}个，学习了{last_7_days_data[6]}个；
第二天：目标{total_num}个，学习了{last_7_days_data[5]}个；
第三天：目标{total_num}个，学习了{last_7_days_data[4]}个；
第四天：目标{total_num}个，学习了{last_7_days_data[3]}个；
第五天：目标{total_num}个，学习了{last_7_days_data[2]}个；
第六天：目标{total_num}个，学习了{last_7_days_data[1]}个；
第七天：目标{total_num}个，学习了{last_7_days_data[0]}个；"""

        plan.gen_status = 'ING'
        plan.save()

        plan_record = EnWordRecitePlanRecord.objects.create(
            plan=plan,
            gen_plan_content=content,
            gen_status='ING',
        )

        code = 'english_word_recite_plan_change'
        params = {'situtation': content}
        ext_params = {'plan_id': plan.id}
        result: CozeWorkflowResult = CozeWorkflowService.create_workflow_runs_sync(code, params, ext_params)

        try:
            res = json.loads(json.loads(result.output_str).get('Output'))
            # 校验计划是否正确
            if not ('high' in res and 'middle' in res and 'low' in res and 'total' in res):
                raise Exception(f'计划数据结构错误:{res}')

            if res['high'] == 0 and res['middle'] == 0 and res['low'] == 0:
                raise Exception(f'计划数据内容无效:{res}')
        except Exception as e:
            result.process_status = 'FAIL'
            result.process_fail_reason = str(e)
            result.save()

            res = {"low": 6, "high": 15, "total": 30, "middle": 9,
                   "new_study_plan": "每天学习30个单词，其中高频词汇15个，中频词汇9个，低频词汇6个"}

        # 计算背诵单词需要的天数
        studied_word_count = EnWordReciteDayRecord.objects.filter(plan=plan, is_deleted=False).count()
        left_word_count = max(0, cls.TOTAL_WORD_COUNT - studied_word_count)
        res.update({'days': math.ceil(left_word_count / res['total'])})

        plan_record.gen_status = 'SUCCESS'
        plan_record.plan_content = res
        plan_record.save()
        plan.gen_status = plan_record.gen_status
        plan.save()

        return plan_record
