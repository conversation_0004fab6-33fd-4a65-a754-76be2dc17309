from django.urls import path

from . import views

urlpatterns = [
    path('v1/client/support_params', views.AppSupportParamsView.as_view()),
    path('v1/client/prompt_templates/article_generation', views.AppPromptTemplateListView.as_view()),
    path('v1/client/app_add', views.AppAddListView.as_view()),
    path('v1/client/app_list', views.AppAddListView.as_view()),
    path('v1/client/app/<str:app_id>/info', views.AppManageView.as_view()),
    path('v1/client/app/<str:app_id>/edit', views.AppManageView.as_view()),
    path('v1/client/app/<str:app_id>/delete', views.AppManageView.as_view()),
]

urlpatterns += [
    path('v1/sensitive_word_review', views.SensitiveWordReviewView.as_view()),
]

urlpatterns += [
    path('v1/add_conversation', views.ConversationAddView.as_view()),
    path('v1/conversation_token_exceed', views.ConversationTokenExceedView.as_view()),
    path('v1/chat_messages', views.ChatMessageView.as_view()),
    path('v1/chat_messages/<str:message_id>/stop', views.ChatMessageStopView.as_view()),
    path('v1/chat_messages/<str:message_id>/retry', views.ChatMessageRetryView.as_view()),
    path('v1/chat_messages/<str:message_id>/ref_source', views.MessageRefSourceView.as_view()),
    path('v1/chat_messages/task_status', views.MessageTaskStatusView.as_view()),
]


# coze 问答
urlpatterns += [
    path('v1/coze/add_conversation', views.CozeConversationAddView.as_view()),
    path('v1/coze/chat_messages', views.CozeChatMessageView.as_view()),
    path('v1/coze/chat_cancel/<str:message_id>', views.CozeChatCancelView.as_view()),
]

# 特殊应用单独处理
urlpatterns += [
    path('v1/app/prompt_optimize', views.PromptOptimizeView.as_view()),
    path('v1/app/prologue_generate', views.PrologueGenerateView.as_view()),
    path('v1/app/document_extraction', views.DocumentExtractionView.as_view()),
    path('v1/app/document_summary', views.DocumentSummaryView.as_view()),

    path('v1/app/content_extractor', views.ContentExtractorView.as_view()),
    path('v1/app/red_generator', views.RedGeneratorView.as_view()),

    path('v1/app/video_script', views.VideoScriptView.as_view()),
    # 单独代码优化
    path('v1/app/code_optimization', views.CodeOptimizationView.as_view()),
    # 单独解题助手
    path('v1/app/problem_solving', views.ProblemSolvingView.as_view()),
    # 数学解题助手
    path('v1/app/math_problem_solving', views.MathProblemSolvingView.as_view()),

    # 英语长难句解析
    path('v1/app/ocr_result', views.OcrResultView.as_view()),
    path('v1/app/complex_sentence_analysis', views.ComplexSentenceAnalysisView.as_view()),
]

urlpatterns += [
    path('v1/dataset_add', views.DatasetAddView.as_view()),
    path('v1/datasets/<str:dataset_no>/add_documents', views.DatasetAddDocumentsView.as_view()),
    path('v1/datasets/<str:dataset_no>/del_documents', views.DatasetDelDocumentsView.as_view()),

    path('v1/datasets/documents', views.DatasetDocumentsView.as_view()),
    path('v1/datasets/<str:dataset_no>/delete', views.DatasetDeleteView.as_view()),

    # 即将废弃⚠️
    path('v1/datasets/<str:dataset_no>/set_documents', views.DatasetSetDocumentsView.as_view()),

    path('v1/datasets/add_knowledge_document', views.DatasetAddKnowledgeDocumentView.as_view()),
    path('v1/datasets/add_subtitle_document', views.DatasetAddSubtitleDocumentView.as_view()),
]

urlpatterns += [
    path('v1/datasets/document_knowledge', views.DocumentKnowledgeGenView.as_view()),
    path('v1/datasets/document_knowledge_add', views.DocumentKnowledgeAddView.as_view()),
    path('v1/datasets/document_knowledge/<int:pk>/edit', views.DocumentKnowledgeEditView.as_view()),
    path('v1/datasets/document_knowledge/<int:pk>/delete', views.DocumentKnowledgeDeleteView.as_view()),
    path('v1/datasets/knowledge_search', views.DocumentKnowledgeSearchView.as_view()),
    path('v1/datasets/knowledge_regenerate', views.DocumentKnowledgeRegenerateView.as_view()),
    path('v1/datasets/knowledge_deep_search', views.DocumentKnowledgeDeepSearchView.as_view()),
    path('v1/datasets/knowledge_search_retry', views.DocumentKnowledgeSearchRetryView.as_view()),
]

urlpatterns += [
    path('v1/course_note/create_task', views.CourseNoteCreateTaskView.as_view()),
    path('v1/course_note/task_status', views.CourseNoteTaskStatusView.as_view()),
    path('v1/course_note/video_keywords', views.CourseNoteVideoKeywordsView.as_view()),
]

urlpatterns += [
    path('v1/course_section/video_knowledge_extract', views.CSVideoKnowledgeExtractView.as_view()),
]

urlpatterns += [
    path('v1/skip/user_detail', views.UserDetailView.as_view()),
    path('v1/skip/question_detail', views.QuestionDetailView.as_view()),
    path('v1/skip/paper_detail', views.PaperDetailView.as_view()),
    path('v1/skip/paper_answer_detail', views.PaperAnswerDetailView.as_view()),
    path('v1/math/video_abstract', views.MathVideoAbstractView.as_view()),
    path('v1/math/video_knowledge_extract', views.MathVideoKnowledgeExtractView.as_view()),

    path('v1/question_knowledge_extract', views.QuestionKnowledgeExtractView.as_view()),
    path('v1/test_answer_report_generator', views.TestAnswerReportGeneratorView.as_view()),
    path('v1/english/waikan_question_detail', views.EnglishReaderQuestionDetailView.as_view()),
    path('v1/english/waikan_again_question_detail', views.EnglishReaderAgainQuestionDetailView.as_view()),
    path('v1/english/waikan_question_report', views.EnglishReaderReportView.as_view()),
    path('v1/english_word_list', views.EnglishWordListView.as_view()),

]

# 英语词汇出题
urlpatterns += [
    path('v1/english/word_question', views.EnglishWordQuestionView.as_view()),
    path('v1/english/word_question_submit', views.EnglishWordQuestionSubmitView.as_view()),
    path('v1/english/word_wrong_question_count', views.EnglishWordWrongQuestionCountView.as_view()),
    path('v1/english/word_wrong_question', views.EnglishWordWrongQuestionView.as_view()),
    path('v1/english/word_wrong_question_submit', views.EnglishWordWrongQuestionSubmitView.as_view()),
]

# 英语单词背诵
urlpatterns += [
    path('v1/english/word_recite_word_search', views.EnglishWordSearchView.as_view()),
    path('v1/english/word_recite_basic_question', views.EnglishWordReciteBasicQuestion.as_view()),
    path('v1/english/word_recite_post_basic_question', views.EnglishWordRecitePostBasicQuestion.as_view()),
    path('v1/english/word_recite_week_test', views.EnglishWordReciteWeekTest.as_view()),
    path('v1/english/word_recite_post_week_test', views.EnglishWordRecitePostBasicQuestion.as_view()),
    path('v1/english/word_recite_plan_manual_change', views.EnglishWordRecitePlanManualChange.as_view()),

    path('v1/english/word_recite_daily_question', views.EnglishWordReciteDailyQuestion.as_view()),
    path('v1/english/word_recite_daily_post_question', views.EnglishWordReciteDailyPostQuestion.as_view()),
    path('v1/english/word_recite_review_post_question', views.EnglishWordReciteReviewPostQuestion.as_view()),
    path('v1/english/word_recite_change_plan', views.EnglishWordReciteChangePlan.as_view()),
    path('v1/english/word_recite_check_plan', views.EnglishWordReciteCheckPlan.as_view()),
    path('v1/english/word_recite_plan_use', views.EnglishWordRecitePlanUse.as_view()),
]

# 英语试卷解读
urlpatterns += [
    path('v1/english/paper_explain', views.EnglishPaperExplainView.as_view()),
    path('v1/english/paper_explain_status', views.EnglishPaperExplainStatusView.as_view()),
]

# 学情分析
urlpatterns += [
    path('v1/learning_analysis_submit', views.StudentLearnAnalysisView.as_view()),
    path('v1/learning_analysis_status', views.StudentLearnAnalysisStatusView.as_view()),
    path('v1/dsx/learning_stat', views.ExercisesLearnStatusView.as_view()),
    path('v1/learn/user_study_plan_stat/', views.SuperviseLearnStatusView.as_view()), # 教务督学提交
    path('v1/learn/user_study_plan_stat_check', views.SuperviseLearnStatusCheckView.as_view()), # 教务督学查看
    path('v1/learning_status/supervise_init/', views.SuperviseInitStatusView.as_view()), # 教务督学初始化状态提交
    path('v1/learning_status/supervise_init/check', views.SuperviseInitStatusCheckView.as_view()), # 教务督学初始化状态查看
    path('v1/learning_status/Personalized_Exam_Syllabus', views.PersonalizedExamSyllabusView.as_view()),
]


urlpatterns += [
    path('v1/knowledge/subject_list', views.KnowledgeSubjectListView.as_view()),
    path('v1/knowledge/knowledge_list', views.SubjectKnowledgeListView.as_view()),
    path('v1/knowledge/knowledge_detail', views.SubjectKnowledgeDetailView.as_view()),
    path('v1/gaoshu/screenshot_question', views.GaoshuScreenshotQuestionView.as_view()),
    path('v1/college/huolande_test', views.HuolandeTestView.as_view()),
    path('v1/college/college_analysis', views.CollegeAnalysisView.as_view()),
    path('v1/college/kaoyan_review_plan', views.KaoYanReviewPlanNewView.as_view()),
]

# 学习指导
urlpatterns += [
    path('v1/dsx/learning_guide',views.StudyGuidesView.as_view()),
    path('v1/dsx/learning_guide_app', views.StudyGuidesAppView.as_view()),
]


from .views import document_proofreader_views as dpviews
# 讲义审校
urlpatterns += [
    path('v1/document_proofreader/', dpviews.DocumentProofreaderView.as_view(), name="document_proofreader"),
    # 异步任务状态查询路由
    path('v1/document_proofreader/async_status/', dpviews.DocumentProofreaderAsyncStatusView.as_view(), name="document_proofreader_async_status"),
    path('v1/document_proofreader/async_batch_status/', dpviews.DocumentProofreaderAsyncBatchStatusView.as_view(), name="document_proofreader_async_batch_status"),
    # AI内容生成
    path('v1/document_proofreader/ai_creation/', dpviews.DocumentAICreationView.as_view(), name="document_ai_creation"),
]

# 动手学（代码练习解析）
urlpatterns += [
    path('v1/code_exercise/submit', views.CodeExerciseView.as_view()),
]

# 知舟问答2.0
urlpatterns += [
    path('v1/ask/invoke', views.DayiAppView.as_view()),
    path('v1/ask/invoke_retry', views.DayiAppRetryView.as_view()),
]

#智痕雷达
urlpatterns += [
    # path('v1/language_style')
    path('v1/zhihen_radar',views.ZhihenRadarView.as_view()),
]


# 试卷分析
urlpatterns += [
    path('v1/exam_analysis/subject_list', views.ExamAnalysisSubjectView.as_view()),
    path('v1/exam_analysis/analysis_data_word_cloud', views.ExamAnalysisDataWordCloudView.as_view()),
    path('v1/exam_analysis/analysis_data_heatmap', views.ExamAnalysisDataHeatMapView.as_view()),
    path('v1/exam_analysis/analysis_data_kp_stats', views.ExamAnalysisDataKpStatsView.as_view()),
    path('v1/exam_analysis/analysis_data_score_distribution', views.ExamAnalysisDataScoreDisView.as_view()),
    path('v1/exam_analysis/analysis_data_kp_difficulty', views.ExamAnalysisDataKpDifficultyView.as_view()),
    path('v1/exam_analysis/analysis_data_total', views.ExamAnalysisDataTotalView.as_view()),
]

# 动手学知识点出题
urlpatterns += [
    path('v1/dsx/gene_question',views.GeneQuestionView.as_view()),
]

# 知识解析基础版
urlpatterns += [
    path('v1/datasets/knowledge_analysis',views.KnowledgeAnalysisView.as_view()),
    path('v1/datasets/knowledge_analysis_regenerate', views.KnowledgeAnalysisRegenerateView.as_view())
]

# 考研规划迭代版
urlpatterns += [
    path('v1/college/college_analysis_new', views.CollegeAnalysisNewView.as_view()),
    path('v1/college/college_analysis_new_with_goal', views.CollegeAnalysisNewWithGoalView.as_view()),
    path('v1/college/college_analysis_new_with_goal/get_colleges', views.CollegeAnalysisNewCollegeView.as_view()),
]

# 408题库刷题
urlpatterns += [
    path('v1/ai_practice/paper_detail', views.AIPracticePaperDetailView.as_view()),
    path('v1/ai_practice/paper_question_submit', views.AIPracticeQuestionPaperSubmitView.as_view()),
    path('v1/ai_practice/subjective_answer_report', views.AIPracticeSubjectiveReportView.as_view()),
    path('v1/ai_practice/paper_submit', views.AIPracticePaperSubmitView.as_view()),
    path('v1/ai_practice/paper_answer', views.AIPracticePaperAnswerView.as_view()),
    path('v1/ai_practice/paper_report', views.AIPracticePaperReportView.as_view()),
    path('v1/ai_practice/stage_change_info', views.AIPracticeStageChangeInfoView.as_view()),
    path('v1/ai_practice/extract_408_knowledge', views.ExtractKnowledgeView.as_view()),
]
