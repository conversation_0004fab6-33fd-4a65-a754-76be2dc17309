import csv
import json
import time
from datetime import datetime

from app.api.college_analysis.college_information import get_college_and_major_info
from app.api.college_analysis.huolande_test import huolande_test
from app.errors import ParameterError
from app.models import App, CourseSectionKnowledge, GaoshuUserQuestionRecord, KnowledgeLibrary
from app.services.college_analysis_new_service import CollegeAnalysisNewService
from api_client.account.client import account_client
from api_client.account.dto import UserDetailDto
from app.api.dto import (
    CSVideoKnowledgeExtractDto, ChatMessageDto, QuestionKnowledgeExtractDto,
    EnglishReaderReportDto, EnglishReaderAgainReportDto, EnglishReaderQuestionDetailDto, GaoshuScreenshotQuestionDto,
    HuolandeTestDto, CollegeAnalysisDto, KaoYanReviewPlanDto, CollegeAnalysisNewDto,
)
from app.api.validators import (
    CSVideoKnowledgeExtractValidator, QuestionKnowledgeExtractValidator,
    EnglishReaderReportValidator, EnglishReaderReportAgainValidator, EnglishReaderQuestionDetailValidator,
    GaoshuScreenshotQuestionValidator, HuolandeTestValidator, CollegeAnalysisValidator, KaoYanReviewPlanValidator,
    CollegeAnalysisNewValidator,
)

from app.services.app_generate_service import AppGenerateService
from app.services.english_word_list import EnglishWordListService
from app.services.main_subject.course_section_knowledge_service import CourseSectionKnowledgeService
from app.services.main_subject.question_knowledge_service import QuestionKnowledgeService
from app.services.main_subject.test_answer_report_service import TestAnswerReportService
from app.services.main_subject.new_question_service import NewQuestionService
from app.services.main_subject.test_paper_service import TestPaperService
from app.services.screenshot_question_service import ScreenshotQuestionService
from app.services.waikan_question_bank import WaiKanQuestionService
from django_ext import base_view
from django_ext.response import make_response, make_stream_response
from app.services.coze_workflow_service import gaoshu_ocr, gaoshu_ocr_judge


class UserDetailView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        user_id = request.query_params.get('user_id')
        if not user_id:
            raise ValueError('user_id is empty')

        info: UserDetailDto = account_client.get_user_detail(user_id)
        info_dict = {
            'uuid': info.uuid,
            'nickname': info.nickname,
            'realname': info.realname,
            'sex': info.sex,
        } if info else {}
        return make_response(info_dict)


class QuestionDetailView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        question_id = request.query_params.get('question_id')
        if not question_id:
            raise ValueError('question_id is empty')

        data = NewQuestionService.get_question_detail(question_id)
        return make_response(data)


class PaperDetailView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        paper_id = request.query_params.get('paper_id')
        if not paper_id:
            raise ValueError('paper_id is empty')

        data = TestPaperService.get_paper_detail(paper_id)
        return make_response(data)


class PaperAnswerDetailView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        answer_id = request.query_params.get('answer_id')
        if not answer_id:
            raise ValueError('answer_id is empty')

        data = TestPaperService.get_answer_detail(answer_id)
        print(data)
        return make_response(data)


class EnglishReaderQuestionDetailView(base_view.BaseView):
    validator_class = EnglishReaderQuestionDetailValidator
    def post(self, request, *args, **kwargs):
        dto: EnglishReaderQuestionDetailDto = self.validate_request(request.data)
        userinfo = dto.userinfo
        user_id = userinfo.get('user_id')
        if not user_id:
            raise ValueError('user_id is empty')

        data = WaiKanQuestionService.get_question_detail(user_id)

        return make_response(data)


class EnglishReaderAgainQuestionDetailView(base_view.BaseView):
    validator_class = EnglishReaderReportAgainValidator
    def post(self, request, *args, **kwargs):

        dto: EnglishReaderAgainReportDto = self.validate_request(request.data)
        userinfo = dto.userinfo
        user_id = userinfo.get('user_id')
        question_id = dto.question_id
        wrong_sub_question_ids = dto.wrong_sub_question_ids
        print(f"dto啊啊啊啊啊啊啊啊啊{dto}")
        if not userinfo:
            raise ValueError('user_id is empty')

        data = WaiKanQuestionService.get_again_question_detail(user_id,question_id,wrong_sub_question_ids)

        return make_response(data)


class HuolandeTestView(base_view.BaseView):
    validator_class = HuolandeTestValidator

    def post(self, request, *args, **kwargs):
        dto: HuolandeTestDto = self.validate_request(request.data)

        # 提取 part1 到 part5 的数据
        part1 = {
            'realistic': dto.part1_realistic,
            'investigative': dto.part1_investigative,
            'artistic': dto.part1_artistic,
            'social': dto.part1_social,
            'enterprising': dto.part1_enterprising,
            'conventional': dto.part1_conventional,

        }

        part2 = {
            'realistic': dto.part2_realistic,
            'investigative': dto.part2_investigative,
            'artistic': dto.part2_artistic,
            'social': dto.part2_social,
            'enterprising': dto.part2_enterprising,
            'conventional': dto.part2_conventional,

        }

        part3 = {
            'realistic': dto.part3_realistic,
            'investigative': dto.part3_investigative,
            'artistic': dto.part3_artistic,
            'social': dto.part3_social,
            'enterprising': dto.part3_enterprising,
            'conventional': dto.part3_conventional,

        }

        part4 = {
            'realistic': dto.part4_realistic,
            'investigative': dto.part4_investigative,
            'artistic': dto.part4_artistic,
            'social': dto.part4_social,
            'enterprising': dto.part4_enterprising,
            'conventional': dto.part4_conventional,

        }

        part5 = {
            'realistic': dto.part5_realistic,
            'investigative': dto.part5_investigative,
            'artistic': dto.part5_artistic,
            'social': dto.part5_social,
            'enterprising': dto.part5_enterprising,
            'conventional': dto.part5_conventional,

        }

        # 组装 content 变量
        content = [
            {
                'results': {
                    'part1': {'dimensions': part1},
                    'part2': {'dimensions': part2},
                    'part3': {'dimensions': part3},
                    'part4': {'dimensions': part4},
                    'part5': {'dimensions': part5}
                }
            }
        ]

        response = huolande_test(content)
        return make_response(response)


class CollegeAnalysisView(base_view.BaseView):
    validator_class = CollegeAnalysisValidator

    def post(self, request, *args, **kwargs):
        dto: CollegeAnalysisDto = self.validate_request(request.data)

        # # 提取目标院校和专业
        # target_colleges = []
        # for college in dto.target_college_major:
        #     target_colleges.append({
        #         'college_name': college.college_name,
        #         'college_code':college.college_code,
        #         'major_name': college.major_name,
        #         'major_code':college.major_code
        #     })

        # 提取霍兰德测试结果
        holland_test_result = dto.holland_test_result

        # 构建 all_fields 字典
        all_fields = {
            # 基本信息
            '基本信息': {
                '本科院校层次': dto.bachelor_level,
                '本科专业': dto.bachelor_major,
                '是否跨考': dto.is_cross_exam,
                '考研年份': dto.exam_year,
            },

            # 成绩背景
            '成绩背景': {
                '总GPA区间': dto.gpa_range,
                '专业排名': dto.major_ranking,
                '科研经历': dto.research_experience,
                "竞赛经历": dto.competition_experience,
            },

            # 能力评估
            '能力评估': {
                '英语能力': dto.english_ability,
                '数学基础': dto.math_basis_select,
            },

            # 备考状态
            '备考状态': {
                '考生身份': dto.candidate_status,
                '实习经历': dto.intern_experience,
                '工作经历': dto.job_experience,
                '同步备考': dto.concurrent_preparation,
                '日均学习时间': dto.daily_learning_time,
            },

            # 院校需求
            '院校需求': {
                '意向地区': dto.regions,
                '学费敏感度': dto.tuition_sensitivity,
                '院校专业地区优先级排序': dto.priority_order,
                '目标专业方向': [major.dict() for major in
                                 dto.target_major_direction] if dto.target_major_direction else [],
                '想要考学硕/专硕': dto.master_type,
                '目标院校和专业': [college.dict() for college in
                                   dto.target_college_major] if dto.target_college_major else [],
            },

            # 个性需求
            '个性需求': {
                '备考痛点': dto.preparation_pain_points,
                '优先服务': dto.preferred_services,
                '读研期间对实习、住宿资源需求': dto.internship_accommodation_needs,
                '调剂底线': dto.admission_baseline,
                '霍兰德职业测试结果': dto.holland_test_result,
                '个人需求': dto.personal_needs,
                '硕士类型': dto.master_degree_type,
            },

            # 联系方式
            '联系方式': {
                '联系方式': dto.contact_info,
            },
        }
        # 解析dto.priority_order（前端改）

        # dto.priority_order = "region,college_level"

        college_result = get_college_and_major_info(
            [major.dict() for major in dto.target_major_direction] if dto.target_major_direction else [],
            dto.bachelor_level,
            dto.master_degree_type,
            dto.regions,
            dto.priority_order
        )
        query = json.dumps(all_fields, ensure_ascii=False)

        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            'target_colleges': [college.dict() for college in
                                dto.target_college_major] if dto.target_college_major else [],
            'holland_test_result': holland_test_result,
            "report_id": dto.report_id,
            'college_result': college_result,
        }

        chat_dto = ChatMessageDto(
            app_id='college_analysis',
            query=query,
            stream=dto.stream,
            inputs=inputs,
        )

        response = AppGenerateService.generate(chat_dto, request.user)

        if dto.stream:
            return make_stream_response(response)
        else:
            return make_response({
                'content': response.get('answer', '')
            })


class KaoYanReviewPlanView(base_view.BaseView):
    validator_class = KaoYanReviewPlanValidator

    def post(self, request, *args, **kwargs):
        dto: KaoYanReviewPlanDto = self.validate_request(request.data)

        # 获取当前时间
        start_review_time = datetime.now().strftime('%Y-%m-%d')

        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            "report_id": dto.report_id,
            "start_review_time":start_review_time,
            'target_college_level':dto.target_college_level,
           }

        chat_dto = ChatMessageDto(
            app_id='kaoyan_review_plan',
            query='',
            stream=dto.stream,
            inputs=inputs,
        )

        response = AppGenerateService.generate(chat_dto, request.user)
        print(f"第四步response👍在这{response}")
        if dto.stream:
            return make_stream_response(response)
        else:
            print("🔥🚀🚀")
            return make_response({
                'content': response.get('answer', '')
            })

class GaoshuScreenshotQuestionView(base_view.BaseView):
    validator_class = GaoshuScreenshotQuestionValidator

    def post(self, request, *args, **kwargs):
        dto: GaoshuScreenshotQuestionDto = self.validate_request(request.data)
        pic = dto.pic
        question = dto.question
        # keywords = dto.keywords
        userinfo = dto.userinfo
        user_id = userinfo.get('user_id')
        course_id = dto.course_id
        if not pic or not user_id:
            raise ParameterError()

        # 先通过course_id查询出对应的课节知识点，需修改之后加入定义
        knowledge_records = CourseSectionKnowledge.objects.filter(
            course_section_id=course_id
        )
        knowledge_records_list = []
        knowledge_list_values = list(knowledge_records.values_list('knowledge_list', flat=True))
        for record in knowledge_list_values:
            try:
                knowledge_records_list.extend(record)
            except (json.JSONDecodeError, TypeError):
                continue

        # 获取ocr识别内容
        description = gaoshu_ocr(pic,user_id)
        print(f"图片内容：{description}")


        # 获取判断是否有问有题以及提取知识点结果
        data = gaoshu_ocr_judge(description,user_id,question,knowledge_list_values)

        judge = data.get('judge')
        knowledge = data.get('knowledge')

        print (f"知识点列表的类型是{type(knowledge)}")
        print("Judge:", judge)
        print("Knowledge:", knowledge)

        # 图片中提取到的知识点筛选对应的定义
        knowledge_details = []
        for name in knowledge:
            knowledge_record = KnowledgeLibrary.objects.filter(name=name).first()
            if knowledge_record:
                knowledge_details.append({
                    'name': knowledge_record.name,
                    'desc': knowledge_record.desc
                })
            else:
                knowledge_details.append({
                    'name': name,
                    'desc': '无'
                })
        keywords = "**包含知识点**\n"
        for idx, detail in enumerate(knowledge_details, start=1):
            keywords += f"{idx}.**{detail['name']}**：{detail['desc']}\n"
        print(f"知识点详情列表：{keywords}")

        if judge == "00":
            return make_response(keywords)

        app_model: App = App.objects.filter(app_type="screenshot_gaoshu").first()
        account = request.user
        params = {
            "judge":judge,
            "keywords":keywords,
            "pic": description,
            "question": question
        }

        message = ScreenshotQuestionService.create_message(
            app_model=app_model,
            from_account=account,
            userinfo=userinfo,
            params=params
        )
        GaoshuUserQuestionRecord.objects.create(
            message=message,
            user_id=user_id,
            course_section_id=dto.course_id,
            question=question,
            knowledge_list=knowledge_records_list
        )

        response = ScreenshotQuestionService.submit_question(
            message=message,
            params=params,
        )

        return make_stream_response(response)


class CollegeAnalysisNewView(base_view.BaseView):
    validator_class = CollegeAnalysisNewValidator

    def post(self, request, *args, **kwargs):
        dto: CollegeAnalysisNewDto = self.validate_request(request.data)

        # 提取霍兰德测试结果
        # holland_test_result = dto.holland_test_result

        # 构建 all_fields 字典
        all_fields = {
            # 基本信息
            '基本信息': {
                '本科院校名称': dto.bachelor_college_name,
                '本科院校代码': dto.bachelor_college_code,
                '本科专业': dto.bachelor_major, # 二级学科例如：0201经济学类
            },

            # 成绩背景
            '成绩背景': {
                '总GPA区间': dto.gpa_range,
                '专业排名': dto.major_ranking,
                '科研经历': dto.research_experience,
                "竞赛经历": dto.competition_experience,
            },

            # 能力评估
            '能力评估': {
                '英语能力': dto.english_ability,
                '数学基础': dto.math_basis_select,
            },

            # 院校需求
            '院校需求': {
                '意向地区': dto.regions, #改为列表
                '院校专业地区优先级排序': dto.priority_order,
                '想要考学硕/专硕': dto.master_type
            },

            # 个性需求
            '个性需求': {
                '主观诉求': dto.personal_needs,
                '是否跨考':dto.cross_exam,
            },

        }
        recommend_college_major_info = CollegeAnalysisNewService.filter_by_bachelor_major_code(dto.bachelor_major,
                                                                                               dto.personal_needs,
                                                                                               dto.bachelor_college_code,
                                                                                               dto.regions,dto.cross_exam)

        query = json.dumps(all_fields, ensure_ascii=False)

        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            # 'holland_test_result': holland_test_result,
            'recommend_college_major_info': recommend_college_major_info,
        }

        chat_dto = ChatMessageDto(
            app_id='college_analysis_new',
            query=query,
            stream=dto.stream,
            inputs=inputs,
        )

        response = AppGenerateService.generate(chat_dto, request.user)

        if dto.stream:
            return make_stream_response(response)
        else:
            return make_response({
                'content': response.get('answer', '')
            })


class CollegeAnalysisNewCollegeView(base_view.BaseView):
    def get(self, request, *args, **kwargs):

        college_data = []
        csv_path = '本科院校_更新2.csv'

        with open(csv_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            next(reader)
            for row in reader:
                if len(row) >= 2:
                    college_data.append({
                        'name': row[0],
                        'code': row[1]
                    })

        return make_response(college_data)

class CollegeAnalysisNewWithGoalView(base_view.BaseView):
    validator_class = CollegeAnalysisNewValidator

    def post(self, request, *args, **kwargs):
        dto: CollegeAnalysisNewDto = self.validate_request(request.data)

        # 构建 all_fields 字典
        all_fields = {
            # 基本信息
            '基本信息': {
                '本科院校名称': dto.bachelor_college_name,
                '本科院校代码': dto.bachelor_college_code,
                '本科专业': dto.bachelor_major, # 二级学科例如：0201经济学类
            },

            # 成绩背景
            '成绩背景': {
                '总GPA区间': dto.gpa_range,
                '专业排名': dto.major_ranking,
                '科研经历': dto.research_experience,
                "竞赛经历": dto.competition_experience,
            },

            # 能力评估
            '能力评估': {
                '英语能力': dto.english_ability,
                '数学基础': dto.math_basis_select,
            },

            # 院校需求
            '院校需求': {
                '意向地区': dto.regions, #改为列表
                '院校专业地区优先级排序': dto.priority_order,
                '想要考学硕/专硕': dto.master_type
            },

            # 个性需求
            '个性需求': {
                '主观诉求': dto.personal_needs,
                '是否跨考':dto.cross_exam,
            },

            '目标院校': {
                '目标院校名称':dto.target_college_name,
                '目标院校代码': dto.target_college_code,
                '目标专业名称': dto.target_major_name,
                '目标专业代码': dto.target_major_code,
            }
        }

        analysis_result = CollegeAnalysisNewService.college_analysis_with_goal(dto.bachelor_major,dto.bachelor_college_code,dto.target_college_code,dto.target_major_code,dto.priority_order)

        # print(f"分析的差距数据🔥:{analysis_result}")
        # print(f"学生所填信息🔥:{all_fields}")
        query = json.dumps(all_fields, ensure_ascii=False)

        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            'analysis_result': analysis_result,
        }

        chat_dto = ChatMessageDto(
            app_id='college_analysis_new_with_goal',
            query=query,
            stream=dto.stream,
            inputs=inputs,
        )

        response = AppGenerateService.generate(chat_dto, request.user)

        if dto.stream:
            return make_stream_response(response)
        else:
            return make_response({
                'content': response.get('answer', '')
            })


class EnglishReaderReportView(base_view.BaseView):
    validator_class = EnglishReaderReportValidator

    def post(self, request, *args, **kwargs):
        dto: EnglishReaderReportDto = self.validate_request(request.data)

        # 提取 inputs 内容
        inputs = {
            "question_id": dto.question_id,
            "answer_detail": [sub_question.dict() for sub_question in dto.answer_detail],  # 转换为字典
            "userinfo": dto.userinfo
        }

        chat_dto = ChatMessageDto(
            app_id='waikan_question_first_report',
            query='',
            inputs=inputs,
            stream=False
        )

        response = AppGenerateService.generate(chat_dto, request.user)

        return make_response({
            'content': response.get('answer', '')
        })


class EnglishWordListView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        word_freq= request.query_params.get('word_freq')
        if not word_freq:
            raise ValueError('word_freq is empty')

        data = EnglishWordListService.get_word_list(word_freq)
        return make_response(data)


class MathVideoAbstractView(base_view.BaseView):
    validator_class = CSVideoKnowledgeExtractValidator

    def post(self, request, *args, **kwargs):
        dto: CSVideoKnowledgeExtractDto = self.validate_request(request.data)

        video_abstract = CourseSectionKnowledgeService.abstract_video(dto)
        return make_response({
            'video_abstract': video_abstract
        })


class MathVideoKnowledgeExtractView(base_view.BaseView):
    validator_class = CSVideoKnowledgeExtractValidator

    def post(self, request, *args, **kwargs):
        dto: CSVideoKnowledgeExtractDto = self.validate_request(request.data)

        knowledge_list = CourseSectionKnowledgeService.extract_video_knowledge(dto)
        return make_response({'knowledge_list': knowledge_list})


class CSVideoKnowledgeExtractView(base_view.BaseView):
    validator_class = CSVideoKnowledgeExtractValidator

    def post(self, request, *args, **kwargs):
        dto: CSVideoKnowledgeExtractDto = self.validate_request(request.data)
        knowledge_list = CourseSectionKnowledgeService.extract_video_knowledge(dto)
        return make_response({'knowledge_list': knowledge_list})


class QuestionKnowledgeExtractView(base_view.BaseView):
    validator_class = QuestionKnowledgeExtractValidator

    def post(self, request, *args, **kwargs):
        dto: QuestionKnowledgeExtractDto = self.validate_request(request.data)
        is_success, knowledge_list = QuestionKnowledgeService.extract_question_knowledge(dto)
        return make_response({
            'is_success': is_success,
            'knowledge_list': knowledge_list,
        })


class TestAnswerReportGeneratorView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        answer_id = request.data.get('answer_id')
        user_id = request.data.get('user_id')
        if not answer_id and not user_id:
            raise ParameterError()

        response = TestAnswerReportService.gen_answer_report(answer_id, user_id, request.user)
        return make_stream_response(response)
