from app.api.dto import CourseNoteTaskCreateDto
from app.models import CourseNoteTask


class CourseNoteService:

    @classmethod
    def create_note_task(cls, dto: CourseNoteTaskCreateDto) -> CourseNoteTask:
        return CourseNoteTask.objects.create(
            course_id=dto.course_id,
            course_lecture_id=dto.course_lecture_id,
            chapter_id=dto.chapter_id,
            chapter_name=dto.chapter_name,
            chapter_lecture=dto.chapter_lecture,
            document_nos=dto.document_nos,
        )

    @classmethod
    def process_code_block(cls, text: str):
        text_arr = text.split('\n')
        new_text_arr = []
        for row in text_arr:
            leading_spaces_count = len(row) - len(row.lstrip(' '))
            space_str = ' ' * leading_spaces_count
            strip_row = row.strip()
            if strip_row.startswith('$$'):
                row = row.replace('$$', f'$$\n{space_str}')
            if strip_row.endswith('$$'):
                row = row.replace('$$', f'\n{space_str}$$')
            new_text_arr.append(row)
        return '\n'.join(new_text_arr)

    @classmethod
    def get_task_status(cls, task: CourseNoteTask):
        if task.status == 'success':
            qs = task.coursenotetaskchangedetail_set.filter(is_deleted=False, status='success')
            note_list = []
            for i in qs:
                note_list.append({
                    'document_no': i.document_no,
                    'note': cls.process_code_block(i.video_note),
                    'lecture': i.video_lecture,
                })
            return {'status': 'success', 'note_list': note_list}
        elif task.status == 'failed':
            return {'status': 'failed', 'note_list': []}
        else:
            return {'status': 'processing', 'note_list': []}
