# Generated by Django 4.2 on 2024-12-09 06:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0014_message_file_objs_message_message_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='QuestionNewRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_pic_url', models.CharField(max_length=255, verbose_name='题目链接')),
                ('question_content', models.TextField(null=True, verbose_name='题目内容')),
                ('message', models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
            ],
            options={
                'verbose_name': '新题记录',
                'verbose_name_plural': '新题记录',
                'db_table': 'ai_question_new_record',
            },
        ),
    ]
