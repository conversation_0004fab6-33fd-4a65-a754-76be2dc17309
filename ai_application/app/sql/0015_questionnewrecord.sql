--
-- Create model QuestionNewRecord
--
CREATE TABLE `ai_question_new_record`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool         NOT NULL,
    `question_pic_url` varchar(255) NOT NULL,
    `question_content` longtext NULL,
    `message_id`       bigint NULL
);
CREATE INDEX `ai_question_new_record_add_time_e22ca371` ON `ai_question_new_record` (`add_time`);
CREATE INDEX `ai_question_new_record_message_id_b87e2df7` ON `ai_question_new_record` (`message_id`);