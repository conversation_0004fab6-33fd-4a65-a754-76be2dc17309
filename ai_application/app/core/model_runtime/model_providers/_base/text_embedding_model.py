import time
from abc import abstractmethod

from pydantic import ConfigDict

from app.core.model_runtime.entities.provider_entities import ModelPropertyKey, ModelType
from app.core.model_runtime.entities.text_embedding_entities import TextEmbeddingResult
from app.core.model_runtime.model_providers._base.ai_model import AIModel


class TextEmbeddingModel(AIModel):
    """
    Model class for text embedding model.
    """
    model_type: ModelType = ModelType.TEXT_EMBEDDING

    # pydantic configs
    model_config = ConfigDict(protected_namespaces=())

    def invoke(self, model: str, texts: list[str]) -> TextEmbeddingResult:
        self.started_at = time.perf_counter()

        return self._invoke(model, texts)

    @abstractmethod
    def _invoke(self, model: str, texts: list[str]) -> TextEmbeddingResult:
        raise NotImplementedError

    @abstractmethod
    def get_num_tokens(self, model: str, credentials: dict, texts: list[str]) -> int:
        raise NotImplementedError

    def _get_context_size(self, model: str) -> int:
        model_schema = self.get_model_schema(model)

        if model_schema and ModelPropertyKey.CONTEXT_SIZE in model_schema.model_properties:
            return model_schema.model_properties[ModelPropertyKey.CONTEXT_SIZE]

        return 1000

    def _get_max_chunks(self, model: str) -> int:
        model_schema = self.get_model_schema(model)

        if model_schema and ModelPropertyKey.MAX_CHUNKS in model_schema.model_properties:
            return model_schema.model_properties[ModelPropertyKey.MAX_CHUNKS]

        return 1
