import json
import time
from typing import Generator

from app.constants.app import AppMode
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate
from app.services.question_service import QuestionService


class KnowledgeQuestionFeature(BaseFeature):

    def __init__(self):
        knowledge_deep_question = None
        knowledge_deep_no_question = None
        prompt_templates = PromptTemplate.objects.filter(
            app_no__in=['knowledge_deep_question', 'knowledge_deep_no_question'])
        for p in prompt_templates:
            if p.app_no == 'knowledge_deep_question':
                knowledge_deep_question = p
            elif p.app_no == 'knowledge_deep_no_question':
                knowledge_deep_no_question = p

        if not knowledge_deep_question or not knowledge_deep_no_question:
            raise Exception('prompt template not found')

        self.knowledge_deep_question = knowledge_deep_question
        self.knowledge_deep_no_question = knowledge_deep_no_question

    def analysis_question(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            question: dict
    ) -> Generator:
        user_inputs = application_generate_entity.inputs.copy()
        user_inputs['problem'] = question["question"]
        user_inputs['answer'] = question["analysis"]

        prompt_template = self.knowledge_deep_question

        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=user_inputs.get('deep_question_prompt')
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        # 调用模型
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=True
        )

    def analysis_question_by_knowledge(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            knowledge: str
    ) -> Generator:
        user_inputs = application_generate_entity.inputs.copy()
        user_inputs['knowledge'] = knowledge

        prompt_template = self.knowledge_deep_no_question

        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=user_inputs.get('deep_no_question_prompt')
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        # 调用模型
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=True
        )

    def query_questions_by_knowledge(
            self,
            knowledge_name: str,
            tracing_log: MessageTracingLog | None = None
    ):
        started_at = time.perf_counter()
        questions = QuestionService.get_questions_by_knowledge(knowledge_name)
        latency = time.perf_counter() - started_at

        if tracing_log:
            tracing_query = knowledge_name
            answer = json.dumps(questions, ensure_ascii=False) if questions else '[]'
            tracing_log.add_tracing(
                tracing_type='knowledge_question_db',
                query=tracing_query,
                answer=answer,
                latency=latency,
            )

        # 检查返回的题目数量
        return questions[:2]
