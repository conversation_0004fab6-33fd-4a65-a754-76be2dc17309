from django.db import models

from ._base import BaseModel


class SubjectDomain(BaseModel):
    subject_code = models.Char<PERSON>ield('一级科目code', max_length=100, blank=True)
    subject_name = models.CharField('一级科目名称', max_length=100, blank=True)
    main_subject_code = models.CharField('主科目code', max_length=100, blank=True)
    main_subject_name = models.CharField('主科目名称', max_length=100, blank=True)
    is_knowledge_search_enable = models.BooleanField('是否知识解析使用', default=False)
    sort = models.IntegerField('排序值', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '学科表'
        db_table = 'ai_data_subject_domain'


class KnowledgeLibrary(BaseModel):
    class Nature(models.TextChoices):
        common = ('common', '通用名词')
        major = ('major', '专有名词')

    subject_domain = models.ForeignKey(SubjectDomain, on_delete=models.CASCADE, db_constraint=False, null=True)
    subject = models.CharField('考研科目', max_length=100, blank=True)
    main_subject = models.CharField('主科目', max_length=100, blank=True)
    nature = models.CharField('名词特征', max_length=32, choices=Nature.choices, default=Nature.major)
    name = models.CharField('名词', max_length=100, blank=True)
    desc = models.TextField('名词描述', null=True)
    content = models.TextField('知识点详解', null=True)
    old_knowledge_id = models.CharField('旧知识点库id', max_length=32, blank=True)
    first_letter = models.CharField('首字母', max_length=10, blank=True)
    sort = models.IntegerField('排序值', default=0)
    # NOT_START, ING, SUCCESS, FAIL
    status = models.CharField('状态', max_length=100, default='NOT_START')

    class Meta:
        verbose_name = verbose_name_plural = '知识点库'
        db_table = 'ai_data_knowledge_library'


class CourseSectionKnowledge(BaseModel):
    course_section_id = models.CharField('课节ID', max_length=100, blank=True)
    # 数学一，数学二，英语一
    subject = models.CharField('考研科目', max_length=100, blank=True)
    main_subject = models.CharField('主科目', max_length=100, blank=True)
    subtitles = models.TextField('字幕内容', null=True)
    subtitles_abstract = models.TextField('字幕摘要', null=True)
    is_extract_knowledge = models.BooleanField('是否已提取知识点', default=False)
    knowledge_list = models.JSONField('知识点列表', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '课节知识点'
        db_table = 'ai_data_course_section_knowledge'


class QuestionKnowledge(BaseModel):
    question_id = models.CharField('题目ID', max_length=100, blank=True)
    question_content = models.TextField('题目内容', null=True)
    subject = models.CharField('考研科目', max_length=100, blank=True)
    main_subject = models.CharField('主科目', max_length=100, blank=True)
    is_extract_knowledge = models.BooleanField('是否已提取知识点', default=False)
    knowledge_list = models.JSONField('知识点列表', null=True)
    subject_id = models.CharField('考研科目id', max_length=100, blank=True)
    question_uuid = models.CharField('题目uuid', max_length=100, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '题目知识点'
        db_table = 'ai_data_question_knowledge'


class QuestionKnowledgeMap(BaseModel):
    question_id = models.CharField('题目ID', max_length=100, blank=True)
    knowledge = models.ForeignKey(KnowledgeLibrary, on_delete=models.CASCADE, db_constraint=False)

    class Meta:
        verbose_name = verbose_name_plural = '题目知识点关联'
        db_table = 'ai_data_question_knowledge_map'


class EnglishWordLibrary(BaseModel):
    class Freq(models.TextChoices):
        low = ('low', '偶考')
        middle = ('middle', '常考')
        high = ('high', '必考')

    word = models.CharField('单词', max_length=100, blank=True, db_index=True)
    word_freq = models.CharField('词频', max_length=32, blank=True)
    part = models.CharField('part', max_length=100, blank=True)
    title1 = models.CharField('一级标题', max_length=100, blank=True)
    title2 = models.CharField('二级标题', max_length=100, blank=True)
    title3 = models.CharField('三级标题', max_length=100, blank=True)
    american_sound_mark_url = models.CharField('美式读音', max_length=255, null=True, blank=True)
    english_sound_mark_url = models.CharField('英式读音', max_length=255, null=True, blank=True)
    american_phonetic_symbols = models.CharField('美式英标', max_length=255, null=True, blank=True)
    english_phonetic_symbols = models.CharField('英式英标', max_length=255, null=True, blank=True)
    real_example_sentence = models.JSONField('真题例句', null=True)
    synonym = models.JSONField('同义词', null=True)
    antonym = models.JSONField('反义词', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '英语单词库'
        db_table = 'ai_data_english_word_library'


class StudentLearnStat(BaseModel):
    user_id = models.CharField('用户ID', max_length=100, db_index=True)
    rec_days = models.IntegerField('最近几天', default=7)
    task_id = models.CharField('任务id', max_length=36, unique=True, null=None)
    query = models.TextField('请求内容', null=True)
    analysis = models.TextField('解析内容', null=True)
    # NOT_START ING SUCCESS FAIL
    status = models.CharField('解析状态', max_length=100, blank=True)
    fail_reason = models.TextField('失败原因', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '学情分析记录'
        db_table = 'ai_student_learn_stat'

class SuperviseLearnStat(BaseModel):
    user_id = models.CharField('用户ID', max_length=100, db_index=True)
    course_id = models.CharField('课程ID', max_length=100, blank=True, null=True)
    subject_id = models.CharField('科目ID', max_length=100, blank=True, null=True)
    start_date = models.DateField('开始日期', blank=True, null=True)
    end_date = models.DateField('结束日期', blank=True, null=True)
    task_id = models.CharField('任务id', max_length=36, unique=True, null=True, default=None, blank=True)
    # NOT_START ING SUCCESS FAIL
    status = models.CharField('解析状态', max_length=100, blank=True, null=True)
    fail_reason = models.TextField('失败原因', null=True)

    query = models.TextField('请求内容', null=True)
    analysis = models.TextField('报告内容', null=True)
    

    class Meta:
        verbose_name = verbose_name_plural = '教务督学记录'
        db_table = 'ai_student_supervise_learn_stat'

class SuperviseLearnStageStat(BaseModel):
    user_id = models.CharField('用户ID', max_length=100, db_index=True)
    course_id = models.CharField('课程ID', max_length=100, blank=True, null=True)
    task_id = models.CharField('任务id', max_length=36, unique=True, null=True, default=None, blank=True)
    # NOT_START ING SUCCESS FAIL
    status = models.CharField('解析状态', max_length=100, blank=True, null=True)
    fail_reason = models.TextField('失败原因', null=True)
    
    query = models.TextField('请求内容', null=True)
    analysis = models.TextField('报告内容', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '教务督学学习阶段记录'
        db_table = 'ai_student_supervise_learn_stage_stat'    

class SuperViseInitStudentStatus(BaseModel):

    # 基础信息
    user_id = models.CharField('用户ID', max_length=100, db_index=True)
    date = models.DateField('填写日期', auto_now_add=True)  # 改用 DateField
    exam_date = models.DateField('考试日期')  # 改用 DateField
    graduation_major = models.CharField('毕业专业', max_length=100, blank=True, null=True)
    graduation_school = models.CharField('毕业院校', max_length=100, blank=True, null=True)
    graduation_major_code = models.CharField('毕业专业代码', max_length=100, blank=True, null=True)
    graduation_school_code = models.CharField('毕业院校代码', max_length=100, blank=True, null=True)
    
    # 目标信息
    target = models.JSONField('目标院校基本信息', default=dict, null=True, blank=True)
    
    # 学习状态
    education_level = models.CharField('学历层次', max_length=100, blank=True, null=True)
    study_status = models.CharField('在读状态', max_length=100, blank=True, null=True)
    graduation_years = models.CharField('毕业年限', max_length=100, blank=True, null=True)
    study_stage = models.CharField('学习阶段', max_length=100, blank=True, null=True)
    
    # 学业表现
    academic_performance = models.CharField('在校成绩评价', max_length=255, blank=True, null=True)
    english_level = models.CharField('英语能力等级', max_length=100, blank=True, null=True)
    math_subjects = models.JSONField('学过的数学科目', default=list, null=True, blank=True)
    math_mastery = models.CharField('数学掌握情况', max_length=255, blank=True, null=True)
    political_subjects = models.JSONField('学过的政治科目', default=list, null=True, blank=True)
    political_mastery = models.CharField('政治掌握情况', max_length=255, blank=True, null=True)

    task_id = models.CharField('任务ID', max_length=36, unique=True, null=True, default=None, blank=True)
    fail_reason = models.TextField('失败原因', null=True, blank=True)

    query = models.TextField('请求内容', null=True, blank=True)
    analysis = models.TextField('报告内容', null=True, blank=True)
    status = models.CharField('解析状态', max_length=100, blank=True, null=True)

    class Meta:
        verbose_name = verbose_name_plural = '教务督学用户初始化状态'
        db_table = 'ai_student_supervise_init_status'
