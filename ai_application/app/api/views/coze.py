from app.api.dto import CozeConversationAdd, ChatMessageDto, CozeChatMessageDto
from app.api.validators import CozeConversationAddValidator, CozeChatMessageValidator
from app.errors import ParameterError
from app.services.coze_app_generate_service import CozeGenerateService
from django_ext import base_view
from django_ext.response import make_response, make_stream_response


class CozeConversationAddView(base_view.BaseView):
    validator_class = CozeConversationAddValidator

    def post(self, request, *args, **kwargs):
        dto: CozeConversationAdd = self.validate_request(request.data)

        conversation = CozeGenerateService.add_conversation(dto, request.user)
        return make_response({'conversation_id': conversation.conversation_no})


class CozeChatMessageView(base_view.BaseView):
    validator_class = CozeChatMessageValidator

    def post(self, request, *args, **kwargs):
        dto: CozeChatMessageDto = self.validate_request(request.data)

        if not dto.query and not dto.image:
            raise ParameterError(detail_err='参数错误')

        response = CozeGenerateService.generate(dto, request.user)
        if isinstance(response, dict):
            return make_response(response)
        else:
            return make_stream_response(response)


class CozeChatCancelView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        CozeGenerateService.chat_cancel(kwargs['message_id'], request.user)
        return make_response()
