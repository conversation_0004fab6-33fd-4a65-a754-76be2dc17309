--
-- Add field course_section_id to gaoshuuserquestionrecord
--
ALTER TABLE `ai_gaoshu_screenshot_user_record` ADD COLUMN `course_section_id` varchar(255) DEFAULT '' NOT NULL;
--
-- Add field message to gaoshuuserquestionrecord
--
ALTER TABLE `ai_gaoshu_screenshot_user_record` ADD COLUMN `message_id` bigint NOT NULL;
--
-- Alter field knowledge_list on gaoshuuserquestionrecord
--
ALTER TABLE `ai_gaoshu_screenshot_user_record` MODIFY `knowledge_list` json NULL;
CREATE INDEX `ai_gaoshu_screenshot_user_record_message_id_1ff8787d` ON `ai_gaoshu_screenshot_user_record` (`message_id`);