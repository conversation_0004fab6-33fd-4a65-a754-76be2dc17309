from rest_framework import serializers

from app.models import App, KnowledgeLibrary, EnglishWordTestQuestion, EnWordRecitePlanRecord, EnglishWordLibrary


class AppListSerializer(serializers.ModelSerializer):
    app_id = serializers.CharField(source='app_no')

    class Meta:
        model = App
        fields = ('app_id', 'name', 'add_time')


class AppInfoSerializer(serializers.ModelSerializer):
    app_id = serializers.CharField(source='app_no')
    support_params = serializers.SerializerMethodField()

    class Meta:
        model = App
        fields = ('app_id', 'name', 'add_time', 'support_params')

    def get_support_params(self, app: App) -> list:
        app_model_config = app.app_model_config
        return app_model_config.support_params


class EnglishWordTestQuestionSerializer(serializers.ModelSerializer):

    class Meta:
        model = EnglishWordTestQuestion
        fields = ('id', 'word', 'question_type', 'question', 'options', 'answer', 'analysis', 'level')


class KnowledgeListSerializer(serializers.ModelSerializer):

    class Meta:
        model = KnowledgeLibrary
        fields = ('id', 'name', 'first_letter')


class EnPlanRecordSerializer(serializers.ModelSerializer):
    plan_record_id = serializers.IntegerField(source='id')
    plan_content = serializers.SerializerMethodField()

    class Meta:
        model = EnWordRecitePlanRecord
        fields = ('plan_record_id', 'gen_plan_content', 'plan_content')

    def get_plan_content(self, obj: EnWordRecitePlanRecord):
        plan_content = obj.plan_content
        if plan_content and 'new_study_plan' in plan_content:
            plan_content['study_plan'] = plan_content['new_study_plan']
        return plan_content


class EnWordSerializer(serializers.ModelSerializer):
    real_example_sentence = serializers.SerializerMethodField()

    class Meta:
        model = EnglishWordLibrary
        fields = ('id', 'word', 'synonym', 'antonym',
                  'american_sound_mark_url', 'english_sound_mark_url',
                  'american_phonetic_symbols', 'english_phonetic_symbols',
                  'real_example_sentence')

    def get_real_example_sentence(self, obj: EnglishWordLibrary):
        if not obj.real_example_sentence:
            return []

        return [{
            'content': i['content'],
            'explain': i['explain'],
        } for i in obj.real_example_sentence]

