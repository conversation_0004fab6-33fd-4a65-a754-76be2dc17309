--
-- Create model MessageTask
--
CREATE TABLE `ai_message_task`
(
    `id`              bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`        datetime(6) NOT NULL,
    `modified_time`   datetime(6) NOT NULL,
    `is_deleted`      bool        NOT NULL,
    `process_status`  varchar(16) NOT NULL,
    `fail_reason`     longtext NULL,
    `retry_times`     integer     NOT NULL,
    `last_retry_time` datetime(6) NULL,
    `next_retry_time` datetime(6) NULL,
    `message_id`      bigint      NOT NULL
);
CREATE INDEX `ai_message_task_add_time_faf8b0d2` ON `ai_message_task` (`add_time`);
CREATE INDEX `ai_message_task_process_status_242594e3` ON `ai_message_task` (`process_status`);
CREATE INDEX `ai_message_task_next_retry_time_65063845` ON `ai_message_task` (`next_retry_time`);
CREATE INDEX `ai_message_task_message_id_f40374c0` ON `ai_message_task` (`message_id`);