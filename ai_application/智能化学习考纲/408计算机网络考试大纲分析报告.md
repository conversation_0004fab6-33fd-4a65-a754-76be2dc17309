### 考研408计算机网络考试大纲分析报告


#### 一、各科目分值分布及重要程度分析  
考研408计算机学科专业基础综合试卷总分150分，四门科目分值分布通常为：**数据结构45分、计算机组成原理45分、操作系统35分、计算机网络25分**。计算机网络分值占比约17%，虽低于其他三科，但考点集中、逻辑性强，且部分核心章节（如传输层TCP、网络层IP路由）是综合题常考内容，对总分贡献关键。需重点掌握核心考点，兼顾基础概念。


#### 二、各章节考点分析（结合历年真题）  


### **第一章：计算机网络概述**  
| 考点内容                                                                 | 标签   | 考试题型 | 难度等级 |  
|--------------------------------------------------------------------------|--------|----------|----------|  
| （一）计算机网络基本概念：定义、组成与功能                                 | 基础   | 选择     | 易       |  
| （一）计算机网络基本概念：分类（LAN/WAN/MAN等）                           | 基础   | 选择     | 易       |  
| （一）计算机网络基本概念：主要性能指标（带宽、速率、时延、吞吐量、丢包率） | 重要   | 选择/计算 | 中       |  
| （二）计算机网络体系结构：分层结构（分层意义、各层功能）                   | 基础   | 选择     | 易       |  
| （二）计算机网络体系结构：协议、接口、服务的概念                           | 基础   | 选择     | 易       |  
| （二）计算机网络体系结构：ISO/OSI模型与TCP/IP模型对比                     | 重要   | 选择     | 中       |  

**分析**：本章为网络基础，TCP/IP模型各层功能（如网络层路由、传输层可靠传输）是后续章节学习的框架，性能指标（如时延计算）偶尔考选择或填空，难度较低。


### **第二章：物理层**  
| 考点内容                                                                 | 标签   | 考试题型 | 难度等级 |  
|--------------------------------------------------------------------------|--------|----------|----------|  
| （一）通信基础：基本概念（信道、信号、码元、波特、速率、信源/信宿）       | 基础   | 选择     | 易       |  
| （一）通信基础：奈奎斯特定理（无噪声信道最大速率）与香农定理（有噪声信道容量） | 重要   | 选择/计算 | 中       |  
| （一）通信基础：编码（曼彻斯特编码）与调制（ASK/FSK/PSK）                 | 基础   | 选择     | 易       |  
| （一）通信基础：电路交换、报文交换与分组交换的特点对比                     | 重要   | 选择     | 中       |  
| （一）通信基础：数据报与虚电路的概念                                     | 了解   | 选择     | 易       |  
| （二）传输介质：双绞线、同轴电缆、光纤（传输特性对比）                     | 了解   | 选择     | 易       |  
| （三）物理层设备：中继器（信号放大）、集线器（多端口中继器，半双工）       | 了解   | 选择     | 易       |  

**分析**：奈奎斯特/香农定理是核心计算考点（需记住公式），分组交换与电路交换的优缺点对比常考，设备功能简单（中继器/集线器工作在物理层，不能隔离冲突域），了解即可。


### **第三章：数据链路层**  
| 考点内容                                                                 | 标签   | 考试题型   | 难度等级 |  
|--------------------------------------------------------------------------|--------|------------|----------|  
| （一）数据链路层功能（链路管理、组帧、差错控制、流量控制、MAC）           | 基础   | 选择       | 易       |  
| （二）组帧（字符计数法、带填充的首尾定界符法）                             | 基础   | 选择       | 易       |  
| （三）差错控制：检错编码（CRC原理）                                       | 重要   | 选择/计算 | 中       |  
| （四）流量控制与可靠传输机制：滑动窗口机制原理                             | 核心   | 综合题     | 难       |  
| （四）流量控制与可靠传输机制：停止-等待协议（优缺点、信道利用率计算）       | 重要   | 选择/计算 | 中       |  
| （四）流量控制与可靠传输机制：GBN协议（发送窗口>1，累积确认，超时重传）    | 核心   | 综合题     | 难       |  
| （四）流量控制与可靠传输机制：SR协议（发送/接收窗口>1，选择重传）          | 核心   | 综合题     | 难       |  
| （五）介质访问控制：信道划分（FDM/TDM/WDM/CDM概念）                       | 基础   | 选择       | 易       |  
| （五）介质访问控制：随机访问（CSMA/CD协议原理、冲突检测与退避机制）         | 核心   | 选择/综合 | 中       |  
| （五）介质访问控制：随机访问（CSMA/CA协议，无线局域网）                     | 重要   | 选择       | 中       |  
| （六）局域网：以太网（IEEE 802.3帧格式、MAC地址）                         | 重要   | 选择       | 中       |  
| （六）局域网：VLAN基本原理（隔离广播域）                                   | 基础   | 选择       | 易       |  
| （七）广域网：PPP协议（功能、帧格式）                                     | 了解   | 选择       | 易       |  
| （八）数据链路层设备：以太网交换机（工作原理、MAC地址表、转发方式）         | 重要   | 选择       | 中       |  

**分析**：可靠传输协议（GBN/SR）是综合题高频考点（需画图分析帧传输过程），CSMA/CD（以太网核心）和交换机工作原理常考，CRC检错计算偶考，难度中等偏上。


### **第四章：网络层**  
| 考点内容                                                                 | 标签   | 考试题型   | 难度等级 |  
|--------------------------------------------------------------------------|--------|------------|----------|  
| （一）网络层功能：路由与转发（路由表与转发表区别）                         | 基础   | 选择       | 易       |  
| （二）路由算法：距离-向量路由算法（RIP协议原理、计数到无穷问题）             | 核心   | 选择/综合 | 中       |  
| （二）路由算法：链路状态路由算法（OSPF协议原理、洪泛法）                   | 核心   | 选择       | 中       |  
| （三）IPv4：IPv4分组格式（版本、首部长度、总长度、TTL、协议字段）           | 重要   | 选择       | 中       |  
| （三）IPv4：IPv4地址与NAT（私有地址转换原理）                             | 重要   | 选择       | 中       |  
| （三）IPv4：子网划分与CIDR（子网掩码计算、路由聚集）                       | 核心   | 选择/计算 | 难       |  
| （三）IPv4：ARP协议（地址解析过程）、DHCP协议（动态分配IP）                 | 重要   | 选择       | 中       |  
| （三）IPv4：ICMP协议（ping/traceroute工具原理）                           | 重要   | 选择       | 中       |  
| （四）IPv6：主要特点（128位地址、无校验和、简化首部）                     | 基础   | 选择       | 易       |  
| （五）路由协议：BGP协议（域间路由，路径向量算法）                         | 重要   | 选择       | 中       |  
| （八）网络层设备：路由器组成（路由选择、分组转发流程）                     | 核心   | 综合题     | 难       |  

**分析**：IPv4子网划分与CIDR计算每年必考（需熟练掌握子网掩码、网络地址、广播地址计算），RIP/OSPF路由协议原理、路由器转发过程是核心，ICMP/ARP/DHCP协议功能常考，难度高。


### **第五章：传输层**  
| 考点内容                                                                 | 标签   | 考试题型   | 难度等级 |  
|--------------------------------------------------------------------------|--------|------------|----------|  
| （一）传输层服务：端口号概念（熟知端口、登记端口、动态端口）               | 基础   | 选择       | 易       |  
| （二）UDP：UDP数据报格式（首部字段）、校验（伪首部）                       | 重要   | 选择       | 中       |  
| （三）TCP：TCP段格式（序号、确认号、标志位SYN/ACK/FIN/RST、窗口大小）     | 核心   | 选择/综合 | 难       |  
| （三）TCP：连接管理（三次握手过程及原因、四次挥手过程）                   | 核心   | 选择/综合 | 难       |  
| （三）TCP：可靠传输机制（超时重传、快速重传、确认机制）                   | 核心   | 综合题     | 难       |  
| （三）TCP：流量控制（滑动窗口协议，接收窗口rwnd）                         | 核心   | 综合题     | 难       |  
| （三）TCP：拥塞控制（慢开始、拥塞避免、快重传、快恢复算法流程）             | 核心   | 综合题     | 难       |  

**分析**：传输层是计算机网络分值最高的章节，TCP协议（连接管理、拥塞控制、可靠传输）是综合题必考内容（需详细掌握三次握手/四次挥手的每一步、拥塞控制四个阶段的阈值变化），难度极高。


### **第六章：应用层**  
| 考点内容                                                                 | 标签   | 考试题型 | 难度等级 |  
|--------------------------------------------------------------------------|--------|----------|----------|  
| （一）网络应用模型：C/S模型与P2P模型对比                                 | 基础   | 选择     | 易       |  
| （二）DNS：层次域名空间（根域、顶级域、权威域名服务器）                   | 重要   | 选择     | 中       |  
| （二）DNS：域名解析过程（递归查询与迭代查询）                             | 重要   | 选择     | 中       |  
| （三）FTP：控制连接（21端口）与数据连接（20端口，主动/被动模式）           | 基础   | 选择     | 易       |  
| （四）电子邮件：SMTP协议（发送邮件，25端口）与POP3协议（接收邮件，110端口） | 基础   | 选择     | 易       |  
| （五）WWW：HTTP协议（请求方法GET/POST、状态码200/404/500）                | 核心   | 选择/简答 | 中       |  
| （五）WWW：HTTP无状态特性与Cookie机制                                     | 重要   | 选择     | 中       |  

**分析**：HTTP协议是应用层核心（请求方法、状态码、无状态特性常考），DNS解析过程重要，其他协议（FTP/Email）了解基本原理即可，难度中等。


#### 三、总结  
计算机网络核心章节为**传输层（TCP）、网络层（IPv4路由与地址）、数据链路层（可靠传输与MAC协议）**，占分约80%；应用层（HTTP/DNS）和概述为基础，物理层了解即可。复习需重点突破TCP拥塞控制、IPv4地址计算、GBN/SR协议等综合题考点，同时掌握选择高频考点（如CSMA/CD、路由协议特点）。