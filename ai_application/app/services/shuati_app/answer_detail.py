from app.errors import ParameterError
from app.models import STUserPaperAnswer, STCoreCourseList
from app.services.shuati_app.constants import QuestionType
from app.services.shuati_app.paper_detail import get_paper_questions
from django_ext.utils.date_utils import utc2str


def get_ai_practice_answer_detail(answer_id: int):
    answer: STUserPaperAnswer = STUserPaperAnswer.objects.filter(id=answer_id).first()
    if not answer:
        raise ParameterError(detail='答题记录不存在')

    paper = answer.paper
    paper_answer_questions = answer.stuserpaperquestionanswer_set.filter(
        is_deleted=False).values('question_id', 'answer_status', 'choice_answer', 'score_rate', 'subjective_answer')
    paper_answer_questions_map = {i['question_id']: i for i in paper_answer_questions}

    question_list = get_paper_questions(paper)
    right_num = 0
    for q in question_list:
        answer_info = paper_answer_questions_map.get(q['question_id'])
        if not answer_info:
            continue
        q['answer_status'] = answer_info.get('answer_status', '')
        q['user_answer'] = {
            'choice_answer': answer_info.get('choice_answer'),
            'subjective_answer': answer_info.get('subjective_answer'),
        }
        q['subjective_answer_status'] = ''
        is_right = answer_info['score_rate'] > 80
        if is_right:
            right_num += 1
        if q['question_type'] == QuestionType.SUBJECTIVE and answer_info['score_rate'] >= 0:
            q['subjective_answer_status'] = 'right' if is_right else 'wrong'

    core_course_map = {i['core_course_code']: i for i in STCoreCourseList}
    core_course_info = core_course_map.get(paper.core_course_code)

    return {
        'status': 'success',
        'subject_name': core_course_info['subject_name'],
        'core_course_name': core_course_info['core_course_name'],
        'question_num': len(question_list),
        'right_num': right_num,
        'answer_time': utc2str(answer.add_time),
        'is_finished': answer.is_finished,
        'finished_time': utc2str(answer.finished_time) if answer.finished_time else '',
        'question_list': question_list,
    }
