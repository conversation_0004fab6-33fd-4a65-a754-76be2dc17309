--
-- <PERSON>reate model CourseSectionKnowledge
--
CREATE TABLE `ai_data_course_section_knowledge`
(
    `id`                 bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`           datetime(6) NOT NULL,
    `modified_time`      datetime(6) NOT NULL,
    `is_deleted`         bool         NOT NULL,
    `course_section_id`  varchar(100) NOT NULL,
    `subject`            varchar(100) NOT NULL,
    `main_subject`       varchar(100) NOT NULL,
    `subtitles`          longtext NULL,
    `subtitles_abstract` longtext NULL,
    `knowledge_list`     json NULL
);
--
-- Create model QuestionKnowledge
--
CREATE TABLE `ai_data_question_knowledge`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool         NOT NULL,
    `question_id`      varchar(100) NOT NULL,
    `question_content` longtext NULL,
    `subject`          varchar(100) NOT NULL,
    `main_subject`     varchar(100) NOT NULL,
    `knowledge_list`   json NULL
);
--
-- Alter field message_type on message
--
-- (no-op)
CREATE INDEX `ai_data_course_section_knowledge_add_time_cdac4c0b` ON `ai_data_course_section_knowledge` (`add_time`);
CREATE INDEX `ai_data_question_knowledge_add_time_da2fb458` ON `ai_data_question_knowledge` (`add_time`);