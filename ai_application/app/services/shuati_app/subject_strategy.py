from django.core.cache import cache

from app.models import STSubjectStageStrategy
from app.services.shuati_app.constants import LearningStage
from app.services.shuati_app.dto import QuestionSelectStrategy, LearningStageRequired, ModiUpRequired


def get_subject_question_strategy(
        subject_id: str,
        core_course_code: str
) -> dict[str, dict[str, QuestionSelectStrategy]]:
    cache_key = f'st_question_strategy_{core_course_code}'
    stage_strategy_map = cache.get(cache_key)
    # stage_strategy_map = None  # TODO DEBUG
    if stage_strategy_map is None:
        stage_strategy_map, _ = _get_strategy_by_db(subject_id, core_course_code)

    res_map = {}
    for stage, strategy in stage_strategy_map.items():
        try:
            res_map[stage] = {
                'normal': QuestionSelectStrategy(**strategy['normal']) if strategy['normal'] else None,
                'strengthen': QuestionSelectStrategy(**strategy['strengthen']) if strategy['strengthen'] else None,
            }
        except:
            continue
    return res_map


def get_subject_change_required(
        subject_id: str,
        core_course_code: str
) -> dict[str, ModiUpRequired | LearningStageRequired]:
    cache_key = f'st_change_required_{core_course_code}'
    stage_required_map = cache.get(cache_key)
    # stage_required_map = None  # TODO DEBUG
    if stage_required_map is None:
        _, stage_required_map = _get_strategy_by_db(subject_id, core_course_code)

    res_map = {}
    for stage, required in stage_required_map.items():
        try:
            if stage == LearningStage.MODI.value:
                res_map[stage] = ModiUpRequired(**required)
            else:
                res_map[stage] = LearningStageRequired(**required)
        except:
            continue
    return res_map


def _get_strategy_by_db(subject_id: str, core_course_code: str):
    strategy_cache_key = f'st_question_strategy_{core_course_code}'
    required_cache_key = f'st_change_required_{core_course_code}'

    qs = STSubjectStageStrategy.objects.filter(
        subject_id=subject_id,
        core_course_code=core_course_code,
    )
    stage_strategy_map = {}
    stage_required_map = {}
    for i in qs:
        stage_strategy_map[i.learning_stage] = {
            'normal': i.stage_question_strategy,
            'strengthen': i.stage_strengthen_question_strategy,
        }
        stage_required_map[i.learning_stage] = i.stage_change_required
    cache.set(strategy_cache_key, stage_strategy_map, timeout=600)
    cache.set(required_cache_key, stage_required_map, timeout=600)

    return stage_strategy_map, stage_required_map
