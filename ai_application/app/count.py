from app.models.exam_analyis import (
    ExamAnalysisKnowledgePoint, 
    ExamAnalysisExamQuestion,
    ExamAnalysisKnowledgePointAnalysis,
    ExamAnalysisKnowledgePointWithStats
)

def main(subject_code=3):
    """统计知识点数据并保存到数据库"""
    # 从数据库获取指定学科代码的知识点结构
    kp_structure = {}
    for kp in ExamAnalysisKnowledgePoint.objects.filter(subject_code_id=subject_code):
        if kp.subject not in kp_structure:
            kp_structure[kp.subject] = []
        kp_structure[kp.subject].append(kp.point_name)

    # 从数据库获取指定学科代码的十年统计数据
    yearly_data = {}
    for analysis in ExamAnalysisKnowledgePointAnalysis.objects.filter(subject_code_id=subject_code):
        if analysis.year not in yearly_data:
            yearly_data[analysis.year] = {}
        if analysis.subject not in yearly_data[analysis.year]:
            yearly_data[analysis.year][analysis.subject] = {}
        yearly_data[analysis.year][analysis.subject][analysis.point_name] = {
            "考查题目数": analysis.question_count,
            "题号难度": analysis.question_difficulty
        }

    # 初始化统计结果
    result = {}
    for subject, kp_list in kp_structure.items():
        result[subject] = {}
        for kp in kp_list:
                result[subject][kp] = {
                    "考查次数": 0,
                    "平均难度": 0.0,
                    "客观题数量": 0,
                    "客观题平均难度": 0.0,
                    "主观题数量": 0,
                    "主观题平均难度": 0.0
                }

    # 统计十年数据
    for year, subjects in yearly_data.items():
        for subject, kps in subjects.items():
            for kp, data in kps.items():
                if subject in result and kp in result[subject]:
                    # 累加考查次数
                    result[subject][kp]["考查次数"] += data["考查题目数"]
                    
                    # 计算各类平均难度
                    if data["题号难度"]:
                        # 分离客观题和主观题
                        choice_diffs = []
                        comp_diffs = []
                        for q_num, diff in data["题号难度"].items():
                            q_num = int(q_num)
                            # 查询题目类型
                            question = ExamAnalysisExamQuestion.objects.filter(
                                subject_code_id=subject_code,
                                year=year,
                                question_number=q_num
                            ).first()
                            if question and question.question_type == '客观题':
                                choice_diffs.append(diff)
                            else:
                                comp_diffs.append(diff)
                        
                        # 计算各类题目数量
                        choice_count = len(choice_diffs)
                        comp_count = len(comp_diffs)
                        
                        # 更新题目数量统计
                        result[subject][kp]["客观题数量"] += choice_count
                        result[subject][kp]["主观题数量"] += comp_count
                        
                        # 计算总平均难度
                        difficulties = list(data["题号难度"].values())
                        avg_diff = sum(difficulties) / len(difficulties)
                        
                        # 计算客观题平均难度
                        choice_avg = sum(choice_diffs) / choice_count if choice_count else 0
                        
                        # 计算主观题平均难度
                        comp_avg = sum(comp_diffs) / comp_count if comp_count else 0
                        
                        # 加权平均计算
                        total_choice = result[subject][kp]["客观题数量"]
                        total_comp = result[subject][kp]["主观题数量"]
                        
                        # 总平均难度
                        prev_avg = result[subject][kp]["平均难度"]
                        new_avg = (prev_avg * (result[subject][kp]["考查次数"] - data["考查题目数"]) + avg_diff * data["考查题目数"])
                        new_avg /= result[subject][kp]["考查次数"]
                        result[subject][kp]["平均难度"] = round(new_avg, 2)
                        
                        # 客观题平均难度
                        if total_choice > 0:
                            prev_choice = result[subject][kp]["客观题平均难度"]
                            new_choice = (prev_choice * (total_choice - choice_count) + choice_avg * choice_count)
                            new_choice /= total_choice
                            result[subject][kp]["客观题平均难度"] = round(new_choice, 2)
                        
                        # 主观题平均难度
                        if total_comp > 0:
                            prev_comp = result[subject][kp]["主观题平均难度"]
                            new_comp = (prev_comp * (total_comp - comp_count) + comp_avg * comp_count)
                            new_comp /= total_comp
                            result[subject][kp]["主观题平均难度"] = round(new_comp, 2)

    # 保存结果到数据库
    for subject, kp_data in result.items():
        for kp_name, stats in kp_data.items():
            ExamAnalysisKnowledgePointWithStats.objects.update_or_create(
                subject=subject,
                point_name=kp_name,
                defaults={
                    'exam_count': stats["考查次数"],
                    'avg_difficulty': stats["平均难度"],
                    'choice_count': stats["客观题数量"],
                    'choice_avg_difficulty': stats["客观题平均难度"],
                    'comprehensive_count': stats["主观题数量"],
                    'comprehensive_avg_difficulty': stats["主观题平均难度"],
                    'subject_code_id': subject_code
                }
            )

    print("统计结果已保存到数据库")
main()
if __name__ == "__main__":
    main()
