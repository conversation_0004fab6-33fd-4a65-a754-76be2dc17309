# 阿纪
from openai import OpenAI
from app.models import PromptTemplate

def get_chat_response(question):
    template = PromptTemplate.objects.filter(app_no='math_problem_solving').first()
    prompt =template.prompt_content
    client = OpenAI(
        api_key="9595878c-3684-44af-b775-7f8fa8847ea6",
        base_url="https://ark.cn-beijing.volces.com/api/v3",
    )

    # Non-streaming:
    stream = client.chat.completions.create(
        model="doubao-1-5-pro-256k-250115",
        messages=[
            {"role": "system", "content": prompt},
            {"role": "user", "content": question},
        ],
        stream=True
    )
    response_content = ""
    for chunk in stream:
        if not chunk.choices:
            continue
        response_content += chunk.choices[0].delta.content or ""

    return response_content
