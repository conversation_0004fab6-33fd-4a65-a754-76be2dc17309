import logging

from django.conf import settings
from django.db.models import OuterRef, Exists
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate

from app.core.langchain_openai_thinking import ChatOpenAIThinking
from app.errors import AppSystemError
from app.models import STQuestion, STKnowledge, PromptTemplate, STUserPaperQuestionAnswer
from app.services.shuati_app.dto import KnowledgeMasteryLevel, PaperDistribution
from app.services.shuati_app.utils import safe_parse_json_response

logger = logging.getLogger(__name__)


DS_KNOWLEDGE_LIST = [
    "时间复杂度", "空间复杂度", "线性表", "顺序表", "单链表", "循环链表", "双向链表",
    "栈", "顺序栈", "链栈", "递归", "队列", "顺序队", "链队", "串", "模式匹配",
    "BF算法", "KMP算法", "next表", "数组", "特殊矩阵压缩", "树的概念", "二叉树的性质",
    "二叉树的存储结构", "二叉树的遍历", "线索二叉树", "树的存储结构", "树与二叉树的转换",
    "森林与二叉树的转换", "树的遍历", "哈夫曼树", "哈夫曼编码", "图的基本概念",
    "邻接矩阵", "邻接表", "十字链表", "邻接多重表", "深度优先搜索", "广度优先搜索",
    "最小生成树", "最短路径", "拓扑排序", "关键路径", "顺序查找", "折半查找", "分块查找",
    "二叉搜索树", "平衡二叉树", "红黑树", "B树", "B+树", "散列表", "直接插入排序",
    "折半插入排序", "起泡排序", "简单选择排序", "希尔排序", "快速排序", "树形选择排序",
    "堆排序", "归并排序", "基数排序", "多路平衡归并", "置换选择排序", "最佳归并树"
]


class SelectQuestionClient:

    def __init__(self, subject_id, core_course_code, user_id):
        self.subject_id = subject_id
        self.core_course_code = core_course_code
        self.user_id = user_id
        self.paper_required = '- 题目总数：10 道\n- 题型要求：单选题 10 道'
        self.qbank_stat = {
            'type': '单选题、主观题',
            'difficulty': '1 - 5',
            'knowledge': ','.join(DS_KNOWLEDGE_LIST),
        }
        # self.first_distribution = PaperDistribution(**first_dist)

        self.gen_paper_llm = ChatOpenAIThinking(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base=settings.DOUBAO_API_BASE,
            model_name='doubao-1-5-pro-256k-250115',
            temperature=0.3,
        )
        self.convert_distribution_llm = ChatOpenAIThinking(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base=settings.DOUBAO_API_BASE,
            model_name='doubao-1-5-pro-32k-250115',
            temperature=0.3,
        )

        # 问题类型枚举
        q_type_map = {}
        for k, v in STQuestion.Type.choices:
            q_type_map[v] = k
        self.q_type_map = q_type_map

    def _build_first_gen_paper_prompt(self) -> str:
        prompt = PromptTemplate.objects.get(app_no='first_shuati_paper_dist')
        return prompt.prompt_content

    def _build_gen_paper_prompt(self) -> str:
        prompt = PromptTemplate.objects.get(app_no='shuati_paper_dist')
        return prompt.prompt_content

    def _build_convert_distribution_prompt(self) -> str:
        prompt = PromptTemplate.objects.get(app_no='shuati_paper_dist_param')
        return prompt.prompt_content

    def _build_knowledge_mastery_level(self, desc: list[KnowledgeMasteryLevel]):
        desc_str = '\n'.join([
            f"- {item.knowledge_name}: {item.accuracy}%" for item in desc
        ])
        return f"知识点的掌握程度\n{desc_str}"

    def get_paper_distribution(self, user_desc: list[KnowledgeMasteryLevel]) -> PaperDistribution:
        if not user_desc:
            first_paper_desc = self._gen_first_paper_desc()
            first_distribution = self._convert_distribution(first_paper_desc)
            print(f"格式转换后🔥：{first_distribution}")
            return first_distribution

        knowledge_mastery_level = self._build_knowledge_mastery_level(user_desc)
        paper_desc = self._gen_paper_desc(knowledge_mastery_level)
        return self._convert_distribution(paper_desc)

    def select(self, paper_dist: PaperDistribution) -> list[int]:
        knowledge_names = [r.knowledge for r in paper_dist.rules]
        knowledge_qs = STKnowledge.objects.filter(name__in=knowledge_names)
        knowledge_name_map = {i.name: i.id for i in knowledge_qs}

        answered_sub_query = STUserPaperQuestionAnswer.objects.filter(
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            question_id=OuterRef('pk')
        ).values('question_id')

        query_list = []
        for r in paper_dist.rules:
            # 处理不符合条件的问题
            if r.question_type not in self.q_type_map:
                continue
            if r.knowledge not in knowledge_name_map:
                continue

            question_type = self.q_type_map.get(r.question_type)
            knowledge_id = knowledge_name_map.get(r.knowledge)

            sub_query = STQuestion.objects.filter(
                question_type=question_type,
                difficulty__gte=r.difficulty,
                stquestionknowledge__knowledge_id=knowledge_id,
            ).filter(
                # 排除存在答题记录的题目（NOT EXISTS）
                ~Exists(answered_sub_query)
            ).values('id').distinct().order_by('difficulty')[:r.question_count]
            query_list.append(sub_query)

        if not query_list:
            return []

        if len(query_list) > 1:
            combined = query_list[0].union(*query_list[1:])
        else:
            combined = query_list[0]

        return [i['id'] for i in combined]

    def _gen_first_paper_desc(self) -> str:
        """生成首次组卷的描述"""
        sys_prompt = self._build_first_gen_paper_prompt()

        prompt_messages = [
            SystemMessage(content=sys_prompt),
            HumanMessage(content="")
        ]
        prompt = ChatPromptTemplate.from_messages(prompt_messages)
        chain = prompt | self.gen_paper_llm
        res = chain.invoke({})
        print(f"首次组卷的描述是🔥✈️:{res.content}")
        return res.content

    def _gen_paper_desc(self, knowledge_mastery_level: str):
        sys_prompt = self._build_gen_paper_prompt()

        prompt_messages = [
            SystemMessage(content=sys_prompt),
            HumanMessage(content=knowledge_mastery_level)
        ]
        prompt = ChatPromptTemplate.from_messages(prompt_messages)
        chain = prompt | self.gen_paper_llm
        res = chain.invoke({})
        return res.content

    def _convert_distribution(self, paper_desc: str) -> PaperDistribution:
        sys_prompt = self._build_convert_distribution_prompt()
        prompt_messages = [
            SystemMessage(content=sys_prompt),
            HumanMessage(content=paper_desc)
        ]
        prompt = ChatPromptTemplate.from_messages(prompt_messages)
        chain = prompt | self.convert_distribution_llm
        res = chain.invoke({})

        distribution = safe_parse_json_response(res.content)
        if not distribution:
            raise AppSystemError(detail_err='无法解析结果')

        try:
            return PaperDistribution.build_model(distribution)
        except Exception as e:
            logger.exception(e)
            raise AppSystemError(detail_err=f'无法解析结果: {e}')


def _test():
    from app.services.shuati_app.dto import KnowledgeMasteryLevel

    subject_id = 'TcT7x2dUZfG7BPyAeRNGFM'
    core_course_code = 'CC_SJJG'
    client = SelectQuestionClient(subject_id, core_course_code, 'user_id')

    # 第一次组卷
    paper_dist = client.get_paper_distribution(user_desc=[])
    res = client.select(paper_dist)

    # 第二次组卷
    user_desc = [
        KnowledgeMasteryLevel(knowledge_name="时间复杂度", accuracy=30),
        KnowledgeMasteryLevel(knowledge_name="单链表", accuracy=40),
        KnowledgeMasteryLevel(knowledge_name="队列", accuracy=20),
        KnowledgeMasteryLevel(knowledge_name="线性表", accuracy=80),
    ]

    paper_dist = client.get_paper_distribution(user_desc=user_desc)
    res = client.select(paper_dist)

