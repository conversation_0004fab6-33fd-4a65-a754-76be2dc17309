from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.models import PromptTemplate


class VideoKnowledgeExtractFeature:
    # 提取摘要内容知识点

    def __init__(self, app_no):
        self.prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no=app_no).first()
        if not self.prompt_template:
            raise Exception('prompt template not found')

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            main_subject: str,
            knowledge_list: list,
            math_video_content: str
    ) -> LLMResult:
        main_subject_map = {'high_math': '高等数学', 'linear_algebra': '线性代数', 'math_prob': '概率论与数理统计'}

        user_inputs = application_generate_entity.inputs.copy()

        knowledge_list_str = '\n'.join(knowledge_list)
        main_subject_title = main_subject_map.get(main_subject)

        knowledge_param_str = ''
        if main_subject_title:
            knowledge_param_str = f'### {main_subject_title}知识点\n'
        knowledge_list_str = f'{knowledge_param_str}{knowledge_list_str}'

        user_inputs['knowledge'] = knowledge_list_str

        prompt_template = self.prompt_template
        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=''
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=math_video_content,
            stream=False
        )
