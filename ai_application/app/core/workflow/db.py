# 阿纪
from django.core.management.base import BaseCommand
from app.models import EnglishReaderStrategy, EnglishReaderPublications
import os
from docx import Document

class Command(BaseCommand):
    help = 'Import English Reader data from DOCX files in specified folders'

    def add_arguments(self, parser):
        parser.add_argument('strategy_folder', type=str, help='Path to the strategy folder')
        parser.add_argument('publications_folder', type=str, help='Path to the publications folder')

    def handle(self, *args, **kwargs):
        strategy_folder = kwargs['strategy_folder']
        publications_folder = kwargs['publications_folder']

        self.save_strategy_files(strategy_folder)
        self.save_publications_files(publications_folder)

    def save_strategy_files(self, folder_path):
        strategy_id = 1
        for filename in os.listdir(folder_path):
            if filename.endswith('.docx'):
                file_path = os.path.join(folder_path, filename)
                doc = Document(file_path)
                strategy_content = '\n'.join([para.text for para in doc.paragraphs])
                strategy_name = os.path.splitext(filename)[0]

                # 保存到数据库
                EnglishReaderStrategy.objects.create(
                    strategy_id=strategy_id,
                    strategy_content=strategy_content,
                    strategy_name=strategy_name
                )
                strategy_id += 1
                self.stdout.write(self.style.SUCCESS(f'Successfully imported strategy: {strategy_name}'))

    def save_publications_files(self, folder_path):
        publications_id = 1
        for filename in os.listdir(folder_path):
            if filename.endswith('.docx'):
                file_path = os.path.join(folder_path, filename)
                doc = Document(file_path)
                publications_content = '\n'.join([para.text for para in doc.paragraphs])
                publications_name = os.path.splitext(filename)[0]

                # 保存到数据库
                EnglishReaderPublications.objects.create(
                    publications_id=publications_id,
                    publications_content=publications_content,
                    publications_name=publications_name
                )
                publications_id += 1
                self.stdout.write(self.style.SUCCESS(f'Successfully imported publication: {publications_name}'))