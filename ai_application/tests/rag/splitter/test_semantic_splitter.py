from app.core.model_provider_manager import ModelProviderManager
from app.core.model_runtime.entities.provider_entities import ModelType
from app.core.rag.embedding.cached_embedding import CacheEmbedding
from app.core.rag.extractor.text_extractor import TextExtractor
from app.core.rag.splitter.semantic_splitter import SemanticSplitter


def test_semantic_splitter():
    file_path = '/Users/<USER>/Downloads/操作系统-less.md'
    docs = TextExtractor(file_path).extract()

    model_manager = ModelProviderManager()

    embedding_model = model_manager.get_model_instance(
        provider='tongyi',
        model_type=ModelType.TEXT_EMBEDDING,
        model='text-embedding-v2'
    )
    embeddings = CacheEmbedding(embedding_model)

    splitter = SemanticSplitter(
        embeddings=embeddings,
        sentence_split_regex=r"(?<=[。？！\n])\s*",
        breakpoint_threshold_amount=60
    )
    res = splitter.split_documents(docs)
    print(len(res))
    for i in res:
        print(len(i.page_content))
