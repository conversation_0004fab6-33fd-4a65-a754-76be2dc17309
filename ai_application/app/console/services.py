from docx import Document
from docx.table import Table
from docx.text.paragraph import Paragraph
from app.models import EnglishWaikanTestRecord, EnglishWordTestAnswerDetail, EnWordReciteBasicAnswerDetail
from app.models import SuperviseLearnStat


def extract_docx_content(file_obj):
    doc = Document(file_obj)
    extracted_content = ""
    content_blocks = []
    current_section = ""
    current_title = None

    # 保存所有标题的层级栈
    section_stack = []

    # 遍历docx文档中的所有段落、表格和图片
    for block in iter_block_items(doc):
        if isinstance(block, Paragraph):
            # 获取段落样式名
            style_name = block.style.name
            block_text = block.text.strip()

            if not block_text:
                continue  # 跳过空段落

            # 根据样式名处理标题
            if "Heading 1" in style_name:
                extracted_content += f"# {block_text}\n\n"
                if current_title:
                    content_blocks.append({
                        "title": current_title,
                        "content": current_section
                    })
                current_section = ""  # 开始新的一级标题内容
                current_title = block_text
                last_heading_level = 1

                # 清空栈，开始新的一级标题
                section_stack = [block_text]

            elif "Heading 2" in style_name:
                extracted_content += f"## {block_text}\n\n"
                # 追加到上一个一级标题的内容
                current_section += f"## {block_text}\n\n"
                last_heading_level = 2

                # 更新栈
                section_stack = section_stack[:1]  # 保留一级标题
                section_stack.append(block_text)

            elif "Heading 3" in style_name:
                extracted_content += f"### {block_text}\n\n"
                # 追加到上一个二级标题的内容
                current_section += f"### {block_text}\n\n"
                last_heading_level = 3

                # 更新栈，保留一级、二级标题
                section_stack = section_stack[:2]
                section_stack.append(block_text)

            else:
                # 普通段落处理，追加到当前section
                if block.runs:
                    for run in block.runs:
                        if run._r.xpath('./w:drawing'):  # 检查是否有图片
                            # 插入图片标记
                            extracted_content += "[此处是图片]\n\n"
                            current_section += "[此处是图片]\n\n"
                        else:
                            extracted_content += f"{run.text}\n\n"
                            current_section += f"{run.text}\n\n"

        elif isinstance(block, Table):
            # 将表格转换为HTML格式并追加到解析内容中
            table_html = table_to_html(block)
            extracted_content += table_html
            current_section += table_html

    # 保存最后一个section
    if current_title:
        content_blocks.append({
            "title": current_title,
            "content": current_section
        })

    return extracted_content, content_blocks


def iter_block_items(parent):
    """
    遍历docx文档中的所有块，包括段落、表格和图片，保持顺序。
    """
    for child in parent.element.body:
        # 处理段落
        if child.tag.endswith('p'):  # 段落
            yield Paragraph(child, parent)

        # 处理表格
        elif child.tag.endswith('tbl'):  # 表格
            yield Table(child, parent)


def table_to_html(table):
    """
    将docx表格转换为HTML格式。
    """
    html_content = '<table border="1" style="border-collapse: collapse;">\n'

    for row in table.rows:
        html_content += '<tr>\n'
        for cell in row.cells:
            cell_text = cell.text.strip() if cell.text.strip() else '&nbsp;'  # 处理空单元格
            html_content += f'<td>{cell_text}</td>\n'
        html_content += '</tr>\n'

    html_content += '</table>\n'
    return html_content


class MessageStatsWaikanQuestion:

    @classmethod
    def get_stats(cls, app_type, name, start_of_today, end_of_today):
        queryset = EnglishWaikanTestRecord.objects.filter(is_deleted=False)
        exception_queryset = queryset.filter(is_exception=True)
        today_exception_queryset = exception_queryset.filter(add_time__range=(start_of_today, end_of_today))
        return {
            'name': name,
            'app_type': app_type,
            'message_type': app_type,
            'total_count': queryset.count(),
            'exception_count': exception_queryset.count(),
            'today_exception_count': today_exception_queryset.count(),
        }

    @classmethod
    def ge_queryset(cls):
        return EnglishWaikanTestRecord.objects.filter(is_deleted=False).select_related('question').order_by('-pk')


class MessageStatsEnWordTestQuestion:

    @classmethod
    def get_stats(cls, app_type, name, start_of_today, end_of_today):
        queryset = EnglishWordTestAnswerDetail.objects.filter(is_deleted=False)
        exception_queryset = queryset.filter(is_exception=True)
        today_exception_queryset = exception_queryset.filter(add_time__range=(start_of_today, end_of_today))
        return {
            'name': name,
            'app_type': app_type,
            'message_type': app_type,
            'total_count': queryset.count(),
            'exception_count': exception_queryset.count(),
            'today_exception_count': today_exception_queryset.count(),
        }

    @classmethod
    def ge_queryset(cls):
        return EnglishWordTestAnswerDetail.objects.filter(is_deleted=False).select_related('record').select_related('question').order_by('-pk')


class MessageStatsEnWordReciteQuestion:

    @classmethod
    def get_stats(cls, app_type, name, start_of_today, end_of_today):
        queryset = EnWordReciteBasicAnswerDetail.objects.filter(is_deleted=False)
        exception_queryset = queryset.filter(is_exception=True)
        today_exception_queryset = exception_queryset.filter(add_time__range=(start_of_today, end_of_today))
        return {
            'name': name,
            'app_type': app_type,
            'message_type': app_type,
            'total_count': queryset.count(),
            'exception_count': exception_queryset.count(),
            'today_exception_count': today_exception_queryset.count(),
        }

    @classmethod
    def ge_queryset(cls):
        return EnWordReciteBasicAnswerDetail.objects.filter(
            is_deleted=False, is_answered=True
        ).select_related('basic_paper').select_related('question').order_by('-pk')


class MessageStatsSuperviseLearnStat:

    @classmethod
    def get_stats(cls, app_type, name, start_of_today, end_of_today):
        queryset = SuperviseLearnStat.objects.filter(is_deleted=False)
        exception_queryset = queryset.filter(status='FAIL')
        today_exception_queryset = exception_queryset.filter(add_time__range=(start_of_today, end_of_today))
        return {
            'name': name,
            'app_type': app_type,
            'message_type': app_type,
            'total_count': queryset.count(),
            'exception_count': exception_queryset.count(),
            'today_exception_count': today_exception_queryset.count(),
        }

    @classmethod
    def ge_queryset(cls):
        return SuperviseLearnStat.objects.filter(is_deleted=False).order_by('-pk')
