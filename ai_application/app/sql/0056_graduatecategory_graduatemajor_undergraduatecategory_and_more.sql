--
-- Create model GraduateCategory
--
CREATE TABLE `ai_graduate_category` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `name` varchar(100) NOT NULL);
--
-- Create model GraduateMajor
--
CREATE TABLE `ai_graduate_major` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `name` varchar(100) NOT NULL, `category_id` bigint NOT NULL);
--
-- Create model UndergraduateCategory
--
CREATE TABLE `ai_undergraduate_category` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `name` varchar(100) NOT NULL);
--
-- <PERSON>reate model UndergraduateMajor
--
CREATE TABLE `ai_undergraduate_major` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `name` varchar(100) NOT NULL, `category_id` bigint NOT NULL);
--
-- Create model MajorCorrelation
--
CREATE TABLE `ai_major_correlation` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `level` integer NOT NULL, `grad_major_id` bigint NOT NULL, `undergrad_major_id` bigint NOT NULL);
--
-- Create model CategoryCorrelation
--
CREATE TABLE `ai_category_correlation` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `level` integer NOT NULL, `grad_category_id` bigint NOT NULL, `undergrad_category_id` bigint NOT NULL);
CREATE INDEX `ai_graduate_category_add_time_3d28d646` ON `ai_graduate_category` (`add_time`);
ALTER TABLE `ai_graduate_major` ADD CONSTRAINT `ai_graduate_major_category_id_ef7de86c_fk_ai_gradua` FOREIGN KEY (`category_id`) REFERENCES `ai_graduate_category` (`id`);
CREATE INDEX `ai_graduate_major_add_time_8b4c1a46` ON `ai_graduate_major` (`add_time`);
CREATE INDEX `ai_undergraduate_category_add_time_88b481ff` ON `ai_undergraduate_category` (`add_time`);
ALTER TABLE `ai_undergraduate_major` ADD CONSTRAINT `ai_undergraduate_maj_category_id_fb9c71a2_fk_ai_underg` FOREIGN KEY (`category_id`) REFERENCES `ai_undergraduate_category` (`id`);
CREATE INDEX `ai_undergraduate_major_add_time_37df404d` ON `ai_undergraduate_major` (`add_time`);
ALTER TABLE `ai_major_correlation` ADD CONSTRAINT `ai_major_correlation_undergrad_major_id_grad__848ee212_uniq` UNIQUE (`undergrad_major_id`, `grad_major_id`);
ALTER TABLE `ai_major_correlation` ADD CONSTRAINT `ai_major_correlation_grad_major_id_546fb4e8_fk_ai_gradua` FOREIGN KEY (`grad_major_id`) REFERENCES `ai_graduate_major` (`id`);
ALTER TABLE `ai_major_correlation` ADD CONSTRAINT `ai_major_correlation_undergrad_major_id_f661c9c4_fk_ai_underg` FOREIGN KEY (`undergrad_major_id`) REFERENCES `ai_undergraduate_major` (`id`);
CREATE INDEX `ai_major_correlation_add_time_a8c5155a` ON `ai_major_correlation` (`add_time`);
ALTER TABLE `ai_category_correlation` ADD CONSTRAINT `ai_category_correlation_undergrad_category_id_gr_b880be1a_uniq` UNIQUE (`undergrad_category_id`, `grad_category_id`);
ALTER TABLE `ai_category_correlation` ADD CONSTRAINT `ai_category_correlat_grad_category_id_dbc6b4ad_fk_ai_gradua` FOREIGN KEY (`grad_category_id`) REFERENCES `ai_graduate_category` (`id`);
ALTER TABLE `ai_category_correlation` ADD CONSTRAINT `ai_category_correlat_undergrad_category_i_a668ba19_fk_ai_underg` FOREIGN KEY (`undergrad_category_id`) REFERENCES `ai_undergraduate_category` (`id`);
CREATE INDEX `ai_category_correlation_add_time_18a48b81` ON `ai_category_correlation` (`add_time`);