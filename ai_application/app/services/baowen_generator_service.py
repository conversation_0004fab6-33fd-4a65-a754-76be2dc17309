# 阿纪
import random
import re
from typing import Any

from app.api.dto import ChatMessageDto
from app.models import Account, InvokeFrom, PromptTemplate, HotArticleSegment, HotArticleContent
from app.services.app_generate_service import AppGenerateService


class ContentGeneratorService:

    @classmethod
    def extract_content(cls, text: str, account: Account) -> dict:
        template = PromptTemplate.objects.filter(app_no='extract_content').first()

        dto = ChatMessageDto(
            app_id='extract_content',
            inputs={
                "max_tokens": 1000,
                "temperature": 0.3,
                'top_p': 0.5,
                'prompt_template': template.id
            },
            query=text,
            stream=False,
        )
        response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)
        extracted_text = response.get('answer', '')

        return {
            "extracted_text": extracted_text,
        }

    @classmethod
    def generate_content(cls, account: Account) -> dict:
        template = PromptTemplate.objects.filter(app_no='article_generation_body_new').first()
        # 提取观点内容
        segment = cls.get_viewpoint_segment()
        if not segment:
            raise ValueError("No segments available for content generation")
        viewpoint = segment.viewpoint

        dto = ChatMessageDto(
            app_id='article_generation',
            inputs={
                'prompt_template': template.id
            },
            query=viewpoint,
            stream=False,
        )

        response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)
        response_content = response.get('answer', '')

        # 解析生成的内容
        parsed_content = cls.parse_response_content(response_content)

        # 将生成的内容保存到 HotArticleContent 表，并更新 HotArticleSegment 的生成次数
        cls.save_generated_content(segment, parsed_content)

        return parsed_content

    @classmethod
    def parse_response_content(cls, response_content: str) -> dict:

        result = {
            "covers": ["https://oss.kaoyanvip.cn/attach/file1733886471847.jpg"],
            "title": "",
            "body": "",
            "tags": []
        }

        lines = response_content.split("\n")

        title_found = False  # 是否已经找到标题
        body_lines = []
        tags_line = ""

        # 遍历所有行，提取标题和正文
        for i, line in enumerate(lines):
            if not title_found and line.strip():
                # 第一个非空行作为标题
                result["title"] = line.strip()
                title_found = True
            elif title_found and line.strip():
                # 找到标题后，接下来的内容作为正文起始
                body_lines = lines[i:]
                break

        # 遍历正文部分，提取正文内容和标签行
        for i, line in enumerate(body_lines):
            if line.startswith("#"):
                # 以 "#" 开头的行认为是标签行
                tags_line = line
                body_lines = body_lines[:i]  # 标签行之前的内容作为正文
                break

        result["body"] = "\n".join(body_lines).strip()

        # tags可以接受为空数组
        if tags_line:
            result["tags"] = [
                re.sub(r"[^\w\s#]", "", tag.strip())  # 移除 emoji 表情
                for tag in tags_line.split("#")  # 按 "#" 分割每个标签
                if tag.strip() \
                ]
        return result

    # @classmethod
    # def get_viewpoint(cls) -> str:
    #     # TODO 观点内容系统生成，目前先随机内置一些
    #     temp_contents = [
    #         '考研可以提升就业竞争力，尤其是对于希望进入好企业的学生来说，院校档次很重要。',
    #         '许多二本或民办高校的学生在考研时自信能考上985院校。',
    #         '目前中国招收的博士生中，80%是学术型硕士，20%是专业型硕士。',
    #         '计算机专业毕业生在就业市场上的薪资较高',
    #     ]
    #     return random.choice(temp_contents)

    @classmethod
    def get_viewpoint_segment(cls) -> HotArticleSegment:

        # 优先按 gen_article_times 升序排序，若相同则按 id 升序。

        segment = HotArticleSegment.objects.filter(
            is_deleted=False,
            is_gen_viewpoint=True
        ).order_by('gen_article_times', 'id').first()

        return segment

    @classmethod
    def save_generated_content(cls, segment: HotArticleSegment, content_data: dict):
        # 保存生成的内容到 HotArticleContent 表
        HotArticleContent.objects.create(
            segment=segment,
            covers=content_data.get("covers"),
            title=content_data.get("title"),
            body=content_data.get("body"),
            tags=content_data.get("tags"),
        )

        segment.gen_article_times += 1
        segment.save()


class RedContentGeneratorService:

    SUBJECTS = [
        ('考研 VS 就业', 'https://oss.kaoyanvip.cn/attach/file1734492689166.png'),
        ('二本逆袭985', 'https://oss.kaoyanvip.cn/attach/file1734492688667.png'),
        ('考研择校建议', 'https://oss.kaoyanvip.cn/attach/file1734492689492.png'),
        ('高目标 VS 现实选择',  'https://oss.kaoyanvip.cn/attach/file1734492690341.png'),
        ('考研提升就业力', 'https://oss.kaoyanvip.cn/attach/file1734492689779.png'),
        ('考研迷思与策略', 'https://oss.kaoyanvip.cn/attach/file1734492690072.png'),
    ]

    @classmethod
    def _get_a_random_subject(cls) -> tuple[str, str]:
        subjects = cls.SUBJECTS[:]
        random.shuffle(subjects)
        return random.choice(subjects)

    @classmethod
    def generate_content(cls, account: Account, invoke_from: str = InvokeFrom.api.value) -> dict[str, Any]:
        # 随机获取主题和及其封面图
        subject, cover = cls._get_a_random_subject()

        # 提取观点内容
        segment = ContentGeneratorService.get_viewpoint_segment()
        if not segment:
            raise ValueError("No segments available for content generation")

        dto = ChatMessageDto(
            app_id='red_generator',
            inputs={
                'subject': subject,
                'viewpoint': segment.viewpoint
            },
            stream=False,
        )
        response = AppGenerateService.generate(dto, account, invoke_from=invoke_from)
        # 解析生成的内容
        parsed_content = ContentGeneratorService.parse_response_content(response.get('answer', ''))
        parsed_content['covers'] = [cover]

        # 将生成的内容保存到 HotArticleContent 表，并更新 HotArticleSegment 的生成次数
        ContentGeneratorService.save_generated_content(segment, parsed_content)

        return parsed_content
