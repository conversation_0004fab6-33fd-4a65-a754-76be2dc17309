from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.queue_entities import (
    AppQueueEvent, MessageQueueMessage, QueueStopEvent, QueueErrorEvent,
    QueueMessageEndEvent
)


class MessageBasedAppQueueManager(AppQueueManager):

    def __init__(
            self,
            task_id: str,
            conversation_id: str,
            message_id: str,
            app_mode: str
    ) -> None:
        super().__init__(task_id)

        self._conversation_id = str(conversation_id)
        self._message_id = str(message_id)
        self._app_mode = app_mode

    def _publish(self, event: AppQueueEvent) -> None:
        message = MessageQueueMessage(
            task_id=self._task_id,
            message_id=self._message_id,
            conversation_id=self._conversation_id,
            app_mode=self._app_mode,
            event=event
        )
        self._q.put(message)

        if isinstance(event, QueueStopEvent | QueueErrorEvent | QueueMessageEndEvent):
            self.stop_listen()
