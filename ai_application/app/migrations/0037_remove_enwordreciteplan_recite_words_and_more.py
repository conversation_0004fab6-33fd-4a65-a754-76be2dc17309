# Generated by Django 4.2 on 2025-04-02 07:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0036_enwordrecitebasicanswer_enwordrecitebasicpaper_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='enwordreciteplan',
            name='recite_words',
        ),
        migrations.RemoveField(
            model_name='enwordreciteplanrecord',
            name='recite_words',
        ),
        migrations.AlterField(
            model_name='enwordrecitedayrecord',
            name='day',
            field=models.DateField(auto_now_add=True, verbose_name='背诵日期'),
        ),
        migrations.CreateModel(
            name='EnWordReciteDayPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('day', models.DateField(auto_now_add=True, verbose_name='背诵日期')),
                ('recite_words', models.JSONField(null=True, verbose_name='背诵单词列表')),
                ('plan', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordreciteplan')),
                ('plan_record', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordreciteplanrecord')),
            ],
            options={
                'verbose_name': '背单词当日计划单词表',
                'verbose_name_plural': '背单词当日计划单词表',
                'db_table': 'ai_english_word_recite_day_plan',
            },
        ),
        migrations.AddField(
            model_name='enwordrecitedayrecord',
            name='day_plan',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitedayplan'),
        ),
    ]
