--
-- Create model STFirstRoundPaper
--
CREATE TABLE `ai_st_first_round_paper`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool        NOT NULL,
    `subject_id`       varchar(32) NOT NULL,
    `core_course_code` varchar(32) NOT NULL,
    `paper_content`    json NULL
);
--
-- Create model STKnowledge
--
CREATE TABLE `ai_st_knowledge`
(
    `id`                bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`          datetime(6) NOT NULL,
    `modified_time`     datetime(6) NOT NULL,
    `is_deleted`        bool          NOT NULL,
    `subject_id`        varchar(32)   NOT NULL,
    `core_course_code`  varchar(32)   NOT NULL,
    `subject_name`      varchar(32)   NOT NULL,
    `core_course_name`  varchar(32)   NOT NULL,
    `name`              varchar(32)   NOT NULL,
    `definition`        varchar(255)  NOT NULL,
    `kg_qs_count`       integer       NOT NULL,
    `related_video_url` varchar(255)  NOT NULL,
    `percentage`        numeric(5, 2) NOT NULL,
    `is_first_round`    bool          NOT NULL
);
--
-- Create model STQuestion
--
CREATE TABLE `ai_st_question`
(
    `id`                      bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`                datetime(6) NOT NULL,
    `modified_time`           datetime(6) NOT NULL,
    `is_deleted`              bool        NOT NULL,
    `subject_id`              varchar(32) NOT NULL,
    `core_course_code`        varchar(32) NOT NULL,
    `subject_name`            varchar(32) NOT NULL,
    `core_course_name`        varchar(32) NOT NULL,
    `question_intid`          varchar(16) NOT NULL,
    `question_type`           integer     NOT NULL,
    `question_content`        longtext NULL,
    `difficulty`              integer     NOT NULL,
    `is_exam`                 bool        NOT NULL,
    `is_unified`              bool        NOT NULL,
    `exam_year`               integer     NOT NULL,
    `exam_school`             varchar(32) NOT NULL,
    `analysis`                longtext NULL,
    `format_question_content` json NULL,
    `knowledge_list`          json NULL,
    `knowledge_count`         integer     NOT NULL
);
--
-- Create model STSubjectStageStrategy
--
CREATE TABLE `ai_st_subject_stage_strategy`
(
    `id`                                 bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`                           datetime(6) NOT NULL,
    `modified_time`                      datetime(6) NOT NULL,
    `is_deleted`                         bool        NOT NULL,
    `subject_id`                         varchar(32) NOT NULL,
    `core_course_code`                   varchar(32) NOT NULL,
    `learning_stage`                     varchar(32) NOT NULL,
    `stage_question_strategy`            json NULL,
    `stage_strengthen_question_strategy` json NULL,
    `stage_change_required`              json NULL
);
--
-- Create model STUserPaper
--
CREATE TABLE `ai_st_user_paper`
(
    `id`                  bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`            datetime(6) NOT NULL,
    `modified_time`       datetime(6) NOT NULL,
    `is_deleted`          bool          NOT NULL,
    `subject_id`          varchar(32)   NOT NULL,
    `core_course_code`    varchar(32)   NOT NULL,
    `user_id`             varchar(32)   NOT NULL,
    `question_ids`        json NULL,
    `is_answered`         bool          NOT NULL,
    `learning_stage`      varchar(32)   NOT NULL,
    `is_stage_strengthen` bool          NOT NULL,
    `stage_round`         integer       NOT NULL,
    `stage_total_rounds`  integer       NOT NULL,
    `total_round`         integer       NOT NULL,
    `paper_score`         numeric(5, 2) NOT NULL
);
--
-- Create model STUserPaperAnswer
--
CREATE TABLE `ai_st_user_paper_answer`
(
    `id`                    bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`              datetime(6) NOT NULL,
    `modified_time`         datetime(6) NOT NULL,
    `is_deleted`            bool          NOT NULL,
    `user_id`               varchar(32)   NOT NULL,
    `is_finished`           bool          NOT NULL,
    `finished_time`         datetime(6) NULL,
    `answered_question_ids` json NULL,
    `answered_score`        numeric(5, 2) NOT NULL,
    `subjective_score`      numeric(5, 2) NOT NULL,
    `report_status`         varchar(16)   NOT NULL,
    `report_retry_count`    integer       NOT NULL,
    `report_params`         json NULL,
    `report`                longtext NULL,
    `paper_id`              bigint        NOT NULL
);
--
-- Create model STUserPaperDistribution
--
CREATE TABLE `ai_st_user_paper_distribution`
(
    `id`                 bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`           datetime(6) NOT NULL,
    `modified_time`      datetime(6) NOT NULL,
    `is_deleted`         bool        NOT NULL,
    `subject_id`         varchar(32) NOT NULL,
    `core_course_code`   varchar(32) NOT NULL,
    `user_id`            varchar(32) NOT NULL,
    `paper_distribution` json NULL,
    `is_selected`        bool        NOT NULL,
    `selected_questions` json NULL
);
--
-- Create model STUserRoundAnalysis
--
CREATE TABLE `ai_st_user_round_analysis`
(
    `id`                     bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`               datetime(6) NOT NULL,
    `modified_time`          datetime(6) NOT NULL,
    `is_deleted`             bool          NOT NULL,
    `subject_id`             varchar(32)   NOT NULL,
    `core_course_code`       varchar(32)   NOT NULL,
    `user_id`                varchar(32)   NOT NULL,
    `examined_rounds`        integer       NOT NULL,
    `total_assessment_score` numeric(8, 2) NOT NULL,
    `total_subjective_score` numeric(8, 2) NOT NULL,
    `learning_stage`         varchar(32)   NOT NULL,
    `stage_examined_rounds`  integer       NOT NULL,
    `stage_total_rounds`     integer       NOT NULL,
    `stage_change_type`      varchar(16)   NOT NULL,
    `is_stage_strengthen`    bool          NOT NULL,
    `strengthen_pass_rounds` integer       NOT NULL,
    `is_test_stop`           bool          NOT NULL,
    `stop_reason`            integer       NOT NULL,
    `strengthen_fail_rounds` integer       NOT NULL
);
--
-- Add field scene_info to conversation
--
ALTER TABLE `ai_conversation`
    ADD COLUMN `scene_info` json NULL;
--
-- Alter field malicious_attack_type on message
--
-- (no-op)
--
-- Create model STUserRoundChangeRecord
--
CREATE TABLE `ai_st_user_round_change_record`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool        NOT NULL,
    `subject_id`       varchar(32) NOT NULL,
    `core_course_code` varchar(32) NOT NULL,
    `user_id`          varchar(32) NOT NULL,
    `old_stage`        varchar(32) NOT NULL,
    `new_stage`        varchar(32) NOT NULL,
    `change_type`      varchar(16) NOT NULL,
    `change_report`    longtext NULL,
    `answer_id`        bigint      NOT NULL
);
--
-- Create model STUserPaperQuestion
--
CREATE TABLE `ai_st_user_paper_question`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool          NOT NULL,
    `subject_id`       varchar(32)   NOT NULL,
    `core_course_code` varchar(32)   NOT NULL,
    `user_id`          varchar(32)   NOT NULL,
    `score`            numeric(5, 2) NOT NULL,
    `paper_id`         bigint        NOT NULL,
    `question_id`      bigint        NOT NULL
);
--
-- Create model STUserKnowledgeMasteryLevel
--
CREATE TABLE `ai_st_user_knowledge_mastery_level`
(
    `id`                      bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`                datetime(6) NOT NULL,
    `modified_time`           datetime(6) NOT NULL,
    `is_deleted`              bool          NOT NULL,
    `subject_id`              varchar(32)   NOT NULL,
    `core_course_code`        varchar(32)   NOT NULL,
    `user_id`                 varchar(32)   NOT NULL,
    `answer_count`            integer       NOT NULL,
    `right_count`             integer       NOT NULL,
    `wrong_count`             integer       NOT NULL,
    `accuracy`                numeric(5, 2) NOT NULL,
    `total_accuracy`          numeric(5, 2) NOT NULL,
    `last_answer_status`      varchar(16)   NOT NULL,
    `last_answer_time`        datetime(6) NULL,
    `occr_round`              integer       NOT NULL,
    `consecutive_right_round` integer       NOT NULL,
    `consecutive_wrong_round` integer       NOT NULL,
    `waiting_round`           integer       NOT NULL,
    `knowledge_id`            bigint        NOT NULL
);
--
-- Create model STUserKnowledgeDistribution
--
CREATE TABLE `ai_st_user_knowledge_distribution`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool          NOT NULL,
    `subject_id`       varchar(32)   NOT NULL,
    `core_course_code` varchar(32)   NOT NULL,
    `user_id`          varchar(32)   NOT NULL,
    `question_type`    integer       NOT NULL,
    `difficulty`       integer       NOT NULL,
    `answer_count`     integer       NOT NULL,
    `right_count`      integer       NOT NULL,
    `wrong_count`      integer       NOT NULL,
    `accuracy`         numeric(5, 2) NOT NULL,
    `knowledge_id`     bigint        NOT NULL
);
--
-- Create model STUserAssessmentDetail
--
CREATE TABLE `ai_st_user_assessment_detail`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool          NOT NULL,
    `subject_id`       varchar(32)   NOT NULL,
    `core_course_code` varchar(32)   NOT NULL,
    `user_id`          varchar(32)   NOT NULL,
    `assessment_type`  varchar(16)   NOT NULL,
    `assessment_round` integer       NOT NULL,
    `assessment_score` numeric(5, 2) NOT NULL,
    `subjective_score` numeric(5, 2) NULL,
    `answer_id`        bigint        NOT NULL,
    `paper_id`         bigint        NOT NULL
);
--
-- Create model STQuestionKnowledge
--
CREATE TABLE `ai_st_question_knowledge`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool   NOT NULL,
    `knowledge_id`  bigint NOT NULL,
    `question_id`   bigint NOT NULL
);
--
-- Create model STKnowledgeRelation
--
CREATE TABLE `ai_st_knowledge_relation`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool   NOT NULL,
    `knowledge_id`     bigint NOT NULL,
    `pre_knowledge_id` bigint NOT NULL
);
--
-- Create model STUserPaperQuestionAnswer
--
CREATE TABLE `ai_st_user_paper_question_answer`
(
    `id`                 bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`           datetime(6) NOT NULL,
    `modified_time`      datetime(6) NOT NULL,
    `is_deleted`         bool          NOT NULL,
    `subject_id`         varchar(32)   NOT NULL,
    `core_course_code`   varchar(32)   NOT NULL,
    `user_id`            varchar(32)   NOT NULL,
    `answer_status`      varchar(16)   NOT NULL,
    `choice_answer`      json NULL,
    `subjective_answer`  json NULL,
    `image_text`         longtext NULL,
    `score_rate`         integer       NOT NULL,
    `answer_score`       numeric(5, 2) NOT NULL,
    `report_status`      varchar(16)   NOT NULL,
    `report_retry_count` integer       NOT NULL,
    `report`             longtext NULL,
    `answer_id`          bigint        NOT NULL,
    `paper_id`           bigint        NOT NULL,
    `question_id`        bigint        NOT NULL
);
CREATE INDEX `ai_st_first_round_paper_add_time_b637120b` ON `ai_st_first_round_paper` (`add_time`);
CREATE INDEX `ai_st_knowledge_add_time_0bdc7c30` ON `ai_st_knowledge` (`add_time`);
CREATE INDEX `ai_st_question_add_time_fe991f90` ON `ai_st_question` (`add_time`);
CREATE INDEX `ai_st_question_question_intid_4cf7aaa1` ON `ai_st_question` (`question_intid`);
CREATE INDEX `ai_st_question_difficulty_question_type__dd1f6aaf_idx` ON `ai_st_question` (`difficulty`, `question_type`, `core_course_code`);
CREATE INDEX `ai_st_subject_stage_strategy_add_time_f3484f5a` ON `ai_st_subject_stage_strategy` (`add_time`);
CREATE INDEX `ai_st_user_paper_add_time_d088bb52` ON `ai_st_user_paper` (`add_time`);
CREATE INDEX `ai_st_user_paper_user_id_0b4b5ca9` ON `ai_st_user_paper` (`user_id`);
CREATE INDEX `ai_st_user_paper_answer_add_time_7492884a` ON `ai_st_user_paper_answer` (`add_time`);
CREATE INDEX `ai_st_user_paper_answer_user_id_e540bcb8` ON `ai_st_user_paper_answer` (`user_id`);
CREATE INDEX `ai_st_user_paper_answer_paper_id_6e66d7bb` ON `ai_st_user_paper_answer` (`paper_id`);
CREATE INDEX `ai_st_user_paper_distribution_add_time_42ebcab3` ON `ai_st_user_paper_distribution` (`add_time`);
CREATE INDEX `ai_st_user_paper_distribution_user_id_20ae3e6b` ON `ai_st_user_paper_distribution` (`user_id`);
CREATE INDEX `ai_st_user_round_analysis_add_time_e8384550` ON `ai_st_user_round_analysis` (`add_time`);
CREATE INDEX `ai_st_user_round_analysis_user_id_434feb9a` ON `ai_st_user_round_analysis` (`user_id`);
CREATE INDEX `ai_st_user_round_change_record_add_time_fef632a6` ON `ai_st_user_round_change_record` (`add_time`);
CREATE INDEX `ai_st_user_round_change_record_user_id_b82c632a` ON `ai_st_user_round_change_record` (`user_id`);
CREATE INDEX `ai_st_user_round_change_record_answer_id_618a9f7b` ON `ai_st_user_round_change_record` (`answer_id`);
CREATE INDEX `ai_st_user_paper_question_add_time_dd4492d9` ON `ai_st_user_paper_question` (`add_time`);
CREATE INDEX `ai_st_user_paper_question_user_id_520bbee8` ON `ai_st_user_paper_question` (`user_id`);
CREATE INDEX `ai_st_user_paper_question_paper_id_f8ddcbb7` ON `ai_st_user_paper_question` (`paper_id`);
CREATE INDEX `ai_st_user_paper_question_question_id_2dfced7b` ON `ai_st_user_paper_question` (`question_id`);
CREATE INDEX `ai_st_user_knowledge_mastery_level_add_time_581ecbe4` ON `ai_st_user_knowledge_mastery_level` (`add_time`);
CREATE INDEX `ai_st_user_knowledge_mastery_level_user_id_fd8255f4` ON `ai_st_user_knowledge_mastery_level` (`user_id`);
CREATE INDEX `ai_st_user_knowledge_mastery_level_knowledge_id_a8f4195f` ON `ai_st_user_knowledge_mastery_level` (`knowledge_id`);
CREATE INDEX `ai_st_user_knowledge_distribution_add_time_2771c98e` ON `ai_st_user_knowledge_distribution` (`add_time`);
CREATE INDEX `ai_st_user_knowledge_distribution_user_id_fdea31da` ON `ai_st_user_knowledge_distribution` (`user_id`);
CREATE INDEX `ai_st_user_knowledge_distribution_knowledge_id_3f905118` ON `ai_st_user_knowledge_distribution` (`knowledge_id`);
CREATE INDEX `ai_st_user_assessment_detail_add_time_e57da21c` ON `ai_st_user_assessment_detail` (`add_time`);
CREATE INDEX `ai_st_user_assessment_detail_user_id_d766aebc` ON `ai_st_user_assessment_detail` (`user_id`);
CREATE INDEX `ai_st_user_assessment_detail_answer_id_d45f6d6f` ON `ai_st_user_assessment_detail` (`answer_id`);
CREATE INDEX `ai_st_user_assessment_detail_paper_id_e94d9dea` ON `ai_st_user_assessment_detail` (`paper_id`);
CREATE INDEX `ai_st_question_knowledge_add_time_7fe6a78f` ON `ai_st_question_knowledge` (`add_time`);
CREATE INDEX `ai_st_question_knowledge_knowledge_id_38e62aba` ON `ai_st_question_knowledge` (`knowledge_id`);
CREATE INDEX `ai_st_question_knowledge_question_id_5077a17c` ON `ai_st_question_knowledge` (`question_id`);
CREATE INDEX `ai_st_knowledge_relation_add_time_28cc6943` ON `ai_st_knowledge_relation` (`add_time`);
CREATE INDEX `ai_st_knowledge_relation_knowledge_id_191f36d5` ON `ai_st_knowledge_relation` (`knowledge_id`);
CREATE INDEX `ai_st_knowledge_relation_pre_knowledge_id_8064d635` ON `ai_st_knowledge_relation` (`pre_knowledge_id`);
CREATE INDEX `ai_st_user_paper_question_answer_add_time_97c84e8b` ON `ai_st_user_paper_question_answer` (`add_time`);
CREATE INDEX `ai_st_user_paper_question_answer_user_id_65edabfb` ON `ai_st_user_paper_question_answer` (`user_id`);
CREATE INDEX `ai_st_user_paper_question_answer_answer_id_0d572238` ON `ai_st_user_paper_question_answer` (`answer_id`);
CREATE INDEX `ai_st_user_paper_question_answer_paper_id_5853281e` ON `ai_st_user_paper_question_answer` (`paper_id`);
CREATE INDEX `ai_st_user_paper_question_answer_question_id_5049a76f` ON `ai_st_user_paper_question_answer` (`question_id`);
CREATE INDEX `ai_st_user_paper_question_user_id_core_course_code_aadba622_idx` ON `ai_st_user_paper_question_answer` (`user_id`, `core_course_code`);