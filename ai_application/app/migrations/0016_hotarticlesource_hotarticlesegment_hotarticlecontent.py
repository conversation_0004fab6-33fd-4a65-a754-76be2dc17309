# Generated by Django 4.2 on 2024-12-16 02:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0015_questionnewrecord'),
    ]

    operations = [
        migrations.CreateModel(
            name='HotArticleSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('source', models.CharField(choices=[('media_subtitle', '音视频字幕'), ('document', '文档内容')], default='media_subtitle', max_length=32, verbose_name='来源')),
                ('content', models.TextField(blank=True, verbose_name='内容')),
                ('status', models.CharField(blank=True, max_length=32, verbose_name='状态')),
                ('biz_id', models.CharField(blank=True, db_index=True, max_length=32, verbose_name='业务id')),
            ],
            options={
                'verbose_name': '文档源',
                'verbose_name_plural': '文档源',
                'db_table': 'ai_hot_article_source',
            },
        ),
        migrations.CreateModel(
            name='HotArticleSegment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('content', models.TextField(blank=True, verbose_name='内容')),
                ('word_count', models.IntegerField(default=0, verbose_name='字数')),
                ('is_gen_viewpoint', models.BooleanField(default=False, verbose_name='是否生成观点')),
                ('viewpoint', models.TextField(blank=True, verbose_name='观点')),
                ('gen_article_times', models.IntegerField(default=0, verbose_name='生成文章次数')),
                ('source', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.hotarticlesource')),
            ],
            options={
                'verbose_name': '文档分段',
                'verbose_name_plural': '文档分段',
                'db_table': 'ai_hot_article_segment',
            },
        ),
        migrations.CreateModel(
            name='HotArticleContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('covers', models.JSONField(null=True, verbose_name='封面')),
                ('title', models.CharField(blank=True, max_length=255, verbose_name='标题')),
                ('body', models.TextField(blank=True, verbose_name='内容')),
                ('tags', models.JSONField(null=True, verbose_name='标签')),
                ('segment', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.hotarticlesegment')),
            ],
            options={
                'verbose_name': '文章内容',
                'verbose_name_plural': '文章内容',
                'db_table': 'ai_hot_article_content',
            },
        ),
    ]
