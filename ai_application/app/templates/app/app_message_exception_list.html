<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常消息页面</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/marked/13.0.2/marked.min.js"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        td {
            vertical-align: top;
        }

        th {
            background-color: #f2f2f2;
            osition: sticky;
            top: -2px; /* 固定在顶部 */
            z-index: 10; /* 确保在其他内容之上 */
        }
        
        .table-container {
            max-height: 80vh; /* 设置容器高度以启用滚动 */
            overflow-y: auto; /* 启用垂直滚动 */
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        #pagination {
            float: right;
            margin-right: 20px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .modal-content {
            background-color: white;
            margin: 20% auto;
            padding: 20px;
            width: 50%;
            height: 300px;
            border: 1px solid #ccc;
        }

        .modal-content img {
            max-width: 100%;
            height: 300px;
        }
    </style>
</head>
<body>
<h1>
    <span>【{{ app_name }}】异常消息页面</span>
    <a style="font-size: 18px" href="{% url 'app_message_stats' %}">返回</a>
</h1>

<div class="table-container" id="table-container">
    <table id="data-table">
        <thead>
        <tr>
            <th>账号 ID</th>
            <th style="width:10%">提问时间</th>
             {% if not is_inner_app %}
            <th style="width:70px">所属应用</th>
            <th style="width:70px">课程名称</th>
            <th style="width:70px">平台</th>
            {% endif %}
            <th style="width:20%">提问内容</th>
            <th style="width:20%">回答</th>
            <th>tokens</th>
            <th>异常原因</th>
            <th>敏感词</th>
        </tr>
        </thead>
        <tbody id="table-body">
        {% for msg in message_list %}
            <tr>
                <td>{{ msg.user_id }}</td>
                <td>{{ msg.add_time }}</td>
                {% if not is_inner_app %}
                <td>{{ msg.app_name }}</td>
                <td>{{ msg.goods_name }}</td>
                <td>{{ msg.platform }}</td>
                {% endif %}
                <td>
                    <div class="message-content">{{ msg.query }}</div>
                    {% if msg.question_img %}
                        <div class="image-preview-show"><img width="50" height="50" src="{{ msg.question_img }}" alt=""></div>
                    {% endif %}
                </td>
                <td class="markdown-body">{{ msg.answer }}</td>
                <td class="message-token">
                    <div>提问：{{ msg.message_tokens }}</div>
                    <div>回答：{{ msg.answer_tokens }}</div>
                </td>
                <td>
                    {% if msg.is_exception %}
                        {% if msg.exception_reason %}
                            <div>失败原因：{{ msg.exception_reason }}</div>
                        {% endif %}
                        {% if msg.exception_image %}
                            <div class="fail-image image-preview-show">
                                <img width="50" height="50" src="{{ msg.exception_image }}" alt="">
                            </div>
                        {% endif %}
                    {% endif %}
                </td>
                <td>{{ msg.sensitive_content }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>


<br>

{#<p id="pagination">#}
    {# 上一页 #}
{#    {% if prev_page > 0 %}#}
{#        <button>#}
{#            <a href="{% if message_type %}{% url 'app_exception_message_type_list' app_id message_type %}{% else %}{% url 'app_exception_message_list' app_id %}{% endif %}?page={{ prev_page }}&limit={{ limit }}">上一页</a>#}
{#        </button>#}
{#    {% else %}#}
{#        <button disabled>上一页</button>#}
{#    {% endif %}#}
{##}
    {# 下一页 #}
{#    {% if next_page > 0 %}#}
{#        <button>#}
{#            <a href="{% if message_type %}{% url 'app_exception_message_type_list' app_id message_type %}{% else %}{% url 'app_exception_message_list' app_id %}{% endif %}?page={{ next_page }}&limit={{ limit }}">下一页</a>#}
{#        </button>#}
{#    {% else %}#}
{#        <button disabled>下一页</button>#}
{#    {% endif %}#}
{#</p>#}

<div class="modal" id="previewModal">
    <div class="modal-content" id="preview-img">
    </div>
</div>

<script>
    const previewModal = document.getElementById('previewModal');
    const md_list = document.querySelectorAll('.markdown-body');
    md_list.forEach(md_item => {
        md_item.innerHTML = marked.parse(md_item.innerHTML);
    });

    $('.image-preview-show').on('click', function () {
        const src = $(this).find('img')[0].src

        $('#preview-img').html(`
            <img src="${src}"  alt=""/>
        `)
        previewModal.style.display = 'block';
    })

    // 点击模态框外部区域关闭模态框
    window.onclick = function (event) {
        if (event.target === previewModal) {
            previewModal.style.display = 'none';
        }
    };
</script>


<div id="loading" style="display: none;">加载中...</div>

<script>
    let currentPage = 1;
    let isLoading = false;
    let hasMore = true;
    
    // 初始加载第一页数据
    window.addEventListener('DOMContentLoaded', (event) => {
        currentPage = 1;
        setupInfiniteScroll();
    });
    
    function setupInfiniteScroll() {
        var dom = document.getElementById("table-container")
        dom.addEventListener('scroll', function() {
            console.log('xxxxxx')
            const { scrollTop, scrollHeight, clientHeight } = dom;
            
            // 当滚动到接近底部时加载更多
            if (scrollTop + clientHeight >= scrollHeight - 100 && !isLoading && hasMore) {
                loadMoreData();
            }
        });
    }
    
    function loadMoreData() {
        isLoading = true;
        document.getElementById('loading').style.display = 'block';
        
        currentPage++;
        
        const options = {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json',
            },
        };
        
        fetch(`?page=${currentPage}`, options)
        .then(response => response.json())
        .then(data => {
            if (data.message_list.length > 0) {
                const tableBody = document.getElementById('table-body');
                console.log(1111)
                console.log(data.message_list)
                
                // 添加新数据到表格
                data.message_list.forEach(msg => {
                    const row = document.createElement('tr');
                    
                    row.innerHTML = `
                        <td>${msg.user_id ? msg.user_id:''}</td>
                        <td>${msg.add_time ? msg.add_time:null}</td>
                    `
                    if ( !data.is_inner_app){
                        row.innerHTML += `
                            <td>${msg.app_name ? msg.app_name:''}</td>
                            <td>${msg.goods_name ? msg.goods_name: ''}</td>
                            <td>${msg.platform ? msg.platform: ''}</td>
                        `
                    }
                    if (msg.question_img){
                        row.innerHTML += `
                            <td>
                                <div class="message-content">${msg.query}</div>
                                <div class="image-preview-show"><img width="50" height="50" src="${msg.question_img}" alt=""></div>
                            </td>
                        `
                    }else {
                        row.innerHTML += `
                            <td>
                                <div class="message-content">${msg.query}</div>
                            </td>
                        `
                    }
                    row.innerHTML += `
                         <td>
                            <div class="markdown-body message-content">${msg.answer?marked.parse(msg.answer): ''}</div>
                        </td>
                        <td class="message-token">
                            <div>提问：${msg.message_tokens}</div>
                            <div>回答：${msg.answer_tokens}</div>
                        </td>
                    `
                    if (msg.is_exception){
                        let exception_reason = null;
                        let exception_image = null;
                        if (msg.exception_reason){
                            exception_reason = `<div>失败原因：${msg.exception_reason}</div>`
                        }
                        if (msg.exception_image){
                            exception_image = `
                            <div class="fail-image image-preview-show">
                                <img width="50" height="50" src="${msg.exception_image}" alt="">
                            </div>
                            `
                        }
                        if (exception_reason && exception_image){
                            row.innerHTML += `
                                <td>
                                     ${exception_reason}
                                     ${exception_image}
                                </td>
                            `
                        } else if (exception_reason && !exception_image) {
                            row.innerHTML += `
                                <td>
                                     ${exception_reason}
                                </td>
                            `
                        } else if (!exception_reason && exception_image) {
                            row.innerHTML += `
                                <td>
                                     ${exception_image}
                                </td>
                            `
                        }
                    }else{
                        row.innerHTML += '<td></td>'
                    }
                    row.innerHTML += `<td>${msg.sensitive_content}</td>`
                    
                    tableBody.appendChild(row);
                });
                
                hasMore = currentPage < data.next_page;
            } else {
                hasMore = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            currentPage--;  // 出错时回退页码
        })
        .finally(() => {
            isLoading = false;
            document.getElementById('loading').style.display = 'none';
        });
    }
</script>
</body>
</html>