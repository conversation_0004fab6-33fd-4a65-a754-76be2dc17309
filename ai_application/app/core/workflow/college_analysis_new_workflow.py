from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import CompletionAppGenerateEntity, ChatAppGenerateEntity
from app.core.features.college_analysis_new import CollegeAnalysisNewFeature
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError
from app.models import Message, HollandTestResult
import json


class CollegeAnalysisNewWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

    def run(self):
        student_info = self.application_generate_entity.query
        inputs = self.application_generate_entity.inputs
        stream = self.application_generate_entity.stream
        # holland_test_result = inputs.get('holland_test_result', '')
        # report_id = inputs.get('report_id')

        # if not report_id:
        #     raise ParameterError(detail='缺少 report_id')
        recommend_college_major_info = inputs.get('recommend_college_major_info',{})
        # 获取霍兰德测试信息
        # holland_test_record = HollandTestResult.objects.filter(genre=holland_test_result).first()
        # holland_test_data = holland_test_record.result if holland_test_record else '无'

        result = self._get_result(recommend_college_major_info, student_info,stream )

        self._publish_workflow_end_message()

        return result

    def _get_result(self, recommend_college_major_info:dict,student_info:str,stream: bool) -> LLMResult:
        self.college_analysis_new_feature = CollegeAnalysisNewFeature(app_no='college_analysis_new')
        recommend_college_major_info_str = json.dumps(recommend_college_major_info, ensure_ascii=False)

        result = self.college_analysis_new_feature.run(
            self.application_generate_entity,
            stream=stream,
            query=recommend_college_major_info_str,
            student_info= student_info
        )

        tracing_info = {
            'tracing_type': 'college_analysis_new',
            'query': recommend_college_major_info_str
        }

        return self._handle_node_response(result, stream, tracing_info=tracing_info)

class CollegeAnalysisNewWithGoalWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

    def run(self):
        student_info = self.application_generate_entity.query
        inputs = self.application_generate_entity.inputs
        stream = self.application_generate_entity.stream

        # report_id = inputs.get('report_id')
        #
        # if not report_id:
        #     raise ParameterError(detail='缺少 report_id')
        analysis_result = inputs.get('analysis_result',{})

        result = self._get_result(analysis_result, student_info,stream)

        self._publish_workflow_end_message()

        return result

    def _get_result(self, analysis_result:dict,student_info:str,stream: bool) -> LLMResult:
        self.college_analysis_new_with_goal_feature = CollegeAnalysisNewFeature(app_no='college_analysis_new_with_goal')
        analysis_result_str = json.dumps(analysis_result, ensure_ascii=False)

        result = self.college_analysis_new_with_goal_feature.run(
            self.application_generate_entity,
            stream=stream,
            query=analysis_result_str,
            student_info= student_info
        )

        tracing_info = {
            'tracing_type': 'college_analysis_new_with_goal',
            'query': analysis_result_str
        }
        return self._handle_node_response(result, stream, tracing_info=tracing_info)
