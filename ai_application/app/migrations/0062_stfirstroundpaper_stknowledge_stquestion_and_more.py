# Generated by Django 4.2 on 2025-08-13 14:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0061_citygdp_kaoyanqueryresult_majorinfo_majorrelation_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='STFirstRoundPaper',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.Char<PERSON><PERSON>(max_length=32, verbose_name='核心课code')),
                ('paper_content', models.JSONField(null=True, verbose_name='试卷内容')),
            ],
            options={
                'verbose_name': '第一轮试卷表',
                'verbose_name_plural': '第一轮试卷表',
                'db_table': 'ai_st_first_round_paper',
            },
        ),
        migrations.CreateModel(
            name='STKnowledge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('subject_name', models.CharField(max_length=32, verbose_name='学科名称')),
                ('core_course_name', models.CharField(max_length=32, verbose_name='核心课名称')),
                ('name', models.CharField(max_length=32, verbose_name='知识点名称')),
                ('definition', models.CharField(blank=True, max_length=255, verbose_name='知识点定义')),
                ('kg_qs_count', models.IntegerField(default=0, verbose_name='知识点下题目数量(选择+主观)')),
                ('related_video_url', models.CharField(blank=True, max_length=255, verbose_name='知识点视频链接')),
                ('percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='百分比')),
                ('is_first_round', models.BooleanField(default=False, verbose_name='是否第一轮知识点')),
            ],
            options={
                'verbose_name': '知识点库',
                'verbose_name_plural': '知识点库',
                'db_table': 'ai_st_knowledge',
            },
        ),
        migrations.CreateModel(
            name='STQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('subject_name', models.CharField(max_length=32, verbose_name='学科名称')),
                ('core_course_name', models.CharField(max_length=32, verbose_name='核心课名称')),
                ('question_intid', models.CharField(blank=True, db_index=True, max_length=16, verbose_name='题目id')),
                ('question_type', models.IntegerField(choices=[(0, '单选题'), (1, '多选题'), (2, '主观题'), (3, '材料题'), (4, '共享题干题'), (5, '共享选项题'), (6, '填空题')], default=0, verbose_name='题目类型')),
                ('question_content', models.TextField(null=True, verbose_name='题目内容')),
                ('difficulty', models.IntegerField(default=0, verbose_name='难度')),
                ('is_exam', models.BooleanField(default=False, verbose_name='是否真题')),
                ('is_unified', models.BooleanField(default=False, verbose_name='是否统考')),
                ('exam_year', models.IntegerField(default=0, verbose_name='考试年份')),
                ('exam_school', models.CharField(blank=True, max_length=32, verbose_name='考试学校')),
                ('analysis', models.TextField(null=True, verbose_name='解析')),
                ('format_question_content', models.JSONField(null=True, verbose_name='格式化题目内容')),
                ('knowledge_list', models.JSONField(null=True, verbose_name='知识点列表')),
                ('knowledge_count', models.IntegerField(default=0, verbose_name='知识点数量')),
            ],
            options={
                'verbose_name': '题目表',
                'verbose_name_plural': '题目表',
                'db_table': 'ai_st_question',
                'index_together': {('difficulty', 'question_type', 'core_course_code')},
            },
        ),
        migrations.CreateModel(
            name='STSubjectStageStrategy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('learning_stage', models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提升')], default='modi', max_length=32, verbose_name='阶段')),
                ('stage_question_strategy', models.JSONField(null=True, verbose_name='阶段出题策略')),
                ('stage_strengthen_question_strategy', models.JSONField(null=True, verbose_name='强化阶段出题策略')),
                ('stage_change_required', models.JSONField(null=True, verbose_name='阶段变更要求')),
            ],
            options={
                'verbose_name': '科目阶段策略要求',
                'verbose_name_plural': '科目阶段策略要求',
                'db_table': 'ai_st_subject_stage_strategy',
            },
        ),
        migrations.CreateModel(
            name='STUserPaper',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('question_ids', models.JSONField(null=True, verbose_name='试卷id')),
                ('is_answered', models.BooleanField(default=False, verbose_name='是否答过')),
                ('learning_stage', models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提升')], default='modi', max_length=32, verbose_name='阶段')),
                ('is_stage_strengthen', models.BooleanField(default=False, verbose_name='是否阶段强化')),
                ('stage_round', models.IntegerField(default=1, verbose_name='阶段轮数')),
                ('stage_total_rounds', models.IntegerField(default=1, verbose_name='阶段总轮数')),
                ('total_round', models.IntegerField(default=1, verbose_name='总轮数')),
                ('paper_score', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='试卷总分')),
            ],
            options={
                'verbose_name': '用户试卷',
                'verbose_name_plural': '用户试卷',
                'db_table': 'ai_st_user_paper',
            },
        ),
        migrations.CreateModel(
            name='STUserPaperAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('is_finished', models.BooleanField(default=False, verbose_name='是否完成')),
                ('finished_time', models.DateTimeField(null=True, verbose_name='完成时间')),
                ('answered_question_ids', models.JSONField(null=True, verbose_name='已答题的题目id列表')),
                ('answered_score', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='答题总分')),
                ('subjective_score', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='主观题得分')),
                ('report_status', models.CharField(default='not_start', max_length=16, verbose_name='答卷报告状态')),
                ('report_retry_count', models.IntegerField(default=0, verbose_name='重试次数')),
                ('report_params', models.JSONField(null=True, verbose_name='答卷报告参数')),
                ('report', models.TextField(null=True, verbose_name='答卷报告')),
                ('paper', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stuserpaper')),
            ],
            options={
                'verbose_name': '用户答卷',
                'verbose_name_plural': '用户答卷',
                'db_table': 'ai_st_user_paper_answer',
            },
        ),
        migrations.CreateModel(
            name='STUserPaperDistribution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('paper_distribution', models.JSONField(null=True, verbose_name='组卷规则')),
                ('is_selected', models.BooleanField(default=False, verbose_name='是否选中')),
                ('selected_questions', models.JSONField(null=True, verbose_name='选中的题目')),
            ],
            options={
                'verbose_name': '用户组卷规则',
                'verbose_name_plural': '用户组卷规则',
                'db_table': 'ai_st_user_paper_distribution',
            },
        ),
        migrations.CreateModel(
            name='STUserRoundAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('examined_rounds', models.IntegerField(default=0, verbose_name='已考察轮次')),
                ('total_assessment_score', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='总评估得分')),
                ('total_subjective_score', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='主观题得分')),
                ('learning_stage', models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提升')], default='modi', max_length=32, verbose_name='答题阶段')),
                ('stage_examined_rounds', models.IntegerField(default=0, verbose_name='阶段已考察轮次')),
                ('stage_total_rounds', models.IntegerField(default=1, verbose_name='阶段总轮数')),
                ('stage_change_type', models.CharField(choices=[('up', '升级'), ('down', '降级'), ('strengthen', '阶段强化')], default='up', max_length=16, verbose_name='阶段变更类型')),
                ('is_stage_strengthen', models.BooleanField(default=False, verbose_name='是否阶段强化')),
                ('strengthen_pass_rounds', models.IntegerField(default=0, verbose_name='强化已通过轮次')),
                ('is_test_stop', models.BooleanField(default=False, verbose_name='是否停止测试')),
                ('stop_reason', models.IntegerField(default=0, verbose_name='停止原因')),
                ('strengthen_fail_rounds', models.IntegerField(default=0, verbose_name='强化连续失败轮次')),
            ],
            options={
                'verbose_name': '用户答题轮次分析表',
                'verbose_name_plural': '用户答题轮次分析表',
                'db_table': 'ai_st_user_round_analysis',
            },
        ),
        migrations.AddField(
            model_name='conversation',
            name='scene_info',
            field=models.JSONField(null=True, verbose_name='业务场景'),
        ),
        migrations.AlterField(
            model_name='message',
            name='malicious_attack_type',
            field=models.IntegerField(blank=True, choices=[(0, '0'), (1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5'), (6, '6')], null=True, verbose_name='恶意攻击类型'),
        ),
        migrations.CreateModel(
            name='STUserRoundChangeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('old_stage', models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提升')], max_length=32, verbose_name='旧阶段')),
                ('new_stage', models.CharField(choices=[('modi', '摸底测试'), ('basic', '基础巩固'), ('basic_improve_l', '基础提升_初级'), ('basic_improve_m', '基础提升_中级'), ('basic_improve_h', '基础提升_高级'), ('core_basic', '核心基础'), ('core_improve_l', '核心提升_初级'), ('core_improve_m', '核心提升_中级'), ('core_improve_h', '核心提升_高级'), ('core_improve', '核心提升')], max_length=32, verbose_name='新阶段')),
                ('change_type', models.CharField(choices=[('up', '升级'), ('down', '降级'), ('strengthen', '阶段强化')], max_length=16, verbose_name='变更类型')),
                ('change_report', models.TextField(null=True, verbose_name='变更报告')),
                ('answer', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stuserpaperanswer')),
            ],
            options={
                'verbose_name': '阶段变更记录',
                'verbose_name_plural': '阶段变更记录',
                'db_table': 'ai_st_user_round_change_record',
            },
        ),
        migrations.CreateModel(
            name='STUserPaperQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('score', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='分数')),
                ('paper', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stuserpaper')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stquestion')),
            ],
            options={
                'verbose_name': '用户试卷问题',
                'verbose_name_plural': '用户试卷问题',
                'db_table': 'ai_st_user_paper_question',
            },
        ),
        migrations.CreateModel(
            name='STUserKnowledgeMasteryLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('answer_count', models.IntegerField(default=0, verbose_name='答题次数')),
                ('right_count', models.IntegerField(default=0, verbose_name='正确次数')),
                ('wrong_count', models.IntegerField(default=0, verbose_name='错误次数')),
                ('accuracy', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='正确率')),
                ('total_accuracy', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='总正确率(做对次数/该知识点总题数)')),
                ('last_answer_status', models.CharField(blank=True, max_length=16, verbose_name='上次答题状态')),
                ('last_answer_time', models.DateTimeField(null=True, verbose_name='上次答题时间')),
                ('occr_round', models.IntegerField(default=0, verbose_name='出现轮数')),
                ('consecutive_right_round', models.IntegerField(default=0, verbose_name='连续答对轮数')),
                ('consecutive_wrong_round', models.IntegerField(default=0, verbose_name='连续答错轮数')),
                ('waiting_round', models.IntegerField(default=0, verbose_name='等待轮数')),
                ('knowledge', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stknowledge')),
            ],
            options={
                'verbose_name': '用户知识点掌握情况',
                'verbose_name_plural': '用户知识点掌握情况',
                'db_table': 'ai_st_user_knowledge_mastery_level',
            },
        ),
        migrations.CreateModel(
            name='STUserKnowledgeDistribution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('question_type', models.IntegerField(default=0, verbose_name='题目类型')),
                ('difficulty', models.IntegerField(default=0, verbose_name='难度')),
                ('answer_count', models.IntegerField(default=0, verbose_name='答题次数')),
                ('right_count', models.IntegerField(default=0, verbose_name='正确次数')),
                ('wrong_count', models.IntegerField(default=0, verbose_name='错误次数')),
                ('accuracy', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='正确率')),
                ('knowledge', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stknowledge')),
            ],
            options={
                'verbose_name': '用户知识点答题分布情况',
                'verbose_name_plural': '用户知识点答题分布情况',
                'db_table': 'ai_st_user_knowledge_distribution',
            },
        ),
        migrations.CreateModel(
            name='STUserAssessmentDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('assessment_type', models.CharField(choices=[('baseline', '摸底'), ('regular', '常规')], max_length=16, verbose_name='评估类型')),
                ('assessment_round', models.IntegerField(default=0, verbose_name='评估轮次')),
                ('assessment_score', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='本轮评估得分')),
                ('subjective_score', models.DecimalField(decimal_places=2, max_digits=5, null=True, verbose_name='主观题得分')),
                ('answer', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stuserpaperanswer')),
                ('paper', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stuserpaper')),
            ],
            options={
                'verbose_name': '用户评估明细表',
                'verbose_name_plural': '用户评估明细表',
                'db_table': 'ai_st_user_assessment_detail',
            },
        ),
        migrations.CreateModel(
            name='STQuestionKnowledge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('knowledge', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stknowledge')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stquestion')),
            ],
            options={
                'verbose_name': '题目知识点关联',
                'verbose_name_plural': '题目知识点关联',
                'db_table': 'ai_st_question_knowledge',
            },
        ),
        migrations.CreateModel(
            name='STKnowledgeRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('knowledge', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='knowledges', to='app.stknowledge')),
                ('pre_knowledge', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='pre_knowledges', to='app.stknowledge')),
            ],
            options={
                'verbose_name': '知识点关联关系',
                'verbose_name_plural': '知识点关联关系',
                'db_table': 'ai_st_knowledge_relation',
            },
        ),
        migrations.CreateModel(
            name='STUserPaperQuestionAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject_id', models.CharField(max_length=32, verbose_name='学科id')),
                ('core_course_code', models.CharField(max_length=32, verbose_name='核心课code')),
                ('user_id', models.CharField(db_index=True, max_length=32, verbose_name='用户id')),
                ('answer_status', models.CharField(blank=True, max_length=16, verbose_name='答题状态')),
                ('choice_answer', models.JSONField(null=True, verbose_name='选择题答案')),
                ('subjective_answer', models.JSONField(null=True, verbose_name='主观题答案')),
                ('image_text', models.TextField(null=True, verbose_name='图片文字')),
                ('score_rate', models.IntegerField(default=0, verbose_name='得分率')),
                ('answer_score', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='答题得分')),
                ('report_status', models.CharField(default='not_start', max_length=16, verbose_name='报告状态')),
                ('report_retry_count', models.IntegerField(default=0, verbose_name='重试次数')),
                ('report', models.TextField(null=True, verbose_name='答题报告')),
                ('answer', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stuserpaperanswer')),
                ('paper', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stuserpaper')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.stquestion')),
            ],
            options={
                'verbose_name': '用户答卷问题详情',
                'verbose_name_plural': '用户答卷问题详情',
                'db_table': 'ai_st_user_paper_question_answer',
                'index_together': {('user_id', 'core_course_code')},
            },
        ),
    ]
