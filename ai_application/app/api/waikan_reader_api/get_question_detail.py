import requests

def get_question_detail():

    url = "http://127.0.0.1:8101/api/v1/english/waikan_question_detail"

    payload = {}
    headers = {
        "x-api-key": "ak_f288a20832ab417d",
        "x-signature": "91e9b4e95b65e5fc14013baeb7e5bfe252b1db58071c01b5b19b889fe85ddfdb",
        "timestamp": "1720405843",
        "nonce": "ld8h259m",
        "x-sign-debug": "1"
    }

    response = requests.request("GET", url, headers=headers, data=payload)
    print(response)
    if response.status_code == 200:
        try:

            response_data = response.json()

            data = response_data.get('data')
            if data:
                return data
            else:
                print("Response does not contain 'data' field.")
                return None
        except ValueError as e:
            print(f"Failed to parse JSON response: {e}")
            return None
    else:
        print(f"Request failed with status code: {response.status_code}")
        print(f"Response text: {response.text}")
        return None


