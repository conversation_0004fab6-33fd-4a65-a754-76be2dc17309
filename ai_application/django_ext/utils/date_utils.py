import datetime

from django.utils import timezone


def utc2local(utc_time: datetime.datetime):
    return utc_time.astimezone(timezone.get_current_timezone())


def local2utc(local_time: datetime.datetime):
    return local_time.astimezone(datetime.timezone.utc)


def utc2str(utc_time: datetime.datetime, f: str = '%Y-%m-%d %H:%M:%S'):
    return utc2local(utc_time).strftime(f)


def str2local(s: str, f: str = '%Y-%m-%d %H:%M:%S'):
    return datetime.datetime.strptime(s, f).astimezone(timezone.get_current_timezone())


def str2utc(s: str, f: str = '%Y-%m-%d %H:%M:%S'):
    return local2utc(str2local(s, f))


def local_now():
    return utc2local(timezone.now())


def now_str(f: str = '%Y-%m-%d %H:%M:%S'):
    return utc2str(timezone.now(), f)
