from app.constants.app import AppMode
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.model_manager import ModelInstance
from app.core.prompt.entities import PromptParser
from app.core.prompt.prompt_templates import query_determine
from app.core.prompt.simple_prompt_transform import SimplePromptTransform
from app.core.prompt.utils.prompt_message_util import PromptMessageUtil


class QueryDetermineFeature:

    def determine(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ) -> bool:
        prompt_parser = PromptParser(
            prompt_template=query_determine.PROMPT,
            input_variables={
                'question': query,
            },
            output_parser=query_determine.output_parser,
        )

        prompt_messages = SimplePromptTransform().get_prompt(
            app_mode=AppMode.COMPLETION,
            pre_prompt=prompt_parser.get_prompt(),
            query=''
        )

        model_instance = ModelInstance(
            provider_model_bundle=application_generate_entity.model_conf.provider_model_bundle,
            model=application_generate_entity.model_conf.model
        )

        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            model_parameters=application_generate_entity.model_conf.model_params,
            stream=False
        )

        if tracing_log:
            log_content = PromptMessageUtil.prompt_messages_to_prompt_for_saving(invoke_result.prompt_messages)
            tracing_log.add_tracing(
                tracing_type='query_determine',
                query=query,
                content=log_content,
                answer=invoke_result.message.content,
                message_tokens=invoke_result.usage.prompt_tokens,
                answer_tokens=invoke_result.usage.completion_tokens,
                total_tokens=invoke_result.usage.total_tokens,
                latency=invoke_result.usage.latency,
            )

        res_dict = prompt_parser.parse_answer(invoke_result.message.content)
        return res_dict.get('result') == '1'
