from django.db import models
from ._base import BaseModel

class GraduateMajorInfo(BaseModel):

    # 研究生一级学科
    graduate_first_discipline = models.CharField(max_length=255, verbose_name='研究生一级学科', null=True, blank=True)

    # 研究生二级门类代码
    graduate_second_category_code = models.CharField(max_length=10, verbose_name='研究生二级门类代码', null=True,
                                                     blank=True)

    # 研究生二级门类
    graduate_second_category = models.CharField(max_length=255, verbose_name='研究生二级门类', null=True, blank=True)

    # 研究生三级专业代码
    graduate_third_major_code = models.CharField(max_length=10, verbose_name='研究生三级专业代码', null=True,
                                                 blank=True)

    # 研究生三级专业
    graduate_third_major = models.CharField(max_length=255, verbose_name='研究生三级专业', null=True, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '研究生专业信息'
        db_table = 'ai_graduate_major_info'

class GraduateCollegeInfo(BaseModel):

    # 院校代码
    code = models.CharField(max_length=10, verbose_name='院校代码', null=True, blank=True)

    # 院校名称
    name = models.CharField(max_length=255, verbose_name='院校名称', null=True, blank=True)

    # 主管部门
    department = models.CharField(max_length=255, verbose_name='主管部门', null=True, blank=True)

    # 院校省份
    province = models.CharField(max_length=255, verbose_name='院校省份', null=True, blank=True)

    #院校城市
    city = models.CharField(max_length=255, verbose_name='院校城市', null=True, blank=True)

    # 院校层次
    level = models.CharField(max_length=255, verbose_name='院校层次', null=True, blank=True)

    # 院校性质
    type = models.CharField(max_length=255, verbose_name='院校性质', null=True, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '研究生院校信息'
        db_table = 'ai_graduate_college_info'