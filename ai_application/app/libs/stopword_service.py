import os


class StopWordService:

    def __init__(self):
        self.stopwords = self._init_stopwords()

    def _init_stopwords(self) -> list:
        current_path = os.path.abspath(__file__)
        words_path = os.path.join(os.path.dirname(current_path), 'stopwords_cn.txt')

        if not os.path.exists(words_path):
            raise FileNotFoundError(f"Failed to load stopwords file {words_path}: file not found")

        all_words = []
        with open(words_path, 'r', encoding='utf-8') as f:
            for line in f:
                w = line.strip()
                if w:
                    all_words.append(w)
        return all_words

    def filter_stopwords(self, words: list) -> list:
        return [w for w in words if w not in self.stopwords]


stopword_service = StopWordService()
