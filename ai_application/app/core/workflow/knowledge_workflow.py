import random
import uuid

from django.conf import settings

from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.features.knowledge_query_db import KnowledgeQueryDBFeature
from app.core.features.knowledge_query import KnowledgeQueryFactory
from app.core.features.knowledge_query_split import KnowledgeQuerySplitFeature
from app.core.features.knowledge_question import KnowledgeQuestionFeature
from app.core.features.video_keyword_query import VideoKeywordQueryFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import KnowledgeNotResultError
from app.models import Message, Knowledge


class KnowledgeWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager,
    ):
        super().__init__(application_generate_entity, message, queue_manager)
        self.is_deep = self.inputs.get('search_mode') == 'deep'
        self.enable_recommend_video = self.inputs.get('enable_recommend_video', False)

    def run(self):
        query = self.application_generate_entity.query
        if not query:
            raise KnowledgeNotResultError(detail=settings.KNOWLEDGE_SEARCH_NO_RESULT)

        is_regenerate = self.inputs.get('is_regenerate')
        is_recommend = self.inputs.get('is_recommend')

        # 如果是重新生成，则不需拆分知识点
        if is_regenerate or is_recommend:
            split_keywords = [query]
        else:
            if Knowledge.objects.filter(is_deleted=False, name=query).exists():
                split_keywords = [query]
            else:
                # 拆分用户搜索内容，提取知识点
                split_keywords = KnowledgeQuerySplitFeature().run(
                    application_generate_entity=self.application_generate_entity,
                    query=query,
                    tracing_log=self.tracing_log
                )
                # 处理知识点顺序
                split_keywords = self._sort_keywords(split_keywords, self.application_generate_entity.query)
                # 知识点最多获取3个
                split_keywords = split_keywords[:3]

        # 搜索关键词，得出已存储在数据库的知识点信息和没有存储的知识点名称列表
        kg_list = self._query_knowledge_by_keywords(query, split_keywords, is_recommend=is_recommend)
        # 优化一下搜索逻辑，如果检索出来的知识点包含在用户的输入中，则当作优先知识点展示

        if not kg_list:
            raise KnowledgeNotResultError(detail=settings.KNOWLEDGE_SEARCH_NO_RESULT)

        self._query_knowledge_desc(kg_list)

    def _sort_keywords(self, keywords: list, query: str) -> list:
        exist_keywords_map = {}
        exist_keywords = []
        for k in keywords:
            k_idx = query.find(k)
            if k_idx > -1:
                exist_keywords_map[k] = k_idx
                exist_keywords.append(k)
            else:
                # 知识点不在问题内则不处理
                continue
        sorted_keywords = sorted(exist_keywords, key=lambda x: exist_keywords_map[x])
        return sorted_keywords

    def _query_knowledge_by_keywords(self, query: str, split_keywords: list, is_recommend: bool = False):
        if not split_keywords:
            return []

        search_type = self.inputs.get('search_type')
        document_no = self.inputs.get('document_no')
        exist_kgs_map = {}
        if search_type == 'local' and document_no:
            kg_results = KnowledgeQueryDBFeature().run(
                query=query,
                keywords=split_keywords,
                original_knowledge_document_no=document_no,
                is_recommend=is_recommend,
                tracing_log=self.tracing_log
            )
        else:
            kg_results = [{
                'name': k,
                'definition': '',
                'others': [],
            } for k in split_keywords]

        return kg_results

    def _query_knowledge_desc(self, kg_list: list):
        kg_query_factory = KnowledgeQueryFactory(self.is_deep)

        for kg in kg_list:
            kg_name = kg.get('name')
            definition = kg.get('definition', '')
            others = kg.get('others', [])
            kg_hash = uuid.uuid4().hex

            kg_query_feature = kg_query_factory.get_knowledge_query(
                application_generate_entity=self.application_generate_entity,
                knowledge=kg_name,
                definition=definition,
            )
            kg_generator = kg_query_feature.run()

            # 输出知识点详情
            # pos=body时，type为空
            self._publish_workflow_custom_message(' ', workflow_chunk={
                'id': kg_hash, 'pos': 'head', 'type': '',
            })
            # 填入知识点名称
            self._publish_workflow_custom_message(kg_name, workflow_chunk={
                'id': kg_hash, 'pos': 'body', 'type': 'title',
            })
            # 填入定义
            if definition:
                self._publish_workflow_custom_message(definition, workflow_chunk={
                    'id': kg_hash, 'pos': 'body', 'type': 'definition',
                })

            if kg_query_feature.is_llm_generate:
                if definition:
                    tracing_type = 'knowledge_deep_query_local' if self.is_deep else 'knowledge_query_local'
                else:
                    tracing_type = 'knowledge_deep_query_llm' if self.is_deep else 'knowledge_query_llm'
                node_result = self._handle_node_generate_response(
                    generator=kg_generator,
                    workflow_chunk={
                        'id': kg_hash, 'pos': 'body', 'type': 'detail',
                    }, tracing_info={
                        'tracing_type': tracing_type,
                        'query': kg_name,
                    }
                )
                node_answer = node_result.message.content
                completion_tokens = node_result.usage.completion_tokens
                kg_query_feature.check_desc_usable(completion_tokens)
                if node_answer:
                    kg_query_feature.update_knowledge_store(node_answer)
            else:
                for kg_detail in kg_generator:
                    self._publish_workflow_custom_message(kg_detail, workflow_chunk={
                        'id': kg_hash, 'pos': 'body', 'type': 'detail',
                    })

            # 深度需要返回题目信息
            if self.is_deep:
                # 查询题目
                question_feature = KnowledgeQuestionFeature()
                questions = question_feature.query_questions_by_knowledge(kg_name, tracing_log=self.tracing_log)
                if questions:
                    for idx, q in enumerate(questions, start=1):
                        question_generator = question_feature.analysis_question(
                            application_generate_entity=self.application_generate_entity,
                            question=q
                        )
                        self._publish_workflow_custom_message(
                            text=f'\n## 问题{idx}\n\n{q["question"]}\n\n',
                            workflow_chunk={
                                'id': kg_hash, 'pos': 'body', 'type': 'detail',
                            }
                        )
                        self._handle_node_generate_response(
                            generator=question_generator,
                            workflow_chunk={
                                'id': kg_hash, 'pos': 'body', 'type': 'detail',
                            },
                            tracing_info={
                                'tracing_type': 'knowledge_deep_question',
                                'query': q["question"],
                            }
                        )
                else:
                    question_generator = question_feature.analysis_question_by_knowledge(
                        application_generate_entity=self.application_generate_entity,
                        knowledge=kg_name
                    )
                    self._handle_node_generate_response(
                        generator=question_generator,
                        workflow_chunk={
                            'id': kg_hash, 'pos': 'body', 'type': 'detail',
                        },
                        tracing_info={
                            'tracing_type': 'knowledge_deep_no_question',
                            'query': kg_name,
                        }
                    )

            # 通用模式记录下推荐知识点信息
            if not self.is_deep:
                for other_kg_name in others:
                    self._publish_workflow_custom_message(other_kg_name, workflow_chunk={
                        'id': kg_hash, 'pos': 'body', 'type': 'recommend',
                    })

            # 消息是否可用：token是否超出
            is_usable_str = '1' if kg_query_feature.is_usable else '0'
            self._publish_workflow_custom_message(is_usable_str, workflow_chunk={
                'id': kg_hash, 'pos': 'body', 'type': 'is_usable',
            })
            # 如果是模型生成，则可以重新生成
            regeneration_str = '1' if kg_query_feature.is_llm_generate else '0'
            self._publish_workflow_custom_message(regeneration_str, workflow_chunk={
                'id': kg_hash, 'pos': 'body', 'type': 'regeneration',
            })

            # 知识点尾部信息
            # video keyword
            if self.enable_recommend_video:
                document_no_list = VideoKeywordQueryFeature().run(kg_name)
                random.shuffle(document_no_list)    # random
                content_text = '\n'.join(document_no_list)
                self._publish_workflow_custom_message(
                    text=content_text,
                    workflow_chunk={'id': kg_hash, 'pos': 'body', 'type': 'video'}
                )
                self.tracing_log.add_tracing(
                    tracing_type='knowledge_question_video',
                    query=kg_name,
                    answer=content_text,
                )

            self._publish_workflow_custom_message(' ', workflow_chunk={
                'id': kg_hash, 'pos': 'tail', 'type': '',
            })

        self._publish_workflow_end_message()




