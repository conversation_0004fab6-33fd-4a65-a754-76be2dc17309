import json

from api_client.examination.client import examination_client
from app.services.question_service import QuestionService


class NewQuestionService:

    @classmethod
    def get_question_detail(cls, question_id: str):
        res = examination_client.search_questions(params={'num': question_id}, size=1)
        origin_questions = res.get('results')
        origin_question = origin_questions[0] if origin_questions else None

        new_questions = cls.format_questions([question_id])
        new_question_info = new_questions[0]

        return {
            'q_id': str(question_id),
            'q_type': origin_question['classification']['template'] if origin_question else None,
            'difficulty': origin_question['difficulty'] if origin_question else None,
            'question': new_question_info,
        }

    @classmethod
    def format_questions(cls, question_ids):
        formatted_questions = QuestionService.get_question_by_ids(question_ids)
        if not formatted_questions:
            raise ValueError('question_id is not found')

        formatted_questions_map = {str(q['q_id']): q for q in formatted_questions}

        new_questions = []
        for question_id in question_ids:
            formatted_question = formatted_questions_map.get(str(question_id))
            if not formatted_question:
                continue

            if formatted_question['q_type'] in [0, 1, 2, 6]:
                new_question_info = cls._rebuild_single_question(formatted_question)
            else:
                new_question_info = cls._rebuild_complex_question(formatted_question)
            new_questions.append(new_question_info)
        return new_questions

    @classmethod
    def _rebuild_single_question(cls, q_item):
        data = {
            'q_id': q_item['q_id'],
            'question': '',
            'right_answer': '',
            'analysis': q_item['rich_analysis'],
            'sub_questions': [],
        }
        if not data['analysis']:
            data['analysis'] = q_item['analysis']

        if q_item['q_type'] in [0, 1]:
            title = q_item['title']
            choice_body = '\n'.join(q_item['choices'])
            data['question'] = f'{title}\n{choice_body}'
            data['right_answer'] = '\n'.join(q_item['choice_right_answer'])
        elif q_item['q_type'] == 2:
            data['question'] = q_item['title']
            data['right_answer'] = q_item['subjective_right_answer']
        elif q_item['q_type'] == 6:
            data['question'] = q_item['title']
            # TODO 填空题需要重新处理
            data['right_answer'] = json.dumps(q_item['fill_right_answer'])

        return data

    @classmethod
    def _rebuild_complex_question(cls, q_item):
        sub_questions = q_item['sub_questions']
        new_sub_questions = []
        for s_q in sub_questions:
            s_q['q_id'] = s_q['sub_q_id']
            new_sub_questions.append(cls._rebuild_single_question(s_q))

        return {
            'question': q_item['title'],
            'sub_questions': new_sub_questions,
        }

