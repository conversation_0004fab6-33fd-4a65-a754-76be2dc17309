import uuid
from collections import defaultdict
from pathlib import Path

from django.conf import settings
from django.db import transaction
from django.utils import timezone

from app.api.dto import DatasetSetDocumentsDto, DatasetCreateDto, DatasetSetDocumentDto, DatasetAddSubtitleDocumentDto, \
    DatasetAddKnowledgeDocumentDto
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.callback_handler.index_tool_callback_handler import DatasetIndexToolCallbackHandler
from app.core.rag.extractor.entities.datasource_type import DatasourceType
from app.core.rag.index_processor.constant.index_type import IndexType
from app.core.rag.retrieval.dataset_retrieval import DatasetRetrieval
from app.errors import DatasetFileNumExceedError, DatasetFileTypeNotAllowError
from app.libs.redis import redis_default_client
from app.models import Account, Dataset, DatasetProcessRule, DatasetDocument, CourseVideoContent, Message
from app.tasks.clean_dataset_task import clean_dataset_task
from app.tasks.clean_document_task import clean_document_task
from app.tasks.document_indexing_task import document_indexing_task


class DocumentService:
    @classmethod
    def create_dataset(cls, dto: DatasetCreateDto, account: Account) -> tuple:
        dataset = cls.create_empty_dataset(dto.name, account)
        documents = cls.add_documents(dataset, dto.documents)
        return dataset, documents

    @classmethod
    def add_documents(cls, dataset: Dataset, documents: list[DatasetSetDocumentDto]) -> list:
        if not documents:
            return []

        new_documents = []
        for d in documents:
            new_documents.append({'name': d.name, 'url': d.url})

        data_source = {
            'type': DatasourceType.REMOTE_FILE.value,
            'info_list': {
                'file_info_list': new_documents
            }
        }
        document_data = {'data_source': data_source}
        return cls.add_document_with_dataset(
            dataset=dataset,
            document_data=document_data,
        )

    @classmethod
    def add_knowledge_document(cls, dataset: Dataset, dto: DatasetAddKnowledgeDocumentDto):
        data_source = {
            'type': DatasourceType.REMOTE_FILE.value,
            'info_list': {
                'file_info_list': [
                    {'name': dto.name, 'url': dto.url}
                ]
            }
        }
        document_data = {'data_source': data_source}
        res = cls.add_document_with_dataset(dataset, document_data, IndexType.KNOWLEDGE_INDEX.value)
        return res[0]['document_no'] if res else ''

    @classmethod
    def add_subtitle_document(cls, dataset: Dataset, dto: DatasetAddSubtitleDocumentDto) -> str:
        data_source = {
            'type': DatasourceType.COURSE_VIDEO_FILE.value,
            'info_list': {
                'file_info_list': [
                    {'name': dto.name, 'content': dto.content}
                ]
            }
        }
        document_data = {'data_source': data_source}
        res = cls.add_document_with_dataset(dataset, document_data)
        return res[0]['document_no'] if res else ''

    @classmethod
    def create_empty_dataset(cls, name: str, account: Account) -> Dataset:
        with transaction.atomic():
            dataset = Dataset.objects.create(
                account=account,
                dataset_no=uuid.uuid4(),
                name=name,
                embedding_model_provider=settings.EMBEDDING_MODEL_PROVIDER,
                embedding_model=settings.EMBEDDING_MODEL
            )
            DatasetProcessRule.objects.create(
                dataset=dataset,
                rules=DatasetProcessRule.DEFAULT_RULES
            )
        return dataset

    @classmethod
    def save_document_with_dataset(
            cls,
            dataset: Dataset,
            dto: DatasetSetDocumentsDto,
            account: Account
    ):
        # 判断上传文件限制
        count = len(dto.documents)
        if count > settings.RAG_MAX_FILE_NUM:
            raise DatasetFileNumExceedError()
        # 判断文件格式
        for i in dto.documents:
            if i.file_extension not in settings.RAG_ALLOW_FILE_TYPES:
                raise DatasetFileTypeNotAllowError()

        old_document_nos = list(dataset.datasetdocument_set.filter(
            is_deleted=False).values_list('document_no', flat=True))

        new_documents = []
        exist_document_nos = []
        for d in dto.documents:
            if not d.document_no:
                # 校验 name 和 url 是否为空
                new_documents.append({'name': d.name, 'url': d.url})
            else:
                exist_document_nos.append(d.document_no)

        del_document_nos = set(old_document_nos) - set(exist_document_nos)

        if del_document_nos:
            # 删除文档
            cls.delete_document(dataset, list(del_document_nos))

        if new_documents:
            data_source = {
                'type': DatasourceType.REMOTE_FILE.value,
                'info_list': {
                    'file_info_list': new_documents
                }
            }
            document_data = {'data_source': data_source}
            cls.add_document_with_dataset(
                dataset=dataset,
                document_data=document_data,
            )

    @classmethod
    def add_document_with_dataset(
            cls,
            dataset: Dataset,
            document_data: dict,
            index_type=IndexType.PARAGRAPH_INDEX.value
    ) -> list:
        doc_language = 'Chinese'
        data_source_type = document_data["data_source"]["type"]

        documents = []
        document_nos = []
        returned_documents = []
        if document_data["data_source"]["type"] == DatasourceType.REMOTE_FILE.value:
            file_list = document_data["data_source"]["info_list"]['file_info_list']
            for file_info in file_list:
                data_source_info = {
                    'name': file_info['name'],
                    'url': file_info['url'],
                }
                document = cls.build_document(
                    dataset=dataset,
                    name=file_info['name'],
                    data_source_type=data_source_type,
                    document_language=doc_language,
                    data_source_info=data_source_info,
                )
                # 兼容旧逻辑，源文档编号和知识文档编号一致
                document.original_knowledge_document_no = document.document_no
                document.index_type = index_type

                documents.append(document)
                document_nos.append(document.document_no)
                returned_documents.append({
                    'document_no': document.document_no,
                    'name': file_info['name'],
                    'url': file_info['url'],
                })
            if documents:
                DatasetDocument.objects.bulk_create(documents)
                document_indexing_task.delay(dataset.dataset_no, document_nos)
            return returned_documents
        if document_data["data_source"]["type"] == DatasourceType.COURSE_VIDEO_FILE.value:
            # 目前只处理一个文件
            file_info = document_data["data_source"]["info_list"]['file_info_list'][0]
            video_content = CourseVideoContent.objects.create(
                dataset=dataset,
                name=file_info['name'],
                content=file_info['content'],
            )
            data_source_info = {'video_content_id': video_content.id}
            document = cls.build_document(
                dataset=dataset,
                name=file_info['name'],
                data_source_type=data_source_type,
                document_language=doc_language,
                data_source_info=data_source_info,
            )
            document.index_type = IndexType.SUBTITLE_INDEX.value
            document.save()
            document_nos = [document.document_no]
            document_indexing_task.delay(dataset.dataset_no, document_nos)
            return [{
                'document_no': document.document_no,
                'name': file_info['name'],
                'video_content_id': video_content.id,
            }]

    @classmethod
    def delete_document(cls, dataset: Dataset, del_document_nos: list[str]):
        for d_no in del_document_nos:
            indexing_cache_key = 'document_{}_is_deleted'.format(d_no)
            redis_default_client.setex(indexing_cache_key, 600, '1')

        clean_document_task.delay(
            dataset.dataset_no,
            del_document_nos,
            index_type=IndexType.PARAGRAPH_INDEX.value
        )
        DatasetDocument.objects.filter(
            is_deleted=False, dataset=dataset,
            document_no__in=del_document_nos
        ).update(
            is_deleted=True,
            modified_time=timezone.now()
        )

    @classmethod
    def build_document(
            cls,
            dataset: Dataset,
            name: str,
            data_source_type: str,
            document_language: str,
            data_source_info: dict
    ) -> DatasetDocument:
        process_rule = dataset.latest_process_rule

        document = DatasetDocument(
            document_no=str(uuid.uuid4()),
            dataset=dataset,
            dataset_process_rule=process_rule,
            data_source_type=data_source_type,
            data_source_info=data_source_info,
            name=name,
            indexing_status='waiting',
            doc_language=document_language
        )
        return document

    @classmethod
    def get_datasets_documents(cls, dataset_nos: list):
        if not dataset_nos:
            return []

        datasets = list(Dataset.objects.filter(
            is_deleted=False, dataset_no__in=dataset_nos).values('id', 'dataset_no'))
        if not datasets:
            return []

        dataset_no_map = {d['id']: d['dataset_no'] for d in datasets}

        documents = DatasetDocument.objects.filter(
            is_deleted=False, dataset_id__in=dataset_no_map.keys()
        )

        d_map = defaultdict(list)
        for d in documents:
            d_map[d.dataset_id].append({
                'document_no': d.document_no,
                'name': d.name,
                'url': d.data_source_info['url'],
                'status': d.cal_status,
            })

        data = []
        for d_id, docs in d_map.items():
            data.append({
                'dataset_no': dataset_no_map[d_id],
                'documents': docs
            })
        return data

    @classmethod
    def delete_dataset(cls, dataset: Dataset):
        clean_dataset_task.delay(dataset.dataset_no, IndexType.PARAGRAPH_INDEX.value)
        dataset.is_deleted = False
        dataset.save(update_fields=['is_deleted'])

    @classmethod
    def create_chat2_empty_dataset(cls, ):
        dataset_no = settings.CHAT2_DATASET

        account = Account.objects.first()
        with transaction.atomic():
            dataset = Dataset.objects.create(
                account=account,
                dataset_no=dataset_no,
                name='知舟问答外挂资料',
                embedding_model_provider=settings.EMBEDDING_MODEL_PROVIDER,
                embedding_model='text-embedding-v3'
            )
            DatasetProcessRule.objects.create(
                dataset=dataset,
                rules=DatasetProcessRule.DEFAULT_RULES
            )
        return dataset

    @classmethod
    def add_chat2_document(cls, file_name: str, file_url: str):
        dataset_no = settings.CHAT2_DATASET
        dataset = Dataset.objects.get(dataset_no=dataset_no)

        data_source = {
            'type': DatasourceType.REMOTE_FILE.value,
            'info_list': {
                'file_info_list': [
                    {'name': file_name, 'url': file_url}
                ]
            }
        }

        document_data = {'data_source': data_source}
        res = cls.add_document_with_dataset(dataset, document_data, IndexType.CHAT_DOC_INDEX.value)
        return res[0]['document_no'] if res else ''

    @classmethod
    def query_chat2_document(cls, query: str, message: Message):
        dataset_no = settings.CHAT2_DATASET
        dataset = Dataset.objects.get(dataset_no=dataset_no)

        # get context from datasets
        hit_callback = DatasetIndexToolCallbackHandler(message=message)
        tracing_log = MessageTracingLog(message)

        dataset_retrieval = DatasetRetrieval()

        # 只需要获取已经完成解析的文档
        query_documents = dataset.datasetdocument_set.filter(
            is_deleted=False,
            indexing_status='completed'
        )

        context = dataset_retrieval.retrieve(
            query, dataset, query_documents, hit_callback,
            is_content_with_context=False,
            retrieval_model={'top_k': 1},
            tracing_log=tracing_log
        )

        return context
