from app.services.gene_question_service import GeneQuestionService
from django_ext import base_view
from app.api.validators import GeneQuestionValidator
from app.api.dto import GeneQuestionDto
from django_ext.response import make_response


class GeneQuestionView(base_view.BaseView):

    validator_class = GeneQuestionValidator

    def post(self,request, *args, **kwargs):
        dto:GeneQuestionDto = self.validate_request(request.data)

        try:
            result = GeneQuestionService.generate(dto,request.user)
            return make_response(result)
        except Exception as e:
            error_message = f"解析过程中出现错误：{str(e)}"
            return make_response({"error": error_message}, status=500)
