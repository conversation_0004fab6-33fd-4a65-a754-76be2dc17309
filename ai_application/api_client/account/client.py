import logging

from api_client.account.dto import UserDetailDto
from api_client.base_client import BaseClient
from api_client.decorators import catch_error

logger = logging.getLogger(__name__)


class AccountClient(BaseClient):
    host = 'http://account-svc:8000'
    host_local = 'http://account.kaoyango.com'

    @catch_error()
    def get_user_detail(self, user_id: str) -> UserDetailDto | None:
        url = f'/user/{user_id}/'
        res = self._get(url)
        return UserDetailDto.build_model(res)


account_client = AccountClient()
