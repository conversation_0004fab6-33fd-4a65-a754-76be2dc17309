import json

from django.http import StreamingHttpResponse
from django.views.generic import TemplateView

from app.api.dto import KnowledgeSearchDto
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.features.video_keyword_query import VideoKeywordQueryFeature
from app.models import PromptTemplate, Account, Knowledge, DatasetDocument
from app.services.knowledge_service import KnowledgeService
from django_ext.base_view import BaseView
from django_ext.response import make_response
from django.http import JsonResponse
from app.models import Message
from app.core.workflow.knowledge_workflow_2 import KnowledgeWorkflow
from django.db.models import F

# 原始文档id
DEMO_DOCUMENT_NO = '42cfffda-0703-46e9-8716-d03191d1f939'


class KnowledgeListView2(TemplateView):
    template_name = 'app/knowledge_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        dataset_document = DatasetDocument.objects.filter(
            is_deleted=False, original_knowledge_document_no=DEMO_DOCUMENT_NO).first()
        if dataset_document:
            qs = Knowledge.objects.filter(
                is_deleted=False, dataset_document=dataset_document).order_by('id')
            kgs = [{
                'name': k.name,
                'definition': k.definition,
            } for k in qs]
        else:
            kgs = []
        context.update({'knowledge_list': kgs})
        return context


class KnowledgeSearchView2(TemplateView):
    template_name = 'app/knowledge_search_2.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        templates = PromptTemplate.objects.filter(
            is_deleted=False,
            app_no__in=[
                'knowledge_query_split',
                'knowledge_query_llm', 'knowledge_query_local',
                'knowledge_deep_query_llm', 'knowledge_deep_query_local',
                'knowledge_deep_question', 'knowledge_deep_no_question',
            ]
        )
        knowledge_query_split = ''
        knowledge_with_local = ''
        knowledge_with_llm = ''
        knowledge_deep_query_llm = ''
        knowledge_deep_query_local = ''
        knowledge_deep_question = ''
        knowledge_deep_no_question = ''

        for t in templates:
            prompt = t.get_debug_prompt_content()
            if t.app_no == 'knowledge_query_split':
                knowledge_query_split = prompt
            elif t.app_no == 'knowledge_query_llm':
                knowledge_with_llm = prompt
            elif t.app_no == 'knowledge_query_local':
                knowledge_with_local = prompt
            elif t.app_no == 'knowledge_deep_query_llm':
                knowledge_deep_query_llm = prompt
            elif t.app_no == 'knowledge_deep_query_local':
                knowledge_deep_query_local = prompt
            elif t.app_no == 'knowledge_deep_question':
                knowledge_deep_question = prompt
            elif t.app_no == 'knowledge_deep_no_question':
                knowledge_deep_no_question = prompt

        context.update({
            'knowledge_query_split': knowledge_query_split,
            'knowledge_with_local': knowledge_with_local,
            'knowledge_with_llm': knowledge_with_llm,
            'knowledge_deep_query_llm': knowledge_deep_query_llm,
            'knowledge_deep_query_local': knowledge_deep_query_local,
            'knowledge_deep_question': knowledge_deep_question,
            'knowledge_deep_no_question': knowledge_deep_no_question,

        })
        return context


class KnowledgeSearchResultView2(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        query = request.data.get('query')

        # search_mode = request.data.get('search_mode')
        search_type = request.data.get('search_type')

        split_prompt = request.data.get('split_prompt')
        local_prompt = request.data.get('local_prompt')
        llm_prompt = request.data.get('llm_prompt')

        PromptTemplate.objects.filter(app_no='knowledge_query_split').update(debug_prompt_content=split_prompt)
        if search_type == 'local':
            PromptTemplate.objects.filter(app_no='knowledge_query_local').update(debug_prompt_content=local_prompt)
            PromptTemplate.objects.filter(app_no='knowledge_query_llm').update(debug_prompt_content=llm_prompt)
        elif search_type == 'llm':
            PromptTemplate.objects.filter(app_no='knowledge_query_local').update(debug_prompt_content=local_prompt)

        dto = KnowledgeSearchDto(
            query=query,
            course_id='console_course',
            document_no=DEMO_DOCUMENT_NO,
            # search_mode=search_mode,
            search_type=search_type,
        )
        account = Account.objects.first()
        response = KnowledgeService.query_knowledge(dto, account, 'console')

        stream_response = StreamingHttpResponse(response, content_type='text/event-stream; charset=utf-8')
        stream_response['Cache-Control'] = 'no-cache'
        return stream_response


class KnowledgeDeepSearchResultView2(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        query = request.data.get('query')

        # search_mode = request.data.get('search_mode')
        search_type = request.data.get('search_type')

        split_prompt = request.data.get('split_prompt')
        deep_local_prompt = request.data.get('deep_local_prompt')
        deep_llm_prompt = request.data.get('deep_llm_prompt')
        deep_question_prompt = request.data.get('deep_question_prompt')
        deep_no_question_prompt = request.data.get('deep_no_question_prompt')

        PromptTemplate.objects.filter(app_no='knowledge_query_split').update(debug_prompt_content=split_prompt)
        if search_type == 'local':
            PromptTemplate.objects.filter(app_no='knowledge_deep_query_local').update(debug_prompt_content=deep_local_prompt)
            PromptTemplate.objects.filter(app_no='knowledge_deep_query_llm').update(debug_prompt_content=deep_llm_prompt)
            PromptTemplate.objects.filter(app_no='knowledge_deep_question').update(debug_prompt_content=deep_question_prompt)
            PromptTemplate.objects.filter(app_no='knowledge_deep_no_question').update(debug_prompt_content=deep_no_question_prompt)
        elif search_type == 'llm':
            PromptTemplate.objects.filter(app_no='knowledge_deep_query_llm').update(debug_prompt_content=deep_llm_prompt)
            PromptTemplate.objects.filter(app_no='knowledge_deep_question').update(debug_prompt_content=deep_question_prompt)
            PromptTemplate.objects.filter(app_no='knowledge_deep_no_question').update(debug_prompt_content=deep_no_question_prompt)

        dto = KnowledgeSearchDto(
            query=query,
            course_id='console_course',
            document_no=DEMO_DOCUMENT_NO,
            # search_mode=search_mode,
            search_type=search_type,
            userinfo={'user_id': 0, 'user_name': 'console调试', 'user_type': ''},
        )
        account = Account.objects.first()
        response = KnowledgeService.query_knowledge(dto, account, 'console')
        stream_response = StreamingHttpResponse(response, content_type='text/event-stream; charset=utf-8')
        stream_response['Cache-Control'] = 'no-cache'
        return stream_response


class KnowledgeSearchWithVideoView2(TemplateView):
    template_name = 'app/knowledge_search_with_video.html'


class KnowledgeSearchWithVideoResultView2(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        query = request.data.get('query')
        if query:
            document_nos = VideoKeywordQueryFeature().run(query)
            if document_nos:
                docs = DatasetDocument.objects.filter(document_no__in=document_nos)
                return make_response([{'name': doc.name} for doc in docs])
        return make_response([])


class Knowledgesave_knowledge_view(BaseView):

    def post(self, request):
            data = request.data
            message_id = data.get('message_id')
            save_to_db = data.get('save_to_db', False)

            try:
                message = Message.objects.get(id=message_id)
                # 从消息内容中提取知识点名称（需根据实际消息结构调整）
                # 假设消息内容中包含知识点名称，例如：
                knowledge_name = message.content.split('\n')[0].strip()  # 示例提取方式

                # 获取知识点对象
                knowledge = Knowledge.objects.get(name=knowledge_name)

                # 点赞次数统计（原子更新）
                knowledge = Knowledge.objects.filter(name=knowledge_name).update(
                    like_count=F('like_count') + 1
                )
                updated_knowledge = Knowledge.objects.get(name=knowledge_name)

                if save_to_db:
                    # 检查条件：搜索≥3次且点赞≥3次
                    if updated_knowledge.search_count >= 3 and updated_knowledge.like_count >= 3:
                        # 执行保存逻辑（原有保存代码）
                        application_generate_entity = CompletionAppGenerateEntity(query=message.query)
                        queue_manager = AppQueueManager(queue_name="knowledge_queue")
                        workflow = KnowledgeWorkflow(
                            application_generate_entity=application_generate_entity,
                            message=message,
                            queue_manager=queue_manager,
                            save_to_db=True
                        )
                        workflow.trigger_save()
                        return JsonResponse({
                            "success": True,
                            "message": "知识点已保存到数据库",
                            "search_count": updated_knowledge.search_count,
                            "like_count": updated_knowledge.like_count
                        })
                    else:
                        return JsonResponse({
                            "success": False,
                            "reason": f"需要搜索至少3次（当前{updated_knowledge.search_count}次）"
                                      + f"且点赞至少3次（当前{updated_knowledge.like_count}次）"
                        })
                else:
                    return JsonResponse({
                        "success": True,
                        "message": "点赞成功",
                        "search_count": updated_knowledge.search_count,
                        "like_count": updated_knowledge.like_count
                    })

            except Message.DoesNotExist:
                return JsonResponse({"success": False, "reason": "消息不存在"})
            except Knowledge.DoesNotExist:
                return JsonResponse({"success": False, "reason": "知识点不存在"})
            except Exception as e:
                return JsonResponse({
                    "success": False,
                    "reason": f"操作失败：{str(e)}"
                }, status=500)