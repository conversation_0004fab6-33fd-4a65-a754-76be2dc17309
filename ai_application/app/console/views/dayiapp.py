import os
import re
import uuid
from django.conf import settings
from django.views.generic import TemplateView
from app.api.dto import DayiApptDto
from app.constants.app import ConversationStatus
from app.models import Conversation, App, Account, InvokeFrom
from app.models.dayiapp import AssistantRequest, SubjectPrompt
from app.services.chat_app2.dayiapp import DayiAppGenerateService
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
import json
import logging

from django_ext.response import make_stream_response
from helpers.upload_helper import upload_file

logger = logging.getLogger(__name__)


class STAIAssistantIndexView(TemplateView):
    template_name = "app/shuati/zhizhou_shuati.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        latest_prompt = SubjectPrompt.objects.order_by('-updated_at').first()
        app_no = 'app_077a9a772c33411a'
        app_model = App.objects.get(app_no=app_no)
        app_model_config = app_model.app_model_config

        from_account = Account.objects.first()
        conversation = Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app_id=app_model.id,
            app_model_config_id=app_model_config.id,
            model_provider=app_model_config.model_provider,
            model_id=app_model_config.model_id,
            inputs={},
            name='New conversation',
            status=ConversationStatus.NORMAL.value,
            from_account=from_account,
            invoke_from=InvokeFrom.console.value,
        )

        context['initial_system_prompt'] = latest_prompt.prompt if latest_prompt else ""
        context['conversation_id'] = conversation.conversation_no
        # 添加额外的上下文数据
        return context


# 修正后的 DayiappView 类
class AIAssistantIndexView(TemplateView):  # 或继承自 BaseView，取决于你的需求
    template_name = "app/dayiapp_index.html"

    # 如果需要添加额外功能，可以在这里定义方法
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        latest_prompt = SubjectPrompt.objects.order_by('-updated_at').first()
        app_no = 'app_077a9a772c33411a'
        app_model = App.objects.get(app_no=app_no)
        app_model_config = app_model.app_model_config

        from_account = Account.objects.first()
        conversation = Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app_id=app_model.id,
            app_model_config_id=app_model_config.id,
            model_provider=app_model_config.model_provider,
            model_id=app_model_config.model_id,
            inputs={},
            name='New conversation',
            status=ConversationStatus.NORMAL.value,
            from_account=from_account,
            invoke_from=InvokeFrom.console.value,
        )

        context['initial_system_prompt'] = latest_prompt.prompt if latest_prompt else ""
        context['conversation_id'] = conversation.conversation_no
        # 添加额外的上下文数据
        return context


class SubjectPromptView(View):
    @method_decorator(csrf_exempt)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get(self, request):
        """获取所有学科提示词（用于前端初始化）"""
        subjects = SubjectPrompt.objects.all().values('subject_id', 'name', 'prompt')
        return JsonResponse(list(subjects), safe=False)

    def post(self, request):
        """批量保存学科提示词"""
        try:
            data = json.loads(request.body)
            updated_records = []

            for subject_data in data:
                # 直接使用 subject_data['subject_id'] 和 subject_data['prompt']
                if len(subject_data['prompt'].strip()) > 2000:
                    return JsonResponse({
                        "error": f"学科 {subject_data.get('name', '未知')} 的提示词长度超过限制（2000字）"
                    }, status=400)

                obj, created = SubjectPrompt.objects.update_or_create(
                    subject_id=subject_data['subject_id'],
                    defaults={'prompt': subject_data['prompt'].strip()}
                )
            return JsonResponse({
                "message": "保存成功",
                "records": updated_records})

        except json.JSONDecodeError:
            return JsonResponse({"error": "无效的JSON格式"}, status=400)
        except Exception as e:
            return JsonResponse({"error": f"保存失败: {str(e)}"}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AIAssistantView(View):
    MAX_PROMPT_LENGTH = 50000  # 最大提示词长度限制

    def post(self, request, *args, **kwargs):
        """处理POST请求（API接口）"""
        # 解析请求数据
        # data = json.loads(request.body)
        user_input = request.POST.get('input', '').strip()
        all_system_prompts = json.loads(request.POST.get('system_prompt', {}))
        subject_id = request.POST.get('subject_id', 'other')
        user_id = request.POST.get('user_id', '')
        conversation_no = request.POST.get('conversation_id', '')
        scene_info_str = request.POST.get('scene_info', '{}')
        if isinstance(scene_info_str, str):
            try:
                scene_info = json.loads(scene_info_str)
            except json.JSONDecodeError:
                scene_info = {}
        else:
            scene_info = scene_info_str if isinstance(scene_info_str, dict) else {}

        images = request.FILES.getlist('images')
        print(f"火火火🚀", scene_info)
        image_file_urls = []
        if images:
            for image_file in images:
                image_file_url = upload_file(image_file, file_name=image_file.name, sub_path='zhizhou_wenda')
                image_file_urls.append(image_file_url)

        # 输入验证
        if not user_input:
            return JsonResponse({"error": "输入内容不能为空"}, status=400)

        image_pattern = re.compile(r'(https?://\S+\.(?:jpg|jpeg|png|gif|bmp|webp)(?:\?\S*)?)', re.IGNORECASE)
        image_urls = image_pattern.findall(user_input)
        text_content = image_pattern.sub('', user_input).strip()
        all_image_urls = image_file_urls + image_urls

        # 验证提示词类型（确保是字典）
        if not isinstance(all_system_prompts, dict):
            return JsonResponse({"error": "system_prompts 必须为字典类型"}, status=400)

        # 验证每个提示词长度（可选）
        for prompt in all_system_prompts.values():
            if len(prompt) > self.MAX_PROMPT_LENGTH:
                return JsonResponse({"error": "提示词长度超过限制"}, status=400)

        # 创建数据库记录
        request_record = AssistantRequest.objects.create(
            subject_id=subject_id,
            system_prompt=json.dumps(all_system_prompts, ensure_ascii=False),
            user_input=user_input,
        )

        from_account = Account.objects.first()

        dto: DayiApptDto = DayiApptDto(
            conversation_id=conversation_no,
            query=text_content,
            images=all_image_urls,
            scene_info=scene_info,
            userinfo={'user_id': user_id}
        )
        response_content = DayiAppGenerateService.generate(dto, from_account)
        return make_stream_response(response_content)
