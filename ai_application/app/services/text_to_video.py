import logging
import os
import subprocess
import shutil
import requests
import time
import json
# from pycorrector.corrector import Corrector
from concurrent.futures import ThreadPoolExecutor, as_completed

# from app.core.features.text_to_video import *
from app.core.features.text_to_video import gene_analysis_report,gene_sc
from app.models import DataStructureKnowledge
from app.models.knowledge_simple import KnowledgeSimple
from app.models.knowledge_video import KnowledgeVideo
from helpers.upload_helper import upload_file
from django.http import JsonResponse

logger = logging.getLogger(__name__)


def get_knowledge_data():
    kps = KnowledgeSimple.objects.filter(
        course_id=467,
        definition__isnull=False,
        is_usable=1,
    )
    knowledge_list = []
    for kp in kps:
        knowledge_dict = {
            "name": kp.name,
            "defination": kp.definition,
            "analysis": kp.desc
        }
        knowledge_list.append(knowledge_dict)
    return knowledge_list


# def correct_subtitle_srt(input_path, output_path):
#     with open(input_path, 'r', encoding='utf-8') as fin, open(output_path, 'w', encoding='utf-8') as fout:
#         corrector = Corrector()
#         lines = fin.readlines()
#         for line in lines:
#             stripped_line = line.strip()

#             if not stripped_line:
#                 fout.write('\n')  # 空行
#             elif '-->' in stripped_line or stripped_line.isdigit():
#                 fout.write(stripped_line + '\n')  # 编号/时间戳
#             else:
#                 result = corrector.correct(stripped_line)

#                 # 如果 result 是字典，提取 "text" 字段；否则直接转字符串
#                 if isinstance(result, dict):
#                     corrected_text = result.get("text", stripped_line)
#                 else:
#                     corrected_text = str(result)

#                 fout.write(corrected_text + '\n')  # 纠正后的文本行


def add_subtitle_to_video(input_video, subtitle_file, output_video):
    command = [
        'ffmpeg',
        '-i', input_video,
        '-vf', f'subtitles={subtitle_file}',
        '-c:a', 'copy',
        output_video
    ]
    try:
        subprocess.run(command, check=True)
        print(f"✅ Subtitle added to {output_video}")
    except subprocess.CalledProcessError as e:
        print("❌ Error adding subtitles:", e)


def process_knowledge(core_course_name, kp, task=None):
    try:
        print(f"🎬 process_knowledge 被调用: {kp}")
        save_path = f'app/core/features/text_to_video/output_files/{kp["name"]}'
        print(f"📁 创建保存路径: {save_path}")
        os.makedirs(save_path, exist_ok=True)

        analysis_report = gene_analysis_report(kp['name'])

        script = gene_sc(
            f'课程名：《{core_course_name}》 知识点名：{analysis_report["name"]} 知识点定义：{analysis_report["definition"]} '
            f'前置知识点:{analysis_report["pre"]} '
            f'知识点讲解：{analysis_report["basic"]} '
            f'案例说明：{analysis_report["case"]} '
            f'总结：{analysis_report["summary"]}'
        )

        processed_script = []
        for item in script:
            processed_script.append({
                'id': item.get('id'),
                'title': item.get('title'),
                'text': item.get('text'),
                'audio': item.get('audio'),
                'generate_figure': False,
                'figure_code': ''
            })

        task_data = {
            'core_course_name': core_course_name,
            'name': analysis_report['name'],
            'definition': analysis_report['definition'],
            'ppt_script_list': processed_script
        }

        create_response = requests.post(
            'http://yantucs-video.kaoyan-vip.cn/internal_api/knowledge_video_task/create',
            json=task_data,
            timeout=6000
        )
        create_response.raise_for_status()

        task_result = create_response.json()
        task_id = task_result.get('data', {}).get('task_id')
        if not task_id:
            raise RuntimeError('创建任务失败，未获取到 task_id')

        # 更新知识点定义
        DataStructureKnowledge.objects.filter(
            subject_2=core_course_name,
            name=kp['name'],
        ).update(
            definition=analysis_report['definition'],
        )

        # 轮询任务状态
        max_attempts = 120
        attempt = 0
        video_url = None

        while attempt < max_attempts:
            status_response = requests.get(
                'http://yantucs-video.kaoyan-vip.cn/internal_api/knowledge_video_task/status',
                params={'task_id': task_id},
                timeout=30
            )

            if status_response.status_code != 200:
                time.sleep(10)
                attempt += 1
                continue

            status_result = status_response.json()
            task_status = status_result.get('data', {}).get('status')
            print(f"🎥 任务状态: {task_status}")

            if task_status == 'success':
                video_url = status_result.get('data', {}).get('video_url')
                if video_url:
                    print(f"✅ 视频生成成功: {video_url}")
                    break
            elif task_status == 'fail':
                error_msg = status_result.get('error', '未知错误')
                raise RuntimeError(f"视频生成失败: {error_msg}")

            time.sleep(10)
            attempt += 1

        if not video_url:
            raise TimeoutError("视频生成超时，未能获取到视频 URL")

        existing_video = KnowledgeVideo.objects.filter(
            core_course_name=core_course_name, knowledge_name=kp["name"]).first()
        if existing_video:
            existing_video.video = video_url
            existing_video.save()
        else:
            new_video = KnowledgeVideo.objects.create(
                video=video_url,
                core_course_name=core_course_name,
                knowledge_name=kp["name"]
            )

        return video_url

    except Exception as e:
        logger.error("📛 process_knowledge 失败: %s", str(e))

        if task is not None:
            fail_reason = f"视频生成失败: {str(e)}"
            max_len = 255
            task.fail_reason = fail_reason[:max_len]
            task.status = "FAILED"
            task.save(update_fields=["status", "fail_reason"])
        
        raise e  # 继续抛出异常用于上层日志或处理



# 检查知识点是否已处理
def is_processed(kp):
    knowledge = KnowledgeSimple.objects.filter(
        course_id=467,
        definition__isnull=False,
        is_usable=1,
        name=kp['name']
    ).first()
    record=KnowledgeVideo.objects.filter(
        name=knowledge
    ).first()
    if record:
        return True
    return False

def gene_video_content():
    knowledge_list = get_knowledge_data()
    print('\nTotal items:', len(knowledge_list), '\n')

    with ThreadPoolExecutor(max_workers=1) as executor:  # 根据机器设置 max_workers
        for kp in knowledge_list:
            if not is_processed(kp):
                futures = executor.submit(process_knowledge, kp)
        for future in as_completed(futures):
            pass  # 日志已在 process_knowledge 中处理
