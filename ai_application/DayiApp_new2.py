import os
import django
import re
import time
import json
import logging

# from OCR_test import BaiduOcrService
#from app.libs.baidu_ocr import BaiduOcr
from typing import Dict, Optional, Generator

from ai_application.env import env
from prompt import subject_prompts

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_application.settings")
django.setup()
from app.libs.baidu_ocr import BaiduOcr as ExternalBaiduOcr
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import <PERSON>nableLambda, RunnableBranch, RunnableParallel
from app.services.question_service import QuestionService
from app.core.apps.message_tracing_log import MessageTracingLog

logger = logging.getLogger(__name__)
# BAIDU ORC处理
BAIDU_OCR_API_ID = env('BAIDU_OCR_API_ID')
BAIDU_OCR_API_KEY = env('BAIDU_OCR_API_KEY')
BAIDU_OCR_SECRET_KEY = env('BAIDU_OCR_SECRET_KEY')


class BaiduOcrService:
    @staticmethod
    def baidu_ocr_get_text(image_url: str) -> str:
        # 移除URL末尾的逗号
        image_url = image_url.rstrip(',')

        # 使用外部导入的BaiduOcr类
        res = ExternalBaiduOcr().basic_accurate(image_file_url=image_url)
        logger.info(f'get_baidu_result:{res}')

        # 确保res是一个列表
        if not isinstance(res, list):
            logger.error(f"Unexpected response format from Baidu OCR: {res}")
            return ''

        words = [i.get('words', '') for i in res]
        return ' '.join(words) or ''


# 题库服务类
class DayiQuestionService:
    @staticmethod
    def query_similar_questions(
            input_text: str,
            score_threshold: float = 0.6,
            size: int = 1,  # 添加size参数
            debug: bool = True
    ):
        try:
            if debug:
                print(f"\n===== 开始题库搜索 =====")
                print(f"搜索文本: {input_text}")
                print(f"搜索参数: size={size}, threshold={score_threshold}")

            # 调用题库服务获取相似问题
            questions = QuestionService.get_questions_by_vector(input_text, size=size)

            if debug and not questions:
                print("警告: 向量搜索未返回任何结果")

            # 过滤并排序
            valid_questions = [q for q in questions if q.get('score', 0) >= score_threshold]
            valid_questions.sort(key=lambda x: x.get('score', 0), reverse=True)

            # 打印搜索结果
            if valid_questions:
                print(f"\n===== 搜索到 {len(valid_questions)} 个相似题目 =====")
                for i, q in enumerate(valid_questions, 1):
                    print(f"{i}. 题目: {q.get('question', '无题目')}")
                    print(f"   相似度: {q.get('score', 0):.2%}")
                    print(f"   参考答案: {q.get('answer_body', '暂无')[:100]}...")
            else:
                print("\n===== 未找到相似题目 =====")
                if questions:
                    print("提示: 找到了一些题目，但相似度低于阈值")
                    for i, q in enumerate(questions[:min(3, len(questions))], 1):
                        print(f"{i}. 最接近的题目: {q.get('question', '无题目')}")
                        print(f"   相似度: {q.get('score', 0):.2%}")

            return valid_questions
        except Exception as e:
            logger.error(f"题库搜索异常: {str(e)}")
            return []


def final_chain_main(
        input_str: str,
        api_base: str = "https://ark.cn-beijing.volces.com/api/v3",
        text_model: str = "doubao-pro-256k-241115",
        visual_model: str = "doubao-1.5-vision-pro-250328",
        system_prompt: str = None,
        cache_size: int = 128
) -> Generator:
    # ---------- 初始化配置 ----------
    llm_text = ChatOpenAI(
        openai_api_key="35fa4429-2bb1-4891-ba42-a1aa4a933ff1",
        openai_api_base=api_base,
        model_name=text_model,
        temperature=0.3,
        streaming=True  # 启用流式输出
    )

    llm_visual = ChatOpenAI(
        openai_api_key="35fa4429-2bb1-4891-ba42-a1aa4a933ff1",
        openai_api_base=api_base,
        model_name=visual_model,
        temperature=0.5,
        streaming=True  # 启用流式输出
    )


    # ---------- 工具函数 ----------
    def determine_subject(input_text: str) -> str:
        """优化后的多模态学科分类"""
        image_pattern = re.compile(r'(https?://\S+\.(?:jpg|jpeg|png|gif|bmp|webp)(?:\?\S*)?)', re.IGNORECASE)
        image_urls = image_pattern.findall(input_text)
        text_content = image_pattern.sub('', input_text).strip()

        # 构建多模态消息结构
        content = []

        # 优先处理文本内容
        if text_content:
            # 添加强调标记
            emphasized_text = f"【重点分析】文本关键词：{text_content}"
            content.append({"type": "text", "text": emphasized_text})

        # 添加图片内容（最多3张）
        if image_urls:
            for url in image_urls[:3]:  # 限制处理图片数量
                content.append({"type": "image_url", "image_url": {"url": url}})

        # 增强版分类提示词
        classification_prompt = """请按以下步骤分析：
    1. 识别文本中的学科关键词（如：作文→英语，公式→数学，细胞→生物）
    2. 分析图片内容特征（如：坐标系→数学，实验器材→化学，器官图→生物）
    3. 从[math||law|english|english_translation|psychology|mechanical engineering|
    electrical engineering|computer science|education|politics|p.e|finance|nursing comprehensive 308|
    the management comprehensive examination 199|art|
    comprehensive examination of western medicine 306|other]中选择最匹配的学科
    只需返回学科名称，不要其他内容"""

        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content=classification_prompt),
            HumanMessage(content=content)
        ])

        # 添加重试机制
        max_retries = 2
        for attempt in range(max_retries):
            try:
                result = (prompt | llm_visual | RunnableLambda(
                    lambda x: x.content.strip().lower() or "other")).invoke({})
                print("🚀===========", result)

                # 后处理验证
                valid_subjects = ["math", "law", "english_translation", "english", "psychology","mechanical engineering",
                                  "electrical engineering","computer science","education","politics",
                                  "p.e","finance","nursing comprehensive 308","the management comprehensive examination 199",
                                  "art","comprehensive examination of western medicine 306"]
                # 转换为小写集合进行匹配
                valid_subjects_lower = [s.lower() for s in valid_subjects]
                if result not in valid_subjects_lower:
                    logger.warning(f"Invalid subject detected: {result}")
                    return "other"

                return valid_subjects[valid_subjects_lower.index(result)]  # 返回原始大小写的学科名称
            except Exception as e:
                logger.error(f"学科分类异常（尝试 {attempt + 1}）: {str(e)}")
                if attempt == max_retries - 1:
                    return "other"

    def is_question(input_text: str) -> bool:
        """判断是否为问题"""
        text = re.sub(r'http\S+', '', input_text)  # 移除图片URL
        return any(c in text for c in ['?', '？']) or \
            any(word in text.lower() for word in ['how', 'why', 'what', 'when', '哪里', '吗', '是什么', '为什么'])

    # ---------- 处理链组件 ----------
    def build_prompt(context: Dict) -> list:
        """动态构建提示词"""
        messages = []
        subject = context.get('subject', 'other')

        # 添加系统提示
        sys_prompt = subject_prompts.get(subject, subject_prompts['other'])
        similar_questions = context.get('similar_questions', [])

        # 添加题库参考信息
        if similar_questions:
            sys_prompt += "\n【题库参考】匹配到高相似题目：\n" + \
                          "\n".join([f"► 题目：{q['question']}\n  参考答案：{q.get('answer', '暂无')}"
                                     for q in similar_questions])
            # 添加相似度提示
            max_score = max(q.get('score', 0) for q in similar_questions)
            sys_prompt += f"\n最高匹配度：{max_score:.0%}"

        messages.append(SystemMessage(content=sys_prompt))

        # 添加用户消息
        content = []
        if context.get('image_urls'):
            for url in context['image_urls']:
                content.append({"type": "image_url", "image_url": {"url": url}})
        if context.get('text_content'):
            content.append({"type": "text", "text": context['text_content']})
        messages.append(HumanMessage(content=content))

        return messages

    def process_content(context: Dict) -> Generator:
        """内容处理路由"""
        # 选择模型
        llm = llm_visual if context['has_image'] else llm_text
        # 构建消息
        messages = build_prompt({
            "subject": context["subject"],
            "similar_questions": context.get("similar_questions"),
            "image_urls": context.get("image_urls"),
            "text_content": context.get("text_content")
        })
        return llm.stream(messages)

    # 新增OCR处理函数
    def ocr_and_combine(context: Dict) -> str:
        """OCR识别并组合文本"""
        base_text = context["text_content"]
        for url in context["image_urls"]:
            ocr_text = BaiduOcrService.baidu_ocr_get_text(url)
            base_text += f"\n[图片内容]: {ocr_text}"
        return base_text.strip()


    # ---------- 主处理链 ----------
    processing_chain = (
            RunnableLambda(lambda x: {"raw_input": x})
            | RunnableParallel({
        "subject": RunnableLambda(lambda x: determine_subject(x["raw_input"])),
        "image_urls": RunnableLambda(lambda x: re.findall(
            r'(https?://\S+\.(?:jpg|jpeg|png|gif|bmp|webp)(?:\?\S*)?)',
            x["raw_input"],
            re.IGNORECASE
        )),
        "text_content": RunnableLambda(lambda x: re.sub(
            r'(https?://\S+\.(?:jpg|jpeg|png|gif|bmp|webp)(?:\?\S*)?)',
            '',
            x["raw_input"]
        ).strip())
    })
            | RunnableParallel({
        # 处理OCR和搜索查询
        "search_query": RunnableLambda(
            lambda x: ocr_and_combine({
                "text_content": x["text_content"],
                "image_urls": x["image_urls"]
            })
            if x["image_urls"] else x["text_content"]
        ),
        "subject": RunnableLambda(lambda x: x["subject"]),
        "image_urls": RunnableLambda(lambda x: x["image_urls"]),
        "text_content": RunnableLambda(lambda x: x["text_content"]),
        "has_image": RunnableLambda(lambda x: len(x["image_urls"]) > 0),
        "is_question": RunnableLambda(lambda x: is_question(x["text_content"]))
    })
            | RunnableBranch(
        # 正确的RunnableBranch格式：[(条件1, 处理链1), (条件2, 处理链2), ..., 默认处理链]
        (lambda x: x["is_question"],
         RunnableParallel({
             "similar_questions": RunnableLambda(
                 lambda x: DayiQuestionService.query_similar_questions(
                     x["search_query"],
                     size=1,
                     score_threshold=0.6,
                 ),
             ),
             "context": RunnableLambda(lambda x: x)
         }) | RunnableLambda(lambda x: {**x["context"], "similar_questions": x["similar_questions"]})),
        RunnableLambda(lambda x: {**x, "similar_questions": []})
    )
    | RunnableLambda(process_content)  # 返回流式响应生成器
)

    # 直接返回处理链的流式生成器
    return processing_chain.invoke(input_str)


# 测试
# if __name__ == "__main__":
#     test_case = "https://oss.kaoyanvip.cn/attach/file1745747749916.png ,这篇英语作文有哪些要修改的地方？"
#     print(final_chain_main(test_case))

if __name__ == "__main__":
    test_case = "复合材料（如碳纤维增强聚合物）在轻量化设计中如何解决各向异性带来的强度挑战？"
    stream_generator = final_chain_main(test_case)

    # 逐块打印流式内容（模拟前端实时接收）
    print("流式输出开始：")
    for chunk in stream_generator:
        print(chunk.content, end="", flush=True)  # 实时输出每个文本块
    print("\n流式输出结束")