--
-- <PERSON>reate model SubjecttoKnowledge
--
CREATE TABLE `ai_subject_to_knowledge` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `subject_1` varchar(100) NOT NULL, `subject_2` varchar(100) NOT NULL, `categories` varchar(100) NOT NULL, `knowledge_points` varchar(100) NOT NULL);
--
-- <PERSON>reate model TexttoVideo
--
CREATE TABLE `ai_text_to_video` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `name` varchar(20) NOT NULL, `video` varchar(200) NOT NULL, `script` json NULL, `user_info` json NOT NULL);
--
-- <PERSON>reate model KnowledgeVideo
--
CREATE TABLE `ai_knowledge_video` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `video` varchar(200) NOT NULL, `image` varchar(200) NOT NULL, `name_id` bigint NOT NULL);
CREATE INDEX `ai_subject_to_knowledge_add_time_438c41ee` ON `ai_subject_to_knowledge` (`add_time`);
CREATE INDEX `ai_text_to_video_add_time_94a634b4` ON `ai_text_to_video` (`add_time`);
ALTER TABLE `ai_knowledge_video` ADD CONSTRAINT `ai_knowledge_video_name_id_0e1d9683_fk_ai_knowledge_simple_id` FOREIGN KEY (`name_id`) REFERENCES `ai_knowledge_simple` (`id`);
CREATE INDEX `ai_knowledge_video_add_time_a70b4639` ON `ai_knowledge_video` (`add_time`);