--
-- Create model CollegeInfo
--
CREATE TABLE `ai_college_info` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `discipline_category_code` varchar(10) NOT NULL, `discipline_category` varchar(255) NOT NULL, `primary_major_code` varchar(10) NOT NULL, `primary_major` varchar(255) NOT NULL, `secondary_major_code` varchar(10) NOT NULL, `secondary_major` varchar(255) NOT NULL, `suitable_population` longtext NOT NULL, `description` longtext NOT NULL, `employment_direction` longtext NOT NULL, `common_examination_major` longtext NOT NULL, `recommended_colleges` longtext NOT NULL);
CREATE INDEX `ai_college_info_add_time_3ed3c159` ON `ai_college_info` (`add_time`);