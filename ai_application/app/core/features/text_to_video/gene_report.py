import json
from django.conf import settings
import time
from openai import OpenAI

def gene_analysis_report(kp):
    default_prompt = """
    # 角色
    你是一位严谨且富有教学经验的知识点解析专家，擅长将抽象的计算机相关知识点转化为结构清晰、条理分明、层层递进的讲解内容，帮助基础薄弱的学生从0到1掌握关键知识。

    ## 输入任务
    你将收到一个知识点名称，请你以教学辞典的风格进行知识拆解，从定义、前置知识、基本内容、应用案例到总结，生成一个结构化的字典，字段如下：

    ## 输出结构（json）
    
    "name": 知识点名称（字符串）,
    "definition": 用一两句话给出简明清晰的定义，突出它"是什么",
    "pre": 用两三句话说明掌握该知识点所需的前置知识或基础能力,
    "basic": 用**五到十句话**详细讲解该知识点的基本概念，语言要通俗易懂，内容控制在100～200字之间，适合基础较差的学生理解,
    "case": 用**五到十句话**给出该知识点的典型应用或例题讲解，语言生动，可以使用类比或简单案例辅助解释，内容控制在100～200字之间,
    "summary": 用一两句话总结这个知识点的本质、常见用途、易错点或理解关键
    

    ## 输出要求
    - 输出格式必须为json，使用单引号、英文逗号，字段顺序保持一致，；
    - 所有字段必须完整输出；
    - `basic` 与 `case` 字段内容要求为 5～10 句，100～200 字，不能简略；
    - 内容通俗、具体，适合初学者；
    - 案例可以是典型例题（含简要描述和思路）或现实生活中的类比场景；
    - 不要加入解释性文字或注释，直接输出字典结构。

    输入为：
    {knowledge_point}
    """
    
    # 使用原生 OpenAI 客户端
    client = OpenAI(
        api_key=settings.DOUBAO_API_KEY,
        base_url="https://ark.cn-beijing.volces.com/api/v3",
    )

    
    # 格式化提示词
    formatted_prompt = default_prompt.format(knowledge_point=kp)
    
    
    # 调用 API
    response = client.chat.completions.create(
        model="doubao-1.5-pro-32k-250115",
        messages=[
            {"role": "user", "content": formatted_prompt}
        ],
        temperature=0.7
    )
    
    result = response.choices[0].message.content
    print(result)
    time.sleep(10)
    return json.loads(result)
