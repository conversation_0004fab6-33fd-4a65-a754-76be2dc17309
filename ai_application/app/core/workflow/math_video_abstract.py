import json
import os

from django.conf import settings

from app.core.features.math_video_knowledge_extract.math_video_abstract import MathVideoAbstractFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import KnowledgeLibrary


class MathVideoAbstractWorkflow(BaseWorkflow):

    def run(self):
        main_subject_map = {
            'high_math': '高等数学',
            'linear_algebra': '线性代数',
            'math_prob': '概率论与数理统计',
            'data_structure': '数据结构',
            'organization': '组成原理',
            'network': '计算机网络',
            'os': '操作系统',
        }

        main_subject = self.application_generate_entity.inputs.get('main_subject')
        if not (main_subject and main_subject in main_subject_map):
            raise Exception('main_subject is valid')
        video_subtitles = self.application_generate_entity.query
        if not video_subtitles:
            raise Exception('video_subtitles is empty')

        if main_subject in ['high_math', 'linear_algebra', 'math_prob']:
            knowledge_json_file = settings.BASE_DIR.joinpath(f'knowledge_list/{main_subject}.json')
            if not os.path.exists(knowledge_json_file):
                raise Exception('main_subject path is valid')

            try:
                with open(knowledge_json_file, 'r') as f:
                    knowledge_list = json.loads(f.read())
            except:
                raise Exception('knowledge_list is valid')
            knowledge_list = [i['name'] for i in knowledge_list]
        else:
            qs = KnowledgeLibrary.objects.filter(
                subject_domain__main_subject_code=main_subject,
                is_deleted=False, nature='major'
            )
            knowledge_list = []
            for i in qs:
                knowledge_list.append(i.name)

        video_content = self._abstract_math_video(knowledge_list)
        self._publish_workflow_custom_message(video_content)
        self._publish_workflow_end_message()

    def _abstract_math_video(self, knowledge_list) -> str:
        main_subject = self.application_generate_entity.inputs.get('main_subject')

        if main_subject in ['high_math', 'linear_algebra', 'math_prob']:
            app_no = 'math_video_abstract'
        else:
            app_no = '408_video_abstract'

        video_subtitles = self.application_generate_entity.query
        result = MathVideoAbstractFeature(app_no).run(
            self.application_generate_entity,
            main_subject=main_subject,
            knowledge_list=knowledge_list,
            video_content=video_subtitles,
        )

        tracing_info = {
            'tracing_type': app_no,
            'query': video_subtitles
        }
        self.tracing_log.add_tracing_by_llm_result(
            tracing_type=tracing_info.get('tracing_type'),
            query=tracing_info.get('query'),
            llm_result=result
        )

        return result.message.content
