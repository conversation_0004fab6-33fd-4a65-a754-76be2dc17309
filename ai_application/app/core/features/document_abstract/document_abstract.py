import math

from app.constants.app import AppMode
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.model_manager import ModelInstance
from app.core.prompt.simple_prompt_transform import SimplePromptTransform
from app.core.prompt.utils.prompt_message_util import PromptMessageUtil
from app.errors import DocumentExtractionError
from app.models import PromptTemplate


class DocumentAbstractFeature:

    def process(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ) -> str:
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no='document_abstract', is_deleted=False
        ).first()
        if not prompt_template:
            raise Exception('prompt template not found')

        max_text_length = 100000
        content_length = len(query)
        if content_length < max_text_length:
            return query

        max_chunk_length = 80000
        chunk_length = math.ceil(content_length / math.ceil(content_length / max_chunk_length))

        split_content = []
        temp_str = ''
        for i in query.split('\n'):
            if temp_str and len(temp_str + '\n' + i) > chunk_length:
                split_content.append(temp_str)
                temp_str = i
            else:
                temp_str = temp_str + '\n' + i
        if temp_str:
            split_content.append(temp_str)

        answer = ''
        for q in split_content:
            new_q = f'{answer}\n{q}' if answer else q
            answer = self._process_chunk(
                application_generate_entity=application_generate_entity,
                prompt_template=prompt_template,
                query=new_q,
                tracing_log=tracing_log,
            )

        return answer

    def _process_chunk(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            prompt_template: PromptTemplate,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        query_in_prompt = 'query' in prompt_template.special_variable_list
        prompt_messages = SimplePromptTransform().get_prompt(
            app_mode=AppMode.COMPLETION,
            pre_prompt=prompt_template.prompt_content,
            query=query,
            query_in_prompt=query_in_prompt,
        )
        model_instance = ModelInstance(
            provider_model_bundle=application_generate_entity.model_conf.provider_model_bundle,
            model=application_generate_entity.model_conf.model
        )

        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            # model_parameters=application_generate_entity.model_conf.model_params,
            model_parameters={"max_tokens": 3000, "temperature": 0.1},
            stream=False
        )

        if tracing_log:
            log_content = PromptMessageUtil.prompt_messages_to_prompt_for_saving(invoke_result.prompt_messages)
            tracing_log.add_tracing(
                tracing_type='document_abstract',
                query=query,
                content=log_content,
                answer=invoke_result.message.content,
                message_tokens=invoke_result.usage.prompt_tokens,
                answer_tokens=invoke_result.usage.completion_tokens,
                total_tokens=invoke_result.usage.total_tokens,
                latency=invoke_result.usage.latency,
            )

        if not invoke_result.is_success:
            raise DocumentExtractionError()

        return invoke_result.message.content
