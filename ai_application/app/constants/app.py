from enum import Enum


class AppType(Enum):
    CHAT_APP = 'chat_app'
    LOCAL_CHAT_APP = 'local_chat_app'
    CODE_OPTIMIZATION = 'code_optimization'
    PROBLEM_SOLVING = 'problem_solving'
    KNOWLEDGE_QUERY = 'knowledge_query'
    KNOWLEDGE_ANALYSIS_SIMPLE = 'knowledge_analysis_simple'
    MATH_PROBLEM_SOLVING = 'math_problem_solving'
    COMPLEX_SENTENCE_ANALYSIS = 'complex_sentence_analysis'
    # math_video_abstract = 'math_video_abstract'
    # math_video_knowledge_extract = 'math_video_knowledge_extract'
    # CONTENT_EXTRACTION = 'article_generation'
    # CONTENT_EXTRACTION = 'prompt_optimize'
    # CONTENT_EXTRACTION = 'document_abstract'
    # CONTENT_EXTRACTION = 'document_summary'
    # CONTENT_EXTRACTION = 'video_script'

    @classmethod
    def value_of(cls, value: str) -> 'AppType':
        for t in cls:
            if t.value == value:
                return t
        raise ValueError(f'invalid app_type value {value}')


class AppMode(Enum):
    CHAT = 'chat'
    COMPLETION = 'completion'

    @classmethod
    def value_of(cls, value: str) -> 'AppMode':
        for mode in cls:
            if mode.value == value:
                return mode
        raise ValueError(f'invalid mode value {value}')


class AppRunType(Enum):
    NATIVE = 'native'
    DIFY = 'dify'


class ConversationStatus(Enum):
    NORMAL = "normal"


class MessageStatus(Enum):
    NOT_ANSWERED = "not_answered"
    NORMAL = "normal"
    ERROR = "error"
    REPLACED = "replaced"


class StreamEvent(Enum):
    ERROR = "error"
    THINKING = "thinking"
    THINKING_END = "thinking_end"
    MESSAGE = "message"
    MESSAGE_END = "message_end"
    TOKEN_EXCEED = "token_exceed"


OUTER_APP_NOS = (
    'app_28482099ed2945e2',         # 知舟问答
    'app_c04f55acabe94352',         # 智学问答
    'knowledge_query',              # 知识点解析
    'knowledge_analysis_simple',    #知识解析基础版    
    'complex_sentence_analysis',    # 英语长难句语法分析
    'math_problem_solving',         # 数学解题助手
)

OUTER_APP_TYPES = {
    'chat_app': '知舟问答',
    'problem_solving': '408解题助手',
    'code_optimization': '代码优化',
    'local_chat_app': '智学问答',
    'knowledge_query': '知识解析',
    'complex_sentence_analysis': '英语长难句语法分析',
    'math_problem_solving': '数学丨解题助手',
    'en_article_judgment': '作文批改',
    'test_answer_report': '数学测试解读',
    'screenshot_gaoshu': '高数截屏答疑',
    'student_learn_stat': '学情分析',
    'college_analysis': '院校专业推荐',
    'dsx_learning_stat': '动手学-学情分析',
    'study_guides': '动手学-学习指导',
    'study_guides_app': 'app-学习指导报告',
    'chat_app2': '知舟问答2.0',
    'dsx_code_exercise': '动手学-代码练习解析',
    'zhihenRadar': '智痕雷达',
    'knowledge_analysis_simple': '知识解析（基础版）',
    'supervise_learn_stat': '教务督学',
    'shuati_answer_report': '智学刷题-答卷报告',
    'shuati_subjective_report': '智学刷题-主观题报告',
}

CHAT_MESSAGE_TYPES_MAP = {
    'chat_app': 'normal',
    'problem_solving': 'question',
    'code_optimization': 'code',
    'math_problem_solving': 'math_question',
    'en_article_judgment': 'normal',
}

INNER_APP_NOS = (
    'content_extraction',           # 爆文-内容提炼
    'article_generation',           # 爆文-文章生成
    'prompt_optimize',              # prompt优化
    'document_summary',             # 文档转换助手-内容转换
    'document_extraction',          # 文档转换助手-核心提炼
    'video_script',                 # 视频脚本生成器
)
