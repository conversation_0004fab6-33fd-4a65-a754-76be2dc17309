from django_ext import base_view
from app.api.dto import StudyGuidesDto, StudyGuidesAppDto
from app.services.study_guides_service import StudyGuidesService, StudyGuidesAppService
from django_ext.response import make_response
from app.api.validators import StudyGuidesValida<PERSON>, StudyGuidesAppValidator


class StudyGuidesView(base_view.BaseView):
    """<UNK>"""
    validator_class = StudyGuidesValidator

    def post(self, request, *args, **kwargs):
        dto: StudyGuidesDto = self.validate_request(request.data)

        try:
            response = StudyGuidesService.generate(dto,request.user)
            # return make_response({"result": response})
            return make_response(response)

        except Exception as e:
            error_message = f"解析过程中出现错误：{str(e)}"
            return make_response({"error": error_message}, status=500)


class StudyGuidesAppView(base_view.BaseView):
    """<UNK>"""
    validator_class = StudyGuidesAppValidator

    def post(self, request, *args, **kwargs):
        dto: StudyGuidesAppDto = self.validate_request(request.data)

        try:
            response = StudyGuidesAppService.generate(dto,request.user)
            # return make_response({"result": response})
            return make_response(response)

        except Exception as e:
            error_message = f"解析过程中出现错误：{str(e)}"
            return make_response({"error": error_message}, status=500)
