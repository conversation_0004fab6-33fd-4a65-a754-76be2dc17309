<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>知识解析基础版</title>
<script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
<style>
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f7fa;
    color: #333;
  }
  .header {
    text-align: center;
    margin-bottom: 30px;
  }
  .header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
  }
  .header p {
    color: #7f8c8d;
    font-size: 18px;
  }
  .search-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }
  .search-box {
    width: 400px;
    padding: 12px 15px;
    margin-right: 10px;
    border: 2px solid #3498db;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s;
  }
  .search-box:focus {
    border-color: #2980b9;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  }
  .search-button {
    padding: 12px 25px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
  }
  .search-button:hover {
    background: #2980b9;
  }
  .char-count {
    display: flex;
    align-items: center;
    padding: 0 15px;
    font-size: 14px;
    color: #7f8c8d;
  }
  .char-count span {
    font-weight: bold;
    color: #3498db;
    margin: 0 5px;
  }
  .knowledge-link {
    display: block;
    text-align: center;
    margin-bottom: 20px;
    font-size: 16px;
  }
  .knowledge-link a {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
  }
  .knowledge-link a:hover {
    text-decoration: underline;
  }
  .container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
  }
  .panel {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    padding: 20px;
    margin-bottom: 20px;
    flex: 1;
  }
  .panel-title {
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
  }
  .results-container {
    flex: 3;
  }
  .result-list {
    list-style: none;
    padding: 0;
  }
  .result-item {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ecf0f1;
  }
  .result-title {
    font-size: 20px;
    color: #2c3e50;
    margin-bottom: 8px;
  }
  .result-definition {
    font-size: 16px;
    color: #34495e;
    margin-bottom: 10px;
  }
  .deep-result-list {
    font-size: 16px;
    line-height: 1.6;
  }
  #searchDetails {
    white-space: pre-line;
    line-height: 1.6;
    font-size: 14px;
  }
  #searchDetails p {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 10px;
    border-left: 3px solid #3498db;
  }
  .search-keywords {
    margin-bottom: 15px;
  }
  .search-keyword {
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 8px;
    padding: 5px 12px;
    background-color: #e1f0fa;
    border-radius: 20px;
    font-size: 14px;
    color: #2980b9;
  }
  .prompt-button {
    padding: 8px 15px;
    background-color: #2ecc71;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
    margin-bottom: 10px;
    width: 100%;
    text-align: left;
  }
  .prompt-button:hover {
    background-color: #27ae60;
  }
  .save-button {
    padding: 10px 20px;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s;
    display: block;
    margin: 20px auto;
  }
  .save-button:hover {
    background-color: #c0392b;
  }
  /* 新增弹出层样式 */
  .promptManagementModal {
    display: none;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.2);
    z-index: 1000;
    width: 700px;
    max-width: 90%;
  }
  .modal-title {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 1px solid #ecf0f1;
    padding-bottom: 10px;
  }
  .prompt_input {
    resize: vertical;
    height: 300px;
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-family: monospace;
    font-size: 14px;
    margin-bottom: 15px;
  }
  .modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  .modal-button {
    padding: 8px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  }
  .confirm-button {
    background-color: #3498db;
    color: white;
  }
  .cancel-button {
    background-color: #95a5a6;
    color: white;
  }
  /* 遮罩层样式 */
  #overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
  .loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
  }
  .loading-content {
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    background-color: white;
    color: #2c3e50;
    text-align: center;
  }
  .loading-spinner {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .markdown-body {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  }
</style>
</head>
<body>

<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>正在处理您的请求，请稍候...</p>
    </div>
</div>

<div class="header">
  <h1>知识解析基础版</h1>
  <p>智能搜索与知识提取工具</p>
</div>

<div class="search-container">
  <input type="text" id="searchInput" class="search-box" placeholder="输入您想查询的知识点...">
  <button onclick="search()" class="search-button">搜索</button>
  <div class="char-count">已输入 <span id="queryCount">0</span> 字符</div>
</div>

<p class="knowledge-link"><a href="{% url 'knowledge_list_2' %}" target="_blank">查看完整知识点列表 →</a></p>

<div class="container">
  <div class="panel">
    <div class="panel-title">搜索设置</div>

    <div class="form-group" style="margin-bottom: 20px">
        <label for="search_type" style="display: block; margin-bottom: 8px; font-weight: 500;">查询方式:</label>
        <select id="search_type" name="search_type" style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ddd;">
            <option value="local">调用本地文档</option>
            <option value="llm">LLM直接回答</option>
        </select>
    </div>

    <div class="panel-title">提示词管理</div>

    <div>
        <button id="split_prompt_btn" class="prompt-button">1. 拆分搜索内容提示词</button>
        <div id="split_prompt_modal" class="promptManagementModal">
          <h2 class="modal-title">拆分搜索内容提示词</h2>
            <textarea id="split_prompt" class="prompt_input" name="split_prompt">{{ knowledge_query_split }}</textarea>
            <div class="modal-buttons">
                <button id="confirm_prompt_split" class="modal-button confirm-button" type="button">确定</button>
                <button id="cancel_prompt_split" class="modal-button cancel-button" type="button">取消</button>
            </div>
        </div>
    </div>

    <div id="local_prompt_wrap" style="margin-top: 15px;">
        <button id="local_prompt_btn" class="prompt-button">2. 本地文档搜索提示词</button>
        <div id="local_prompt_modal" class="promptManagementModal">
          <h2 class="modal-title">本地文档搜索提示词</h2>
            <textarea id="local_prompt" class="prompt_input" name="local_prompt">{{ knowledge_with_local }}</textarea>
            <div class="modal-buttons">
                <button id="confirm_prompt_local" class="modal-button confirm-button" type="button">确定</button>
                <button id="cancel_prompt_local" class="modal-button cancel-button" type="button">取消</button>
            </div>
        </div>
    </div>

    <div id="llm_prompt_wrap" style="margin-top: 15px;">
        <button id="llm_prompt_btn" class="prompt-button">3. LLM搜索提示词</button>
        <div id="llm_prompt_modal" class="promptManagementModal">
          <h2 class="modal-title">LLM搜索提示词</h2>
            <textarea id="llm_prompt" class="prompt_input" name="llm_prompt">{{ knowledge_with_llm }}</textarea>
            <div class="modal-buttons">
                <button id="confirm_prompt_llm" class="modal-button confirm-button" type="button">确定</button>
                <button id="cancel_prompt_llm" class="modal-button cancel-button" type="button">取消</button>
            </div>
        </div>
    </div>
  </div>

  <div class="panel results-container">
    <div class="panel-title">搜索结果</div>
    <div class="result-list" id="resultList">
      <div class="placeholder" style="text-align: center; padding: 40px 0; color: #7f8c8d;">
        <p>请输入搜索内容并点击"搜索"按钮获取结果</p>
      </div>
    </div>
    <div class="deep-result-list markdown-body" id="deep-result-list"></div>
    <button onclick="saveToDatabase()" class="save-button">👍 点赞此结果</button>
  </div>

  <div class="panel">
    <div class="panel-title">搜索过程</div>
    <div class="search-keywords" id="searchKeywords"></div>
    <div id="searchDetails">
      <div class="placeholder" style="text-align: center; padding: 20px 0; color: #7f8c8d;">
        <p>搜索过程将显示在这里</p>
      </div>
    </div>
  </div>
</div>

<!-- 新增遮罩层 -->
<div id="overlay"></div>

<script>
// 提示词内容初始化
let temp_split_prompt = `{{ knowledge_query_split }}`;
let temp_local_prompt = `{{ knowledge_with_local }}`;
let temp_llm_prompt = `{{ knowledge_with_llm }}`;
let message_id = null;

function search() {
    const input = $('#searchInput').val();
    if (!input) {
        alert('请输入搜索内容');
        return;
    }

    const search_type = $('#search_type').val();
    const local_prompt = $('#local_prompt').val();
    const llm_prompt = $('#llm_prompt').val();
    const split_prompt = $('#split_prompt').val();

    const resultList = document.getElementById('resultList');
    const deepResultList = document.getElementById('deep-result-list');
    resultList.innerHTML = '<div class="placeholder" style="text-align: center; padding: 40px 0; color: #7f8c8d;"><p></p></div>';
    deepResultList.innerHTML = '';

    const searchKeywords = document.getElementById('searchKeywords');
    searchKeywords.innerHTML = '';

    const searchDetails = document.getElementById('searchDetails');
    searchDetails.innerHTML = '<div class="placeholder" style="text-align: center; padding: 20px 0; color: #7f8c8d;"><p>正在处理搜索过程...</p></div>';

    $('#loadingOverlay').css('display', 'flex');

    message_id = null;
    const url = "{% url 'knowledge_search_result_2' %}";
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: input,
            search_type,
            split_prompt,
            local_prompt,
            llm_prompt,
        })
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('网络响应错误');
        }
        return response.body;
      })
      .then(stream => {
        let streamComplete = false;
        const reader = stream.getReader();
        const decoder = new TextDecoder();
        let result_text = '';

        function readChunk() {
          reader.read().then(({ done, value }) => {
            if (done) {
              streamComplete = true;
              console.log('数据流完成');
              find_tracing();
              return;
            }
            const textChunk = decoder.decode(value, { stream: true });

            if (textChunk.startsWith("data: ")) {
                const json_str = textChunk.slice(6);
                try {
                    const jsonData = JSON.parse(json_str);
                    if (!message_id) {
                        message_id = jsonData.message_id;
                    }
                    if (jsonData.answer) {
                        result_text += jsonData.answer;
                        deepResultList.innerHTML = marked.parse(result_text);
                    }
                } catch (error) {
                    console.error('JSON解析错误', json_str, error);
                }
            }
            readChunk();
          }).catch(error => {
            console.error('读取数据流错误', error);
            $('#loadingOverlay').css('display', 'none');
          });
        }
        readChunk();
      })
      .catch(error => {
          $('#loadingOverlay').css('display', 'none');
          console.error('请求错误:', error);
          resultList.innerHTML = '<div class="error" style="text-align: center; padding: 40px 0; color: #e74c3c;"><p>请求失败: ' + error.message + '</p></div>';
      });
}

function saveToDatabase() {
    if (!message_id) {
        alert('请先进行搜索获取结果');
        return;
    }

    // 显示加载动画
    const saveButton = document.querySelector('.save-button');
    const originalText = saveButton.textContent;
    saveButton.textContent = '处理中...';
    saveButton.disabled = true;

    fetch("{% url 'save_knowledge' %}", {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message_id: message_id,
            save_to_db: true,
        }),
    })
    .then(response => response.json())
    .then(data => {
        saveButton.textContent = originalText;
        saveButton.disabled = false;

        if (data.success) {
            alert('点赞成功！当前点赞次数：' + data.like_count);
        } else {
            alert('点赞失败：' + data.reason);
        }
    })
    .catch(error => {
        console.error('点赞失败:', error);
        saveButton.textContent = originalText;
        saveButton.disabled = false;
        alert('点赞失败，请重试');
    });
}

function find_tracing() {
    if (!message_id) return;

    $.ajax({
        type: 'POST',
        url: "{% url 'message_tracing' %}",
        contentType: 'application/json',
        data: JSON.stringify({
            message_id,
        }),
        success: function(response) {
            $('#loadingOverlay').css('display', 'none');
            const results = response.data;
            const searchDetails = document.getElementById('searchDetails');
            searchDetails.innerHTML = '';

            if (results.tracing && results.tracing.length > 0) {
                results.tracing.forEach(function(traceDetail) {
                    const detailElement = document.createElement('p');
                    detailElement.textContent = traceDetail;
                    searchDetails.appendChild(detailElement);
                });
            } else {
                searchDetails.innerHTML = '<div class="placeholder" style="text-align: center; padding: 20px 0; color: #7f8c8d;"><p>无搜索过程详情</p></div>';
            }
        },
        error: function(xhr, status, error) {
            $('#loadingOverlay').css('display', 'none');
            console.error('搜索过程详情请求失败:', error);
            const searchDetails = document.getElementById('searchDetails');
            searchDetails.innerHTML = '<div class="error" style="text-align: center; padding: 20px 0; color: #e74c3c;"><p>获取搜索过程失败</p></div>';
        }
    });
}

$(function () {
    function togglePrompt() {
        const search_type = $('#search_type').val();

        // 根据查询方式显示相应的提示词管理
        if (search_type === 'local') {
            $('#local_prompt_wrap').show();
            $('#llm_prompt_wrap').show();
        } else if (search_type === 'llm') {
            $('#local_prompt_wrap').hide();
            $('#llm_prompt_wrap').show();
        }
    }

    // 初始化显示状态
    togglePrompt();

    // 当查询方式改变时更新提示词显示
    $('#search_type').on('change', togglePrompt);

    // 字符计数功能
    function updateCharCount() {
        const count = $('#searchInput').val().length;
        $('#queryCount').text(count);
    }
    $('#searchInput').on('input', updateCharCount);
    updateCharCount();

    // 回车键搜索
    $('#searchInput').on('keydown', function(e){
        if (e.which === 13) {
            e.preventDefault();
            search();
        }
    });

    // 提示词管理弹窗控制
    function setupPromptModal(buttonId, modalId, textareaId, tempVar, confirmId, cancelId) {
        $(buttonId).click(function() {
            $(modalId).show();
            $('#overlay').show();
            $(textareaId).val(tempVar);
        });

        $(confirmId).click(function() {
            tempVar = $(textareaId).val();
            $(modalId).hide();
            $('#overlay').hide();
        });

        $(cancelId).click(function() {
            $(modalId).hide();
            $('#overlay').hide();
        });
    }

    // 设置各个提示词弹窗
    setupPromptModal(
        '#split_prompt_btn',
        '#split_prompt_modal',
        '#split_prompt',
        temp_split_prompt,
        '#confirm_prompt_split',
        '#cancel_prompt_split'
    );

    setupPromptModal(
        '#local_prompt_btn',
        '#local_prompt_modal',
        '#local_prompt',
        temp_local_prompt,
        '#confirm_prompt_local',
        '#cancel_prompt_local'
    );

    setupPromptModal(
        '#llm_prompt_btn',
        '#llm_prompt_modal',
        '#llm_prompt',
        temp_llm_prompt,
        '#confirm_prompt_llm',
        '#cancel_prompt_llm'
    );

    // 点击遮罩层关闭所有弹窗
    $('#overlay').click(function() {
        $('.promptManagementModal').hide();
        $(this).hide();
    });
});
</script>

</body>
</html>