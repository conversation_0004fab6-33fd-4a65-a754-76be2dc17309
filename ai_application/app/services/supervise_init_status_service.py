import json
import math
from collections import Counter
import pdb
import time
from langchain.chains.llm import <PERSON><PERSON>hain
from django.conf import settings
from langchain_openai import ChatOpenAI
from scipy.special import erf
from scipy.optimize import minimize
from contextlib import contextmanager

from app.models.college_analysis_new import UndergraduateCollegeInfo
from app.api.dto import ChatMessageDto
from app.models.main_subject import SuperViseInitStudentStatus
from api_client.yantucs_data.client import yantucs_data_client
from app.errors import AppNotFoundError
from app.models import App, Account

from app.services.college_analysis_new_service import calculate_college_gap_coefficient, get_gdp_coefficient_by_city
from app.services.app_generate_service import AppGenerateService
import logging
from app.models import MajorRelation, MajorInfo, UndergraduateMajorCourse

from datetime import date,datetime

logger = logging.getLogger(__name__)


MAJOR_MATH_BASE_SCORES = {
    # 学术学位
    "01": 39.3,     
    "02": 39.5,    
    "03": 39.2,      
    "04": 40.1,    
    "05": 33.3,     
    "06": 34.3,    
    "07": 34.0,      
    "08": 34.0,     
    "09": 33.3,     
    "10": 33.3,     
    "11": 34.0,  
    "12": 39.3,    
    "13": 37.3,    
    "14": 34.0,   
}


class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        elif hasattr(obj, 'isoformat'):  # Handle Django DateField
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):  # Handle Django model instances
            return str(obj)
        return super().default(obj)


class CustomNonlinearFunctionV3:
    # --- 1. 优化设置 ---
    # 定义模型函数，p是参数列表 [A1, A2, B]
    @staticmethod
    def model_function(x, p):
        A1, A2, B = p
        A3 = 10.0 - A1 - A2
        C1, C2, C3 = 0.1, 0.5, 4.0
        term1 = A1 * erf(B * (x - C1))
        term2 = A2 * erf(B * (x - C2))
        term3 = A3 * erf(B * (x - C3))
        return term1 + term2 + term3

    # 定义误差函数，用于优化
    @staticmethod
    def error_function(p):
        A1, A2, B = p
        # 惩罚无效的参数组合
        if not (0 < A1 < 10 and 0 < A2 < 10 and 0 < (A1 + A2) < 10 and B > 0):
            return 1e9
        
        # 直接调用静态方法，不通过类名引用
        y_at_2_5 = CustomNonlinearFunctionV3.model_function(2.5, p)
        y_at_4 = CustomNonlinearFunctionV3.model_function(4.0, p)
        
        # 计算与目标的平方误差
        error = (y_at_2_5 - 3.0)**2 + (y_at_4 - 7.0)**2
        return error

    # --- 2. 优化参数 ---
    _optimized = False
    A1_opt = 1.0
    A2_opt = 2.0
    B_opt = 1.5
    A3_opt = 10.0 - A1_opt - A2_opt

    @classmethod
    def _optimize_parameters(cls):
        """执行参数优化"""
        if cls._optimized:
            return
            
        initial_guess = [1.0, 2.0, 1.5]
        bounds = ((0.01, 9.99), (0.01, 9.99), (0.1, 5.0))
        
        result = minimize(cls.error_function, initial_guess, method='L-BFGS-B', bounds=bounds)
        
        cls.A1_opt, cls.A2_opt, cls.B_opt = result.x
        cls.A3_opt = 10.0 - cls.A1_opt - cls.A2_opt
        cls._optimized = True

    # --- 3. 定义最终函数 ---
    @classmethod
    def custom_nonlinear_function(cls, x):
        """
        一个经过优化的、具有三个拐点的非线性函数。
        由三个高斯误差函数(erf)复合而成。

        该函数具有以下特性：
        - 在 x=0.1, x=0.5, 和 x=4.0 处有三个拐点。
        - 在 x=2.5 时，函数值约等于 3。
        - 在 x=4.0 时，函数值约等于 7。
        - 渐近线为 y = ±10。

        Args:
            x (float or np.ndarray): 输入值或NumPy数组。

        Returns:
            float or np.ndarray: 计算后的函数输出值。
        """
        # --- 使用经过数值优化找到的最佳参数 ---
        cls._optimize_parameters()
        A1 = cls.A1_opt
        A2 = cls.A2_opt
        A3 = cls.A3_opt
        B = cls.B_opt
        C1, C2, C3 = 0.1, 0.5, 4.0

        term1 = A1 * erf(B * (x - C1))
        term2 = A2 * erf(B * (x - C2))
        term3 = A3 * erf(B * (x - C3))
        
        return term1 + term2 + term3


from collections import OrderedDict

class SuperviseInitStatusService:
    """处理初始学习状态数据的服务类"""
    
    # 缓存设置
    CACHE_TIMEOUT = 60 * 60  # 1小时
    CACHE_MAX_ENTRIES = 1000  # 最大缓存条目数
    _CACHE = OrderedDict()  # LRU缓存数据结构

    @classmethod
    def _get_cache(cls, key):
        """自定义缓存获取方法"""
        return cls._CACHE.get(key)

    @classmethod
    def _set_cache(cls, key, value):
        """自定义缓存设置方法（LRU策略）"""
        # 如果键已存在，先删除以更新位置
        if key in cls._CACHE:
            del cls._CACHE[key]
        # 添加新条目
        cls._CACHE[key] = value
        # 移动到最后（表示最近使用）
        cls._CACHE.move_to_end(key)
        
        # 检查缓存大小并移除最旧条目
        if len(cls._CACHE) > cls.CACHE_MAX_ENTRIES:
            oldest_key = next(iter(cls._CACHE))
            del cls._CACHE[oldest_key]
            logger.debug(f"LRU缓存已满，移除最旧条目: {oldest_key}")

    @classmethod
    def college_analysis_with_goal(cls, bachelor_major_code, bachelor_college_code, target_college_code,
                                   target_major_code):
        """
        重写父类方法，使用yantucs_data_client获取院校专业信息
        """
        # 基础难度计算:基础难度 = 复录比 × 分数线分位值 × 院校专业系数
        target_results = yantucs_data_client.get_school_info(target_college_code, target_major_code)
        
        # 判断是否为空列表
        if not target_results:
            return {
                "error": "未找到目标院校和专业的相关信息，请检查输入的院校代码或专业代码是否正确。",
                "code": 404
            }
            
        # 找到招生人数最多的学校信息
        max_enroll_num = -1
        target_result = None
        for school in target_results:
            if school.get("enroll_num", 0) > max_enroll_num:
                max_enroll_num = school.get("enroll_num", 0)
                target_result = school
                
        # 提取 re_exam_num 和 enroll_num
        re_exam_num = target_result.get('re_exam_num', 0)
        enroll_num = target_result.get('enroll_num', 0)
        school_level_display = target_result.get('school_level_display', '普通院校')
        print(f"复试人数:{re_exam_num},录取人数:{enroll_num}")
        # 计算复录比
        if re_exam_num > 0 and enroll_num > 0:
            admission_ratio = re_exam_num / enroll_num
        else:
            # 根据院校层次设定默认复录比
            default_ratio_mapping = {
                "985工程": 2.2,
                "211工程": 1.8,
                "双一流大学": 1.6,
                "普通院校": 1.4
            }
            admission_ratio = default_ratio_mapping.get(school_level_display, 1.4)  # 默认普通院校

        print(f"复录比: {admission_ratio}")

        # 计算分数线分位值
        # 提取专业复试平均分
        major_re_exam_info = target_result.get("major_re_exam_info", {})
        average_professional_line = major_re_exam_info.get("average", None)
        lowest_professional_line = major_re_exam_info.get("lowest", None)
        highest_professional_line = major_re_exam_info.get("highest", None)

        # 提取专业线
        # major_score_info = target_result.get("major_score_info", {})
        # professional_line = major_score_info.get("total", None)
        # if average_professional_line is None:
        #     raise ValueError("未找到专业线信息")

        # 获取学校代码和专业代码
        school_code = target_result.get("school_code")
        major_code = target_result.get("major_code")

        # 查找国家线
        national_line = None
        if school_code and major_code:
            # 尝试精确匹配
            matched = MajorInfo.objects.filter(college_code=school_code, major_code=major_code).first()
            if not matched:
                # 使用 major_code 前四位进行模糊匹配
                prefix = major_code[:4]
                matched = MajorInfo.objects.filter(college_code=school_code, major_code__startswith=prefix).first()

            if matched:
                national_line = matched.national_line_score

        # 计算分数线分位值
        default_score_position_mapping = {
            '清北': 0.09,
            'C9（除清北）': 0.08,
            "985工程": 0.07,
            "211工程": 0.05,
            "双一流大学": 0.04,
            "普通院校": 0.02
        }
        print(f"national_line在这边啊啊啊啊啊啊:{national_line}")
        if average_professional_line is not None and national_line is not None:
            try:
                score_position_value = max(0, (average_professional_line - national_line) / national_line)
            except ZeroDivisionError:
                score_position_value = 0  # 防止除以零错误
        else:
            if target_college_code in ['10001', '10003']:
                college_level = '清北'
            elif target_college_code in ['10335', '10248', '10246', '10358', '10284', '10213', '10698']:
                college_level = 'C9（除清北）'
            else:
                college_level = target_result.get("school_level_display", "普通院校")
            score_position_value = default_score_position_mapping.get(college_level, 0.02)


        # 计算院校专业系数 = 院校层级系数 × 城市 GDP 修正系数) × 学科评级系数 / 10
        college_level_score = {
            '清北': 10,
            'C9（除清北）': 9,
            "985工程": 7,
            "211工程": 5,
            "双一流大学": 4,
            "普通院校": 3,
            "双非院校": 3,
            "境外高等教育机构在海南自由贸易港设立的实施理工农医类学科专业的学校": 3,
            "民办": 3,
            "专科": 3,
            "中外合作办学及内地与港澳合作办学": 3,
        }

        # 特殊处理：清华大学（10003）和北京大学（10001）
        if target_college_code in ['10001', '10003']:
            college_level = '清北'
        elif target_college_code in ['10335', '10248', '10246', '10358', '10284', '10213', '10698']:
            college_level = 'C9（除清北）'
        else:
            college_level = target_result.get("school_level_display", "普通院校")

        college_level_coefficient = college_level_score.get(college_level, 3)
        print(f"目标院校层级得分：{college_level_coefficient}")

        # 获取专业评级
        major_level = target_result.get("major_eval")
        if major_level == '':
            if len(major_code) >= 4:
                prefix = major_code[:4]  # 取前四位
                # 查询 ai_major_info 表中 major_code 以 prefix 开头的数据
                matched = MajorInfo.objects.filter(college_code=school_code, major_code=prefix).first()
                if matched and matched.major_evaluation:
                    major_level = matched.major_evaluation
        print(f"专业评级: {major_level}")

        # 定义评估等级映射(参考专业评级划分比例进行赋分)
        major_evaluation_coefficient_mapping = {
            'A+': 9.86,
            'A': 9.60,
            'A-': 9.14,
            'B+': 8.29,
            'B': 7.14,
            'B-': 6,
            'C+': 4.86,
            'C': 3.71,
            'C-': 2.57
        }
        major_evaluation_coefficient = major_evaluation_coefficient_mapping.get(major_level, 6)
        print(f"专业评级得分: {major_evaluation_coefficient}")

        # 计算城市GDP修正系数
        city_gdp_coefficient = get_gdp_coefficient_by_city(target_result)
        college_major_coefficient = college_level_coefficient * city_gdp_coefficient * major_evaluation_coefficient / 10
        print(f"城市 GDP 修正系数: {city_gdp_coefficient}")
        print(f"院校专业系数: {college_major_coefficient}")

        # 计算跨门类惩罚系数（与关联度相关联，暂时假设 bachelor_major_code是二级门类例如“0101哲学类”）
        # 提取 target_major_code 的前四位
        target_major_code_prefix = target_major_code[:4]
        print(f"本科二级门类: {bachelor_major_code},目标二级门类: {target_major_code_prefix}")
        # 查询 MajorRelation 表
        relation_record = MajorRelation.objects.filter(
            undergraduate_second_category=bachelor_major_code,
            graduate_second_category_code=target_major_code_prefix
        ).first()

        # 获取 relation_level
        if relation_record:
            relation_level = relation_record.relation_level
            print(f"关联度等级: {relation_level}")
        else:
            relation_level = None
            print("未找到对应的关联度记录")

        # 根据 relation_level 确定跨门类惩罚系数
        if relation_level is not None:
            if relation_level <= 2:
                penalty_coefficient = 0.00
                print("高度相关，无惩罚")
            elif 2 < relation_level <= 4:
                penalty_coefficient = 0.15
                print("中度相关，轻微惩罚")
            elif 4 < relation_level <= 7:
                penalty_coefficient = 0.4
                print("低度相关，中等惩罚")
            else:
                penalty_coefficient = 0.8
                print("无关联，强惩罚")
        else:
            # 如果未找到关联度，默认使用最高惩罚系数 0.8 或抛出异常
            penalty_coefficient = 0.8
            print("未找到关联度，采用默认无关联惩罚系数 0.8")

        print(f"跨门类惩罚系数: {penalty_coefficient}")

        # 计算知识差异惩罚系数(暂时使用大模型来给出评分)：知识差异惩罚系数 = 0.1 × (1 - 课程匹配度)
        # 找到本科核心课程
        major_course_obj = UndergraduateMajorCourse.objects.filter(
            second_category=bachelor_major_code
        ).first()

        if major_course_obj:
            undergraduate_major_course = major_course_obj.core_courses
        else:
            undergraduate_major_course = "未找到核心课程信息"
        print(f"本科核心课程: {undergraduate_major_course}")
        # 提取 exam_range 的第一个考试科目信息
        exam_range = target_result.get("exam_range", [])
        if exam_range:
            first_exam = exam_range[0]
            exam_text = (
                f"政治科目：{first_exam.get('polity', '')}\n"
                f"专业课一：{first_exam.get('subject1', '')}\n"
                f"专业课二：{first_exam.get('subject2', '')}"
            )
        else:
            exam_text = "未找到考试科目信息"

        # 使用大模型对知识差异惩罚系数进行打分（后期可能会修改）
        prompt_template = """
        请分析以下本科核心课程与研究生考试科目的知识匹配程度，按照以下的评分规则，给出一个 0 到 1 之间的评分：

        | 匹配度范围 | 评价等级     | 详细描述                                       |
        |------------|--------------|------------------------------------------------|
        | 0.8        | 高度匹配     | 核心课程覆盖充分，知识储备充足                 |
        | 0.6 - 0.8  | 中度匹配     | 基础课程匹配良好，需补充 1-2 门               |
        | 0.4 - 0.6  | 低度匹配     | 仅基础课程匹配，需系统补充                     |
        | < 0.4      | 严重脱节     | 知识体系差异巨大                               |

        【本科核心课程】
        {undergraduate_course}

        【研究生考试科目】
        {exam_subjects}

        务必请只输出一个数字，保留一位小数。
        """
        from langchain_core.prompts import PromptTemplate
        
        prompt = PromptTemplate.from_template(prompt_template)

        llm = ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="doubao-seed-1-6-250615",
            temperature=0.3,    
        )

        chain = LLMChain(llm=llm, prompt=prompt)

        response = chain.run({
            "undergraduate_course": undergraduate_major_course,
            "exam_subjects": exam_text
        })
        print(f"大模型返回结果为: {response}")
        try:
            knowledge_difference_score = float(response.strip())
        except ValueError:
            print("大模型返回非数值结果，默认设置为 0.5")
            knowledge_difference_score = 0.5

        print(f"知识差异惩罚系数（匹配度得分）: {knowledge_difference_score}")

        knowledge_penalty_coefficient = 0.1 * (1 - knowledge_difference_score)

        print(f"知识差异惩罚系数为: {knowledge_penalty_coefficient}")

        # 增加院校跨度修正系数
        # 院校跨度数据=目标院校系数/ 本科院校系数
        # undergraduate_college_level = MajorInfo.objects.filter(college_code=bachelor_college_code).first().college_level
        undergraduate_college_level = UndergraduateCollegeInfo.objects.filter(
            undergraduate_code=bachelor_college_code).first().level_display
        undergraduate_college_coefficient = college_level_score.get(undergraduate_college_level, 3)

        college_gap_score = college_level_coefficient / undergraduate_college_coefficient

        print(f"院校跨度得分：{college_gap_score}")
        college_gap_coefficient = calculate_college_gap_coefficient(college_gap_score)
        print(f"院校跨度修正系数：{college_gap_coefficient}")
        # 计算总难度=基础难度× (1 + 跨门类惩罚系数 + 知识差异惩罚系数)× 院校跨度修正系数
        base_difficulty = admission_ratio * score_position_value * college_major_coefficient
        total_difficulty = base_difficulty * (
                    1 + penalty_coefficient + knowledge_penalty_coefficient) * college_gap_coefficient

        # 提取专业复试平均分
        major_re_exam_info = target_result.get("major_re_exam_info", {})
        average_professional_line = major_re_exam_info.get("average", None)

        print(f"进入复试的平均分为: {average_professional_line}")
        print(f"基础难度为: {base_difficulty}")
        print(f"跨门类惩罚系数为: {penalty_coefficient}")
        print(f"知识差异惩罚系数为: {knowledge_penalty_coefficient}")
        print(f"总难度为: {total_difficulty}")
        return {
            "总难度": total_difficulty,
            "知识匹配度": knowledge_difference_score,
        }

    @classmethod
    def get_report(cls, record: SuperViseInitStudentStatus):
        start_time = time.time()
        logger.info("Getting account and app instances")
        
        logger.info(f"Record ID: {record.id}")
        from_account = Account.objects.first()
        # 获取在读生分析实例
        supervise_init_undergraduated_status_stat: App = App.objects.filter(
            is_deleted=False, 
            app_type='supervise_init_undergraduated_st'
        ).first()
        
        if not supervise_init_undergraduated_status_stat:
            raise AppNotFoundError(detail="未找到对应的在读生 App 实例")
        logger.info(f"Using App: {supervise_init_undergraduated_status_stat.app_no}")
        # 处理数据
        processed_data = cls.parse_init_undergraduated_data(record)

        # 创建 ChatMessageDto 对象
        chat_dto = ChatMessageDto(
            app_id=supervise_init_undergraduated_status_stat.app_no,
            query=json.dumps(processed_data, ensure_ascii=False, cls=DateTimeEncoder),
            stream=False,
            inputs=processed_data,
        )

        logger.info("UnderGraduate Chat DTO Created")
        
        # 生成报告
        undergraduated_res = AppGenerateService.generate(chat_dto, from_account)
        content = undergraduated_res.get('answer', '')
        record.analysis = content
        record.status = 'SUCCESS'
        record.query = processed_data
        record.save()
        end_time = time.time()
        logger.info(f"生成报告耗时: {end_time - start_time} 秒")
        return content
        
    @classmethod
    def parse_init_undergraduated_data(cls, record:SuperViseInitStudentStatus):

        try:
            # 获取本科阶段课程信息
            graduation_courses = cls.get_graduation_courses(record.graduation_major_code)
            target = record.target
            # 初始化变量
            english_level = None
            # 检查单个目标院校的政治科目情况
            has_any_politics_subject = False
            temp_base_info = cls.parse_base_info(
                target['target_school_code'],
                target['target_major_code'],
            )
            temp_policy = temp_base_info.get('policy', '无')
            if cls.is_politics_subject(temp_policy):
                has_any_politics_subject = True
            
            # 只有当有政治科目时才计算政治水平评估
            political_score = None
            if has_any_politics_subject:
                political_score = 45

            # 检查单个目标院校是否有数学科目，并计算数学分数
            has_any_math_subject = False
            math_score = None
            # 直接使用record.target而不是循环
            temp_base_info = cls.parse_base_info(
                target['target_school_code'],
                target['target_major_code'],
            )
            # 确定实际的subject1（考虑政治科目转移的情况）
            temp_policy = temp_base_info.get('policy', '无')
            temp_subject1 = temp_base_info.get('subject1', '无')
            actual_temp_subject1 = temp_policy if not cls.is_politics_subject(temp_policy) and temp_policy != '无' else temp_subject1

            if actual_temp_subject1 in ['(301)数学一', '(302)数学二', '(303)数学三']:
                has_any_math_subject = True
                # 直接计算数学分数（不需要检查是否为第一个）
                math_score = cls.estimate_math_score(
                    exam_type = actual_temp_subject1,
                    assessment = record.math_mastery,
                    studied_subjects = record.math_subjects,
                    major_code = record.graduation_major_code[:2]
                )
            # 添加调试日志
            logger.info(f"Debug - base_info keys: {list(temp_base_info.keys())}")
            logger.info(f"Debug - base_info policy: '{temp_base_info.get('policy', 'KEY_NOT_FOUND')}' (type: {type(temp_base_info.get('policy'))})")
            logger.info(f"Debug - base_info subject1: '{temp_base_info.get('subject1', 'KEY_NOT_FOUND')}'")
            logger.info(f"Debug - base_info subject2: '{temp_base_info.get('subject2', 'KEY_NOT_FOUND')}')")

            # 获取英语水平评估（只计算一次）
            if english_level is None:
                language_subject = temp_base_info.get('language')
                # 检查是否为标准英语科目
                if language_subject in ['(201)英语一', '(204)英语二']:
                    english_level = cls.parse_English_level(
                        language_subject,
                        record.english_level
                    )
                else:
                    # 如果不是标准英语科目，直接赋值50
                    english_level = 50

            # 获取原始的政治和subject1信息
            original_policy = temp_base_info.get('policy', '无')
            original_subject1 = temp_base_info.get('subject1', '无')

            # 确定实际的subject1（如果政治科目不是'政治'，则使用政治科目）
            actual_subject1 = original_policy if original_policy != '(101)思想政治理论' and original_policy != '无' else original_subject1

            # 检查实际的subject1是否为数学科目
            is_math_subject = actual_subject1 in ['(301)数学一', '(302)数学二', '(303)数学三']

            # 获取专业关联度
            relation = cls.get_major_relation(
                record.graduation_major_code,
                record.target['target_major_code']
            )

            # 如果实际的subject1不是数学，则计算subject1的专业课分数
            subject1_professional_score = None
            if not is_math_subject and actual_subject1 != '无':
                subject1_professional_score = cls.get_professional_score(relation, record.academic_performance)

                # 如果subject2为无，说明subject1满分是300分，需要乘以2
                subject2 = temp_base_info.get('subject2', '无')
                if subject2 == '无' :
                    if original_policy == '(101)思想政治理论':
                        subject1_professional_score = subject1_professional_score * 2
                    else:
                        subject1_professional_score = (subject1_professional_score * 4)/3
            
            # # 解析专业课代码并获取教材信息
            # text = base_info.get('subject2', '')
            # professional_code_match = re.search(r'\((\d+)\)', text)
            # textbooks = []
            # if professional_code_match:
            #     textbooks = cls.get_textbooks(professional_code_match.group(1))
            
            # 构建考试范围信息
            # 如果政治科目不是'政治'，则将政治信息转移到subject1
            if original_policy != '(101)思想政治理论':
                exam_range = {
                    'policy': '无',
                    'language': temp_base_info.get('language', '无'),
                    'subject1': original_policy if original_policy != '无' else original_subject1,
                    'subject2': temp_base_info.get('subject2', '无')
                }
            else:
                exam_range = {
                    'policy': original_policy,
                    'language': temp_base_info.get('language', '无'),
                    'subject1': original_subject1,
                    'subject2': temp_base_info.get('subject2', '无')
                }
            
            college_analysis = cls.college_analysis_with_goal(record.graduation_major_code, record.graduation_school_code, target['target_school_code'], target['target_major_code'])
            
            
            # 查找国家线
            national_line = None
            if target['target_school_code'] and target['target_major_code']:
                # 尝试精确匹配
                matched = MajorInfo.objects.filter(college_code=target['target_school_code'], major_code=target['target_major_code']).first()
                if not matched:
                    # 使用 major_code 前四位进行模糊匹配
                    prefix = target['target_major_code'][:4]
                    matched = MajorInfo.objects.filter(college_code=target['target_school_code'], major_code__startswith=prefix).first()

                if matched:
                    national_line = matched.national_line_score

            national_line = int(national_line) + 10 if national_line else 100
            

            base_score = national_line+10
            if temp_base_info.get('major_enroll_info').get('平均分'):
                base_score = int(temp_base_info.get('major_enroll_info')['平均分'])
            target_score = round(min(base_score + CustomNonlinearFunctionV3.custom_nonlinear_function(college_analysis.get("总难度")), temp_base_info.get('major_enroll_info').get('最高分')))
            target_score = round(max(target_score, temp_base_info.get('major_enroll_info').get('最低分')))

            # 构建专业课水平列表
            professional_levels = []

            # 添加调试日志
            logger.info(f"Debug - 构建专业课水平:")
            logger.info(f"Debug - original_policy: '{original_policy}' (type: {type(original_policy)})")
            logger.info(f"Debug - original_subject1: '{original_subject1}'")
            logger.info(f"Debug - actual_subject1: '{actual_subject1}'")
            logger.info(f"Debug - is_math_subject: {is_math_subject}")
            logger.info(f"Debug - subject1_professional_score: {subject1_professional_score}")

            # 如果政治科目不是'政治'且不是'无'，说明政治科目转移到了subject1
            condition1 = original_policy != '(101)思想政治理论' and original_policy != '无'
            logger.info(f"Debug - 条件1 (政治科目转移): {condition1}")
            if condition1:
                logger.info(f"Debug - 使用政治科目作为专业课: {original_policy}")
                # 使用政治科目作为专业课
                professional_levels.append({
                    "科目名称": original_policy,
                    "预估分数": subject1_professional_score,
                    "科目类型": "专业课"
                })
            elif not is_math_subject and actual_subject1 != '无':
                logger.info(f"Debug - 使用实际subject1作为专业课: {actual_subject1}")
                # 如果实际的subject1不是数学且不是从政治转移来的，添加subject1的信息
                professional_levels.append({
                    "科目名称": actual_subject1,
                    "预估分数": subject1_professional_score,
                    "科目类型": "专业课"
                })
            else:
                logger.info(f"Debug - 不满足任何条件，专业课水平为空")

            # 处理subject2（如果不为"无"）
            subject2 = temp_base_info.get('subject2', '无')
            if subject2 != '无':
                subject2_professional_score = cls.get_professional_score(relation, record.academic_performance)
                professional_levels.append({
                    "科目名称": subject2,
                    "预估分数": subject2_professional_score,
                    "科目类型": "专业课"
                })

            # 如果实际的subject1是数学且没有subject2，或者没有任何专业课且政治是真正的政治科目，保持原有的专业课水平计算
            # if (is_math_subject and subject2 == '无') or (actual_subject1 == '无' and subject2 == '无' and original_policy == '(101)思想政治理论'):
            #     traditional_professional_score = cls.get_professional_score(relation, record.academic_performance)
            #     professional_levels.append({


            #         "科目名称": "传统专业课",
            #         "预估分数": traditional_professional_score,
            #         "科目类型": "专业课"
            #     })

            # 检查是否为已毕业状态并应用系数到专业课预估分数
            if hasattr(record, 'study_status') and record.study_status == "已毕业":
                coefficient = 1.0
                if hasattr(record, 'graduation_years'):
                    coefficient = cls._get_graduation_year_coefficient(record.graduation_years)
                
                # 对professional_levels中的所有预估分数应用系数
                for subject in professional_levels:
                    if "预估分数" in subject and subject["预估分数"] is not None:
                        subject["预估分数"] = round(subject["预估分数"] * coefficient)

            # 构建单个目标院校的完整信息
            target_info = {
                "目标院校": target['target_school'],
                "目标院校层级": temp_base_info['school_level_display'],
                "目标专业": target['target_major'],
                "目标专业代码": target['target_major_code'],
                "考试范围": exam_range,
                "录取信息": temp_base_info.get('major_enroll_info', {}),
                "专业课水平": professional_levels,
                # "目标分数": target.get('target_score', '无'),
                "预估目标分数": target_score,
                "知识匹配度": college_analysis.get("知识匹配度")
            }
            logger.info(f"Processed target school: {target['target_school']} - {target['target_major']}")

            # 构建最终的分析数据
            parsed_data = {
                "基础信息": {
                    "学历层次": record.education_level,
                    "本科院校": record.graduation_school,
                    "本科专业": record.graduation_major,
                    "本科阶段核心课程": graduation_courses
                },
                "学习状态": {
                    "成绩排名": cls.parse_academic_performance(record.academic_performance),
                    "计划生成日期": record.date.isoformat() if isinstance(record.date, (date, datetime)) else record.date,
                },
                "考试时间": record.exam_date.isoformat() if isinstance(record.exam_date, (date, datetime)) else record.exam_date,
                "学科能力评估": cls._build_subject_assessment(
                    has_any_math_subject, math_score, record, english_level, political_score, has_any_politics_subject
                ),
                "目标院校信息": target_info
            }
            
            return parsed_data



        except Exception as e:
            logger.error(f"解析初始学习状态数据失败: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "message": str(e)
            }

    @classmethod
    def _get_graduation_year_coefficient(cls, graduation_years):
        """根据毕业年限获取系数"""
        if graduation_years == "1年":
            return 0.6
        elif graduation_years == "2-3年":
            return 0.5
        elif graduation_years == "4-5年":
            return 0.3
        elif graduation_years == "6年及以上":
            return 0.1
        else:
            return 1.0  # 默认不调整

    @classmethod
    def _build_subject_assessment(cls, has_math_subject, math_score, record, english_level, political_score, has_politics_subject):
        """构建学科能力评估，根据是否有数学科目和政治科目决定是否包含相应项"""
        assessment = {
            "外语水平": english_level,
        }

        # 检查是否为已毕业状态并应用系数
        coefficient = 1.0
        if hasattr(record, 'study_status') and record.study_status == "已毕业":
            if hasattr(record, 'graduation_years'):
                coefficient = cls._get_graduation_year_coefficient(record.graduation_years)
            else:
                # 如果没有 graduation_years 属性，默认使用 1.0
                coefficient = 1.0

        # 只有当有政治科目时才添加政治相关项
        if has_politics_subject and political_score is not None:
            # 如果已毕业，调整政治分数
            adjusted_political_score = political_score * coefficient
            assessment["政治水平"] = round(adjusted_political_score)

        # 只有当有数学科目时才添加数学相关项
        if has_math_subject and math_score is not None:
            # 如果已毕业，调整数学分数
            adjusted_math_score = math_score * coefficient
            assessment["数学水平"] = round(adjusted_math_score)
            assessment["学过的数学科目"] = record.math_subjects

        return assessment

    @classmethod
    def parse_init_graduated_data(cls, record:SuperViseInitStudentStatus):
            pass


    @classmethod
    def parse_academic_performance(cls,academic_performance):
        """解析学业成绩"""
        
        if academic_performance == "其他":
            return "后50%"
        
        return academic_performance
    

    @classmethod
    def parse_base_info(cls, school_code, major_code):
        """解析基本需求
        Args:
            school_code: 学校代码
            major_code: 专业代码
        Returns:
            解析后的学校信息字典
        """
        # 生成缓存key
        cache_key = f"school_info_{school_code}_{major_code}"
        
        # 尝试从缓存获取
        cached_data = cls._get_cache(cache_key)
        if cached_data:
            logger.debug(f"从缓存获取学校信息: {school_code}-{major_code}")
            return cached_data

        parsed_data = {}
        max_enroll_num = -1  # 初始化最大招生人数变量

        try:
            schools_info = yantucs_data_client.get_school_info(school_code, major_code)
            if not schools_info:
                raise AppNotFoundError(detail=f"未找到对应的学校信息: 学校代码={school_code}, 专业代码={major_code}")
            
            # 找到招生人数最多的学校信息
            for school in schools_info:
                if max_enroll_num < school.get("enroll_num"):
                    max_enroll_num = school.get("enroll_num")
                    school_info = school
            
            analyse_data = school_info
            exam_range = analyse_data.get('exam_range', [])
            
            policy_result = cls.process_subject(exam_range, 'polity')
            language_result = cls.process_subject(exam_range, 'language')
            subject1_result = cls.process_subject(exam_range, 'subject1')
            subject2_result = cls.process_subject(exam_range, 'subject2')

            # 添加调试日志
            logger.info(f"Debug - parse_base_info for {school_code}-{major_code}:")
            logger.info(f"Debug - exam_range: {exam_range}")
            logger.info(f"Debug - policy_result: '{policy_result}' (type: {type(policy_result)})")
            logger.info(f"Debug - subject1_result: '{subject1_result}'")

            # 初始化默认的复试信息
            major_re_exam_info = {
                'policy': '无',
                'language': '50',
                'subject1': '60',
                'subject2': '60'
            }

            re_exam_info = analyse_data.get('major_re_exam_info', {})
            # 解析复试信息
            if re_exam_info["average"]:
                # 如果政治科目不是'政治'，将政治信息转移到subject1
                if policy_result != '(101)思想政治理论':
                    major_re_exam_info = {
                        'policy': '无',  # 设为默认值
                        'language': re_exam_info.get('language', '50'),
                        'subject1': re_exam_info.get('polity', '60'),  # 使用政治的信息
                        'subject2': re_exam_info.get('subject2', '60')
                    }
                else:
                    major_re_exam_info = {
                        'policy': re_exam_info.get('polity', '50'),
                        'language': re_exam_info.get('language', '50'),
                        'subject1': re_exam_info.get('subject1', '60'),
                        'subject2': re_exam_info.get('subject2', '60')
                    }

            major_enroll_info = {
                'policy': '无',
                'language': '无',
                'subject1': '无',
                'subject2': '无',
            }

            enroll_info = analyse_data.get('major_enroll_info', {})

            if enroll_info["average"]:
                # 如果政治科目不是'政治'，将政治信息转移到subject1
                if policy_result != '(101)思想政治理论':
                    major_enroll_info = {
                        'policy': '无',  # 设为无
                        'language': enroll_info.get('language', '无'),
                        'subject1': enroll_info.get('polity', '无'),  # 使用政治的信息
                        'subject2': enroll_info.get('subject2', '无'),
                        '最低分': enroll_info.get('lowest', '无'),
                        '平均分': enroll_info.get('average', '无'),
                        '最高分': enroll_info.get('highest', '无'),
                    }
                else:
                    major_enroll_info = {
                        'policy': enroll_info.get('polity', '无'),
                        'language': enroll_info.get('language', '无'),
                        'subject1': enroll_info.get('subject1', '无'),
                        'subject2': enroll_info.get('subject2', '无'),
                        '最低分': enroll_info.get('lowest', '无'),
                        '平均分': enroll_info.get('average', '无'),
                        '最高分': enroll_info.get('highest', '无'),
                    }
            else:
                for year in ["2024", "2023", "2022"]:
                    pre_schools_info = yantucs_data_client.get_school_info(school_code,major_code,year)
                    if not pre_schools_info:
                        raise AppNotFoundError(detail=f"未找到对应的学校信息: 学校代码={school_code}, 专业代码={major_code}, 年份={year}")
                    pre_school_info = pre_schools_info[0] if pre_schools_info[0] else {}
                    # 找到招生人数最多的学校信息
                    for school in pre_schools_info:
                        if max_enroll_num < school.get("enroll_num"):
                            max_enroll_num = school.get("enroll_num")
                            pre_school_info = school
            
                    enroll_info = pre_school_info.get('major_enroll_info', {})
                    if enroll_info["average"]:
                        # 如果政治科目不是'政治'，将政治信息转移到subject1
                        if policy_result != '(101)思想政治理论':
                            major_enroll_info = {
                                'policy': '无',  # 设为无
                                'language': enroll_info.get('language', '无'),
                                'subject1': enroll_info.get('polity', '无'),  # 使用政治的信息
                                'subject2': enroll_info.get('subject2', '无'),
                                '最低分': enroll_info.get('lowest', '无'),
                                '平均分': enroll_info.get('average', '无'),
                                '最高分': enroll_info.get('highest', '无'),
                            }
                        else:
                            major_enroll_info = {
                                'policy': enroll_info.get('polity', '无'),
                                'language': enroll_info.get('language', '无'),
                                'subject1': enroll_info.get('subject1', '无'),
                                'subject2': enroll_info.get('subject2', '无'),
                                '最低分': enroll_info.get('lowest', '无'),
                                '平均分': enroll_info.get('average', '无'),
                                '最高分': enroll_info.get('highest', '无'),
                            }
                        break
            
            if enroll_info["average"] == None:
                province = analyse_data.get("school_city")["province"]
                region = map_province_to_region(province)
                school_code_list = get_similar_school(
                    major_code, region, analyse_data.get("school_level"), province,
                    target_school_code=school_code, target_major_code=major_code
                )
                logger.info(f"开始查询相似学校录取信息: 目标学校={school_code}, 专业={major_code}, 相似学校数量={len(school_code_list)}")

                for other_chool_code in school_code_list:
                    other_schools_info = yantucs_data_client.get_school_info(other_chool_code,major_code)
                    if not other_schools_info:
                        logger.warning(f"相似学校信息查询失败: 学校代码={other_chool_code}, 专业代码={major_code}, 跳过该学校")
                        continue  # 跳过这个学校，继续查找其他相似学校

                    other_school_info = other_schools_info[0] if other_schools_info[0] else {}

                    # 找到招生人数最多的学校信息
                    for school in other_schools_info:
                        if max_enroll_num < school.get("enroll_num"):
                            max_enroll_num = school.get("enroll_num")
                            other_school_info = school


                    enroll_info = other_school_info.get('major_enroll_info', {})
                    if enroll_info["average"]:
                        logger.info(f"从相似学校获取到录取信息: 学校代码={other_chool_code}")
                        # 如果政治科目不是'政治'，将政治信息转移到subject1
                        if policy_result != '(101)思想政治理论':
                            major_enroll_info = {
                                'policy': '无',  # 设为无
                                'language': enroll_info.get('language', '无'),
                                'subject1': enroll_info.get('polity', '无'),  # 使用政治的信息
                                'subject2': enroll_info.get('subject2', '无'),
                                '最低分': enroll_info.get('lowest', '无'),
                                '平均分': enroll_info.get('average', '无'),
                                '最高分': enroll_info.get('highest', '无'),
                            }
                        else:
                            major_enroll_info = {
                                'policy': enroll_info.get('polity', '无'),
                                'language': enroll_info.get('language', '无'),
                                'subject1': enroll_info.get('subject1', '无'),
                                'subject2': enroll_info.get('subject2', '无'),
                                '最低分': enroll_info.get('lowest', '无'),
                                '平均分': enroll_info.get('average', '无'),
                                '最高分': enroll_info.get('highest', '无'),
                            }
                        break

                # 如果所有相似学校都没有找到录取信息，记录警告
                if major_enroll_info.get('平均分') == '无':
                    logger.warning(f"所有相似学校都未找到录取信息: 目标学校={school_code}, 专业={major_code}, 将使用默认录取信息")
            
            parsed_data = {
                'school_level_display': school_info.get('school_level_display', '未知'),
                'policy': policy_result,
                'language': language_result,
                'subject1': subject1_result,
                'subject2': subject2_result,
                'major_re_exam_info': major_re_exam_info,
                'major_enroll_info': major_enroll_info,
            }
            
            # 存入缓存
            cls._set_cache(cache_key, parsed_data)
            return parsed_data
            
        except Exception as e:
            logger.error(f"获取学校信息失败: 学校代码={school_code}, 专业代码={major_code}, 错误信息={str(e)}", exc_info=True)
            raise



    @classmethod
    def is_politics_subject(cls, subject):
        """判断是否为真正的政治科目"""
        politics_subjects = [
            '(101)思想政治理论',
            '政治',
            '思想政治理论'
        ]
        return subject in politics_subjects

    @classmethod
    def process_subject(cls, exam_range, subject_key):
        """
        处理考试科目，优化language字段的逻辑
        对于language字段，优先选择(201)英语一或(204)英语二
        """
        # 提取并清理科目名称
        subject_values = []
        for item in exam_range:
            subject = item.get(subject_key)
            if subject:
                subject_values.append(subject)

        if not subject_values:
            return '无'

        # 特殊处理language字段
        if subject_key == 'language':
            return cls._process_language_subject(subject_values)

        # 其他字段使用原有逻辑：统计并返回最常见的科目
        subject_counts = Counter(subject_values)
        return subject_counts.most_common(1)[0][0]

    @classmethod
    def _process_language_subject(cls, language_values):
        """
        专门处理language字段，优先选择标准英语考试科目
        """
        # 标准英语考试科目
        standard_english = ['(201)英语一', '(204)英语二']

        # 首先检查是否有标准英语科目
        for lang in language_values:
            if lang in standard_english:
                return lang

        # 如果没有标准英语科目，返回最常见的
        language_counts = Counter(language_values)
        most_common = language_counts.most_common(1)[0][0]

        # 如果最常见的不是标准英语科目，返回None表示需要特殊处理
        if most_common not in standard_english:
            return None

        return most_common

    @classmethod
    def get_graduation_courses(cls,graduation_major_code):

        major_course_obj = UndergraduateMajorCourse.objects.filter(
            second_category=graduation_major_code
        ).first()

        if major_course_obj:
            return major_course_obj.core_courses
            
        raise AppNotFoundError(detail=f"未找到专业代码: {graduation_major_code} 的相关课程信息")

    @classmethod
    def get_major_relation(cls, graduation_major_code, target_major_code):
        """获取专业联系指数
        Args:
            graduation_major_code: 本科专业代码
            target_major_code: 目标专业代码
        Returns:
            专业关联度等级
        """
        if not graduation_major_code or not target_major_code:
            raise ValueError("专业代码不能为空")
            
        # 生成缓存key
        cache_key = f"major_relation_{graduation_major_code[:4]}_{target_major_code[:4]}"
        
        # 尝试从缓存获取
        cached_data = cls._get_cache(cache_key)
        if cached_data:
            logger.debug(f"从缓存获取专业关联度: {graduation_major_code}-{target_major_code}")
            return cached_data

        try:
            target_major_code_prefix = target_major_code[:4]
            relation_record = MajorRelation.objects.filter(
                undergraduate_second_category=graduation_major_code,
                graduate_second_category_code=target_major_code_prefix
            ).first()
            
            if relation_record:
                relation_level = relation_record.relation_level or '无'
                # 存入缓存
                cls._set_cache(cache_key, relation_level)
                return relation_level
                
            raise AppNotFoundError(
                detail=f"未找到专业代码: {graduation_major_code} 和研究生专业代码: {target_major_code_prefix} 的相关联系信息"
            )
        except Exception as e:
            logger.error(f"获取专业关联度失败: {str(e)}", exc_info=True)
            raise

    @classmethod
    def get_professional_score(cls,relation_score, GPA):
        """根据专业联系指数和GPA排名算专业课成绩"""
        # 假设关系指数为1-10，GPA排名为10%，30%，50%，其他
        if relation_score == '无':
            return 0
        
        GPA_score_map = {
            '前10%': 0.65,
            '前30%': 0.55,
            '前50%': 0.49,
            '其他': 0.40
        }
        
        relation_score_map = cls.map_value(relation_score)
        professional_score = relation_score_map * GPA_score_map.get(GPA, 0.49)

        return round(professional_score)


    @classmethod
    def estimate_math_score(cls,exam_type, assessment, studied_subjects,major_code='08'):

        # 自我评估乘数
        ASSESSMENT_MULTIPLIERS = {
            "优秀": 1.35, "良好": 1.20, "一般": 1.00, "较差": 0.85
        }

        # 考研数学科目结构
        EXAM_CONFIG = {
            '(301)数学一': {
                '高等数学': 84, '线性代数': 33, '概率统计': 33
            },
            '(302)数学二': {
                '高等数学': 117, '线性代数': 33,
            },
            '(303)数学三': {
                '高等数学': 84, '线性代数': 33, '概率统计': 33
            }
        }

        # 步骤1: 计算学生的数学潜力指数 (0-100)
        base_score = MAJOR_MATH_BASE_SCORES.get(major_code, 34.0) # 如果专业未定义，给一个工科的默认值
        multiplier = ASSESSMENT_MULTIPLIERS.get(assessment, 1.0)
        math_potential_score = min(base_score * multiplier, 100.0)

        # 步骤2: 根据潜力指数，计算各科目的预估分数
        target_exam_scores = EXAM_CONFIG[exam_type]
        student_unlearned_subjects = []
        total_estimated_score = 0
        for subject, full_score in target_exam_scores.items():
            
            # 判断学生是否学过该科目
            if subject in studied_subjects:
                total_estimated_score += (math_potential_score / 100.0) * full_score
            else:
                student_unlearned_subjects.append(subject) # 没学过则为知识盲区

        result = {
            '总分': round(total_estimated_score)
        }
        if student_unlearned_subjects:
            result['未学习科目'] = student_unlearned_subjects
            return result
        return round(total_estimated_score)
            
    @classmethod
    def parse_English_level(cls,exam_type, english_level):

        # 验证输入有效性
        valid_levels = ['四级','六级','专四','专八','托雅','其他']
        valid_exam_types = ['(201)英语一', '(204)英语二']
        
        if exam_type not in valid_exam_types:
            raise ValueError(f"无效考试类型: {exam_type}。请使用{valid_exam_types}")
        
        if english_level not in valid_levels:
            raise ValueError(f"无效英语水平: {english_level}。请使用{valid_levels}")
        
        # 分数映射表（典型值）
        SCORE_MAPPING = {
            '(201)英语一': {  # 英语一
                '四级': 45,
                '六级': 50,
                '专四': 70,
                '专八': 77,
                '托雅': 75,
                '其他': 40
            },
            '(204)英语二': {  # 英语二
                '四级': 50,
                '六级': 55,
                '专四': 70,
                '专八': 75,
                '托雅': 78,
                '其他': 45
            }
        }
        
        return SCORE_MAPPING[exam_type][english_level]
    
    @classmethod
    def map_value(cls, x):
        """
        使用对数尺度将输入范围 [1, 10] 的值映射到输出范围 [140, 45]。
        """
        a = -95 / math.log(10)  # (45 - 140) / ln(10) = -95 / ln(10)
        b = 140
        x = int(x)
        # 为确保映射平滑，我们假设x可以是浮点数。
        return a * math.log(x) + b

    @classmethod
    @contextmanager
    def get_external_db_connection(cls):
        import pymysql
        """获取外部数据库连接的上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(
                host='********',
                user='root',
                password='yantu2016go',
                database='college_data',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            yield connection
        except Exception as e:
            logger.error(f"外部数据库连接失败: {str(e)}", exc_info=True)
            raise
        finally:
            if connection:
                connection.close()

    @classmethod
    def execute_external_query(cls, sql_query, params=None):
        """
        执行外部数据库查询
        
        Args:
            sql_query (str): SQL查询语句
            params (tuple/list, optional): 查询参数
            
        Returns:
            list: 查询结果列表，每个元素为字典格式
        """
        try:
            with cls.get_external_db_connection() as connection:
                with connection.cursor() as cursor:
                    cursor.execute(sql_query, params or ())
                    result = cursor.fetchall()
                    logger.info(f"外部数据库查询成功，返回 {len(result)} 条记录")
                    return result
        except Exception as e:
            logger.error(f"外部数据库查询失败: {str(e)}", exc_info=True)
            return []

def get_similar_school(major_code, region, undergraduate_level, province, target_school_code=None, target_major_code=None):
    """获取相似学校列表，基于rec_score相似度排序，同省份的学校排在前面

    Args:
        major_code (str): 专业代码
        region (str): 地区
        undergraduate_level (int): 学校等级
        province (str): 省份名称
        target_school_code (str, optional): 目标学校代码，用于查询rec_score
        target_major_code (str, optional): 目标专业代码，用于查询rec_score

    Returns:
        list: 按rec_score相似度排序的学校代码列表，同省的排在前面
    """
    schools_list = yantucs_data_client.get_school_by_region(
        major_code=major_code,
        region=region,
        undergraduate_level=undergraduate_level
    )

    # 将学校按省份分组
    same_province_schools = []
    other_province_schools = []

    for school in schools_list:
        school_province = school.get("school_city", {}).get("province")
        if school_province == province:
            same_province_schools.append(school.get("school_code"))
        else:
            other_province_schools.append(school.get("school_code"))

    # 如果提供了目标学校和专业代码，则基于rec_score进行排序
    if target_school_code and target_major_code:
        try:
            # 获取目标学校的rec_score
            target_rec_score = _get_school_rec_score(target_school_code, target_major_code)

            if target_rec_score is not None:
                # 对同省份学校按rec_score相似度排序
                same_province_schools = _sort_schools_by_rec_score_similarity(
                    same_province_schools, major_code, target_rec_score
                )
                # 对其他省份学校按rec_score相似度排序
                other_province_schools = _sort_schools_by_rec_score_similarity(
                    other_province_schools, major_code, target_rec_score
                )
        except Exception as e:
            logger.warning(f"基于rec_score排序失败，使用默认排序: {str(e)}")

    # 合并两个列表，同省的排在前面
    return same_province_schools + other_province_schools


def _get_school_rec_score(school_code, major_code):
    """获取指定学校和专业的rec_score

    Args:
        school_code (str): 学校代码
        major_code (str): 专业代码

    Returns:
        float: rec_score值，如果未找到则返回None
    """
    try:
        sql_query = """
        SELECT rec_score
        FROM search_catalog
        WHERE school LIKE %s AND major LIKE %s AND year = 2025
        LIMIT 1
        """
        result = SuperviseInitStatusService.execute_external_query(
            sql_query,
            (f"({school_code})%", f"({major_code})%")
        )

        if result and len(result) > 0:
            return result[0].get('rec_score')
        return None
    except Exception as e:
        logger.error(f"查询rec_score失败: {str(e)}")
        return None


def _sort_schools_by_rec_score_similarity(school_codes, major_code, target_rec_score):
    """根据rec_score与目标值的相似度对学校列表进行排序

    Args:
        school_codes (list): 学校代码列表
        major_code (str): 专业代码
        target_rec_score (float): 目标rec_score值

    Returns:
        list: 按rec_score相似度排序的学校代码列表
    """
    if not school_codes:
        return school_codes

    try:
        # 构建查询条件，查询所有学校的rec_score
        school_conditions = " OR ".join([f"school LIKE %s" for _ in school_codes])
        sql_query = f"""
        SELECT school, rec_score
        FROM search_catalog
        WHERE ({school_conditions}) AND major LIKE %s AND year = 2025
        """

        # 准备查询参数
        params = []
        for school_code in school_codes:
            params.append(f"({school_code})%")
        params.append(f"({major_code})%")

        result = SuperviseInitStatusService.execute_external_query(sql_query, params)

        # 构建学校代码到rec_score的映射
        school_rec_scores = {}
        for row in result:
            school_field = row.get('school', '')
            # 从 "(code)name" 格式中提取 code
            if school_field.startswith('(') and ')' in school_field:
                school_code = school_field.split(')')[0][1:]  # 提取括号内的代码
                rec_score = row.get('rec_score')
                if school_code and rec_score is not None:
                    school_rec_scores[school_code] = rec_score

        # 按rec_score与目标值的差值绝对值排序
        def sort_key(school_code):
            rec_score = school_rec_scores.get(school_code)
            if rec_score is None:
                return float('inf')  # 没有rec_score的学校排在最后
            return abs(rec_score - target_rec_score)

        return sorted(school_codes, key=sort_key)

    except Exception as e:
        logger.error(f"按rec_score排序失败: {str(e)}")
        return school_codes

region_mapping = {
    # 华北地区
    "北京市": "华北", "天津市": "华北", "河北省": "华北", "山西省": "华北", "内蒙古自治区": "华北",
    # 东北地区
    "辽宁省": "东北", "吉林省": "东北", "黑龙江省": "东北",
    # 华东地区
    "上海市": "华东", "江苏省": "华东", "浙江省": "华东", "安徽省": "华东", 
    "福建省": "华东", "江西省": "华东", "山东省": "华东", "台湾省": "华东",
    # 华中地区
    "河南省": "华中", "湖北省": "华中", "湖南省": "华中",
    # 华南地区
    "广东省": "华南", "广西壮族自治区": "华南", "海南省": "华南", 
    "香港特别行政区": "华南", "澳门特别行政区": "华南",
    # 西南地区
    "重庆市": "西南", "四川省": "西南", "贵州省": "西南", 
    "云南省": "西南", "西藏自治区": "西南",
    # 西北地区
    "陕西省": "西北", "甘肃省": "西北", "青海省": "西北", 
    "宁夏回族自治区": "西北", "新疆维吾尔自治区": "西北"
}

def map_province_to_region(province_name):
    """
    将省市名称映射到对应的地区
    :param province_name: 省市名称字符串，如"北京市"
    :return: 地区名称字符串，如"华北"
    """
    import re
    # 预编译正则表达式，用于去除省市自治区后缀
    pattern = re.compile(r'(.*?)(?:省|市|自治区|特别行政区)$')
    match = pattern.match(province_name)
    if match:
        simplified = match.group(1)
    else:
        simplified = province_name

    # 尝试直接匹配
    if province_name in region_mapping:
        return region_mapping[province_name]
    # 尝试匹配简化后的名称
    if simplified in region_mapping:
        return region_mapping[simplified]
    # 尝试匹配简化后的名称是否在region_mapping的某个全名中包含
    for full_name in region_mapping:
        if simplified in full_name:
            return region_mapping[full_name]
    return "未知地区"
