import datetime

from celery import shared_task
from django.utils import timezone

from app.models import MessageTask
from app.tasks.process_message_task import process_message_task


@shared_task(queue='message_task')
def auto_check_process_message_task():
    wait_timeout = timezone.now() - datetime.timedelta(seconds=30)
    qs1 = MessageTask.objects.filter(
        is_deleted=False,
        process_status=MessageTask.ProcessStatus.waiting,
        add_time__lt=wait_timeout
    )
    for i in qs1:
        process_message_task.delay(i.task_id)

    ing_timeout = timezone.now() - datetime.timedelta(seconds=600)
    MessageTask.objects.filter(
        is_deleted=False,
        process_status=MessageTask.ProcessStatus.ing,
        add_time__lt=ing_timeout
    ).update(
        process_status=MessageTask.ProcessStatus.fail,
        fail_reason='处理超时',
    )

    qs2 = MessageTask.objects.filter(
        is_deleted=False,
        process_status=MessageTask.ProcessStatus.fail,
        retry_times__lt=3
    )
    for i in qs2:
        process_message_task.delay(i.task_id, is_retry=True)
