<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本分割评估</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .page-title {
            width: 200px;
            margin: 20px auto;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }
        .container {
            display: flex;
            max-width: 1200px;
            min-height: 650px; /* 固定高度 */
            margin: 20px auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .left-column, .right-column {
            padding: 20px;
            box-sizing: border-box;
        }
        .left-column {
            width: 40%;
            background-color: #f9f9f9;
            border-right: 1px solid #eaeaea;
        }
        .right-column {
            width: 60%;
            height: 820px; /* 固定高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            border-left: 1px solid #eaeaea; /* 为了美观，可以添加分隔线 */
        }
        h2 {
            color: #333;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #666;
        }
        .help-text {
            display: block;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .left-column form {
            display: flex;
            flex-direction: column;
        }
        .check-form-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .check-form-group label {
            margin-right: 10px;
        }
        input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="number"]:focus, textarea:focus {
            border-color: #007bff;
            outline: none;
        }
        textarea {
            resize: vertical;
            height: 300px; /* 设置一个初始高度 */
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .form-group {
            margin-bottom: 10px;
        }
        .right-column ul {
            list-style-type: none;
            padding: 0;
        }
        .right-column li {
            margin-bottom: 10px;
            background-color: #fafafa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
        }
        .right-column li h3 {
            margin: 0 0 5px 0;
            font-size: 14px;
        }
        .right-column li p {
            margin: 0;
            font-size: 14px;
            white-space: pre-line;
        }
        /* 下拉框样式 */
        .left-column select {
            width: 100%; /* 宽度与输入框一致 */
            padding: 10px; /* 内边距 */
            border: 1px solid #ddd; /* 边框颜色 */
            border-radius: 4px; /* 边框圆角 */
            box-sizing: border-box; /* 盒模型 */
            background-color: white; /* 背景颜色 */
            color: #666; /* 文本颜色 */
            font-size: 16px; /* 字体大小 */
            cursor: pointer; /* 鼠标悬停时的光标样式 */
        }

        .left-column select:focus {
            border-color: #007bff; /* 聚焦时的边框颜色 */
            outline: none; /* 移除聚焦时的轮廓 */
        }
        /* 下拉框选项样式 */
        .left-column select option {
            padding: 5px; /* 选项的内边距 */
            background-color: #fff; /* 选项的背景颜色 */
            color: #333; /* 选项的文本颜色 */
        }
        .left-column select option:hover {
            background-color: #f0f0f0; /* 选项悬停时的背景颜色 */
        }
        /* 加载框样式 */
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
        .char-count {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>正在请求...</p>
        </div>
    </div>

    <div class="page-title">文本分割评估</div>
    <div class="container">
        <div class="left-column">
            <form id="myForm" action="{% url 'split_estimate_submit' %}">
                <div class="form-group">
                    <label for="splitter">选择分块器:</label>
                    <select id="splitter" name="splitter">
                        <option value="markdown">Markdown分块器</option>
                        <option value="semantic" selected>语义分块器</option>
                    </select>
                </div>
                <div class="markdown-warp">
                    <div class="form-group">
                        <label for="chunk_size">chunk_size:</label>
                        <input type="number" id="chunk_size" name="chunk_size" placeholder="Enter chunk_size here..." value="500">
                    </div>
                    <div class="form-group">
                        <label for="chunk_overlap">chunk_overlap:</label>
                        <input type="number" id="chunk_overlap" name="chunk_overlap" placeholder="Enter chunk_overlap here..." value="30">
                    </div>
                    <div class="form-group check-form-group">
                        <label for="remove_images">是否删除图片链接:</label>
                        <input type="checkbox" id="remove_images" name="remove_images">
                    </div>
                    <div class="form-group check-form-group">
                        <label for="remove_hyperlinks">是否删除超链接文本:</label>
                        <input type="checkbox" id="remove_hyperlinks" name="remove_hyperlinks">
                    </div>
                </div>
                <div class="semantic-warp">
                    <div class="form-group">
                        <label for="buffer_size">缓冲区大小:</label>
                        <input type="number" id="buffer_size" name="buffer_size" placeholder="Enter buffer_size here..." value="1">
                        <span class="help-text">它决定了在计算句子之间相似性时要考虑的句子数量。</span>
                    </div>
                    <div class="form-group">
                        <label for="breakpoint_threshold_type">确定分割点方法:</label>
                        <select id="breakpoint_threshold_type" name="breakpoint_threshold_type">
                            <option value="percentile">百分位数</option>
                            <option value="standard_deviation">标准差</option>
                            <option value="interquartile">四分位数范围</option>
                            <option value="gradient">梯度方法</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="breakpoint_threshold_amount">分割阈值量:</label>
                        <input type="number"
                               id="breakpoint_threshold_amount"
                               name="breakpoint_threshold_amount"
                               placeholder="Enter breakpoint_threshold_amount here..."
                               value="1"
                        >
                        <span class="help-text threshold_amount_help"></span>
                    </div>
                    <div class="form-group">
                        <label for="sentence_split_symbol">分割句子符号（支持多个）:</label>
                        <input type="text" id="sentence_split_symbol"
                               name="sentence_split_symbol"
                               placeholder="Enter sentence_split_symbol here..."
                               value="。？！\n"
                        >
                    </div>
                </div>
                <div class="form-group">
                    <label for="content">文本内容:</label>
                    <textarea id="content" name="content" rows="4" required placeholder="Enter more text here..."></textarea>
                </div>

                <button type="submit">Submit</button>
            </form>
        </div>
        <div class="right-column">
            <h2>分段预览</h2>
            <ul id="preview-list">
            </ul>
        </div>
    </div>
    <script>
        $(function() {
            const breakpointThresholdType = [
                {type: 'percentile', default: '95', help_text: '计算所有句子之间的差异，然后任何大于X百分位数的差异都会被分割'},
                {type: 'standard_deviation', default: '3', help_text: '任何超过X个标准差的差异都会被分割'},
                {type: 'interquartile', default: '1.5', help_text: '使用四分位距来分割块'},
                {type: 'gradient', default: '95', help_text: '这种方法可能会考虑句子间差异的变化率，而不是仅仅基于一个静态的阈值。'},
            ]

            function onChangeBreakpointThresholdType() {
                const type = $('#breakpoint_threshold_type').val();
                const default_value = breakpointThresholdType.find(item => item.type === type).default;
                const help_text = breakpointThresholdType.find(item => item.type === type).help_text
                $('#breakpoint_threshold_amount').val(default_value);
                $('.threshold_amount_help').html(help_text)
            }

            function onChangeSplitter() {
                const splitter = $('#splitter').val();
                if (splitter === 'markdown') {
                    $('.markdown-warp').show();
                    $('.semantic-warp').hide();
                } else if (splitter === 'semantic') {
                    $('.markdown-warp').hide();
                    $('.semantic-warp').show();
                }
            }

            onChangeSplitter()
            onChangeBreakpointThresholdType()

            $('#breakpoint_threshold_type').change(() => {
                onChangeBreakpointThresholdType()
            })

            $('#splitter').change(() => {
                onChangeSplitter()
            })

            function validate_markdown_splitter () {
                const remove_images = $('#remove_images').is(':checked');
                const remove_hyperlinks = $('#remove_hyperlinks').is(':checked');

                const formData = {
                    chunk_size: parseInt($('#chunk_size').val()),
                    chunk_overlap: parseInt($('#chunk_overlap').val()),
                    content: $('#content').val(),
                    remove_images,
                    remove_hyperlinks,
                };
                if (formData.chunk_size < 0 || formData.chunk_size > 1000) {
                    alert('chunk_size值错误【0-1000】')
                    return
                }
                if (formData.chunk_overlap < 0 || formData.chunk_overlap > 100) {
                    alert('chunk_overlap值错误【0-100】')
                    return
                }
                if (formData.chunk_overlap >= formData.chunk_size) {
                    alert('chunk_overlap不能大于chunk_size')
                    return
                }
                if (!formData.content) {
                    alert('内容不能为空')
                    return
                }
                return formData
            }
            function validate_semantic_splitter () {
                const formData = {
                    buffer_size: parseInt($('#buffer_size').val()),
                    breakpoint_threshold_type: $('#breakpoint_threshold_type').val(),
                    breakpoint_threshold_amount: parseFloat($('#breakpoint_threshold_amount').val()),
                    sentence_split_symbol: $('#sentence_split_symbol').val(),
                    content: $('#content').val(),
                };
                if (!formData.content) {
                    alert('内容不能为空')
                    return
                }
                return formData
            }

            $('#myForm').submit(function(e) {
                e.preventDefault(); // 阻止表单的默认提交行为

                let formData = null;
                const splitter = $('#splitter').val();
                if (splitter == 'markdown') {
                    formData = validate_markdown_splitter()
                } else if (splitter == 'semantic') {
                    formData = validate_semantic_splitter()
                } else {
                    alert('分块器选择错误')
                    return
                }
                if (!formData) return
                formData.splitter = $('#splitter').val()

                $('#loadingOverlay').css('display', 'flex')
                $.ajax({
                    type: 'POST',
                    url: $('#myForm').attr('action'), // 替换为你的服务器端点
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        // 处理响应数据
                        const previewList = $('#preview-list');
                        previewList.empty(); // 清空现有列表
                        $.each(response.data, function(index, item) {
                            const doc_id_line = item.doc_id ? '<h3>doc_id: ' + item.doc_id + '</h3>' : ''
                            const title_line = item.title ? '<h3>标题: ' + item.title + '</h3>' : ''
                            previewList.append(
                                '<li>' +
                                    doc_id_line +
                                    title_line +
                                    '<p>' + item.page_content + '</p>' +
                                    '<div class="char-count">字符数量: ' + item.char_len + '</div>' +
                                '</li>'
                            );
                        });
                        $('#loadingOverlay').css('display', 'none')
                    },
                    error: function(xhr, status, error) {
                        // 处理错误情况
                        $('.right-column p').text('Error submitting form.');
                        $('#loadingOverlay').css('display', 'none')
                    }
                });

            })

        })

    </script>
</body>
</html>