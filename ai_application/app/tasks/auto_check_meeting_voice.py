import logging

from celery import shared_task

from app.services.meeting_service import MeetingService

logger = logging.getLogger(__name__)


@shared_task(queue='message_task')
def auto_check_meeting_voice_convert():
    logger.info('auto_check_meeting_voice_convert begin...')
    MeetingService.process_unconverted_records(batch_size=4)


@shared_task(queue='message_task')
def auto_check_meeting_voice_extract():
    logger.info('auto_check_meeting_voice_extract begin...')
    MeetingService.process_not_extracted_records(batch_size=3)
