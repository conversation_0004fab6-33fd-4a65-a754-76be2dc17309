import ast
import json
import re
import logging
import os

import numpy as np
import pandas as pd
from typing import Dict, Optional, Union
from app.models import ExamAnalysisKnowledgePointWithStats
from concurrent.futures import ThreadPoolExecutor, as_completed
from django.conf import settings
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from app.models import KaoGangAnalysis, SuperViseInitStudentStatus

from app.models import KaoGangAnalysis, SuperViseInitStudentStatus, PersonalizedExamSyllabus

# 添加日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置文本大模型
llm_text = ChatOpenAI(
    openai_api_key=settings.DOUBAO_API_KEY,
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-seed-1-6-250615",
    max_tokens=20480
)


def load_knowledge_priority():
    """
    加载知识点优先度数据
    """
    try:
        with open(os.path.join(os.path.dirname(__file__), '..', 'sorted_knowledge_priority.json'), 'r',
                  encoding='utf-8') as f:
            priority_data = json.load(f)
        return priority_data
    except Exception as e:
        logger.error(f"加载知识点优先度数据失败: {e}")
        return {}


def generate_all_subjects_kaogang(kaogang_content, student_info, knowledge_plan):
    """
    调用大模型：一次性为所有科目生成个性化考纲，并根据知识点优先度分配掌握程度
    """
    template = """你是一个专业的408计算机考研辅导专家。请根据以下信息为用户生成408计算机考研的个性化学习大纲，包含数据结构、计算机组成原理、操作系统、计算机网络四个科目。

## 408计算机考研大纲内容：
{kaogang_content}

## 学生目标分数：
{student_info}

## 考到目标分数优先需要掌握的知识点：
{knowledge_plan}

## 要求：
- 请结合408计算机考研大纲内容和学生基础信息，为学生生成一份个性化的408计算机考研学习大纲
- 大纲需要包含数据结构、计算机组成原理、操作系统、计算机网络四个科目
- 在每个科目考研大纲列表的最后一列加上掌握程度列，仅用五角星数量表示即可
  - 掌握程度标识对应五角星数量：
        了解，选学   ：  ☆
        了解，识记   ：  ☆☆
        理解，会用   ：  ☆☆☆
        熟悉，重点练 ： ☆☆☆☆
        掌握，必须会 ： ☆☆☆☆☆
- 将难度等级列的1、3、5、7、9分别对应一级、二级、三级、四级、五级
- 根据考到目标分数优先需要掌握的知识点:{knowledge_plan}调整考点内容掌握程度：
    考点内容掌握程度调整规则：
        1. 根据优先需要掌握的知识点顺序，越排在前面的掌握程度需要越高
        2. 同时还要根据学生的目标总分、以及难度调整考点内容的掌握程度
            - 对应的目标总分越高、对难度高的考点内容的掌握程度也越高
            - 对应的目标总分越低，对难度高的考点内容的掌握程度要相应的调低

- 无需根据目标总分来划分到每个科目的目标分数
- 输出内容应结构清晰，便于学生理解和执行

## 输出内容结构要求：
- 需在标题下面说明用户的目标分数，在学科中间的间隙加上一些风趣的鼓励解释的话语，
- 无需解释说明大纲内容的难度等级及掌握程度，只需直接输出用户大纲内容及备考建议
- 输出格式应包含清晰的标题结构，如"## 数据结构"、"## 计算机组成原理"等
- 列表内的表示重要程度、考试题型、难度等级、掌握程度等级的格式需要一致，不可以同一个字段用两种方法表示程度
- 重要程度和掌握程度务必用五角星数量表示程度高低,且五角星的颜色一致
请根据以上信息生成详细的个性化408计算机考研学习大纲。
"""

    prompt = PromptTemplate.from_template(template)
    llm_chain = prompt | llm_text

    # 调用大模型生成所有科目的个性化考研大纲
    result = llm_chain.invoke({
        "kaogang_content": kaogang_content or '',
        "student_info": student_info or '',
        "knowledge_plan": knowledge_plan
    })

    return result


def generate_personal_kaogang(target_score):
    """
    根据KaoGangAnalysis数据库记录和SuperViseInitStudentStatus数据库记录生成个性化考研大纲
    """
    try:
        # 获取数学学科的考纲内容
        kaogang_content = KaoGangAnalysis.objects.filter(subject="408计算机").first()
        print(f"🚀", kaogang_content)
        if not kaogang_content:
            logger.error("KaoGangAnalysis表中没有408计算机科目的数据")
            return None

        # # 获取SuperViseInitStudentStatus中user_id为"i9981"的记录
        # student_records = SuperViseInitStudentStatus.objects.filter(user_id=user_id)
        # if not student_records.exists():
        #     logger.error("SuperViseInitStudentStatus表中没有user_id为'i9981'的记录")
        #     return None
        #
        # # 获取query字段数据（学生基础信息）
        # student_record = student_records.first()
        # student_info = student_record.query
        #
        # # 尝试多种方式解析学生信息
        # parsed_student_info = None
        # if isinstance(student_info, dict):
        #     # 如果已经是字典格式，直接使用
        #     parsed_student_info = student_info
        # elif isinstance(student_info, str):
        #     # 如果是字符串，尝试多种解析方式
        #     try:
        #         # 首先尝试标准JSON解析
        #         parsed_student_info = json.loads(student_info)
        #     except json.JSONDecodeError:
        #         try:
        #             # 如果JSON解析失败，尝试使用ast.literal_eval解析Python字典字符串
        #             parsed_student_info = ast.literal_eval(student_info)
        #         except (ValueError, SyntaxError) as e:
        #             logger.error(f"学生信息解析失败: {e}")
        #             logger.error(f"原始内容: {student_info[:200]}...")
        #             return None
        # else:
        #     logger.error(f"学生信息格式不正确: {type(student_info)}")
        #     return None
        #
        # # 确保解析后的数据是字典格式
        # if not isinstance(parsed_student_info, dict):
        #     logger.error(f"解析后的学生信息不是字典格式: {type(parsed_student_info)}")
        #     return None
        #
        # # 从目标院校信息中提取考试范围信息
        # target_school_info = parsed_student_info["目标院校信息"][0]
        # exam_scope = target_school_info["目标院校基本信息"]["考试范围"]
        # subject2_content = exam_scope["subject2"]
        #
        # # 输出调试信息
        # logger.info(f"成功提取subject2_content: {subject2_content}")
        #
        # # 如果subject2_content是"(408)计算机学科专业基础"这样的字符串，需要从录取信息中获取实际分数
        # if isinstance(subject2_content, str) and subject2_content.startswith("(408)"):
        #     # 从录取信息中查找对应科目的实际分数
        #     admission_info = target_school_info["目标院校基本信息"]["录取信息"]
        #     print(f"🚀🚀", admission_info )
        #     # 直接获取录取信息中的subject2值
        #     subject2_content = admission_info.get("subject2", 100)  # 默认100分
        #     print(f"🚀🚀", subject2_content)

        # # 加载知识点优先度数据
        # knowledge_priority = load_knowledge_priority()

        # 根据subject2_content值确定alpha和beta参数
        if target_score >= 115:
            alpha = 0.6
            beta = 0.2
        elif 80 <= target_score < 115:
            alpha = 0.7
            beta = 0.3
        else:  # target_score < 80
            alpha = 0.8
            beta = 0.4

        SUBJECT_MAX_SCORES = {
            '计算机组成原理': 45,
            '计算机网络': 25,
            '操作系统': 35,
            '数据结构': 45
        }

        # 读取数据
        df = load_knowledge_points()

        # 初始化（默认不按科目配分，直接全局把 150 分按频次分摊）
        selector = KnowledgePointSelector(
            df=df,
            exam_total_score=150,
            alpha=alpha,
            beta=beta,
            kappa=1.0,
            use_subject_caps=False,  # 若想尊重科目配分改 True
            subject_max_scores=SUBJECT_MAX_SCORES
        )

        plan = selector.recommend_to_target(
            target_score=target_score,
            safety=1.05,
            ensure_per_subject_k=2,  # 或 {'计算机网络':2, '数据结构':1}
            rank_by='priority'  # 也可 'gain'
        )
        # print(plan.to_string(index=False))
        knowledge_plan = plan.to_string(index=False)

        # 调用大模型一次性处理四门学科
        result = generate_all_subjects_kaogang(kaogang_content.kaogang_content, target_score, knowledge_plan)

        # 组合结果
        content = result.content if hasattr(result, 'content') else str(result)

        # 保存到PersonalizedExamSyllabus数据库
        try:
            exam_syllabus_obj = PersonalizedExamSyllabus.objects.get(student_score=target_score)
            # 如果记录已存在，则更新exam_syllabus字段
            exam_syllabus_obj.exam_syllabus = content
            exam_syllabus_obj.save()
            logger.info(f"个性化考纲已更新到数据库，学生分数: {target_score}")
        except PersonalizedExamSyllabus.DoesNotExist:
            # 如果记录不存在，则创建新记录
            exam_syllabus_obj = PersonalizedExamSyllabus.objects.create(
                student_score=target_score,
                exam_syllabus=content
            )
            logger.info(f"个性化考纲已创建到数据库，学生分数: {target_score}")

        return content

    except Exception as e:
        logger.error(f"生成个性化考研大纲时发生未预期的异常: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return None


# -*- coding: utf-8 -*-


# ---------------------------
# 1) 数据加载
# ---------------------------
def load_knowledge_points(subjects=None) -> pd.DataFrame:
    """
    从数据库读取知识点数据 -> DataFrame
    需要字段: subject, knowledge_point, total_freq, avg_difficulty
    其他字段保留以便后续扩展，但本简化方案不使用。
    """
    if subjects is None:
        subjects = ['计算机组成原理', '计算机网络', '操作系统', '数据结构']

    data = {
        'subject': [],
        'knowledge_point': [],
        'total_freq': [],
        'avg_difficulty': [],
        'mcq_freq': [],
        'mcq_difficulty': [],
        'subjective_freq': [],
        'subjective_difficulty': []
    }
    for subj in subjects:
        qs = ExamAnalysisKnowledgePointWithStats.objects.filter(subject=subj).values()
        for item in qs:
            data['subject'].append(item["subject"])
            data['knowledge_point'].append(item["point_name"])
            data['total_freq'].append(item.get("exam_count", 0))
            data['avg_difficulty'].append(item.get("avg_difficulty", 0.0))
            data['mcq_freq'].append(item.get("choice_count", 0))
            data['mcq_difficulty'].append(item.get("choice_avg_difficulty", 0.0))
            data['subjective_freq'].append(item.get("comprehensive_count", 0))
            data['subjective_difficulty'].append(item.get("comprehensive_avg_difficulty", 0.0))

    df = pd.DataFrame(data)
    # 基础清洗与类型安全
    for col in ['total_freq', 'avg_difficulty']:
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0.0)
    return df


# ---------------------------
# 2) 优先级与选集：核心类
# ---------------------------
class KnowledgePointSelector:
    """
    简化方案（科目内 z-score + 近似分值质量 + 可学性 + 贪心累加到目标分）
    """

    def __init__(
            self,
            df: pd.DataFrame,
            exam_total_score: float = 150.0,
            alpha: float = 0.7,  # priority 对考频的权重
            beta: float = 0.3,  # priority 对难度惩罚的权重
            kappa: float = 1.0,  # learnability 中对难度的敏感度
            use_subject_caps: bool = False,  # 是否按科目满分分摊分值质量
            subject_max_scores: Optional[Dict[str, float]] = None,
            subject_col: str = 'subject'
    ):
        self.df = df.copy()
        self.exam_total_score = float(exam_total_score)
        self.alpha = float(alpha)
        self.beta = float(beta)
        self.kappa = float(kappa)
        self.use_subject_caps = bool(use_subject_caps)
        self.subject_max_scores = subject_max_scores or {}
        self.subject_col = subject_col

        self._validate()
        self._compute_groupwise_zscores()
        self._compute_score_mass()
        self._compute_learnability()
        self._compute_gain()
        self._compute_priority()

    # ---------- 工具 ----------
    def _validate(self):
        needed = {'subject', 'knowledge_point', 'total_freq', 'avg_difficulty'}
        missing = needed - set(self.df.columns)
        if missing:
            raise ValueError(f"缺少必要字段: {missing}")
        # 过滤无 subject / 知识点的脏数据
        self.df['subject'] = self.df['subject'].astype(str)
        self.df['knowledge_point'] = self.df['knowledge_point'].astype(str)
        self.df = self.df[(self.df['subject'] != '') & (self.df['knowledge_point'] != '')].reset_index(drop=True)

    @staticmethod
    def _zscore_safe(x: pd.Series) -> pd.Series:
        mu = x.mean()
        sigma = x.std(ddof=0)
        if sigma == 0 or np.isnan(sigma):
            return pd.Series(np.zeros(len(x)), index=x.index)
        return (x - mu) / sigma

    # ---------- 核心计算 ----------
    def _compute_groupwise_zscores(self):
        # 科目内 z-score
        self.df['z_total_freq'] = self.df.groupby(self.subject_col)['total_freq'].transform(self._zscore_safe)
        self.df['z_avg_difficulty'] = self.df.groupby(self.subject_col)['avg_difficulty'].transform(self._zscore_safe)

    def _compute_score_mass(self):
        """
        以考频近似“分值质量”。
        - 默认: 全局按 total_freq 占比把 exam_total_score 分摊到每个知识点。
        - 可选: use_subject_caps=True 时，先在科目内按占比分摊到科目满分，再汇总（更贴合官方配分）。
        """
        if self.use_subject_caps:
            # 按科目满分分摊
            if not self.subject_max_scores:
                raise ValueError("use_subject_caps=True 但未提供 subject_max_scores。")
            self.df['score_mass'] = 0.0
            for subj, g in self.df.groupby(self.subject_col):
                cap = float(self.subject_max_scores.get(subj, 0.0))
                den = g['total_freq'].sum()
                if den <= 0:
                    # 若该科目无频次，均分该科满分（或直接给 0；此处采用均分更温和）
                    share = (cap / max(len(g), 1))
                    self.df.loc[g.index, 'score_mass'] = share
                else:
                    self.df.loc[g.index, 'score_mass'] = cap * (g['total_freq'] / den)
            # 校验总体近似为 sum(cap)
        else:
            # 全局分摊
            den = self.df['total_freq'].sum()
            if den <= 0:
                # 极端情况：所有频次为 0，则全体均分到总分
                self.df['score_mass'] = self.exam_total_score / max(len(self.df), 1)
            else:
                self.df['score_mass'] = self.exam_total_score * (self.df['total_freq'] / den)

    def _compute_learnability(self):
        """
        根据难度的科目内 z 分数，使用逻辑函数得到可学性(0~1)，难度越大越小。
        l = 1 / (1 + exp(kappa * z_diff))
        """
        z = self.df['z_avg_difficulty'].astype(float)
        self.df['learnability'] = 1.0 / (1.0 + np.exp(self.kappa * z))

    def _compute_gain(self):
        """
        预期可拿分 = 分值质量 * 可学性
        """
        self.df['gain'] = self.df['score_mass'] * self.df['learnability']

    def _compute_priority(self):
        """
        性价比优先级：priority = alpha * z_freq - beta * z_diff
        """
        self.df['priority'] = self.alpha * self.df['z_total_freq'] - self.beta * self.df['z_avg_difficulty']

    # ---------- 对外接口 ----------
    def recommend_global(self, top_k: int = 30, by: str = 'priority') -> pd.DataFrame:
        """
        返回全局 Top-K，by 可选 'priority' 或 'gain'
        """
        by = by.lower()
        if by not in ('priority', 'gain'):
            raise ValueError("by 必须是 'priority' 或 'gain'")

        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority'
        ]
        df_sorted = (self.df.sort_values([by, 'total_freq', 'avg_difficulty'],
                                         ascending=[False, False, True])
                     .loc[:, cols]
                     .reset_index(drop=True))
        return df_sorted.head(top_k)

    def recommend_per_subject(self, k_per_subject: int = 5, by: str = 'priority') -> pd.DataFrame:
        """
        每科 Top-k，by 可选 'priority' 或 'gain'
        """
        by = by.lower()
        if by not in ('priority', 'gain'):
            raise ValueError("by 必须是 'priority' 或 'gain'")

        def topk(g):
            return g.sort_values([by, 'total_freq', 'avg_difficulty'],
                                 ascending=[False, False, True]).head(k_per_subject)

        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority'
        ]
        out = (self.df.groupby(self.subject_col, group_keys=False)
               .apply(topk)
               .loc[:, cols]
               .reset_index(drop=True))
        return out

    def recommend_to_target(
            self,
            target_score,
            safety: float = 1.05,
            ensure_per_subject_k: Optional[Union[int, Dict[str, int]]] = None,
            rank_by: str = 'priority'
    ) -> pd.DataFrame:
        """
        给定目标分，输出一个“学习清单”，使累计 gain ≥ target_score * safety。
        - ensure_per_subject_k:
            * None: 不强制覆盖
            * int: 每个科目至少取 k 个
            * dict: 每科自定义保底数量，如 {'计算机网络':2, '数据结构':1}
        - rank_by: 'priority' 或 'gain'
        返回列包含 cumulative_gain 以便查看覆盖进度。
        """
        rank_by = rank_by.lower()
        if rank_by not in ('priority', 'gain'):
            raise ValueError("rank_by 必须是 'priority' 或 'gain'")

        # 1) 初步排序（稳定 tie-breaker：total_freq desc, avg_difficulty asc, kp 名字字典序）
        base = self.df.assign(
            kp_key=self.df['knowledge_point'].astype(str)
        ).sort_values(
            [rank_by, 'total_freq', 'avg_difficulty', 'kp_key'],
            ascending=[False, False, True, True]
        ).drop(columns=['kp_key'])

        # 2) 先做科目保底（如果需要）
        picked_idx = []
        if ensure_per_subject_k is not None:
            if isinstance(ensure_per_subject_k, int):
                want = {s: ensure_per_subject_k for s in base[self.subject_col].unique()}
            elif isinstance(ensure_per_subject_k, dict):
                want = {str(k): int(v) for k, v in ensure_per_subject_k.items()}
            else:
                raise ValueError("ensure_per_subject_k 需为 int 或 dict")

            for subj, need in want.items():
                if need <= 0:
                    continue
                g = base[base[self.subject_col] == subj]
                take = g.head(need)
                picked_idx.extend(list(take.index))

        picked_idx = list(dict.fromkeys(picked_idx))  # 去重保持顺序
        picked = base.loc[picked_idx].copy()
        rest = base.drop(index=picked_idx)

        # 3) 贪心补齐到目标 gain
        goal = float(target_score) * float(safety)
        if not picked.empty:
            picked['cumulative_gain'] = picked['gain'].cumsum()
        else:
            picked = pd.DataFrame(columns=base.columns.tolist() + ['cumulative_gain'])

        current = picked['gain'].sum() if not picked.empty else 0.0
        for i, row in rest.iterrows():
            if current >= goal:
                break
            picked = pd.concat([picked, row.to_frame().T], ignore_index=True)
            current += float(row['gain'])
            picked.loc[picked.index[-1], 'cumulative_gain'] = current

        # 4) 结果整理
        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority', 'cumulative_gain'
        ]
        result = picked.loc[:, cols].reset_index(drop=True)

        # 附加元信息
        result.attrs['target_score'] = target_score
        result.attrs['safety'] = safety
        result.attrs['goal_gain'] = goal
        result.attrs['achieved_gain'] = float(result['gain'].sum()) if not result.empty else 0.0
        result.attrs['alpha'] = self.alpha
        result.attrs['beta'] = self.beta
        result.attrs['kappa'] = self.kappa
        result.attrs['use_subject_caps'] = self.use_subject_caps
        return result