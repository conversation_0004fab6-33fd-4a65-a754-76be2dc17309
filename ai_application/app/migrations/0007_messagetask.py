# Generated by Django 4.2 on 2024-11-05 11:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0006_appmodelconfig_prompt_templates_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('process_status', models.CharField(choices=[('waiting', '未开始'), ('ing', '进行中'), ('success', '成功'), ('fail', '失败')], db_index=True, default='waiting', max_length=16, verbose_name='执行状态')),
                ('fail_reason', models.TextField(null=True, verbose_name='失败原因')),
                ('retry_times', models.IntegerField(default=0, verbose_name='重试次数')),
                ('last_retry_time', models.DateTimeField(null=True, verbose_name='最后重试时间')),
                ('next_retry_time', models.DateTimeField(db_index=True, null=True, verbose_name='下一次重试时间')),
                ('message', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
            ],
            options={
                'verbose_name': '模型消息任务表',
                'verbose_name_plural': '模型消息任务表',
                'db_table': 'ai_message_task',
            },
        ),
    ]
