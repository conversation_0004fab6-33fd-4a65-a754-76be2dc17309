from typing import Any

from app.constants.common import FormParameterType
from django_ext.base_dto_model import MyBaseModel


class ParameterRule(MyBaseModel):
    name: str
    label: str
    help: str | None = None
    placeholder: str | None = None
    type: FormParameterType
    required: bool = False
    max_length: int | None = None
    options: list[str] | None = None
    default: Any | None = None


class PromptFormEntity(MyBaseModel):
    parameter_rules: list[ParameterRule]
