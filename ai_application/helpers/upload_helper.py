import os
import uuid

from django.conf import settings

from helpers.oss_client import get_oss_client


def upload_file(
        file,
        file_name: str,
        sub_path: str,
        headers: dict | None = None
) -> str:
    if headers is None:
        headers = {}

    file_ext = file_name.split('.')[-1].lower()
    upload_file_name = f'{uuid.uuid4().hex}.{file_ext}'
    full_path = f'uploads/{sub_path}/{upload_file_name}'

    oss_client = get_oss_client()
    res = oss_client.upload_object(
        obj=file,
        full_path=full_path,
        headers=headers
    )
    return os.path.join(settings.OSS_HTTP, res)
