import logging

from api_client.base_client import BaseClient

logger = logging.getLogger(__name__)

__all__ = ['exam_client']


class ExamClient(BaseClient):
    host = 'http://exam-svc:8000'
    host_test = 'http://exam.kaoyan-vip.cn'
    host_local = 'http://exam.kaoyan-vip.cn'

    @property
    def base_url(self):
        return f'{self.host}'

    def get_paper_detail(self, paper_uuid):
        url = f'/api/v1/paper/{paper_uuid}/'
        res = self._get(url, timeout=30)
        return res

    def get_answer_detail(self, answer_id):
        url = f'/api/v1/answer/{answer_id}/'
        res = self._get(url, timeout=30)
        return res


exam_client = ExamClient()
