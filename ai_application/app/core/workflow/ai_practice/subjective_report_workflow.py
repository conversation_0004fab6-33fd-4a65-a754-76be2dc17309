import time

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate

from app.core.features.llm_ocr import LLMOcrFeature
from app.core.invoke_llm import Lang<PERSON>hainLlmWrapper
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import PromptTemplate, STUserPaperQuestionAnswer


class SubjectiveReportWorkflow(BaseWorkflow):

    def run(self):
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no='shuati_subjective_report').first()
        if not prompt_template:
            raise ValueError('没有找到对应的提示词')

        inputs = self.application_generate_entity.inputs.copy()
        question_answer_id = inputs.get('question_answer_id')
        question_answer: STUserPaperQuestionAnswer = STUserPaperQuestionAnswer.objects.filter(
            id=question_answer_id).first()
        if not question_answer:
            raise ValueError('问题不存在')
        question = question_answer.question

        # 目前只处理主观题
        format_question = question.get_format_question_content()
        question_content = format_question.title

        user_answer = self.application_generate_entity.query
        image_urls = self.application_generate_entity.file_objs
        if image_urls:
            image_text = LLMOcrFeature().run(image_urls, tracing_log=self.tracing_log)
            if image_text:
                question_answer.image_text = image_text
                question_answer.save(update_fields=['image_text'])

            user_answer = f'{user_answer}\n{image_text}'

        query = f'题目内容：\n{question_content}\n\n用户回答：\n{user_answer}'

        prompt_messages = [
            SystemMessage(content=prompt_template.prompt_content),
            HumanMessage(content=query)
        ]

        wrapper = LangChainLlmWrapper(
            model_provider=prompt_template.model_provider,
            model_id=prompt_template.model_id,
            model_params=prompt_template.model_params,
        )
        llm = wrapper.get_llm()

        prompt = ChatPromptTemplate.from_messages(prompt_messages)

        start_time = time.perf_counter()
        invoke_result = (prompt | llm).invoke({})
        latency = time.perf_counter() - start_time
        llm_result = wrapper.handle_non_stream_response(invoke_result, latency)

        self._handle_node_response(
            llm_result,
            stream=self.stream,
            tracing_info={
                'tracing_type': 'shuati_subjective_report',
                'query': query,
            }
        )
        self._publish_workflow_end_message()
