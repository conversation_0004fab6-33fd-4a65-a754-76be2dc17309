import json


def huolande_test(content):
    testlist = content
    print(f"霍兰德测试测试结果：{testlist}")
    # 创建类别名称到首字母缩写的映射
    dimension_abbreviation = {
        'realistic': 'R',
        'investigative': 'I',
        'artistic': 'A',
        'social': 'S',
        'enterprising': 'E',
        'conventional': 'C'
    }

    # 处理每个用户的数据
    for i in range(len(testlist)):
        hashmap = {'realistic': 0, 'investigative': 0, 'artistic': 0, 'social': 0, 'enterprising': 0, 'conventional': 0}

        # 计算每个维度的总分
        for p in testlist[i]['results']:
            for d in testlist[i]['results'][p]['dimensions']:
                if isinstance(testlist[i]['results'][p]['dimensions'][d], list):
                    s = sum(testlist[i]['results'][p]['dimensions'][d])
                    hashmap[d] += s
                else:
                    hashmap[d] += testlist[i]['results'][p]['dimensions'][d]

        # 获取总分排名前三的维度名称
        top_3_dimensions = sorted(hashmap.items(), key=lambda x: x[1], reverse=True)[:3]

        top_3_names = ''.join([dimension_abbreviation[dimension] for dimension, score in top_3_dimensions])

        print(top_3_names)
        return top_3_names