# Generated by Django 4.2 on 2025-03-17 09:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0028_merge_20250317_1142'),
    ]

    operations = [
        migrations.CreateModel(
            name='KnowledgeLibrary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('subject', models.CharField(blank=True, max_length=100, verbose_name='考研科目')),
                ('main_subject', models.CharField(blank=True, max_length=100, verbose_name='主科目')),
                ('nature', models.CharField(choices=[('common', '通用名词'), ('major', '专有名词')], default='major', max_length=32, verbose_name='名词特征')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='名词')),
                ('desc', models.TextField(null=True, verbose_name='名词描述')),
                ('old_knowledge_id', models.CharField(blank=True, max_length=32, verbose_name='旧知识点库id')),
            ],
            options={
                'verbose_name': '知识点库',
                'verbose_name_plural': '知识点库',
                'db_table': 'ai_data_knowledge_library',
            },
        ),
        migrations.AddField(
            model_name='coursesectionknowledge',
            name='is_extract_knowledge',
            field=models.BooleanField(default=False, verbose_name='是否已提取知识点'),
        ),
        migrations.AddField(
            model_name='questionknowledge',
            name='is_extract_knowledge',
            field=models.BooleanField(default=False, verbose_name='是否已提取知识点'),
        ),
        migrations.CreateModel(
            name='QuestionKnowledgeMap',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_id', models.CharField(blank=True, max_length=100, verbose_name='题目ID')),
                ('knowledge', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.knowledgelibrary')),
            ],
            options={
                'verbose_name': '题目知识点关联',
                'verbose_name_plural': '题目知识点关联',
                'db_table': 'ai_data_question_knowledge_map',
            },
        ),
    ]
