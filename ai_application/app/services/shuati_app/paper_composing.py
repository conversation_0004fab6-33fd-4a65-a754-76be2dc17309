from django.db import transaction

from app.models import (
    STUser<PERSON><PERSON>, STUserRoundAnalysis, STUserPaperQuestion, STUserRoundChangeRecord
)
from app.services.shuati_app.constants import LearningStage, StageChangeType
from app.services.shuati_app.dto import ModiUpRequired
from app.services.shuati_app.select_question_second import (
    BasicQuestionGenerator, ModiQuestionGenerator
)
from app.services.shuati_app.subject_strategy import get_subject_question_strategy, get_subject_change_required

from django_ext.exceptions import InternalException


class PaperComposingClient:

    def __init__(self, subject_id: str, core_course_code: str, user_id: str):
        self.subject_id = subject_id
        self.core_course_code = core_course_code
        self.user_id = user_id

        self.strategy_map = get_subject_question_strategy(self.subject_id, self.core_course_code)

    def get_paper(self) -> STUserPaper | None:
        paper: STUserPaper = STUserPaper.objects.filter(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
        ).order_by('-id').first()
        # 如果试卷未作答，则直接返回
        if paper and not paper.is_answered:
            return paper

        return self._get_paper_by_stage()

    def _get_paper_by_stage(self) -> STUserPaper | None:
        # 根据阶段返回对应策略的试卷
        user_round_analysis, _ = STUserRoundAnalysis.objects.get_or_create(
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id
        )
        # 测试已停止
        if user_round_analysis.is_test_stop:
            return None

        single_strategy_map = self.strategy_map.get(user_round_analysis.learning_stage)
        if not single_strategy_map:
            raise InternalException(code=50000, detail='出题策略错误')

        if user_round_analysis.learning_stage == LearningStage.MODI:

            if user_round_analysis.examined_rounds <= 0:
                stage_required_map = get_subject_change_required(self.subject_id, self.core_course_code)
                modi_up_required: ModiUpRequired | None = stage_required_map.get(LearningStage.MODI)
                if not modi_up_required:
                    raise InternalException(code=50000, detail='阶段错误')

                user_round_analysis.stage_total_rounds = modi_up_required.rounds_required
                user_round_analysis.save(update_fields=['stage_total_rounds'])

            is_first_round = user_round_analysis.stage_examined_rounds == 0

            strategy = single_strategy_map.get('normal')
            generator = ModiQuestionGenerator(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                strategy=strategy,
                cycle_num=user_round_analysis.cycle_num,
                is_first_round=is_first_round,
            )
        else:
            if user_round_analysis.is_stage_strengthen:
                strategy = single_strategy_map.get('strengthen')
            else:
                strategy = single_strategy_map.get('normal')

            is_all_wrong_kg = self._check_is_all_wrong_kg(user_round_analysis)
            # print('is_all_wrong_kg', is_all_wrong_kg)
            generator = BasicQuestionGenerator(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                strategy=strategy,
                is_all_wrong_kg=is_all_wrong_kg,
                cycle_num=user_round_analysis.cycle_num,
            )

        question_ids = generator.get_questions()
        question_score_map = generator.get_question_score_map()

        return self._create_paper(
            learning_stage=user_round_analysis.learning_stage,
            is_stage_strengthen=user_round_analysis.is_stage_strengthen,
            cycle_num=user_round_analysis.cycle_num,
            stage_round=user_round_analysis.stage_examined_rounds + 1,
            stage_total_rounds=user_round_analysis.stage_total_rounds,
            total_round=user_round_analysis.examined_rounds + 1,
            question_ids=question_ids,
            question_score_map=question_score_map
        )

    def _check_is_all_wrong_kg(self, user_round_analysis: STUserRoundAnalysis) -> bool:
        if user_round_analysis.is_stage_strengthen:
            return True

        # 判断是否是掉过该阶段
        has_curr_stage_down = STUserRoundChangeRecord.objects.filter(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            new_stage=user_round_analysis.learning_stage,
            change_type=StageChangeType.DOWN,
            cycle_num=user_round_analysis.cycle_num,
        ).count() > 0
        if has_curr_stage_down:
            return True

        # 判断是否是升级到该阶段2次
        has_curr_stage_up_again = STUserRoundChangeRecord.objects.filter(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            new_stage=user_round_analysis.learning_stage,
            change_type=StageChangeType.UP,
            cycle_num=user_round_analysis.cycle_num,
        ).count() > 1
        return has_curr_stage_up_again

    def _create_paper(
            self,
            learning_stage,
            is_stage_strengthen: bool,
            cycle_num: int,
            stage_round,
            stage_total_rounds,
            total_round,
            question_ids: list,
            question_score_map: dict
    ) -> STUserPaper:
        with transaction.atomic():
            paper = STUserPaper.objects.create(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                question_ids=question_ids,
                learning_stage=learning_stage,
                is_stage_strengthen=is_stage_strengthen,
                stage_round=stage_round,
                stage_total_rounds=stage_total_rounds,
                total_round=total_round,
                paper_score=sum(question_score_map.values()),
                cycle_num=cycle_num,
            )
            question_objs = [STUserPaperQuestion(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                paper=paper,
                question_id=q_id,
                score=question_score_map.get(q_id, 0),
                cycle_num=cycle_num,
            ) for q_id in question_ids]
            STUserPaperQuestion.objects.bulk_create(question_objs)
        return paper
