from app.models import DatasetRetrieverResource, Dataset, DatasetQuery, Message


class DatasetIndexToolCallbackHandler:
    def __init__(
            self,
            message: Message
    ) -> None:
        self._message = message

    def on_query(self, query: str, dataset: Dataset) -> None:
        DatasetQuery.objects.create(
            dataset=dataset,
            content=query,
            app=self._message.app,
            message=self._message,
        )

    def return_retriever_resource_info(self, resource: list[dict]):
        retriever_resource_list = []
        if resource and len(resource) > 0:
            for item in resource:
                retriever_resource_list.append(DatasetRetrieverResource(
                    message=self._message,
                    position=item.get('position'),
                    dataset=item.get('dataset'),
                    dataset_name=item.get('dataset_name'),
                    dataset_document=item.get('dataset_document'),
                    dataset_document_name=item.get('dataset_document_name'),
                    data_source_type=item.get('data_source_type'),
                    segment=item.get('segment'),
                    content=item.get('content'),
                    content_with_context=item.get('content_with_context'),
                    score=item.get('score'),
                ))
        DatasetRetrieverResource.objects.bulk_create(retriever_resource_list)
