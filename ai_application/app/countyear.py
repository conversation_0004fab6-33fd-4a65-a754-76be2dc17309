from django.db.models import Avg, Count
from collections import defaultdict
from app.models.exam_analyis import (
    ExamAnalysisKnowledgePoint, 
    ExamAnalysisExamQuestion,
    ExamAnalysisKnowledgePointAnalysis
)

def analyze_knowledge_points(subject_code=3):
    # 获取指定学科代码的知识点及其对应学科
    all_knowledge_points = {}
    for point in ExamAnalysisKnowledgePoint.objects.filter(subject_code_id=subject_code):
        all_knowledge_points[point.point_name] = point.subject

    # 获取指定学科代码的所有年份
    years = ExamAnalysisExamQuestion.objects.filter(subject_code_id=subject_code).values_list('year', flat=True).distinct()

    for year in years:
        # 获取该年份所有题目
        questions = ExamAnalysisExamQuestion.objects.filter(year=year, subject_code_id=subject_code)
        
        knowledge_point_difficulty = defaultdict(list)
        knowledge_point_questions = defaultdict(int)
        knowledge_point_numbers = defaultdict(list)

        # 初始化所有知识点的计数为0
        for kp in all_knowledge_points:
            knowledge_point_difficulty[kp] = []
            knowledge_point_questions[kp] = 0
            knowledge_point_numbers[kp] = []

        for question in questions:
            question_difficulty = question.difficulty or 1  # 默认为难度1
            question_kps = question.knowledge_points or []
            
            # 更新知识点的频率和难度
            for kp in question_kps:
                if kp in all_knowledge_points:  # 只处理有学科映射的知识点
                    knowledge_point_difficulty[kp].append(question_difficulty)
                    knowledge_point_questions[kp] += 1
                    knowledge_point_numbers[kp].append(question.question_number)

        # 创建题号难度字典
        knowledge_point_number_difficulty = {}
        for kp, numbers in knowledge_point_numbers.items():
            difficulty_dict = {}
            for i, number in enumerate(numbers):
                difficulty = knowledge_point_difficulty[kp][i]
                difficulty_dict[number] = difficulty
            knowledge_point_number_difficulty[kp] = difficulty_dict

        # 动态生成学科分组结构
        year_result = {}
        subjects = set(all_knowledge_points.values())
        
        for subject in subjects:
            year_result[subject] = {}
        
        for kp, subject in all_knowledge_points.items():
            if knowledge_point_questions[kp] > 0:  # 只保留考查题目数大于0的知识点
                year_result[subject][kp] = {
                    "考查题目数": knowledge_point_questions[kp],
                    "题号难度": knowledge_point_number_difficulty[kp]
                }

        # 保存到KnowledgePointAnalysis模型
        for subject, kp_data in year_result.items():
            for kp_name, kp_info in kp_data.items():
                ExamAnalysisKnowledgePointAnalysis.objects.update_or_create(
                    year=year,
                    subject=subject,
                    point_name=kp_name,
                    defaults={
                        'question_count': kp_info["考查题目数"],
                        'question_difficulty': kp_info["题号难度"],
                        'subject_code_id': subject_code
                    }
                )

    print("知识点分析完成，结果已保存到数据库")
analyze_knowledge_points()
if __name__ == "__main__":
    analyze_knowledge_points()
