from collections import defaultdict

from app.models import (
    STUserPaperAnswer, STUserKnowledgeMasteryLevel, STQuestionKnowledge, STKnowledge, STUserKnowledgeDistribution
)
from app.services.shuati_app.dto import KnowledgeMasteryLevel


def get_user_mastery_level(subject_id, core_course_code, user_id) -> list[KnowledgeMasteryLevel]:
    mastery_levels = STUserKnowledgeMasteryLevel.objects.filter(
        user_id=user_id,
        subject_id=subject_id,
        core_course_code=core_course_code
    ).select_related('knowledge')

    user_desc = []
    for level in mastery_levels:
        user_desc.append(
            KnowledgeMasteryLevel(
                knowledge_name=level.knowledge.name,
                accuracy=level.accuracy
            )
        )

    return user_desc


def update_user_knowledge_mastery_level(paper_answer: STUserPaperAnswer):
    paper = paper_answer.paper
    question_ids = paper.get_question_ids()

    # 按题目ID分组知识点，获取所有题目对应的知识点映射关系
    question_knowledge_relations = STQuestionKnowledge.objects.filter(
        is_deleted=False,
        question_id__in=question_ids
    ).select_related('knowledge')

    question_to_knowledge_list = defaultdict(list)
    all_knowledge_map = {}
    for relation in question_knowledge_relations:
        question_to_knowledge_list[relation.question_id].append(relation.knowledge)
        all_knowledge_map[relation.knowledge_id] = relation.knowledge

    user_question_answers = paper_answer.stuserpaperquestionanswer_set.filter(is_deleted=False)

    # 遍历每个题目，更新对应知识点的答题情况
    answered_kg_stat = defaultdict(dict)

    # 遍历每个题目，更新对应知识点，题型，难度的答题情况
    answered_kg_distribute = defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))

    user_last_kg_stat_map = {}
    for a in user_question_answers:
        question_knowledge_list = question_to_knowledge_list.get(a.question_id, [])
        for knowledge in question_knowledge_list:
            # 按照答题时间前后记录知识点顺序
            if not user_last_kg_stat_map.get(knowledge.id):
                user_last_kg_stat_map[knowledge.id] = {
                    'last_answer_status': 'right', 'last_answer_time': a.add_time
                }

            q_type = a.question.question_type
            q_d = a.question.difficulty
            if not answered_kg_distribute[knowledge.id][q_type][q_d]:
                answered_kg_distribute[knowledge.id][q_type][q_d] = {
                    'answer_count': 0, 'right_count': 0, 'wrong_count': 0,
                }

            if not answered_kg_stat[knowledge.id]:
                answered_kg_stat[knowledge.id] = {
                    'answer_count': 0, 'right_count': 0, 'wrong_count': 0,
                }

            # 增加答题次数
            answered_kg_distribute[knowledge.id][q_type][q_d]['answer_count'] += 1
            answered_kg_stat[knowledge.id]['answer_count'] += 1

            # 如果答案正确，增加正确次数
            if a.answer_status == 'right':
                answered_kg_distribute[knowledge.id][q_type][q_d]['right_count'] += 1
                answered_kg_stat[knowledge.id]['right_count'] += 1
            elif a.answer_status == 'wrong':
                answered_kg_distribute[knowledge.id][q_type][q_d]['wrong_count'] += 1
                answered_kg_stat[knowledge.id]['wrong_count'] += 1
                user_last_kg_stat_map[knowledge.id]['last_answer_status'] = 'wrong'
            elif a.answer_status == 'subjective':
                if a.score_rate >= 80:
                    # 主观题得分率达到80%
                    answered_kg_distribute[knowledge.id][q_type][q_d]['right_count'] += 1
                    answered_kg_stat[knowledge.id]['right_count'] += 1
                else:
                    answered_kg_distribute[knowledge.id][q_type][q_d]['wrong_count'] += 1
                    answered_kg_stat[knowledge.id]['wrong_count'] += 1
                    user_last_kg_stat_map[knowledge.id]['last_answer_status'] = 'wrong'

    # 获取知识点答题情况分布
    for kg_id, type_diff_stat in answered_kg_distribute.items():
        knowledge: STKnowledge = all_knowledge_map.get(kg_id)
        for q_type, diff_stat in type_diff_stat.items():
            for diff, stat in diff_stat.items():
                answer_count = stat.get('answer_count', 0)
                if not answer_count:
                    continue

                right_count = stat.get('right_count', 0)
                wrong_count = stat.get('wrong_count', 0)

                kg_dist_obj, _ = STUserKnowledgeDistribution.objects.get_or_create(
                    subject_id=knowledge.subject_id,
                    core_course_code=knowledge.core_course_code,
                    user_id=paper.user_id,
                    knowledge=knowledge,
                    question_type=q_type,
                    difficulty=diff,
                    cycle_num=paper_answer.cycle_num,
                )

                kg_dist_obj.answer_count += answer_count
                kg_dist_obj.right_count += right_count
                kg_dist_obj.wrong_count += wrong_count
                kg_dist_obj.accuracy = round(kg_dist_obj.right_count / kg_dist_obj.answer_count * 100)
                kg_dist_obj.save()

    # 批量更新知识点答题情况
    for kg_id, kg_stat in answered_kg_stat.items():
        knowledge: STKnowledge = all_knowledge_map.get(kg_id)

        answer_count = kg_stat.get('answer_count', 0)
        if not answer_count:
            continue

        right_count = kg_stat.get('right_count', 0)
        wrong_count = kg_stat.get('wrong_count', 0)

        mastery_level, _ = STUserKnowledgeMasteryLevel.objects.get_or_create(
            subject_id=knowledge.subject_id,
            core_course_code=knowledge.core_course_code,
            user_id=paper.user_id,
            knowledge=knowledge,
        )
        mastery_level.answer_count += answer_count
        mastery_level.right_count += right_count
        mastery_level.wrong_count += wrong_count
        mastery_level.accuracy = round(mastery_level.right_count / mastery_level.answer_count * 100)
        if knowledge and knowledge.kg_qs_count > 0:
            mastery_level.total_accuracy = round(mastery_level.right_count / knowledge.kg_qs_count * 100)

        # 更新轮次
        mastery_level.occr_round += 1
        if wrong_count:
            mastery_level.consecutive_right_round = 0
            mastery_level.consecutive_wrong_round += 1
        else:
            mastery_level.consecutive_right_round += 1
            mastery_level.consecutive_wrong_round = 0

        user_last_kg_stat_i = user_last_kg_stat_map.get(kg_id)
        if user_last_kg_stat_i:
            mastery_level.last_answer_status = user_last_kg_stat_i.get('last_answer_status')
            mastery_level.last_answer_time = user_last_kg_stat_i.get('last_answer_time')
        mastery_level.save()
