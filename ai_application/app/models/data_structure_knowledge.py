from django.db import models
from ._base import BaseModel
from .knowledge_simple import KnowledgeSimple
from .knowledge_video import KnowledgeVideo


class DataStructureKnowledge(BaseModel):
    subject_1 = models.CharField(max_length=255, verbose_name="一级学科")
    subject_2 = models.Char<PERSON>ield(max_length=255, verbose_name="二级学科")
    categories = models.CharField(max_length=255, verbose_name="章节")
    knowledge_points = models.CharField('知识点', max_length=100)
    definition = models.TextField('定义', null=True)
    desc = models.TextField('描述', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '数据结构知识点'
        db_table = 'ai_data_structure_knowledge'

    def __str__(self):
        return f"{self.subject_1} - {self.subject_2} - {self.categories} - {self.knowledge_points}"

    @property
    def video(self):
        kv = KnowledgeVideo.objects.filter(
            core_course_name=self.subject_2, knowledge_name=self.knowledge_points).first()
        if kv:
            return kv.video
        return ''

