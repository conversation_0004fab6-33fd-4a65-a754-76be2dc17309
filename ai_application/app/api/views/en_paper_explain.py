import uuid

from django.conf import settings

from app.errors import ParameterError
from django_ext import base_view
from django_ext.exceptions import InternalException
from django_ext.response import make_response
from app.models import EnglishPaperExplain


class EnglishPaperExplainView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        answer_id = request.data.get('answer_id')
        if not user_id or not answer_id:
            raise ParameterError()

        # 压测使用
        if settings.ENVIRONMENT == settings.ENV_PRODUCT:
            paper_explain = EnglishPaperExplain.objects.filter(answer_id=answer_id).first()
            if paper_explain:
                return make_response({
                    'task_id': paper_explain.task_id
                })

        paper_explain = EnglishPaperExplain.objects.create(
            user_id=user_id,
            answer_id=answer_id,
            summary_status='NOT_START',
            status='NOT_START',
            task_id=str(uuid.uuid4())
        )
        return make_response({
            'task_id': paper_explain.task_id
        })


class EnglishPaperExplainStatusView(base_view.BaseView):
    def get(self, request, *args, **kwargs):
        task_id = request.query_params.get('task_id')
        paper_explain: EnglishPaperExplain = EnglishPaperExplain.objects.filter(
            is_deleted=False, task_id=task_id).first()
        if not paper_explain:
            raise InternalException(code=50000, detail='该任务不存在')

        if paper_explain.status == 'SUCCESS':
            question_qs = paper_explain.englishpaperquestionexplain_set.filter(
                is_deleted=False, status='SUCCESS')

            questions_explain = []
            for q in question_qs:
                questions_explain.append({
                    'question_id': q.question_id,
                    'question_type': q.question_type,
                    'explain_content': q.explain_content,
                })
            return make_response({
                'status': paper_explain.status,
                'data': {
                    'questions': questions_explain,
                    'summary': paper_explain.summary_content,
                },
            })
        if paper_explain.status == 'FAIL':
            return make_response({
                'status': paper_explain.status,
                'fail_reason': paper_explain.fail_reason
            })

        return make_response({'status': 'ING'})
