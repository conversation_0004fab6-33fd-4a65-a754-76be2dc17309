from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain_core.runnables import RunnableLambda, RunnableBranch
from functools import lru_cache
import os
import re
from typing import List, Union
# from .app import dayiapp
# print(type(dayiapp))

# 配置大模型实例
llm_text = ChatOpenAI(
    openai_api_key=os.environ.get("ARK_API_KEY"),
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-pro-256k-241115"
)

llm_visual = ChatOpenAI(
    openai_api_key=os.environ.get("ARK_API_KEY"),
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-1.5-vision-pro-250328"
)

# 系统提示词
SYSTEM_PROMPT = """# 角色
你是一个专业且全面的知识解惑专家，能够精准解答用户提出的各类疑问甚至是专业性的问题，为用户提供准确、可靠且丰富的专业知识与信息。

## 技能
### 技能 1: 解答疑问
1. 当用户提出问题时，仔细分析问题类型。
2. 运用自身知识储备，结合必要的工具搜索，给出清晰、准确的答案。
3. 确保回答逻辑清晰、易于理解。

### 技能 2: 提供专业知识和信息
1. 如果用户需要特定领域的专业知识或信息，迅速定位相关内容。
2. 对知识和信息进行整理、提炼，以简洁明了的方式呈现给用户。

### 输出格式：
- 解答问题：
- 专业知识补充：

## 限制
- 仅围绕解答用户疑问、提供专业知识和信息展开交流，拒绝回答与该任务无关的话题。
- 回答需简洁明了，避免冗长复杂的表述。
- 提供的知识和信息要基于可靠来源，若需引用需明确说明。"""

# 图片URL正则匹配
IMAGE_URL_PATTERN = re.compile(
    r'(https?://\S+\.(?:jpg|jpeg|png|gif|bmp|webp)(?:\?\S*)?)',
    re.IGNORECASE
)


# 增加预处理函数
def preprocess_input(input_str: str) -> str:
    """统一处理特殊字符和多余空格"""
    input_str = re.sub(r'\s+', ' ', input_str).strip()  # 合并连续空格
    return input_str

# 修改分类器
def classify_content_type(input_dict: dict) -> str:
    input_str = preprocess_input(input_dict["input"])
    return "visual" if IMAGE_URL_PATTERN.search(input_str) else "text"


def build_visual_message(input_dict: dict) -> List[HumanMessage]:
    """构建视觉模型消息"""
    input_str = input_dict["input"]
    # 提取内容组件
    text_content = IMAGE_URL_PATTERN.sub('', input_str).strip()
    image_urls = IMAGE_URL_PATTERN.findall(input_str)
    # 构建消息内容
    message_content = []
    if text_content:
        full_text = SYSTEM_PROMPT + "\n\n用户问题：" + text_content
    else:
        full_text = SYSTEM_PROMPT + "\n\n请根据图片内容进行分析"

    message_content.append({"type": "text", "text": full_text})

    for url in image_urls:
        message_content.append({
            "type": "image_url",
            "image_url": {"url": url}
        })

    return [HumanMessage(content=message_content)]

# 增加缓存机制
@lru_cache(maxsize=128)
def classify_content_type_cached(input_str: str) -> str:
    return "visual" if IMAGE_URL_PATTERN.search(input_str) else "text"


# 异步处理支持
async def async_process_chain(input_str: str):
    from langchain_core.runnables import RunnableParallel

    chain = RunnableParallel({
        "classification": RunnableLambda(classify_content_type),
        "content": RunnableLambda(lambda x: x["input"])
    }) | full_chain

    return await chain.invoke({"input": input_str})


# 文本处理链
text_prompt = ChatPromptTemplate.from_messages([
    SystemMessage(content=SYSTEM_PROMPT),
    HumanMessagePromptTemplate.from_template("{input}")
])

text_chain = (
        text_prompt
        | llm_text
        | RunnableLambda(lambda x: x.content)
)

# 视觉处理链
visual_chain = (
        RunnableLambda(build_visual_message)
        | llm_visual
        | RunnableLambda(lambda x: x.content)
)

# 构建路由链
full_chain = RunnableBranch(
    (lambda x: classify_content_type(x) == "visual", visual_chain),
    text_chain
).with_types(input_type=dict)

# 最终处理管道
final_chain = RunnableLambda(lambda x: {"input": x}) | full_chain

def get_chain():
    """获取可部署的链实例"""
    return final_chain

# 使用示例
if __name__ == "__main__":
    test_cases = [
        # "五一劳动节是什么时候",
        "https://oss.kaoyanvip.cn/attach/file1745747749916.png ， 请分析这篇作文",
        # "比较这两个logo：https://example.com/logo1.png 和 https://example.com/logo2.png"
    ]

    for case in test_cases:
        print(f"输入：{case}")
        print(f"输出：{final_chain.invoke(case)}\n{'-' * 40}")
        print(f"输出：{final_chain.invoke(case)}\n{'-' * 40}")