import logging
import threading
from typing import Generator

from app.api.dto import Chat<PERSON><PERSON>ageD<PERSON>, ConversationAdd
from app.constants.app import ConversationStatus
from app.core.app_response.chat_response import ChatResponse
from app.core.apps.base_app_generator import BaseAppGenerator
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.apps.chat.app_runner import Chat<PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.apps.message_based_app_queue_manage import MessageBasedAppQueueManager
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.entities.queue_entities import QueueErrorEvent
from app.errors import (
    ConversationFinishedError, ConversationTokenExceedError, DatasetNotFoundError, ContainSensitiveError,
    SpecialMessageError, LLMRequestError
)
from app.models import App, Account, Conversation, Message, Dataset, InvokeFrom

logger = logging.getLogger(__name__)


class ChatAppGenerator(BaseAppGenerator):
    def generate(
            self,
            app_model: App,
            dto: ChatMessageDto,
            from_account: Account | None = None,
            invoke_from: str = InvokeFrom.api.value,
    ) -> Generator:
        # 查询会话历史
        conversation = None
        if dto.conversation_id:
            conversation = self._get_conversation(app_model, dto.conversation_id)

        query = dto.query.replace('\x00', '')

        user_inputs = {}
        if conversation:
            user_inputs = conversation.inputs or {}
        if isinstance(dto.inputs, dict):
            user_inputs.update(dto.inputs)

        return self._generate(
            query=query,
            user_inputs=user_inputs,
            app_model=app_model,
            conversation=conversation,
            pre_prompt=dto.pre_prompt,
            message_type=dto.message_type,
            file_objs=dto.file_objs,
            from_account=from_account,
            from_biz_id=dto.biz_id,
            from_user_info=dto.userinfo,
            stream=dto.stream,
            is_override=dto.is_override,
            invoke_from=invoke_from,
        )

    def _generate(
            self,
            query: str,
            user_inputs: dict,
            app_model: App,
            conversation: Conversation,
            from_account: Account,
            from_biz_id: str = '',
            from_user_info: dict | None = None,
            pre_prompt: str = '',
            message_type: str = 'normal',
            file_objs: list | None = None,
            last_message: Message | None = None,
            stream: bool = True,
            is_override: bool = False,
            invoke_from: str = InvokeFrom.api.value,
    ):
        app_model_config = self._get_app_model_config(app_model, conversation)

        prompt_template = self._get_prompt_template(user_inputs=user_inputs, app_model_config=app_model_config)
        model_config = self._get_model_config(
            user_inputs=user_inputs,
            app_model_config=app_model_config,
            conversation=conversation,
            is_override=is_override,
            prompt_template=prompt_template,
        )
        pre_prompt = self._get_pre_prompt(
            pre_prompt=pre_prompt,
            user_inputs=user_inputs,
            app_model_config=app_model_config,
            conversation=conversation,
            is_override=is_override,
            prompt_template=prompt_template,
        )

        if conversation and not is_override:
            conversation_hash = conversation.conversation_hash
        else:
            conversation_hash = self._generate_conversation_hash()

        application_generate_entity = ChatAppGenerateEntity(
            app_no=app_model.app_no,
            app_type=app_model.app_type,
            app_model_id=app_model.id,
            app_model_config_id=app_model_config.id,
            model_conf=model_config,
            pre_prompt=pre_prompt,
            query=query,
            inputs=user_inputs,
            conversation_hash=conversation_hash,
            context_limit=3,
            is_override=is_override,
            message_type=message_type,
            file_objs=file_objs,
            stream=stream,
            invoke_from=invoke_from,
            support_tools=app_model_config.support_tools or []
        )

        # 重写逻辑记录inputs
        if conversation and is_override:
            conversation.inputs = application_generate_entity.inputs
            conversation.conversation_hash = application_generate_entity.conversation_hash
            conversation.model_params = application_generate_entity.model_conf.model_params
            conversation.pre_prompt = application_generate_entity.pre_prompt
            conversation.save(update_fields=['inputs', 'conversation_hash', 'model_params', 'pre_prompt'])

        # 创建conversion 和 message
        (
            conversation,
            message
        ) = self._init_generate_records(
            application_generate_entity=application_generate_entity,
            from_account=from_account,
            from_biz_id=from_biz_id,
            userinfo=from_user_info,
            conversation=conversation,
            last_message=last_message
        )
        # 处理本地文件
        if conversation.dataset_id:
            application_generate_entity.dataset_id = conversation.dataset_id
            if conversation.document_nos:
                application_generate_entity.document_nos = conversation.document_no_list

        queue_manager = MessageBasedAppQueueManager(
            task_id=message.message_no,
            conversation_id=conversation.conversation_no,
            message_id=message.message_no,
            app_mode='chat'
        )

        # new thread
        worker_thread = threading.Thread(target=self._generate_worker, kwargs={
            'application_generate_entity': application_generate_entity,
            'queue_manager': queue_manager,
            'conversation_id': conversation.id,
            'message_id': message.id,
            'last_message_id': last_message.id if last_message else None,
        })
        worker_thread.start()

        # return response or stream generator
        response = ChatResponse(
            application_generate_entity=application_generate_entity,
            queue_manager=queue_manager,
            conversation=conversation,
            message=message,
            stream=stream,
        ).process()
        return self._convert_generate_response(response)

    def _generate_worker(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            queue_manager: AppQueueManager,
            conversation_id: int,
            message_id: str,
            last_message_id: int | None = None,
    ) -> None:
        try:
            conversation = Conversation.objects.get(id=conversation_id)
            message = Message.objects.get(id=message_id)
            last_message = Message.objects.get(id=last_message_id) if last_message_id else None

            ChatAppRunner().run(
                application_generate_entity=application_generate_entity,
                queue_manager=queue_manager,
                conversation=conversation,
                message=message,
                last_message=last_message
            )
        except ConversationTokenExceedError as ee:
            logger.exception(ee)
            queue_manager.publish_error(e_type=QueueErrorEvent.ErrorType.CONVERSATION_TOKEN_EXCEED, e=ee.detail)
        except ContainSensitiveError as se:
            logger.exception(se)
            queue_manager.publish_error(e_type=QueueErrorEvent.ErrorType.CONTAIN_SENSITIVE, e=se.detail)
        except SpecialMessageError as sp_e:
            logger.exception(sp_e)
            queue_manager.publish_error(e_type=QueueErrorEvent.ErrorType.SPECIAL_ERROR, e=sp_e.detail)
        except LLMRequestError as llm_e:
            queue_manager.publish_error(e_type=QueueErrorEvent.ErrorType.LLM_REQUEST_ERROR, e=llm_e.detail_err)
        except Exception as e:
            logger.exception(e)
            queue_manager.publish_error(str(e))

    def add_conversation(
            self,
            app_model: App,
            dto: ConversationAdd,
            from_account: Account,
            invoke_from: str = InvokeFrom.api.value,
    ) -> Conversation:

        if app_model.app_type == 'chat_app2':
            user_inputs = {}
            app_model_config = self._get_app_model_config(app_model)

            return self._add_conversation_for_chat_app(
                app_model=app_model,
                app_model_config=app_model_config,
                inputs=user_inputs,
                from_account=from_account,
                from_biz_id=dto.biz_id,
                invoke_from=invoke_from,
            )


        else:
            user_inputs = dto.inputs if isinstance(dto.inputs, dict) else {}

            app_model_config = self._get_app_model_config(app_model)
            prompt_template = self._get_prompt_template(user_inputs=user_inputs, app_model_config=app_model_config)

            model_config = self._get_model_config(
                user_inputs=user_inputs,
                app_model_config=app_model_config,
                prompt_template=prompt_template,
            )

            dataset: Dataset | None = None
            document_nos = []
            if dto.dataset_no:
                # 校验app是否包含dataset
                # if 'repository' not in app_model_config.support_params:
                #     raise DatasetNotSupportError()
                dataset = Dataset.objects.filter(is_deleted=False, dataset_no=dto.dataset_no).first()
                if not dataset:
                    raise DatasetNotFoundError()

                if dto.document_nos:
                    document_nos = list(dataset.datasetdocument_set.filter(
                        is_deleted=False, document_no__in=dto.document_nos
                    ).values_list('document_no', flat=True))

            pre_prompt = self._get_pre_prompt(
                pre_prompt=dto.pre_prompt,
                user_inputs=user_inputs,
                app_model_config=app_model_config,
                has_dataset=True if dataset else False,
                prompt_template=prompt_template,
            )
            return self._add_conversation(
                app_model=app_model,
                app_model_config=app_model_config,
                inputs=user_inputs,
                model_conf=model_config,
                pre_prompt=pre_prompt,
                dataset=dataset,
                document_nos=document_nos,
                from_account=from_account,
                from_biz_id=dto.biz_id,
                invoke_from=invoke_from,
            )

    def retry_message(
            self,
            message: Message,
            from_account: Account,
            invoke_from: str = InvokeFrom.api.value,
    ):
        app_model = message.app
        conversation = message.conversation
        if conversation.status != ConversationStatus.NORMAL.value:
            raise ConversationFinishedError()

        return self._generate(
            query=message.query,
            user_inputs=conversation.inputs,
            app_model=app_model,
            conversation=conversation,
            message_type=message.message_type,
            file_objs=message.file_objs,
            from_account=from_account,
            from_biz_id=message.from_biz_id,
            from_user_info=message.userinfo,
            last_message=message,
            invoke_from=invoke_from,
        )
