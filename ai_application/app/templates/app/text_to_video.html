<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智学视听</title>
    <link href="https://cdn.kaoyanvip.cn/bootstrap-4.5.0.min.css" rel="stylesheet">
    <style>
        html, body {
            height: 100%;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            height: 100vh;
            background-color: #f8f9fa;
            padding: 20px;
            display: flex;
            flex-direction: column;
            margin: 0;
        }
        .knowledge-list-container {
            flex: 1;
            overflow-y: auto;
        }
        .main-content {
            padding: 20px;
            height: calc(100vh); /* 减去padding */
            overflow: hidden;
        }
        .knowledge-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }
        .knowledge-item:hover {
            background-color: #e9ecef;
        }
        .category-title {
            font-weight: bold;
            padding: 10px;
            background-color: #e9ecef;
            margin-top: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .subcategory-title {
            font-weight: bold;
            padding: 8px 10px 8px 20px;
            background-color: #f1f3f5;
            margin-top: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        .knowledge-items {
            padding-left: 30px;
        }
        .subcategory-list {
            display: none;
        }
        .category-item.active .subcategory-list {
            display: block;
        }
        .knowledge-items {
            display: none;
        }
        .subcategory-item.active .knowledge-items {
            display: block;
        }
        .subcategory-item .subcategory-title::before {
            content: "▶ ";
            font-size: 10px;
        }
        .subcategory-item.active .subcategory-title::before {
            content: "▼ ";
        }
        .search-match {
            background-color: #fff3cd;
            font-weight: bold;
        }
        .search-box {
            padding: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧知识列表 -->
            <div class="col-md-2 sidebar">
                <h4 style="flex-shrink: 0;">知识列表</h4>
                <div class="search-box mb-1">
                    <input type="text" id="knowledge-search" class="form-control" placeholder="搜索...">
                </div>
                <div id="knowledge-list" class="knowledge-list-container">
                    {% for category, subcategories in category_structure.items %}
                        <div class="category-item">
                            <div class="category-title">{{ category }}</div>
                            <div class="subcategory-list">
                                {% for subcategory, items in subcategories.items %}
                                    <div class="subcategory-item {% if first_kp.subject_2 == subcategory %}active{% endif %}">
                                        <div class="subcategory-title">{{ subcategory }}</div>
                                        {% if items %}
                                            <div class="knowledge-items">
                                                {% for item in items %}
                                                    <div class="knowledge-item"
                                                         data-id="{{ item.id }}"
                                                         data-desc="{{ item.desc|escape }}"
                                                         data-video="{{ item.video_url }}"
                                                         data-chart="{{ item.image_url|default_if_none:'' }}">
                                                        {{ item.name }}
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
{#                <div class="d-grid gap-2 mt-3">#}
{#                    <button id="search-btn" class="btn btn-primary">智学视听搜索</button>#}
{#                </div>#}
            </div>

            <!-- 右侧主内容区 -->
            <div class="col-md-10 main-content">
                <div class="row h-100">
                    <!-- 左侧视频区 -->
                    <div class="col-md-7 h-100 d-flex flex-column">
                        <div class="video-container" style="width: 100%; aspect-ratio: 16/9; max-height: 720px; box-shadow: 0 0 5px 2px rgba(128, 128, 128, 0.3);">
                            <video id="knowledge-video" width="100%" height="100%" controls>
                                <source src="{{ first_kp.video_url }}" type="video/mp4">
                                您的浏览器不支持HTML5视频
                            </video>
                        </div>
                        <div id="chart-container" class="mt-3" style="min-height: 200px; background-color: white; display: flex; justify-content: center; align-items: center;">
                            <img id="chart-image" src="" alt="统计图表" style="max-width: 100%; max-height: 100%; display: none;">
                        </div>
                    </div>

                    <!-- 右侧Markdown报告区 -->
                    <div class="col-md-5 h-100 d-flex flex-column">
                        <div id="markdown-content" class="border p-3 bg-light flex-grow-1"
                             style="overflow-y: auto;">{{ first_kp.desc }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/bootstrap-4.5.0.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/marked/13.0.2/marked.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 添加分类点击事件
            const categoryTitles = document.querySelectorAll('.category-title');
            categoryTitles.forEach(title => {
                title.addEventListener('click', function() {
                    this.parentElement.classList.toggle('active');
                });
            });

            // 添加子分类点击事件
            document.querySelectorAll('.subcategory-title').forEach(title => {
                title.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.parentElement.classList.toggle('active');
                });
            });
            
            // 默认展开第一个分类
            document.querySelector('.category-item')?.classList.add('active');

            // 搜索功能
            const searchInput = document.getElementById('knowledge-search');
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const searchTerm = this.value.toLowerCase();
                    const knowledgeItems = document.querySelectorAll('.knowledge-item');
                    const categoryTitles = document.querySelectorAll('.category-title');
                    const subcategoryTitles = document.querySelectorAll('.subcategory-title');
                    
                    // 重置所有状态
                    knowledgeItems.forEach(item => {
                        item.classList.remove('search-match');
                        item.style.display = '';
                    });
                    categoryTitles.forEach(title => title.classList.remove('search-match'));
                    subcategoryTitles.forEach(title => title.classList.remove('search-match'));
                    
                    if (searchTerm) {
                        // 搜索知识点
                        knowledgeItems.forEach(item => {
                            const itemText = item.textContent.toLowerCase();
                            if (itemText.includes(searchTerm)) {
                                item.classList.add('search-match');
                                // 展开父级分类
                                item.closest('.category-item')?.classList.add('active');
                                item.closest('.subcategory-item')?.classList.add('active');
                            } else {
                                item.style.display = 'none';
                            }
                        });
                        
                        // 搜索目录名称
                        categoryTitles.forEach(title => {
                            const titleText = title.textContent.toLowerCase();
                            if (titleText.includes(searchTerm)) {
                                title.classList.add('search-match');
                                title.closest('.category-item')?.classList.add('active');
                            }
                        });
                        
                        // 搜索子目录名称
                        subcategoryTitles.forEach(title => {
                            const titleText = title.textContent.toLowerCase();
                            if (titleText.includes(searchTerm)) {
                                title.classList.add('search-match');
                                title.closest('.subcategory-item')?.classList.add('active');
                                title.closest('.category-item')?.classList.add('active');
                            }
                        });
                    }
                }
            });

            const knowledgeItems = document.querySelectorAll('.knowledge-item');
            const markdownContent = document.getElementById('markdown-content');
            const videoElement = document.getElementById('knowledge-video');
            const videoSource = videoElement.querySelector('source');

            // 配置 marked
            marked.setOptions({
                breaks: true,
                gfm: true
            });
            markdownContent.innerHTML = marked.parse(markdownContent.innerHTML);

            knowledgeItems.forEach(item => {
                item.addEventListener('click', function () {
                    const desc = this.getAttribute('data-desc');
                    const videoUrl = this.getAttribute('data-video');
                    const chartUrl = this.getAttribute('data-chart');

                    if (desc) {
                        markdownContent.innerHTML = marked.parse(desc);
                    }

                    if (videoUrl) {
                        videoSource.src = videoUrl;
                        videoElement.load();  // 不自动播放，只重新加载
                    }

                    const chartImage = document.getElementById('chart-image');
                    if (chartUrl) {
                        chartImage.src = chartUrl;
                        chartImage.style.display = 'block';
                    } else {
                        chartImage.style.display = 'none';
                    }
                });
            });

            // 添加搜索按钮点击事件
            document.getElementById('search-btn').addEventListener('click', function() {
                window.location.href = '/console/app/text_to_video_search';
            });
        });
    </script>
</body>
</html>
