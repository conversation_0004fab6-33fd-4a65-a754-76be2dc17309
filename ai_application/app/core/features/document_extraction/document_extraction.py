from app.constants.app import AppMode
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.model_manager import ModelInstance
from app.core.prompt.simple_prompt_transform import SimplePromptTransform
from app.core.prompt.utils.prompt_message_util import PromptMessageUtil
from app.errors import DocumentExtractionError
from app.models import PromptTemplate


class DocumentExtractionFeature:

    def process(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no='document_extraction', is_deleted=False
        ).first()
        if not prompt_template:
            raise Exception('prompt template not found')

        query_in_prompt = 'query' in prompt_template.special_variable_list
        prompt_messages = SimplePromptTransform().get_prompt(
            app_mode=AppMode.COMPLETION,
            pre_prompt=prompt_template.prompt_content,
            query=query,
            query_in_prompt=query_in_prompt,
        )
        model_instance = ModelInstance(
            provider_model_bundle=application_generate_entity.model_conf.provider_model_bundle,
            model=application_generate_entity.model_conf.model
        )

        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            # model_parameters=application_generate_entity.model_conf.model_params,
            model_parameters={"max_tokens": 3000, "temperature": 0.1},
            stream=False
        )

        if tracing_log:
            log_content = PromptMessageUtil.prompt_messages_to_prompt_for_saving(invoke_result.prompt_messages)
            tracing_log.add_tracing(
                tracing_type='document_extraction',
                query=query,
                content=log_content,
                answer=invoke_result.message.content,
                message_tokens=invoke_result.usage.prompt_tokens,
                answer_tokens=invoke_result.usage.completion_tokens,
                total_tokens=invoke_result.usage.total_tokens,
                latency=invoke_result.usage.latency,
            )

        if not invoke_result.is_success:
            raise DocumentExtractionError()

        return invoke_result.message.content
