from app.core.model_provider_manager import ModelProviderManager
from app.core.model_runtime.entities.provider_entities import ModelType
from app.core.rag.embedding.base_embedding import Embeddings
from app.core.rag.embedding.cached_embedding import CacheEmbedding
from app.core.rag.models.document import Document
from app.core.rag.vdb.vector_base import BaseVector, BaseVectorFactory
from app.core.rag.vdb.vector_type import VectorType
from app.models import Dataset


class Vector:
    def __init__(self, dataset: Dataset, attributes: list = None):
        if attributes is None:
            attributes = ['doc_id', 'dataset_no', 'document_no', 'doc_hash']
        self._vector_type = VectorType.ANALYTIC_PGVECTOR.value
        self._dataset = dataset
        self._embeddings = self._get_embeddings()
        self._attributes = attributes
        self._vector_processor = self._init_vector()

    def _get_embeddings(self) -> Embeddings:
        model_manager = ModelProviderManager()

        embedding_model = model_manager.get_model_instance(
            provider=self._dataset.embedding_model_provider,
            model_type=ModelType.TEXT_EMBEDDING,
            model=self._dataset.embedding_model
        )
        return CacheEmbedding(embedding_model)

    def _init_vector(self) -> BaseVector:
        vector_factory_cls = self.get_vector_factory(self._vector_type)
        return vector_factory_cls().init_vector(self._dataset, self._attributes, self._embeddings)

    @staticmethod
    def get_vector_factory(vector_type: str) -> type[BaseVectorFactory]:
        match vector_type:
            case VectorType.ANALYTIC_PGVECTOR.value:
                from app.core.rag.vdb.analytic_pgvector.analytic_pgvector import AnalyticPGVectorFactory
                return AnalyticPGVectorFactory
            case _:
                raise ValueError(f"Vector store {vector_type} is not supported.")

    def embed_documents(self, documents: list[Document]):
        return self._embeddings.embed_documents([document.page_content for document in documents])

    def create(self, texts: list[Document], **kwargs):
        embeddings = self.embed_documents(texts)
        self._vector_processor.create(
            texts=texts,
            embeddings=embeddings,
            **kwargs
        )

    def add_texts(self, documents: list[Document], **kwargs):
        if kwargs.get('duplicate_check', False):
            documents = self._filter_duplicate_texts(documents)
        embeddings = self.embed_documents(documents)
        self._vector_processor.create(
            texts=documents,
            embeddings=embeddings,
            **kwargs
        )

    def text_exists(self, doc_id: str) -> bool:
        return self._vector_processor.text_exists(doc_id)

    def delete_by_doc_ids(self, doc_ids: list[str]) -> None:
        self._vector_processor.delete_by_doc_ids(doc_ids)

    def delete_by_metadata_field(self, key: str, value: str) -> None:
        self._vector_processor.delete_by_metadata_field(key, value)

    def search_by_vector(self, query: str, **kwargs) -> list[Document]:
        query_vector = self._embeddings.embed_query(query)
        return self._vector_processor.search_by_vector(query_vector, **kwargs)

    def search_by_full_text(self, query: str, **kwargs) -> list[Document]:
        return self._vector_processor.search_by_full_text(query, **kwargs)

    def delete(self) -> None:
        self._vector_processor.delete()

    def _filter_duplicate_texts(self, texts: list[Document]) -> list[Document]:
        for text in texts:
            doc_id = text.metadata['doc_id']
            exists_duplicate_node = self.text_exists(doc_id)
            if exists_duplicate_node:
                texts.remove(text)

        return texts

    def __getattr__(self, name):
        if self._vector_processor is not None:
            method = getattr(self._vector_processor, name)
            if callable(method):
                return method

        raise AttributeError(f"'vector_processor' object has no attribute '{name}'")
