try:
    import lxml.etree as ET  # It's faster than 'xml.etree.ElementTree' in CPython
except ImportError:
    import xml.etree.ElementTree as ET

class NotSupport(Exception):
    """
    not support exception
    """
    pass

# 导入主要的解析器组件
from .parsers import WordParser, convert_markdown_to_docx, convert_markdown_to_docx_with_template, DOC_XML_ROOT

__all__ = [
    'WordParser',
    'convert_markdown_to_docx', 
    'convert_markdown_to_docx_with_template',
    'DOC_XML_ROOT',
    'NotSupport',
    'ET'
]