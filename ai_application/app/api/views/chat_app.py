# 知舟问答2.0
from app.api.dto import DayiApptDto
from app.api.validators import DayiAppValidator
from app.services.chat_app2.dayiapp import DayiAppGenerateService
from django_ext import base_view
from django_ext.response import make_stream_response, make_response


class DayiAppView(base_view.BaseView):
    validator_class = DayiAppValidator

    def post(self, request, *args, **kwargs):
        dto: DayiApptDto = self.validate_request(request.data)
        response_content = DayiAppGenerateService.generate(dto, request.user)
        if isinstance(response_content, dict):
            return make_response(response_content)
        else:
            return make_stream_response(response_content)


class DayiAppRetryView(base_view.BaseView):
    validator_class = DayiAppValidator

    def post(self, request, *args, **kwargs):
        message_id = request.data.get('message_id')
        stream = True if request.data.get('stream', True) else False
        if not message_id:
            raise ValueError('message_id is empty')

        response_content = DayiAppGenerateService.retry_generate(message_id, stream, request.user)
        if isinstance(response_content, dict):
            return make_response(response_content)
        else:
            return make_stream_response(response_content)
