from enum import Enum
from typing import Any

from pydantic import ConfigDict, Field

from app.constants.common import FormParameterType
from django_ext.base_dto_model import MyBaseModel


class ModelType(Enum):
    LLM = "llm"
    TEXT_EMBEDDING = "text-embedding"


class DefaultParameterName(Enum):
    TEMPERATURE = "temperature"
    # TOP_P = "top_p"
    # PRESENCE_PENALTY = "presence_penalty"
    # FREQUENCY_PENALTY = "frequency_penalty"
    MAX_TOKENS = "max_tokens"
    # RESPONSE_FORMAT = "response_format"


class ModelPropertyKey(Enum):
    CONTEXT_SIZE = "context_size"
    MAX_INPUT_SIZE = "max_input_size"
    MAX_CHUNKS = "max_chunks"


class ParameterRule(MyBaseModel):
    name: str
    use_template: str | None = None
    label: str
    type: FormParameterType
    help: str | None = None
    required: bool = False
    default: Any = None
    min: float | None = None
    max: float | None = None
    step: int | float | None = None
    precision: int | None = None
    options: list[str] = Field(default=list)


class AIModelEntity(MyBaseModel):
    model: str
    label: str = ''
    model_properties: dict[ModelPropertyKey, Any]
    features: list = []
    deprecated: bool = False
    parameter_rules: list[ParameterRule] = Field(default=list)
    model_config = ConfigDict(protected_namespaces=())


class ProviderEntity(MyBaseModel):
    provider: str
    models: list[AIModelEntity] = Field(default=list)
