import logging
from typing import Generator

from app.api.dto import ChatMessageDto
from app.constants.app import AppMode, MessageStatus
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.apps.chat.app_generator import ChatAppGenerator
from app.core.apps.completion.app_generator import CompletionAppGenerator
from app.errors import AppNotFoundError, MessageNotFoundError
from app.models import App, Account, Message, InvokeFrom, MessageTask

logger = logging.getLogger(__name__)


class AppGenerateService:

    @classmethod
    def generate(
            cls,
            dto: ChatMessageDto,
            from_account: Account | None = None,
            invoke_from: str = InvokeFrom.api.value,
    ) -> Generator | dict:
        if not from_account:
            from_account = Account.objects.first()

        app_model: App = App.objects.filter(is_deleted=False, app_no=dto.app_id).first()
        if not app_model:
            raise AppNotFoundError()

        if app_model.mode == AppMode.CHAT.value:
            return ChatAppGenerator().generate(
                app_model=app_model,
                dto=dto,
                from_account=from_account,
                invoke_from=invoke_from,
            )
        elif app_model.mode == AppMode.COMPLETION.value:
            return CompletionAppGenerator().generate(
                app_model=app_model,
                dto=dto,
                from_account=from_account,
                invoke_from=invoke_from,
            )
        else:
            raise ValueError(f'Invalid app mode {app_model.mode}')

    @classmethod
    def generate_for_task(cls, task: MessageTask):
        app_model = task.message.app
        if app_model.mode == AppMode.CHAT.value:
            CompletionAppGenerator().generate_for_task(task=task)
        elif app_model.mode == AppMode.COMPLETION.value:
            CompletionAppGenerator().generate_for_task(task=task)
        else:
            raise ValueError(f'Invalid app mode {app_model.mode}')

    @classmethod
    def stop_message(cls, message_no: str, from_account: Account):
        message: Message = Message.objects.filter(
            is_deleted=False,
            from_account=from_account,
            message_no=message_no
        ).first()
        if not message:
            raise MessageNotFoundError()

        if message.status == MessageStatus.REPLACED.value:
            new_message: Message = Message.objects.filter(
                is_deleted=False, replaced_message_no=message_no
            ).order_by('-id').first()
            if not new_message:
                raise MessageNotFoundError()
            task_id = new_message.message_no
        else:
            task_id = message_no

        AppQueueManager.set_stop_flag(task_id=task_id)

    @classmethod
    def retry_message(cls, message_id: str, from_account: Account):
        message: Message = Message.objects.filter(
            is_deleted=False,
            from_account=from_account,
            message_no=message_id
        ).first()
        if not message:
            raise MessageNotFoundError()

        return ChatAppGenerator().retry_message(message, from_account)
