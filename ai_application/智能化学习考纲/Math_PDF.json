{"数学一": "2025年全国硕士研究生招生考试数学考试大纲(数学一)\n考试科目：高等数学、线性代数、概率论与数理统计\n考试形式和试卷结构\n一、试卷满分及考试时间\n试卷满分为 150分，考试时间为 180分钟.\n二、答题方式\n答题方式为闭卷、笔试.\n三、试卷内容结构\n高等数学 约 60%\n线性代数 约 20%\n概率论与数理统计 约 20%\n四、试卷题型结构\n单选题 10小题，每小题 5分，共 50分\n填空题 6小题，每小题 5分，共 30分\n解答题（包括证明题） 6小题，共 70分\n \n高等数学\n一、函数、极限、连续\n🟥考试内容\n函数的概念及表示法函数的有界性、单调性、周期性和奇偶性复合函数、反函数、分段函数和隐函数基本初等函数的性质及其图形初等函数函数关系的建立\n数列极限与函数极限的定义及其性质函数的左极限和右极限无穷小量和无穷大量的概念及其关系无穷小量的性质及无穷小量的比较极限的四则运算极限存在的两个准则：单调有界准则和夹逼准则两个重要极限：\nx sinx lim -=1,lim =e x→0X X→001+1X\n函数连续的概念函数间断点的类型初等函数的连续性闭区间上连续函数的性质\n>考试要求\n1.理解函数的概念，掌握函数的表示法，会建立应用问题的函数关系.\n2.了解函数的有界性、单调性、周期性和奇偶性.\n3.理解复合函数及分段函数的概念，了解反函数及隐函数的概念.\n4.掌握基本初等函数的性质及其图形，了解初等函数的概念.\n5.理解极限的概念，理解函数左极限与右极限的概念以及函数极限存在与左极限、右极限之间的关系.\n6.掌握极限的性质及四则运算法则.\n7.掌握极限存在的两个准则，并会利用它们求极限，掌握利用两个重要极限求极限的方法.\n8.理解无穷小量、无穷大量的概念，掌握无穷小量的比较方法，会用等价无穷小量求极限.\n9.理解函数连续性的概念(含左连续与右连续)，会判别函数间断点的类型.\n10.了解连续函数的性质和初等函数的连续性，理解闭区间上连续函数的性质(有界性、最大值和最小值定理、介值定理)，并会应用这些性质.\n二、一元函数微分学\n>考试内容\n导数和微分的概念导数的几何意义和物理意义函数的可导性与连续性之间的关系平面曲线的切线和法线导数和微分的四则运算基本初等函数的导数复合函数、反函数、隐函数以及参数方程所确定的函数的微分法高阶导数一阶微分形式的不变性微分中值定理洛必达(L’Hospital)法则函数单调性的判别函数的极值函数图形的凹凸性、拐点及渐近线函数图形的描绘函数的最大值与最小值弧微分曲率的概念曲率圆与曲率半径\n>考试要求\n1.理解导数和微分的概念，理解导数与微分的关系，理解导数的几何意义，会求平面曲线的切线方程和法线方程，了解导数的物理意义，会用导数描述一些物理量，理解函数的可导性与连续性之间的关系.\n2.掌握导数的四则运算法则和复合函数的求导法则，掌握基本初等函数的导数公式.了解微分的四则运算法则和一阶微分形式的不变性，会求函数的微分.\n3.了解高阶导数的概念，会求简单函数的高阶导数.\n4.会求分段函数的导数，会求隐函数和由参数方程所确定的函数以及反函数的导数.\n5.理解并会用罗尔(Rolle)定理、拉格朗日(Lagrange)中值定理和泰勒(Taylor)定理，了解并会用柯西(Cauchy)中值定理.\n6.掌握用洛必达法则求未定式极限的方法.\n7.理解函数的极值概念，掌握用导数判断函数的单调性和求函数极值的方法，掌握函数最大值和最小值的求法及其应用.\n8.会用导数判断函数图形的凹凸性(注:在区间 (a,b)内,设函数f(x)具有二阶导数.时，当f”(x)>0时,f(x)的图形是凹的;当f”(x)<0,f(x)的图形是凸的),会求函数图形的拐点以及水平、铅直和斜渐近线，会描绘函数的图形.\n9.了解曲率、曲率圆与曲率半径的概念，会计算曲率和曲率半径.\n三、一元函数积分学\n🟥考试内容\n原函数和不定积分的概念不定积分的基本性质基本积分公式定积分的概念和基本性质定积分中值定理积分上限的函数及其导数牛顿-莱布尼茨(Newton-Leibniz)公式不定积分和定积分的换元积分法与分部积分法有理函数、三角函数的有理式和简单无理函数的积分反常(广义)积分定积分的应用\n>考试要求\n1.理解原函数的概念，理解不定积分和定积分的概念.\n2.掌握不定积分的基本公式，掌握不定积分和定积分的性质及定积分中值定理，掌握换元积分法与分部积分法.\n3.会求有理函数、三角函数有理式和简单无理函数的积分.\n4.理解积分上限的函数，会求它的导数，掌握牛顿-莱布尼茨公式.\n5.了解反常积分的概念，了解反常积分收敛的比较判别法，会计算反常积分.\n6.掌握用定积分表达和计算一些几何量与物理量(平面图形的面积、平面曲线的弧长、旋转体的体积及侧面积、平行截面面积为已知的立体体积、功、引力、压力、质心、形心等)及函数的平均值.\n四、向量代数和空间解析几何\n>考试内容\n向量的概念向量的线性运算向量的数量积和向量积向量的混合积两向量垂直、平行的条件两向量的夹角向量的坐标表达式及其运算单位向量方向数与方向余弦曲面方程和空间曲线方程的概念平面方程直线方程平面与平面、平面与直线、直线与直线的夹角以及平行、垂直的条件点到平面和点到直线的距离球面柱面旋转曲面常用的二次曲面方程及其图形空间曲线的参数方程和一般方程空间曲线在坐标面上的投影曲线方程\n>考试要求\n1.理解空间直角坐标系，理解向量的概念及其表示.\n2.掌握向量的运算(线性运算、数量积、向量积、混合积)，了解两个向量垂直、平行的条件.\n3.理解单位向量、方向数与方向余弦、向量的坐标表达式，掌握用坐标表达式进行向量运算的方法.\n4.掌握平面方程和直线方程及其求法.\n5.会求平面与平面、平面与直线、直线与直线之间的夹角，并会利用平面、直线的相互关系(平行、垂直、相交等))解决有关问题.\n6.会求点到直线以及点到平面的距离.\n7.了解曲面方程和空间曲线方程的概念.\n8.了解常用二次曲面的方程及其图形，会求简单的柱面和旋转曲面的方程.\n9.了解空间曲线的参数方程和一般方程.了解空间曲线在坐标平面上的投影，并会求该投影曲线的方程.\n五、多元函数微分学\n🟥考试内容\n多元函数的概念二元函数的几何意义二元函数的极限与连续的概念有界闭区域上多元连续函数的性质多元函数的偏导数和全微分全微分存在的必要条件和充分条件多元复合函数、隐函数的求导法二阶偏导数方向导数和梯度空间曲线\n的切线和法平面曲面的切平面和法线二元函数的二阶泰勒公式多元函数的极值和条件极值多元函数的最大值、最小值及其简单应用\n>考试要求\n1.理解多元函数的概念，理解二元函数的几何意义.\n2.了解二元函数的极限与连续的概念以及有界闭区域上连续函数的性质.\n3.理解多元函数偏导数和全微分的概念，会求全微分，了解全微分存在的必要条件和充分条件，了解全微分形式的不变性.\n4.理解方向导数与梯度的概念，并掌握其计算方法.\n5.掌握多元复合函数一阶、二阶偏导数的求法.\n6.了解隐函数存在定理，会求多元隐函数的偏导数.\n7.了解空间曲线的切线和法平面及曲面的切平面和法线的概念，会求它们的方程.\n8.了解二元函数的二阶泰勒公式.\n9.理解多元函数极值和条件极值的概念，掌握多元函数极值存在的必要条件，了解二元函数极值存在的充分条件，会求二元函数的极值，会用拉格朗日乘数法求条件极值，会求简单多元函数的最大值和最小值，并会解决一些简单的应用问题.\n六、多元函数积分学\n🟥考试内容\n二重积分与三重积分的概念、性质、计算和应用两类曲线积分的概念、性质及计算两类曲线积分的关系格林(Green)公式平面曲线积分与路径无关的条件二元函数全微分的原函数两类曲面积分的概念、性质及计算两类曲面积分的关系高斯(Gauss)公式斯托克斯(Stokes)公式散度、旋度的概念及计算曲线积分和曲面积分的应用\n>考试要求\n1.理解二重积分、三重积分的概念，了解重积分的性质，了解二重积分的中值定理.\n2.掌握二重积分的计算方法(直角坐标、极坐标)，会计算三重积分(直角坐标、柱面坐标、球面坐标).\n3.理解两类曲线积分的概念，了解两类曲线积分的性质及两类曲线积分的关系.\n4.掌握计算两类曲线积分的方法.\n5.掌握格林公式并会运用平面曲线积分与路径无关的条件，会求二元函数全微分的原函数.\n6.了解两类曲面积分的概念、性质及两类曲面积分的关系，掌握计算两类曲面积分的方法，掌握用高斯公式计算曲面积分的方法，并会用斯托克斯公式计算曲线积分.\n7.了解散度与旋度的概念，并会计算.\n8.会用重积分、曲线积分及曲面积分求一些几何量与物理量(平面图形的面积、体积、曲面面积、弧长、质量、质心、形心、转动惯量、引力、功及流量等).\n七、无穷级数\n🟥考试内容\n常数项级数的收敛与发散的概念收敛级数的和的概念级数的基本性质与收敛的必要条件几何级数与p级数及其收敛性正项级数收敛性的判别法交错级数与莱布尼茨定理任意项级数的绝对收敛与条件收敛函数项级数的收敛域与和函数的概念幂级数及其收敛半径、收敛区间(指开区间)和收敛域幂级数的和函数幂级数在其收敛区间内的基本性质简单幂级数的和函数的求法初等函数的幂级数展开式函数在[-l,t]上的函数在[0,t]上的正弦级数和余弦级数\n>考试要求\n1.理解常数项级数收敛、发散以及收敛级数的和的概念，掌握级数的基本性质及收敛的必要条件.\n2.掌握几何级数与p级数的收敛与发散的条件.\n3.掌握正项级数收敛性的比较判别法、比值判别法、根值判别法，会用积分判别法.\n4.掌握交错级数的莱布尼茨判别法.\n5.了解任意项级数绝对收敛与条件收敛的概念以及绝对收敛与收敛的关系.\n6.了解函数项级数的收敛域及和函数的概念.\n7.理解幂级数收敛半径的概念，并掌握幂级数的收敛半径、收敛区间及收敛域的求法.\n8.了解幂级数在其收敛区间内的基本性质(和函数的连续性、逐项求导和逐项积分)，会求一些幂级数在收敛区间内的和函数，并会由此求出某些数项级数的和.\n9.了解函数展开为泰勒级数的充分必要条件.\n10.掌握ex, xsin, xcos,ln( 🟥x10.掌握e*,sin x,cosx,ln(1+x), (1+x)“的麦克劳林(Maclaurin) 展开式,会用它们将一些简单函数间接展开为幂级数.\n11.了解傅里叶级数的概念和狄利克雷收敛定理,会将定义在[-l,i]上的函数展开为傅里叶级数,会将定义在[0,t]上的函数展开为正弦级数与余弦级数,会写出傅里叶级数的和函数的表达式.\n八、常微分方程\n>考试内容\n常微分方程的基本概念变量可分离的微分方程齐次微分方程一阶线性微分方程伯努利(Bernoulli)方程全微分方程可用简单的变量代换求解的某些微分方程可降阶的高阶微分方程线性微分方程解的性质及解的结构定理二阶常系数齐次线性微分方程高于二阶的某些常系数齐次线性微分方程简单的二阶常系数非齐次线性微分方程欧拉(Euler)方程微分方程的简单应用\n>考试要求\n1.了解微分方程及其阶、解、通解、初始条件和特解等概念.\n2.掌握变量可分离的微分方程及一阶线性微分方程的解法.\n3.会解齐次微分方程、伯努利方程和全微分方程，会用简单的变量代换解某些微分方程.\n4.会用降阶法解下列形式的微分方程:y(“)=f(x),y”=f(x,y)和y”=f(yy).\n5.理解线性微分方程解的性质及解的结构.\n6.掌握二阶常系数齐次线性微分方程的解法，并会解某些高于二阶的常系数齐次线性微分方程.\n7.会解自由项为多项式、指数函数、正弦函数、余弦函数以及它们的和与积的二阶常系数非齐次线性微分方程.\n8.会解欧拉方程.\n9.会用微分方程解决一些简单的应用问题.\n线性代数\n一、行列式\n>考试内容\n行列式的概念和基本性质行列式按行(列)展开定理\n🟥考试要求\n1.了解行列式的概念，掌握行列式的性质.\n2.会应用行列式的性质和行列式按行(列)展开定理计算行列式.\n二、矩阵\n🟥考试内容\n矩阵的概念矩阵的线性运算矩阵的乘法方阵的幂方阵乘积的行列式矩阵的转置逆矩阵的概念和性质矩阵可逆的充分必要条件伴随矩阵矩阵的初等\n变换初等矩阵矩阵的秩矩阵的等价分块矩阵及其运算\n>考试要求\n1.理解矩阵的概念，了解单位矩阵、数量矩阵、对角矩阵、三角矩阵、对称矩阵和反对称矩阵以及它们的性质.\n2.掌握矩阵的线性运算、乘法、转置以及它们的运算规律，了解方阵的幂与方阵乘积的行列式的性质.\n3.理解逆矩阵的概念，掌握逆矩阵的性质以及矩阵可逆的充分必要条件，理解伴随矩阵的概念，会用伴随矩阵求逆矩阵.\n4.理解矩阵初等变换的概念，了解初等矩阵的性质和矩阵等价的概念，理解矩阵的秩的概念，掌握用初等变换求矩阵的秩和逆矩阵的方法.\n5.了解分块矩阵及其运算.\n三、向量\n🟥考试内容\n向量的概念向量的线性组合与线性表示向量组的线性相关与线性无关向量组的极大线性无关组等价向量组向量组的秩向量组的秩与矩阵的秩之间的关系向量空间及其相关概念 n维向量空间的基变换和坐标变换过渡矩阵向量的内积线性无关向量组的正交规范化方法规范正交基正交矩阵及其性质\n>考试要求\n1.理解n维向量、向量的线性组合与线性表示的概念.\n2.理解向量组线性相关、线性无关的概念，掌握向量组线性相关、线性无关的有关性质及判别法.\n3.理解向量组的极大线性无关组和向量组的秩的概念，会求向量组的极大线性无关组及秩.\n4.理解向量组等价的概念，理解矩阵的秩与其行(列)向量组的秩之间的关系.\n5.了解n维向量空间、子空间、基底、维数、坐标等概念.\n6.了解基变换和坐标变换公式，会求过渡矩阵.\n7.了解内积的概念，掌握线性无关向量组正交规范化的施密特(Schmidt)方法.\n8.了解规范正交基、正交矩阵的概念以及它们的性质.\n四、线性方程组\n🟥考试内容\n线性方程组的克拉默(Cramer)法则齐次线性方程组有非零解的充分必要条件非齐次线性方程组有解的充分必要条件线性方程组解的性质和解的结构齐次线性方程组的基础解系和通解解空间非齐次线性方程组的通解\n>考试要求\nl.会用克拉默法则.\n2.理解齐次线性方程组有非零解的充分必要条件及非齐次线性方程组有解的充分必要条件.\n3.理解齐次线性方程组的基础解系、通解及解空间的概念，掌握齐次线性方程组的基础解系和通解的求法.\n4.理解非齐次线性方程组解的结构及通解的概念.\n5.掌握用初等行变换求解线性方程组的方法.\n五、矩阵的特征值和特征向量\n🟥考试内容\n矩阵的特征值和特征向量的概念、性质相似变换、相似矩阵的概念及性质矩阵可相似对角化的充分必要条件及相似对角矩阵实对称矩阵的特征值、特征向量及其相似对角矩阵\n>考试要求\n1.理解矩阵的特征值和特征向量的概念及性质，会求矩阵的特征值和特征向量.\n2.理解相似矩阵的概念、性质及矩阵可相似对角化的充分必要条件，掌握将矩阵化为相似对角矩阵的方法.\n3.掌握实对称矩阵的特征值和特征向量的性质.\n六、二次型\n>考试内容\n二次型及其矩阵表示合同变换与合同矩阵二次型的秩惯性定理二次型的标准形和规范形用正交变换和配方法化二次型为标准形二次型及其矩阵的正定性\n>考试要求\n1.掌握二次型及其矩阵表示，了解二次型秩的概念，了解合同变换与合同矩阵的概念，了解二次型的标准形、规范形的概念以及惯性定理.\n2.掌握用正交变换化二次型为标准形的方法，会用配方法化二次型为标准形.\n3.理解正定二次型、正定矩阵的概念，并掌握其判别法.\n概率论与数理统计\n一、随机事件和概率\n>考试内容\n随机事件与样本空间事件的关系与运算完备事件组概率的概念概率的基本性质古典型概率几何型概率条件概率概率的基本公式事件的独立性独立重复试验\n>考试要求\n1.了解样本空间(基本事件空间)的概念，理解随机事件的概念，掌握事件的关系及运算.\n12内部资料，翻印必究\n2.理解概率、条件概率的概念，掌握概率的基本性质，会计算古典型概率和几何型概率，掌握概率的加法公式、减法公式、乘法公式、全概率公式以及贝叶斯(Bayes)公式.\n3.理解事件独立性的概念，掌握用事件独立性进行概率计算的方法；理解独立重复试验的概念，掌握计算有关事件概率的方法.\n二、随机变量及其分布\n🟥考试内容\n随机变量随机变量分布函数的概念及其性质离散型随机变量的概率分布连续型随机变量的概率密度常见随机变量的分布随机变量函数的分布\n>考试要求\n1.理解随机变量的概念,理解分布函数F(x)=P{X≤x}(-00<x<+00)的概念及性质, F x P X x会计算与随机变量相联系的事件的概率.\n2.理解离散型随机变量及其概率分布的概念，掌握 0-1分布、二项分布(,)B n p 、几何分布、超几何分布、泊松（Poisson）分布()P )及其应用.\n3.了解泊松定理的结论和应用条件，会用泊松分布近似表示二项分布.\n4.理解连续型随机变量及其概率密度的概念，掌握均匀分布(,)U a b 、正态分布N , ) 、指数分布及其应用，其中参数为( 0)(,2) ) > )的指数分布()E )的概率密度为\nf(x)=入e-2,x>0,0,x≤0.\n5.会求随机变量函数的分布.\n三、多维随机变量及其分布\n>考试内容\n多维随机变量及其分布二维离散型随机变量的概率分布、边缘分布和条件分布二维连续型随机变量的概率密度、边缘概率密度和条件密度随机变量的独立性和不相关性常用二维随机变量的分布两个及两个以上随机变量简单函数的分布\n🟥考试要求\n1.理解多维随机变量的概念，理解多维随机变量的分布的概念和性质，理解二维离散型随机变量的概率分布、边缘分布和条件分布，理解二维连续型随机变量的概率密度、边缘密度和条件密度，会求与二维随机变量相关事件的概率.\n2.理解随机变量的独立性及不相关性的概念，掌握随机变量相互独立的条件.\n3.掌握二维均匀分布,了解二维正态分布N(μ1, H2; 0,0;p)的概率密度,理解其中参数的概率意义.\n4.会求两个随机变量简单函数的分布，会求多个相互独立随机变量简单函数的分布.\n四、随机变量的数字特征\n🟥考试内容\n随机变量的数学期望(均值)、方差、标准差及其性质随机变量函数的数学期望矩、协方差、相关系数及其性质\n>考试要求\n1.理解随机变量数字特征(数学期望、方差、标准差、矩、协方差、相关系数)的概念，会运用数字特征的基本性质，并掌握常用分布的数字特征.\n2.会求随机变量函数的数学期望.\n五、大数定律和中心极限定理\n🟥考试内容\n切比雪夫(Chebyshev)不等式切比雪夫大数定律伯努利(Bernoulli)大数定律辛钦(Khinchine)大数定律棣莫弗-拉普拉斯(DeMoivre-Laplace)定理列维-林德伯格(Levy-Lindberg)定理\n🟥考试要求\n1.了解切比雪夫不等式.\n2.了解切比雪夫大数定律、伯努利大数定律和辛钦大数定律(独立同分布随机变量序14\n列的大数定律).\n3.了解棣莫弗-拉普拉斯定理(二项分布以正态分布为极限分布)和列维-林德伯格定理(独立同分布随机变量序列的中心极限定理).\n六、数理统计的基本概念\n>考试内容\n总体个体简单随机样本统计量样本均值样本方差和样本矩 X2分布t分布 F分布分位数正态总体的常用抽样分布\n🟥考试要求\n1.理解总体、简单随机样本、统计量、样本均值、样本方差及样本矩的概念，其中样本方差定义为\nS2-1-2(X,-X) n-i=1\n2.了解x2分布、 t分布和 F分布的概念及性质，了解上侧α分位数的概念并会查表计算.\n3.了解正态总体的常用抽样分布.\n七、参数估计\n🟥考试内容\n点估计的概念估计量与估计值矩估计法最大似然估计法估计量的评选标准区间估计的概念单个正态总体的均值和方差的区间估计两个正态总体的均值差和方差比的区间估计\n>考试要求\n1.理解参数的点估计、估计量与估计值的概念.\n2.掌握矩估计法(一阶矩、二阶矩)和最大似然估计法.\n3.了解估计量的无偏性、有效性(最小方差性)和一致性(相合性)的概念，并会验证估计量的无偏性.\n4、理解区间估计的概念，会求单个正态总体的均值和方差的置信区间，会求两个正态总体的均值差和方差比的置信区间.\n八、假设检验\n>考试内容\n显著性检验假设检验的两类错误单个及两个正态总体的均值和方差的假设检验🟥考试要求\n1.理解显著性检验的基本思想，掌握假设检验的基本步骤，了解假设检验可能产生的两类错误.\n2.掌握单个及两个正态总体的均值和方差的假设检验.\n \n  ", "数学二": "2025年全国硕士研究生招生考试数学考试大纲(数学二)\n考试科目：高等数学、线性代数\n考试形式和试卷结构\n一、试卷满分及考试时间\n试卷满分为 150分，考试时间为 180分钟.\n二、答题方式\n答题方式为闭卷、笔试.\n三、试卷内容结构\n高等数学 约 80%\n线性代数 约 20%\n四、试卷题型结构\n单选题10小题，每小题 5分，共 50分填空题6小题，每小题 5分，共 30分\n解答题（包括证明题） 6小题，共 70分\n \n高等数学\n一、函数、极限、连续\n🟥考试内容\n函数的概念及表示法函数的有界性、单调性、周期性和奇偶性复合函数、反函数、分段函数和隐函数基本初等函数的性质及其图形初等函数函数关系的建立数列极限与函数极限的定义及其性质函数的左极限和右极限无穷小量和无穷大量的概念及其关系无穷小量的性质及无穷小量的比较极限的四则运算极限存在的两个准则：单调有界准则和夹逼准则两个重要极限：\nsinx lim -=1,lim x→01+1)=e X X→00\n函数连续的概念函数间断点的类型初等函数的连续性闭区间上连续函数的性质\n>考试要求\n1.理解函数的概念，掌握函数的表示法，会建立应用问题的函数关系.\n2.了解函数的有界性、单调性、周期性和奇偶性.\n3.理解复合函数及分段函数的概念，了解反函数及隐函数的概念.\n4.掌握基本初等函数的性质及其图形，了解初等函数的概念.\n5.理解极限的概念，理解函数左极限与右极限的概念以及函数极限存在与左极限、右极限之间的关系.\n6.掌握极限的性质及四则运算法则.\n7.掌握极限存在的两个准则，并会利用它们求极限，掌握利用两个重要极限求极限的方法.\n8.理解无穷小量、无穷大量的概念，掌握无穷小量的比较方法，会用等价无穷小量求极限.\n9.理解函数连续性的概念(含左连续与右连续)，会判别函数间断点的类型.\n10.了解连续函数的性质和初等函数的连续性，理解闭区间上连续函数的性质(有界性、最大值和最小值定理、介值定理)，并会应用这些性质.\n二、一元函数微分学\n>考试内容\n导数和微分的概念导数的几何意义和物理意义函数的可导性与连续性之间的关系平面曲线的切线和法线导数和微分的四则运算基本初等函数的导数复合函数、反函数、隐函数以及参数方程所确定的函数的微分法高阶导数一阶微分形式的不变性微分中值定理洛必达(L’Hospital)法则函数单调性的判别函数的极值函数图形的凹凸性、拐点及渐近线函数图形的描绘函数的最大值与最小值弧微分曲率的概念曲率圆与曲率半径\n>考试要求\n1.理解导数和微分的概念，理解导数与微分的关系，理解导数的几何意义，会求平面曲线的切线方程和法线方程，了解导数的物理意义，会用导数描述一些物理量，理解函数的可导性与连续性之间的关系.\n2.掌握导数的四则运算法则和复合函数的求导法则，掌握基本初等函数的导数公式.了解微分的四则运算法则和一阶微分形式的不变性，会求函数的微分.\n3.了解高阶导数的概念，会求简单函数的高阶导数.\n4.会求分段函数的导数，会求隐函数和由参数方程所确定的函数以及反函数的导数.\n5.理解并会用罗尔(Rolle)定理、拉格朗日(Lagrange)中值定理和泰勒(Taylor)定理，了解并会用柯西(Cauchy)中值定理.\n6.掌握用洛必达法则求未定式极限的方法.\n7.理解函数的极值概念，掌握用导数判断函数的单调性和求函数极值的方法，掌握函数最大值和最小值的求法及其应用.\n8.会用导数判断函数图形的凹凸性(注:在区间 (a,b)内,设函数f(x)具有二阶导数.时，当f”(x)>0时,f(x)的图形是凹的;当f”(x)<0,f(x)的图形是凸的),会求函数图形的拐点以及水平、铅直和斜渐近线，会描绘函数的图形.\n9.了解曲率、曲率圆与曲率半径的概念，会计算曲率和曲率半径.\n三、一元函数积分学\n🟥考试内容\n原函数和不定积分的概念不定积分的基本性质基本积分公式定积分的概念和基本性质定积分中值定理积分上限的函数及其导数牛顿-莱布尼茨(Newton-Leibniz)公式不定积分和定积分的换元积分法与分部积分法有理函数、三角函数的有理式和简单无理函数的积分反常(广义)积分定积分的应用\n>考试要求\n1.理解原函数的概念，理解不定积分和定积分的概念.\n2.掌握不定积分的基本公式，掌握不定积分和定积分的性质及定积分中值定理，掌握换元积分法与分部积分法.\n3.会求有理函数、三角函数有理式和简单无理函数的积分.\n4.理解积分上限的函数，会求它的导数，掌握牛顿-莱布尼茨公式.\n5.了解反常积分的概念，了解反常积分收敛的比较判别法，会计算反常积分.\n6.掌握用定积分表达和计算一些几何量与物理量(平面图形的面积、平面曲线的弧长、旋转体的体积及侧面积、平行截面面积为已知的立体体积、功、引力、压力、质心、形心等)及函数的平均值.\n四、多元函数微积分学\n>考试内容\n多元函数的概念二元函数的几何意义二元函数的极限与连续的概念有界闭区域上二元连续函数的性质多元函数的偏导数和全微分多元复合函数、隐函数的求\n导法二阶偏导数多元函数的极值和条件极值、最大值和最小值二重积分的概念、基本性质和计算\n>考试要求\n1.了解多元函数的概念，了解二元函数的几何意义.\n2.了解二元函数的极限与连续的概念，了解有界闭区域上二元连续函数的性质.\n3.了解多元函数偏导数与全微分的概念，会求多元复合函数一阶、二阶偏导数，会求全微分，了解隐函数存在定理，会求多元隐函数的偏导数.\n4.了解多元函数极值和条件极值的概念，掌握多元函数极值存在的必要条件，了解二元函数极值存在的充分条件，会求二元函数的极值，会用拉格朗日乘数法求条件极值，会求简单多元函数的最大值和最小值，并会解决一些简单的应用问题.\n5.了解二重积分的概念，了解二重积分的基本性质，了解二重积分的中值定理，掌握二重积分的计算方法（直角坐标、极坐标）.\n五、常微分方程\n🟥考试内容\n常微分方程的基本概念变量可分离的微分方程齐次微分方程一阶线性微分方程可降阶的高阶微分方程线性微分方程解的性质及解的结构定理二阶常系数齐次线性微分方程高于二阶的某些常系数齐次线性微分方程简单的二阶常系数非齐次线性微分方程微分方程的简单应用\n考试要求\n1.了解微分方程及其阶、解、通解、初始条件和特解等概念.\n2.掌握变量可分离的微分方程及一阶线性微分方程的解法，会解齐次微分方程.\n3.会用降阶法解下列形式的微分方程:y()=f(x),y”=f(x,y’)和y”=f(y,y’). f x y f x y(,) f y y(,)\n4.理解线性微分方程解的性质及解的结构.\n5.掌握二阶常系数齐次线性微分方程的解法，并会解某些高于二阶的常系数齐次线性微分方程.\n6.会解自由项为多项式、指数函数、正弦函数、余弦函数以及它们的和与积的二阶常系数非齐次线性微分方程.\n7.会用微分方程解决一些简单的应用问题.\n线性代数\n一、行列式\n>考试内容\n行列式的概念和基本性质行列式按行(列)展开定理\n>考试要求\n1.了解行列式的概念，掌握行列式的性质.\n2.会应用行列式的性质和行列式按行(列)展开定理计算行列式.\n二、矩阵\n🟥考试内容\n矩阵的概念矩阵的线性运算矩阵的乘法方阵的幂方阵乘积的行列式矩阵的转置逆矩阵的概念和性质矩阵可逆的充分必要条件伴随矩阵矩阵的初等变换初等矩阵矩阵的秩矩阵的等价分块矩阵及其运算\n>考试要求\n1.理解矩阵的概念，了解单位矩阵、数量矩阵、对角矩阵、三角矩阵、对称矩阵、反对称矩阵和正交矩阵以及它们的性质.\n2.掌握矩阵的线性运算、乘法、转置以及它们的运算规律，了解方阵的幂与方阵乘积的行列式的性质.\n3.理解逆矩阵的概念，掌握逆矩阵的性质以及矩阵可逆的充分必要条件，理解伴随矩阵的概念，会用伴随矩阵求逆矩阵.\n4.理解矩阵初等变换的概念，了解初等矩阵的性质和矩阵等价的概念，理解矩阵的秩的概念，掌握用初等变换求矩阵的秩和逆矩阵的方法.\n5.了解分块矩阵及其运算.\n三、向量\n🟥考试内容\n向量的概念向量的线性组合与线性表示向量组的线性相关与线性无关向量组的极大线性无关组等价向量组向量组的秩向量组的秩与矩阵的秩之间的关系向量的内积线性无关向量组的正交规范化方法\n>考试要求\n1.理解n维向量、向量的线性组合与线性表示的概念.\n2.理解向量组线性相关、线性无关的概念，掌握向量组线性相关、线性无关的有关性质及判别法.\n3.了解向量组的极大线性无关组和向量组的秩的概念，会求向量组的极大线性无关组及秩.\n4.了解向量组等价的概念，了解矩阵的秩与其行（列）向量组的秩的关系.\n5.了解内积的概念，掌握线性无关向量组正交规范化的施密特（Schmidt）方法.\n四、线性方程组\n>考试内容\n线性方程组的克拉默(Cramer)法则齐次线性方程组有非零解的充分必要条件非齐次线性方程组有解的充分必要条件线性方程组解的性质和解的结构齐次线性方程组的基础解系和通解非齐次线性方程组的通解\n>考试要求\nl.会用克拉默法则.\n2.理解齐次线性方程组有非零解的充分必要条件及非齐次线性方程组有解的充分必要条件.\n3.理解齐次线性方程组的基础解系及通解的概念，掌握齐次线性方程组的基础解系和通解的求法.\n4.理解非齐次线性方程组的解的结构及通解的概念.\n5.会用初等行变换求解线性方程组.\n五、矩阵的特征值和特征向量\n>考试内容\n矩阵的特征值和特征向量的概念、性质相似矩阵的概念及性质矩阵可相似对角化的充分必要条件及相似对角矩阵实对称矩阵的特征值、特征向量及其相似对角矩阵\n>考试要求\n1.理解矩阵的特征值和特征向量的概念及性质，会求矩阵的特征值和特征向量.\n2.理解相似矩阵的概念、性质及矩阵可相似对角化的充分必要条件，会将矩阵化为相似对角矩阵的的方法.\n3.理解实对称矩阵的特征值和特征向量的性质.\n六、二次型\n🟥考试内容\n二次型及其矩阵表示合同变换与合同矩阵二次型的秩惯性定理二次型的标准形和规范形用正交变换和配方法化二次型为标准形二次型及其矩阵的正定性二次型及其矩阵表示合同变换与合同矩阵二次型的秩惯性定理二次型的标准形和规范形用正交变换和配方法化二次型为标准形二次型及其矩阵的正定性\n>考试要求\n1.掌握二次型及其矩阵表示，了解二次型的秩的概念，了解合同变换与合同矩阵的概念，了解二次型的标准形、规范形的概念以及惯性定理.\n2.掌握用正交变换和配方法化二次型为标准形的方法，会用配方法化二次型为标准形.\n3.理解正定二次型、正定矩阵的概念，并掌握其判别法.\n \n ", "数学三": "2025年全国硕士研究生招生考试数学考试大纲(数学三)\n考试科目：高等数学、线性代数、概率论与数理统计\n考试形式和试卷结构\n一、试卷满分及考试时间\n试卷满分为 150分，考试时间为 180分钟.\n二、答题方式\n答题方式为闭卷、笔试.\n三、试卷内容结构\n高等数学 约 60%\n线性代数 约 20%\n概率论与数理统计 约 20%\n四、试卷题型结构\n单选题 10小题，每小题 5分，共 50分\n填空题 6小题，每小题 5分，共 30分\n解答题（包括证明题） 6小题，共 70分\n \n高等数学\n一、函数、极限、连续\n🟥考试内容\n函数的概念及表示法函数的有界性、单调性、周期性和奇偶性复合函数、反函数、分段函数和隐函数基本初等函数的性质及其图形初等函数函数关系的建立\n数列极限与函数极限的定义及其性质函数的左极限和右极限无穷小量和无穷大量的概念及其关系无穷小量的性质及无穷小量的比较极限的四则运算极限存在的两个准则：单调有界准则和夹逼准则两个重要极限：\nx sinx lim -=1,lim =e x→0X X→001+1X\n函数连续的概念函数间断点的类型初等函数的连续性闭区间上连续函数的性质\n>考试要求\n1.理解函数的概念，掌握函数的表示法，会建立应用问题的函数关系.\n2.了解函数的有界性、单调性、周期性和奇偶性.\n3.理解复合函数及分段函数的概念，了解反函数及隐函数的概念.\n4.掌握基本初等函数的性质及其图形，了解初等函数的概念.\n5.理解极限的概念，理解函数左极限与右极限的概念以及函数极限存在与左极限、右极限之间的关系.\n6.了解极限的性质与极限存在的两个准则，掌握极限的四则运算法则，掌握利用两个重要极限求极限的方法.\n7.理解无穷小量、无穷大量的概念，掌握无穷小量的比较方法.会用等价无穷小量求极限.\n8.理解函数连续性的概念（含左连续与右连续），会判别函数间断点的类型.\n9.了解连续函数的性质和初等函数的连续性，理解闭区间上连续函数的性质（有界性、最大值和最小值定理、介值定理)，并会应用这些性质.\n二、一元函数微分学\n>考试内容\n导数和微分的概念导数的几何意义和经济意义函数的可导性与连续性之间的关系平面曲线的切线和法线导数和微分的四则运算基本初等函数的导数复合函数、反函数、隐函数的微分法高阶导数一阶微分形式的不变性微分中值定理洛必达(L’Hospital)法则函数单调性的判别函数的极值函数图形的凹凸性、拐点及渐近线函数图形的描绘函数的最大值与最小值\n>考试要求\n1.理解导数的概念及可导性与连续性之间的关系，了解导数的几何意义与经济意义（含边际与弹性的概念），会求平面曲线的切线方程和法线方程.\n2.掌握基本初等函数的导数公式、导数的四则运算法则及复合函数的求导法则，会求分段函数的导数，会求反函数与隐函数的导数.\n3.了解高阶导数的概念，会求简单函数的高阶导数.\n4.了解微分的概念、导数与微分之间的关系以及一阶微分形式的不变性，会求函数的微分.\n5.理解并会用罗尔（Rolle）定理、拉格朗日( Lagrange)中值定理和泰勒（Taylor）定理，了解并会用柯西（Cauchy)中值定理.\n6.掌握用洛必达法则求未定式极限.\n7.掌握函数单调性的判别方法，了解函数极值的概念，掌握函数极值、最大值和最小值的求法及其应用.\n8.会用导数判断函数图形的凹凸性(注：在区间 .a,b)内，设函数f具有二阶导数.时，当f”(x)>0时,f(x)的图形是凹的;当f”(x)<0,f(x)的图形是凸的),会求函数图形的拐点以及水平、铅直和斜渐近线.\n9.会描绘函数的图形.\n三、一元函数积分学\n>考试内容\n原函数和不定积分的概念不定积分的基本性质基本积分公式定积分的概念和基本性质定积分中值定理积分上限的函数及其导数牛顿-莱布尼茨(Newton-Leibniz)公式不定积分和定积分的换元积分法与分部积分法反常(广义)积分定积分的应用\n>考试要求\n1.理解原函数与不定积分的概念，掌握不定积分的基本性质和基本积分公式，掌握不定积分的换元积分法与分部积分法．\n2.了解定积分的概念和基本性质，了解定积分中值定理，理解积分上限的函数并会求它的导数，掌握牛顿-莱布尼茨公式以及定积分的换元积分法和分部积分法．\n3.会利用定积分计算平面图形的面积、旋转体的体积和函数的平均值，会利用定积分求解简单的经济应用问题．\n4.理解反常积分的概念，了解反常积分收敛的比较判别法，会计算反常积分．\n四、多元函数微积分学\n🟥考试内容\n多元函数的概念二元函数的几何意义二元函数的极限与连续的概念有界闭区域上二元连续函数的性质多元函数偏导数的概念与计算多元复合函数的求导法与隐函数求导法二阶偏导数全微分多元函数的极值和条件极值、最大值和最小值二重积分的概念、基本性质和计算无界区域上简单的反常二重积分\n>考试要求\n1.了解多元函数的概念，了解二元函数的几何意义．\n2.了解二元函数的极限与连续的概念，了解有界闭区域上二元连续函数的性质．\n3.了解多元函数偏导数与全微分的概念,会求多元复合函数一阶、二阶偏导数，会求全微分,会求多元隐函数的偏导数．\n4.了解多元函数极值和条件极值的概念，掌握多元函数极值存在的必要条件，了解二元函数极值存在的充分条件，会求二元函数的极值，会用拉格朗日乘数法求条件极值，会求简单多元函数的最大值和最小值，并会解决简单的应用问题．\n5.理解二重积分的概念，了解二重积分的基本性质，了解二重积分的中值定理，掌握二重积分的计算方法（直角坐标、极坐标），了解无界区域上较简单的反常二重积分并会计算．\n五、无穷级数\n🟥考试内容\n常数项级数的收敛与发散的概念收敛级数的和的概念级数的基本性质与收敛的必要条件几何级数与p级数及其收敛性正项级数收敛性的判别法任意项级数的绝对收敛与条件收敛交错级数与莱布尼茨定理幂级数及其收敛半径、收敛区间（指开区间）和收敛域幂级数的和函数幂级数在其收敛区间内的基本性质简单幂级数的和函数的求法初等函数的幂级数展开式\n>考试要求\n1.理解常数项级数的收敛、发散以及收敛级数的和的概念，掌握级数的基本性质及收敛的必要条件．\n2.掌握几何级数与p级数的收敛与发散的条件.\n3.掌握正项级数收敛性的比较判别法、比值判别法、根值判别法，会用积分判别法.\n4.掌握交错级数的莱布尼茨判别法.\n5.了解任意项级数绝对收敛与条件收敛的概念以及绝对收敛与收敛的关系.\n6.理解幂级数收敛半径的概念，并掌握幂级数的收敛半径、收敛区间及收敛域的求法.\n7.了解幂级数在其收敛区间内的基本性质(和函数的连续性、逐项求导和逐项积分)，会求一些幂级数在收敛区间内的和函数，并会由此求出某些数项级数的和.\n8.掌握ex, xsin, xcos,8.掌握e*,sin x,cosx,ln(1+x),(1+x)“的麦克劳林(Maclaurin) 展开式,会用它们将一ln( 🟥x些简单函数间接展开为幂级数.\n六、常微分方程与差分方程\n>考试内容\n常微分方程的基本概念变量可分离的微分方程齐次微分方程一阶线性微分方程线性微分方程解的性质及解的结构定理二阶常系数齐次线性微分方程及简单的非齐次线性微分方程差分与差分方程的概念差分方程的通解与特解一阶常系数线性差分方程微分方程的简单应用\n🟥考试要求\n1.了解微分方程及其阶、解、通解、初始条件和特解等概念.\n2.掌握变量可分离的微分方程、齐次微分方程和一阶线性微分方程的求解方法.\n3.掌握线性微分方程解的性质及解的结构.\n4.掌握二阶常系数齐次线性微分方程的解法，并会解某些高于二阶的常系数齐次线性微分方程.\n5.会解自由项为多项式、指数函数、正弦函数、余弦函数以及它们的和与积的二阶常系数非齐次线性微分方程.\n6.了解差分与差分方程及其通解与特解等概念．\n7.了解一阶常系数线性差分方程的求解方法．\n8.会用微分方程求解简单的经济应用问题．\n线性代数\n一、行列式\n>考试内容\n行列式的概念和基本性质行列式按行(列)展开定理\n🟥考试要求\n1.了解行列式的概念，掌握行列式的性质.\n2.会应用行列式的性质和行列式按行(列)展开定理计算行列式.\n二、矩阵\n>考试内容\n矩阵的概念矩阵的线性运算矩阵的乘法方阵的幂方阵乘积的行列式矩阵的转置逆矩阵的概念和性质矩阵可逆的充分必要条件伴随矩阵矩阵的初等变换初等矩阵矩阵的秩矩阵的等价分块矩阵及其运算\n>考试要求\n1.理解矩阵的概念，了解单位矩阵、数量矩阵、对角矩阵、三角矩阵的定义及性质，了解对称矩阵、反对称矩阵及正交矩阵等的定义和性质.\n2.掌握矩阵的线性运算、乘法、转置以及它们的运算规律，了解方阵的幂与方阵乘积的行列式的性质.\n3.理解逆矩阵的概念，掌握逆矩阵的性质以及矩阵可逆的充分必要条件，理解伴随矩阵的概念，会用伴随矩阵求逆矩阵.\n4.了解矩阵的初等变换和初等矩阵以及矩阵等价的概念，理解矩阵的秩的概念，掌握用初等变换求矩阵的逆矩阵和秩的方法.\n5.了解分块矩阵的概念，掌握分块矩阵的运算法则.\n三、向量\n🟥考试内容\n向量的概念向量的线性组合与线性表示向量组的线性相关与线性无关向量组的极大线性无关组等价向量组向量组的秩向量组的秩与矩阵的秩之间的关系向量的内积线性无关向量组的正交规范化方法\n>考试要求\n1.了解向量的概念，掌握向量的加法和数乘运算法则．\n2.理解向量的线性组合与线性表示、向量组线性相关、线性无关等概念，掌握向量组线性相关、线性无关的有关性质及判别法．\n3.理解向量组的极大线性无关组的概念，会求向量组的极大线性无关组及秩．\n4.理解向量组等价的概念，理解矩阵的秩与其行（列）向量组的秩之间的关系．\n5.了解内积的概念．掌握线性无关向量组正交规范化的施密特（Schmidt）方法．\n四、线性方程组\n🟥考试内容\n线性方程组的克拉默(Cramer)法则齐次线性方程组有非零解的充分必要条件非齐次线性方程组有解的充分必要条件线性方程组解的性质和解的结构齐次线性方程组的基础解系和通解非齐次线性方程组的通解\n🟥考试要求\n1.会用克拉默法则解线性方程组．\n2.掌握非齐次线性方程组有解和无解的判定方法．\n3.理解齐次线性方程组的基础解系的概念，掌握齐次线性方程组的基础解系和通解的求法．\n4.理解非齐次线性方程组解的结构及通解的概念.\n5.掌握用初等行变换求解线性方程组的方法.\n五、矩阵的特征值和特征向量\n🟥考试内容\n矩阵的特征值和特征向量的概念、性质相似变换、相似矩阵的概念及性质矩阵可相似对角化的充分必要条件及相似对角矩阵实对称矩阵的特征值和特征向量及其相似对角矩阵\n>考试要求\n1.理解矩阵的特征值、特征向量的概念，掌握矩阵特征值的性质，掌握求矩阵特征值和特征向量的方法．\n2.理解矩阵相似的概念，掌握相似矩阵的性质，了解矩阵可相似对角化的充分必要条件，掌握将矩阵化为相似对角矩阵的方法．\n3.掌握实对称矩阵的特征值和特征向量的性质．\n六、二次型\n🟥考试内容\n二次型及其矩阵表示合同变换与合同矩阵二次型的秩惯性定理二次型的标准形和规范形用正交变换和配方法化二次型为标准形二次型及其矩阵的正定性>考试要求\n1.掌握二次型及其矩阵表示，了解二次型的秩的概念，了解合同变换与合同矩阵的概念，了解二次型的标准形、规范形的概念以及惯性定理.\n2.掌握用正交变换化二次型为标准形的方法，会用配方法化二次型为标准形.\n3.理解正定二次型、正定矩阵的概念，并掌握其判别法.\n \n概率论与数理统计\n一、随机事件和概率\n>考试内容\n随机事件与样本空间事件的关系与运算完备事件组概率的概念概率的基本性质古典型概率几何型概率条件概率概率的基本公式事件的独立性独立重复试验\n>考试要求\n1.了解样本空间(基本事件空间)的概念，理解随机事件的概念，掌握事件的关系及运算.\n2.理解概率、条件概率的概念，掌握概率的基本性质，会计算古典型概率和几何型概率，掌握概率的加法公式、减法公式、乘法公式、全概率公式以及贝叶斯(Bayes)公式.\n3.理解事件独立性的概念，掌握用事件独立性进行概率计算的方法；理解独立重复试验的概念，掌握计算有关事件概率的方法.\n二、随机变量及其分布\n>考试内容\n随机变量随机变量分布函数的概念及其性质离散型随机变量的概率分布连续型随机变量的概率密度常见随机变量的分布随机变量函数的分布\n>考试要求\n1.理解随机变量的概念,理解分布函数F(x)=P{X≤x}(-00<x<+00)的概念及性质, F x P X x会计算与随机变量相联系的事件的概率.\n2.理解离散型随机变量及其概率分布的概念，掌握 0-1分布、二项分布(,)B n p 、几何分布、超几何分布、泊松（Poisson）分布()P )及其应用.\n3.掌握泊松定理的结论和应用条件，会用泊松分布近似表示二项分布.\n4.理解连续型随机变量及其概率密度的概念，掌握均匀分布(,)U a b 、正态分布2N O ) 、指数分布及其应用，其中参数为( 0)(, ) ) > )的指数分布()E )的概率密度为\n入e-x,x>0, f(x)=0,x≤0.\n5.会求随机变量函数的分布.\n三、多维随机变量及其分布\n🟥考试内容\n多维随机变量及其分布二维离散型随机变量的概率分布、边缘分布和条件分布二维连续型随机变量的概率密度、边缘概率密度和条件密度随机变量的独立性和不相关性常用二维随机变量的分布两个及两个以上随机变量简单函数的分布\n🟥考试要求\n1.理解多维随机变量的分布函数的概念和基本性质．\n2.理解二维离散型随机变量的概率分布和二维连续型随机变量的概率密度，掌握二维随机变量的边缘分布和条件分布．\n3.理解随机变量的独立性和不相关性的概念，掌握随机变量相互独立的条件，理解随机变量的不相关性与独立性的关系．\n4.掌握二维均匀分布和二维正态分布N(u, u; o,0;p),理解其中参数的概率意义．\n5.会根据两个随机变量的联合分布求其函数的分布，会根据多个相互独立随机变量的联合分布求其简单函数的分布．\n四、随机变量的数字特征\n🟥考试内容\n随机变量的数学期望(均值)、方差、标准差及其性质随机变量函数的数学期望切比雪夫（Chebyshev）不等式矩、协方差、相关系数及其性质\n🟥考试要求\n1.理解随机变量数字特征(数学期望、方差、标准差、矩、协方差、相关系数)的概念，会运用数字特征的基本性质，并掌握常用分布的数字特征.\n2.会求随机变量函数的数学期望.\n3.了解切比雪夫不等式\n五、大数定律和中心极限定理\n🟥考试内容\n切比雪夫大数定律伯努利（Bernoulli）大数定律辛钦（Khinchine）大数定律棣莫弗—拉普拉斯（De Moivre－Laplace）定理列维—林德伯格（Levy－Lindberg）定理\n>考试要求\n1.了解切比雪夫大数定律、伯努利大数定律和辛钦大数定律（独立同分布随机变量序列的大数定律）．\n2.了解棣莫弗—拉普拉斯中心极限定理（二项分布以正态分布为极限分布）、列维—林德伯格中心极限定理（独立同分布随机变量序列的中心极限定理），并会用相关定理近似计算有关随机事件的概率．\n六、数理统计的基本概念\n>考试内容\n总体个体简单随机样本统计量样本均值样本方差和样本矩 X2分布t分布 F分布分位数正态总体的常用抽样分布\n>考试要求\n1.了解总体、简单随机样本、统计量、样本均值、样本方差及样本矩的概念，其中样本方差定义为\nS²= 12(X-X) n-i=1\n2.了解产生x2变量、 t变量和 F变量的典型模式；了解标准正态分布、x2分布、 t分布和 F分布的上侧α分位数，会查相应的数值表．\n3．掌握正态总体的样本均值、样本方差、样本矩的抽样分布．\n4.了解经验分布函数的概念和性质．\n七、参数估计\n>考试内容\n点估计的概念估计量与估计值矩估计法最大似然估计法\n🟥考试要求\n1.了解参数的点估计、估计量与估计值的概念.\n2.掌握矩估计法(一阶矩、二阶矩)和最大似然估计法.\n"}