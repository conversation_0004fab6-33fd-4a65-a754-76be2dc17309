import re

from django.db import transaction

from app.models import EnglishPaperExplain, EnglishPaperQuestionExplain
from app.services.coze_workflow_service import CozeWorkflowService
from app.services.main_subject.answer_service import AnswerService


class EnPaperExplainService:

    @classmethod
    def judge_paper(cls, questions) -> bool:
        if len(questions) != 9:
            return False

        # 第1题是否是完形填空
        if len(questions[0]['question']['sub_questions']) != 20:
            return False
        # 2-5题是否是阅读理解
        for i in range(1, 5):
            if len(questions[i]['question']['sub_questions']) != 5:
                return False
        # 第6题为新题型
        if questions[5]['q_type'] not in [3, 4, 5]:
            return False
        # 7-9为主观题
        for i in range(6, 9):
            if questions[i]['q_type'] != 2:
                return False
        return True

    @classmethod
    def build_wanxing(cls, question):
        title = question['question']['question']

        sub_questions = []
        for idx, q in enumerate(question['question']['sub_questions'], start=1):
            sub_items = [
                f'{idx}. 题目:{q["question"]}',
                f'正确答案:{q["right_answer"]}',
                f'解析:{q["analysis"]}',
                f'分值:{q["score"]}',
            ]
            sub_questions.append('\n'.join(sub_items))

        sub_questions_str = "\n".join(sub_questions)
        question_content = f'{title} \n {sub_questions_str}'

        wrong_questions = []
        for idx, item in enumerate(question['user_sub_answers'], start=1):
            if item['user_answer_status'] == 0:
                wrong_questions.append(str(idx))

        total = len(question['question']['sub_questions'])
        wrong_num = len(wrong_questions)
        right_num = total - wrong_num

        if len(wrong_questions) > 1:
            wrong_stat_str = ','.join(wrong_questions)

            right_rate = int(100 * (total - wrong_num) / total) if total else 0
            answer_stat_str = f'第{wrong_stat_str}答错，正确率{right_rate}%'
        else:
            answer_stat_str = '全对，正确率100%'

        return total, right_num, {
            'wx_questions': question_content,
            'wx_user_answer': '',
            'wx_user_situtation': answer_stat_str,
        }

    @classmethod
    def build_read(cls, question):
        title = question['question']['question']

        sub_questions = []
        for idx, q in enumerate(question['question']['sub_questions'], start=1):
            sub_items = [
                f'{idx}. 题目:{q["question"]}',
                f'正确答案:{q["right_answer"]}',
                f'解析:{q["analysis"]}',
                f'分值:{q["score"]}',
            ]
            sub_questions.append('\n'.join(sub_items))

        sub_questions_str = "\n".join(sub_questions)
        question_content = f'{title} \n {sub_questions_str}'

        wrong_questions = []
        for idx, item in enumerate(question['user_sub_answers'], start=1):
            if item['user_answer_status'] == 0:
                wrong_questions.append(str(idx))

        total = len(question['question']['sub_questions'])
        wrong_num = len(wrong_questions)
        right_num = total - wrong_num
        if wrong_num > 1:
            wrong_stat_str = ','.join(wrong_questions)
            total = len(question['question']['sub_questions'])
            right_rate = int(100 * right_num / total) if total else 0
            answer_stat_str = f'第{wrong_stat_str}答错，正确率{right_rate}%'
        else:
            answer_stat_str = '全对，正确率100%'

        return total, right_num, {
            'yd_questions': question_content,
            'yd_user_answer': '',
            'yd_user_situtation': answer_stat_str,
        }

    @classmethod
    def build_new(cls, question):
        title = question['question']['question']

        sub_questions = []
        for idx, q in enumerate(question['question']['sub_questions'], start=1):
            sub_items = [
                f'{idx}. 题目:{q["question"]}',
                f'正确答案:{q["right_answer"]}',
                f'解析:{q["analysis"]}',
                f'分值:{q["score"]}',
            ]
            sub_questions.append('\n'.join(sub_items))

        sub_questions_str = "\n".join(sub_questions)
        question_content = f'{title} \n {sub_questions_str}'

        wrong_questions = []
        for idx, item in enumerate(question['user_sub_answers'], start=1):
            if item['user_answer_status'] == 0:
                wrong_questions.append(str(idx))

        total = len(question['question']['sub_questions'])
        wrong_num = len(wrong_questions)
        right_num = total - wrong_num
        if wrong_num > 1:
            wrong_stat_str = ','.join(wrong_questions)
            total = len(question['question']['sub_questions'])
            right_rate = int(100 * right_num / total) if total else 0
            answer_stat_str = f'第{wrong_stat_str}答错，正确率{right_rate}%'
        else:
            answer_stat_str = '全对，正确率100%'

        return total, right_num, {
            'xtx_questions': question_content,
            'xtx_user_answer': '',
            'xtx_user_situtation': answer_stat_str,
        }

    @classmethod
    def extract_img_url(cls, content):
        # 使用正则表达式匹配<img>标签中的src属性值
        pattern = r'<img\s+[^>]*src="([^"]+)"[^>]*>'
        matches = re.findall(pattern, content)
        return matches[0] if matches else ''

    @classmethod
    def build_translate(cls, question):
        title = question['question']['question']

        return {
            'fy_question': title,
            'fy_user_answer': cls.extract_img_url(question['user_answer']['user_answer']) if question['user_answer']['user_answer'] else '',
        }

    @classmethod
    def build_x_write(cls, question):
        title = question['question']['question']

        return {
            'zw_question': title,
            'zw_user_answer': cls.extract_img_url(question['user_answer']['user_answer']) if question['user_answer']['user_answer'] else '',
        }

    @classmethod
    def build_d_write(cls, question):
        title = question['question']['question']

        return {
            'zw_question': title,
            'zw_user_answer': cls.extract_img_url(question['user_answer']['user_answer']) if question['user_answer']['user_answer'] else '',
        }

    @classmethod
    def submit_answer(cls, paper_explain: EnglishPaperExplain):
        answer_id = paper_explain.answer_id
        exam_paper_content = AnswerService.get_test_answer_detail(answer_id)
        # 判断是否为真题试卷
        questions = exam_paper_content.get('questions', [])
        if not cls.judge_paper(questions):
            raise Exception('试卷格式错误，非英语真题试卷')

        questions_explain_objs = []

        # 第1题完形填空
        total, right_num, wanxing_params = cls.build_wanxing(questions[0])
        questions_explain_objs.append(EnglishPaperQuestionExplain(
            paper_explain=paper_explain,
            question_id=questions[0]['q_id'],
            question_type='完形填空',
            req_params=wanxing_params,
            status='ING',
            sub_question_num=total,
            sub_right_question_num=right_num,
        ))

        CozeWorkflowService.create_workflow_runs(
            'english_paper_explain_wanxing',
            wanxing_params,
            ext_params={'paper_explain_id': paper_explain.id, 'question_id': questions[0]['q_id']}
        )

        # 2-5题阅读理解
        for i in range(1, 5):
            total, right_num, read_params = cls.build_read(questions[i])
            questions_explain_objs.append(EnglishPaperQuestionExplain(
                paper_explain=paper_explain,
                question_id=questions[i]['q_id'],
                question_type='阅读理解',
                req_params=read_params,
                status='ING',
                sub_question_num=total,
                sub_right_question_num=right_num,
            ))
            CozeWorkflowService.create_workflow_runs(
                'english_paper_explain_read',
                read_params,
                ext_params={'paper_explain_id': paper_explain.id, 'question_id': questions[i]['q_id']}
            )

        # 第6题为新题型
        total, right_num, new_params = cls.build_new(questions[5])
        questions_explain_objs.append(EnglishPaperQuestionExplain(
            paper_explain=paper_explain,
            question_id=questions[5]['q_id'],
            question_type='新题型',
            req_params=new_params,
            status='ING',
            sub_question_num=total,
            sub_right_question_num=right_num,
        ))
        CozeWorkflowService.create_workflow_runs(
            'english_paper_explain_new',
            new_params,
            ext_params={'paper_explain_id': paper_explain.id, 'question_id': questions[5]['q_id']}
        )

        # 第7题为翻译
        translate_params = cls.build_translate(questions[6])
        questions_explain_objs.append(EnglishPaperQuestionExplain(
            paper_explain=paper_explain,
            question_id=questions[6]['q_id'],
            question_type='翻译',
            req_params=translate_params,
            status='ING'
        ))
        CozeWorkflowService.create_workflow_runs(
            'english_paper_explain_translate',
            translate_params,
            ext_params={'paper_explain_id': paper_explain.id, 'question_id': questions[6]['q_id']}
        )

        # 第8题为小作文
        x_write_params = cls.build_x_write(questions[7])
        questions_explain_objs.append(EnglishPaperQuestionExplain(
            paper_explain=paper_explain,
            question_id=questions[7]['q_id'],
            question_type='小作文',
            req_params=x_write_params,
            status='ING'
        ))
        CozeWorkflowService.create_workflow_runs(
            'english_paper_explain_x_write',
            x_write_params,
            ext_params={'paper_explain_id': paper_explain.id, 'question_id': questions[7]['q_id']}
        )

        # 第9题为大作文
        d_write_params = cls.build_d_write(questions[8])
        questions_explain_objs.append(EnglishPaperQuestionExplain(
            paper_explain=paper_explain,
            question_id=questions[8]['q_id'],
            question_type='大作文',
            req_params=d_write_params,
            status='ING'
        ))
        CozeWorkflowService.create_workflow_runs(
            'english_paper_explain_d_write',
            d_write_params,
            ext_params={'paper_explain_id': paper_explain.id, 'question_id': questions[8]['q_id']}
        )

        with transaction.atomic():
            EnglishPaperQuestionExplain.objects.bulk_create(questions_explain_objs)
