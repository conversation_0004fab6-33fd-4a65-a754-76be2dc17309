import json
import re

import pandas as pd
from dataclasses import dataclass


@dataclass
class WordSound:
    word: str
    american_phonetic_symbols: str = ''
    english_phonetic_symbols: str = ''


file_list = [
    '/Users/<USER>/Desktop/pro_examination_lexicon_word.xlsx',

]


word_map: dict[str, WordSound] = {}


def parse_data():
    for file in file_list:
        if file.endswith('csv'):
            file_df = pd.read_csv(file)
        elif file.endswith('xls') or file.endswith('xlsx'):
            file_df = pd.read_excel(file)
        else:
            raise Exception('类型错不支持')

        for idx, row in file_df.iterrows():
            item = row.to_dict()
            ws = WordSound(
                word=item['word'],
                english_phonetic_symbols=item['american_soundmark'],
                american_phonetic_symbols=item['english_soundmark'],
            )
            word_map[ws.word] = ws


def generate_variants(word):
    """生成去掉括号和保留括号内容的两种形式"""
    pattern = re.compile(r'\(([^)]+)\)')  # 匹配 (x) 并提取 x
    match = pattern.search(word)

    if not match:
        return [word]  # 如果没有括号，直接返回原词

    # 去掉括号内容（如 odo(u)r → odor）
    without = re.sub(r'\([^)]+\)', '', word)

    # 保留括号内容（如 odo(u)r → odour）
    with_content = re.sub(r'\(([^)]+)\)', r'\1', word)

    return [without, with_content]
