from django.db import models
from ._base import BaseModel

# 文档校正模型
class DocumentProofreader(BaseModel):
    """文档校正模型"""
    file_name = models.CharField('文件名', max_length=255, blank=True)
    original_content = models.TextField('原始内容', blank=True)
    corrected_content = models.TextField('校正后内容', blank=True)
    labeled_content = models.TextField('带标记内容', blank=True)
    status = models.CharField('状态', max_length=20, default='pending')  # pending, processing, completed, failed
    task_directory = models.CharField('任务目录', max_length=255, blank=True) 

    class Meta:
        db_table = 'ai_document_proofreader'
        verbose_name = '文档校正'
        verbose_name_plural = '文档校正'

# 文档校正错误模型（从表）
class DocumentProofreaderError(BaseModel):
    """文档校正错误模型"""
    proofreader = models.ForeignKey(DocumentProofreader, on_delete=models.CASCADE, verbose_name='所属校正任务')
    error_id = models.CharField('错误ID', max_length=50)
    error_text = models.TextField('错误文本')
    error_reason = models.TextField('错误理由')
    error_suggestion = models.TextField('修改建议')
    error_type = models.CharField('错误类型', max_length=255, default='专业术语错误类')

    class Meta:
        db_table = 'ai_document_proofreader_error'
        verbose_name = '文档校正错误'
        verbose_name_plural = '文档校正错误'

# 专有名词词库模型
class ProofreadingDictionary(BaseModel):
    """校对词库模型"""
    word = models.CharField('词语', max_length=255, db_index=True)
    category = models.CharField('类别', max_length=50, default='专有名词', help_text='词语类别，如：专有名词、技术术语、品牌名等')
    description = models.TextField('描述', blank=True, help_text='词语描述或使用说明')
    usage_count = models.IntegerField('使用次数', default=0, help_text='该词被添加到词库的次数')
    is_active = models.BooleanField('是否启用', default=True)

    class Meta:
        db_table = 'ai_document_proofreading_dictionary'
        verbose_name = '校对词库'
        verbose_name_plural = '校对词库'
        ordering = ['-add_time']

    def __str__(self):
        return f"{self.word} ({self.category})"

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

# 异步文档校正任务模型
class DocumentProofreaderAsyncTask(BaseModel):
    """异步文档校正任务模型"""
    task_id = models.CharField('任务ID', max_length=100, unique=True, db_index=True)
    file_name = models.CharField('文件名', max_length=255)
    original_content = models.TextField('原始内容', blank=True)
    status = models.CharField('任务状态', max_length=20, default='pending', 
                            choices=[
                                ('pending', '未开始'),
                                ('processing', '校对中'),
                                ('success', '成功'),
                                ('failed', '失败')
                            ])
    fail_reason = models.TextField('失败原因', blank=True)
    retry_times = models.IntegerField('重试次数', default=0)
    proofreader = models.ForeignKey(DocumentProofreader, on_delete=models.SET_NULL, 
                                   null=True, blank=True, verbose_name='关联的校对记录')
    user_session = models.CharField('用户会话', max_length=100, blank=True, help_text='用于桌面通知')
    notification_email = models.EmailField('通知邮箱', blank=True, help_text='任务完成后发送邮件通知')
    notification_sent = models.BooleanField('通知已发送', default=False)
    
    class Meta:
        db_table = 'ai_document_proofreader_async_task'
        verbose_name = '异步文档校正任务'
        verbose_name_plural = '异步文档校正任务'
        ordering = ['-add_time']
        
    def __str__(self):
        return f"{self.file_name} - {self.get_status_display()}"
    
    def mark_processing(self):
        """标记为处理中"""
        self.status = 'processing'
        self.save(update_fields=['status', 'modified_time'])
    
    def mark_success(self, proofreader_id=None):
        """标记为成功"""
        self.status = 'success'
        if proofreader_id:
            self.proofreader_id = proofreader_id
        self.save(update_fields=['status', 'proofreader_id', 'modified_time'])
    
    def mark_failed(self, reason):
        """标记为失败"""
        self.status = 'failed'
        self.fail_reason = reason
        self.retry_times += 1
        self.save(update_fields=['status', 'fail_reason', 'retry_times', 'modified_time'])
    
    def can_retry(self, max_retries=3):
        """是否可以重试"""
        return self.retry_times < max_retries and self.status == 'failed'