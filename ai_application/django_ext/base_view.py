from rest_framework.generics import GenericAPI<PERSON>iew

from django_ext.paginator import queryset_paginate
from django_ext.response import make_response


class SimpleCreateMixin:
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return make_response()

    def perform_create(self, serializer):
        serializer.save()


class SimpleUpdateMixin:
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return make_response()

    def perform_update(self, serializer):
        serializer.save()


class CreateMixin:

    def create(self, request, *args, **kwargs):
        dto = self.validate_request(request.data)
        self.perform_create(dto)
        return make_response()

    def perform_create(self, dto):
        raise NotImplementedError()


class UpdateMixin:
    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        dto = self.validate_request(request.data)
        self.perform_update(instance, dto)
        return make_response()

    def perform_update(self, instance, dto):
        raise NotImplementedError()


class DestroyMixin:
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return make_response()

    def perform_destroy(self, instance):
        instance.delete()


class RetrieveMixin:
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return make_response(serializer.data)


class BaseView(GenericAPIView):
    validator_class = None
    page = True

    def get_page(self):
        return self.page

    def get_validator_class(self):
        return self.validator_class

    def validate_request(self, data, return_dict=False):
        validator_class = self.get_validator_class()
        if validator_class is None:
            raise ValueError
        if return_dict:
            return validator_class.build_dict(data)
        return validator_class.build_dto(data)

    def finalize_response(self, request, response, *args, **kwargs):
        if response is None:
            response = make_response()
        return super().finalize_response(request, response, *args, **kwargs)


class BaseCreateView(CreateMixin, BaseView):

    def post(self, request, *args, **kwargs):
        return self.create(request, *args, **kwargs)


class BaseUpdateView(UpdateMixin, BaseView):
    def put(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)


class BaseDestroyView(DestroyMixin, BaseView):

    def delete(self, request, *args, **kwargs):
        return self.destroy(request, *args, **kwargs)


class BaseRetrieveView(RetrieveMixin, BaseView):
    def get(self, request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)


class BaseSingleView(RetrieveMixin,
                     UpdateMixin,
                     DestroyMixin,
                     BaseView):
    def get(self, request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request, *args, **kwargs):
        return self.update(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        return self.destroy(request, *args, **kwargs)


class BaseListView(BaseView):
    def fill_data(self, data):
        return data

    def get(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        if self.get_page():
            serializer_class = self.get_serializer_class()
            data = queryset_paginate(queryset, serializer_class, request.query_params)
            return make_response(self.fill_data(data))

        serializer = self.get_serializer(queryset, many=True)
        return make_response({
            'data': self.fill_data(serializer.data)
        })


class BaseListCreateView(CreateMixin, BaseListView):

    def post(self, request, *args, **kwargs):
        return self.create(request, *args, **kwargs)
