import datetime

from django.db.models import Count

from app.models import EnWordReciteBasicPaper, EnWordReciteBasicAnswer, EnWordReciteDayRecord, EnWordReciteQuestion
from app.services.main_subject.en_word_recite_service import EnWordReciteService
from django_ext.utils.date_utils import utc2local


def gen_basic_test_paper(user_id):
    # 兼容之前的逻辑
    answer: EnWordReciteBasicAnswer = EnWordReciteBasicAnswer.objects.filter(
        is_deleted=False,
        user_id=user_id,
        basic_paper__paper_type=EnWordReciteBasicPaper.PaperType.basic_test
    ).first()
    if answer:
        return get_paper_detail(answer.basic_paper)

    paper = EnWordReciteBasicPaper.objects.filter(
        is_deleted=False, user_id=user_id,
        paper_type=EnWordReciteBasicPaper.PaperType.basic_test
    ).first()
    if not paper:
        paper = EnWordReciteService.create_basic_paper(user_id)
    return get_paper_detail(paper)


def gen_week_test_paper(user_id):
    latest_paper: EnWordReciteBasicPaper = EnWordReciteBasicPaper.objects.filter(
        is_deleted=False, user_id=user_id,
        paper_type=EnWordReciteBasicPaper.PaperType.week_test
    ).first()
    if latest_paper and utc2local(latest_paper.add_time).date() == datetime.date.today():
        return get_paper_detail(latest_paper)

    # 出题
    paper = EnWordReciteService.create_week_paper(user_id)
    return get_paper_detail(paper)


def get_paper_detail(paper: EnWordReciteBasicPaper):
    paper_detail = paper.enwordrecitebasicpaperdetail_set.filter(is_deleted=False).select_related('question')
    return {
        'paper_id': paper.id,
        'question_list': [
            format_recite_question(detail.question)
            for detail in paper_detail if not detail.question.is_deleted
        ]
    }


def get_word_questions(word_ids, recited_question_map: dict):
    """
    {question_id: {
        'user_answer': i.user_answer,
        'is_right': i.is_right,
    }
    """
    if not word_ids:
        return []

    question_list = EnWordReciteQuestion.objects.filter(
        is_deleted=False,
        word_id__in=word_ids,
        question_type=EnWordReciteQuestion.QuestionType.en2ch
    )
    question_map = {
        question.word_id: format_recite_question(question)
        for question in question_list
    }

    # 按照今日计划存储的单词顺序返回
    data = []
    for w_id in word_ids:
        if w_id not in question_map:
            continue

        question_ = question_map.get(w_id)
        recited_question = recited_question_map.get(question_['question_id'])
        if recited_question:
            question_['is_answered'] = True
            question_['user_answer'] = recited_question['user_answer']
            question_['is_right'] = recited_question['is_right']

        data.append(question_)
    return data


def format_recite_question(question: EnWordReciteQuestion):
    return {
            'question_id': question.id,
            'question': question.question,
            'options': question.options,
            'answer': question.answer,
            'analysis': question.analysis,
            'en_pronounce': question.word.english_sound_mark_url,
            'us_pronounce': question.word.american_sound_mark_url,
            'en_phonetic_symbols': question.word.english_phonetic_symbols,
            'us_phonetic_symbols': question.word.american_phonetic_symbols,
            'freq': question.word.word_freq,
            'is_answered': False,
            'user_answer': '',
            'is_right': False,
        }
