import datetime
import logging

from celery import shared_task
from django.utils import timezone

from app.core.indexing_runner import IndexingRunner
from app.models import DatasetDocument, DocumentSegment

logger = logging.getLogger(__name__)


@shared_task(queue='dataset')
def auto_check_document_deleted_task():
    logger.debug('Start auto_check_document_deleted_task....')
    # 检查前3小时删除的文件
    now = timezone.now()
    start = now - datetime.timedelta(hours=3)
    deleted_documents = DatasetDocument.objects.filter(
        is_deleted=True, modified_time__gte=start, modified_time__lte=now
    )
    for doc in deleted_documents:
        indexing_runner = IndexingRunner()
        indexing_runner.clean_index(doc.dataset, doc)
        DocumentSegment.objects.filter(
            dataset_document=doc,
            is_deleted=False,
        ).update(is_deleted=True)
