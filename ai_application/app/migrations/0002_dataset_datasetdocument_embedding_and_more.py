# Generated by Django 4.2 on 2024-08-20 05:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Dataset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('dataset_no', models.CharField(max_length=36, unique=True, verbose_name='资料库编号')),
                ('name', models.Char<PERSON>ield(blank=True, max_length=100, verbose_name='资料库名称')),
                ('description', models.TextField(blank=True, verbose_name='资料库描述')),
                ('index_struct', models.J<PERSON><PERSON>ield(null=True, verbose_name='索引结构')),
                ('embedding_model_provider', models.CharField(blank=True, max_length=100, verbose_name='嵌入模型提供方')),
                ('embedding_model', models.CharField(blank=True, max_length=100, verbose_name='嵌入模型')),
                ('account', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.account')),
            ],
            options={
                'verbose_name': '资料库',
                'verbose_name_plural': '资料库',
                'db_table': 'ai_dataset',
            },
        ),
        migrations.CreateModel(
            name='DatasetDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('document_no', models.CharField(max_length=36, unique=True, verbose_name='文档编号')),
                ('name', models.CharField(blank=True, max_length=255, verbose_name='文档名称')),
                ('data_source_type', models.CharField(blank=True, max_length=40, verbose_name='数据源类型')),
                ('data_source_info', models.JSONField(null=True, verbose_name='数据源信息')),
                ('processing_started_at', models.DateTimeField(null=True, verbose_name='开始处理时间')),
                ('word_count', models.IntegerField(default=0, verbose_name='字数')),
                ('parsing_completed_at', models.DateTimeField(null=True, verbose_name='解析完成时间')),
                ('splitting_completed_at', models.DateTimeField(null=True, verbose_name='分片完成时间')),
                ('tokens', models.IntegerField(default=0, verbose_name='tokens')),
                ('indexing_latency', models.FloatField(default=0, verbose_name='索引耗时')),
                ('completed_at', models.DateTimeField(null=True, verbose_name='索引完成时间')),
                ('error', models.TextField(blank=True, verbose_name='错误信息')),
                ('stopped_at', models.DateTimeField(null=True, verbose_name='停止时间')),
                ('indexing_status', models.CharField(blank=True, max_length=40, verbose_name='索引状态')),
                ('enabled', models.BooleanField(default=True, verbose_name='是否启用')),
                ('doc_type', models.CharField(blank=True, max_length=40, verbose_name='文档类型')),
                ('doc_metadata', models.JSONField(null=True, verbose_name='文档元数据')),
                ('doc_language', models.CharField(blank=True, max_length=40, verbose_name='文档语言')),
                ('dataset', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.dataset')),
            ],
            options={
                'verbose_name': '文档',
                'verbose_name_plural': '文档',
                'db_table': 'ai_dataset_document',
            },
        ),
        migrations.CreateModel(
            name='Embedding',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('provider_name', models.CharField(blank=True, max_length=100, verbose_name='嵌入模型提供方')),
                ('model_name', models.CharField(blank=True, max_length=100, verbose_name='嵌入模型')),
                ('text_hash', models.CharField(blank=True, db_index=True, max_length=64, verbose_name='文本Hash')),
                ('embedding', models.BinaryField(null=True, verbose_name='嵌入向量')),
            ],
            options={
                'verbose_name': '向量内容缓存',
                'verbose_name_plural': '向量内容缓存',
                'db_table': 'ai_embeddings',
            },
        ),
        migrations.AddField(
            model_name='conversation',
            name='only_use_dataset',
            field=models.BooleanField(default=False, verbose_name='是否仅从本地知识库回答'),
        ),
        migrations.AddField(
            model_name='message',
            name='is_query_rewrite',
            field=models.BooleanField(default=False, verbose_name='是否query改写'),
        ),
        migrations.CreateModel(
            name='RagMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('content', models.JSONField(null=True, verbose_name='整体输入')),
                ('answer', models.TextField(null=True, verbose_name='回答')),
                ('message_tokens', models.IntegerField(default=0, verbose_name='消息tokens')),
                ('answer_tokens', models.IntegerField(default=0, verbose_name='回答tokens')),
                ('latency', models.FloatField(default=0, verbose_name='响应时间')),
                ('is_model_rating', models.BooleanField(default=False, verbose_name='是否模型评分')),
                ('model_score', models.IntegerField(default=0, verbose_name='模型得分')),
                ('direct_content', models.JSONField(null=True, verbose_name='直接问答整体输入')),
                ('direct_answer', models.TextField(null=True, verbose_name='直接回答')),
                ('direct_message_tokens', models.IntegerField(default=0, verbose_name='消息tokens')),
                ('direct_answer_tokens', models.IntegerField(default=0, verbose_name='回答tokens')),
                ('direct_latency', models.FloatField(default=0, verbose_name='响应时间')),
                ('conversation', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.conversation')),
                ('message', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
            ],
            options={
                'verbose_name': 'RAG消息记录',
                'verbose_name_plural': 'RAG消息记录',
                'db_table': 'ai_rag_message',
            },
        ),
        migrations.CreateModel(
            name='MessageTracing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('type', models.CharField(blank=True, max_length=32, verbose_name='类型')),
                ('query', models.TextField(null=True, verbose_name='用户输入')),
                ('content', models.JSONField(null=True, verbose_name='整体输入')),
                ('answer', models.TextField(null=True, verbose_name='回答')),
                ('message_tokens', models.IntegerField(default=0, verbose_name='消息tokens')),
                ('answer_tokens', models.IntegerField(default=0, verbose_name='回答tokens')),
                ('total_tokens', models.IntegerField(default=0, verbose_name='全部tokens')),
                ('latency', models.FloatField(default=0, verbose_name='响应时间')),
                ('message', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
            ],
            options={
                'verbose_name': '消息追踪记录',
                'verbose_name_plural': '消息追踪记录',
                'db_table': 'ai_message_tracing',
            },
        ),
        migrations.CreateModel(
            name='MessageQueryRewrite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('chat_history', models.JSONField(null=True, verbose_name='聊天历史')),
                ('origin_query', models.TextField(null=True, verbose_name='原始query')),
                ('prompt', models.TextField(null=True, verbose_name='提示词')),
                ('new_query', models.TextField(null=True, verbose_name='改写后query')),
                ('conversation', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.conversation')),
                ('message', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
            ],
            options={
                'verbose_name': 'query改写',
                'verbose_name_plural': 'query改写',
                'db_table': 'ai_message_query_rewrite',
            },
        ),
        migrations.CreateModel(
            name='DocumentSegment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('position', models.IntegerField(default=0, verbose_name='段落位置')),
                ('content', models.TextField(blank=True, verbose_name='内容')),
                ('word_count', models.IntegerField(default=0, verbose_name='字数')),
                ('tokens', models.IntegerField(default=0, verbose_name='tokens')),
                ('index_node_id', models.CharField(blank=True, max_length=255, verbose_name='索引节点ID')),
                ('index_node_hash', models.CharField(blank=True, max_length=255, verbose_name='索引节点Hash')),
                ('hit_count', models.IntegerField(default=0, verbose_name='命中次数')),
                ('status', models.CharField(blank=True, max_length=40, verbose_name='状态')),
                ('indexing_at', models.DateTimeField(null=True, verbose_name='索引时间')),
                ('completed_at', models.DateTimeField(null=True, verbose_name='完成时间')),
                ('error', models.TextField(null=True, verbose_name='错误信息')),
                ('stopped_at', models.DateTimeField(null=True, verbose_name='停止时间')),
                ('dataset', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.dataset')),
                ('dataset_document', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.datasetdocument')),
            ],
            options={
                'verbose_name': '文档分段',
                'verbose_name_plural': '文档分段',
                'db_table': 'ai_document_segments',
            },
        ),
        migrations.CreateModel(
            name='DatasetRetrieverResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('position', models.IntegerField(default=0, verbose_name='段落位置')),
                ('dataset_name', models.CharField(blank=True, max_length=100, verbose_name='资料库名称')),
                ('dataset_document_name', models.CharField(blank=True, max_length=255, verbose_name='文档名称')),
                ('data_source_type', models.CharField(blank=True, max_length=40, verbose_name='数据源类型')),
                ('score', models.FloatField(null=True, verbose_name='得分')),
                ('content', models.TextField(null=True, verbose_name='内容')),
                ('content_with_context', models.JSONField(null=True, verbose_name='包含上下文内容')),
                ('dataset', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.dataset')),
                ('dataset_document', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.datasetdocument')),
                ('message', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
                ('segment', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.documentsegment')),
            ],
            options={
                'verbose_name': '检索资源记录',
                'verbose_name_plural': '检索资源记录',
                'db_table': 'ai_dataset_retriever_resources',
            },
        ),
        migrations.CreateModel(
            name='DatasetQuery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('content', models.TextField(blank=True, verbose_name='查询内容')),
                ('app', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.app')),
                ('dataset', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.dataset')),
                ('message', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.message')),
            ],
            options={
                'verbose_name': '资料库查询记录',
                'verbose_name_plural': '资料库查询记录',
                'db_table': 'ai_dataset_queries',
            },
        ),
        migrations.CreateModel(
            name='DatasetProcessRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('rules', models.JSONField(null=True, verbose_name='处理规则')),
                ('dataset', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.dataset')),
            ],
            options={
                'verbose_name': '资料库处理规则',
                'verbose_name_plural': '资料库处理规则',
                'db_table': 'ai_dataset_process_rule',
            },
        ),
        migrations.AddField(
            model_name='datasetdocument',
            name='dataset_process_rule',
            field=models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.datasetprocessrule'),
        ),
        migrations.AddField(
            model_name='conversation',
            name='dataset',
            field=models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.dataset'),
        ),
    ]
