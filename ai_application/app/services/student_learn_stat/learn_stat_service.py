import datetime
import logging
from collections import defaultdict

from api_client.yantucs_data.client import yantucs_data_client
from app.models import CourseSectionKnowledge, QuestionKnowledge
from app.services.main_subject.answer_service import AnswerService

logger = logging.getLogger(__name__)


class LearnStatService:

    @classmethod
    def get_kgs_by_course_section_ids(cls, course_section_ids: list):
        if not course_section_ids:
            return []

        qs = CourseSectionKnowledge.objects.filter(
            course_section_id__in=course_section_ids
        ).values('course_section_id', 'knowledge_list', 'main_subject')
        cs_kg_map = defaultdict(list)
        for cs in qs:
            cs_kg_map[cs['main_subject']].extend(cs['knowledge_list'])

        main_subject_map = {
            'data_structure': '数据结构',
            'organization': '组成原理',
            'network': '计算机网络',
            'os': '操作系统',
            'high_math': '高等数学',
            'linear_algebra': '线性代数',
            'math_prob': '概率论与数理统计',
        }

        learned_kgs = []
        for main_subject, kgs in cs_kg_map.items():
            learned_kgs.append({
                'subject': main_subject_map.get(main_subject),
                'kgs': list(set(kgs)),
            })
        return {
            'learned_kgs': learned_kgs,
        }

    @classmethod
    def get_weakness_kgs_by_answer_ids(cls, answer_ids: list):
        wrong_question_ids = []
        for answer_id in answer_ids:
            try:
                exam_paper_content = AnswerService.get_test_answer_detail(answer_id)
            except:
                continue

            questions = exam_paper_content.get('questions', [])
            for question in questions:
                question_id = question.get('q_id')

                user_answer_detail = question.get('user_answer', {})
                user_answer = user_answer_detail.get('user_answer', '')
                user_sub_answers = question.get('user_sub_answers', [])

                if not (user_answer or user_sub_answers):
                    user_answer_detail['user_answer_status'] = 2

                user_answer_status = user_answer_detail['user_answer_status']
                if user_answer_status in [0, 2]:
                    wrong_question_ids.append(question_id)

        # wrong_question_ids = ['33507', '33498', '33497', '33493', '33486',]
        weakness_kgs_map = defaultdict(list)
        if wrong_question_ids:
            qs2 = QuestionKnowledge.objects.filter(question_id__in=wrong_question_ids)
            for q_k in qs2:
                for k in q_k.knowledge_list:
                    weakness_kgs_map[k['subject']].append(k['knowledge'])

        weakness_kgs = []
        for main_subject, kgs in weakness_kgs_map.items():
            weakness_kgs.append({
                'subject': main_subject,
                'kgs': list(set(kgs)),
            })
        return weakness_kgs

    @classmethod
    def get_user_learn_kgs(cls, user_id):
        # 获取最近30天的数据
        learn_start_time = datetime.date.today() - datetime.timedelta(days=30)
        try:
            res = yantucs_data_client.get_user_learn_stat(user_id, 'computer_408', 2026, learn_start_time)
        except Exception as e:
            logger.exception(e)
            res = {'finished_video_course_section_ids': [], 'paper_answer_ids': []}

        # 计算已学知识点
        finished_video_cs_ids = res.get('finished_video_course_section_ids', [])
        learned_kgs = cls.get_kgs_by_course_section_ids(finished_video_cs_ids)

        # 计算薄弱知识点
        answer_ids = res.get('paper_answer_ids', [])
        # answer_ids = ['3mSzLGyG2LV2vJ8tHkf2B8']
        weakness_kgs = cls.get_weakness_kgs_by_answer_ids(answer_ids)

        return {
            'learned_kgs': learned_kgs,
            'weakness_kgs': weakness_kgs,
        }

    @classmethod
    def _update_or_create_question_kg(
            cls,
            q_id,
            q_subject_id,
            obj: QuestionKnowledge | None
    ):
        if obj and obj.knowledge_list:
            return obj.knowledge_list

        q_kgs = cls._get_q_kgs_by_api(q_id)
        is_extract_knowledge = True if q_kgs else False

        if obj:
            obj.knowledge_list = q_kgs
            obj.subject_id = q_subject_id
            obj.is_extract_knowledge = is_extract_knowledge
            obj.save()
        else:
            QuestionKnowledge.objects.create(
                question_id=q_id,
                subject_id=q_subject_id,
                knowledge_list=q_kgs,
                is_extract_knowledge=is_extract_knowledge
            )
        return q_kgs

    @classmethod
    def _get_q_kgs_by_api(cls, q_id):
        try:
            res3 = yantucs_data_client.get_question_kgs([q_id])
        except:
            res3 = []

        question_kgs = res3[0] if res3 else None
        if not question_kgs:
            return []

        # [{"subject": "心理学导论", "knowledge": "视觉的基本现象"}]
        if question_kgs['core_course'] and question_kgs['new_kgs']:
            new_kgs = []
            for new_kg in question_kgs['new_kgs']:
                new_kgs.append({
                    'subject': question_kgs['core_course']['name'],
                    'knowledge': new_kg['name'],
                })

            return new_kgs

        if question_kgs['old_kgs']:
            old_kgs = []
            for i in question_kgs['old_kgs']:
                old_kgs.append({
                    'subject': i['collection_name'],
                    'knowledge': i['name'],
                })
            return old_kgs

        # TODO 使用模型获取知识点

        return []

    @classmethod
    def get_weakness_kgs_by_days(cls, user_id: str, days: int):
        today = datetime.date.today()
        day_point = today - datetime.timedelta(days=days)
        learn_end_time = datetime.datetime.combine(day_point, datetime.time.min)

        try:
            res = yantucs_data_client.get_user_answer_stat(user_id, learn_end_time)
        except Exception as e:
            logger.exception(e)
            res = {'wrong_question_ids': []}

        # 获取对应题目的知识点
        origin_wrong_question_ids = res.get('wrong_question_ids')
        wrong_question_id_subject_map = {}
        for i in origin_wrong_question_ids:
            for q_id in i['question_ids']:
                wrong_question_id_subject_map[q_id] = i['subjects']

        question_knowledge_list_map = cls._get_questions_kgs_map(wrong_question_id_subject_map)
        weakness_kgs_map = defaultdict(list)
        for _, kgs in question_knowledge_list_map.items():
            for kg in kgs:
                if kg['knowledge'] not in weakness_kgs_map[kg['subject']]:
                    weakness_kgs_map[kg['subject']].append(kg['knowledge'])

        weakness_kgs = []
        for main_subject, kgs in weakness_kgs_map.items():
            weakness_kgs.append({
                'subject': main_subject,
                'kgs': kgs,
            })
        return weakness_kgs

    @classmethod
    def get_questions_kgs(cls, question_ids, subject_id):
        question_subject_map = {q_id: [subject_id] for q_id in question_ids}
        question_knowledge_list_map = cls._get_questions_kgs_map(question_subject_map)
        data = []
        for q_id, kgs in question_knowledge_list_map.items():
            data.append({
                'question_id': q_id,
                'knowledge_list': kgs,
            })
        return data

    @classmethod
    def _get_questions_kgs_map(cls, question_subject_map: dict):
        question_ids = list(question_subject_map.keys())

        exist_question_kg_qs = QuestionKnowledge.objects.filter(question_id__in=question_ids)
        exist_question_ids = []
        question_knowledge_list_map = {}
        for i in exist_question_kg_qs:
            # 临时判断按照uuid存储
            if i.question_uuid or i.is_extract_knowledge:
                exist_question_ids.append(i.question_id)
            if i.knowledge_list:
                question_knowledge_list_map[i.question_id] = i.knowledge_list

        not_exist_q_ids = list(set(question_ids) - set(exist_question_ids))
        if not_exist_q_ids:
            # 更新数据库问题
            for q_id in question_ids:
                q_subject_ids = question_subject_map.get(q_id)
                question_subject_id = q_subject_ids[0] if q_subject_ids else ''
                obj: QuestionKnowledge = QuestionKnowledge.objects.filter(question_id=q_id).first()
                q_kgs = cls._update_or_create_question_kg(
                    q_id, question_subject_id, obj
                )
                if q_kgs:
                    question_knowledge_list_map[q_id] = q_kgs

        return question_knowledge_list_map
