--
-- Create model GraduateCollegeInfo
--
CREATE TABLE `ai_graduate_college_info` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `code` varchar(10) NULL, `name` varchar(255) NULL, `department` varchar(255) NULL, `province` varchar(255) NULL, `city` varchar(255) NULL, `level` varchar(255) NULL, `type` varchar(255) NULL);
--
-- Create model GraduateMajorInfo
--
CREATE TABLE `ai_graduate_major_info` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `graduate_first_discipline` varchar(255) NULL, `graduate_second_category_code` varchar(10) NULL, `graduate_second_category` varchar(255) NULL, `graduate_third_major_code` varchar(10) NULL, `graduate_third_major` varchar(255) NULL);
CREATE INDEX `ai_graduate_college_info_add_time_0f4568cb` ON `ai_graduate_college_info` (`add_time`);
CREATE INDEX `ai_graduate_major_info_add_time_630d8199` ON `ai_graduate_major_info` (`add_time`);