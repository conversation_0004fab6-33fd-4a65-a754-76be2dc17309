import json
import logging
import re
import time

from cozepy import WorkflowExecuteStatus, WorkflowEventType
from django.conf import settings

from app.core.entities.coze_workflow_entites import (
    CozeWorkflowMessageEvent, CozeWorkflowMessageEndEvent, CozeWorkflowMessageErrorEvent
)
from app.libs.coze_api import CozeApiClient
from app.models import (
    Conversation,
    CozeWorkflowResult, CozeWorkflow, EnglishWordTestStrategy, EnglishWordTestQuestion, EnglishWordTestOrigin,
    EnglishWordLibrary, EnWordReciteQuestion
)
from app.services.coze_app_generate_service import coze_chat_stream
from app.core.utils.string_utils import extract_code_from_markdown
from app.services.main_subject.en_paper_explain_process_service import EnPaperExplainProcessService

logger = logging.getLogger(__name__)


def parse_answer_str(answer_str: str) -> tuple[str, str]:
    # 匹配答案
    answer_pattern = r"答案：(.*?)\n"
    answer_match = re.search(answer_pattern, answer_str)
    answer = answer_match.group(1).strip() if answer_match else None

    # 匹配解析内容
    analysis_pattern = r"解析：(.*?)(?=\n|$)"
    analysis_match = re.search(analysis_pattern, answer_str, re.DOTALL)
    analysis = analysis_match.group(1).strip() if analysis_match else None

    return f'答案：{answer}', f'解析：{analysis}'


def get_debug_coze_api_client():
    COZE_JWT_OAUTH_CLIENT_ID = '1147711770850'
    COZE_JWT_OAUTH_PRIVATE_KEY = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""
    COZE_JWT_OAUTH_PUBLIC_KEY_ID = '-F-hbQT9oZZI6bDoJsq6uEXF5flxdhTvi2wNZce4msA'

    return CozeApiClient(
        coze_api_base=settings.COZE_API_BASE,
        jwt_oauth_client_id=COZE_JWT_OAUTH_CLIENT_ID,
        jwt_oauth_private_key=COZE_JWT_OAUTH_PRIVATE_KEY,
        jwt_oauth_public_key_id=COZE_JWT_OAUTH_PUBLIC_KEY_ID,
        expire_time=settings.COZE_JWT_TOKEN_EXPIRE_TIME,
    ).coze


# coze_api_client = get_debug_coze_api_client()

from app.libs.coze_api import coze_api_client


class CozeWorkflowService:

    @classmethod
    def create_workflow(cls, workflow_id: str, parameters: dict, ext_params=None, is_async= True):
        if ext_params is None:
            ext_params = {}

        parameters = parameters or {}
        workflow_runs = coze_api_client.workflows.runs
        print(f'out_str在这里啊啊啊啊啊啊啊:{is_async}')

        res = workflow_runs.create(
            workflow_id=workflow_id,
            parameters=parameters,
            is_async=is_async
        )

        if is_async:
            output_str = ''

        else:
            output_str = json.dumps({'Output': res.data})
        return CozeWorkflowResult.objects.create(
            workflow_id=workflow_id,
            parameters=parameters,
            execute_id=res.execute_id if is_async else '',
            ext_params=ext_params,
            status=CozeWorkflowResult.ExecuteStatus.RUNNING if is_async else CozeWorkflowResult.ExecuteStatus.SUCCESS,
            output_str=output_str
        )

    @classmethod
    def create_workflow_stream(cls, workflow_id: str, parameters: dict, ext_params=None):
        if ext_params is None:
            ext_params = {}

        parameters = parameters or {}

        workflow_runs = coze_api_client.workflows.runs

        answer = ''

        res_generator = workflow_runs.stream(
            workflow_id=workflow_id,
            parameters=parameters
        )
        start_at = time.perf_counter()
        for event in res_generator:
            if event.event == WorkflowEventType.MESSAGE:
                if event.message.content:
                    answer += event.message.content
                    yield CozeWorkflowMessageEvent(content=event.message.content)
                if event.message.node_is_finish:
                    yield CozeWorkflowMessageEvent(content=event.message.content)
                    latency = time.perf_counter() - start_at
                    yield CozeWorkflowMessageEndEvent(answer=answer, latency=latency)
            elif event.event == WorkflowEventType.ERROR:
                CozeWorkflowMessageErrorEvent()
            elif event.event == WorkflowEventType.DONE:
                latency = time.perf_counter() - start_at
                yield CozeWorkflowMessageEndEvent(answer=answer, latency=latency)

    @classmethod
    def query_workflow_run_result(cls, workflow_id: str, execute_id: str):
        workflow_runs = coze_api_client.workflows.runs
        res = workflow_runs.run_histories.retrieve(
            workflow_id=workflow_id,
            execute_id=execute_id,
        )
        if res.execute_status == WorkflowExecuteStatus.SUCCESS:
            return {
                'status': CozeWorkflowResult.ExecuteStatus.SUCCESS,
                'output_str': res.output,
            }
        elif res.execute_status == WorkflowExecuteStatus.FAIL:
            return {
                'status': CozeWorkflowResult.ExecuteStatus.FAIL,
                'err_log_id': res.logid,
            }
        else:
            return {
                'status': CozeWorkflowResult.ExecuteStatus.RUNNING
            }

    @classmethod
    def create_workflow_runs(cls, code, params, ext_params):
        workflow: CozeWorkflow = CozeWorkflow.objects.filter(code=code).first()
        if not workflow:
            return

        CozeWorkflowService.create_workflow(workflow.workflow_id, params, ext_params)

    @classmethod
    def create_workflow_runs_sync(cls, code, params, ext_params) -> CozeWorkflowResult:
        workflow: CozeWorkflow = CozeWorkflow.objects.filter(code=code).first()
        if not workflow:
            raise Exception('CozeWorkflow not exist')

        return CozeWorkflowService.create_workflow(workflow.workflow_id, params, ext_params, is_async=False)

    @classmethod
    def create_workflow_runs_stream(cls, code, params, ext_params) :
        workflow: CozeWorkflow = CozeWorkflow.objects.filter(code=code).first()
        if not workflow:
            raise Exception('CozeWorkflow not exist')

        # 调用流式接口
        return CozeWorkflowService.create_workflow_stream(workflow.workflow_id, params, ext_params)

    @classmethod
    def check_running_workflow(cls):
        qs = CozeWorkflowResult.objects.filter(
            is_deleted=False,
            # workflow_id='7494102821067931683',
            status=CozeWorkflowResult.ExecuteStatus.RUNNING
        )[:100]
        for i in qs:
            res = cls.query_workflow_run_result(i.workflow_id, i.execute_id)
            if res.get('status') == CozeWorkflowResult.ExecuteStatus.SUCCESS:
                i.status = CozeWorkflowResult.ExecuteStatus.SUCCESS
                i.output_str = res.get('output_str', '')
                i.save()

                cls.handle_workflow_success(i)
            elif res.get('status') == CozeWorkflowResult.ExecuteStatus.FAIL:
                i.status = CozeWorkflowResult.ExecuteStatus.FAIL
                i.err_log_id = res.get('err_log_id', '')
                i.save()

    @classmethod
    def handle_workflow_success(cls, workflow_result: CozeWorkflowResult):
        workflow_model: CozeWorkflow = CozeWorkflow.objects.filter(
            is_deleted=False, workflow_id=workflow_result.workflow_id).first()
        if not workflow_model:
            return

        if workflow_model.code == 'english_word_test_strategy':
            test_origin_id = workflow_result.ext_params.get('test_origin_id')
            test_origin: EnglishWordTestOrigin = EnglishWordTestOrigin.objects.filter(
                is_deleted=False, id=test_origin_id).first()

            try:
                strategy_res = json.loads(workflow_result.output_str)
                strategy_list = json.loads(strategy_res.get('输出', ''))

                strategy_objs = []
                for strategy in strategy_list:
                    if not strategy.strip():
                        continue
                    strategy_objs.append(EnglishWordTestStrategy(
                        test_origin=test_origin,
                        strategy_content=strategy.strip(),
                    ))
                EnglishWordTestStrategy.objects.bulk_create(strategy_objs)
            except Exception as e:
                logger.exception(e)
        elif workflow_model.code == 'english_word_test_question':
            en_word_id = workflow_result.ext_params.get('en_word_id')
            en_word: EnglishWordLibrary = EnglishWordLibrary.objects.filter(is_deleted=False, id=en_word_id).first()

            try:
                output_dict = json.loads(workflow_result.output_str)
                questions_dict = json.loads(output_dict.get('Output'))
                questions_str = extract_code_from_markdown(questions_dict.get('output'))
                # questions_res = safe_parse_json(questions_str)
                questions_res = json.loads(questions_str)

                q_objs = []
                for q_item in questions_res:
                    options = q_item.get('options', [])
                    filtered_options = [re.sub(r"[A-D]\.\s*", "", o) for o in options]

                    q_objs.append(EnglishWordTestQuestion(
                        word=en_word.word,
                        en_word=en_word,
                        question_type=q_item.get('type', ''),
                        question=q_item.get('question', ''),
                        options=filtered_options,
                        analysis=q_item.get('analysis', ''),
                        answer=q_item.get('answer', ''),
                        level=q_item.get('level', ''),
                    ))
                EnglishWordTestQuestion.objects.bulk_create(q_objs)

                workflow_result.process_status = 'SUCCESS'
                workflow_result.save()
            except Exception as e:
                logger.exception(e)
                workflow_result.process_status = 'FAIL'
                workflow_result.process_fail_reason = str(e)
                workflow_result.save()
        elif workflow_model.code == 'english_word_recite_question':
            en_word_id = workflow_result.ext_params.get('en_word_id')
            en_word: EnglishWordLibrary = EnglishWordLibrary.objects.filter(is_deleted=False, id=en_word_id).first()

            try:
                output_dict = json.loads(workflow_result.output_str)
                question_info_list = json.loads(output_dict.get('Output')).get('output')
                parsed_question = parse_recite_question(question_info_list)

                EnWordReciteQuestion.objects.create(
                    word=en_word,
                    question_type=EnWordReciteQuestion.QuestionType.en2ch,
                    question=parsed_question.get('title', ''),
                    options=parsed_question.get('options', []),
                    answer=parsed_question.get('answer', ''),
                    analysis=parsed_question.get('analysis', ''),
                    example_sentence=parsed_question.get('example_sentence', ''),
                )

                workflow_result.process_status = 'SUCCESS'
                workflow_result.save()
            except Exception as e:
                logger.exception(e)
                workflow_result.process_status = 'FAIL'
                workflow_result.process_fail_reason = str(e)
                workflow_result.save()
        elif workflow_model.code == 'english_word_recite_question2':
            en_word_id = workflow_result.ext_params.get('en_word_id')
            en_word: EnglishWordLibrary = EnglishWordLibrary.objects.filter(is_deleted=False, id=en_word_id).first()

            try:
                output_dict = json.loads(workflow_result.output_str)
                question_info_list = json.loads(output_dict.get('Output')).get('output')

                parsed_question = parse_recite_question2(question_info_list)

                EnWordReciteQuestion.objects.create(
                    word=en_word,
                    question_type=EnWordReciteQuestion.QuestionType.multi_define,
                    question=parsed_question.get('title'),
                    options=parsed_question.get('options'),
                    answer=parsed_question.get('answer'),
                    analysis=parsed_question.get('analysis'),
                    example_sentence=parsed_question.get('example_sentence'),
                )

                workflow_result.process_status = 'SUCCESS'
                workflow_result.save()
            except Exception as e:
                logger.exception(e)
                workflow_result.process_status = 'FAIL'
                workflow_result.process_fail_reason = str(e)
                workflow_result.save()
        elif workflow_model.code == 'english_word_recite_question3':
            en_word_id = workflow_result.ext_params.get('en_word_id')
            en_word: EnglishWordLibrary = EnglishWordLibrary.objects.filter(is_deleted=False, id=en_word_id).first()

            try:
                output_dict = json.loads(workflow_result.output_str)
                question_info_list = json.loads(output_dict.get('Output')).get('output')

                parsed_question = parse_recite_question3(question_info_list)

                EnWordReciteQuestion.objects.create(
                    word=en_word,
                    question_type=EnWordReciteQuestion.QuestionType.relate_define,
                    question=parsed_question.get('title'),
                    options=parsed_question.get('options'),
                    answer=parsed_question.get('answer'),
                    analysis=parsed_question.get('analysis'),
                )

                workflow_result.process_status = 'SUCCESS'
                workflow_result.save()
            except Exception as e:
                logger.exception(e)
                workflow_result.process_status = 'FAIL'
                workflow_result.process_fail_reason = str(e)
                workflow_result.save()
        elif workflow_model.code in [
            'english_paper_explain_wanxing',
            'english_paper_explain_read',
            'english_paper_explain_new',
            'english_paper_explain_translate',
            'english_paper_explain_x_write',
            'english_paper_explain_d_write',
        ]:
            EnPaperExplainProcessService.process_question_explain(workflow_result)
        elif workflow_model.code == 'english_paper_explain_summary':
            EnPaperExplainProcessService.process_summary_explain(workflow_result)

    @classmethod
    def post_question(cls, app_model, user, dto):
        coze_conversation = coze_api_client.conversations.create(bot_id=dto.app_id)
        conversation = Conversation.objects.create(
            conversation_no=coze_conversation.id,
            app=app_model,
            app_model_config=app_model.app_model_config,
            name='New conversation',
            from_account=user,
            from_biz_id=dto.biz_id,
        )
        dto.conversation_id = conversation.conversation_no

        time.sleep(0.1)
        return coze_chat_stream(
            app_model=app_model,
            dto=dto,
            from_account=user,
            coze_client=coze_api_client,
        )


def parse_recite_question(question_info_list):
    """
    :param question_info_list:
    :return:
    """
    if len(question_info_list) < 9:
        raise Exception('题目解析失败，格式错误')

    title = question_info_list[1].strip()
    title = re.sub(r"^\[", "", title)
    title = re.sub(r"\]$", "", title)

    options = []
    # 处理选项
    for i in question_info_list[2:6]:
        i = i.strip()
        i = re.sub(r"\s*[A-D]\.\s*", "", i)
        i = re.sub(r"^\[", "", i)
        i = re.sub(r"\]$", "", i)

        options.append(i)

    matches = re.findall(r"[ABCD]", question_info_list[6])
    answer = matches[0] if matches else ''
    analysis = question_info_list[7].strip()
    example_sentence = question_info_list[8].strip()
    example_sentence = example_sentence.replace('例句:', '')
    example_sentence = re.sub(r"^\[", "", example_sentence)
    example_sentence = re.sub(r"\]$", "", example_sentence)

    return {
        'title': title,
        'options': options,
        'analysis': analysis,
        'answer': answer,
        'example_sentence': example_sentence,
    }


def parse_recite_question2(question_info_list):
    """
    :param question_info_list:
    :return:
    """
    if len(question_info_list) < 9:
        raise Exception('题目解析失败，格式错误')

    title = question_info_list[1].strip()
    title = re.sub(r"^\[", "", title)
    title = re.sub(r"\]$", "", title)

    options = []
    # 处理选项
    for i in question_info_list[2:6]:
        i = i.strip()
        i = re.sub(r"\s*[A-D]\.\s*", "", i)
        i = re.sub(r"^\[", "", i)
        i = re.sub(r"\]$", "", i)

        options.append(i)

    matches = re.findall(r"[ABCD]", question_info_list[6])
    answer = matches[0] if matches else ''
    analysis = question_info_list[7].strip()
    example_sentence = question_info_list[8].strip()
    example_sentence = example_sentence.replace('例句:', '')
    example_sentence = re.sub(r"^\[", "", example_sentence)
    example_sentence = re.sub(r"\]$", "", example_sentence)

    return {
        'title': title,
        'question_type': EnWordReciteQuestion.QuestionType.multi_define,
        'options': options,
        'analysis': analysis,
        'answer': answer,
        'example_sentence': example_sentence,
    }


def parse_recite_question3(question_info_list):
    """
    :param question_info_list:
    :return:
    """
    if len(question_info_list) < 9:
        raise Exception('题目解析失败，格式错误')

    title = question_info_list[1].strip()
    title = re.sub(r"^\[", "", title)
    title = re.sub(r"\]$", "", title)

    options = []
    # 处理选项
    for i in question_info_list[2:6]:
        i = i.strip()
        i = re.sub(r"\s*[A-D]\.\s*", "", i)
        i = re.sub(r"^\[", "", i)
        i = re.sub(r"\]$", "", i)

        options.append(i)

    matches = re.findall(r"[ABCD]", question_info_list[6])
    answer = matches[0] if matches else ''
    analysis = question_info_list[7].strip()
    example_sentence = question_info_list[8].strip()
    example_sentence = example_sentence.replace('例句:', '')
    example_sentence = re.sub(r"^\[", "", example_sentence)
    example_sentence = re.sub(r"\]$", "", example_sentence)

    return {
        'title': title,
        'question_type': EnWordReciteQuestion.QuestionType.relate_define,
        'options': options,
        'analysis': analysis,
        'answer': answer,
        'example_sentence': example_sentence,
    }


def create_test_question(word_freq, word_count=100):
    allow_word_freqs = ['low', 'middle', 'high']
    assert word_freq in allow_word_freqs, f"need {allow_word_freqs}"
    assert isinstance(word_count, int) and word_count >= 1, 'invalid word_count'

    from app.models import EnglishWordLibrary, EnglishWordTestStrategy, EnglishWordTestQuestion

    strategy_qs = EnglishWordTestStrategy.objects.filter(is_deleted=False).all()
    strategy_arr = [i.strategy_content for i in strategy_qs]
    strategy_str = '\n'.join(strategy_arr)

    code = 'english_word_test_question'
    # 出题
    # 已经出过的单词
    workflow: CozeWorkflow = CozeWorkflow.objects.get(code=code)

    has_question_words = []
    qs = CozeWorkflowResult.objects.filter(is_deleted=False, workflow_id=workflow.workflow_id)
    for i in qs:
        has_question_words.append(i.ext_params.get('en_word_id'))

    all_words = list(EnglishWordLibrary.objects.filter(
        is_deleted=False, word_freq=word_freq
    ).values_list('id', flat=True))
    not_exist_words = list(set(all_words) - set(has_question_words))
    query_words = not_exist_words[:word_count]

    # has_question_words_qs = EnglishWordTestQuestion.objects.values('en_word_id').annotate(question_count=Count('id'))
    # has_question_words = [i['en_word_id'] for i in has_question_words_qs]

    word_list = EnglishWordLibrary.objects.filter(
        is_deleted=False, word_freq=word_freq, id__in=query_words
    )
    for word in word_list:
        params = {
            'word': word.word,
            'strategy': strategy_str,
        }

        ext_params = {
          'en_word_id': word.id,
        }
        CozeWorkflowService.create_workflow_runs(code, params, ext_params)
        print(f'单词:{word.word}')
        time.sleep(0.1)


def create_recite_question(word_freq, word_count=100):
    allow_word_freqs = ['low', 'middle', 'high']
    assert word_freq in allow_word_freqs, f"need {allow_word_freqs}"
    assert isinstance(word_count, int) and word_count >= 1, 'invalid word_count'

    from app.models import EnglishWordLibrary

    # 出题
    # 已经出过的单词
    code = 'english_word_recite_question'
    workflow: CozeWorkflow = CozeWorkflow.objects.get(code=code)
    qs = CozeWorkflowResult.objects.filter(
        is_deleted=False, workflow_id__in=[workflow.workflow_id, '7486697839978020864'],
        id__gt=15350
    )
    has_question_words = [i.ext_params.get('en_word_id') for i in qs]

    all_words = list(EnglishWordLibrary.objects.filter(
        is_deleted=False, word_freq=word_freq
    ).values_list('id', flat=True))
    not_exist_words = list(set(all_words) - set(has_question_words))
    query_words = not_exist_words[:word_count]

    word_list = EnglishWordLibrary.objects.filter(
        is_deleted=False, id__in=query_words
    )

    for word in word_list:
        params = {
            'highwords': word.word,
        }

        ext_params = {
          'en_word_id': word.id,
        }
        # CozeWorkflowService.create_workflow_runs_sync(code, params, ext_params)
        CozeWorkflowService.create_workflow_runs(code, params, ext_params)
        print(f'单词:{word.word}')
        time.sleep(0.1)


def create_recite_question2(word_freq, word_count=100):
    allow_word_freqs = ['low', 'middle', 'high']
    assert word_freq in allow_word_freqs, f"need {allow_word_freqs}"
    assert isinstance(word_count, int) and word_count >= 1, 'invalid word_count'

    from app.models import EnglishWordLibrary

    # 出题
    # 已经出过的单词
    code = 'english_word_recite_question2'
    workflow: CozeWorkflow = CozeWorkflow.objects.get(code=code)
    has_question_words = []
    qs = CozeWorkflowResult.objects.filter(
        is_deleted=False, workflow_id=workflow.workflow_id,
        id__gt=17136,
    )
    for i in qs:
        has_question_words.append(i.ext_params.get('en_word_id'))

    all_words = list(EnglishWordLibrary.objects.filter(
        is_deleted=False, word_freq=word_freq
    ).values_list('id', flat=True))
    not_exist_words = list(set(all_words) - set(has_question_words))
    query_words = not_exist_words[:word_count]

    word_list = EnglishWordLibrary.objects.filter(
        is_deleted=False, id__in=query_words
    )

    for word in word_list:
        params = {
            'highwords': word.word,
        }

        ext_params = {
          'en_word_id': word.id,
        }
        # CozeWorkflowService.create_workflow_runs_sync(code, params, ext_params)
        CozeWorkflowService.create_workflow_runs(code, params, ext_params)
        print(f'单词:{word.word}')
        time.sleep(0.1)


def create_recite_question3(word_freq, word_count=100):
    allow_word_freqs = ['low', 'middle', 'high']
    assert word_freq in allow_word_freqs, f"need {allow_word_freqs}"
    assert isinstance(word_count, int) and word_count >= 1, 'invalid word_count'

    from app.models import EnglishWordLibrary

    # 出题
    # 已经出过的单词
    code = 'english_word_recite_question3'
    workflow: CozeWorkflow = CozeWorkflow.objects.get(code=code)
    has_question_words = []
    qs = CozeWorkflowResult.objects.filter(
        is_deleted=False, workflow_id=workflow.workflow_id,
        id__gt=17136,
    )
    for i in qs:
        has_question_words.append(i.ext_params.get('en_word_id'))

    all_words_qs = EnglishWordLibrary.objects.filter(
        is_deleted=False, word_freq=word_freq,
    )

    all_words = []
    for w in all_words_qs:
        if w.antonym or w.synonym:
            all_words.append(w.id)

    not_exist_words = list(set(all_words) - set(has_question_words))
    query_words = not_exist_words[:word_count]

    word_list = EnglishWordLibrary.objects.filter(
        is_deleted=False, id__in=query_words
    )

    for word in word_list:
        params = {
            'highwords': word.word,
        }

        ext_params = {
          'en_word_id': word.id,
        }
        # CozeWorkflowService.create_workflow_runs_sync(code, params, ext_params)
        CozeWorkflowService.create_workflow_runs(code, params, ext_params)
        print(f'单词:{word.word}')
        time.sleep(0.1)


def gaoshu_ocr(pic,user_id):

    params = {
        'pic': pic,
        'user_id':user_id,
    }

    ext_params = { }

    code = 'knowledge_gaoshu_OCR'

    workflow: CozeWorkflow = CozeWorkflow.objects.get(code=code)

    response =  CozeWorkflowService.create_workflow_runs_sync(code, params, ext_params)
    output_str = response.output_str

    # 解析外层 JSON
    outer_json = json.loads(output_str)
    # 解析内层 JSON
    inner_json = json.loads(outer_json.get('Output', '{}'))
    data = inner_json.get('data', '')

    print (f"ocr识别结果是:{output_str}")
    return data

def gaoshu_ocr_judge(pic,user_id,question,keywords):

    params = {
        'pic': pic,
        'user_id':user_id,
        'question':question,
        'keywords':keywords,
    }

    ext_params = { }

    code = 'knowledge_gaoshu_judPIC'

    workflow: CozeWorkflow = CozeWorkflow.objects.get(code=code)

    response =  CozeWorkflowService.create_workflow_runs_sync(code, params, ext_params)
    output_str = response.output_str
    outer_json = json.loads(output_str)
    # 解析内层 JSON
    inner_json = json.loads(outer_json.get('Output', '{}'))
    judge = inner_json.get('judge', '')
    knowledge = inner_json.get('knowledge', [])
    print(f"判断的结果是:{output_str}")
    return {'judge': judge, 'knowledge': knowledge}

