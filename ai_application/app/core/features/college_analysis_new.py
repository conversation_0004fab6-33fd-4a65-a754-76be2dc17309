from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate
from typing import Dict
import json


class CollegeAnalysisNewFeature(BaseFeature):

    def __init__(self, app_no: str):
        self.prompt_template = self._get_prompt_template(app_no)

        if not self.prompt_template:
            raise Exception('Prompt template not found')

    def _get_prompt_template(self, app_no: str) -> PromptTemplate:
        return PromptTemplate.objects.filter(app_no=app_no).first()


    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            stream: bool,
            query:str,
            student_info:str,

    ):

        pre_prompt = self.prompt_template.assemble_prompt(
            query="",
            inputs ={"student_info":student_info}
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=self.prompt_template
        )
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query = query,
            stream=stream
        )

