import json

from api_client.exam.client import exam_client
from app.errors import QuestionNotFoundError
from app.services.main_subject.new_question_service import NewQuestionService
from app.services.question_service import QuestionService


class TestPaperService:
    choiceMap = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"]

    @classmethod
    def get_paper_detail(cls, paper_id: str):
        res = exam_client.get_paper_detail(paper_id)
        q_ids = []
        for g in res['paper_detail']['groups']:
            for q in g['questions']:
                q_ids.append(q['id'])

        format_questions = NewQuestionService.format_questions(q_ids)

        return {
            'uuid': res['uuid'],
            'intid': res['intid'],
            'name': res['name'],
            'paper_score': res['mark'],
            'questions': format_questions,
        }

    @classmethod
    def get_answer_detail(cls, answer_id: str):
        """
        测试数据：
        单选题
        """
        # 0单选 1多选 2主观 3材料 4共享题干 5共享选项 6填空
        answer = exam_client.get_answer_detail(answer_id)
        answer_detail = answer['answer_detail']

        q_ids = []
        for g in answer_detail['groups']:
            for q in g['questions']:
                q_ids.append(str(q['id']))

        # q_ids = ["144545", "128971", "157553", "147299"]
        # q_ids = ["144545"]
        formatted_questions = QuestionService.get_question_by_ids(q_ids)
        formatted_questions_map = {i['q_id']: i for i in formatted_questions}
        # TODO 获取数据仓库的试题数据

        questions = []
        for g in answer_detail['groups']:
            for q in g['questions']:
                q_id = str(q['id'])

                if q_id in formatted_questions_map:
                    question_info = formatted_questions_map.get(q_id)
                else:
                    question_info = cls._format_origin_question(q)

                if question_info['q_type'] in [0, 1, 2, 6]:
                    new_question_info = cls._rebuild_single_question(question_info, q['mark'])
                    user_answer = cls._fill_single_question_answer(question_info, q)
                    user_sub_answers = []
                else:
                    new_question_info = cls._rebuild_complex_question(question_info, q)
                    user_answer = None
                    user_sub_answers = cls._fill_complex_question_answer(question_info, q)

                questions.append({
                    'q_id': str(q['id']),
                    'q_type': question_info['q_type'],
                    'difficulty': q['difficulty'],
                    'question': new_question_info,
                    'user_answer': user_answer,
                    'user_sub_answers': user_sub_answers,
                })

        return {
            'paper_name': answer['paper_detail']['name'],
            'start_time': answer['start_time'],
            'end_time': answer['end_time'],
            'paper_score': float(answer_detail['marks']),
            'answer_score': float(answer['my_score']),
            'answer_rank': answer['my_rank'],
            'questions': questions,
        }

    @classmethod
    def get_question_content_by_vector(cls, q_id):
        q_id = str(q_id)
        vector_questions = QuestionService.get_vector_question_by_ids([q_id])
        if not vector_questions:
            raise QuestionNotFoundError()

        question_info = vector_questions[0]
        if question_info['type_id'] in [0, 1, 2, 6]:
            question_content = cls._get_single_question_origin_content(question_info)
        else:
            question_content = cls._get_complex_question_origin_content(question_info)
        return question_content

    @classmethod
    def _get_single_question_origin_content(cls, q_item):
        if q_item['type_id'] in [0, 1]:
            title = q_item['master_title']
            choice_body = '\n'.join(q_item['sub_question_info'][0]['choice_body'])
            question_content = f'{title}\n{choice_body}'
        else:
            question_content = q_item['master_title']
        return question_content

    @classmethod
    def _get_complex_question_origin_content(cls, q_item):
        main_title = q_item['master_title']
        sub_titles = []
        for sub_idx, sub in enumerate(q_item['sub_question_info'], start=1):
            if isinstance(sub['choice_body'], list) and len(sub['choice_body']) > 1:
                # 默认为选择题
                sub_title = sub['sub_title']
                sub_choice_body = '\n'.join(sub['choice_body'])
                sub_question_content = f'{sub_title}\n{sub_choice_body}'
                sub_titles.append(f'{sub_idx}. {sub_question_content}')
            else:
                sub_titles.append(f'{sub_idx}. {sub["sub_title"]}')

        sub_titles_str = '\n\n'.join(sub_titles)
        return f'{main_title}\n\n{sub_titles_str}'

    @classmethod
    def _format_origin_question(cls, question_info):
        q_item = {
            'q_id': str(question_info['id']),
            'q_type': question_info["classification"]["template"],
            'title': question_info['question']['title'],
            'choices': [],
            'choice_right_answer': [],
            'subjective_right_answer': '',
            'fill_right_answer': [],
            'analysis': '',
            'rich_analysis': '',
            'sub_questions': [],
        }
        if question_info["classification"]["template"] in [0, 1]:
            q_item['analysis'] = question_info['question']['analysis']
            q_item['rich_analysis'] = question_info['question']['rich_analysis']
            for c_idx, c in enumerate(question_info['question']['choices_set']):
                choice_label = cls.choiceMap[c_idx]
                choice_body = f"{choice_label}: {c['body']}"
                q_item['choices'].append(choice_body)
                if c['is_answer']:
                    q_item['choice_right_answer'].append(choice_body)

        elif question_info["classification"]["template"] == 2:
            q_item['analysis'] = question_info['question']['analysis']
            q_item['rich_analysis'] = question_info['question']['rich_analysis']
            q_item['subjective_right_answer'] = question_info['question']['answer']
        elif question_info["classification"]["template"] == 3:
            pass
        elif question_info["classification"]["template"] == 4:
            pass
        elif question_info["classification"]["template"] == 5:
            pass
        elif question_info["classification"]["template"] == 6:
            q_item['analysis'] = question_info['question']['analysis']
            q_item['rich_analysis'] = question_info['question']['rich_analysis']
            q_item['fill_right_answer'] = question_info['question']['answer']

        return q_item

    @classmethod
    def _rebuild_single_question(cls, q_item, score):
        data = {
            'q_id': q_item['q_id'],
            'question': '',
            'right_answer': '',
            'analysis': q_item['rich_analysis'],
            'score': score,
            'sub_questions': [],
        }
        if not data['analysis']:
            data['analysis'] = q_item['analysis']

        if q_item['q_type'] in [0, 1]:
            title = q_item['title']
            choice_body = '\n'.join(q_item['choices'])
            data['question'] = f'{title}\n{choice_body}'
            data['right_answer'] = '\n'.join(q_item['choice_right_answer'])
        elif q_item['q_type'] == 2:
            data['question'] = q_item['title']
            data['right_answer'] = q_item['subjective_right_answer']
        elif q_item['q_type'] == 6:
            data['question'] = q_item['title']
            # TODO 填空题需要重新处理
            data['right_answer'] = json.dumps(q_item['fill_right_answer'])

        return data

    @classmethod
    def _rebuild_complex_question(cls, q_item, answer_question):
        # 处理问题mapping
        sub_answer_questions_mapping = {}
        answer_question_info = answer_question['question']
        for s_q in answer_question_info.get('materialchoice_set', []):
            sub_answer_questions_mapping[str(s_q['id'])] = s_q
        for s_q in answer_question_info.get('materialsubjective_set', []):
            sub_answer_questions_mapping[str(s_q['id'])] = s_q
        for s_q in answer_question_info.get('materialblank_set', []):
            sub_answer_questions_mapping[str(s_q['id'])] = s_q
        for s_q in answer_question_info.get('shared_options_questions', []):
            sub_answer_questions_mapping[str(s_q['id'])] = s_q

        sub_questions = q_item['sub_questions']
        new_sub_questions = []
        for s_q in sub_questions:
            sub_answer_question = sub_answer_questions_mapping.get(s_q['q_id'])
            if not sub_answer_question:
                continue
            new_sub_questions.append(cls._rebuild_single_question(s_q, sub_answer_question['mark']))

        return {
            'question': q_item['title'],
            'sub_questions': new_sub_questions,
        }

    @classmethod
    def _fill_single_question_answer(cls, q_item, answer_question):
        data = {
            # 0答错，1答对，2未作答，3未批阅
            'user_answer_status': answer_question['my_answer']['result'] if answer_question['my_answer'] else 2,
            'user_answer': '',
            'my_answer': answer_question['my_answer'],
        }
        if not (answer_question['my_answer'] and answer_question['my_answer'].get('is_answered')):
            data['user_answer_status'] = 2

        my_answer = answer_question['my_answer']
        if q_item['q_type'] in [0, 1]:
            my_choice_answer = my_answer['answer'] if my_answer else []
            my_choice_answer_label = []
            for c_idx, c in enumerate(answer_question['question']['choices_set']):
                choice_label = cls.choiceMap[c_idx]
                if c['uuid'] in my_choice_answer:
                    my_choice_answer_label.append(choice_label)
            data['user_answer'] = ','.join(my_choice_answer_label)
        elif q_item['q_type'] == 2:
            # TODO 主观题存储的是图片，需要处理
            data['user_answer'] = str(my_answer['answer']) if my_answer else ''
        elif q_item['q_type'] == 6:
            # TODO 填空题需要重新处理
            data['user_answer'] = str(my_answer['answer']) if my_answer else ''
        else:
            data['user_answer'] = str(my_answer['answer']) if my_answer else ''

        return data

    @classmethod
    def _fill_complex_question_answer(cls, q_item, answer_question):
        # 处理问题mapping
        sub_answer_questions_mapping = {}
        answer_question_info = answer_question['question']
        for s_q in answer_question_info.get('materialchoice_set', []):
            sub_answer_questions_mapping[str(s_q['id'])] = s_q
        for s_q in answer_question_info.get('materialsubjective_set', []):
            sub_answer_questions_mapping[str(s_q['id'])] = s_q
        for s_q in answer_question_info.get('materialblank_set', []):
            sub_answer_questions_mapping[str(s_q['id'])] = s_q

        sub_questions = q_item['sub_questions']
        sub_question_answers = []
        for s_q in sub_questions:
            sub_answer_question = sub_answer_questions_mapping.get(s_q['q_id'])
            if not sub_answer_question:
                continue
            sub_question_answers.append(cls._fill_complex_sub_question_answer(s_q, sub_answer_question))
        return sub_question_answers

    @classmethod
    def _fill_complex_sub_question_answer(cls, q_item, answer_question):
        data = {
            # 0答错，1答对，2未作答，3未批阅
            'q_id': q_item['q_id'],
            'user_answer_status': answer_question['my_answer']['result'] if answer_question['my_answer'] else 2,
            'user_answer': '',
        }
        my_answer = answer_question['my_answer']
        if q_item['q_type'] in [0, 1]:
            my_choice_answer = my_answer['answer'] if my_answer else []
            my_choice_answer_label = []
            for c_idx, c in enumerate(answer_question['mchoices_set']):
                choice_label = cls.choiceMap[c_idx]
                if c['uuid'] in my_choice_answer:
                    my_choice_answer_label.append(choice_label)
            data['user_answer'] = ','.join(my_choice_answer_label)
        elif q_item['q_type'] == 2:
            data['user_answer'] = my_answer['answer'] if my_answer else ''
        elif q_item['q_type'] == 6:
            # TODO 填空题需要重新处理
            data['user_answer'] = str(my_answer['answer']) if my_answer else ''

        return data
