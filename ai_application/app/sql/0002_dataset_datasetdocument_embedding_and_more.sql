--
-- Create model Dataset
--
CREATE TABLE `ai_dataset`
(
    `id`                       bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`                 datetime(6) NOT NULL,
    `modified_time`            datetime(6) NOT NULL,
    `is_deleted`               bool         NOT NULL,
    `dataset_no`               varchar(36)  NOT NULL UNIQUE,
    `name`                     varchar(100) NOT NULL,
    `description`              longtext     NOT NULL,
    `index_struct`             json NULL,
    `embedding_model_provider` varchar(100) NOT NULL,
    `embedding_model`          varchar(100) NOT NULL,
    `account_id`               bigint       NOT NULL
);
--
-- Create model DatasetDocument
--
CREATE TABLE `ai_dataset_document`
(
    `id`                     bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`               datetime(6) NOT NULL,
    `modified_time`          datetime(6) NOT NULL,
    `is_deleted`             bool             NOT NULL,
    `document_no`            varchar(36)      NOT NULL UNIQUE,
    `name`                   varchar(255)     NOT NULL,
    `data_source_type`       varchar(40)      NOT NULL,
    `data_source_info`       json NULL,
    `processing_started_at`  datetime(6) NULL,
    `word_count`             integer          NOT NULL,
    `parsing_completed_at`   datetime(6) NULL,
    `splitting_completed_at` datetime(6) NULL,
    `tokens`                 integer          NOT NULL,
    `indexing_latency`       double precision NOT NULL,
    `completed_at`           datetime(6) NULL,
    `error`                  longtext         NOT NULL,
    `stopped_at`             datetime(6) NULL,
    `indexing_status`        varchar(40)      NOT NULL,
    `enabled`                bool             NOT NULL,
    `doc_type`               varchar(40)      NOT NULL,
    `doc_metadata`           json NULL,
    `doc_language`           varchar(40)      NOT NULL,
    `dataset_id`             bigint           NOT NULL
);
--
-- Create model Embedding
--
CREATE TABLE `ai_embeddings`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `provider_name` varchar(100) NOT NULL,
    `model_name`    varchar(100) NOT NULL,
    `text_hash`     varchar(64)  NOT NULL,
    `embedding`     longblob NULL
);
--
-- Add field only_use_dataset to conversation
--
ALTER TABLE `ai_conversation`
    ADD COLUMN `only_use_dataset` bool DEFAULT b'0' NOT NULL;
--
-- Add field is_query_rewrite to message
--
ALTER TABLE `ai_message`
    ADD COLUMN `is_query_rewrite` bool DEFAULT b'0' NOT NULL;
--
-- Create model RagMessage
--
CREATE TABLE `ai_rag_message`
(
    `id`                    bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`              datetime(6) NOT NULL,
    `modified_time`         datetime(6) NOT NULL,
    `is_deleted`            bool             NOT NULL,
    `content`               json NULL,
    `answer`                longtext NULL,
    `message_tokens`        integer          NOT NULL,
    `answer_tokens`         integer          NOT NULL,
    `latency`               double precision NOT NULL,
    `is_model_rating`       bool             NOT NULL,
    `model_score`           integer          NOT NULL,
    `direct_content`        json NULL,
    `direct_answer`         longtext NULL,
    `direct_message_tokens` integer          NOT NULL,
    `direct_answer_tokens`  integer          NOT NULL,
    `direct_latency`        double precision NOT NULL,
    `conversation_id`       bigint           NOT NULL,
    `message_id`            bigint           NOT NULL
);
--
-- Create model MessageTracing
--
CREATE TABLE `ai_message_tracing`
(
    `id`             bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`       datetime(6) NOT NULL,
    `modified_time`  datetime(6) NOT NULL,
    `is_deleted`     bool             NOT NULL,
    `type`           varchar(32)      NOT NULL,
    `query`          longtext NULL,
    `content`        json NULL,
    `answer`         longtext NULL,
    `message_tokens` integer          NOT NULL,
    `answer_tokens`  integer          NOT NULL,
    `total_tokens`   integer          NOT NULL,
    `latency`        double precision NOT NULL,
    `message_id`     bigint           NOT NULL
);
--
-- Create model MessageQueryRewrite
--
CREATE TABLE `ai_message_query_rewrite`
(
    `id`              bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`        datetime(6) NOT NULL,
    `modified_time`   datetime(6) NOT NULL,
    `is_deleted`      bool   NOT NULL,
    `chat_history`    json NULL,
    `origin_query`    longtext NULL,
    `prompt`          longtext NULL,
    `new_query`       longtext NULL,
    `conversation_id` bigint NOT NULL,
    `message_id`      bigint NOT NULL
);
--
-- Create model DocumentSegment
--
CREATE TABLE `ai_document_segments`
(
    `id`                  bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`            datetime(6) NOT NULL,
    `modified_time`       datetime(6) NOT NULL,
    `is_deleted`          bool         NOT NULL,
    `position`            integer      NOT NULL,
    `content`             longtext     NOT NULL,
    `word_count`          integer      NOT NULL,
    `tokens`              integer      NOT NULL,
    `index_node_id`       varchar(255) NOT NULL,
    `index_node_hash`     varchar(255) NOT NULL,
    `hit_count`           integer      NOT NULL,
    `status`              varchar(40)  NOT NULL,
    `indexing_at`         datetime(6) NULL,
    `completed_at`        datetime(6) NULL,
    `error`               longtext NULL,
    `stopped_at`          datetime(6) NULL,
    `dataset_id`          bigint       NOT NULL,
    `dataset_document_id` bigint       NOT NULL
);
--
-- Create model DatasetRetrieverResource
--
CREATE TABLE `ai_dataset_retriever_resources`
(
    `id`                    bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`              datetime(6) NOT NULL,
    `modified_time`         datetime(6) NOT NULL,
    `is_deleted`            bool         NOT NULL,
    `position`              integer      NOT NULL,
    `dataset_name`          varchar(100) NOT NULL,
    `dataset_document_name` varchar(255) NOT NULL,
    `data_source_type`      varchar(40)  NOT NULL,
    `score`                 double precision NULL,
    `content`               longtext NULL,
    `content_with_context`  json NULL,
    `dataset_id`            bigint       NOT NULL,
    `dataset_document_id`   bigint       NOT NULL,
    `message_id`            bigint       NOT NULL,
    `segment_id`            bigint       NOT NULL
);
--
-- Create model DatasetQuery
--
CREATE TABLE `ai_dataset_queries`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool     NOT NULL,
    `content`       longtext NOT NULL,
    `app_id`        bigint   NOT NULL,
    `dataset_id`    bigint   NOT NULL,
    `message_id`    bigint   NOT NULL
);
--
-- Create model DatasetProcessRule
--
CREATE TABLE `ai_dataset_process_rule`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool   NOT NULL,
    `rules`         json NULL,
    `dataset_id`    bigint NOT NULL
);
--
-- Add field dataset_process_rule to datasetdocument
--
ALTER TABLE `ai_dataset_document`
    ADD COLUMN `dataset_process_rule_id` bigint NOT NULL;
--
-- Add field dataset to conversation
--
ALTER TABLE `ai_conversation`
    ADD COLUMN `dataset_id` bigint NULL;
CREATE INDEX `ai_dataset_add_time_7d46801c` ON `ai_dataset` (`add_time`);
CREATE INDEX `ai_dataset_account_id_1e02a219` ON `ai_dataset` (`account_id`);
CREATE INDEX `ai_dataset_document_add_time_9920f757` ON `ai_dataset_document` (`add_time`);
CREATE INDEX `ai_dataset_document_dataset_id_42da634f` ON `ai_dataset_document` (`dataset_id`);
CREATE INDEX `ai_embeddings_add_time_22e91c55` ON `ai_embeddings` (`add_time`);
CREATE INDEX `ai_embeddings_text_hash_b98415f7` ON `ai_embeddings` (`text_hash`);
CREATE INDEX `ai_rag_message_add_time_5cc15a8c` ON `ai_rag_message` (`add_time`);
CREATE INDEX `ai_rag_message_conversation_id_05d4199c` ON `ai_rag_message` (`conversation_id`);
CREATE INDEX `ai_rag_message_message_id_920b7225` ON `ai_rag_message` (`message_id`);
CREATE INDEX `ai_message_tracing_add_time_cbaccc6c` ON `ai_message_tracing` (`add_time`);
CREATE INDEX `ai_message_tracing_message_id_59870649` ON `ai_message_tracing` (`message_id`);
CREATE INDEX `ai_message_query_rewrite_add_time_aad1cc72` ON `ai_message_query_rewrite` (`add_time`);
CREATE INDEX `ai_message_query_rewrite_conversation_id_37a99330` ON `ai_message_query_rewrite` (`conversation_id`);
CREATE INDEX `ai_message_query_rewrite_message_id_fc9028c0` ON `ai_message_query_rewrite` (`message_id`);
CREATE INDEX `ai_document_segments_add_time_9218ec18` ON `ai_document_segments` (`add_time`);
CREATE INDEX `ai_document_segments_dataset_id_7e8aa9ff` ON `ai_document_segments` (`dataset_id`);
CREATE INDEX `ai_document_segments_dataset_document_id_c38cacae` ON `ai_document_segments` (`dataset_document_id`);
CREATE INDEX `ai_dataset_retriever_resources_add_time_8aa30f0a` ON `ai_dataset_retriever_resources` (`add_time`);
CREATE INDEX `ai_dataset_retriever_resources_dataset_id_7338364d` ON `ai_dataset_retriever_resources` (`dataset_id`);
CREATE INDEX `ai_dataset_retriever_resources_dataset_document_id_e6b740d4` ON `ai_dataset_retriever_resources` (`dataset_document_id`);
CREATE INDEX `ai_dataset_retriever_resources_message_id_ecbf3cf5` ON `ai_dataset_retriever_resources` (`message_id`);
CREATE INDEX `ai_dataset_retriever_resources_segment_id_22395480` ON `ai_dataset_retriever_resources` (`segment_id`);
CREATE INDEX `ai_dataset_queries_add_time_1409c7d0` ON `ai_dataset_queries` (`add_time`);
CREATE INDEX `ai_dataset_queries_app_id_de05bf01` ON `ai_dataset_queries` (`app_id`);
CREATE INDEX `ai_dataset_queries_dataset_id_d7d00016` ON `ai_dataset_queries` (`dataset_id`);
CREATE INDEX `ai_dataset_queries_message_id_f36b691b` ON `ai_dataset_queries` (`message_id`);
CREATE INDEX `ai_dataset_process_rule_add_time_427c30ea` ON `ai_dataset_process_rule` (`add_time`);
CREATE INDEX `ai_dataset_process_rule_dataset_id_ec1b1630` ON `ai_dataset_process_rule` (`dataset_id`);
CREATE INDEX `ai_dataset_document_dataset_process_rule_id_d5dae4f0` ON `ai_dataset_document` (`dataset_process_rule_id`);
CREATE INDEX `ai_conversation_dataset_id_e052c552` ON `ai_conversation` (`dataset_id`);