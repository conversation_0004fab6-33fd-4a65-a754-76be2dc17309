import pandas as pd
import numpy as np
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()
from app.models import ExamAnalysisKnowledgePointWithStats

# ---------------------------
# 1) Data loading (same schema you already use)
# ---------------------------
def load_knowledge_points():
    """
    从数据库读取知识点数据 -> DataFrame
    需要字段: subject, knowledge_point, total_freq, avg_difficulty
    其余字段可留着但此简化方案不会使用。
    """
    data = {
        'subject': [],
        'knowledge_point': [],
        'total_freq': [],
        'avg_difficulty': [],
        'mcq_freq': [],
        'mcq_difficulty': [],
        'subjective_freq': [],
        'subjective_difficulty': []
    }
    subjects = ['计算机组成原理', '计算机网络', '操作系统', '数据结构']
    for subject in subjects:
        qs = ExamAnalysisKnowledgePointWithStats.objects.filter(subject=subject).values()
        for item in qs:
            data['subject'].append(item["subject"])
            data['knowledge_point'].append(item["point_name"])
            data['total_freq'].append(item["exam_count"])
            data['avg_difficulty'].append(item["avg_difficulty"])
            data['mcq_freq'].append(item.get("choice_count", 0))
            data['mcq_difficulty'].append(item.get("choice_avg_difficulty", 0.0))
            data['subjective_freq'].append(item.get("comprehensive_count", 0))
            data['subjective_difficulty'].append(item.get("comprehensive_avg_difficulty", 0.0))
    return pd.DataFrame(data)


# ---------------------------
# 2) Simplified knowledge-point prioritizer
# ---------------------------
class KnowledgePointPrioritizer:
    """
    使用“科目内 z-score”的 total_freq 与 avg_difficulty 进行跨科目统一比较，
    生成学习优先级（越大越优先）。
    priority = alpha * z_total_freq - beta * z_avg_difficulty
    """
    def __init__(self, df: pd.DataFrame, alpha: float = 0.7, beta: float = 0.3,
                 subject_col: str = 'subject'):
        self.df = df.copy()
        self.alpha = float(alpha)
        self.beta = float(beta)
        self.subject_col = subject_col
        self._validate()
        self._compute_groupwise_zscores()
        self._compute_priority()

    def _validate(self):
        needed = {'subject', 'knowledge_point', 'total_freq', 'avg_difficulty'}
        missing = needed - set(self.df.columns)
        if missing:
            raise ValueError(f"Missing required columns: {missing}")
        # 类型安全：转数值，无法转的当作 NaN 再填充
        self.df['total_freq'] = pd.to_numeric(self.df['total_freq'], errors='coerce').fillna(0)
        self.df['avg_difficulty'] = pd.to_numeric(self.df['avg_difficulty'], errors='coerce').fillna(0)

    @staticmethod
    def _zscore_safe(x: pd.Series) -> pd.Series:
        mu = x.mean()
        sigma = x.std(ddof=0)  # population std; use ddof=1 if you prefer sample std
        if sigma == 0 or np.isnan(sigma):
            return pd.Series(np.zeros(len(x)), index=x.index)
        return (x - mu) / sigma

    def _compute_groupwise_zscores(self):
        # 科目内 z-score
        self.df['z_total_freq'] = self.df.groupby(self.subject_col)['total_freq'].transform(self._zscore_safe)
        self.df['z_avg_difficulty'] = self.df.groupby(self.subject_col)['avg_difficulty'].transform(self._zscore_safe)

    def _compute_priority(self):
        # 越常考(高 z_total_freq)越优先；越难(高 z_avg_difficulty)越靠后
        self.df['priority'] = self.alpha * self.df['z_total_freq'] - self.beta * self.df['z_avg_difficulty']
        # 提供一个标签，便于解释
        self.df['tag'] = np.select(
            [
                (self.df['z_total_freq'] >= 0.5) & (self.df['z_avg_difficulty'] <= 0),
                (self.df['z_total_freq'] >= 0.5) & (self.df['z_avg_difficulty'] > 0),
                (self.df['z_total_freq'] < 0.5) & (self.df['z_avg_difficulty'] <= 0),
            ],
            ['Quick Win(高频/不难)', 'Must Cover(高频/偏难)', 'Fillers(低频/不难)'],
            default='Stretch(低频/偏难)'
        )

    def recommend_global(self, top_k: int = 30) -> pd.DataFrame:
        """
        返回全局前 top_k 的知识点。
        """
        out_cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'priority', 'tag'
        ]
        return (self.df.sort_values('priority', ascending=False)
                    .loc[:, out_cols]
                    .head(top_k)
                    .reset_index(drop=True))

    def recommend_per_subject(self, k_per_subject: int = 5) -> pd.DataFrame:
        """
        每个科目各取前 k 个知识点。
        """
        def topk(g):
            return g.sort_values('priority', ascending=False).head(k_per_subject)
        out_cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'priority', 'tag'
        ]
        return (self.df.groupby(self.subject_col, group_keys=False)
                    .apply(topk)
                    .loc[:, out_cols]
                    .reset_index(drop=True))


# ---------------------------
# 3) Run
# ---------------------------
if __name__ == '__main__':
    # 读取数据
    kp_df = load_knowledge_points()

    # 你可以调参：alpha 越大越强调“考频”；beta 越大越惩罚“难度”
    prioritizer = KnowledgePointPrioritizer(kp_df, alpha=0.7, beta=0.3)

    print("="*50)
    print("全局学习优先级 Top-30（越上越该学）")
    print("="*50)
    top_global = prioritizer.recommend_global(top_k=30)
    print(top_global.to_string(index=False))

    print("\n" + "="*50)
    print("各科目 Top-5 建议项")
    print("="*50)
    top_by_subject = prioritizer.recommend_per_subject(k_per_subject=5)
    # 为了阅读友好，按 subject 分块输出
    for subj, g in top_by_subject.groupby('subject'):
        print(f"\n【{subj}】")
        print(g.drop(columns=['subject']).to_string(index=False))
