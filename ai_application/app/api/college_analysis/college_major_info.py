import requests
import json

from django.conf import settings


def extract_college_major_info(school_code,major_code):
    if settings.ENVIRONMENT == settings.ENV_PRODUCT:
        host = 'http://yantucs-data-svc:8000'
    else:
        host = 'https://yantucs-data.yantucs.com'
    url = f"{host}/internal_api/v1/college/kaoyan_learn_query/"

    payload = json.dumps({
        "school_code": school_code,
        "year": "2024",
        "major_code": major_code
    })
    headers = {
        'Content-Type': 'application/json',
    }

    response = requests.post(url, headers=headers, data=payload)

    try:
        result = response.json()
        if 'data' in result and isinstance(result['data'], list):
            return result['data']
        else:
            print("Data field is missing or not a list.")
            return []
    except json.JSONDecodeError:
        print("Failed to decode JSON from response.")
        return []

def extract_college_introduction_info(school_code):
    if settings.ENVIRONMENT == settings.ENV_PRODUCT:
        host = 'http://yantucs-data-svc:8000'
    else:
        host = 'https://yantucs-data.yantucs.com'
    url = f"{host}/internal_api/v1/college/school_list/?name&code={school_code}&level"

    payload = {}
    headers = {
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Accept': '*/*',
        'Host': 'yantucs-data.yantucs.com',
        'Connection': 'keep-alive',
        'Cookie': 'acw_tc=1a142b5e17525749793097655e05ffafc474d04fb7bba25bd684b905c13a57'
    }

    response = requests.request("GET", url, headers=headers, data=payload)

    try:
        result = response.json()
        if 'data' in result and isinstance(result['data'], list) and len(result['data']) > 0:
            return result['data'][0].get('introduction', '')
        else:
            print("Data field is missing or not a list.")
            return ''
    except json.JSONDecodeError:
        print("Failed to decode JSON from response.")
        return ''