--
-- Remove field recite_words from enwordreciteplan
--
ALTER TABLE `ai_english_word_recite_plan` DROP COLUMN `recite_words`;
--
-- Remove field recite_words from enwordreciteplanrecord
--
ALTER TABLE `ai_english_word_recite_plan_record` DROP COLUMN `recite_words`;
--
-- Alter field day on enwordrecitedayrecord
--
ALTER TABLE `ai_english_word_recite_day_record`
    ALTER COLUMN `day` SET DEFAULT '2025-04-02';
ALTER TABLE `ai_english_word_recite_day_record` MODIFY `day` date NOT NULL;
ALTER TABLE `ai_english_word_recite_day_record`
    ALTER COLUMN `day` DROP DEFAULT;
--
-- Create model EnWordReciteDayPlan
--
CREATE TABLE `ai_english_word_recite_day_plan`
(
    `id`             bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`       datetime(6) NOT NULL,
    `modified_time`  datetime(6) NOT NULL,
    `is_deleted`     bool   NOT NULL,
    `day`            date   NOT NULL,
    `recite_words`   json NULL,
    `plan_id`        bigint NOT NULL,
    `plan_record_id` bigint NOT NULL
);
--
-- Add field day_plan to enwordrecitedayrecord
--
ALTER TABLE `ai_english_word_recite_day_record`
    ADD COLUMN `day_plan_id` bigint NULL;
CREATE INDEX `ai_english_word_recite_day_plan_add_time_221983b0` ON `ai_english_word_recite_day_plan` (`add_time`);
CREATE INDEX `ai_english_word_recite_day_plan_plan_id_e72d5ff2` ON `ai_english_word_recite_day_plan` (`plan_id`);
CREATE INDEX `ai_english_word_recite_day_plan_plan_record_id_7f61675e` ON `ai_english_word_recite_day_plan` (`plan_record_id`);
CREATE INDEX `ai_english_word_recite_day_record_day_plan_id_f227c816` ON `ai_english_word_recite_day_record` (`day_plan_id`);