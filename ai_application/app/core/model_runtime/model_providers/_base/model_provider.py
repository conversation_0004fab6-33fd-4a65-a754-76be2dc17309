import os
from abc import ABC

from app.core.helper.module_import_helper import import_module_from_source, get_subclasses_from_module
from app.core.model_runtime.entities.provider_entities import ProviderEntity, ModelType, AIModelEntity
from app.core.model_runtime.model_providers._base.ai_model import AIModel


class ModelProvider(ABC):
    provider_schema: ProviderEntity | None = None
    model_instance_map: dict[str, AIModel] = {}

    def get_provider_schema(self) -> ProviderEntity:
        if self.provider_schema:
            return self.provider_schema

        # get dirname of the current path
        provider_name = self.__class__.__module__.split('.')[-1]

        self.provider_schema = ProviderEntity(provider=provider_name)
        return self.provider_schema

    def models(self, model_type: ModelType) -> list[AIModelEntity]:
        model_instance = self.get_model_instance(model_type)

        # get predefined models (predefined_models)
        models = model_instance.predefined_models()

        # return models
        return models

    def get_model_instance(self, model_type: ModelType) -> AIModel:
        """
        Get model instance

        :param model_type: model type defined in `ModelType`
        :return:
        """
        # get dirname of the current path
        provider_name = self.__class__.__module__.split(".")[-1]

        if f"{provider_name}.{model_type.value}" in self.model_instance_map:
            return self.model_instance_map[f"{provider_name}.{model_type.value}"]

        # get the path of the model type classes
        base_path = os.path.abspath(__file__)
        model_type_name = model_type.value.replace('-', '_')
        model_type_path = os.path.join(os.path.dirname(os.path.dirname(base_path)), provider_name, model_type_name)
        model_type_py_path = os.path.join(model_type_path, f'{model_type_name}.py')

        if not os.path.isdir(model_type_path) or not os.path.exists(model_type_py_path):
            raise Exception(f'Invalid model type {model_type} for provider {provider_name}')

        # Dynamic loading {model_type_name}.py file and find the subclass of AIModel
        parent_module = '.'.join(self.__class__.__module__.split('.')[:-1])
        mod = import_module_from_source(
            module_name=f"{parent_module}.{model_type_name}.{model_type_name}", py_file_path=model_type_py_path
        )

        model_class = next(
            filter(
                lambda x: x.__module__ == mod.__name__ and not x.__abstractmethods__,
                get_subclasses_from_module(mod, AIModel),
            ),
            None,
        )
        if not model_class:
            raise Exception(f"Missing AIModel Class for model type {model_type} in {model_type_py_path}")

        model_instance_map = model_class()
        self.model_instance_map[f"{provider_name}.{model_type.value}"] = model_instance_map

        return model_instance_map
