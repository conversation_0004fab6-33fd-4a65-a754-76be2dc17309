import os
import traceback
from celery import shared_task
from django.conf import settings

from app.models.document_proofreader import DocumentProofreader, DocumentProofreaderError, DocumentProofreaderAsyncTask
from app.services.document_proofreader.document_proofreader_fun import proofread_document_file
from app.services.document_proofreader.parsers.markdown_to_docx import convert_markdown_to_docx
import logging


@shared_task(bind=True, max_retries=3, queue='doc_proofreader_check')
def async_document_proofreader_task(self, task_id, file_name, original_content, file_extension='.md', dictionary=None, convert=None, subject_category='通用'):
    """
    异步文档校对任务
    
    Args:
        task_id: 任务ID
        file_name: 文件名
        original_content: 原始内容  
        file_extension: 文件扩展名
        dictionary: 词库列表
    """
    logging.info(f"📚 开始异步校对任务，任务ID: {task_id}")
    
    try:
        # 获取任务记录
        async_task = DocumentProofreaderAsyncTask.objects.get(task_id=task_id)
        # 标记为处理中
        async_task.mark_processing()
        logging.info(f"📊 任务状态更新为处理中: {task_id}")
        
        # 创建校对记录
        proofreader = DocumentProofreader.objects.create(
            file_name=file_name,
            original_content=original_content,
            status='processing'
        )
        
        # 执行校对，传入词库
        corrected_text, labeled_text, result_dict = proofread_document_file(
            original_content,
            progress_callback=lambda current, total: logging.info(f"📊 校对进度: {current}/{total}"),
            dictionary=dictionary,    # 传入词库
            conversion_rules=convert,  # 传入转换规则
            subject_category=subject_category,  # 传入目标审校类别
            proofreader_id=proofreader.id,
        )
        
        # 更新校对记录
        proofreader.corrected_content = corrected_text
        proofreader.labeled_content = labeled_text
        proofreader.status = 'completed'
        proofreader.save()
        
        # 保存错误列表
        error_list = result_dict.get('error_list', [])
        for error in error_list:
            DocumentProofreaderError.objects.create(
                proofreader=proofreader,
                error_id=error.get('id', ''),
                error_text=error.get('error_text', ''),
                error_reason=error.get('error_reason', ''),
                error_suggestion=error.get('error_suggestion', ''),
                error_type=error.get('error_type', ''),
            )
        
        # 创建任务目录并保存文件
        original_filename_without_ext = os.path.splitext(file_name)[0]
        download_filename_md = f"{original_filename_without_ext}_corrected.md"
        download_filename_docx = f"{original_filename_without_ext}_corrected.docx"
        
        original_filename_safe = "".join([c for c in file_name if c.isalnum() or c in (' ', '.', '_')]).rstrip()
        task_dir_name = f'proofreader_task_{proofreader.id}_{original_filename_safe}'
        
        task_directory = os.path.join(settings.MEDIA_ROOT, 'document_proofreader', task_dir_name)
        os.makedirs(task_directory, exist_ok=True)
        
        # 保存MD文件
        md_file_path = os.path.join(task_directory, download_filename_md)
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write(corrected_text)
        
        # 转换并保存Word文档
        docx_file_path = os.path.join(task_directory, download_filename_docx)
        convert_markdown_to_docx(corrected_text, docx_file_path)
        
        # 更新任务目录
        proofreader.task_directory = task_dir_name
        proofreader.save()
        
        # 标记任务成功
        async_task.mark_success(proofreader.id)
        
        logging.info(f"✅ 异步校对任务完成！任务ID: {task_id}, 校对ID: {proofreader.id}")
        
        return {
            'status': 'success',
            'proofreader_id': proofreader.id,
            'task_id': task_id,
            'message': '校对完成'
        }
        
    except DocumentProofreaderAsyncTask.DoesNotExist:
        error_msg = f"任务记录不存在: {task_id}"
        logging.error(f"❌ {error_msg}")
        return {'status': 'error', 'message': error_msg}
        
    except Exception as e:
        error_msg = f"校对失败: {str(e)}"
        logging.error(f"❌ 异步校对任务失败: {error_msg}")
        logging.error(traceback.format_exc())
        
        try:
            # 更新任务状态为失败
            async_task = DocumentProofreaderAsyncTask.objects.get(task_id=task_id)
            async_task.mark_failed(error_msg)
            
            # 如果可以重试，则重试
            if async_task.can_retry():
                logging.info(f"🔄 任务将重试，当前重试次数: {async_task.retry_times}")
                raise self.retry(countdown=60 * async_task.retry_times)  # 递增重试间隔
                
        except DocumentProofreaderAsyncTask.DoesNotExist:
            pass
        
        return {'status': 'error', 'message': error_msg}


@shared_task(queue='doc_proofreader_check')
def check_async_proofreader_tasks():
    """
    检查异步校对任务状态
    定时任务，每10秒执行一次，用于监控任务状态变化
    """
    logging.info("🔍 开始检查异步校对任务状态...")
    
    # 查找已完成但未标记处理完成的任务
    completed_tasks = DocumentProofreaderAsyncTask.objects.filter(
        status__in=['success', 'failed'],
        notification_sent=False,
        is_deleted=False
    )
    
    processed_count = 0
    
    for task in completed_tasks:
        try:
            # 记录任务完成状态
            if task.status == 'success':
                logging.info(f"✅ 任务成功完成: {task.file_name} (ID: {task.task_id})")
                if task.proofreader:
                    error_count = task.proofreader.documentproofreadererror_set.filter(is_deleted=False).count()
                    logging.info(f"   📊 发现 {error_count} 个错误")
            else:
                logging.error(f"❌ 任务处理失败: {task.file_name} (ID: {task.task_id})")
                logging.error(f"   💡 失败原因: {task.fail_reason}")
                if task.can_retry():
                    logging.info(f"   🔄 可重试，当前重试次数: {task.retry_times}")
            
            # 标记为已处理（不再发送通知）
            task.notification_sent = True
            task.save(update_fields=['notification_sent', 'modified_time'])
            
            processed_count += 1
            
        except Exception as e:
            logging.error(f"❌ 处理任务状态失败: {task.file_name}, 错误: {str(e)}")
    
    # 统计当前任务状态
    stats = {
        'pending': DocumentProofreaderAsyncTask.objects.filter(is_deleted=False, status='pending').count(),
        'processing': DocumentProofreaderAsyncTask.objects.filter(is_deleted=False, status='processing').count(),
        'success': DocumentProofreaderAsyncTask.objects.filter(is_deleted=False, status='success').count(),
        'failed': DocumentProofreaderAsyncTask.objects.filter(is_deleted=False, status='failed').count(),
    }
    
    if processed_count > 0:
        logging.info(f"✅ 本轮处理了 {processed_count} 个任务状态更新")
    
    logging.info(f"📊 当前任务统计: 等待={stats['pending']}, 处理中={stats['processing']}, 成功={stats['success']}, 失败={stats['failed']}")
    
    return {
        'processed_count': processed_count,
        'stats': stats
    }