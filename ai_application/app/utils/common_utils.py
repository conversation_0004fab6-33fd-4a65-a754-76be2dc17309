import json
from hashlib import sha256
from typing import Any


def filter_none_params(params: dict) -> dict:
    new_params = {}
    for k, v in params.items():
        if k is not None:
            new_params[k] = v
    return new_params


def json_dumps(o):
    return json.dumps(o, ensure_ascii=False)


def validate_float(value: Any):
    try:
        float_value = float(value)
        if isinstance(float_value, float):
            return float_value
    except ValueError:
        return None


def validate_int(value: Any):
    try:
        int_value = int(value)
        if isinstance(int_value, int):
            return int_value
    except ValueError:
        return None


def generate_text_hash(text: str) -> str:
    hash_text = str(text) + 'None'
    return sha256(hash_text.encode()).hexdigest()
