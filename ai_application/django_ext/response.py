from typing import Generator

from django.conf import settings
from django.http import StreamingHttpResponse
from rest_framework.response import Response

from django_ext.exceptions import error_code


def res_msg(code, data=None, msg=None, detail=None, internal_err=None):
    result = {
        'code': int(code),
        'msg': msg if msg else error_code.get(str(code), '系统错误'),
    }
    if data is not None:
        result['data'] = data
    if detail is not None:
        result['detail'] = detail

    if internal_err is not None and getattr(settings, 'ENVIRONMENT', 0) != 2:
        result['internal_err'] = internal_err

    return result


def make_response(data=None, **kwargs):
    data = res_msg(0, data=data)

    response = Response(data=data, **kwargs)
    return response


def make_stream_response(response: Generator):
    stream_response = StreamingHttpResponse(response, content_type='text/event-stream; charset=utf-8')
    stream_response['Cache-Control'] = 'no-cache'
    return stream_response

