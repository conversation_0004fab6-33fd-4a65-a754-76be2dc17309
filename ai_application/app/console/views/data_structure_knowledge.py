from django.views.generic import TemplateView
from django.views import View
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from app.models.data_structure_knowledge import DataStructureKnowledge
from app.models.knowledge_simple import KnowledgeSimple
from app.models.knowledge_video import KnowledgeVideo
from app.tasks.text2video_task import delay_process_know_video_task
from app.models.shuati import STQuestion, STKnowledge, STQuestionKnowledge
from django.db.models import Count
import json


class DataStructureKnowledgeView(TemplateView):
    template_name = "app/data_structure_knowledge.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        data = DataStructureKnowledge.objects.filter(is_deleted=0).order_by('id')

        subject_1_dict = {}
        subject_2_map = {}

        for item in data:
            s1 = item.subject_1
            s2 = item.subject_2
            cat = item.categories
            kp = item.knowledge_points

            if s1 not in subject_1_dict:
                subject_1_dict[s1] = []
            if s2 not in subject_1_dict[s1]:
                subject_1_dict[s1].append(s2)

            if s2 not in subject_2_map:
                subject_2_map[s2] = {}
            if cat not in subject_2_map[s2]:
                subject_2_map[s2][cat] = []
            subject_2_map[s2][cat].append({'kp': kp, 'has_video': bool(item.video)})

        # 只保留唯一的一级学科及其所有二级学科
        unique_subject_1 = []
        for s1, s2_list in subject_1_dict.items():
            unique_subject_1.append({'name': s1, 'subject_2_list': s2_list})
        context['subject_1_list'] = unique_subject_1
        context['subject_2_map'] = subject_2_map
        return context


@method_decorator(csrf_exempt, name='dispatch')
class AddKnowledgePointView(View):
    def post(self, request):
        data = json.loads(request.body)
        subject_2 = data.get('subject_2')
        category = data.get('categories')
        kp = data.get('knowledge_point')

        subject = DataStructureKnowledge.objects.filter(subject_2=subject_2).first()
        subject_1 = subject.subject_1 if subject else ''

        DataStructureKnowledge.objects.create(
            subject_1=subject_1,
            subject_2=subject_2,
            categories=category,
            knowledge_points=kp
        )
        return JsonResponse({'knowledge_point': kp})


@method_decorator(csrf_exempt, name='dispatch')
class DeleteKnowledgePointView(View):
    def post(self, request):
        data = json.loads(request.body)
        name = data.get('knowledge_point')
        subject_2 = data.get('subject_2')
        category = data.get('categories')
        if not all([name, subject_2, category]):
            return JsonResponse({'deleted': False, 'error': 'Missing parameter'})
        obj = DataStructureKnowledge.objects.filter(
            knowledge_points=name,
            subject_2=subject_2,
            categories=category,
            is_deleted=0
        ).first()
        if obj:
            obj.is_deleted = 1
            obj.save()
            return JsonResponse({'deleted': True})
        else:
            return JsonResponse({'deleted': False, 'error': '知识点不存在'})


@method_decorator(csrf_exempt, name='dispatch')
class GetVideoView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            subject_2 = data.get('subject_2')
            categories = data.get('categories')
            kp = data.get('knowledge_point')
            obj = DataStructureKnowledge.objects.filter(
                subject_2=subject_2,
                categories=categories,
                knowledge_points=kp
            ).first()
            video = obj.video if obj and obj.video else ''
            return JsonResponse({'video': video})
        except Exception as e:
            return JsonResponse({'video': '', 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AddVideoView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            subject_2 = data.get('subject_2', '').strip()
            categories = data.get('categories', '').strip()
            kp = data.get('knowledge_point', '').strip()
            video = data.get('video', '').strip()
            obj = DataStructureKnowledge.objects.filter(
                subject_2__iexact=subject_2,
                categories__iexact=categories,
                knowledge_points__iexact=kp
            ).first()
            if obj:
                obj.video = video
                obj.save()
                return JsonResponse({'success': True})
            else:
                # 打印调试信息
                print(f'知识点未找到: subject_2={subject_2}, categories={categories}, kp={kp}')
                return JsonResponse({'success': False, 'error': '知识点不存在'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class TextToVideoRemindView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            kp_name = data.get('knowledge_point')
            subject_2 = data.get('subject_2')
            print(f"🎬 收到文生视频请求: {subject_2}, {kp_name}")
            
            if not kp_name:
                return JsonResponse({'success': False, 'msg': '知识点名称不能为空'})
            
            # 检查是否已经有视频
            existing_video = KnowledgeVideo.objects.filter(core_course_name=subject_2, knowledge_name=kp_name).first()
            if existing_video:
                print(f"📹 知识点已有视频: {existing_video.video}")
                return JsonResponse({'success': True, 'video_url': existing_video.video})
            else:
                # 如果没有视频，则创建记录
                knowledge = KnowledgeVideo.objects.create(
                    knowledge_name=kp_name,
                    status='not_start',
                    core_course_name=subject_2
                )

            # 生成视频并上传，获取url
            try:
                print(f"🚀 开始生成视频: {kp_name}")
                # delay_process_know_video_task.delay(knowledge.id)
                delay_process_know_video_task(knowledge.id)
                
                # 查找生成的视频
                video_obj = KnowledgeVideo.objects.filter(
                    core_course_name=subject_2, knowledge_name=kp_name).order_by('-id').first()
                video_url = video_obj.video if video_obj else ''
                
                if video_url:
                    print(f"✅ 视频生成成功: {video_url}")
                    return JsonResponse({'success': True, 'video_url': video_url})
                else:
                    print(f"❌ 视频生成失败，未找到视频记录")
                    return JsonResponse({'success': False, 'msg': '视频生成失败，未找到生成的视频'})
                    
            except ImportError as e:
                print(f"❌ 导入错误: {str(e)}")
                return JsonResponse({'success': False, 'msg': f'导入错误: {str(e)}'})
            except Exception as e:
                print(f"❌ 视频生成异常: {str(e)}")
                import traceback
                traceback.print_exc()
                return JsonResponse({'success': False, 'msg': f'视频生成失败: {str(e)}'})
                
        except Exception as e:
            print(f"❌ 请求处理异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return JsonResponse({'success': False, 'msg': f'请求处理失败: {str(e)}'})


@method_decorator(csrf_exempt, name='dispatch')
class QuestionTypeDifficultyOverviewView(View):
    def post(self, request):
        try:
            data = json.loads(request.body or '{}')
            subject_2 = (data.get('subject_2') or '').strip()

            # 仅统计单选题与主观题
            valid_types = [STQuestion.Type.SINGLE, STQuestion.Type.SUBJECTIVE]
            qs = STQuestion.objects.filter(
                is_deleted=False,
                question_type__in=valid_types,
            )
            if subject_2:
                qs = qs.filter(core_course_name=subject_2)

            # 总数与类型总数
            total = qs.count()
            type_totals = (
                qs.values('question_type')
                .annotate(cnt=Count('id'))
            )
            totals_map = {i['question_type']: i['cnt'] for i in type_totals}

            # 难度分布
            dist = (
                qs.values('question_type', 'difficulty')
                .annotate(cnt=Count('id'))
            )
            # 归并到字典，确保难度1~5键存在
            by_diff = {str(int(STQuestion.Type.SINGLE)): {}, str(int(STQuestion.Type.SUBJECTIVE)): {}}
            for d in range(1, 6):
                by_diff[str(int(STQuestion.Type.SINGLE))][str(d)] = 0
                by_diff[str(int(STQuestion.Type.SUBJECTIVE))][str(d)] = 0
            for row in dist:
                qtype = str(row['question_type'])
                diff = str(row['difficulty'])
                if qtype in by_diff:
                    by_diff[qtype][diff] = row['cnt']

            return JsonResponse({
                'total': total,
                'type_totals': {
                    str(int(STQuestion.Type.SINGLE)): totals_map.get(STQuestion.Type.SINGLE, 0),
                    str(int(STQuestion.Type.SUBJECTIVE)): totals_map.get(STQuestion.Type.SUBJECTIVE, 0),
                },
                'by_difficulty': by_diff,
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class QuestionListByTypeDifficultyView(View):
    def post(self, request):
        try:
            data = json.loads(request.body or '{}')
            subject_2 = (data.get('subject_2') or '').strip()
            qtype = int(data.get('question_type'))
            difficulty = int(data.get('difficulty'))

            qs = STQuestion.objects.filter(
                is_deleted=False,
                core_course_name=subject_2,
                question_type=qtype,
                difficulty=difficulty,
            ).order_by('id')

            items = []
            for q in qs:
                content = q.format_question_content or {}
                items.append({
                    'id': q.id,
                    'question_type': q.question_type,
                    'difficulty': q.difficulty,
                    'content': content,
                })

            return JsonResponse({'total': qs.count(), 'items': items})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class QuestionDetailView(View):
    def post(self, request):
        try:
            data = json.loads(request.body or '{}')
            qid = int(data.get('question_id'))
            q = STQuestion.objects.filter(id=qid, is_deleted=False).first()
            if not q:
                return JsonResponse({'error': 'NOT_FOUND'}, status=404)
            return JsonResponse({
                'id': q.id,
                'question_type': q.question_type,
                'difficulty': q.difficulty,
                'content': q.format_question_content or {},
                'analysis': q.analysis or '',
            })
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class QuestionBulkDeleteView(View):
    def post(self, request):
        try:
            data = json.loads(request.body or '{}')
            ids = data.get('question_ids') or []
            if not isinstance(ids, list) or not ids:
                return JsonResponse({'deleted_count': 0})
            # 仅软删除，将 is_deleted 置为 True
            updated = STQuestion.objects.filter(id__in=ids).update(is_deleted=True)
            return JsonResponse({'deleted_count': updated})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class KnowledgePointOverviewView(View):
    """按知识点返回题目数与难度分布（用于“知识点&题目详情”网格）"""
    def post(self, request):
        try:
            data = json.loads(request.body or '{}')
            subject_2 = (data.get('subject_2') or '').strip()

            # 知识点目录：按 id 顺序
            kqs = STKnowledge.objects.filter(is_deleted=False)
            if subject_2:
                kqs = kqs.filter(core_course_name=subject_2)
            kqs = list(kqs.order_by('id').values('id', 'name'))

            if not kqs:
                return JsonResponse({'items': []})

            knowledge_ids = [k['id'] for k in kqs]
            items_map = {
                k['id']: {
                    'id': k['id'],
                    'name': k['name'],
                    'total': 0,
                    'totals_by_type': { '0': 0, '2': 0 },
                    'by_type': {
                        '0': {str(d): 0 for d in range(1, 6)},
                        '2': {str(d): 0 for d in range(1, 6)},
                    },
                }
                for k in kqs
            }

            # 优先使用关联表做统计
            rows = (
                STQuestionKnowledge.objects.filter(
                    knowledge_id__in=knowledge_ids,
                    question__is_deleted=False,
                )
                .filter(question__core_course_name=subject_2) if subject_2 else STQuestionKnowledge.objects.filter(
                    knowledge_id__in=knowledge_ids,
                    question__is_deleted=False,
                )
            )
            rows = rows.values('knowledge_id', 'question__question_type', 'question__difficulty').annotate(cnt=Count('id'))
            for r in rows:
                kid = r['knowledge_id']
                qtype = str(r['question__question_type'])
                diff = str(r['question__difficulty'])
                cnt = r['cnt']
                if qtype in ('0','2') and diff in items_map[kid]['by_type'][qtype]:
                    items_map[kid]['by_type'][qtype][diff] = cnt
                    items_map[kid]['totals_by_type'][qtype] += cnt
                    items_map[kid]['total'] += cnt

            # 若没有统计到数据，回退到 STQuestion.knowledge_list 统计
            if all(v['total'] == 0 for v in items_map.values()):
                for kid in knowledge_ids:
                    qs = STQuestion.objects.filter(is_deleted=False)
                    if subject_2:
                        qs = qs.filter(core_course_name=subject_2)
                    qs = qs.filter(knowledge_list__contains=[kid])
                    dist = qs.values('question_type', 'difficulty').annotate(cnt=Count('id'))
                    total = 0
                    for d in dist:
                        qtype = str(d['question_type'])
                        diff = str(d['difficulty'])
                        cnt = d['cnt']
                        if qtype in ('0','2') and diff in items_map[kid]['by_type'][qtype]:
                            items_map[kid]['by_type'][qtype][diff] = cnt
                            items_map[kid]['totals_by_type'][qtype] += cnt
                            total += cnt
                    items_map[kid]['total'] = total

            items = [items_map[k['id']] for k in kqs]
            return JsonResponse({'items': items})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class KnowledgeQuestionListView(View):
    """按知识点筛选题目（可选题型、难度）"""
    def post(self, request):
        try:
            data = json.loads(request.body or '{}')
            subject_2 = (data.get('subject_2') or '').strip()
            knowledge_id = int(data.get('knowledge_id'))
            qtype = data.get('question_type')
            difficulty = data.get('difficulty')

            # 通过关联表筛选题目
            qk = STQuestionKnowledge.objects.filter(knowledge_id=knowledge_id, question__is_deleted=False)
            if subject_2:
                qk = qk.filter(question__core_course_name=subject_2)

            if qtype is not None and qtype != "":
                qk = qk.filter(question__question_type=int(qtype))
            if difficulty is not None and difficulty != "":
                qk = qk.filter(question__difficulty=int(difficulty))

            q_ids = list(qk.values_list('question_id', flat=True))
            qs = STQuestion.objects.filter(id__in=q_ids, is_deleted=False).order_by('id')

            items = []
            for q in qs:
                content = q.format_question_content or {}
                items.append({
                    'id': q.id,
                    'question_type': q.question_type,
                    'difficulty': q.difficulty,
                    'content': content,
                })

            return JsonResponse({'total': qs.count(), 'items': items})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
