import logging

from django.conf import settings

from app.core.rag.models.document import Document
from app.core.rag.vdb.vector_factory import Vector
from app.models import Dataset, DatasetDocument, DocumentSegment

logger = logging.getLogger(__name__)


class RetrievalService:

    @classmethod
    def retrieve(
            cls,
            dataset: Dataset,
            query_documents: list[DatasetDocument],
            query: str,
            top_k: int,
            score_threshold: float | None
    ) -> list[Document]:
        if query_documents:
            if not DocumentSegment.objects.filter(
                is_deleted=False, dataset_document__in=query_documents, status='completed'
            ).exists():
                return []

        if dataset.available_document_count == 0 or dataset.available_segment_count == 0:
            return []

        all_documents = []
        exceptions = []

        cls.embedding_search(
            dataset=dataset,
            query_documents=query_documents,
            query=query,
            top_k=top_k,
            score_threshold=score_threshold,
            all_documents=all_documents,
            exceptions=exceptions
        )

        if exceptions:
            exception_message = ';\n'.join(exceptions)
            logger.error(exception_message)
            raise Exception(settings.RAG_DEFAULT_ANSWER)

        return all_documents

    @classmethod
    def embedding_search(
            cls,
            dataset: Dataset,
            query_documents: list[DatasetDocument],
            query: str,
            top_k: int,
            score_threshold: float | None,
            all_documents: list,
            exceptions: list
    ):
        try:
            vector = Vector(dataset=dataset)
            documents = vector.search_by_vector(
                query,
                document_nos=[d.document_no for d in query_documents],
                search_type='similarity_score_threshold',
                top_k=top_k,
                score_threshold=score_threshold,
                filter={
                    'group_id': [dataset.dataset_no]
                }
            )
            if documents:
                all_documents.extend(documents)
        except Exception as e:
            exceptions.append(str(e))
