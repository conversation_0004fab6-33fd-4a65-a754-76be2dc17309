from typing import Generator

from app.constants.app import AppMode
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.model_runtime.entities.llm_entities import LLMResult, LLMUsage
from app.core.prompt.entities import AssistantPromptMessage
from app.models import PromptTemplate, KnowledgeStore, KnowledgeSimple
from django.db.models import Max


class KnowledgeQuery(BaseFeature):

    def __init__(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            knowledge: str,
            definition: str,
            stream: bool,
            local_prompt_template: PromptTemplate,
            llm_prompt_template: PromptTemplate,
    ):
        self.application_generate_entity = application_generate_entity
        self.knowledge = knowledge
        self.definition = definition
        self.stream = stream
        self.local_prompt_template = local_prompt_template
        self.llm_prompt_template = llm_prompt_template

        # 预处理inputs
        self.user_inputs = application_generate_entity.inputs.copy()
        self.user_inputs['knowledge'] = knowledge
        self.user_inputs['definition'] = definition

        self.is_usable = True
        # 是否通过模型生成
        self.is_llm_generate = False

    def run(self):
        cached_kg_desc = self._get_cached_kg_desc()
        if self.definition:
            prompt_content = self.user_inputs.get('local_prompt')
        else:
            prompt_content = self.user_inputs.get('llm_prompt')
        self.is_llm_generate = False if cached_kg_desc else True

        prompt_template = self._get_prompt_template()
        if cached_kg_desc:
            if self.stream:
                return self._get_message_generation(cached_kg_desc)
            else:
                return LLMResult(
                    model='',
                    message=AssistantPromptMessage(content=cached_kg_desc),
                    usage=LLMUsage.empty_usage(),
                )
        else:
            pre_prompt = prompt_template.assemble_prompt(
                query='',
                inputs=self.user_inputs,
                prompt_content=prompt_content
            )
            model_conf = get_llm_model_config(
                app_model_conf=self.application_generate_entity.model_conf,
                prompt_template=prompt_template
            )
            # 处理成流式返回
            res = query_llm_by_prompt(
                model_conf=model_conf,
                app_mode=AppMode.COMPLETION,
                pre_prompt=pre_prompt,
                query='',
                stream=self.stream
            )
            return res

    def check_desc_usable(self, completion_tokens: int):
        # 不是模型生成则默认可用
        if self.is_llm_generate:
            prompt_template = self._get_prompt_template()
            # 判断详解输出是否完整，目前采用token的差距在一定的阈值内
            is_saved = True
            max_tokens = prompt_template.model_params.get('max_tokens')
            if max_tokens and abs(max_tokens - completion_tokens) <= 100 or completion_tokens<100:
                is_saved = False
        else:
            is_saved = True
        self.is_usable
        return is_saved

    def check_record_number(self):
        course_id = self.user_inputs.get('course_id')
        record: KnowledgeSimple = KnowledgeSimple.objects.filter(
                course_id=course_id,
                is_deleted=False,
                name=self.knowledge
            )
        return len(record)

    def update_knowledge_store(self, kg_desc, completion_tokens,is_regenerate):
        course_id = self.user_inputs.get('course_id')
        print(self.user_inputs)
        if 'debug' not in course_id:
            if is_regenerate:
                kgl=KnowledgeSimple.objects.filter(
                    course_id=course_id,
                    name=self.knowledge
                )
                old_kg = KnowledgeSimple.objects.filter(
                    course_id=course_id,
                    name=self.knowledge
                ).order_by('-id').first()
                if old_kg:
                    old_kg.definition = self.definition
                    old_kg.desc = kg_desc
                    old_kg.is_usable = False
                    old_kg.tokens = completion_tokens
                    old_kg.save()
            else:
                KnowledgeSimple.objects.create(
                    course_id=course_id,
                    name=self.knowledge,
                    definition=self.definition,
                    desc=kg_desc,
                    is_usable=False,
                    tokens=completion_tokens
                )

    def update_record(self):
        course_id = self.user_inputs.get('course_id')

        if 'debug' not in course_id:
            max_tokens = KnowledgeSimple.objects.filter(
            course_id=course_id,
            name=self.knowledge
        ).aggregate(Max('tokens'))['tokens__max']
            
        if max_tokens is not None:
            # 找到 tokens 最大的那条记录，可能有多条，取第一条
            top_record = KnowledgeSimple.objects.filter(
                course_id=course_id,
                name=self.knowledge,
                tokens=max_tokens
            ).first()

            if top_record:
                top_record.is_usable = True
                top_record.save()


    def _get_cached_kg_desc(self):
        course_id = self.user_inputs.get('course_id')

        # 临时设置后台管理不记录知识点定义
        if 'debug' in course_id:
            return ''

        is_regenerate = self.user_inputs.get('is_regenerate')
        if not is_regenerate:
            cached_kg: KnowledgeSimple = KnowledgeSimple.objects.filter(
                course_id=course_id,
                is_deleted=False,
                name=self.knowledge
            )

            if(len(cached_kg)==3):
                cached_kg=cached_kg.filter(is_usable=True).first()

                # 如果数据库存储的知识点可用，则直接返回
                if cached_kg and cached_kg.is_usable:
                    return cached_kg.desc
        return ''

    def _get_prompt_template(self):
        if self.definition:
            return self.local_prompt_template
        else:
            return self.llm_prompt_template

    def _get_message_generation(self, msg: str) -> Generator:
        yield msg


class KnowledgeQueryFactory:

    def __init__(self):

        local_prompt_template = None
        llm_prompt_template = None
        
        prompt_templates = PromptTemplate.objects.filter(
            app_no__in=['knowledge_analysis_query_local', 'knowledge_analysis_query_llm'])
        for p in prompt_templates:
            if p.app_no == 'knowledge_analysis_query_local':
                local_prompt_template = p
            elif p.app_no == 'knowledge_analysis_query_llm':
                llm_prompt_template = p

        if not local_prompt_template or not llm_prompt_template:
            raise Exception('prompt template not found')

        self.local_prompt_template = local_prompt_template
        self.llm_prompt_template = llm_prompt_template

    def get_knowledge_query(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            knowledge: str,
            definition: str,
            stream: bool,
    ) -> KnowledgeQuery:
        return KnowledgeQuery(
            application_generate_entity=application_generate_entity,
            knowledge=knowledge,
            definition=definition,
            stream=stream,
            local_prompt_template=self.local_prompt_template,
            llm_prompt_template=self.llm_prompt_template,
        )
