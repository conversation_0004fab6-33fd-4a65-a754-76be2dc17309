"""Abstract interface for document loader implementations."""
from abc import ABC, abstractmethod

from app.core.rag.extractor.entities.extract_setting import ExtractSetting
from app.core.rag.models.document import Document
from app.core.rag.splitter.fixed_text_splitter import RecursiveCharacterTextSplitter
from app.core.rag.splitter.text_splitter import TextSplitter
from app.core.rag.vdb.vector_factory import Vector
from app.models import Dataset


class BaseIndexProcessor(ABC):
    """Interface for extract files.
    """

    @abstractmethod
    def extract(self, extract_setting: ExtractSetting, **kwargs) -> list[Document]:
        raise NotImplementedError

    @abstractmethod
    def transform_special(self, documents: list[Document], **kwargs) -> list[Document]:
        raise NotImplementedError

    @abstractmethod
    def transform(self, documents: list[Document], **kwargs) -> list[Document]:
        raise NotImplementedError

    @abstractmethod
    def load(self, dataset: Dataset, documents: list[Document]):
        raise NotImplementedError

    def load_segment(self, dataset: Dataset, documents: list[Document]):
        vector = Vector(dataset)
        vector.add_texts(documents)

    def clean(self, dataset: Dataset, node_ids: list[str] | None):
        raise NotImplementedError

    def embedding_documents(self, dataset: Dataset, documents: list[Document]):
        vector = Vector(dataset)
        vector.embed_documents(documents)

    def _get_splitter(self, processing_rule: dict, file_extension: str) -> TextSplitter:
        segmentation = processing_rule["segmentation"]

        return RecursiveCharacterTextSplitter(
            chunk_size=segmentation["chunk_size"],
            chunk_overlap=segmentation.get('chunk_overlap', 0),
            separators=["\n", "。", ". ", " ", ""],
        )

    # def _get_splitter2(self, processing_rule: dict) -> TextSplitter:
    #     segmentation = processing_rule["segmentation"]
    #
    #     separator = segmentation["separator"]
    #     if separator:
    #         separator = separator.replace('\\n', '\n')
    #
    #     character_splitter = FixedRecursiveCharacterTextSplitter(
    #         chunk_size=segmentation["chunk_size"],
    #         chunk_overlap=segmentation.get('chunk_overlap', 0),
    #         fixed_separator=separator,
    #         separators=["\n\n", "。", ". ", " ", ""],
    #     )
    #     return character_splitter
