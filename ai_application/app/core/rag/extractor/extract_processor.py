import tempfile
from pathlib import Path

from app.core.rag.extractor.entities.datasource_type import DatasourceType
from app.core.rag.extractor.entities.extract_setting import ExtractSetting
from app.core.rag.extractor.markdown_extractor import MarkdownExtractor
from app.core.rag.extractor.pdf_extractor import PdfExtractor
from app.core.rag.extractor.text_extractor import TextExtractor
from app.core.rag.index_processor.constant.index_type import IndexType
from app.core.rag.models.document import Document
from app.core.utils.file_utils import download_remote_file


class ExtractProcessor:

    @classmethod
    def extract(cls, extract_setting: ExtractSetting, file_path: str = None) -> list[Document]:
        if extract_setting.datasource_type == DatasourceType.REMOTE_FILE.value:
            with tempfile.TemporaryDirectory() as temp_dir:
                if not file_path:
                    remote_file_info = extract_setting.remote_file_info
                    suffix = Path(remote_file_info.url).suffix
                    file_path = f"{temp_dir}/{next(tempfile._get_candidate_names())}{suffix}"
                    download_remote_file(remote_file_info.url, file_path)

                if extract_setting.document_model == IndexType.PARAGRAPH_INDEX.value:
                    input_file = Path(file_path)
                    file_extension = input_file.suffix.lower()
                    if file_extension == '.pdf':
                        extractor = PdfExtractor(file_path)
                    elif file_extension in ['.md', '.markdown']:
                        extractor = MarkdownExtractor(file_path)
                    else:
                        # txt
                        extractor = TextExtractor(file_path, autodetect_encoding=True)
                    return extractor.extract()
                elif extract_setting.document_model == IndexType.KNOWLEDGE_INDEX.value:
                    extractor = TextExtractor(file_path, autodetect_encoding=True)
                    return extractor.extract()
                elif extract_setting.document_model == IndexType.CHAT_DOC_INDEX.value:
                    extractor = TextExtractor(file_path, autodetect_encoding=True)
                    return extractor.extract()
