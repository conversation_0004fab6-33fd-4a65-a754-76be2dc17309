import logging
from typing import TypedDict

from api_client.base_client import BaseClient
from api_client.decorators import catch_error

logger = logging.getLogger(__name__)

__all__ = ['data_client']


class DataClient(BaseClient):
    host = 'http://data-svc:8000'
    host_local = 'http://data.kaoyan-vip.cn'

    @property
    def base_url(self):
        return f'{self.host}/internal_api/v1'

    @catch_error([])
    def search_knowledge(self, params: dict) -> list:
        """
        :param params:
            collection_ids: 知识集id，多个逗号分隔
            subject_ids: 科目id，多个逗号分隔
            name: 知识点名称
        :return:
        """
        url = '/three/knowledge/list/'
        return self._get(url, params=params)


data_client = DataClient()
