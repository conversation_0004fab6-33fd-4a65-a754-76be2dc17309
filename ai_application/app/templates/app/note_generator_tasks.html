<!-- app/note_generator_tasks.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记生成任务查看</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h1 {
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            font-size: 14px;
            color: #fff;
            background-color: #007bff;
            text-decoration: none;
            border-radius: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
       <h1>完整笔记查看列表</h1>
    <table>
        <thead>
            <tr>
                <th>名称</th>
                <th>任务开始时间</th>
                <th>任务结束时间</th>
                <th>生成状态</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for debug_task in debug_tasks %}
            <tr>
                <td>{{ debug_task.name }}</td>
                <td>{{ debug_task.processing_started_at|date:"Y-m-d H:i:s" }}</td>
                <td>{{ debug_task.completed_at|date:"Y-m-d H:i:s" }}</td>
                <td>{{ debug_task.status }}</td>
                <td>
                    <a href="{% url 'task_details' debug_task.id %}" class="btn">查看任务详情</a>
                    {% if debug_task.status == 'success' %}
                    <a href="{% url 'note_detail' debug_task.id %}" class="btn">查看完整笔记</a>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

</body>
</html>
