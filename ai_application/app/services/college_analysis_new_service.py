import json
import time

from langchain.chains.llm import <PERSON><PERSON><PERSON><PERSON>
from langchain_core.prompts import PromptTemplate
from django.conf import settings
from langchain_openai import ChatOpenAI
from ai_application.settings import DAYI_TEXT_MODEL
from app.api.college_analysis.college_major_info import extract_college_major_info, extract_college_introduction_info
from app.models import MajorRelation, MajorInfo, UndergraduateMajorCourse, CityGDP, UndergraduateCollegeInfo, \
    KaoYanQueryResult


class CollegeAnalysisNewService:

    @classmethod
    def filter_by_bachelor_major_code(cls, bachelor_major_code, personal_needs, bachelor_college_code,
                                      preferred_regions,cross_exam):
        # 先定位出用户本科院校层次：
        # bachelor_college_level = MajorInfo.objects.filter(college_code=bachelor_college_code).first().college_level
        und_college_info: UndergraduateCollegeInfo = UndergraduateCollegeInfo.objects.filter(
            undergraduate_code=bachelor_college_code).first()
        bachelor_college_level = und_college_info.level_display if und_college_info else ''

        print(f"本科院校层次🔥: {bachelor_college_level}")
        print(f"本科专业为🔥{bachelor_major_code}")
        major_code_list = []
        # 跨考模式
        if cross_exam.get("is_cross_exam", False):
            print("进入跨考模式🚀")
            cross_direction = cross_exam.get("cross_direction", "")
            print(f"跨考方向🔥: {cross_direction}")
            if not cross_direction:
                print("跨考方向为空")
                result = MajorRelation.objects.filter(
                    undergraduate_second_category=bachelor_major_code,
                    relation_level__lt=4
                ).order_by('relation_level').values(
                    'graduate_third_major_code',
                    'graduate_third_major',
                    'relation_level'
                )
                major_code_list = list(result.values_list('graduate_third_major_code', flat=True).distinct())
                print(f"符合条件的研究生三级代码:{major_code_list}")
                # 将专业代码前两位与本科专业代码一致的往前排
                if bachelor_major_code and len(bachelor_major_code) >= 2:
                    prefix = bachelor_major_code[:2]
                    # 排序规则：如果 major_code 前两位匹配，则排在前面
                    major_code_list.sort(
                        key=lambda code: (code[:2] != prefix)
                    )
                    print(f"已将与本科专业同门类的专业排到前面🔥: {major_code_list}")
                else:
                    print("本科专业代码不足两位，无法进行门类匹配排序")

            else:
                print(f"跨考方向🚀{cross_direction}")
                # 如果 cross_direction 有内容，用该字段的值匹配 graduate_second_category_code
                result = MajorRelation.objects.filter(
                    graduate_second_category=cross_direction)
                major_code_list = list(result.values_list('graduate_third_major_code', flat=True).distinct())
                # 添加 cross_direction+00 到 major_code_list
                if cross_direction:
                    code_part = cross_direction[:4]
                    major_code_list.append(f"{code_part}00")

                print(f"符合条件的研究生三级代码:{major_code_list}")

        else:
            print("进入非跨考模式🚀")
            # 非跨考模式
            try:
                with open('本科与研究生专业对应关系.json', 'r', encoding='utf-8') as f:
                    undergraduate_to_graduate = json.load(f)
                graduate_second_categories = undergraduate_to_graduate.get(bachelor_major_code, [])
                print(f"找到研究生二级门类映射啊啊啊啊🔥 {graduate_second_categories}")
            except Exception as e:
                print(f"加载映射文件失败: {e}")
                graduate_second_categories = []

            if graduate_second_categories:
                result = MajorRelation.objects.filter(
                    graduate_second_category__in=graduate_second_categories)

                major_code_list = list(result.values_list('graduate_third_major_code', flat=True).distinct())

                # 添加每个 graduate_second_categories+00 到 major_code_list
                for category in graduate_second_categories:
                    if category and len(category) >= 4:
                        code_part = category[:4]  # 提取前四位代码
                        major_code_list.append(f"{code_part}00")

                print(f"符合条件的研究生三级代码:{major_code_list}")
        all_data = []

        from concurrent.futures import ThreadPoolExecutor, as_completed

        def fetch_data(major_code):
            school_code = ''
            return extract_college_major_info(school_code, major_code)

        with ThreadPoolExecutor(max_workers=8) as executor:
            future_to_major = {executor.submit(fetch_data, major_code): major_code for major_code in
                               major_code_list}
            for future in as_completed(future_to_major):
                try:
                    data = future.result()
                    if data:
                        all_data.extend(data)
                except Exception as exc:
                    print(f"Error fetching data for major code: {future_to_major[future]}, exception: {exc}")

        # Step 3: 根据 personal_needs 做不同处理
        print("开始筛选第二步🚀")

        filtered_data = all_data

        # 条件一：不考数学 → 考数学的往后排
        if "不考数学" in (personal_needs or []):
            filtered_data.sort(
                key=lambda item: any("数学" in exam['subject1'] for exam in item.get('exam_range', []))
            )
            print("已将不考数学的专业排在前面")

        # 条件二：英语不好 → 按 language 成绩升序排序
        if "英语不好" in (personal_needs or []):
            filtered_data.sort(
                key=lambda x: (
                    # 如果 major_score_info 为 None，则视为 float('inf')（即排在最后）
                    float('inf') if x.get('major_score_info') is None else
                    x.get('major_score_info', {}).get('language', float('inf'))
                )
            )
            print("已将英语不好的专业按语言成绩升序排列")

        # 增加一个二级专业评级填充逻辑
        # 统一处理二级专业评级填充逻辑
        all_school_codes = []
        all_major_codes = []
        for item in filtered_data:
            if item.get('major_eval') == '':
                school_code = item.get('school_code', '')
                major_code = item.get('major_code', '')
                if len(major_code) >= 4:
                    prefix = major_code[:4]  # 取前四位
                    all_school_codes.append(school_code)
                    all_major_codes.append(prefix)
                    # 查询 ai_major_info 表中 major_code 以 prefix 开头的数据
                    # 放在下面统一处理
                    # matched = MajorInfo.objects.filter(college_code=school_code, major_code=prefix).first()
                    # if matched and matched.major_evaluation:
                    #     item['major_eval'] = matched.major_evaluation  # 填充评估字段

        if all_school_codes and all_major_codes:
            all_school_major_map = {}
            matched_qs = MajorInfo.objects.filter(
                college_code__in=all_school_codes, major_code__in=all_major_codes
            )
            for m in matched_qs:
                school_major_map_key = f"{m.college_code}_{m.major_code}"
                all_school_major_map[school_major_map_key] = m.major_evaluation

            for item in filtered_data:
                if item.get('major_eval') == '':
                    school_code = item.get('school_code', '')
                    major_code = item.get('major_code', '')
                    if len(major_code) >= 4:
                        prefix = major_code[:4]  # 取前四位
                        school_major_map_key = f"{school_code}_{prefix}"
                        if school_major_map_key in all_school_major_map:
                            item['major_eval'] = all_school_major_map[school_major_map_key]

        # 再次过滤掉 major_eval 仍为空的条目
        # filtered_data = [item for item in filtered_data if item.get('major_eval') != '']

        # Step 4: 根据 院校层次筛选
        print("开始筛选第三步🚀")
        college_level_display = {
            "985工程": 0,
            "211工程": 1,
            "双一流大学": 2,
            "普通院校": 6,
            "双非院校": 6,
            "境外高等教育机构在海南自由贸易港设立的实施理工农医类学科专业的学校": 6,
            "民办": 6,
            "专科": 6,
            "中外合作办学及内地与港澳合作办学": 6,

        }

        # 层级顺序（用于比较）
        college_level_hierarchy = {
            0: 3,  # 985工程 -> 最高层级
            1: 2,  # 211工程 -> 次高层级
            2: 1,  # 双一流大学 -> 第三层级
            6: 0,  # 其他院校 -> 最低层级
        }
        # 获取用户本科院校等级对应的数值
        bachelor_college_level_code = college_level_display.get(bachelor_college_level, 6)  # 默认是普通院校
        print(f"院系代码层次映射🔥: {bachelor_college_level_code}")
        # 获取用户的本科院校等级对应的真实层级数值
        bachelor_college_level_num = college_level_hierarchy.get(bachelor_college_level_code, 0)
        print(f"院系代码层次顺序🔥: {bachelor_college_level_num}")
        # 定义评估等级映射
        evaluation_mapping = {
            'A+': 5,
            'A': 4,
            'A-': 3,
            'B+': 2,
            'B': 1,
            'B-': 0,
            'C+': -1,
            'C': -2,
            'C-': -3
        }
        top_211_colleges = [
            "北京邮电大学",
            "中央财经大学",
            "对外经济贸易大学",
            "中国政法大学",
            "北京外国语大学",
            "中国传媒大学",
            "暨南大学",
            "郑州大学",
            "中南财经政法大学",
            "华中师范大学",
            "武汉理工大学",
            "华中农业大学",
            "湖南师范大学",
            "东北师范大学",
            "南京航空航天大学",
            "南京理工大学",
            "苏州大学",
            "南京师范大学",
            "中国药科大学",
            "西安电子科技大学",
            "上海财经大学",
            "上海外国语大学",
            "上海大学",
            "西南财经大学",
            "天津医科大学",
            "合肥工业大学"
        ]
        # 初始化三个档次的列表
        rush_data, stable_data, safe_data, other_data = [], [], [], []

        for item in filtered_data:
            school_level = item.get('school_level')
            major_eval = item.get('major_eval', 'B')
            school_name = item.get('school_name', '')

            school_level_num = college_level_hierarchy.get(school_level, 0)
            eval_score = evaluation_mapping.get(major_eval, -100)

            if bachelor_college_level_num == 3:  # 985工程
                if (school_level_num == 3 and eval_score >= 3) or (
                        school_level_num == 2 and school_name in top_211_colleges and eval_score >= 4):
                    rush_data.append(item)
                elif (school_level_num == 3 and eval_score >= 2) or (school_level_num == 2 and eval_score >= 3):
                    stable_data.append(item)
                elif (school_level_num == 3 and eval_score >= 1) or (school_level_num == 2 and eval_score >= 2):
                    safe_data.append(item)
                else:
                    other_data.append(item)

            elif bachelor_college_level_num == 2:  # 211工程
                if (school_level_num == 3 and eval_score >= 2) or (school_level_num == 2 and eval_score >= 4):
                    rush_data.append(item)
                elif (school_level_num == 2 and eval_score >= 3) or (school_level_num == 1 and eval_score >= 4):
                    stable_data.append(item)
                elif (school_level_num == 2 and eval_score >= 2) or (school_level_num == 1 and eval_score >= 3):
                    safe_data.append(item)
                else:
                    other_data.append(item)

            elif bachelor_college_level_num == 1:  # 双一流大学
                if (school_level_num == 2 and eval_score >= 3) or (school_level_num == 1 and eval_score >= 4):
                    rush_data.append(item)
                elif (school_level_num == 1 and eval_score >= 3) or (school_level_num == 0 and eval_score >= 4):
                    stable_data.append(item)
                elif (school_level_num == 1 and eval_score >= 2) or (school_level_num == 0 and eval_score >= 3):
                    safe_data.append(item)
                else:
                    other_data.append(item)

            elif bachelor_college_level_num == 0:  # 普通院校
                if (school_level_num == 2 and eval_score >= 1) or (school_level_num == 1 and eval_score >= 3):
                    rush_data.append(item)
                elif (school_level_num == 1 and eval_score >= 2) or (school_level_num == 0 and eval_score >= 4):
                    stable_data.append(item)
                elif (school_level_num == 1 and eval_score >= 1) or (school_level_num == 0 and eval_score >= 3):
                    safe_data.append(item)
                else:
                    other_data.append(item)
            # print(f"other_data{other_data}")
        # 稳妥档后备机制
        if not stable_data:
            print("稳妥档为空，启动后备筛选机制")
            # # 排除已选入其他档次的院校
            # existing_schools = {item.get('school_name') for item in rush_data}

            backup_stable = [
                item for item in other_data
                if bachelor_college_level_num <= college_level_hierarchy.get(item.get('school_level', '普通院校'), 0) <= bachelor_college_level_num + 1
                # and item.get('school_name') not in existing_schools
            ]

            backup_stable = sort_by_admission_ratio(backup_stable)[:2]
            stable_data.extend(backup_stable)
            print(f"补充了 {len(backup_stable)} 所稳妥院校")

        # 保底档后备机制
        if not safe_data:
            print("保底档为空，启动后备筛选机制")
            # 排除已选入其他档次的院校
            existing_schools = {item.get('school_name') for item in rush_data + stable_data}

            backup_safe = [
                item for item in other_data
                if college_level_hierarchy.get(item.get('school_level', '普通院校'), 0) <= bachelor_college_level_num
                   and item.get('school_name') not in existing_schools
            ]

            backup_safe = sort_by_admission_ratio(backup_safe)[:2]
            safe_data.extend(backup_safe)
            print(f"补充了 {len(backup_safe)} 所保底院校")
        # Step 5：按照复录比继续筛选
        print("开始筛选第四步🚀")

        rush_data = sort_by_admission_ratio(rush_data)
        stable_data = sort_by_admission_ratio(stable_data)
        safe_data = sort_by_admission_ratio(safe_data)

        # ========== 新增：分别对三个档次按照意向地区重新排序 ==========
        def re_sort_by_preferred_regions(data, preferred_regions):
            """
            对输入的学校数据 data 按照意向地区进行排序
            """
            if not preferred_regions:
                return data

            city_ranking = {
                "超一线城市": ["北京市", "上海市"],
                "一线城市": ["广州市", "深圳市"],
                "新一线城市": ["成都市", "杭州市", "重庆市", "武汉市", "苏州市", "西安市", "南京市", "长沙市", "郑州市",
                               "天津市", "合肥市", "青岛市", "东莞市", "宁波市", "佛山市"],
                "二线城市": ["济南市", "无锡市", "沈阳市", "昆明市", "福州市", "厦门市", "温州市", "石家庄市", "大连市",
                             "哈尔滨市", "金华市", "泉州市", "南宁市", "长春市", "常州市", "南昌市", "南通市", "贵阳市",
                             "嘉兴市", "徐州市", "惠州市", "太原市", "烟台市", "临沂市", "保定市", "台州市", "绍兴市",
                             "珠海市", "洛阳市", "潍坊市"],
            }

            def process_preferred_regions(preferred_regions):
                result_cities = []
                for region in preferred_regions:
                    if region in city_ranking:
                        result_cities.extend(city_ranking[region])
                    else:
                        result_cities.append(region)
                return result_cities

            preferred_cities = process_preferred_regions(preferred_regions)

            # 排序逻辑
            data.sort(key=lambda item: (
                # 是否在意向地区
                0 if (item.get('school_city', {}).get('city') in preferred_cities or
                      item.get('school_city', {}).get('province') in preferred_cities)
                else 1,

                # 城市层级排序
                next((i for i, level in enumerate([
                    "超一线城市", "一线城市", "新一线城市", "二线城市"
                ]) if any(city in preferred_cities for city in city_ranking.get(level, []))), len(city_ranking))
            ))

            return data

        # 分别对三个档次进行意向地区重排序
        if preferred_regions:
            rush_data = re_sort_by_preferred_regions(rush_data, preferred_regions)
            stable_data = re_sort_by_preferred_regions(stable_data, preferred_regions)
            safe_data = re_sort_by_preferred_regions(safe_data, preferred_regions)

        # 截取最终结果（确保学校名称不重复）
        final_rush = get_unique_schools(rush_data, 2)
        final_stable = get_unique_schools(stable_data, 3)
        final_safe = get_unique_schools(safe_data, 2)

        # 如果数据量 > 1，则进行内部排序
        if len(final_rush) > 1:
            final_rush = internal_sort(final_rush)
        if len(final_stable) > 1:
            final_stable = internal_sort(final_stable)
        if len(final_safe) > 1:
            final_safe = internal_sort(final_safe)
        def remove_major_info_fields(data):
            """
            从数据中的每个字典中移除 major_re_exam_info 和 major_enroll_info 字段
            """
            for item in data:
                item.pop("major_re_exam_info", None)
                item.pop("major_enroll_info", None)
            return data

        # 删除不需要的字段
        final_rush = remove_major_info_fields(final_rush)
        final_stable = remove_major_info_fields(final_stable)
        final_safe = remove_major_info_fields(final_safe)

        # 添加院校介绍字段
        final_rush = add_college_introduction(final_rush)
        final_stable = add_college_introduction(final_stable)
        final_safe = add_college_introduction(final_safe)

        return {
            '冲刺档': final_rush,
            '稳妥档': final_stable,
            '保底档': final_safe,
        }

    @classmethod
    def college_analysis_with_goal(cls, bachelor_major_code, bachelor_college_code, target_college_code,
                                   target_major_code,priority_order):

        # 基础难度计算:基础难度 = 复录比 × 分数线分位值 × 院校专业系数
        target_results = extract_college_major_info(target_college_code, target_major_code)
        # print("target_results🔥:", target_results)
        # 判断是否为空列表
        if not target_results:
            return {
                "error": "未找到目标院校和专业的相关信息，请检查输入的院校代码或专业代码是否正确。",
                "code": 404
            }
        target_result = target_results[0]
        # 提取 re_exam_num 和 enroll_num
        re_exam_num = target_result.get('re_exam_num', 0)
        enroll_num = target_result.get('enroll_num', 0)
        school_level_display = target_result.get('school_level_display', '普通院校')
        print(f"复试人数:{re_exam_num},录取人数:{enroll_num}")
        # 计算复录比
        if re_exam_num > 0 and enroll_num > 0:
            admission_ratio = re_exam_num / enroll_num
        else:
            # 根据院校层次设定默认复录比
            default_ratio_mapping = {
                "985工程": 2.2,
                "211工程": 1.8,
                "双一流大学": 1.6,
                "普通院校": 1.4
            }
            admission_ratio = default_ratio_mapping.get(school_level_display, 1.4)  # 默认普通院校

        print(f"复录比: {admission_ratio}")

        # 计算分数线分位值
        # 提取专业复试平均分
        major_re_exam_info = target_result.get("major_re_exam_info", {})
        average_professional_line = major_re_exam_info.get("average", None)
        lowest_professional_line = major_re_exam_info.get("lowest", None)
        highest_professional_line = major_re_exam_info.get("highest", None)

        # 提取专业线
        # major_score_info = target_result.get("major_score_info", {})
        # professional_line = major_score_info.get("total", None)
        # if average_professional_line is None:
        #     raise ValueError("未找到专业线信息")

        # 获取学校代码和专业代码
        school_code = target_result.get("school_code")
        major_code = target_result.get("major_code")

        # 查找国家线
        national_line = None
        if school_code and major_code:
            # 尝试精确匹配
            matched = MajorInfo.objects.filter(college_code=school_code, major_code=major_code).first()
            if not matched:
                # 使用 major_code 前四位进行模糊匹配
                prefix = major_code[:4]
                matched = MajorInfo.objects.filter(college_code=school_code, major_code__startswith=prefix).first()

            if matched:
                national_line = matched.national_line_score

        # 计算分数线分位值
        default_score_position_mapping = {
            '清北': 0.09,
            'C9（除清北）': 0.08,
            "985工程": 0.07,
            "211工程": 0.05,
            "双一流大学": 0.04,
            "普通院校": 0.02
        }
        print(f"national_line在这边啊啊啊啊啊啊:{national_line}")
        if average_professional_line is not None and national_line is not None:
            try:
                score_position_value = max(0, (average_professional_line - national_line) / national_line)
            except ZeroDivisionError:
                score_position_value = 0  # 防止除以零错误
        else:
            if target_college_code in ['10001', '10003']:
                college_level = '清北'
            elif target_college_code in ['10335', '10248', '10246', '10358', '10284', '10213', '10698']:
                college_level = 'C9（除清北）'
            else:
                college_level = target_result.get("school_level_display", "普通院校")
            score_position_value = default_score_position_mapping.get(college_level, 0.02)

        print(f"复试专业线平均分: {average_professional_line}")
        print(f"国家线: {national_line}")
        print(f"分数线分位值: {score_position_value}")

        # 计算院校专业系数 = 院校层级系数 × 城市 GDP 修正系数) × 学科评级系数 / 10
        college_level_score = {
            '清北': 10,
            'C9（除清北）': 9,
            "985工程": 7,
            "211工程": 5,
            "双一流大学": 4,
            "普通院校": 3,
            "双非院校": 3,
            "境外高等教育机构在海南自由贸易港设立的实施理工农医类学科专业的学校": 3,
            "民办": 3,
            "专科": 3,
            "中外合作办学及内地与港澳合作办学": 3,
        }

        # 特殊处理：清华大学（10003）和北京大学（10001）
        if target_college_code in ['10001', '10003']:
            college_level = '清北'
        elif target_college_code in ['10335', '10248', '10246', '10358', '10284', '10213', '10698']:
            college_level = 'C9（除清北）'
        else:
            college_level = target_result.get("school_level_display", "普通院校")

        college_level_coefficient = college_level_score.get(college_level, 3)
        print(f"目标院校层级得分：{college_level_coefficient}")

        # 获取专业评级
        major_level = target_result.get("major_eval")
        if major_level == '':
            if len(major_code) >= 4:
                prefix = major_code[:4]  # 取前四位
                # 查询 ai_major_info 表中 major_code 以 prefix 开头的数据
                matched = MajorInfo.objects.filter(college_code=school_code, major_code=prefix).first()
                if matched and matched.major_evaluation:
                    major_level = matched.major_evaluation
        print(f"专业评级: {major_level}")

        # 定义评估等级映射(参考专业评级划分比例进行赋分)
        major_evaluation_coefficient_mapping = {
            'A+': 9.86,
            'A': 9.60,
            'A-': 9.14,
            'B+': 8.29,
            'B': 7.14,
            'B-': 6,
            'C+': 4.86,
            'C': 3.71,
            'C-': 2.57
        }
        major_evaluation_coefficient = major_evaluation_coefficient_mapping.get(major_level, 6)
        print(f"专业评级得分: {major_evaluation_coefficient}")

        # 计算城市GDP修正系数
        city_gdp_coefficient = get_gdp_coefficient_by_city(target_result)
        college_major_coefficient = college_level_coefficient * city_gdp_coefficient * major_evaluation_coefficient / 10
        print(f"城市 GDP 修正系数: {city_gdp_coefficient}")
        print(f"院校专业系数: {college_major_coefficient}")

        # 计算跨门类惩罚系数（与关联度相关联，暂时假设 bachelor_major_code是二级门类例如“0101哲学类”）
        # 提取 target_major_code 的前四位
        target_major_code_prefix = target_major_code[:4]
        print(f"本科二级门类: {bachelor_major_code},目标二级门类: {target_major_code_prefix}")
        # 查询 MajorRelation 表
        relation_record = MajorRelation.objects.filter(
            undergraduate_second_category=bachelor_major_code,
            graduate_second_category_code=target_major_code_prefix
        ).first()

        # 获取 relation_level
        if relation_record:
            relation_level = relation_record.relation_level
            print(f"关联度等级: {relation_level}")
        else:
            relation_level = None
            print("未找到对应的关联度记录")

        # 根据 relation_level 确定跨门类惩罚系数
        if relation_level is not None:
            if relation_level <= 2:
                penalty_coefficient = 0.00
                print("高度相关，无惩罚")
            elif 2 < relation_level <= 4:
                penalty_coefficient = 0.15
                print("中度相关，轻微惩罚")
            elif 4 < relation_level <= 7:
                penalty_coefficient = 0.4
                print("低度相关，中等惩罚")
            else:
                penalty_coefficient = 0.8
                print("无关联，强惩罚")
        else:
            # 如果未找到关联度，默认使用最高惩罚系数 0.8 或抛出异常
            penalty_coefficient = 0.8
            print("未找到关联度，采用默认无关联惩罚系数 0.8")

        print(f"跨门类惩罚系数: {penalty_coefficient}")

        # 计算知识差异惩罚系数(暂时使用大模型来给出评分)：知识差异惩罚系数 = 0.1 × (1 - 课程匹配度)
        # 找到本科核心课程
        major_course_obj = UndergraduateMajorCourse.objects.filter(
            second_category=bachelor_major_code
        ).first()

        if major_course_obj:
            undergraduate_major_course = major_course_obj.core_courses
        else:
            undergraduate_major_course = "未找到核心课程信息"
        print(f"本科核心课程: {undergraduate_major_course}")
        # 提取 exam_range 的第一个考试科目信息
        exam_range = target_result.get("exam_range", [])
        if exam_range:
            first_exam = exam_range[0]
            exam_text = (
                f"政治科目：{first_exam.get('polity', '')}\n"
                f"专业课一：{first_exam.get('subject1', '')}\n"
                f"专业课二：{first_exam.get('subject2', '')}"
            )
        else:
            exam_text = "未找到考试科目信息"

        # 使用大模型对知识差异惩罚系数进行打分（后期可能会修改）
        prompt_template = """
        请分析以下本科核心课程与研究生考试科目的知识匹配程度，按照以下的评分规则，给出一个 0 到 1 之间的评分：

        | 匹配度范围 | 评价等级     | 详细描述                                       |
        |------------|--------------|------------------------------------------------|
        | 0.8        | 高度匹配     | 核心课程覆盖充分，知识储备充足                 |
        | 0.6 - 0.8  | 中度匹配     | 基础课程匹配良好，需补充 1-2 门               |
        | 0.4 - 0.6  | 低度匹配     | 仅基础课程匹配，需系统补充                     |
        | < 0.4      | 严重脱节     | 知识体系差异巨大                               |

        【本科核心课程】
        {undergraduate_course}

        【研究生考试科目】
        {exam_subjects}

        务必请只输出一个数字，保留一位小数。
        """

        prompt = PromptTemplate.from_template(prompt_template)

        llm = ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base=settings.DOUBAO_API_BASE,
            model_name=DAYI_TEXT_MODEL,
            temperature=0.3,
        )

        chain = LLMChain(llm=llm, prompt=prompt)

        response = chain.run({
            "undergraduate_course": undergraduate_major_course,
            "exam_subjects": exam_text
        })
        print(f"大模型返回结果为: {response}")
        try:
            knowledge_difference_score = float(response.strip())
        except ValueError:
            print("大模型返回非数值结果，默认设置为 0.5")
            knowledge_difference_score = 0.5

        print(f"知识差异惩罚系数（匹配度得分）: {knowledge_difference_score}")

        knowledge_penalty_coefficient = 0.1 * (1 - knowledge_difference_score)

        print(f"知识差异惩罚系数为: {knowledge_penalty_coefficient}")

        # 增加院校跨度修正系数
        # 院校跨度数据=目标院校系数/ 本科院校系数
        # undergraduate_college_level = MajorInfo.objects.filter(college_code=bachelor_college_code).first().college_level
        undergraduate_college_level = UndergraduateCollegeInfo.objects.filter(
            undergraduate_code=bachelor_college_code).first().level_display
        undergraduate_college_coefficient = college_level_score.get(undergraduate_college_level, 3)

        college_gap_score = college_level_coefficient / undergraduate_college_coefficient

        print(f"院校跨度得分：{college_gap_score}")
        college_gap_coefficient = calculate_college_gap_coefficient(college_gap_score)
        print(f"院校跨度修正系数：{college_gap_coefficient}")
        # 计算总难度=基础难度× (1 + 跨门类惩罚系数 + 知识差异惩罚系数)× 院校跨度修正系数
        base_difficulty = admission_ratio * score_position_value * college_major_coefficient
        total_difficulty = base_difficulty * (
                    1 + penalty_coefficient + knowledge_penalty_coefficient) * college_gap_coefficient

        # 提取专业复试平均分
        major_re_exam_info = target_result.get("major_re_exam_info", {})
        average_professional_line = major_re_exam_info.get("average", None)

        print(f"进入复试的平均分为: {average_professional_line}")
        print(f"基础难度为: {base_difficulty}")
        print(f"跨门类惩罚系数为: {penalty_coefficient}")
        print(f"知识差异惩罚系数为: {knowledge_penalty_coefficient}")
        print(f"总难度为: {total_difficulty}")

        # 目标专业是学硕/专硕
        target_major_type = target_result.get("major_type", "")
        # 如果难度较大，大于等于0.8，需要根据 priority_order 和其他条件进行推荐
        additional_recommendations = []
        if total_difficulty >= 0.8:
            print(f"priority_order的内容为:{priority_order}")
            # 检查 priority_order 是否非空
            if priority_order and priority_order.strip() != '':
                # 解析 priority_order 中的字段顺序
                order_fields = priority_order.strip().split(',')
                # 判断 'college_level' 和 'major' 哪个在前面
                college_level_index = order_fields.index('college_level') if 'college_level' in order_fields else -1
                major_index = order_fields.index('major') if 'major' in order_fields else -1

                if major_index != -1 and college_level_index != -1 and major_index < college_level_index:
                    # 如果 major 在 college_level 前面，则调用 extract_college_major_info
                    school_code = ''
                    target_major_result = extract_college_major_info(school_code, target_major_code)
                    # print(f"这里是这个专业的所有数据: {target_major_result}")
                    # 筛选数据
                    filtered_by_college_level = []
                    # 获取本科院校层级，找出包含目标专业的所有与本科同层级的数据
                    undergraduate_college_level = UndergraduateCollegeInfo.objects.filter(
                        undergraduate_code=bachelor_college_code).first().level_display
                    print(f"这里是专业优先推荐中的本科院校层级为: {undergraduate_college_level}")
                    # 如果不是 985、211 或 双一流，则设为 "双非院校"
                    if undergraduate_college_level not in ["985工程", "211工程", "双一流大学"]:
                        undergraduate_college_level = "普通院校"
                    for item in target_major_result:
                        school_level = item.get("school_level_display")
                        if school_level == undergraduate_college_level:
                            filtered_by_college_level.append(item)
                    # print(f"这里是专业优先推荐中的筛选完跟本科院校同层级的专业数据为: {filtered_by_college_level}")
                    # 根据 用户填写的master_type 进行筛选
                    # if master_type:
                    #     if master_type == "学硕":
                    #         filtered_by_college_level = [
                    #             item for item in filtered_by_college_level
                    #             if item.get("mold_display") == "学术型硕士"
                    #         ]
                    #     elif master_type == "专硕":
                    #         filtered_by_college_level = [
                    #             item for item in filtered_by_college_level
                    #             if item.get("mold_display") == "专业型硕士"
                    #         ]
                    # print(f"这里是专业优先推荐中的筛选完学硕专硕类型后的数据: {filtered_by_college_level}")
                    # 定义城市等级列表
                    city_ranking = {
                        "超一线城市": ["北京市", "上海市"],
                        "一线城市": ["广州市", "深圳市"],
                        "新一线城市": ["成都市", "杭州市", "重庆市", "武汉市", "苏州市", "西安市", "南京市", "长沙市",
                                       "郑州市", "天津市", "合肥市", "青岛市", "东莞市", "宁波市", "佛山市"],
                        "二线城市": ["济南市", "无锡市", "沈阳市", "昆明市", "福州市", "厦门市", "温州市", "石家庄市",
                                     "大连市", "哈尔滨市", "金华市", "泉州市", "南宁市", "长春市", "常州市", "南昌市",
                                     "南通市", "贵阳市", "嘉兴市", "徐州市", "惠州市", "太原市", "烟台市", "临沂市",
                                     "保定市", "台州市", "绍兴市", "珠海市", "洛阳市", "潍坊市"],
                    }

                    second_tier_cities = city_ranking.get("二线城市", [])
                    higher_cities = (
                            city_ranking.get("超一线城市", []) +
                            city_ranking.get("一线城市", []) +
                            city_ranking.get("新一线城市", [])
                    )

                    # 初始化两个组别
                    group_a = []
                    group_b = []

                    print("开始查找符合条件的数据...")

                    for item in filtered_by_college_level:
                        if len(group_a) >= 1 and len(group_b) >= 1:
                            print("已找到足够的推荐院校，停止继续查找。")
                            break
                        major_code = item.get("major_code", "")
                        school_code = item.get("school_code", "")
                        school_city = item.get("school_city", {})
                        city = school_city.get("city", "")
                        province = school_city.get("province", "")
                        major_eval = item.get("major_eval", "")
                        print(f"这里是院校代码: {school_code} ")
                        if major_eval == '':
                            if len(major_code) >= 4:
                                prefix = major_code[:4]  # 取前四位
                                # 查询 ai_major_info 表中 major_code 以 prefix 开头的数据
                                print(f"这里是院校代码和专业前4位: {school_code}---{prefix} ")
                                matched = MajorInfo.objects.filter(college_code=school_code, major_code=prefix).first()
                                print(f"数据库匹配到的数据{matched}")
                                if matched and matched.major_evaluation:
                                    major_eval = matched.major_evaluation  # 填充评估字段
                                else:
                                    major_eval = 'B+'  # 如果未找到匹配的评估等级，默认为 B+
                            else:
                                major_eval = 'B+'  # 如果 major_code 长度不足，默认为 B+
                        print(f"这里是专业评估等级: {major_eval} ")
                        is_in_second_tier = city in second_tier_cities or province in second_tier_cities
                        is_in_higher_cities = city in higher_cities or province in higher_cities

                        # 检查是否符合 A/A+ 组条件
                        if not group_a and major_eval in ["A", "A+","A-","B+"] and is_in_second_tier:
                            group_a.append(item)
                            print(f"已找到 A/A+ 组推荐院校: {item['school_name']}")

                        # 检查是否符合 B 及以下组条件
                        if not group_b and major_eval not in ["A", "A+","A-","B+"] and is_in_higher_cities:
                            group_b.append(item)
                            print(f"已找到 B 及以下组推荐院校: {item['school_name']}")
                    additional_recommendations = group_a + group_b

                # 若 college_level 在 major 前面（优先院校层级）
                elif major_index != -1 and college_level_index != -1 and college_level_index < major_index:
                    print("college_level 前面")
                    # 取目标院校代码，major_code 为空
                    school_code = target_college_code
                    target_college_result = extract_college_major_info(school_code, '')
                    print(f"优先院校层级，目标院校下所有专业信息: {target_college_result}")

                    # 提取目标专业代码前两位
                    target_major_prefix = target_major_code[:2] if len(target_major_code) >= 2 else ''
                    print(f"目标专业前两位代码: {target_major_prefix}")

                    # 提取目标专业评级
                    target_major_eval = target_result.get("major_eval", "")
                    if target_major_eval == '':

                        if len( target_major_code) >= 4:
                            prefix = major_code[:4]  # 取前四位
                            # 查询 ai_major_info 表中 major_code 以 prefix 开头的数据
                            matched = MajorInfo.objects.filter(college_code=school_code, major_code=prefix).first()
                            if matched and matched.major_evaluation:
                                target_major_eval = matched.major_evaluation  # 填充评估字段
                            else:
                                target_major_eval = 'B+'  # 如果未找到匹配的评估等级，默认为 B+
                        else:
                            target_major_eval = 'B+'  # 如果 major_code 长度不足，默认为 B+


                    # 定义评级等级顺序（用于判断高低）
                    eval_hierarchy = ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-']
                    target_eval_index = eval_hierarchy.index(
                        target_major_eval) if target_major_eval in eval_hierarchy else -1

                    # 筛选符合条件的专业
                    filtered_majors = []
                    for item in target_college_result:
                        # 条件1：专业代码前两位与目标一致
                        item_major_code = item.get("major_code", "")
                        item_major_prefix = item_major_code[:2] if len(item_major_code) >= 2 else ''
                        if item_major_prefix != target_major_prefix:
                            continue

                        # 条件2：专业评级比目标低1级或2级
                        item_major_eval = item.get("major_eval", "")
                        if item_major_eval == '':
                            if len(item_major_code) >= 4:
                                prefix = item_major_code[:4]  # 取前四位
                                # 查询 ai_major_info 表中 major_code 以 prefix 开头的数据
                                matched = MajorInfo.objects.filter(college_code=school_code, major_code=prefix).first()
                                if matched and matched.major_evaluation:
                                    item_major_eval = matched.major_evaluation
                        item_eval_index = eval_hierarchy.index(
                            item_major_eval) if item_major_eval in eval_hierarchy else -1
                        if target_eval_index == -1 or item_eval_index == -1:
                            continue

                        # 计算评级差距（正值表示更低）
                        eval_gap = item_eval_index - target_eval_index
                        if eval_gap == 1 or eval_gap == 2 or eval_gap == 3 or eval_gap == 4:
                            filtered_majors.append(item)
                            print(f"符合条件的专业: {item['major_name']}，评级差距: {eval_gap}级")

                    # 取前两条符合条件的数据
                    additional_recommendations = filtered_majors[:2]
                    print(f"筛选出的前两条专业: {[item['major_name'] for item in additional_recommendations]}")

            # 若 priority_order 为空，默认按专业在前的逻辑处理
            else:
                print("priority_order 为空，默认按专业在前逻辑处理")
                # 调用 extract_college_major_info，school_code 为空，使用目标专业代码
                school_code = ''
                target_major_result = extract_college_major_info(school_code, target_major_code)

                # 筛选数据（同前面 major 在前的逻辑）
                filtered_by_college_level = []
                undergraduate_college_level = UndergraduateCollegeInfo.objects.filter(
                    undergraduate_code=bachelor_college_code).first().level_display
                if undergraduate_college_level not in ["985工程", "211工程", "双一流大学"]:
                    undergraduate_college_level = "普通院校"
                for item in target_major_result:
                    school_level = item.get("school_level_display")
                    if school_level == undergraduate_college_level:
                        filtered_by_college_level.append(item)

                # 根据 master_type 筛选
                # if master_type:
                #     if master_type == "学硕":
                #         filtered_by_college_level = [
                #             item for item in filtered_by_college_level
                #             if item.get("mold_display") == "学术型硕士"
                #         ]
                #     elif master_type == "专硕":
                #         filtered_by_college_level = [
                #             item for item in filtered_by_college_level
                #             if item.get("mold_display") == "专业型硕士"
                #         ]

                # 城市等级筛选（同前面逻辑）
                city_ranking = {
                    "超一线城市": ["北京市", "上海市"],
                    "一线城市": ["广州市", "深圳市"],
                    "新一线城市": ["成都市", "杭州市", "重庆市", "武汉市", "苏州市", "西安市", "南京市",
                                   "长沙市",
                                   "郑州市", "天津市", "合肥市", "青岛市", "东莞市", "宁波市", "佛山市"],
                    "二线城市": ["济南市", "无锡市", "沈阳市", "昆明市", "福州市", "厦门市", "温州市",
                                 "石家庄市",
                                 "大连市", "哈尔滨市", "金华市", "泉州市", "南宁市", "长春市", "常州市",
                                 "南昌市",
                                 "南通市", "贵阳市", "嘉兴市", "徐州市", "惠州市", "太原市", "烟台市", "临沂市",
                                 "保定市", "台州市", "绍兴市", "珠海市", "洛阳市", "潍坊市"],
                }
                second_tier_cities = city_ranking.get("二线城市", [])
                higher_cities = (
                        city_ranking.get("超一线城市", []) +
                        city_ranking.get("一线城市", []) +
                        city_ranking.get("新一线城市", [])
                )

                group_a = []
                group_b = []
                print("priority_order 为空，按专业在前逻辑查找推荐院校...")
                for item in filtered_by_college_level:
                    if len(group_a) >= 1 and len(group_b) >= 1:
                        print("已找到足够的推荐院校，停止查找。")
                        break
                    major_code = item.get("major_code", "")
                    school_code = item.get("school_code", "")
                    school_city = item.get("school_city", {})
                    city = school_city.get("city", "")
                    province = school_city.get("province", "")
                    major_eval = item.get("major_eval", "")
                    if major_eval == '':
                        if len(major_code) >= 4:
                            prefix = major_code[:4]  # 取前四位

                            # 查询 ai_major_info 表中 major_code 以 prefix 开头的数据
                            matched = MajorInfo.objects.filter(college_code=school_code, major_code=prefix).first()
                            if matched and matched.major_evaluation:
                                major_eval = matched.major_evaluation  # 填充评估字段
                            else:
                                major_eval = 'B+'  # 如果未找到匹配的评估等级，默认为 B+
                        else:
                            major_eval = 'B+'  # 如果 major_code 长度不足，默认为 B+
                    print(f"这里是专业评估等级: {major_eval} ")
                    is_in_second_tier = city in second_tier_cities or province in second_tier_cities
                    is_in_higher_cities = city in higher_cities or province in higher_cities

                    if not group_a and major_eval in ["A", "A+","A-","B+"] and is_in_second_tier:
                        group_a.append(item)
                        print(f"A/A+ 组推荐院校: {item['school_name']}")
                    if not group_b and major_eval not in ["A", "A+","A-","B+"] and is_in_higher_cities:
                        group_b.append(item)
                        print(f"B 及以下组推荐院校: {item['school_name']}")
                additional_recommendations = group_a + group_b

        # 最终返回结果（确保额外推荐最多两条）
        additional_recommendations = additional_recommendations[:2]

        return {
            "目标专业是学硕/专硕": target_major_type,
            "进入复试的平均分": average_professional_line,
            "进入复试的最低分": lowest_professional_line,
            "进入复试的最高分": highest_professional_line,
            "院校跨度得分": college_gap_score,
            "复录比": admission_ratio,
            "分位值": score_position_value,
            "跨门类惩罚系数": penalty_coefficient,
            "知识匹配度": knowledge_difference_score,
            "总难度": total_difficulty,
            "额外推荐的两个院校专业": additional_recommendations,
        }

def calculate_college_gap_coefficient(ratio):
    """
    根据院校跨度比值区间返回对应的修正系数

    参数:
        ratio (float): 院校层级比值（目标 / 本科）
        target_college_code (str): 目标院校代码

    返回:
        float: 对应的修正系数
    """

    if ratio < 1.1:
        return 1.00  # 平级跨越
    elif 1.1 <= ratio < 1.8:
        # 线性插值：1.1 -> 1.15, 1.8 -> 1.30
        return 1.15 + (ratio - 1.1) * (1.30 - 1.15) / (1.8 - 1.1)
    elif 1.8 <= ratio < 2.5:
        # 线性插值：1.8 -> 1.35, 2.5 -> 1.50
        return 1.35 + (ratio - 1.8) * (1.50 - 1.35) / (2.5 - 1.8)
    else:
        # 极限跨越，线性延伸可选，这里设为固定最大值 1.80
        return 1.80


def get_gdp_coefficient_by_city(target_result):
    # 提取城市信息
    school_city = target_result.get("school_city", {})
    city_name = school_city.get("city")
    province_name = school_city.get("province")

    # 如果 city_name 是 "市辖区"，则使用 province_name
    if city_name == "市辖区":
        query_city = province_name
    else:
        query_city = city_name

    # 查询 CityGDP 表
    if query_city:
        city_gdp_record = CityGDP.objects.filter(city=query_city).first()
        if city_gdp_record:
            rank = city_gdp_record.rank
            # 根据排名区间返回对应的修正系数
            if rank <= 10:
                return 1.2
            elif 11 <= rank <= 20:
                return 1.15
            elif 21 <= rank <= 40:
                return 1.1
            elif 41 <= rank <= 70:
                return 1.05
            elif 71 <= rank <= 100:
                return 1.02
            else:
                return 1.0  # 超过 100 名
        else:
            return 1.0
    else:
        return 1.0


def get_unique_schools(data, count):
    """
    从数据中提取最多 count 个不重复的 school_name。
    """
    seen = set()
    result = []
    for item in data:
        school_name = item.get('school_name')
        if school_name not in seen:
            seen.add(school_name)
            result.append(item)
            if len(result) == count:
                break
    return result


def sort_by_admission_ratio(items):
    # 分成两组：有复录比的 和 没有的
    valid = []
    invalid = []

    for item in items:
        re_exam_num = item.get('re_exam_num', 0)
        enroll_num = item.get('enroll_num', 0)

        if re_exam_num > 0 and enroll_num > 0:
            ratio = enroll_num / re_exam_num
            valid.append((item, ratio))
        else:
            invalid.append(item)

    # 对有复录比的数据按 ratio 降序排序
    valid.sort(key=lambda x: x[1], reverse=True)

    # 只保留原始数据，并合并：valid + invalid
    sorted_items = [item for item, ratio in valid] + invalid
    return sorted_items

def add_college_introduction(data):
    """
    为数据中的每个字典添加 college_introduction 字段
    """
    for item in data:
        school_code = item.get("school_code")
        if school_code:
            # 调用 extract_college_introduction_info 获取介绍信息
            introduction_info = extract_college_introduction_info(school_code)
            item["college_introduction"] = introduction_info
        else:
            item["college_introduction"] = "未找到院校介绍信息"
    return data


# 对每个档次内部进行排序
def internal_sort(data):
    """
    档次内部排序：先按院校层级，再按专业评级
    """
    # 定义院校层级映射
    college_level_rank = {
        "985工程": 3,
        "211工程": 2,
        "双一流大学": 1,
        "普通院校": 0,
    }

    # 定义专业评级映射
    major_eval_rank = {
        'A+': 5,
        'A': 4,
        'A-': 3,
        'B+': 2,
        'B': 1,
        'B-': 0,
        'C+': -1,
        'C': -2,
        'C-': -3
    }
    def sort_key(item):
        # 获取院校层级并转换为数字排名
        school_level = item.get('school_level_display', '普通院校')
        level_score = college_level_rank.get(school_level, 0)

        # 获取专业评级并转换为数字评分
        major_eval = item.get('major_eval', '')
        eval_score = major_eval_rank.get(major_eval, -10)  # 默认最低

        # 排序键：负值保证降序排列
        return -level_score, -eval_score

    return sorted(data, key=sort_key)