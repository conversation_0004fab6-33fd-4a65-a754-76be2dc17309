--
-- <PERSON>reate model EnglishPaperExplain
--
CREATE TABLE `ai_english_paper_explain` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_id` varchar(100) NOT NULL, `answer_id` varchar(100) NOT NULL, `task_id` varchar(100) NOT NULL, `summary_req_params` json NULL, `summary_status` varchar(100) NOT NULL, `summary_content` json NULL, `status` varchar(100) NOT NULL, `fail_reason` longtext NULL);
--
-- <PERSON>reate model EnglishPaperQuestionExplain
--
CREATE TABLE `ai_english_paper_question_explain` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `question_id` varchar(100) NOT NULL, `question_type` varchar(100) NOT NULL, `req_params` json NULL, `sub_question_num` integer NOT NULL, `sub_right_question_num` integer NOT NULL, `status` varchar(100) NOT NULL, `explain_content` json NULL, `paper_explain_id` bigint NOT NULL);
CREATE INDEX `ai_english_paper_explain_add_time_86e0c385` ON `ai_english_paper_explain` (`add_time`);
CREATE INDEX `ai_english_paper_explain_user_id_885bb0a5` ON `ai_english_paper_explain` (`user_id`);
CREATE INDEX `ai_english_paper_explain_answer_id_2172a63d` ON `ai_english_paper_explain` (`answer_id`);
CREATE INDEX `ai_english_paper_explain_task_id_54f6557f` ON `ai_english_paper_explain` (`task_id`);
CREATE INDEX `ai_english_paper_question_explain_add_time_ddad3e5b` ON `ai_english_paper_question_explain` (`add_time`);
CREATE INDEX `ai_english_paper_question_explain_question_id_2294b937` ON `ai_english_paper_question_explain` (`question_id`);
CREATE INDEX `ai_english_paper_question_explain_paper_explain_id_d70dddb0` ON `ai_english_paper_question_explain` (`paper_explain_id`);