from django.utils import timezone
from django.db import transaction

from app.models.en_word_recite import *


def modify_user_recite_history(user_id, day):
    assert day < 0
    today = timezone.now()
    yesterday = today + timezone.timedelta(days=day)
    plan: EnWordRecitePlan = EnWordRecitePlan.objects.filter(is_deleted=False, user_id=user_id).first()
    assert plan

    with ((transaction.atomic())):
        # plan
        plan.modified_time = yesterday
        plan.add_time = yesterday
        plan.last_plan_gen_date = yesterday.date()
        plan.save(update_fields=['modified_time', 'add_time', 'last_plan_gen_date'])

        # plan_record
        plan_record: EnWordRecitePlanRecord = plan.enwordreciteplanrecord_set.last()
        assert plan_record
        plan_record.modified_time = yesterday
        plan_record.add_time = yesterday
        plan_record.save(update_fields=['modified_time', 'add_time'])

        # day plan
        day_plan: EnWordReciteDayPlan = plan_record.enwordrecitedayplan_set.last()
        day_plan.modified_time = yesterday
        day_plan.add_time = yesterday
        day_plan.day = yesterday.date()
        day_plan.save(update_fields=['modified_time', 'add_time', 'day'])

        # EnWordReciteDayRecord
        day_plan.enwordrecitedayrecord_set.update(
            add_time=yesterday,
            modified_time=yesterday,
            day=yesterday.date()
        )



