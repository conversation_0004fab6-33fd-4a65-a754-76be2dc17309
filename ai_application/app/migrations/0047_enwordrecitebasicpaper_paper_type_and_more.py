# Generated by Django 4.2 on 2025-04-16 14:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0046_enwordreciteplan_last_plan_gen_date_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='enwordrecitebasicpaper',
            name='paper_type',
            field=models.CharField(choices=[('basic_test', '摸底测'), ('week_test', '周测')], default='basic_test', max_length=32, verbose_name='试卷类型'),
        ),
        migrations.AddField(
            model_name='enwordrecitebasicpaper',
            name='user_id',
            field=models.CharField(db_index=True, default=None, max_length=100, verbose_name='用户id'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='enwordrecitequestion',
            name='question_type',
            field=models.CharField(choices=[('en2ch', '英译中')], default='en2ch', max_length=16, verbose_name='问题类型'),
        ),
    ]
