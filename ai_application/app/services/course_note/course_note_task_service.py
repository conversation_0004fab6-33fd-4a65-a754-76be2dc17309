from django.conf import settings
from django.db import transaction
from django.utils import timezone

from app.api.dto import ChatMessageDto
from app.core.features.course_note_split import CourseNoteSplitFeature
from app.models import (
    CourseNoteTask, DatasetDocument, CourseVideoContent, CourseNoteTaskChangeDetail, Account,
    CourseNoteContent, PromptTemplate
)
from app.services.app_generate_service import AppGenerateService
from app.services.course_note.utils import parse_subtitle_dict
from app.utils import generate_text_hash


class CourseNoteTaskService:

    @classmethod
    def process_task(cls, task: CourseNoteTask):
        document_nos = task.document_nos_list
        if not document_nos:
            task.processing_started_at = timezone.now()
            task.completed_at = timezone.now()
            task.status = 'success'
            task.save()
            return

        documents = DatasetDocument.objects.filter(is_deleted=False, document_no__in=document_nos)

        notes_map = {}
        if not task.task_debug:
            notes = CourseNoteContent.objects.filter(
                is_deleted=False, course_lecture_id=task.course_lecture_id, document_no__in=document_nos)
            notes_map = {note.document_no: note for note in notes}

        video_document_map = {}
        for d in documents:
            video_content_id = d.data_source_info.get('video_content_id')
            if video_content_id:
                video_document_map[video_content_id] = d

        video_content_ids = list(video_document_map.keys())
        video_contents = CourseVideoContent.objects.filter(is_deleted=False, id__in=video_content_ids)

        task_changes = []
        feature = CourseNoteSplitFeature(task.chapter_lecture)

        # 由于目前一次提交的是一章的内容，所以需要判断在该章内讲义是否重复，如重复，则生成笔记的时候不采用讲义生成
        video_lecture_hash_list = []
        for video_content in video_contents:
            video_document = video_document_map.get(video_content.id)
            if not video_document:
                continue

            video_content_text = video_content.get_content()
            if not video_content_text:
                continue

            video_lecture = feature.run(video_content)
            video_lecture_hash = generate_text_hash(video_lecture)
            if video_lecture_hash in video_lecture_hash_list:
                is_use_lecture = False
            else:
                video_lecture_hash_list.append(video_lecture_hash)
                is_use_lecture = True

            # 比较讲义是否有改动
            if not task.task_debug:
                # 如果使用讲义，则比较讲义是否一致
                if is_use_lecture:
                    note: CourseNoteContent = notes_map.get(video_document.document_no)
                    # 如果讲义一致，则笔记不重新生成
                    if note and note.video_lecture == video_lecture:
                        continue

            task_changes.append(CourseNoteTaskChangeDetail(
                task=task,
                video_content=video_content,
                document_no=video_document.document_no,
                video_lecture=video_lecture,
                is_use_lecture=is_use_lecture,
            ))

        with transaction.atomic():
            task.processing_started_at = timezone.now()
            task.status = 'processing'
            task.save()
            if task_changes:
                CourseNoteTaskChangeDetail.objects.bulk_create(task_changes)

    @classmethod
    def process_course_video_note_task(cls, task_detail: CourseNoteTaskChangeDetail):
        # subtitle_list = parse_subtitle_dict()

        video_lecture = task_detail.video_lecture if task_detail.is_use_lecture else ''
        inputs = {
            'chapter_name': task_detail.task.chapter_name,
            'section_name': task_detail.video_content.name,
            'chapter_note': video_lecture,
            'video_content_id': task_detail.video_content_id,
            # 'subtitle': task_detail.video_content.get_content(),
            # 'subtitle_dict': ','.join(subtitle_list),
        }
        if settings.ENVIRONMENT == settings.ENV_TEST and task_detail.task.task_debug:
            prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no='lecture_note_generation').first()
            inputs['lecture_note_generation'] = prompt_template.get_debug_prompt_content()

        chat_dto = ChatMessageDto(
            app_id='lecture_note_generation',
            query="",
            stream=True,
            inputs=inputs,
            is_async=True
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')

        task_detail.status = 'processing'
        task_detail.message_id = response.get('message_int_id')
        task_detail.processing_started_at = timezone.now()
        task_detail.save()

    @classmethod
    def save_video_note(cls, task_detail: CourseNoteTaskChangeDetail):
        note_content: CourseNoteContent = CourseNoteContent.objects.filter(
            course_lecture_id=task_detail.task.course_lecture_id,
            document_no=task_detail.document_no
        ).first()

        video_lecture = task_detail.video_lecture if task_detail.is_use_lecture else ''
        if note_content:
            note_content.video_lecture = video_lecture
            note_content.video_note = task_detail.video_note
            note_content.save()
        else:
            note_task = task_detail.task
            CourseNoteContent.objects.create(
                course_id=note_task.course_id,
                course_lecture_id=note_task.course_lecture_id,
                chapter_id=note_task.chapter_id,
                chapter_name=note_task.chapter_name,
                video_content=task_detail.video_content,
                document_no=task_detail.document_no,
                video_lecture=video_lecture,
                video_note=task_detail.video_note,
            )
