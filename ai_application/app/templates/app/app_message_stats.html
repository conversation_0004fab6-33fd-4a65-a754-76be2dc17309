<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知舟·应用统计看板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .row {
            display: flex;
            gap: 20px;
        }
        .box {
            flex: 1;
            padding: 20px;
            border-radius: 8px;
            background: #e9ecef;
            text-align: center;
        }
        
        h2 {
            color: #333;
            border-left: 5px solid rgb(169, 168, 250);
            padding-left: 10px;
        }

        .hover-text {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .hover-text:hover::after {
            content: attr(data-text);
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 5px;
            z-index: 999;
            font-size: smaller;
        }
    </style>
</head>

<body>
<div class="container">
    <h1>知舟·应用统计看板</h1>
    <div class="row">
        <div class="box box-left">
    <h2>教辅应用</h2>
    <table>
        <tr>
            <th style="width: 200px">应用名称</th>
            <th>消息总数</th>
            <th>异常消息数量</th>
            <th>今日异常消息数量({{ today }})</th>
        </tr>
        {% for app in outer_app_list %}
            <tr>
                <td>
                    <a class="hover-text" data-text="查看每日数据" href="{% url 'app_msg_daily_stats' app.app_type %}">
                        {{ app.name }}
                    </a>
                    {% if app.message_type %}
{#                        <a class="hover-text" data-text="查看每日数据" href="{% url 'app_msg_daily_type_stats' app.app_type app.message_type %}">#}
{#                            {{ app.name }}#}
{#                        </a>#}
                    {% else %}
{#                        <a class="hover-text" data-text="查看每日数据" href="{% url 'app_msg_daily_stats' app.app_type %}">#}
{#                            {{ app.name }}#}
{#                        </a>#}
                    {% endif %}
                </td>

                <td>
                    <a href="{% url 'app_message_list' app.app_type %}">{{ app.total_count }}</a>

{#                    {% if app.message_type %}#}
{#                        <a href="{% url 'app_message_type_list' app.id app.message_type %}">{{ app.total_count }}</a>#}
{#                    {% else %}#}
{#                        <a href="{% url 'app_message_list' app.id %}">{{ app.total_count }}</a>#}
{#                    {% endif %}#}
                </td>
                <td>
                <a href="{% url 'app_exception_message_list' app.app_type %}">{{ app.exception_count }}</a>
{#                    {% if app.message_type %}#}
{#                        <a href="{% url 'app_exception_message_type_list' app.app_type app.message_type %}">#}
{#                            {{ app.exception_count }}</a>#}
{#                    {% else %}#}
{#                        <a href="{% url 'app_exception_message_list' app.app_type %}">#}
{#                            {{ app.exception_count }}#}
{#                        </a>#}
{#                    {% endif %}#}
                </td>
                <td>
                    <a href="{% url 'app_daily_exception_message_list' app.app_type %}">{{ app.today_exception_count }}</a>
{#                    {% if app.message_type %}#}
{#                        <a href="{% url 'app_daily_exception_message_type_list' app.app_type app.message_type %}">#}
{#                            {{ app.today_exception_count }}</a>#}
{#                    {% else %}#}
{#                        <a href="{% url 'app_daily_exception_message_list' app.app_type %}">#}
{#                            {{ app.today_exception_count }}#}
{#                        </a>#}
{#                    {% endif %}#}
                </td>
            </tr>
        {% endfor %}
    </table>
    </div>
    <div class="box box-right">
    <h2><strong>办公应用</strong></h2>
    <table>
        <tr>
            <th style="width: 200px">应用名称</th>
            <th>消息总数</th>
            <th>异常消息数量</th>
            <th>今日异常消息数量({{ today }})</th>
        </tr>
        {% for app in inner_app_list %}
            <tr>
                <td>
                    <a class="hover-text" data-text="查看每日数据" href="{% url 'app_msg_daily_stats' app.id %}">
                        {{ app.name }}
                    </a>
                </td>
                <td>
                    <a href="{% url 'app_message_list' app.id %}">{{ app.total_count }}</a>
                </td>
                <td>
                    <a href="{% url 'app_exception_message_list' app.id %}">{{ app.exception_count }}</a>
                </td>
                <td>
                    <a href="{% url 'app_daily_exception_message_type_list' app.id app.message_type %}">
                            {{ app.today_exception_count }}</a>
                </td>
            </tr>
        {% endfor %}
    </table>
    </div>
    </div>
</div>
</body>

</html>