from django.utils.module_loading import import_string

from app.constants.app import AppMode
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.apps.base_app_runner import AppRunner
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.model_manager import ModelInstance
from app.core.workflow import WORKFLOW_CLASS_MAP
from app.core.workflow.base_workflow import BaseWorkflow
from app.core.workflow.chat_grammar_workflow import GrammarWorkflow
from app.core.workflow.college_analysis_new_workflow import CollegeAnalysisNewWorkflow, \
    CollegeAnalysisNewWithGoalWorkflow
from app.core.workflow.college_analysis_workflow import CollegeAnalysisWorkflow
from app.core.workflow.doc_ai_create_workflow import DocAICreateWorkflow
from app.core.workflow.kaoyan_review_plan_workflow import KaoYanReviewPlanWorkflow
from app.core.workflow.knowledge_workflow import KnowledgeWorkflow
from app.core.workflow.red_generator_workflow import RedGeneratorWorkflow
from app.core.workflow.learning_report_generator_workflow import LearningReportGeneratorWorkflow
from app.core.workflow.lecture_note_workflow import LectureN<PERSON>WorkFlow
from app.core.workflow.math_problem_solving_workflow import MathProblemSolvingWorkflow
from app.core.workflow.math_video_abstract import MathVideoAbstractWorkflow
from app.core.workflow.math_video_knowledge_extract import MathVideoKnowledgeExtractWorkflow
from app.core.workflow.note_generator_workflow import ChapterNoteWorkflow
from app.core.workflow.question_knowledge_extract_workflow import QuestionKnowledgeExtractWorkflow
from app.core.workflow.test_answer_report_generator_workflow import TestAnswerReportGeneratorWorkflow
from app.core.workflow.waikan_question_first_report import WaikanQuestionFirstReportWorkflow
from app.core.workflow.exercises_learn_status_workflow import ExercisesLearnStatusWorkflow
from app.core.workflow.supervise_learn_status_workflow import SuperviseLearnStatusWorkflow, SuperviseLearnStatus2Workflow, SuperviseLearnStatusNoPlanWorkflow, SuperviseLearnStatusNoPlan2Workflow
from app.core.workflow.code_exercise_workflow import CodeExerciseWorkflow
from app.core.workflow.knowledge_analysis_workflow import KnowledgeAnalysisWorkflow
from app.core.workflow.supervise_init_status_generator import SuperviseInitStatusWorkflow
from app.core.workflow.supervise_plan_scheme_workflow import SuperviseLearnPlanSchemeWorkflow
from app.errors import ContainSensitiveError
from app.models import Message


class CompletionAppRunner(AppRunner):
    def run(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            queue_manager: AppQueueManager,
            message: Message,
    ) -> None:
        # 统一处理敏感词
        if message.is_sensitive:
            raise ContainSensitiveError(detail=message.answer)

        workflow_classname = WORKFLOW_CLASS_MAP.get(application_generate_entity.app_type)
        if workflow_classname:
            workflow_ins: BaseWorkflow = import_string(workflow_classname)(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            )
            return workflow_ins.run()

        if application_generate_entity.app_type == 'knowledge_query':
            return KnowledgeWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'knowledge_analysis_simple':
            return KnowledgeAnalysisWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'red_generator':
            return RedGeneratorWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'complex_sentence_analysis':
            return GrammarWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'chapter_note_generation':
            return ChapterNoteWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'lecture_note_generation':
            return LectureNoteWorkFlow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'math_problem_solving':
            return MathProblemSolvingWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'learning_report_generator':
            return LearningReportGeneratorWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'math_video_abstract':
            return MathVideoAbstractWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'math_video_knowledge_extract':
            return MathVideoKnowledgeExtractWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'question_knowledge_extract':
            return QuestionKnowledgeExtractWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'test_answer_report':
            return TestAnswerReportGeneratorWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'waikan_question_first_report':
            return WaikanQuestionFirstReportWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'waikan_question_first_report':
            return WaikanQuestionFirstReportWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'college_analysis':
            return CollegeAnalysisWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'college_analysis_new':
            return CollegeAnalysisNewWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'college_analysis_new_with_goal':
            return CollegeAnalysisNewWithGoalWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'kaoyan_review_plan':
            return KaoYanReviewPlanWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'dsx_learning_stat':
            return ExercisesLearnStatusWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'dsx_code_exercise':
            return CodeExerciseWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'supervise_learn_stat':
            return SuperviseLearnStatusWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'supervise_learn_stat2':
            return SuperviseLearnStatus2Workflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'supervise_learn_stat_no_plan':
            return SuperviseLearnStatusNoPlanWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'supervise_learn_stat_no_plan2':
            return SuperviseLearnStatusNoPlan2Workflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'knowledge_analysis_simple':
            return KnowledgeAnalysisWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'supervise_init_undergraduated_st':
            return SuperviseInitStatusWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        elif application_generate_entity.app_type == 'supervise_plan_scheme':
            return SuperviseLearnPlanSchemeWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager
            ).run()
        elif application_generate_entity.app_type == 'document_proofreader_ai_create':
            return DocAICreateWorkflow(
                application_generate_entity=application_generate_entity,
                message=message,
                queue_manager=queue_manager,
            ).run()
        pre_prompt = application_generate_entity.pre_prompt
        query = application_generate_entity.query
        query_in_prompt = application_generate_entity.query_in_prompt
        prompt_messages = self.organize_prompt_messages(
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=query,
            files=application_generate_entity.file_objs,
            query_in_prompt=query_in_prompt,
        )

        self.get_pre_calculate_rest_tokens(application_generate_entity.model_conf, prompt_messages)

        model_instance = ModelInstance(
            provider_model_bundle=application_generate_entity.model_conf.provider_model_bundle,
            model=application_generate_entity.model_conf.model
        )
        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            model_parameters=application_generate_entity.model_conf.model_params,
            stream=application_generate_entity.stream
        )

        self._handle_invoke_result(
            invoke_result=invoke_result,
            queue_manager=queue_manager,
            stream=application_generate_entity.stream
        )
