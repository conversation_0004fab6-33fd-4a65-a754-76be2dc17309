import json
import logging
import os
import re
from collections import defaultdict

from django.conf import settings

from app.core.features.question_knowledge_extract.math_question_convert import MathQuestionConvertFeature
from app.core.features.question_knowledge_extract.math_question_knowledge_extract import \
    MathQuestionKnowledgeExtractFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import QuestionNotFoundError
from app.models import QuestionKnowledge, KnowledgeLibrary
from app.services.main_subject.new_question_service import NewQuestionService
from app.services.main_subject.test_paper_service import TestPaperService

logger = logging.getLogger(__name__)


class QuestionKnowledgeExtractWorkflow(BaseWorkflow):

    def get_math_content(self, question_id):
        try:
            # 获取问题原始信息
            question_content = TestPaperService.get_question_content_by_vector(question_id)
        except QuestionNotFoundError as e:
            logger.error(f'question:[{question_id}] not found')
            logger.exception(e)
            data = {'error': e.detail}
            self._publish_workflow_custom_message(json.dumps(data, ensure_ascii=False))
            self._publish_workflow_end_message()
            return

        q_k_obj: QuestionKnowledge = QuestionKnowledge.objects.filter(is_deleted=False, question_id=question_id).first()
        if q_k_obj and q_k_obj.question_content:
            new_question_content = q_k_obj.question_content
        else:
            new_question_content = self._convert_math_question(question_content)
            QuestionKnowledge.objects.update_or_create(
                is_deleted=False, question_id=question_id, defaults={
                    'question_content': new_question_content
                }
            )

        return new_question_content

    def get_math_knowledge(self):
        main_subject_map = {'high_math': '高等数学', 'linear_algebra': '线性代数', 'math_prob': '概率论与数理统计'}

        knowledge_list_arr = []
        all_knowledge_list = []
        for code, name in main_subject_map.items():
            subject_knowledge_list = self._get_subject_knowledge_list(code)
            all_knowledge_list.extend(subject_knowledge_list)
            subject_knowledge_str = '\n'.join(subject_knowledge_list)
            knowledge_list_arr.append(f'### {name}\n{subject_knowledge_str}')
        knowledge_list_str = '\n\n'.join(knowledge_list_arr)
        return all_knowledge_list, knowledge_list_str

    def get_408_knowledge(self):
        main_subject_map = {
            'data_structure': '数据结构',
            'organization': '组成原理',
            'network': '计算机网络',
            'os': '操作系统',
        }

        knowledge_list_arr = []
        all_knowledge_list = []

        qs = KnowledgeLibrary.objects.filter(
            subject_domain__main_subject_code__in=list(main_subject_map.keys()),
            is_deleted=False, nature='major'
        ).values('subject_domain__main_subject_code', 'name')
        subject_group = defaultdict(list)
        for i in qs:
            subject_group[i['subject_domain__main_subject_code']].append(i['name'])

        for code, name in main_subject_map.items():
            subject_knowledge_list = subject_group.get(code)
            all_knowledge_list.extend(subject_knowledge_list)
            subject_knowledge_str = '\n'.join(subject_knowledge_list)
            knowledge_list_arr.append(f'### {name}\n{subject_knowledge_str}')
        knowledge_list_str = '\n\n'.join(knowledge_list_arr)
        return all_knowledge_list, knowledge_list_str

    def run(self):
        # 目前仅解决数学
        # 查询题目信息
        main_subject_map = {
            'high_math': '高等数学',
            'linear_algebra': '线性代数',
            'math_prob': '概率论与数理统计',
            'data_structure': '数据结构',
            'organization': '组成原理',
            'network': '计算机网络',
            'os': '操作系统',
        }

        print('🚀', 1111111)
        question_id = self.application_generate_entity.query
        subject = self.application_generate_entity.inputs.get('subject')

        if not subject or subject == 'math':
            new_question_content = self.get_math_content(question_id)
            all_knowledge_list, knowledge_list_str = self.get_math_knowledge()
        else:
            data = NewQuestionService.get_question_detail(question_id)
            new_question_content = data['question']['question']
            QuestionKnowledge.objects.update_or_create(
                is_deleted=False, question_id=question_id, defaults={
                    'question_content': new_question_content
                }
            )

            all_knowledge_list, knowledge_list_str = self.get_408_knowledge()

        res = self._extract_math_knowledge(new_question_content, knowledge_list_str)
        # 处理返回json
        try:
            kg_list = self._parse_json_list(res)
        except:
            kg_list = []

        data = []
        for k in kg_list:
            if k['knowledge'] in all_knowledge_list:
                data.append(k)

        self._publish_workflow_custom_message(json.dumps(data, ensure_ascii=False))
        self._publish_workflow_end_message()

    def _parse_json_list(self, text):
        pattern = r'\[.*?\]'
        match = re.search(pattern, text, re.DOTALL)

        if match:
            json_str = match.group()
            try:
                # 将提取的 JSON 字符串解析为 Python 对象
                return json.loads(json_str)
            except json.JSONDecodeError as e:
                logger.exception(e)
                return []
        else:
            return []

    def _convert_math_question(self, question_content) -> str:
        result = MathQuestionConvertFeature().run(
            self.application_generate_entity,
            question_content=question_content
        )
        tracing_info = {
            'tracing_type': 'math_question_convert',
            'query': question_content
        }
        self.tracing_log.add_tracing_by_llm_result(
            tracing_type=tracing_info.get('tracing_type'),
            query=tracing_info.get('query'),
            llm_result=result
        )
        return result.message.content

    def _extract_math_knowledge(self, question_content, knowledge_list_str):
        result = MathQuestionKnowledgeExtractFeature().run(
            self.application_generate_entity,
            knowledge_list_str=knowledge_list_str,
            question_content=question_content
        )

        tracing_info = {
            'tracing_type': 'math_question_knowledge_extract',
            'query': question_content
        }
        self.tracing_log.add_tracing_by_llm_result(
            tracing_type=tracing_info.get('tracing_type'),
            query=tracing_info.get('query'),
            llm_result=result
        )
        return result.message.content

    def _get_subject_knowledge_list(self, code) -> list:
        knowledge_json_file = settings.BASE_DIR.joinpath(f'knowledge_list/{code}.json')
        if not os.path.exists(knowledge_json_file):
            raise Exception('main_subject path is valid')

        try:
            with open(knowledge_json_file, 'r') as f:
                knowledge_list = json.loads(f.read())
        except:
            raise Exception('knowledge_list is valid')
        return [i['name'] for i in knowledge_list]
