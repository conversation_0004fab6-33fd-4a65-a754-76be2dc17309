# Generated by Django 4.2 on 2025-07-29 07:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0061_citygdp_kaoyanqueryresult_majorinfo_majorrelation_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CoursePackageContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('unit_name', models.CharField(db_index=True, max_length=36, verbose_name='单元名称')),
                ('subject', models.Char<PERSON><PERSON>(max_length=36, null=True, verbose_name='学科')),
                ('course_package_name', models.CharField(max_length=36, null=True, verbose_name='课包名称')),
                ('stage_name', models.CharField(max_length=36, null=True, verbose_name='阶段名称')),
                ('combine_unit_name', models.CharField(max_length=36, null=True, verbose_name='组合单元名称')),
                ('study_target', models.TextField(null=True, verbose_name='学习目标')),
                ('study_guidence', models.TextField(null=True, verbose_name='学习指导')),
                ('unit_task', models.TextField(null=True, verbose_name='单元任务')),
            ],
            options={
                'verbose_name': '往年课包内容',
                'verbose_name_plural': '往年课包内容',
                'db_table': 'ai_course_package_content',
            },
        ),
        migrations.CreateModel(
            name='SuperViseInitStudentStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(db_index=True, max_length=100, verbose_name='用户ID')),
                ('date', models.DateField(auto_now_add=True, verbose_name='填写日期')),
                ('exam_date', models.DateField(verbose_name='考试日期')),
                ('graduation_major', models.CharField(blank=True, max_length=100, null=True, verbose_name='毕业专业')),
                ('graduation_school', models.CharField(blank=True, max_length=100, null=True, verbose_name='毕业院校')),
                ('graduation_major_code', models.CharField(blank=True, max_length=100, null=True, verbose_name='毕业专业代码')),
                ('graduation_school_code', models.CharField(blank=True, max_length=100, null=True, verbose_name='毕业院校代码')),
                ('target', models.JSONField(blank=True, default=list, null=True, verbose_name='目标院校基本信息')),
                ('education_level', models.CharField(blank=True, max_length=100, null=True, verbose_name='学历层次')),
                ('study_status', models.CharField(blank=True, max_length=100, null=True, verbose_name='在读状态')),
                ('graduation_years', models.CharField(blank=True, max_length=100, null=True, verbose_name='毕业年限')),
                ('study_stage', models.CharField(blank=True, max_length=100, null=True, verbose_name='学习阶段')),
                ('academic_performance', models.CharField(blank=True, max_length=255, null=True, verbose_name='在校成绩评价')),
                ('english_level', models.CharField(blank=True, max_length=100, null=True, verbose_name='英语能力等级')),
                ('math_subjects', models.JSONField(blank=True, default=list, null=True, verbose_name='学过的数学科目')),
                ('math_mastery', models.CharField(blank=True, max_length=255, null=True, verbose_name='数学掌握情况')),
                ('political_subjects', models.JSONField(blank=True, default=list, null=True, verbose_name='学过的政治科目')),
                ('political_mastery', models.CharField(blank=True, max_length=255, null=True, verbose_name='政治掌握情况')),
                ('task_id', models.CharField(blank=True, default=None, max_length=36, null=True, unique=True, verbose_name='任务ID')),
                ('fail_reason', models.TextField(blank=True, null=True, verbose_name='失败原因')),
                ('query', models.TextField(blank=True, null=True, verbose_name='请求内容')),
                ('analysis', models.TextField(blank=True, null=True, verbose_name='报告内容')),
                ('status', models.CharField(blank=True, max_length=100, null=True, verbose_name='解析状态')),
            ],
            options={
                'verbose_name': '教务督学用户初始化状态',
                'verbose_name_plural': '教务督学用户初始化状态',
                'db_table': 'ai_student_supervise_init_status',
            },
        ),
        migrations.CreateModel(
            name='SuperviseLearnStageStat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(db_index=True, max_length=100, verbose_name='用户ID')),
                ('course_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='课程ID')),
                ('task_id', models.CharField(blank=True, default=None, max_length=36, null=True, unique=True, verbose_name='任务id')),
                ('status', models.CharField(blank=True, max_length=100, null=True, verbose_name='解析状态')),
                ('fail_reason', models.TextField(null=True, verbose_name='失败原因')),
                ('query', models.TextField(null=True, verbose_name='请求内容')),
                ('analysis', models.TextField(null=True, verbose_name='报告内容')),
            ],
            options={
                'verbose_name': '教务督学学习阶段记录',
                'verbose_name_plural': '教务督学学习阶段记录',
                'db_table': 'ai_student_supervise_learn_stage_stat',
            },
        ),
        migrations.AlterField(
            model_name='message',
            name='malicious_attack_type',
            field=models.IntegerField(blank=True, choices=[(0, '0'), (1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5'), (6, '6')], null=True, verbose_name='恶意攻击类型'),
        ),
    ]
