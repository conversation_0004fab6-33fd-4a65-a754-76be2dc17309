# Generated by Django 4.2 on 2025-03-11 08:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0025_coursevideocontent_is_subtitle_note_enabled_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CourseSectionKnowledge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('course_section_id', models.CharField(blank=True, max_length=100, verbose_name='课节ID')),
                ('subject', models.CharField(blank=True, max_length=100, verbose_name='考研科目')),
                ('main_subject', models.Char<PERSON>ield(blank=True, max_length=100, verbose_name='主科目')),
                ('subtitles', models.TextField(null=True, verbose_name='字幕内容')),
                ('subtitles_abstract', models.TextField(null=True, verbose_name='字幕摘要')),
                ('knowledge_list', models.JSONField(null=True, verbose_name='知识点列表')),
            ],
            options={
                'verbose_name': '课节知识点',
                'verbose_name_plural': '课节知识点',
                'db_table': 'ai_data_course_section_knowledge',
            },
        ),
        migrations.CreateModel(
            name='QuestionKnowledge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_id', models.CharField(blank=True, max_length=100, verbose_name='题目ID')),
                ('question_content', models.TextField(null=True, verbose_name='题目内容')),
                ('subject', models.CharField(blank=True, max_length=100, verbose_name='考研科目')),
                ('main_subject', models.CharField(blank=True, max_length=100, verbose_name='主科目')),
                ('knowledge_list', models.JSONField(null=True, verbose_name='知识点列表')),
            ],
            options={
                'verbose_name': '题目知识点',
                'verbose_name_plural': '题目知识点',
                'db_table': 'ai_data_question_knowledge',
            },
        ),
        migrations.AlterField(
            model_name='message',
            name='message_type',
            field=models.CharField(choices=[('normal', '普通消息'), ('question', '解题助手'), ('code', '代码优化'), ('math_question', '数学解题助手'), ('grammar', '英语长难句语法分析')], default='normal', max_length=32, verbose_name='消息类型'),
        ),
    ]
