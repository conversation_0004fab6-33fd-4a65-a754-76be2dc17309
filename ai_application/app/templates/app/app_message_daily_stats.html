<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用消息统计</title>

    <style>
        #selectZone {
            text-align: end;
        }
    </style>
</head>

<body>
<div class="container">
    <h1>
        <span>【{{ app_name }}】近{{ days }}天消息数据</span>
        <a style="font-size: 18px" href="{% url 'app_message_stats' %}">返回</a>
    </h1> <br>
    <div id="selectZone">
        <h3>选择查询的日期:</h3>
            <form action="
                {% if message_type %}
                    {% url 'app_msg_daily_type_stats' app_id message_type %}
                {% else %}
                    {% url 'app_msg_daily_stats' app_id %}
                {% endif %}
            ">
            <select name="days">
                {% if days == 7 %}
                    <option value="7" selected>近7天</option> {% else %}
                    <option value="7">近7天</option>{% endif %}
                {% if days == 15 %}
                    <option value="15" selected>近15天</option> {% else %}
                    <option value="15">近15天</option>{% endif %}
                {% if days == 30 %}
                    <option value="30" selected>近30天</option> {% else %}
                    <option value="30">近30天</option>{% endif %}
            </select> &nbsp;
            <button>查询</button>
        </form>
    </div>
    <div id="main" style="width:100%;height:400px;"></div>

</div>


<script src="https://cdn.kaoyanvip.cn/echarts.min.js"></script>
<script>
    function getDataSetEcharts() {
        const myChart = echarts.init(document.getElementById('main'));
        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                }
            },
            toolbox: {
                feature: {
                    dataView: {show: true, readOnly: false},
                    magicType: {show: true, type: ['line', 'bar']},
                    restore: {show: true},
                    saveAsImage: {show: true}
                }
            },
            legend: {
                data: ['正常消息', '异常消息']
            },
            xAxis: [
                {
                    type: 'category',
                    data: {{ date_list|safe }},
                    axisPointer: {
                        type: 'shadow'
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '数量',
                    min: 0,
                    max: {{ max_count|safe }},
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
            ],
            series: [
                {
                    name: '正常消息',
                    type: 'bar',
                    tooltip: {
                        valueFormatter: function (value) {
                            return value + ' 个';
                        }
                    },
                    data: {{ data_by_date_normal|safe }},
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                position: 'top',
                                textStyle: {
                                    color: 'black',
                                    fontsize: 20,
                                }
                            }
                        }
                    }
                },
                {
                    name: '异常消息',
                    type: 'bar',
                    tooltip: {
                        valueFormatter: function (value) {
                            return value + ' 个';
                        }
                    },
                    data: {{ data_by_date_abnormal|safe }},
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                position: 'top',
                                textStyle: {
                                    color: 'black',
                                    fontsize: 20,
                                }
                            }
                        }
                    }
                },
            ]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);

        myChart.on('click', function (obj) {
            let date_str = obj.name;
            let redirectUrl = "{% if message_type %}{% url 'app_msg_daily_detail' %}{% else %}{% url 'app_msg_daily_detail' %}{% endif %}";
            window.location = `${redirectUrl}?app_id={{ app_id }}&date=${date_str}&{% if message_type %}message_type={{ message_type }}{% endif %}`;
        });
    }

    document.onload = getDataSetEcharts()

</script>

</body>

</html>