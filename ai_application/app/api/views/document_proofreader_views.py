import json
import uuid
import logging

from app.models.document_proofreader import DocumentProofreaderAsyncTask
from app.services.document_proofreader.celery_tasks.document_proofreader_async_task import async_document_proofreader_task
from app.services.document_proofreader.document_AICreation import DocumentAICreationNewService
from django_ext.base_view import BaseView
from django_ext.response import make_response, make_stream_response
from django_ext.exceptions import InternalException

logger = logging.getLogger(__name__)


# =============================================================================
# 文档校对相关视图APIFox接口
# =============================================================================
class DocumentProofreaderView(BaseView):
    """文档校对视图"""
    def post(self, request):
        """处理文档校对请求 - 异步处理内容"""
        try:
            # 获取请求数据
            if request.content_type == 'application/json':
                data = json.loads(request.body)
                content = data.get('content', '').strip()
                file_name = data.get('file_name', 'document.md')  # 可选的文件名
                dictionary = data.get('dictionary', [])  # 词库列表
                convert_dict = data.get('convert', {})       # 转换规则
                user_session = data.get('user_session', str(uuid.uuid4()))
                subject_category = data.get('subject_category', '通用')
            
            # 验证内容
            if not content:
                raise InternalException(code=50000, detail='请提供要校对的内容')
            
            logger.info(f"📚 收到内容校对请求，内容长度: {len(content)} 字符，异步处理模式")
            
            # 创建异步任务记录
            task_id = f"proofreader_async_{uuid.uuid4().hex}"
            async_task = DocumentProofreaderAsyncTask.objects.create(
                task_id=task_id,
                file_name=file_name,
                original_content=content,
                user_session=user_session,
                notification_email='',  # 不需要邮箱
                status='pending'
            )
            
            # 提交到Celery队列
            async_document_proofreader_task.delay(
                task_id=task_id,
                file_name=file_name,
                original_content=content,
                file_extension='.md',  # 默认按markdown处理
                dictionary=dictionary,  # 传入词库
                convert=convert_dict,  # 传入转换规则
                subject_category=subject_category,  # 传入目标审校类别
            )
            
            logging.info(f"✅ 异步校对任务已创建，任务ID: {task_id}")
            
            return make_response({
                    'task_id': task_id,
                    'async_task_id': async_task.id,
                    'status': 'pending',
                    'message': '异步校对任务已启动'
                })
            
        except json.JSONDecodeError:
            raise InternalException(code=50000, detail='JSON格式错误')
        except Exception as e:
            raise InternalException(code=50000, detail=f'创建任务失败: {str(e)}')


class DocumentProofreaderAsyncStatusView(BaseView):
    """查询异步任务状态和结果"""
    
    def get(self, request):
        """根据任务ID查询异步任务状态和结果"""
        try:
            task_id = request.GET.get('task_id')
            if not task_id:
                raise InternalException(code=50000, detail='缺少任务ID参数')
            
            # 查找异步任务记录
            async_task = DocumentProofreaderAsyncTask.objects.filter(
                task_id=task_id, is_deleted=False
            ).first()
            
            if not async_task:
                raise InternalException(code=50000, detail='任务不存在')
            
            # 计算预估时间和检查超时
            content_length = len(async_task.original_content)
            estimated_seconds = ((content_length + 2000) // 2000) * 60 + 15 * 60  # 向上取整
            
            from django.utils import timezone
            elapsed_seconds = (timezone.now() - async_task.add_time).total_seconds()
            # is_timeout = elapsed_seconds > estimated_seconds and async_task.status in ['pending', 'processing']
            is_timeout = False
            
            # 构建基本任务信息
            task_info = {
                'task_id': async_task.task_id,
                'async_task_id': async_task.id,
                'file_name': async_task.file_name,
                'status': async_task.status,
                'content_length': content_length,
                'estimated_time': estimated_seconds,
                'elapsed_time': int(elapsed_seconds),
                'is_timeout': False,
                'message': ''
            }
            
            # 根据不同状态返回不同的信息
            if async_task.status == 'pending':
                if is_timeout:
                    task_info['message'] = f'任务处理超时！内容长度{content_length}字符，预估{estimated_seconds}秒，已等待{int(elapsed_seconds)}秒'
                return make_response(task_info)
            
            elif async_task.status == 'processing':
                if is_timeout:
                    task_info['message'] = f'任务处理超时！内容长度{content_length}字符，预估{estimated_seconds}秒，已处理{int(elapsed_seconds)}秒'
                return make_response(task_info)
            
            elif async_task.status == 'failed':
                task_info['fail_reason'] = async_task.fail_reason
                task_info['retry_times'] = async_task.retry_times
                task_info['can_retry'] = async_task.can_retry()
                
                return make_response(task_info)
            
            elif async_task.status == 'success':
                # 任务成功，返回详细的校对结果
                if not async_task.proofreader:
                    error_list = []
                else:
                
                    proofreader = async_task.proofreader
                    
                    # 获取错误列表
                    error_list = list(proofreader.documentproofreadererror_set.filter(is_deleted=False).values(
                        'error_id', 'error_text', 'error_reason', 'error_suggestion', 'error_type'
                    ))
                    
                    # 重命名字段以匹配前端期望
                    for error in error_list:
                        error['id'] = error.pop('error_id')
                
                # 完整的结果信息
                result_data = {
                    **task_info,
                    'proofreader_id': proofreader.id,
                    'labeled_text': proofreader.labeled_content,
                    'corrected_text': proofreader.corrected_content,
                    'error_list': error_list,
                    'error_count': len(error_list),
                    'completion_time': proofreader.modified_time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                return make_response(result_data)
            
            else:
                raise InternalException(code=50000, detail=f'未知的任务状态: {async_task.status}')
                
        except Exception as e:
            raise InternalException(code=50000, detail=f'查询任务状态失败: {str(e)}')


class DocumentProofreaderAsyncBatchStatusView(BaseView):
    """批量查询异步任务状态"""
    
    def post(self, request):
        """批量查询多个任务的状态"""
        try:
            data = json.loads(request.body) if request.content_type == 'application/json' else {}
            task_ids = data.get('task_ids', [])
            
            if not task_ids or not isinstance(task_ids, list):
                raise InternalException(code=50000, detail='请提供任务ID列表')
            
            if len(task_ids) > 50:  # 限制批量查询数量
                raise InternalException(code=50000, detail='一次最多查询50个任务')
            
            # 查询所有任务
            async_tasks = DocumentProofreaderAsyncTask.objects.filter(
                task_id__in=task_ids, is_deleted=False
            )
            
            results = []
            found_task_ids = set()
            
            for async_task in async_tasks:
                found_task_ids.add(async_task.task_id)
                
                task_info = {
                    'task_id': async_task.task_id,
                    'async_task_id': async_task.id,
                    'file_name': async_task.file_name,
                    'status': async_task.status,
                    'create_time': async_task.add_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'update_time': async_task.modified_time.strftime('%Y-%m-%d %H:%M:%S'),
                }
                
                # 根据状态添加额外信息
                if async_task.status == 'failed':
                    task_info.update({
                        'fail_reason': async_task.fail_reason,
                        'retry_times': async_task.retry_times,
                        'can_retry': async_task.can_retry()
                    })
                elif async_task.status == 'success' and async_task.proofreader:
                    error_count = async_task.proofreader.documentproofreadererror_set.filter(is_deleted=False).count()
                    task_info.update({
                        'proofreader_id': async_task.proofreader.id,
                        'error_count': error_count,
                        'completion_time': async_task.proofreader.modified_time.strftime('%Y-%m-%d %H:%M:%S')
                    })
                
                results.append(task_info)
            
            # 检查未找到的任务ID
            not_found_task_ids = [tid for tid in task_ids if tid not in found_task_ids]
            
            return make_response({
                'status': 'success',
                'data': {
                    'tasks': results,
                    'found_count': len(results),
                    'not_found_task_ids': not_found_task_ids
                }
            })
            
        except json.JSONDecodeError:
            raise InternalException(code=50000, detail='JSON格式错误')
        except Exception as e:
            raise InternalException(code=50000, detail=f'批量查询失败: {str(e)}')


class DocumentAICreationView(BaseView):
    """AI内容生成视图"""
    
    def post(self, request, *args, **kwargs):
        """处理AI内容生成请求"""
        data = request.data
        # 获取参数
        prompt = data.get('prompt', '').strip()
        selected_text = data.get('selected_text', '').strip()

        response = DocumentAICreationNewService.generate_content(prompt, selected_text, request.user)
        return make_stream_response(response)
