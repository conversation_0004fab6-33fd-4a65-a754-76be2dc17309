import os
import django
import csv
from collections import defaultdict

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()

from app.models import GraduateMajorInfo


def import_graduate_major_info():
    """
    导入研究生专业信息数据
    只处理本科二级门类为"0101哲学类"的数据
    """
    csv_file_path = '专业列表(含一级二级三级和关联度).csv'
    
    # 用于去重的集合，存储 (研究生一级学科, 研究生二级门类代码, 研究生三级专业代码) 的组合
    processed_records = set()
    
    # 用于存储二级门类信息，避免重复处理
    second_categories = {}
    
    created_count = 0
    
    with open(csv_file_path, mode='r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        for row in reader:
            # 只处理本科二级门类为"0101哲学类"的数据
            undergraduate_second_category = row.get('本科二级门类', '').strip()
            if undergraduate_second_category != '0101哲学类':
                continue
            
            # 获取研究生相关字段
            graduate_first_discipline = row.get('研究生一级学科', '').strip()
            graduate_second_category_code = row.get('研究生二级门类代码', '').strip()
            graduate_second_category = row.get('研究生二级门类', '').strip()
            graduate_third_major_code = row.get('研究生三级专业代码', '').strip()
            graduate_third_major = row.get('研究生三级专业', '').strip()
            
            # 跳过空的研究生信息
            if not graduate_first_discipline or not graduate_second_category_code or not graduate_second_category:
                continue
            
            # 存储二级门类信息
            if graduate_second_category_code not in second_categories:
                second_categories[graduate_second_category_code] = {
                    'graduate_first_discipline': graduate_first_discipline,
                    'graduate_second_category': graduate_second_category
                }
            
            # 如果有三级专业信息，创建记录
            if graduate_third_major_code and graduate_third_major:
                record_key = (graduate_first_discipline, graduate_second_category_code, graduate_third_major_code)
                
                if record_key not in processed_records:
                    GraduateMajorInfo.objects.create(
                        graduate_first_discipline=graduate_first_discipline,
                        graduate_second_category_code=graduate_second_category_code,
                        graduate_second_category=graduate_second_category,
                        graduate_third_major_code=graduate_third_major_code,
                        graduate_third_major=graduate_third_major
                    )
                    processed_records.add(record_key)
                    created_count += 1
                    print(f"✅ 创建三级专业记录: {graduate_first_discipline} -> {graduate_second_category} -> {graduate_third_major}")
    
    # 为每个二级门类创建一个通用记录（二级门类代码后加00）
    for second_code, info in second_categories.items():
        # 生成三级专业代码：二级门类代码 + "00"
        generic_third_code = second_code + "00"
        generic_third_major = generic_third_code + info['graduate_second_category'].replace(second_code, "")
        
        record_key = (info['graduate_first_discipline'], second_code, generic_third_code)
        
        if record_key not in processed_records:
            GraduateMajorInfo.objects.create(
                graduate_first_discipline=info['graduate_first_discipline'],
                graduate_second_category_code=second_code,
                graduate_second_category=info['graduate_second_category'],
                graduate_third_major_code=generic_third_code,
                graduate_third_major=generic_third_major
            )
            processed_records.add(record_key)
            created_count += 1
            print(f"✅ 创建二级门类通用记录: {info['graduate_first_discipline']} -> {info['graduate_second_category']} -> {generic_third_major}")
    
    print(f"\n🎉 导入完成！共创建 {created_count} 条研究生专业信息记录")
    print(f"📊 数据库中总记录数: {GraduateMajorInfo.objects.count()}")


def show_imported_data():
    """
    显示导入的数据概览
    """
    print("\n📋 导入的研究生专业信息概览:")
    print("-" * 80)
    
    # 按一级学科分组显示
    disciplines = GraduateMajorInfo.objects.values_list('graduate_first_discipline', flat=True).distinct()
    
    for discipline in disciplines:
        print(f"\n🎓 {discipline}")
        
        # 获取该一级学科下的二级门类
        second_categories = GraduateMajorInfo.objects.filter(
            graduate_first_discipline=discipline
        ).values_list('graduate_second_category_code', 'graduate_second_category').distinct()
        
        for second_code, second_category in second_categories:
            print(f"  📚 {second_code} - {second_category}")
            
            # 获取该二级门类下的三级专业（最多显示5个）
            third_majors = GraduateMajorInfo.objects.filter(
                graduate_first_discipline=discipline,
                graduate_second_category_code=second_code
            ).values_list('graduate_third_major_code', 'graduate_third_major')[:5]
            
            for third_code, third_major in third_majors:
                print(f"    📖 {third_code} - {third_major}")
            
            # 如果有更多记录，显示省略号
            total_count = GraduateMajorInfo.objects.filter(
                graduate_first_discipline=discipline,
                graduate_second_category_code=second_code
            ).count()
            
            if total_count > 5:
                print(f"    ... 还有 {total_count - 5} 个三级专业")


if __name__ == '__main__':
    print("🚀 开始导入研究生专业信息数据...")
    print("📝 只处理本科二级门类为'0101哲学类'的数据")
    print("=" * 60)
    
    # 清空现有数据（可选）
    existing_count = GraduateMajorInfo.objects.count()
    if existing_count > 0:
        confirm = input(f"⚠️  数据库中已有 {existing_count} 条记录，是否清空后重新导入？(y/N): ")
        if confirm.lower() == 'y':
            GraduateMajorInfo.objects.all().delete()
            print("🗑️  已清空现有数据")
    
    # 导入数据
    import_graduate_major_info()
    
    # 显示导入结果
    show_imported_data()
