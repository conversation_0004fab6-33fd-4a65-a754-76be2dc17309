# Generated by Django 4.2 on 2025-04-28 09:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0052_remove_kaoyanstudentinfo_user_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='KaoYanCourse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(max_length=255, verbose_name='课程名称')),
            ],
            options={
                'verbose_name': '考研课程',
                'verbose_name_plural': '考研课程',
                'db_table': 'ai_kao_yan_course',
            },
        ),
        migrations.CreateModel(
            name='KaoYanTeacherBook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('teacher_name', models.CharField(max_length=255, verbose_name='老师姓名')),
                ('book_name', models.CharField(max_length=255, verbose_name='书籍名称')),
                ('teacher_profile', models.TextField(verbose_name='个人简介')),
            ],
            options={
                'verbose_name': '考研老师_书籍',
                'verbose_name_plural': '考研老师_书籍',
                'db_table': 'ai_kao_yan_teacher_book',
            },
        ),
    ]
