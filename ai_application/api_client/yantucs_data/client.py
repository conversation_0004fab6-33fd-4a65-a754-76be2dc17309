import datetime
import logging

from api_client.base_client import BaseClient

logger = logging.getLogger(__name__)

__all__ = ['yantucs_data_client']


class YantucsDataClient(BaseClient):
    host = 'http://yantucs-data-svc:8000'
    # 除生产都用同一个
    host_preview = 'https://yantucs-data.yantucs.com/'
    host_test = 'https://yantucs-data.yantucs.com/'
    host_local = 'https://yantucs-data.yantucs.com/'

    def get_user_learn_stat(self, user_id, subject, year, learn_start_time: datetime.date):
        learn_start_time_str = learn_start_time.strftime('%Y-%m-%d')

        url = f'/learn/user_learn_stat/'
        res = self._get(url, params={
            'user_id': user_id,
            'subject': subject,
            'year': year,
            'learn_start_time': learn_start_time_str,
        }, timeout=30)
        return res

    def get_user_answer_stat(self, user_id, learn_end_time: datetime.date):
        learn_end_time_str = learn_end_time.strftime('%Y-%m-%d %H:%M:%S')

        url = f'/learn/user_answer_stat/'
        res = self._post(url, data={
            'user_id': user_id,
            'learn_end_time': learn_end_time_str,
        }, timeout=30)
        return res

    def get_questions(self, uuids: list):
        url = f'/learn/questions/'
        res = self._post(url, data={
            'uuids': uuids,
        }, timeout=30)
        return res

    def get_question_kgs(self, question_ids):
        url = f'/learn/question_kgs/'
        res = self._post(url, data={
            'question_ids': question_ids,
        }, timeout=30)
        return res

    def get_video(self, user_id, course_section_ids):
        url = f'/learn/user_learn_videos/'
        res = self._post(url, data={
            'user_id': user_id,
            'course_section_ids': course_section_ids,
        },timeout=30)
        return res

    def get_exercise(self, user_id, course_section_ids):
        url = f'/learn/user_learn_tests/'
        res = self._post(url, data={
            'user_id': user_id,
            'course_section_ids': course_section_ids,
        },timeout=30)
        return res

    def get_ExamFrequency(self, subject_id, start_year, end_year, knowledge_names):
        url = f'/learn/exam_knowledge_freq/'
        res = self._post(url, data={
            'subject_id': subject_id,
            'start_year': start_year,
            'end_year': end_year,
            'knowledge_names': knowledge_names,
        },timeout=30)
        return res

    def get_TeacherAnswer(self,assistant_id,start_time,end_time):
        url = f'/faq/assistant_questions/'
        res = self._post(url, data={
            'assistant_id': assistant_id,
            'start_time': start_time,
            'end_time': end_time,
        },timeout=30)
        return res

    def get_zhihenRadar(self,assistant_id,question_id,answer_id,question,answer,answer_duration):
        url = f'/zhihen_radar/'
    
    def get_user_subjects_stat(self, user_id, course_id):
        
        url = f'/learn/user_course_info/'
        
        res = self._post(url, data={
            'user_id': user_id,
            'course_id': course_id,
        }, timeout=30)

        return res

    def get_user_supervise_learn_stat(self, user_id, course_id, subject_id, start_date, end_date):

        url = f'/learn/user_study_plan_stat/'
        res = self._post(url, data={
            'user_id': user_id,
            'course_id': course_id,
            'subject_id': subject_id,
            'start_date': start_date,
            'end_date': end_date,
        }, timeout=30)
        return res

    def get_school_info(self, school_code,major_code,year = "2025"):
        
        url = f'/college/kaoyan_learn_query/'

        res = self._post(url, data={
            'school_code': school_code,
            'major_code': major_code,
            'year': year
        }, timeout=30)

        return res

    def get_school_by_region(self,major_code,region,undergraduate_level,way=1):

        url = f'/college/school_major_query/'

        res = self._post(url, data={
            'major_code': [major_code],
            'region': [region],
            'undergraduate_level': undergraduate_level,
            'way': way
        }, timeout=30)

        return res
    
    def get_outline_by_id(self,outline_number):

        url = f'/online/erm/outline_content/'

        res = self._get(url, params={
            'outline_number': outline_number
        }, timeout=30)

        return res

    def get_courses_by_ids(self,course_sections):

        url = f'/online/erm/course_sections_resources/'

        res = self._post(url, data={
            'course_section_ids': course_sections
        }, timeout=30)

        return res
    
    def get_paper_details_by_intid(self,intid):

        url = f'/online/exam/paper_detail/'

        res = self._get(url, params = {
            'paper_id': str(intid)
        }, timeout=30)

        return res

yantucs_data_client = YantucsDataClient()
