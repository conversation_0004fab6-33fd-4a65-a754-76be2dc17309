--
-- Create model EnWordReciteBasicAnswer
--
CREATE TABLE `ai_english_word_recite_basic_answer` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_id` varchar(100) NOT NULL, `right_num` integer NOT NULL);
--
-- <PERSON>reate model EnWordReciteBasicPaper
--
CREATE TABLE `ai_english_word_recite_basic_paper` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `question_num` integer NOT NULL);
--
-- Create model EnWordRecitePlan
--
CREATE TABLE `ai_english_word_recite_plan` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_id` varchar(100) NOT NULL, `gen_plan_content` longtext NULL, `plan_content` json NULL, `recite_words` json NULL, `basic_answer_id` bigint NOT NULL);
--
-- Create model EnWordReciteQuestion
--
CREATE TABLE `ai_english_word_recite_question` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `question_type` varchar(16) NOT NULL, `question` longtext NULL, `options` json NULL, `analysis` longtext NULL, `answer` longtext NULL, `word_id` bigint NOT NULL);
--
-- Create model EnWordRecitePlanRecord
--
CREATE TABLE `ai_english_word_recite_plan_record` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `gen_plan_content` longtext NULL, `plan_content` json NULL, `recite_words` json NULL, `plan_id` bigint NOT NULL);
--
-- Create model EnWordReciteDayRecord
--
CREATE TABLE `ai_english_word_recite_day_record` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `day` date NULL, `day_seq` integer NOT NULL, `user_answer` varchar(16) NOT NULL, `is_right` bool NOT NULL, `plan_id` bigint NOT NULL, `plan_record_id` bigint NOT NULL, `question_id` bigint NOT NULL, `word_id` bigint NOT NULL);
--
-- Create model EnWordReciteBasicPaperDetail
--
CREATE TABLE `ai_english_word_recite_basic_paper_detail` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `basic_paper_id` bigint NOT NULL, `question_id` bigint NOT NULL);
--
-- Create model EnWordReciteBasicAnswerDetail
--
CREATE TABLE `ai_english_word_recite_basic_answer_detail` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_answer` varchar(16) NOT NULL, `is_right` bool NOT NULL, `is_answered` bool NOT NULL, `basic_answer_id` bigint NOT NULL, `basic_paper_id` bigint NOT NULL, `question_id` bigint NOT NULL, `word_id` bigint NOT NULL);
--
-- Add field basic_paper to enwordrecitebasicanswer
--
ALTER TABLE `ai_english_word_recite_basic_answer` ADD COLUMN `basic_paper_id` bigint NOT NULL;
CREATE INDEX `ai_english_word_recite_basic_answer_add_time_a0229369` ON `ai_english_word_recite_basic_answer` (`add_time`);
CREATE INDEX `ai_english_word_recite_basic_answer_user_id_24378490` ON `ai_english_word_recite_basic_answer` (`user_id`);
CREATE INDEX `ai_english_word_recite_basic_paper_add_time_b0e8f974` ON `ai_english_word_recite_basic_paper` (`add_time`);
CREATE INDEX `ai_english_word_recite_plan_add_time_6412bbea` ON `ai_english_word_recite_plan` (`add_time`);
CREATE INDEX `ai_english_word_recite_plan_user_id_893eaf32` ON `ai_english_word_recite_plan` (`user_id`);
CREATE INDEX `ai_english_word_recite_plan_basic_answer_id_abe1e8c7` ON `ai_english_word_recite_plan` (`basic_answer_id`);
CREATE INDEX `ai_english_word_recite_question_add_time_92896036` ON `ai_english_word_recite_question` (`add_time`);
CREATE INDEX `ai_english_word_recite_question_word_id_c7048698` ON `ai_english_word_recite_question` (`word_id`);
CREATE INDEX `ai_english_word_recite_plan_record_add_time_ab9c0a66` ON `ai_english_word_recite_plan_record` (`add_time`);
CREATE INDEX `ai_english_word_recite_plan_record_plan_id_d0d12a74` ON `ai_english_word_recite_plan_record` (`plan_id`);
CREATE INDEX `ai_english_word_recite_day_record_add_time_c97814f0` ON `ai_english_word_recite_day_record` (`add_time`);
CREATE INDEX `ai_english_word_recite_day_record_plan_id_536143ce` ON `ai_english_word_recite_day_record` (`plan_id`);
CREATE INDEX `ai_english_word_recite_day_record_plan_record_id_f0f0ceee` ON `ai_english_word_recite_day_record` (`plan_record_id`);
CREATE INDEX `ai_english_word_recite_day_record_question_id_13bd0dad` ON `ai_english_word_recite_day_record` (`question_id`);
CREATE INDEX `ai_english_word_recite_day_record_word_id_068539b7` ON `ai_english_word_recite_day_record` (`word_id`);
CREATE INDEX `ai_english_word_recite_basic_paper_detail_add_time_809b8a10` ON `ai_english_word_recite_basic_paper_detail` (`add_time`);
CREATE INDEX `ai_english_word_recite_basi_basic_paper_id_4210a357` ON `ai_english_word_recite_basic_paper_detail` (`basic_paper_id`);
CREATE INDEX `ai_english_word_recite_basic_paper_detail_question_id_c2fba3a4` ON `ai_english_word_recite_basic_paper_detail` (`question_id`);
CREATE INDEX `ai_english_word_recite_basic_answer_detail_add_time_5447db12` ON `ai_english_word_recite_basic_answer_detail` (`add_time`);
CREATE INDEX `ai_english_word_recite_basi_basic_answer_id_94cbed63` ON `ai_english_word_recite_basic_answer_detail` (`basic_answer_id`);
CREATE INDEX `ai_english_word_recite_basi_basic_paper_id_418a406f` ON `ai_english_word_recite_basic_answer_detail` (`basic_paper_id`);
CREATE INDEX `ai_english_word_recite_basic_answer_detail_question_id_77fcc53a` ON `ai_english_word_recite_basic_answer_detail` (`question_id`);
CREATE INDEX `ai_english_word_recite_basic_answer_detail_word_id_6fb66078` ON `ai_english_word_recite_basic_answer_detail` (`word_id`);
CREATE INDEX `ai_english_word_recite_basic_answer_basic_paper_id_4c71db85` ON `ai_english_word_recite_basic_answer` (`basic_paper_id`);