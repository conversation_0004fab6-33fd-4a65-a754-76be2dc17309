[{"knowledge": "无向图", "num": 73}, {"knowledge": "连通图", "num": 21}, {"knowledge": "度", "num": 63}, {"knowledge": "生成树", "num": 39}, {"knowledge": "队列", "num": 40}, {"knowledge": "先进先出", "num": 20}, {"knowledge": "栈", "num": 85}, {"knowledge": "进栈/入栈", "num": 66}, {"knowledge": "出栈/退栈", "num": 69}, {"knowledge": "可行性", "num": 8}, {"knowledge": "迪杰斯特拉算法", "num": 19}, {"knowledge": "贪心算法", "num": 12}, {"knowledge": "单链表", "num": 74}, {"knowledge": "头结点", "num": 28}, {"knowledge": "位置", "num": 15}, {"knowledge": "线性结构", "num": 31}, {"knowledge": "时间复杂度", "num": 186}, {"knowledge": "空间复杂度", "num": 42}, {"knowledge": "数组", "num": 48}, {"knowledge": "算法", "num": 91}, {"knowledge": "数据元素", "num": 46}, {"knowledge": "双端队列", "num": 3}, {"knowledge": "入队", "num": 29}, {"knowledge": "出队", "num": 25}, {"knowledge": "完全图", "num": 7}, {"knowledge": "子图", "num": 0}, {"knowledge": "拓扑排序", "num": 30}, {"knowledge": "AOV网", "num": 14}, {"knowledge": "栈空条件", "num": 39}, {"knowledge": "栈溢出", "num": 4}, {"knowledge": "后进先出", "num": 34}, {"knowledge": "树", "num": 58}, {"knowledge": "叶子", "num": 86}, {"knowledge": "双亲和孩子", "num": 8}, {"knowledge": "图", "num": 36}, {"knowledge": "简单回路", "num": 8}, {"knowledge": "简单路径", "num": 2}, {"knowledge": "稀疏图", "num": 3}, {"knowledge": "邻接矩阵", "num": 52}, {"knowledge": "邻接表", "num": 58}, {"knowledge": "有向图", "num": 75}, {"knowledge": "行优先存放", "num": 21}, {"knowledge": "关键路径", "num": 20}, {"knowledge": "关键活动", "num": 20}, {"knowledge": "折半查找", "num": 50}, {"knowledge": "有序表", "num": 63}, {"knowledge": "分治思想", "num": 23}, {"knowledge": "分治法", "num": 15}, {"knowledge": "循环队列", "num": 31}, {"knowledge": "队头", "num": 33}, {"knowledge": "队尾", "num": 35}, {"knowledge": "顺序存储结构", "num": 74}, {"knowledge": "大O记号", "num": 71}, {"knowledge": "基本语句", "num": 69}, {"knowledge": "问题规模", "num": 73}, {"knowledge": "对数阶", "num": 31}, {"knowledge": "栈顶", "num": 41}, {"knowledge": "栈底", "num": 37}, {"knowledge": "栈顶指针", "num": 29}, {"knowledge": "栈底指针", "num": 22}, {"knowledge": "二叉树", "num": 217}, {"knowledge": "树的深度/高度", "num": 65}, {"knowledge": "树转换为二叉树", "num": 15}, {"knowledge": "子树", "num": 63}, {"knowledge": "孩子兄弟法", "num": 14}, {"knowledge": "最小生成树", "num": 33}, {"knowledge": "普里姆算法", "num": 25}, {"knowledge": "克鲁斯卡尔算法", "num": 16}, {"knowledge": "2-路归并", "num": 24}, {"knowledge": "归并排序", "num": 24}, {"knowledge": "最坏时间复杂度", "num": 97}, {"knowledge": "最优子结构", "num": 3}, {"knowledge": "存储结构", "num": 45}, {"knowledge": "递归", "num": 84}, {"knowledge": "后缀表达式", "num": 15}, {"knowledge": "中缀表达式", "num": 14}, {"knowledge": "括号匹配", "num": 10}, {"knowledge": "先序遍历", "num": 85}, {"knowledge": "后序遍历", "num": 74}, {"knowledge": "根结点", "num": 33}, {"knowledge": "广度优先搜索", "num": 34}, {"knowledge": "网", "num": 9}, {"knowledge": "链表排序", "num": 28}, {"knowledge": "最好时间复杂度", "num": 93}, {"knowledge": "平均时间复杂度", "num": 85}, {"knowledge": "线性表", "num": 63}, {"knowledge": "入度", "num": 14}, {"knowledge": "出度", "num": 12}, {"knowledge": "AOE网", "num": 21}, {"knowledge": "语句频度", "num": 63}, {"knowledge": "平衡二叉树", "num": 27}, {"knowledge": "平衡因子", "num": 26}, {"knowledge": "插入操作", "num": 159}, {"knowledge": "栈满条件", "num": 16}, {"knowledge": "哈夫曼树", "num": 42}, {"knowledge": "权", "num": 41}, {"knowledge": "二叉排序树", "num": 58}, {"knowledge": "删除操作", "num": 99}, {"knowledge": "相等", "num": 27}, {"knowledge": "B-树", "num": 14}, {"knowledge": "阶数", "num": 12}, {"knowledge": "基数排序", "num": 11}, {"knowledge": "最低位优先", "num": 4}, {"knowledge": "最高位优先", "num": 3}, {"knowledge": "链式存储结构", "num": 34}, {"knowledge": "平均查找长度 (ASL)", "num": 60}, {"knowledge": "顺序表", "num": 52}, {"knowledge": "线索二叉树", "num": 16}, {"knowledge": "兄弟", "num": 7}, {"knowledge": "平方阶", "num": 29}, {"knowledge": "表达式树", "num": 15}, {"knowledge": "二叉链表", "num": 36}, {"knowledge": "数据类型", "num": 19}, {"knowledge": "结构体类型", "num": 19}, {"knowledge": "快速排序", "num": 82}, {"knowledge": "枢轴", "num": 43}, {"knowledge": "排序的稳定性", "num": 72}, {"knowledge": "散列函数", "num": 66}, {"knowledge": "装填因子", "num": 38}, {"knowledge": "森林转换为二叉树", "num": 16}, {"knowledge": "二叉树还原为森林", "num": 8}, {"knowledge": "希尔排序", "num": 35}, {"knowledge": "增量序列", "num": 17}, {"knowledge": "前缀编码", "num": 14}, {"knowledge": "邻接点", "num": 9}, {"knowledge": "连通分量", "num": 8}, {"knowledge": "强连通图", "num": 5}, {"knowledge": "强连通分量", "num": 0}, {"knowledge": "深度优先搜索", "num": 39}, {"knowledge": "图结构", "num": 41}, {"knowledge": "递归栈", "num": 8}, {"knowledge": "中序遍历", "num": 98}, {"knowledge": "模式匹配", "num": 11}, {"knowledge": "KMP算法", "num": 9}, {"knowledge": "next表", "num": 8}, {"knowledge": "直接插入排序", "num": 34}, {"knowledge": "冒泡排序", "num": 33}, {"knowledge": "堆", "num": 32}, {"knowledge": "筛选法", "num": 33}, {"knowledge": "三对角矩阵", "num": 1}, {"knowledge": "压缩存储", "num": 18}, {"knowledge": "存储位置公式", "num": 45}, {"knowledge": "树结构", "num": 26}, {"knowledge": "非终端结点", "num": 33}, {"knowledge": "递归定义", "num": 32}, {"knowledge": "表头", "num": 8}, {"knowledge": "双向链表", "num": 18}, {"knowledge": "循环链表", "num": 19}, {"knowledge": "森林", "num": 16}, {"knowledge": "B+树", "num": 0}, {"knowledge": "外部排序", "num": 3}, {"knowledge": "稀疏矩阵", "num": 21}, {"knowledge": "三元组顺序表", "num": 14}, {"knowledge": "十字链表", "num": 4}, {"knowledge": "手工创建表达式树", "num": 12}, {"knowledge": "对称矩阵", "num": 13}, {"knowledge": "上三角矩阵", "num": 5}, {"knowledge": "线性阶", "num": 41}, {"knowledge": "哈夫曼编码", "num": 27}, {"knowledge": "列优先存放", "num": 10}, {"knowledge": "满二叉树", "num": 22}, {"knowledge": "表尾", "num": 9}, {"knowledge": "内部排序", "num": 10}, {"knowledge": "旋转操作", "num": 17}, {"knowledge": "LL 型调整", "num": 13}, {"knowledge": "RR 型调整", "num": 13}, {"knowledge": "三叉链表", "num": 5}, {"knowledge": "长度", "num": 21}, {"knowledge": "随机存储特性", "num": 24}, {"knowledge": "开放地址法", "num": 44}, {"knowledge": "堆排序", "num": 48}, {"knowledge": "置换-选择排序", "num": 2}, {"knowledge": "首元结点", "num": 2}, {"knowledge": "祖先", "num": 7}, {"knowledge": "子孙", "num": 6}, {"knowledge": "邻接多重表", "num": 4}, {"knowledge": "数据", "num": 17}, {"knowledge": "败者树", "num": 1}, {"knowledge": "多路平衡归并", "num": 4}, {"knowledge": "简单选择排序", "num": 21}, {"knowledge": "递归工作栈", "num": 15}, {"knowledge": "数据项", "num": 9}, {"knowledge": "折半插入排序", "num": 1}, {"knowledge": "线索化", "num": 14}, {"knowledge": "层次", "num": 4}, {"knowledge": "判定树", "num": 22}, {"knowledge": "后根遍历", "num": 6}, {"knowledge": "AVL树", "num": 12}, {"knowledge": "最佳归并树", "num": 1}, {"knowledge": "先根遍历", "num": 5}, {"knowledge": "逻辑结构", "num": 18}, {"knowledge": "多项式时间复杂度", "num": 15}, {"knowledge": "指数阶时间复杂度", "num": 14}, {"knowledge": "立方阶", "num": 12}, {"knowledge": "常数阶", "num": 11}, {"knowledge": "BF算法", "num": 7}, {"knowledge": "弗洛伊德算法", "num": 2}, {"knowledge": "序列性", "num": 8}, {"knowledge": "维数组", "num": 9}, {"knowledge": "行逻辑链接顺序表", "num": 1}, {"knowledge": "空树", "num": 31}, {"knowledge": "二叉树还原为树", "num": 8}, {"knowledge": "特殊矩阵", "num": 6}, {"knowledge": "广义表", "num": 9}, {"knowledge": "链队", "num": 10}, {"knowledge": "顺序队", "num": 13}, {"knowledge": "假溢出", "num": 6}, {"knowledge": "下三角矩阵", "num": 8}, {"knowledge": "双亲表示法", "num": 1}, {"knowledge": "孩子表示法", "num": 2}, {"knowledge": "PM值", "num": 1}, {"knowledge": "链地址法", "num": 26}, {"knowledge": "改进KMP算法", "num": 3}, {"knowledge": "存储密度", "num": 10}, {"knowledge": "数据结构", "num": 16}, {"knowledge": "串", "num": 10}, {"knowledge": "数组类型", "num": 3}, {"knowledge": "头尾链表存储结构", "num": 1}, {"knowledge": "顺序栈", "num": 7}, {"knowledge": "有穷性", "num": 9}, {"knowledge": "确定性", "num": 9}, {"knowledge": "求和定理", "num": 7}, {"knowledge": "层次遍历", "num": 23}, {"knowledge": "空串", "num": 0}, {"knowledge": "链栈", "num": 5}, {"knowledge": "分裂", "num": 5}, {"knowledge": "数据对象", "num": 5}, {"knowledge": "定长顺序存储结构", "num": 2}, {"knowledge": "二分递归", "num": 3}, {"knowledge": "一致性", "num": 5}, {"knowledge": "模型", "num": 1}, {"knowledge": "求积定理", "num": 3}, {"knowledge": "前缀表达式", "num": 1}, {"knowledge": "约瑟夫问题", "num": 1}, {"knowledge": "空栈", "num": 6}, {"knowledge": "稠密图", "num": 4}, {"knowledge": "树形选择排序", "num": 2}, {"knowledge": "地址排序", "num": 1}, {"knowledge": "监视哨", "num": 1}, {"knowledge": "扩展线性链表存储结构", "num": 1}]