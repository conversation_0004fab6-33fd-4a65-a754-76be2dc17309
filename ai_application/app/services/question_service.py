import json
import random
import re

from django.conf import settings
from api_client.data.client import data_client
from api_client.examination.client import examination_client
from api_client.data.vector_data_client import vector_data_client


class QuestionService:
    choiceMap = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"]

    @classmethod
    def trim_p_tag(cls, html_string: str):
        if '<p>' in html_string:
            cleaned_string = re.sub(r'^<p>', '', html_string)
            return re.sub(r'</p>$', '', cleaned_string)
        else:
            return html_string

    @classmethod
    def search_questions(cls, knowledge_name: str, size: int = 2):
        """
        classification__template__in
            0, "单选"
            1, "多选"
            2, "主观"
            3, "材料"
            4, "共享题干"
            5, "共享选项"
            6, "填空"
        :param knowledge_name: 知识点名称
        :param size: 搜索数量
        :return:
        """
        # 搜索对应知识点：
        res = data_client.search_knowledge({'name': knowledge_name})
        # 目前默认只找出5个知识点
        knowledge_ids = [str(i['id']) for i in res][:5]
        if not knowledge_ids:
            return []

        if settings.ENVIRONMENT == settings.ENV_PRODUCT:
            params = {
                'examination_library': settings.QUESTION_LIBRARY_UID_408,
                'examination_source_kind': 1,
                'examination_source_exam_type': 1,
            }
        else:
            params = {
                'examination_library': settings.QUESTION_LIBRARY_UID_408,
            }

        # 目前搜索简易模式，只关注题目题干
        params['is_simple'] = 1
        params['examination_knowledge_point'] = ','.join(knowledge_ids)
        params['classification__template__in'] = '0,1,2,6'
        res = examination_client.search_questions(params, size=size)
        questions = res.get('results', [])

        formatted_questions = []
        for q in questions:
            formatted_questions.append({
                'uuid': q['uuid'],
                'num': q['num'],
                'title': q['title'],
            })
        return formatted_questions

    @classmethod
    def get_vector_question_by_ids(cls, q_ids: list):
        res = vector_data_client.get_questions_by_ids(q_ids)
        return res['data']['data']

    @classmethod
    def get_question_by_ids(cls, q_ids: list):
        res = vector_data_client.get_questions_by_ids(q_ids)

        questions = []
        for item in res['data']['data']:
            # 0单选 1多选 2主观 3材料 4共享题干 5共享选项 6填空
            type_id = item['type_id']
            q_item = {
                'q_id': str(item['question_id']),
                'q_type': type_id,
                'title': item['master_title_format'],
                'choices': [],
                'choice_right_answer': [],
                'subjective_right_answer': '',
                'fill_right_answer': [],
                'analysis': '',
                'rich_analysis': '',
                'sub_questions': [],
            }
            if type_id in [0, 1]:
                q_item['choices'] = item['sub_question_info_format'][0]['choice_body']
                q_item['choice_right_answer'] = item['sub_question_info_format'][0]['answer_body']
                q_item['analysis'] = item['sub_question_info_format'][0]['analysis']
                q_item['rich_analysis'] = item['sub_question_info_format'][0]['rich_analysis']
            elif type_id == 2:
                q_item['subjective_right_answer'] = item['sub_question_info_format'][0]['answer_body']
                q_item['analysis'] = item['sub_question_info_format'][0]['analysis']
                q_item['rich_analysis'] = item['sub_question_info_format'][0]['rich_analysis']
            elif type_id in [3, 4, 5]:
                sub_questions = []
                for sub_index, sub in enumerate(item['sub_question_info_format']):
                    origin_sub_question = item['sub_question_info'][sub_index]
                    sub_q_id = str(origin_sub_question['sub_question_id'])

                    if isinstance(sub['choice_body'], list) and len(sub['choice_body']) > 1:
                        # 默认为选择题
                        sub_questions.append({
                            'q_id': sub_q_id,
                            'q_type': 1,
                            'title': sub['sub_title'],
                            'choices': sub['choice_body'],
                            'choice_right_answer': sub['answer_body'],
                            'subjective_right_answer': '',
                            'analysis': sub['analysis'],
                            'rich_analysis': sub['rich_analysis'],
                        })
                    elif isinstance(sub['choice_body'], str):
                        # 默认为主观题
                        sub_questions.append({
                            'sub_q_id': sub_q_id,
                            'q_type': 2,
                            'title': sub['sub_title'],
                            'choices': [],
                            'choice_right_answer': [],
                            'subjective_right_answer': sub['answer_body'],
                            'analysis': sub['analysis'],
                            'rich_analysis': sub['rich_analysis'],
                        })
                q_item['sub_questions'] = sub_questions
            elif type_id == 6:
                fill_answer = item['sub_question_info_format'][0]['answer_body']
                try:
                    fill_answer_arr = json.loads(fill_answer)
                except:
                    fill_answer_arr = []
                q_item['fill_right_answer'] = fill_answer_arr
                q_item['analysis'] = item['sub_question_info_format'][0]['analysis']
                q_item['rich_analysis'] = item['sub_question_info_format'][0]['rich_analysis']

            questions.append(q_item)
        return questions

    @classmethod
    def __format_questions(cls, questions: list) -> list:
        formatted_questions = []
        for q in questions:
            if q['classification']['template'] in [0, 1]:
                choices = []
                answers = []
                for idx, choice in enumerate(q['question']['choices_set']):
                    if idx > len(cls.choiceMap) - 1:
                        continue
                    choice_body = cls.trim_p_tag(choice['body'])
                    choices.append(f"{cls.choiceMap[idx]}: {choice_body}")
                    if choice['is_answer']:
                        answers.append(cls.choiceMap[idx])
                choices_str = '\n\n'.join(choices)
                title = cls.trim_p_tag(q['question']['title'])
                question_content = f"{title}\n\n{choices_str}"

                analysis_arr = []
                if answers:
                    answer_str = ','.join(answers)
                    analysis_arr.append(f"答案为：{answer_str}")
                if q['question']['analysis']:
                    analysis_str = cls.trim_p_tag(q['question']['analysis'])
                    analysis_arr.append(f"解析：{analysis_str}")

                formatted_questions.append({
                    'title': title,
                    'question': question_content,
                    'analysis': '\n\n'.join(analysis_arr),
                })
            elif q['classification']['template'] in [2, 6]:
                title = cls.trim_p_tag(q['question']['title'])

                analysis_arr = []
                if q['question']['answer']:
                    answer_str = cls.trim_p_tag(q['question']['answer'])
                    analysis_arr.append(f"答案为：{answer_str}")
                if q['question']['analysis']:
                    analysis_str = cls.trim_p_tag(q['question']['analysis'])
                    analysis_arr.append(f"解析：{analysis_str}")

                formatted_questions.append({
                    'title': title,
                    'question': title,
                    'analysis': '\n\n'.join(analysis_arr),
                })

        return formatted_questions

    @classmethod
    def get_questions_by_vector(cls, question_str: str, size: int) -> list:
        # 数据搜索接口 word参数不能有 \n
        question_str = question_str.replace('\n', ' ')
        params = {'word': question_str, 'type': 'question', 'from': 1, 'size': size}

        # 搜索对应知识点：
        questions = vector_data_client.get_answer(params)
        #print(f"questions在这边啊啊啊啊啊啊啊: {questions}")
        # 初始化格式化问题列表
        formatted_questions = []
        # 遍历结果，构造每个问题的信息
        for item in questions['data']['data'][:size]:
            formatted_questions.append(cls._parse_vector_question(item))
        # print(f"questions在这边啊啊啊啊啊啊啊: {formatted_questions}")
        return formatted_questions

    @classmethod
    def get_questions_by_knowledge(cls, knowledge_name: str, size: int = 2) -> list:
        # 先搜索问题
        # 先获取10道，然后随机取两道题目
        questions = cls.search_questions(knowledge_name, size=10)
        if len(questions) >= size:
            questions = random.sample(questions, size)

        new_questions = []
        # 遍历问题，获取每个问题的在向量库内的详细信息
        for question in questions:
            title = question.get('title')  # 取出题目的标题
            if title:
                new_questions = cls.get_questions_by_vector(title, size=1)
                if new_questions:  # 如果有详细答案，则合并数据
                    new_questions.append(new_questions[0])
        return new_questions

    @classmethod
    def _parse_vector_question(cls, question) -> dict:
        master_title = cls.trim_p_tag(question['master_title_format'])
        choice_body = '\n\n'.join(question['sub_question_info_format'][0]['choice_body'])
        answer_body = question['sub_question_info_format'][0]['answer_body']
        answer_analysis = question['sub_question_info_format'][0]['analysis']
        score = float(question['vectorScore'])

        return {
            'question': f"{master_title}\n\n{choice_body}",
            'analysis': f"答案为：{answer_body}\n\n解析：{answer_analysis}",
            'score': score,  # 保持数值类型
            'score_display': f"相似度为：{score:.2%}"
        }
