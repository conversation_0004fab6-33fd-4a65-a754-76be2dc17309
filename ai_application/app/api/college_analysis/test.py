import requests

# 获取大学列表
url = "http://query2.kaoyan-vip.cn/internal_api/v1/school/?year=2025"

payload = {}
headers = {}

response = requests.request("GET", url, headers=headers, data=payload)

university_list = []

if response.status_code == 200:
    data = response.json()
    if data["code"] == 20000:
        universities = data["data"]
        for university in universities:
            university_info = {
                "name": university["name"],
                "code": university["code"],
                "major_name": []  # 初始化专业列表
            }
            university_list.append(university_info)
    else:
        print(f"Error: {data['msg']}")
else:
    print(f"Failed to retrieve data: {response.status_code}")

# 获取每个大学的专业列表
for university in university_list:
    school_code = university["code"]
    url = f"http://query2.kaoyan-vip.cn/internal_api/v1/catalog/?year=2025&school_code={school_code}"

    response = requests.request("GET", url, headers=headers, data=payload)

    if response.status_code == 200:
        data = response.json()
        if data["code"] == 20000:
            majors = data["data"]
            major_names = set()  # 使用集合去重

            for major in majors:
                major_names.add(major["major_name"])

            university["major_name"] = list(major_names)  # 将集合转换为列表
            print(university)  # 每查完一次专业后打印大学信息
        else:
            print(f"Error: {data['msg']}")
    else:
        print(f"Failed to retrieve data: {response.status_code}")
