from app.core.entities.app_entities import CompletionAppGenerateEntity, ChatAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.models import PromptTemplate


class SubtitleFixFeature(BaseFeature):

    def __init__(self):
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no='lecture_note_subtitle_fix').first()
        if not prompt_template:
            raise Exception('prompt template not found')
        self.prompt_template = prompt_template

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            subtitle: str,
            stream: bool,
            prompt_content: str = '',
    ):
        user_inputs = application_generate_entity.inputs.copy()
        user_inputs['subtitle'] = subtitle

        return self.query_llm_by_prompt_template(
            application_generate_entity=application_generate_entity,
            prompt_template=self.prompt_template,
            user_inputs=user_inputs,
            stream=stream,
            prompt_content=prompt_content
        )
