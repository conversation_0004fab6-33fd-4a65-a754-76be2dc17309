import datetime
import json

from django.conf import settings
from django.core.management import BaseCommand

from api_client.data.vector_data_client import vector_data_client
from api_client.examination.client import examination_client
from app.models.model import UnifiedExaminationQuestion as Ueq


class Command(BaseCommand):

    default_start_year = 2009

    @property
    def start_year(self) -> int:
        x = Ueq.objects.filter(is_deleted=False).annotate().values_list('year', flat=True).distinct()
        return int(min(x)) + 1 if x else self.default_start_year

    @property
    def end_year(self) -> int:
        return datetime.datetime.now().year

    def add_arguments(self, parser):
        parser.add_argument("year", type=int)

    def handle(self, *app_labels, **options):
        params = {
            'is_simple': 1,     # 目前搜索简易模式，只关注题目题干
            'examination_library': settings.QUESTION_LIBRARY_UID_408,
            'classification__template__in': '0,1,2,6',
        }
        if settings.ENVIRONMENT == settings.ENV_PRODUCT:
            params = {
                'examination_library': settings.QUESTION_LIBRARY_UID_408,
                'examination_source_kind': 1,
                'examination_source_exam_type': 1,
            }

        year = options['year']
        self.sync_data_by_year(year, params)

    def sync_data_by_year(self, year: int, params: dict):
        self.stdout.write(f'Syncing question data for year {year}')
        question_this_year = {}
        page = 1
        size = 50
        while True:
            # fetch question
            params['examination_year'] = str(year)
            res = examination_client.search_questions(params, page=page, size=size)
            _count = 0
            num_list = []
            for question in res.get('results', []):
                _count += 1
                question_num = question['num']
                question_this_year[question_num] = {
                    'question_num': question_num,
                    'year': str(year),
                    'library': Ueq.LibraryType.CS408,
                }
                num_list.append(question_num)

            # fetch question's answer and analysis
            if num_list:
                data = {
                    'questionIdList': [str(n) for n in num_list],
                }
                detail_res = vector_data_client.get_detail(data)
                found_q_list = []
                for question_detail in detail_res.get('data', {}).get('data', []):
                    question_num = question_detail['question_id']
                    q = question_this_year.get(question_num)
                    if q:
                        found_q_list.append(question_num)
                        sub_question_info = question_detail.get('sub_question_info_format', [])
                        sub_question_info = sub_question_info[0] if sub_question_info else {}
                        choice_body = sub_question_info.get('choice_body')
                        if isinstance(choice_body, str):
                            choice_body = [choice_body]
                        q.update({
                            'title': question_detail.get('master_title_format', ''),
                            'answer_body': json.dumps(sub_question_info.get('answer_body', []), ensure_ascii=False),
                            'choice_body': json.dumps(choice_body, ensure_ascii=False),
                            'analysis': sub_question_info.get('analysis', ''),
                            'content': question_detail,
                        })
                # 数据接口未查询到数据,则标记字段 <lost result>
                for i in (set(num_list) - set(found_q_list)):
                    q = question_this_year.get(i)
                    q.update({
                        'lost_result': True,
                    })

            # 退出while
            if _count == size:
                page += 1
            else:
                break

        # save to db
        self._save_to_db(question_this_year)
        self.stdout.write(self.style.SUCCESS(f'Successfully synced question data in year {year}.'))

    def _save_to_db(self, question_this_year: dict):
        question_obj_list = []
        for q in question_this_year.values():
            question_obj_list.append(Ueq(**q))
        Ueq.objects.bulk_create(question_obj_list)