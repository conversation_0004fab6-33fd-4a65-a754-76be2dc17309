# from app.api.dto import GaoshuScreenshotQuestionDto
# from app.api.validators import GaoshuScreenshotQuestionValidator
# from app.errors import ParameterError
# from app.services.coze_workflow_service import gaoshu_screenshot_question
# from django_ext import base_view
# from django_ext.response import make_response
#
#
# class GaoshuScreenshotQuestionView(base_view.BaseView):
#
#     validator_class = GaoshuScreenshotQuestionValidator
#     def post(self, request, *args, **kwargs):
#         dto: GaoshuScreenshotQuestionDto = self.validate_request(request.data)
#         pic = dto.pic
#         question =dto.question
#         keywords =dto.keywords
#         if not pic:
#             raise ParameterError()
#
#         data = gaoshu_screenshot_question(pic, question, keywords)
#         return make_response(data)
#
