# Generated by Django 4.2 on 2024-11-10 14:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0009_appmodelconfig_model_params_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PromptTemplateChangeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('prompt_content', models.TextField(verbose_name='提示词内容')),
                ('model_params', models.JSONField(null=True, verbose_name='模型参数')),
                ('prompt_template', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.prompttemplate')),
            ],
            options={
                'verbose_name': '提示词模版修改日志',
                'verbose_name_plural': '提示词模版修改日志',
                'db_table': 'ai_prompt_template_change_log',
            },
        ),
    ]
