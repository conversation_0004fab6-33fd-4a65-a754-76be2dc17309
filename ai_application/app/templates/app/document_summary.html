<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档转换助手v1.1-{{ style_name }} Demo</title>
{#    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>#}
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .help-text {
            display: block;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .page-title {
            width: 200px;
            margin: 20px auto;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }
        .container {
            display: flex;
            max-width: 2200px;
            min-height: 650px; /* 固定高度 */
            margin: 20px auto;
            background-color: #fff;
            {#box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);#}
        }
        .directory {
            width: 150px;
            float: left;
            border: 1px solid #ccc;
            padding: 10px;
        }
        .directory ul {
            padding-left: 15px;
        }
        .content {
            margin-left: 170px;
            padding: 10px;
            border: 1px solid #ccc;
        }
        .content.hidden {
            display: none;
        }
        .left-column, .right-column {
            padding: 20px;
            box-sizing: border-box;
        }
        .toc_item {
            cursor: pointer;
        }
        .left-column {
            width: 20%;
            background-color: #f9f9f9;
            border-right: 1px solid #eaeaea;
        }
        .upload-column {
            width: 35%;
        }
        .right-column {
            width: 25%;
            min-height: 650px; /* 固定高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            border-left: 1px solid #eaeaea; /* 为了美观，可以添加分隔线 */
        }
        .left-column h2 {
            color: #333;
        }
        .left-column label {
            display: block;
            margin-bottom: 5px;
            color: #666;
        }
        .left-column form {
            display: flex;
            flex-direction: column;
        }
        .check-form-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .check-form-group label {
            margin-right: 10px;
        }
        input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="number"]:focus, textarea:focus {
            border-color: #007bff;
            outline: none;
        }
        .left-column textarea {
            resize: vertical;
            height: 200px; /* 设置一个初始高度 */
        }
        /* 下拉框样式 */
        .left-column select {
            width: 100%; /* 宽度与输入框一致 */
            padding: 10px; /* 内边距 */
            margin-bottom: 20px; /* 与输入框相同的外边距 */
            border: 1px solid #ddd; /* 边框颜色 */
            border-radius: 4px; /* 边框圆角 */
            box-sizing: border-box; /* 盒模型 */
            background-color: white; /* 背景颜色 */
            color: #666; /* 文本颜色 */
            font-size: 16px; /* 字体大小 */
            cursor: pointer; /* 鼠标悬停时的光标样式 */
        }

        .left-column select:focus {
            border-color: #007bff; /* 聚焦时的边框颜色 */
            outline: none; /* 移除聚焦时的轮廓 */
        }

        /* 下拉框选项样式 */
        .left-column select option {
            padding: 5px; /* 选项的内边距 */
            background-color: #fff; /* 选项的背景颜色 */
            color: #333; /* 选项的文本颜色 */
        }

        .left-column select option:hover {
            background-color: #f0f0f0; /* 选项悬停时的背景颜色 */
        }
        .left-column button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .left-column button:hover {
            background-color: #0056b3;
        }
        #preview-content .result {
            margin: 0;
            font-size: 14px;
            white-space: pre-line;
        }
        #preview-content .usage {
            margin: 0;
            font-size: 14px;
        }
        /* 加载框样式 */
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>正在请求...</p>
        </div>
    </div>
    <div class="page-title">文档转换助手v1.1-{{ style_name }} Demo</div>
    <div class="container">
        <div class="left-column upload-column">
            <form id="uploadForm" enctype="multipart/form-data">
                <input type="file" name="file" id="file" />
                <br>
                <button type="button" id="uploadBtn">Upload</button>
            </form>
            <br>
            <div class="directory">
              <h3>目录</h3>
              <ul id="toc_list">
              </ul>
            </div>

            <div class="content" id="toc_content">
            </div>
        </div>
        <div class="left-column">
            <form id="myForm1" action="{% url 'prompt_estimate_submit' %}">
                <h3>内容提炼</h3>
                <div class="form-group">
                    <label for="max_tokens">max_tokens:</label>
                    <input type="number" id="max_tokens" name="max_tokens" placeholder="Enter max_tokens here..." value="500">
                </div>
                <div class="form-group">
                    <label for="temperature">temperature:</label>
                    <input type="number" id="temperature" name="temperature" placeholder="Enter temperature here..."
                           value="0.3" step="0.1" min="0" max="0.9">
                </div>
                <div class="form-group">
                    <label for="summary_prompt">内容提炼提示词:</label>
                    <span class="help-text">query为参数占位符，不可删除</span>
                    <textarea id="summary_prompt" name="summary_prompt" rows="3" required placeholder="Enter summary_prompt here...">{{ debug_prompt_content2 }}</textarea>
                </div>

                <div class="form-group">
                    <label for="query">query:</label>
                    <textarea id="query" name="query" rows="3" required placeholder="Enter query here..."></textarea>
                    <span id="queryCount">0</span> 字符
                </div>

                <button type="submit">Submit</button>
            </form>
        </div>
        <div class="left-column">
            <h3>文档转换</h3>
            <form id="myForm2" action="{% url 'prompt_estimate_submit' %}">
                <div class="form-group">
                    <label for="max_tokens2">max_tokens:</label>
                    <input type="number" id="max_tokens2" name="max_tokens2" placeholder="Enter max_tokens here..." value="500">
                </div>
                <div class="form-group">
                    <label for="temperature2">temperature:</label>
                    <input type="number" id="temperature2" name="temperature2" placeholder="Enter temperature here..."
                           value="0.3" step="0.1" min="0" max="0.9">
                </div>
                <div class="form-group">
                    <label for="convert_prompt">文档转换提示词:</label>
                    <span class="help-text">query为参数占位符，不可删除</span>
                    <textarea id="convert_prompt" name="convert_prompt" rows="3" required placeholder="Enter convert_prompt here...">{{ debug_prompt_content }}</textarea>
                </div>

                <div class="form-group">
                    <label for="query2">query:</label>
                    <textarea id="query2" name="query2" rows="3" required placeholder="Enter query here..."></textarea>
                    <span id="queryCount2">0</span> 字符
                </div>

                <div class="prompt1_usage">
                </div>

                <button type="submit">Submit</button>
            </form>
        </div>
        <div class="right-column">
            <h2>返回结果：</h2>
            <div id="preview-content">
                <div class="result"></div>
                <div class="usage"></div>
            </div>
        </div>
    </div>
    <script>

        $(function() {
            let content_blocks = []
            const templateId = {{ prompt_template_id }}
            const templateId2 = {{ prompt_template_id2 }}

            $('#uploadBtn').click(function() {
                const formData = new FormData($('#uploadForm')[0]);
                $.ajax({
                    url: '{% url "document_summary_upload" %}', // 服务器端处理上传的URL
                    type: 'POST',
                    data: formData,
                    processData: false,  // 告诉jQuery不要处理发送的数据
                    contentType: false,  // 告诉jQuery不要设置Content-Type请求头
                    success: function(response) {
                        // 文件上传成功后的回调
                        content_blocks = response.data.content_blocks
                        let tocHtml = ''
                        for (let i = 0; i < content_blocks.length; i++) {
                            tocHtml += `<li class="toc_item" data-id="${i}">${content_blocks[i].title}</li>`
                        }
                        $('#toc_list').html(tocHtml)
                        updateQueryContent(0)
                    },
                    error: function() {
                        // 文件上传失败后的回调
                        console.error('File upload failed.');
                    }
                });
            });

            $('#toc_list').on('click', '.toc_item', function () {
                updateQueryContent($(this).data('id'))
            })
            function updateQueryContent(idx) {
                let query = ''
                for (let i = 0; i < content_blocks.length; i++) {
                    if (i === idx) {
                        query = content_blocks[i].content
                    }
                }
                $('#query').val(query)
                $('#toc_content').html(`<p>${query}</p>`)
                updateCharCount();
            }

            function updateCharCount() {
                $('#queryCount').text($('#query').val().length);
                $('#queryCount2').text($('#query2').val().length);
            }

            // 绑定 input 事件到 textarea
            $('#query').on('input', updateCharCount);
            $('#query2').on('input', updateCharCount);
            // 初始调用以设置初始计数
            updateCharCount();

            $('#myForm1').submit(function(e) {
                e.preventDefault(); // 阻止表单的默认提交行为

                const formData = {
                    max_tokens: parseInt($('#max_tokens').val()),
                    temperature: parseFloat($('#temperature').val()),
                    template_id: templateId2,
                    prompt: $('#summary_prompt').val(),
                    query: $('#query').val(),
                };
                if (formData.max_tokens < 0 || formData.max_tokens > 2000) {
                    alert('max_tokens值错误【0-2000】')
                    return
                }
                if (formData.temperature < 0 || formData.temperature > 0.9) {
                    alert('temperature值错误【0-0.9】')
                    return
                }
                if (!formData.prompt) {
                    alert('提示词不能为空')
                    return
                }
                if (!formData.query) {
                    alert('query不能为空')
                    return
                }

                $('#loadingOverlay').css('display', 'flex')
                $.ajax({
                    type: 'POST',
                    url: $('#myForm1').attr('action'), // 替换为你的服务器端点
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        // 处理响应数据
                        $('#query2').val(response.data.answer)
                        updateCharCount();
                        $('.prompt1_usage').html(
                            '<div>耗时：' + response.data.usage.latency + '秒</div>' +
                            '<div>请求token：' + response.data.usage.prompt_tokens + '</div>' +
                            '<div>响应token：' + response.data.usage.completion_tokens + '</div>' +
                            '<div>总token：' + response.data.usage.total_tokens + '</div>' +
                            '<div>返回字符数：' + response.data.char_len + '</div>'
                        )
                        $('#loadingOverlay').css('display', 'none')
                    },
                    error: function(xhr, status, error) {
                        console.log(xhr, status, error)
                        // 处理错误情况
                        console.log(xhr, status, error)
                        $('#preview-content').html('Error submitting form.');
                        $('#loadingOverlay').css('display', 'none')
                    }
                });
            })

            $('#myForm2').submit(function(e) {
                e.preventDefault(); // 阻止表单的默认提交行为

                const formData = {
                    max_tokens: parseInt($('#max_tokens2').val()),
                    temperature: parseFloat($('#temperature2').val()),
                    template_id: templateId,
                    prompt: $('#convert_prompt').val(),
                    query: $('#query2').val(),
                };
                if (formData.max_tokens < 0 || formData.max_tokens > 2000) {
                    alert('max_tokens值错误【0-2000】')
                    return
                }
                if (formData.temperature < 0 || formData.temperature > 0.9) {
                    alert('temperature值错误【0-0.9】')
                    return
                }
                if (!formData.prompt) {
                    alert('提示词不能为空')
                    return
                }
                if (!formData.query) {
                    alert('query不能为空')
                    return
                }

                $('#loadingOverlay').css('display', 'flex')
                $.ajax({
                    type: 'POST',
                    url: $('#myForm2').attr('action'), // 替换为你的服务器端点
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        // 处理响应数据
                        $('#preview-content').html(
                            '<div class="result">' + response.data.answer + '</div>' +
                            '<hr><div class="usage">' +
                            '<div>耗时：' + response.data.usage.latency + '秒</div>' +
                            '<div>请求token：' + response.data.usage.prompt_tokens + '</div>' +
                            '<div>响应token：' + response.data.usage.completion_tokens + '</div>' +
                            '<div>总token：' + response.data.usage.total_tokens + '</div>' +
                            '<div>返回字符数：' + response.data.char_len + '</div>' +
                            '</div>'
                        );
                        $('#loadingOverlay').css('display', 'none')
                    },
                    error: function(xhr, status, error) {
                        // 处理错误情况
                        console.log(xhr, status, error)
                        $('#preview-content').html('Error submitting form.');
                        $('#loadingOverlay').css('display', 'none')
                    }
                });
            })
        })

    </script>
</body>
</html>