--
-- Create model DataStructureKnowledge
--
CREATE TABLE `ai_data_structure_knowledge` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `subject_1` varchar(255) NOT NULL, `subject_2` varchar(255) NOT NULL, `categories` varchar(255) NOT NULL, `knowledge_points` varchar(100) NOT NULL);
--
-- Add field core_course_name to knowledgevideo
--
ALTER TABLE `ai_knowledge_video` ADD COLUMN `core_course_name` varchar(20) DEFAULT '' NOT NULL;
--
-- Add field fail_reason to knowledgevideo
--
ALTER TABLE `ai_knowledge_video` ADD COLUMN `fail_reason` varchar(255) DEFAULT '' NOT NULL;
--
-- Add field knowledge_name to knowledgevideo
--
ALTER TABLE `ai_knowledge_video` ADD COLUMN `knowledge_name` varchar(20) DEFAULT '' NOT NULL;
--
-- Add field status to knowledgevideo
--
<PERSON>TER TABLE `ai_knowledge_video` ADD COLUMN `status` varchar(20) DEFAULT 'not_start' NOT NULL;
--
-- Alter field name on knowledgevideo
--
ALTER TABLE `ai_knowledge_video` DROP FOREIGN KEY `ai_knowledge_video_name_id_0e1d9683_fk_ai_knowledge_simple_id`;
ALTER TABLE `ai_knowledge_video` MODIFY `name_id` bigint NULL;
ALTER TABLE `ai_knowledge_video` ADD CONSTRAINT `ai_knowledge_video_name_id_0e1d9683_fk_ai_knowledge_simple_id` FOREIGN KEY (`name_id`) REFERENCES `ai_knowledge_simple` (`id`);
CREATE INDEX `ai_data_structure_knowledge_add_time_6d135069` ON `ai_data_structure_knowledge` (`add_time`);