import json
from django_ext import base_view
from django_ext.response import make_stream_response, make_response
from app.api.dto import KnowledgeAnalysisDto
from app.api.validators import KnowledgeAnalysisValidator
from app.services.knowledge_analysis_service import KnowledgeAnalysisService

class KnowledgeAnalysisView(base_view.BaseView):
    validator_class = KnowledgeAnalysisValidator

    def post(self, request, *args, **kwargs):
        dto: KnowledgeAnalysisDto = self.validate_request(request.data)
        response = KnowledgeAnalysisService.query_knowledge(
            dto=dto,
            account=request.user,
        )
        if dto.is_stream==True:
            return make_stream_response(response)
        else:
            return make_response({'knowledge_list':json.loads(response['answer'])})
    
class KnowledgeAnalysisRegenerateView(base_view.BaseView):
    validator_class = KnowledgeAnalysisValidator

    def post(self, request, *args, **kwargs):
        dto: KnowledgeAnalysisDto = self.validate_request(request.data)
        response = KnowledgeAnalysisService.regenerate_knowledge(
            dto=dto,
            account=request.user,
        )
        if dto.is_stream==True:
            return make_stream_response(response)
        else:
            return make_response({'knowledge_list':json.loads(response['answer'])})
