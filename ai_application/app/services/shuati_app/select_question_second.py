import random
from collections import defaultdict

from django.core.cache import cache
from django.db.models import OuterRef, Exists, Q
from pydantic import Field

from app.models import (
    STQuestion, STKnowledge, STUserPaperQuestionAnswer, STUserKnowledgeMasteryLevel
)
from app.services.shuati_app.constants import QuestionType
from app.services.shuati_app.dto import QuestionSelectStrategy
from app.services.shuati_app.values import question_score_rules
from django_ext.base_dto_model import MyBaseModel
from django_ext.exceptions import InternalException


class SelectedKnowledgeDistribution(MyBaseModel):
    wrong_kg_ids: list = Field(default=[])
    unlearned_high_kg_ids: list = Field(default=[])
    unlearned_low_kg_ids: list = Field(default=[])
    mastery_kg_ids: list = Field(default=[])

    # 剩余的高频低频知识点
    other_unlearned_high_kg_ids: list = Field(default=[])
    other_unlearned_low_kg_ids: list = Field(default=[])

    def get_selected_kg_ids(self):
        return [
            *self.wrong_kg_ids,
            *self.unlearned_high_kg_ids,
            *self.unlearned_low_kg_ids,
            *self.mastery_kg_ids,
        ]


class BasicQuestionGenerator:

    def __init__(
            self,
            subject_id,
            core_course_code,
            user_id,
            strategy: QuestionSelectStrategy,
            is_all_wrong_kg: bool = False,
            cycle_num: int = 1
    ):
        self.subject_id = subject_id
        self.core_course_code = core_course_code
        self.user_id = user_id
        self.is_all_wrong_kg = is_all_wrong_kg
        self.cycle_num = cycle_num

        if not strategy:
            raise InternalException(code=50000, detail='出题策略错误了')

        # 处理问题分数规则
        question_score_rules_map = defaultdict(lambda: defaultdict(int))
        for i in question_score_rules:
            question_score_rules_map[i.question_type][i.difficulty] = i.score
        self.question_score_rules_map = question_score_rules_map

        self.strategy = strategy
        self.max_question_num = self.strategy.choice_question_num + self.strategy.subjective_question_num

        self.high_freq_kg_ids = []
        self.low_freq_kg_ids = []
        self.all_kg_ids = []
        self.unlearned_kg_ids = []

        self.selected_kg_dist = SelectedKnowledgeDistribution()

        self.selected_q_type_distribution = {
            STQuestion.Type.SINGLE.value: {i: 0 for i in range(1, 6)},
            STQuestion.Type.SUBJECTIVE.value: {i: 0 for i in range(1, 6)},
        }

        self.selected_q_type_questions = {
            STQuestion.Type.SINGLE.value: [],
            STQuestion.Type.SUBJECTIVE.value: [],
        }

    def get_questions(self):
        self._init_selected_kg_ids()
        self._get_choice_questions()
        self._get_subjective_questions()

        selected_choice_questions = self.selected_q_type_questions[STQuestion.Type.SINGLE.value]
        selected_subjective_questions = self.selected_q_type_questions[STQuestion.Type.SUBJECTIVE.value]
        random.shuffle(selected_choice_questions)
        selected_questions = [*selected_choice_questions, *selected_subjective_questions]

        return [q['question_id'] for q in selected_questions]

    def get_total_score(self) -> float:
        score = 0
        for q_type, q_d_dist in self.selected_q_type_distribution.items():
            for d, q_num in q_d_dist.items():
                score += self.question_score_rules_map[q_type][d]
        return round(score, 1)

    def get_question_score_map(self) -> dict:
        question_score_map = {}
        for q_type, questions in self.selected_q_type_questions.items():
            for q in questions:
                q_id = q['question_id']
                q_d = q['difficulty']
                question_score_map[q_id] = self.question_score_rules_map[q_type][q_d]
        return question_score_map

    def _get_all_kg_ids_with_freq(self):
        threshold = self.strategy.high_freq_knowledge_threshold
        threshold_int = int(threshold * 100)
        cache_key = f'{self.core_course_code}_with_freq_kg_ids:{threshold_int}'
        res = cache.get(cache_key)
        if res is not None:
            return res.get('high', []), res.get('low', [])

        kg_qs = STKnowledge.objects.filter(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            kg_qs_count__gt=0
        ).values('id', 'percentage')
        high_freq_kg_ids = [k['id'] for k in kg_qs if k['percentage'] > self.strategy.high_freq_knowledge_threshold]
        low_freq_kg_ids = [k['id'] for k in kg_qs if k['percentage'] <= self.strategy.high_freq_knowledge_threshold]
        res = {'high': high_freq_kg_ids, 'low': low_freq_kg_ids}
        cache.set(cache_key, res, timeout=3600)
        return high_freq_kg_ids, low_freq_kg_ids

    def _init_all_kg_ids_with_freq(self):
        high_freq_kg_ids, low_freq_kg_ids = self._get_all_kg_ids_with_freq()
        self.high_freq_kg_ids = high_freq_kg_ids
        self.low_freq_kg_ids = low_freq_kg_ids
        self.all_kg_ids = [*high_freq_kg_ids, *low_freq_kg_ids]

    def _get_all_unlearned_kg_ids(self) -> list[int]:
        # 获取用户已答过的题目关联的知识点
        answered_knowledge_ids = list(STUserKnowledgeMasteryLevel.objects.filter(
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
        ).values_list('knowledge_id', flat=True))

        # 获取该课程的所有知识点
        unlearned_knowledges = STKnowledge.objects.filter(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            kg_qs_count__gt=0
        )
        # 从未学习知识点中排除已答过的知识点
        if answered_knowledge_ids:
            unlearned_knowledges = unlearned_knowledges.exclude(id__in=answered_knowledge_ids)

        return list(unlearned_knowledges.values_list('id', flat=True))

    def _init_all_unlearned_kg_ids(self):
        self.unlearned_kg_ids = self._get_all_unlearned_kg_ids()

    def _get_wrong_knowledge_percent(self):
        wrong_knowledge_percent = self.strategy.wrong_knowledge_percent
        if self.is_all_wrong_kg:
            wrong_knowledge_percent = 1
        return wrong_knowledge_percent

    def _get_wrong_knowledge_ids(self) -> list[int]:
        wrong_knowledge_percent = self._get_wrong_knowledge_percent()

        wrong_knowledge_num = int(self.strategy.total_knowledge_num * wrong_knowledge_percent)
        if not wrong_knowledge_num:
            return []

        # 查询用户在指定学科和课程中答错的题目对应的知识点
        wrong_knowledge_stats = STUserKnowledgeMasteryLevel.objects.filter(
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
        ).order_by('last_answer_time')[:wrong_knowledge_num]

        return [stat.knowledge_id for stat in wrong_knowledge_stats]

    def _init_selected_kg_ids(self):
        self._init_all_kg_ids_with_freq()
        self._init_all_unlearned_kg_ids()

        # 获取错题知识点
        wrong_kg_ids = self._get_wrong_knowledge_ids()

        # 获取未练知识点，区分高频、低频
        all_unlearned_high_kg_ids = list(set(self.unlearned_kg_ids) & set(self.high_freq_kg_ids))
        all_unlearned_low_kg_ids = list(set(self.unlearned_kg_ids) & set(self.low_freq_kg_ids))
        # 随机打乱知识点列表
        random.shuffle(all_unlearned_high_kg_ids)
        random.shuffle(all_unlearned_low_kg_ids)

        # 获取未练知识点数量，总知识点数量减去错误知识数量
        unlearned_kg_num = self.strategy.total_knowledge_num - len(wrong_kg_ids)

        # 优先获取未练知识点的题目
        high_unlearned_kg_num = int(unlearned_kg_num * self.strategy.high_freq_knowledge_percent)
        low_unlearned_kg_num = unlearned_kg_num - high_unlearned_kg_num

        # 按照数量获取知识点列表
        unlearned_high_kg_ids = all_unlearned_high_kg_ids[:high_unlearned_kg_num]
        unlearned_low_kg_ids = all_unlearned_low_kg_ids[:low_unlearned_kg_num]
        other_unlearned_high_kg_ids = self._get_other_kg_ids(all_unlearned_high_kg_ids, unlearned_high_kg_ids)
        other_unlearned_low_kg_ids = self._get_other_kg_ids(all_unlearned_high_kg_ids, unlearned_low_kg_ids)

        self.selected_kg_dist.wrong_kg_ids = wrong_kg_ids
        self.selected_kg_dist.unlearned_high_kg_ids = unlearned_high_kg_ids
        self.selected_kg_dist.unlearned_low_kg_ids = unlearned_low_kg_ids
        self.selected_kg_dist.other_unlearned_high_kg_ids = other_unlearned_high_kg_ids
        self.selected_kg_dist.other_unlearned_low_kg_ids = other_unlearned_low_kg_ids

        # 此处考虑获取正确率知识点集合的逻辑
        selected_kg_ids = self.selected_kg_dist.get_selected_kg_ids()
        mastery_kg_num = self.strategy.total_knowledge_num - len(selected_kg_ids)

        if mastery_kg_num:
            mastery_kg_ids = list(STUserKnowledgeMasteryLevel.objects.filter(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id
            ).exclude(
                knowledge_id__in=selected_kg_ids
            ).order_by('-accuracy').values_list('knowledge_id', flat=True))[:mastery_kg_num]
            self.selected_kg_dist.mastery_kg_ids = mastery_kg_ids

    def _get_choice_questions(self):
        q_type = STQuestion.Type.SINGLE.value

        # 获取全部知识点的题目分布
        selected_difficulties = self._get_difficulties_by_distribution(q_type)
        # 获取全部知识点，指定难度的题目分布情况
        knowledge_difficulty_questions = self._get_knowledge_difficulty_questions_exclude_answered(
            question_type=q_type,
            knowledge_ids=self.all_kg_ids,
            difficulties=selected_difficulties
        )

        wrong_knowledge_percent = self._get_wrong_knowledge_percent()

        # 先获取错题知识点
        max_wrong_q_num = int(wrong_knowledge_percent * self.max_question_num)
        wrong_questions = []
        if self.selected_kg_dist.wrong_kg_ids:
            wrong_questions = self._get_questions_by_kg_distribution(
                knowledge_difficulty_questions,
                q_type,
                self.selected_kg_dist.wrong_kg_ids,
                max_wrong_q_num
            )

        # 根据实际获得的错误知识点题目数量，计算出未练知识点题目数量
        unlearned_q_num = self.strategy.choice_question_num - len(wrong_questions)
        self._get_question_by_unlearned_kg(
            q_type,
            knowledge_difficulty_questions,
            unlearned_q_num
        )

    def _get_subjective_questions(self):
        # 获取全部知识点的题目分布
        if self.strategy.subjective_question_num == 0:
            return
        q_type = STQuestion.Type.SUBJECTIVE.value

        selected_difficulties = self._get_difficulties_by_distribution(q_type)
        knowledge_difficulty_questions = self._get_knowledge_difficulty_questions_exclude_answered(
            question_type=q_type,
            knowledge_ids=self.all_kg_ids,
            difficulties=selected_difficulties
        )

        # 主观题全部从未练知识点集合中获取
        max_q_num = self.strategy.subjective_question_num
        self._get_question_by_unlearned_kg(
            q_type,
            knowledge_difficulty_questions,
            max_q_num
        )

    def _get_question_by_unlearned_kg(
            self,
            q_type,
            knowledge_difficulty_questions,
            unlearned_q_num,
    ):
        max_unlearned_high_q_num = int(unlearned_q_num * self.strategy.high_freq_knowledge_percent)
        # 如果高频知识点占比不为0，至少要获取一个高频知识点
        if self.strategy.high_freq_knowledge_percent and max_unlearned_high_q_num < 1:
            max_unlearned_high_q_num = 1

        max_unlearned_low_q_num = unlearned_q_num - max_unlearned_high_q_num

        # 获取未练高频知识点
        if self.selected_kg_dist.unlearned_high_kg_ids:
            self._get_questions_by_kg_distribution(
                knowledge_difficulty_questions,
                q_type,
                self.selected_kg_dist.unlearned_high_kg_ids,
                max_unlearned_high_q_num
            )
        # 获取未练低频知识点
        if self.selected_kg_dist.unlearned_low_kg_ids:
            self._get_questions_by_kg_distribution(
                knowledge_difficulty_questions,
                q_type,
                self.selected_kg_dist.unlearned_low_kg_ids,
                max_unlearned_low_q_num
            )
        # 获取正确率集合知识点
        if self.selected_kg_dist.mastery_kg_ids:
            remaining_q_num = self._get_remaining_q_num(q_type)
            self._get_questions_by_kg_distribution(
                knowledge_difficulty_questions,
                q_type,
                self.selected_kg_dist.mastery_kg_ids,
                remaining_q_num
            )

        remaining_q_num = self._get_remaining_q_num(q_type)
        if remaining_q_num <= 0:
            return

        # 如果题目数量不够，则继续从高频知识点中获取题目
        if self.selected_kg_dist.other_unlearned_high_kg_ids:
            self._get_questions_by_kg_distribution(
                knowledge_difficulty_questions,
                q_type,
                self.selected_kg_dist.other_unlearned_high_kg_ids,
                remaining_q_num
            )
            remaining_q_num = self._get_remaining_q_num(q_type)
            if remaining_q_num <= 0:
                return

        # 高频获取完，则从低频获取
        if self.selected_kg_dist.other_unlearned_low_kg_ids:
            self._get_questions_by_kg_distribution(
                knowledge_difficulty_questions,
                q_type,
                self.selected_kg_dist.other_unlearned_low_kg_ids,
                remaining_q_num
            )
            remaining_q_num = self._get_remaining_q_num(q_type)
            if remaining_q_num <= 0:
                return

        # 如果题目还不够，则继续从知识点正确率集合中获取
        selected_kg_ids = self.selected_kg_dist.get_selected_kg_ids()
        addition_kg_ids = list(STUserKnowledgeMasteryLevel.objects.filter(
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id
        ).exclude(
            knowledge_id__in=selected_kg_ids
        ).order_by('-accuracy').values_list('knowledge_id', flat=True))
        if addition_kg_ids:
            self._get_questions_by_kg_distribution(
                knowledge_difficulty_questions,
                q_type,
                addition_kg_ids,
                remaining_q_num
            )

            remaining_q_num = self._get_remaining_q_num(q_type)
            if remaining_q_num <= 0:
                return

        # 如果题目还不够，则从错题集中获取
        # 获取指定知识点，指定难度的题目分布情况
        selected_difficulties = self._get_difficulties_by_distribution(q_type)
        wrong_kg_diff_questions = self._get_knowledge_difficulty_wrong_questions(
            question_type=q_type,
            knowledge_ids=self.all_kg_ids,
            difficulties=selected_difficulties
        )

        selected_kg_ids = self.selected_kg_dist.get_selected_kg_ids()
        self._get_questions_by_kg_distribution(
            wrong_kg_diff_questions,
            q_type,
            selected_kg_ids,
            remaining_q_num
        )

        remaining_q_num = self._get_remaining_q_num(q_type)
        if remaining_q_num <= 0:
            return

        # 如果还不够，则从剩余知识点中抽取错日
        other_selected_kg_ids = self._get_other_kg_ids(self.all_kg_ids, selected_kg_ids)
        self._get_questions_by_kg_distribution(
            wrong_kg_diff_questions,
            q_type,
            other_selected_kg_ids,
            remaining_q_num
        )

    def _get_remaining_q_num(self, q_type: int):
        if q_type == QuestionType.SINGLE.value:
            need_q_num = self.strategy.choice_question_num
        else:
            need_q_num = self.strategy.subjective_question_num
        return need_q_num - len(self.selected_q_type_questions[q_type])

    def _get_other_kg_ids(self, all_kg_ids, exclude_kg_ids) -> list:
        return [kg_id for kg_id in all_kg_ids if kg_id not in exclude_kg_ids]

    def _get_difficulties_by_distribution(self, q_type: int):
        q_distribution = self.strategy.question_type_distribution[q_type]

        # 支持难度选择是多选
        difficulties = []
        for d, q_num in q_distribution.items():
            if q_num <= 0:
                continue
            if isinstance(d, tuple):
                difficulties.extend(d)
            elif isinstance(d, str):
                try:
                    d_arr = [int(i.strip()) for i in d.split(',')]
                    difficulties.extend(d_arr)
                except:
                    pass
            else:
                difficulties.append(d)
        return difficulties

    def _get_unanswered_questions(self, question_type, knowledge_ids, difficulties=None):
        if difficulties is None:
            difficulties = []
        answered_sub_query = STUserPaperQuestionAnswer.objects.filter(
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            cycle_num=self.cycle_num,
            question_id=OuterRef('pk')
        ).values('question_id')
        qs_where = dict(
            is_deleted=False,
            question_type=question_type,
            stquestionknowledge__is_deleted=False,
            stquestionknowledge__knowledge_id__in=knowledge_ids,
        )
        if difficulties:
            qs_where['difficulty__in'] = difficulties
        qs = STQuestion.objects.filter(
            **qs_where
        ).filter(
            # 排除存在答题记录的题目（NOT EXISTS）
            ~Exists(answered_sub_query)
        ).values('id', 'difficulty', 'is_unified', 'knowledge_list').distinct()
        return qs

    def _get_wrong_questions(self, question_type, knowledge_ids, difficulties=None):
        if difficulties is None:
            difficulties = []
        answered_sub_query = STUserPaperQuestionAnswer.objects.filter(
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            score_rate__lt=80,
            cycle_num=self.cycle_num,
            question_id=OuterRef('pk')
        ).values('question_id')

        qs_where = dict(
            is_deleted=False,
            question_type=question_type,
            stquestionknowledge__is_deleted=False,
            stquestionknowledge__knowledge_id__in=knowledge_ids,
        )
        if difficulties:
            qs_where['difficulty__in'] = difficulties
        qs = STQuestion.objects.filter(
            **qs_where
        ).filter(
            Exists(answered_sub_query)
        ).values('id', 'difficulty', 'knowledge_list').distinct()
        return qs

    def _get_knowledge_difficulty_questions_exclude_answered(self, question_type, knowledge_ids, difficulties=None):
        """
        按知识点名称和难度组织题目
        :param question_type:
        :param knowledge_ids:
        :return:
        """
        if difficulties is None:
            difficulties = []

        # 获取与选中知识点相关的未答过题目ID
        unanswered_questions = self._get_unanswered_questions(question_type, knowledge_ids, difficulties)
        # 按知识点名称和难度组织题目
        knowledge_difficulty_questions = defaultdict(lambda: defaultdict(list))
        for q in unanswered_questions:
            for knowledge_id in q['knowledge_list']:
                knowledge_difficulty_questions[knowledge_id][q['difficulty']].append({
                    'question_id': q['id'],
                    'difficulty': q['difficulty'],
                    'knowledge_id': knowledge_id
                })
        return knowledge_difficulty_questions

    def _get_knowledge_difficulty_wrong_questions(self, question_type, knowledge_ids, difficulties=None):
        """
        按知识点名称和难度组织题目
        :param question_type:
        :param knowledge_ids:
        :return:
        """
        if difficulties is None:
            difficulties = []

        # 获取与选中知识点相关的未答过题目ID
        wrong_questions = self._get_wrong_questions(question_type, knowledge_ids, difficulties)
        # 按知识点名称和难度组织题目
        knowledge_difficulty_questions = defaultdict(lambda: defaultdict(list))
        for q in wrong_questions:
            for knowledge_id in q['knowledge_list']:
                knowledge_difficulty_questions[knowledge_id][q['difficulty']].append({
                    'question_id': q['id'],
                    'difficulty': q['difficulty'],
                    'knowledge_id': knowledge_id
                })
        return knowledge_difficulty_questions

    def _get_questions_by_kg_distribution(
            self,
            knowledge_difficulty_questions,
            q_type: int,
            kg_ids: list,
            max_q_num: int,
    ) -> list:
        # 获取本次需要获取的难度题目分布
        # this_q_distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
        this_q_distribution = {}

        q_distribution = self.strategy.question_type_distribution[q_type]
        selected_distribution = self.selected_q_type_distribution[q_type]
        selected_questions = self.selected_q_type_questions[q_type]

        for difficulty, q_num in q_distribution.items():
            if isinstance(difficulty, tuple):
                together_selected_num = 0
                for d in difficulty:
                    together_selected_num += selected_distribution[d]
                d_q_num = q_num - together_selected_num
            elif isinstance(difficulty, str):
                together_selected_num = 0
                try:
                    d_arr = [int(i.strip()) for i in difficulty.split(',')]
                except:
                    d_arr = []
                for d in d_arr:
                    together_selected_num += selected_distribution[d]
                d_q_num = q_num - together_selected_num
            else:
                d_q_num = q_num - selected_distribution[difficulty]
            this_q_distribution[difficulty] = d_q_num if d_q_num > 0 else 0

        # 重置知识点列表
        new_kg_ids = [*kg_ids]
        random.shuffle(new_kg_ids)
        this_selected_questions = []
        for difficulty, q_num in this_q_distribution.items():
            for _ in range(q_num):
                for kg_idx, kg_id in enumerate(new_kg_ids):
                    if isinstance(difficulty, tuple):
                        difficulty_priority = difficulty
                    elif isinstance(difficulty, str):
                        try:
                            difficulty_priority = [int(i.strip()) for i in difficulty.split(',')]
                        except:
                            difficulty_priority = []
                    else:
                        difficulty_priority = [difficulty]

                    # 优先获取统考题目
                    question = self._get_question_with_difficulty(
                        knowledge_difficulty_questions,
                        knowledge_id=kg_id,
                        difficulty_priority=difficulty_priority,
                        is_unified=True,
                        exclude_questions=selected_questions
                    )
                    if not question:
                        question = self._get_question_with_difficulty(
                            knowledge_difficulty_questions,
                            knowledge_id=kg_id,
                            difficulty_priority=difficulty_priority,
                            is_unified=False,
                            exclude_questions=selected_questions
                        )

                    if question:
                        this_selected_questions.append(question)

                        selected_distribution[question['difficulty']] += 1
                        selected_questions.append(question)
                        # 将选中的知识点排到selected_knowledges列表最后面
                        new_kg_ids.append(new_kg_ids.pop(kg_idx))
                        break
                if len(this_selected_questions) >= max_q_num:
                    return this_selected_questions
        return this_selected_questions

    def _get_question_with_difficulty(
            self,
            knowledge_difficulty_questions,
            knowledge_id,
            difficulty_priority: list,
            is_unified: bool = True,
            exclude_questions=None
    ):
        """
        根据难度优先级获取题目，如果指定难度无题则返回空
        """
        if exclude_questions is None:
            exclude_questions = []
        exclude_question_ids = [q['question_id'] for q in exclude_questions]

        # 按照优先级顺序查找题目
        for difficulty in difficulty_priority:
            questions = knowledge_difficulty_questions[knowledge_id][difficulty]
            # 随机打乱一下题目顺序
            if questions:
                random.shuffle(questions)
            for question in questions:
                q_is_unified = bool(question.get('is_unified', 0))
                if q_is_unified != is_unified:
                    continue

                if question['question_id'] not in exclude_question_ids:
                    return question
        return None

    def _get_questions_by_kg_difficulty2(
            self,
            knowledge_difficulty_questions,
            q_type: int,
            kg_ids: list,
            difficulties: list,
            max_q_num: int,
    ) -> list:
        selected_distribution = self.selected_q_type_distribution[q_type]
        selected_questions = self.selected_q_type_questions[q_type]

        # 重置知识点列表
        new_kg_ids = [*kg_ids]
        random.shuffle(new_kg_ids)
        this_selected_questions = []
        for difficulty in difficulties:
            for kg_idx, kg_id in enumerate(new_kg_ids):
                question = self._get_question_with_difficulty(
                    knowledge_difficulty_questions,
                    knowledge_id=kg_id,
                    difficulty_priority=[difficulty],
                    exclude_questions=selected_questions
                )
                if question:
                    this_selected_questions.append(question)

                    selected_distribution[difficulty] += 1
                    selected_questions.append(question)

                    # 将选中的知识点排到selected_knowledges列表最后面
                    new_kg_ids.append(new_kg_ids.pop(kg_idx))
                    break
            if len(this_selected_questions) >= max_q_num:
                return this_selected_questions
        return this_selected_questions


class ModiQuestionGenerator(BasicQuestionGenerator):
    def __init__(
            self,
            subject_id,
            core_course_code,
            user_id,
            strategy,
            is_all_wrong_kg: bool = False,
            cycle_num: int = 1,
            is_first_round=False
    ):
        super().__init__(subject_id, core_course_code, user_id, strategy, is_all_wrong_kg, cycle_num)
        self.is_first_round = is_first_round

    def get_questions(self):
        # 摸底测只取选择题
        self._get_choice_questions()

        selected_choice_questions = self.selected_q_type_questions[STQuestion.Type.SINGLE.value]
        random.shuffle(selected_choice_questions)

        return [q['question_id'] for q in selected_choice_questions]

    def _get_first_round_kg_ids(self) -> list:
        cache_key = f'{self.core_course_code}_first_round_kg_ids'
        kg_ids = cache.get(cache_key)
        if kg_ids is not None:
            return kg_ids

        kg_ids = list(STKnowledge.objects.filter(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            kg_qs_count__gt=0,
            is_first_round=True
        ).values_list('id', flat=True))
        cache.set(cache_key, kg_ids, timeout=3600)
        return kg_ids

    def _get_first_round_questions(self):
        q_type = STQuestion.Type.SINGLE.value

        # 如果是第一轮，则获取第一轮知识点
        kg_ids = self._get_first_round_kg_ids()
        # 获取高频知识点的题目分布
        selected_difficulties = self._get_difficulties_by_distribution(q_type)

        knowledge_difficulty_questions = self._get_knowledge_difficulty_questions_exclude_answered(
            question_type=STQuestion.Type.SINGLE.value,
            knowledge_ids=kg_ids,
            difficulties=selected_difficulties
        )

        # 按照数量获取知识点列表
        max_q_num = self.strategy.choice_question_num
        total_knowledge_num = self.strategy.total_knowledge_num
        selected_kg_ids = kg_ids[:total_knowledge_num]

        self._get_questions_by_kg_distribution(
            knowledge_difficulty_questions,
            q_type,
            selected_kg_ids,
            max_q_num
        )

        if len(self.selected_q_type_questions[q_type]) >= self.strategy.choice_question_num:
            return

        # 如果题目数量不够，则继续从知识点中获取题目
        other_q_num = self.strategy.choice_question_num - len(self.selected_q_type_questions[q_type])
        other_kg_ids = kg_ids[total_knowledge_num:]

        self._get_questions_by_kg_distribution(
            knowledge_difficulty_questions,
            q_type,
            other_kg_ids,
            other_q_num
        )
        # 其他不够情况暂不考虑

    def _get_choice_questions(self):
        if self.is_first_round:
            return self._get_first_round_questions()

        q_type = STQuestion.Type.SINGLE.value
        self._init_all_kg_ids_with_freq()

        selected_difficulties = self._get_difficulties_by_distribution(q_type)
        # 获取高频知识点的题目分布
        knowledge_difficulty_questions = self._get_knowledge_difficulty_questions_exclude_answered(
            question_type=q_type,
            knowledge_ids=self.high_freq_kg_ids,
            difficulties=selected_difficulties
        )

        # 随机打乱知识点列表
        random.shuffle(self.high_freq_kg_ids)

        max_q_num = self.strategy.choice_question_num
        # 获取未练知识点数量，总知识点数量减去主观题知识数量
        total_knowledge_num = self.strategy.total_knowledge_num
        # 按照数量获取知识点列表
        high_kg_ids = self.high_freq_kg_ids[:total_knowledge_num]

        # 获取未练高频知识点
        if high_kg_ids:
            self._get_questions_by_kg_distribution(
                knowledge_difficulty_questions,
                q_type,
                high_kg_ids,
                max_q_num
            )
        remaining_q_num = self._get_remaining_q_num(q_type)
        if remaining_q_num <= 0:
            return

        # 如果题目数量不够，则继续从其他高频知识点中获取题目
        other_high_kg_ids = self.high_freq_kg_ids[total_knowledge_num:]

        self._get_questions_by_kg_distribution(
            knowledge_difficulty_questions,
            q_type,
            other_high_kg_ids,
            remaining_q_num
        )
        remaining_q_num = self._get_remaining_q_num(q_type)
        if remaining_q_num <= 0:
            return

        # 题目数量不够，从低频知识点获取
        # 获取高频知识点的题目分布
        knowledge_difficulty_questions = self._get_knowledge_difficulty_questions_exclude_answered(
            question_type=q_type,
            knowledge_ids=self.low_freq_kg_ids,
            difficulties=selected_difficulties
        )

        # 随机打乱知识点列表
        low_kg_ids = [*self.low_freq_kg_ids]
        random.shuffle(low_kg_ids)

        self._get_questions_by_kg_distribution(
            knowledge_difficulty_questions,
            q_type,
            low_kg_ids,
            remaining_q_num
        )
        # 摸底轮只有三轮，其他不够情况暂不考虑

    def _get_unanswered_questions(self, question_type, knowledge_ids, difficulties=None):
        # 摸底轮，获取未答题目的逻辑可以简化一下；因为第一轮没有未答题目，第二轮未答题目数量少
        qs_where = dict(
            is_deleted=False,
            question_type=question_type,
            stquestionknowledge__is_deleted=False,
            stquestionknowledge__knowledge_id__in=knowledge_ids,
        )
        if difficulties:
            qs_where['difficulty__in'] = difficulties

        if self.is_first_round:
            qs = STQuestion.objects.filter(
                **qs_where
            ).values('id', 'difficulty', 'is_unified', 'knowledge_list')
        else:
            answered_q_ids = list(STUserPaperQuestionAnswer.objects.filter(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                cycle_num=self.cycle_num,
            ).values_list('question_id', flat=True))
            qs = STQuestion.objects.filter(
                **qs_where
            ).filter(
                ~Q(id__in=answered_q_ids)
            ).values('id', 'difficulty', 'is_unified', 'knowledge_list')

        data = []
        q_ids = []
        for i in qs:
            if i['id'] in q_ids:
                continue
            data.append(i)
        return data
