import json
import logging
import uuid
from contextlib import contextmanager

import psycopg2.extras
import psycopg2.errors
import psycopg2.pool
from django.conf import settings

from app.core.rag.embedding.base_embedding import Embeddings
from app.core.rag.models.document import Document
from app.core.rag.vdb.vector_base import BaseVector, BaseVectorFactory
from app.core.rag.vdb.vector_type import VectorType
from app.libs.redis import redis_default_client
from app.models import Dataset

logger = logging.getLogger(__name__)


SQL_CREATE_TABLE = """
CREATE TABLE IF NOT EXISTS {table_name} (
    id serial PRIMARY KEY,
    document_no UUID NOT NULL,
    doc_id UUID NOT NULL,
    text TEXT NOT NULL,
    meta JSONB NOT NULL,
    embedding REAL[] NOT NULL
) DISTRIBUTED BY (id);
CREATE INDEX document_no_idx_{index_suffix}
    ON {table_name} (document_no);
CREATE INDEX doc_id_idx_{index_suffix}
    ON {table_name} (doc_id);
CREATE INDEX embedding_idx_{index_suffix}
    ON {table_name}  
    USING ANN(embedding) 
    WITH (DIM={dimension});
"""


class AnalyticPGVector(BaseVector):
    def __init__(self, collection_name: str, index_suffix: str):
        super().__init__(collection_name)
        self.pool = self._create_connection_pool(settings.ANALYTIC_PG)
        self.table_name = f"embedding_{collection_name}"
        self.index_suffix = index_suffix

    def get_type(self) -> str:
        return VectorType.ANALYTIC_PGVECTOR.value

    def _create_connection_pool(self, config: dict):
        return psycopg2.pool.SimpleConnectionPool(
            1,
            5,
            host=config['HOST'],
            port=config['PORT'],
            user=config['USER'],
            password=config['PASSWORD'],
            database=config['NAME'],
        )

    @contextmanager
    def _get_cursor(self):
        conn = self.pool.getconn()
        cur = conn.cursor()
        try:
            yield cur
        finally:
            cur.close()
            conn.commit()
            self.pool.putconn(conn)

    def create(self, texts: list[Document], embeddings: list[list[float]], **kwargs):
        dimension = len(embeddings[0])
        self._create_collection(dimension)
        return self.add_texts(texts, embeddings)

    def add_texts(self, documents: list[Document], embeddings: list[list[float]], **kwargs):
        values = []
        pks = []
        for i, doc in enumerate(documents):
            document_no = doc.metadata.get("document_no", str(uuid.uuid4()))
            doc_id = doc.metadata.get("doc_id", str(uuid.uuid4()))
            pks.append(doc_id)
            values.append(
                (
                    document_no,
                    doc_id,
                    doc.page_content,
                    json.dumps(doc.metadata),
                    embeddings[i],
                )
            )
        with self._get_cursor() as cur:
            psycopg2.extras.execute_values(
                cur, f"INSERT INTO {self.table_name} (document_no, doc_id, text, meta, embedding) VALUES %s", values
            )

    def text_exists(self, doc_id: str) -> bool:
        with self._get_cursor() as cur:
            cur.execute(f"SELECT id FROM {self.table_name} WHERE doc_id = %s", (doc_id,))
            return cur.fetchone() is not None

    def get_by_doc_ids(self, doc_ids: list[str]) -> list[Document]:
        with self._get_cursor() as cur:
            cur.execute(f"SELECT meta, text FROM {self.table_name} WHERE doc_id IN %s", (tuple(doc_ids),))
            docs = []
            for record in cur:
                docs.append(Document(page_content=record[1], metadata=record[0]))
        return docs

    def delete_by_doc_ids(self, doc_ids: list[str]) -> None:
        with self._get_cursor() as cur:
            cur.execute(f"DELETE FROM {self.table_name} WHERE doc_id IN %s", (tuple(doc_ids),))

    def delete_by_metadata_field(self, key: str, value: str) -> None:
        with self._get_cursor() as cur:
            cur.execute(f"DELETE FROM {self.table_name} WHERE meta->>%s = %s", (key, value))

    def search_by_vector(self, query_vector: list[float], **kwargs) -> list[Document]:
        """
        Search the nearest neighbors to a vector.

        :param query_vector: The input vector to search for similar items.
        :param kwargs:
            top_k: The number of nearest neighbors to return, default is 3.
            score_threshold: The distance of nearest neighbors to return, default is 0.
        :return: List of Documents that are nearest to the query vector.
        """
        top_k = kwargs.get("top_k", 3)
        score_threshold = kwargs.get("score_threshold", 0.0)
        document_nos = kwargs.get("document_nos", [])

        query_vector_str = "{" + ",".join(map(str, query_vector)) + "}"

        if document_nos:
            document_nos_str = ','.join([f"'{i}'" for i in document_nos])
            sql = f"SELECT meta, text, l2_distance(embedding, '{query_vector_str}'::real[]) AS distance FROM {self.table_name} where document_no in ({document_nos_str}) ORDER BY embedding <-> '{query_vector_str}'::real[] LIMIT {top_k}"
        else:
            sql = f"SELECT meta, text, l2_distance(embedding, '{query_vector_str}'::real[]) AS distance FROM {self.table_name} ORDER BY embedding <-> '{query_vector_str}'::real[] LIMIT {top_k}"

        with self._get_cursor() as cur:
            # SELECT ID, l2_distance(<VECTOR_COLUMN_NAME>, array[1,2,3...N]::float4[]) as score FROM <TABLE_NAME> ORDER BY <VECTOR_COLUMN_NAME> <-> array[1,2,3...N]::float4[] LIMIT <TOPK>;
            # f"SELECT meta, text, embedding <=> %s AS distance FROM {self.table_name} ORDER BY distance LIMIT {top_k}",
            cur.execute(sql)
            docs = []
            for record in cur:
                metadata, text, distance = record
                # score = 1 - distance
                score = distance
                metadata["score"] = score
                if score_threshold is None or score > score_threshold:
                    docs.append(Document(page_content=text, metadata=metadata))
        return docs

    def search_by_full_text(self, query: str, **kwargs) -> list[Document]:
        # do not support bm25 search
        return []

    def delete(self) -> None:
        with self._get_cursor() as cur:
            cur.execute(f"DROP TABLE IF EXISTS {self.table_name}")

    def _create_collection(self, dimension: int):
        cache_key = f"vector_indexing_{self._collection_name}"
        lock_name = f"{cache_key}_lock"
        with redis_default_client.lock(lock_name, timeout=20):
            collection_exist_cache_key = f"vector_indexing_{self._collection_name}"
            if redis_default_client.get(collection_exist_cache_key):
                return

            try:
                with self._get_cursor() as cur:
                    cur.execute(SQL_CREATE_TABLE.format(
                        table_name=self.table_name,
                        dimension=dimension,
                        index_suffix=self.index_suffix
                    ))
            except psycopg2.errors.DuplicateTable as e:
                # 忽略索引重复创建的问题
                logger.exception(e)
                pass
            redis_default_client.set(collection_exist_cache_key, 1, ex=3600)


class AnalyticPGVectorFactory(BaseVectorFactory):

    def init_vector(self, dataset: Dataset, attributes: list, embeddings: Embeddings) -> AnalyticPGVector:
        if not dataset.index_struct:
            collection_name = Dataset.gen_collection_name_by_id(dataset)
            dataset.index_struct = self.gen_index_struct_dict(VectorType.ANALYTIC_PGVECTOR, collection_name)
            dataset.save(update_fields=["index_struct"])
        else:
            collection_name = dataset.index_struct["vector_store"]["class_prefix"]

        return AnalyticPGVector(collection_name=collection_name, index_suffix=str(dataset.id))
