import math
from decimal import Decimal

from django.db import transaction
from django.db.models import Sum

from app.models import (
    STUserPaperAnswer, STUserRoundAnalysis, STUserPaper, STUserPaperQuestionAnswer,
    STUserKnowledgeDistribution, STQuestionKnowledge, STQuestion, STUserRoundChangeRecord, STUserKnowledgeMasteryLevel
)
from app.services.shuati_app import stage_report_templates
from app.services.shuati_app.constants import LearningStage, StageChangeType, LEARNING_STAGE_LABELS
from app.services.shuati_app.dto import UpgradeRequired, LearningStageRequired, ModiUpRequired
from app.services.shuati_app.subject_strategy import get_subject_change_required, get_subject_question_strategy
from app.services.shuati_app.values import subject_learning_stage_map
from django_ext.exceptions import InternalException


class LearnStageClient:

    def __init__(self, paper_answer: STUserPaperAnswer):
        self.paper_answer = paper_answer
        self.paper = paper_answer.paper
        self.subject_id = self.paper.subject_id
        self.core_course_code = self.paper.core_course_code
        self.user_id = self.paper.user_id

        self.strategy_map = get_subject_question_strategy(self.subject_id, self.core_course_code)
        self.stage_required_map = get_subject_change_required(self.subject_id, self.core_course_code)

        self.modi_up_required: ModiUpRequired | None = self.stage_required_map.get(LearningStage.MODI)
        if not self.modi_up_required:
            raise InternalException(code=50000, detail='阶段变更错误')

        self.user_round_analysis, _ = STUserRoundAnalysis.objects.get_or_create(
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id
        )

        # 升级弹窗模板使用参数
        # 摸底阶段的题目正确率
        self.modi_q_accuracy = 0
        # 核心正确率
        self.core_q_accuracy = 0
        # 目标核心正确率
        self.target_core_q_accuracy = 0

        # # TODO DEBUG使用
        # self.user_round_analysis.stage_change_type = StageChangeType.STRENGTHEN
        # self.user_round_analysis.examined_rounds = 11
        # self.user_round_analysis.stage_examined_rounds = 2
        # self.user_round_analysis.learning_stage = LearningStage.BASIC_IMPROVE_L
        # self.user_round_analysis.is_stage_strengthen = 1
        # self.user_round_analysis.cycle_num = 1

        self.examined_rounds = self.user_round_analysis.examined_rounds + 1
        self.stage_examined_rounds = self.user_round_analysis.stage_examined_rounds + 1
        self.total_assessment_score = self.user_round_analysis.total_assessment_score + paper_answer.answered_score
        self.total_subjective_score = self.user_round_analysis.total_subjective_score + paper_answer.subjective_score

        self.is_curr_stage_upgraded = self.user_round_analysis.stage_change_type == StageChangeType.UP
        self.is_curr_stage_degraded = self.user_round_analysis.stage_change_type == StageChangeType.DOWN
        self.is_stage_strengthen = self.user_round_analysis.is_stage_strengthen
        # self.strengthen_pass_rounds = self.user_round_analysis.strengthen_pass_rounds
        self.cycle_num = self.user_round_analysis.cycle_num

        self.is_test_stop = False
        self.stop_reason = 0
        self.stage_change_type = None  # up, down
        self.stage_change_report = None  # up, down
        self.curr_learning_stage = self.user_round_analysis.learning_stage
        self.new_learning_stage = self.user_round_analysis.learning_stage
        self.new_stage_total_rounds = 1

    def update_round_analysis(self):
        # 更新轮次
        self._update_learning_stage()
        self._save_round_analysis()

    def _update_learning_stage(self):
        # 摸底轮判断
        if self.curr_learning_stage == LearningStage.MODI:
            # 默认3轮
            rounds_required = self.modi_up_required.rounds_required or 3
            if self.stage_examined_rounds < rounds_required:
                return

            # 第3轮判断摸底测结果, 已经练习了2轮，本轮即为第3轮
            # 摸底后的提升逻辑固定写死，暂不处理
            if self.stage_examined_rounds == rounds_required:
                if self._check_modi_up():
                    # 能力提升-初级
                    self.stage_change_type = StageChangeType.UP
                    self.new_learning_stage = LearningStage.BASIC_IMPROVE_L
                else:
                    # 基础巩固
                    self.stage_change_type = StageChangeType.UP
                    self.new_learning_stage = LearningStage.BASIC

                self._check_new_stage_total_rounds()
            return

        # 正式刷题后的判断逻辑
        stage_list = subject_learning_stage_map.get(self.core_course_code)
        curr_stage_required = self.stage_required_map.get(self.curr_learning_stage)
        self._check_learning_stage_change(curr_stage_required)

        if self.stage_change_type == StageChangeType.UP:
            curr_stage_index = stage_list.index(self.curr_learning_stage)
            next_stage_index = curr_stage_index + 1
            if next_stage_index >= len(stage_list):
                # 没有下一级了，表示通关了
                self.is_test_stop = True
                self.stop_reason = 1
            else:
                self.new_learning_stage = stage_list[next_stage_index]
                self._check_new_stage_total_rounds()

        elif self.stage_change_type == StageChangeType.STRENGTHEN:
            self.is_stage_strengthen = True
            self._check_new_stage_total_rounds()

        elif self.stage_change_type == StageChangeType.DOWN:
            if not self.is_test_stop:
                curr_stage_index = stage_list.index(self.curr_learning_stage)
                prev_stage_index = curr_stage_index - 1
                if prev_stage_index < 0:
                    raise InternalException(code=50000, detail='阶段变更错误')
                self.new_learning_stage = stage_list[prev_stage_index]
                self._check_new_stage_total_rounds()

                # 降级需要判断用户是不是已经降级到该阶段过了
                if STUserRoundChangeRecord.objects.filter(
                    is_deleted=False,
                    subject_id=self.subject_id,
                    core_course_code=self.core_course_code,
                    user_id=self.user_id,
                    new_stage=self.new_learning_stage,
                    change_type=StageChangeType.DOWN,
                    cycle_num=self.cycle_num,
                ).count() > 0:
                    self.is_test_stop = True
                    self.stop_reason = 2

    def _check_new_stage_total_rounds(self):
        # 判断新逻辑的轮数
        if self.stage_change_type == StageChangeType.STRENGTHEN:
            # 强化轮，则返回强化轮次的规则
            curr_stage_required = self.stage_required_map.get(self.curr_learning_stage)
            self.new_stage_total_rounds = curr_stage_required.strengthen_round_required.rounds_required
            return

        # 如果是降级后的逻辑，取再次升级的
        if self.stage_change_type == StageChangeType.DOWN:
            new_stage_required = self.stage_required_map.get(self.new_learning_stage)
            self.new_stage_total_rounds = new_stage_required.up_again_required.stage_rounds_required
            return

        # 获取下一个轮次的策略
        single_strategy_map = self.strategy_map.get(self.new_learning_stage)

        has_new_stage_up_again = STUserRoundChangeRecord.objects.filter(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            new_stage=self.new_learning_stage,
            change_type=StageChangeType.UP,
            cycle_num=self.cycle_num,
        ).count() > 0
        # print('has_new_stage_up_again', has_new_stage_up_again)

        new_stage_required = self.stage_required_map.get(self.new_learning_stage)
        # 如果之前到达过该轮次，使用强化轮的出题策略
        if has_new_stage_up_again:
            # print('new_stage_total_rounds', new_stage_required.strengthen_round_required)
            self.new_stage_total_rounds = new_stage_required.strengthen_round_required.rounds_required
            return

        strategy = single_strategy_map.get('normal')
        round_required = new_stage_required.up_required

        # 判断有没有轮数要求，如果则直接使用
        if round_required.rounds_required:
            self.new_stage_total_rounds = round_required.rounds_required
        else:
            for r in new_stage_required.up_required.question_required:
                # 不考虑没有题型和难度的
                if r.question_type == -1 or r.difficulty == -1:
                    continue

                # 查看要求总题数
                total_where = dict(
                    is_deleted=False,
                    subject_id=self.subject_id,
                    core_course_code=self.core_course_code,
                    knowledge_count__gt=0,
                )
                if r.question_type != -1:
                    total_where['question_type'] = r.question_type
                if r.difficulty != -1:
                    total_where['difficulty'] = r.difficulty
                q_num = STQuestion.objects.filter(**total_where).count()

                # 查看已练题数
                practice_where = dict(
                    is_deleted=False,
                    subject_id=self.subject_id,
                    core_course_code=self.core_course_code,
                    user_id=self.user_id,
                    cycle_num=self.cycle_num,
                )
                if r.question_type != -1:
                    practice_where['question__question_type'] = r.question_type
                if r.difficulty != -1:
                    practice_where['question__difficulty'] = r.difficulty
                practice_q_num = STUserPaperQuestionAnswer.objects.filter(
                    **practice_where
                ).values('question_id').distinct().count()
                need_q_num = int(q_num * r.num_rate) - practice_q_num
                need_q_num = need_q_num if need_q_num > 0 else 0
                # print('practice_where', practice_where)
                # print('practice_q_num', practice_q_num)
                # print('need_q_num', need_q_num)
                # print('q_num', q_num, r.num_rate, int(q_num * r.num_rate))

                # 不考虑共用题目难度的情况
                question_type_distribution = strategy.question_type_distribution[r.question_type]
                pre_round_q_num = 0
                for d, q_num in question_type_distribution.items():
                    d = int(d.split(',')[0])
                    if d == r.difficulty:
                        pre_round_q_num = q_num
                        break
                if not pre_round_q_num:
                    continue

                self.new_stage_total_rounds = math.ceil(need_q_num / pre_round_q_num)
                if self.new_stage_total_rounds < 1:
                    self.new_stage_total_rounds = 1

                break

    def _get_stage_change_report_params(self):
        """
        计算阶段变更报告所需参数
        :return: dict 包含报告所需的各种参数
        """
        # 已练题目总数
        total_q_num = self._get_total_practice_q_num(question_type=-1, difficulty=-1)

        # 计算本轮需要知识点和已覆盖知识点
        practiced_kg_num = 0
        target_kg_num = 0
        if self.curr_learning_stage != LearningStage.MODI:
            current_stage_required = self.stage_required_map.get(self.curr_learning_stage)
            if current_stage_required and hasattr(current_stage_required, 'up_required'):
                for r in current_stage_required.up_required.knowledge_required:
                    # 获取题型难度知识点数量的分布
                    kg_num = STQuestionKnowledge.objects.filter(
                        is_deleted=False,
                        question__subject_id=self.subject_id,
                        question__core_course_code=self.core_course_code,
                        question__is_deleted=False,
                        question__question_type=r.question_type,
                        question__difficulty=r.difficulty
                    ).values('knowledge_id').distinct().count()
                    target_kg_num = int(kg_num * r.num_rate)

                    # 计算该用户在这门科目这个course累计做过的所有知识点数量
                    practiced_kg_num = STUserKnowledgeDistribution.objects.filter(
                        subject_id=self.subject_id,
                        core_course_code=self.core_course_code,
                        user_id=self.user_id,
                        question_type=r.question_type,
                        difficulty=r.difficulty,
                        answer_count__gt=0,
                        cycle_num=self.cycle_num,
                    ).values('knowledge_id').distinct().count()

        # 该阶段已练题目数量
        stage_q_num = self._get_user_answered_q_num_by_round(
            check_rounds=self.stage_examined_rounds,
            question_type=-1,
            difficulty=-1
        )

        weak_points = ''
        if self.stage_change_type == StageChangeType.FAIL_NEXT_CYCLE:
            week_kg_names = list(STUserKnowledgeMasteryLevel.objects.filter(
                user_id=self.user_id,
                subject_id=self.subject_id,
                core_course_code=self.core_course_code
            ).order_by('accuracy').values_list('knowledge__name', flat=True)[:3])
            weak_points = '\n'.join([f'- {k}' for k in week_kg_names])

        return {
            'total_q_num': total_q_num,
            'q_accuracy': self.modi_q_accuracy,
            'q_knowledge': practiced_kg_num,
            'target_q_knowledge': target_kg_num,
            'q_num': stage_q_num,
            'core_q_accuracy': self.core_q_accuracy,
            'target_core_q_accuracy': self.target_core_q_accuracy,
            'next_stage_rounds': self.new_stage_total_rounds,
            'weak_points': weak_points,
        }

    def _get_stage_change_report(self):
        params = self._get_stage_change_report_params()
        print(f"算出来的参数们为🔥{params}")
        # 升级逻辑
        if self.curr_learning_stage == LearningStage.MODI and self.new_learning_stage == LearningStage.BASIC:
            template = stage_report_templates.modi_up_basic_report_template
        elif self.curr_learning_stage == LearningStage.MODI and self.new_learning_stage == LearningStage.BASIC_IMPROVE_L:
            template = stage_report_templates.modi_up_basic_improve_l_report_template
        elif self.curr_learning_stage == LearningStage.BASIC and self.new_learning_stage == LearningStage.BASIC_IMPROVE_L:
            template = stage_report_templates.basic_up_improve_l_report_template
        elif self.curr_learning_stage == LearningStage.BASIC_IMPROVE_L and self.new_learning_stage == LearningStage.BASIC_IMPROVE_M:
            template = stage_report_templates.up_level_report_template
        elif self.curr_learning_stage == LearningStage.BASIC_IMPROVE_M and self.new_learning_stage == LearningStage.BASIC_IMPROVE_H:
            template = stage_report_templates.up_level_report_template
        elif self.curr_learning_stage == LearningStage.BASIC_IMPROVE_H and self.new_learning_stage == LearningStage.CORE_IMPROVE:
            template = stage_report_templates.up_level_report_template
        elif self.curr_learning_stage == LearningStage.BASIC_IMPROVE_H and self.new_learning_stage == LearningStage.CORE_BASIC:
            template = stage_report_templates.up_level_report_template
        elif self.curr_learning_stage == LearningStage.CORE_BASIC and self.new_learning_stage == LearningStage.CORE_IMPROVE_L:
            template = stage_report_templates.up_level_report_template
        elif self.curr_learning_stage == LearningStage.CORE_IMPROVE_L and self.new_learning_stage == LearningStage.CORE_IMPROVE_M:
            template = stage_report_templates.up_level_report_template
        elif self.curr_learning_stage == LearningStage.CORE_IMPROVE_M and self.new_learning_stage == LearningStage.CORE_IMPROVE_H:
            template = stage_report_templates.up_level_report_template
        # 降级逻辑
        elif self.curr_learning_stage == LearningStage.CORE_IMPROVE_H and self.new_learning_stage == LearningStage.CORE_IMPROVE_M:
            template = stage_report_templates.down_level_report_template
        elif self.curr_learning_stage == LearningStage.CORE_IMPROVE_M and self.new_learning_stage == LearningStage.CORE_IMPROVE_L:
            template = stage_report_templates.down_level_report_template
        elif self.curr_learning_stage == LearningStage.CORE_IMPROVE_L and self.new_learning_stage == LearningStage.CORE_BASIC:
            template = stage_report_templates.down_level_report_template
        elif self.curr_learning_stage == LearningStage.CORE_BASIC and self.new_learning_stage == LearningStage.BASIC_IMPROVE_H:
            template = stage_report_templates.down_level_report_template
        elif self.curr_learning_stage == LearningStage.BASIC_IMPROVE_H and self.new_learning_stage == LearningStage.BASIC_IMPROVE_M:
            template = stage_report_templates.down_level_report_template
        elif self.curr_learning_stage == LearningStage.BASIC_IMPROVE_M and self.new_learning_stage == LearningStage.BASIC_IMPROVE_L:
            template = stage_report_templates.down_level_report_template
        elif self.curr_learning_stage == LearningStage.BASIC_IMPROVE_L and self.new_learning_stage == LearningStage.BASIC:
            template = stage_report_templates.down_level_report_template
        # 进入强化阶段逻辑
        elif self.stage_change_type == StageChangeType.STRENGTHEN:
            template = stage_report_templates.strengthen_level_report_template
        elif self.stage_change_type == StageChangeType.PASS_NEXT_CYCLE:
            template = stage_report_templates.next_shuati_for_pass
        elif self.stage_change_type == StageChangeType.FAIL_NEXT_CYCLE:
            template = stage_report_templates.next_shuati_for_fail
        else:
            template = ''

        current_stage = LEARNING_STAGE_LABELS.get(self.curr_learning_stage, '')
        new_stage = LEARNING_STAGE_LABELS.get(self.new_learning_stage, '')
        return (
            template
            .replace("{q_num}", str(params['q_num']))
            .replace("{q_accuracy}", str(params['q_accuracy']))
            .replace("{core_q_accuracy}", str(params['core_q_accuracy']))
            .replace("{target_core_q_accuracy}", str(params['target_core_q_accuracy']))
            .replace("{q_knowledge}", str(params['q_knowledge']))
            .replace("{target_q_knowledge}", str(params['target_q_knowledge']))
            .replace("{current_stage}", str(current_stage))
            .replace("{new_stage}", str(new_stage))
            .replace("{total_q_num}", str(params['total_q_num']))
            .replace("{next_stage_rounds}", str(params['next_stage_rounds']))
            .replace("{weak_points}", str(params['weak_points']))
        )

    def _save_round_analysis(self):
        # 更新总评估得分和已考察轮次
        with (transaction.atomic()):
            if self.stage_change_type:
                # 结束测试后进入摸底阶段，测试周期加1
                if self.is_test_stop:
                    if self.stop_reason == 1:
                        self.stage_change_type = StageChangeType.PASS_NEXT_CYCLE
                    else:
                        self.stage_change_type = StageChangeType.FAIL_NEXT_CYCLE
                    self.new_learning_stage = LearningStage.MODI
                    modi_up_required: ModiUpRequired | None = self.stage_required_map.get(LearningStage.MODI)
                    self.new_stage_total_rounds = modi_up_required.rounds_required
                    self.cycle_num = self.cycle_num + 1

                # 返回报告，这边需要在每个变更的时候单独处理
                self.stage_change_report = self._get_stage_change_report()
                print(f"阶段变化报告如下🔥:{self.stage_change_report}")
                STUserRoundChangeRecord.objects.create(
                    subject_id=self.subject_id,
                    core_course_code=self.core_course_code,
                    user_id=self.user_id,
                    answer=self.paper_answer,
                    old_stage=self.curr_learning_stage,
                    new_stage=self.new_learning_stage,
                    change_type=self.stage_change_type,
                    change_report=self.stage_change_report,
                    cycle_num=self.cycle_num
                )

                self.user_round_analysis.stage_change_type = self.stage_change_type
                self.user_round_analysis.learning_stage = self.new_learning_stage
                # 阶段改变，重置轮数
                self.user_round_analysis.stage_change_type = self.stage_change_type
                self.user_round_analysis.stage_examined_rounds = 0
                self.user_round_analysis.stage_total_rounds = self.new_stage_total_rounds
            else:
                self.user_round_analysis.stage_examined_rounds = self.stage_examined_rounds

            # print('stage_examined_rounds', self.user_round_analysis.stage_examined_rounds)
            # raise  #

            self.user_round_analysis.examined_rounds = self.examined_rounds
            self.user_round_analysis.is_stage_strengthen = self.is_stage_strengthen
            # self.user_round_analysis.strengthen_pass_rounds = self.strengthen_pass_rounds
            self.user_round_analysis.total_assessment_score = self.total_assessment_score
            self.user_round_analysis.total_subjective_score = self.total_subjective_score
            self.user_round_analysis.cycle_num = self.cycle_num
            # 目前没有暂停测试
            # self.user_round_analysis.is_test_stop = self.is_test_stop
            # self.user_round_analysis.stop_reason = self.stop_reason
            self.user_round_analysis.save(update_fields=[
                'stage_change_type',
                'examined_rounds',
                'stage_total_rounds',
                'stage_examined_rounds',
                'is_stage_strengthen',
                # 'strengthen_pass_rounds',
                'total_assessment_score',
                'total_subjective_score',
                'learning_stage',
                'cycle_num',
                # 'is_test_stop',
                # 'stop_reason',
            ])

    def _check_modi_up(self) -> bool:
        # 获取前三轮的所有题目的满分
        res = STUserPaper.objects.filter(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            is_answered=True
        ).aggregate(
            total_paper_score=Sum('paper_score'),
        )
        total_paper_score = res.get('total_paper_score') or 0

        # 计算总分晋级阈值
        user_total_score = self.total_assessment_score
        total_score_threshold = total_paper_score * Decimal(self.modi_up_required.total_score_ratio)

        self.modi_q_accuracy = int(100 * user_total_score / total_paper_score)
        # print('modi_res:', user_total_score, total_paper_score, total_score_threshold)
        # 如果不满足，则不进行下一步
        if user_total_score < total_score_threshold:
            return False

        # 判断单项满足条件
        for r in self.modi_up_required.question_required:
            user_score_res = STUserPaperQuestionAnswer.objects.filter(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                question__question_type=r.question_type,
                question__difficulty=r.difficulty,
                cycle_num=self.cycle_num,
            ).aggregate(
                score=Sum('answer_score')
            )

            user_score = user_score_res.get('score') or 0
            # print('modi_user_score_res:', user_score, r.score_threshold)
            if user_score < r.score_threshold:
                return False

        return True

    def _get_total_practice_q_num(self, question_type, difficulty):
        practice_where = dict(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            cycle_num=self.cycle_num,
        )
        if question_type != -1:
            practice_where['question__question_type'] = question_type
        if difficulty != -1:
            practice_where['question__difficulty'] = difficulty
        practice_q_num = STUserPaperQuestionAnswer.objects.filter(
            **practice_where
        ).values('question_id').distinct().count()
        return practice_q_num

    def _check_learning_stage_change(self, stage_required: LearningStageRequired | None):
        if not stage_required:
            return

        # 判断当前阶段是否在阶段强化期
        if self.is_stage_strengthen and stage_required.strengthen_round_required:
            # 判断是否满足升级降级标准
            self._check_stage_strengthen(stage_required.strengthen_round_required)
            return

        # 判断用户是否是再次升级上来的, 再次升级的要求使用强化轮的要求
        if self.is_curr_stage_upgraded and stage_required.strengthen_round_required:
            has_curr_stage_up_again = STUserRoundChangeRecord.objects.filter(
                is_deleted=False,
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                new_stage=self.curr_learning_stage,
                change_type=StageChangeType.UP,
                cycle_num=self.cycle_num,
            ).count() > 1
            if has_curr_stage_up_again:
                # 判断之前是否升级到过该阶段
                self._check_stage_strengthen(stage_required.strengthen_round_required)
                return

        # 检查用户是否是降级到该阶段的
        if self.is_curr_stage_degraded and stage_required.up_again_required:
            up_again_required = stage_required.up_again_required
            stage_rounds_required = up_again_required.stage_rounds_required

            # 该阶段轮数不够，则跳过
            # 再次升级后需要判断阶段轮次要求
            if stage_rounds_required and self.stage_examined_rounds < stage_rounds_required:
                return

            if self._check_stage_required(stage_required.up_again_required):
                self.stage_change_type = StageChangeType.UP
                self.target_core_q_accuracy = self._get_up_target_q_accuracy(stage_required.up_again_required)
                return
            else:
                self.stage_change_type = StageChangeType.DOWN
                # 降级后如果没满足升级，则停止练习
                self.is_test_stop = True
                self.stop_reason = 2
                return

        # 检查正常升级，还会顺便检查降级到最后一个阶段直接升级的逻辑
        if self.stage_change_type is None and stage_required.up_required:
            if self._check_stage_required(stage_required.up_required):
                self.stage_change_type = StageChangeType.UP
                self.target_core_q_accuracy = self._get_curr_stage_up_target_q_accuracy(stage_required)
                return

        # 检查是否进入阶段强化
        if self.stage_change_type is None and stage_required.strengthen_required:
            if self._check_stage_required(stage_required.strengthen_required):
                self.stage_change_type = StageChangeType.STRENGTHEN
                self.target_core_q_accuracy = self._get_curr_stage_up_target_q_accuracy(stage_required)
                return

        if self.stage_change_type is None and stage_required.down_required:
            if self._check_stage_required(stage_required.down_required):
                self.stage_change_type = StageChangeType.DOWN
                self.target_core_q_accuracy = self._get_curr_stage_up_target_q_accuracy(stage_required)
                return

    def _get_curr_stage_up_target_q_accuracy(self, stage_required: LearningStageRequired) -> int:
        up_required = stage_required.up_required
        if not up_required:
            return 0

        return self._get_up_target_q_accuracy(up_required)

    def _get_up_target_q_accuracy(self, up_required: UpgradeRequired) -> int:
        for q_r in up_required.question_required:
            target_core_q_accuracy = q_r.accuracy
            if isinstance(q_r.accuracy, tuple) or isinstance(q_r.accuracy, list):
                target_core_q_accuracy = q_r.accuracy[1]
            return int(100 * target_core_q_accuracy)
        return 0

    def _check_stage_required(self, required: UpgradeRequired) -> bool:
        check_rounds = 0
        # 计算正确率时的轮次需要重新处理
        acc_check_rounds = self.stage_examined_rounds
        if required.rounds_required:
            check_rounds = required.rounds_required

        for r in required.knowledge_required:
            # 知识点分布暂不考虑轮次
            # 获取题型难度知识点数量的分布
            kg_num = STQuestionKnowledge.objects.filter(
                is_deleted=False,
                question__subject_id=self.subject_id,
                question__core_course_code=self.core_course_code,
                question__is_deleted=False,
                question__question_type=r.question_type,
                question__difficulty=r.difficulty
            ).values('knowledge_id').distinct().count()

            # 查看知识点练习数量
            practiced_kg_num = STUserKnowledgeDistribution.objects.filter(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                question_type=r.question_type,
                difficulty=r.difficulty,
                answer_count__gt=0,
                cycle_num=self.cycle_num
            ).values('knowledge_id').distinct().count()

            if practiced_kg_num < int(kg_num * r.num_rate):
                return False

        for r in required.question_required:
            # 如果检查轮次，则不考虑总题量要求
            num_rate = 0 if check_rounds else r.num_rate
            if num_rate:
                # 查看要求总题数
                total_where = dict(
                    is_deleted=False,
                    subject_id=self.subject_id,
                    core_course_code=self.core_course_code,
                    knowledge_count__gt=0,
                )
                if r.question_type != -1:
                    total_where['question_type'] = r.question_type
                if r.difficulty != -1:
                    total_where['difficulty'] = r.difficulty
                q_num = STQuestion.objects.filter(**total_where).count()

                # 查看已练题数
                practice_q_num = self._get_total_practice_q_num(r.question_type, r.difficulty)

                # print('practice_q_num:', practice_q_num, 'need:', q_num * r.num_rate)
                # # DEBUG使用
                # total_q_num, right_q_num = self._get_user_answered_stat_by_round(
                #     check_rounds=acc_check_rounds,
                #     question_type=r.question_type,
                #     difficulty=r.difficulty,
                # )
                # print('accuracy:', right_q_num / total_q_num, 'need:', r.accuracy)
                if practice_q_num < int(q_num * r.num_rate):
                    return False

            if r.accuracy:
                total_q_num, right_q_num = self._get_user_answered_stat_by_round(
                    check_rounds=acc_check_rounds,
                    question_type=r.question_type,
                    difficulty=r.difficulty,
                )
                # print('accuracy_real:', right_q_num / total_q_num, 'need:', r.accuracy)

                # 查看该阶段全部的正确率
                self.core_q_accuracy = int(100 * right_q_num / total_q_num)

                if total_q_num and not self._check_acc(right_q_num / total_q_num, r.accuracy):
                    return False

        return True

    def _check_stage_strengthen(self, strengthen_round_required: UpgradeRequired):
        # 该阶段轮数不够，则跳过
        stage_rounds_required = strengthen_round_required.rounds_required

        # 需要判断阶段轮次要求
        if stage_rounds_required and self.stage_examined_rounds < stage_rounds_required:
            return

        if self._check_stage_required(strengthen_round_required):
            # 检查该阶段通过的次数
            self.stage_change_type = StageChangeType.UP
        else:
            self.stage_change_type = StageChangeType.DOWN

        self.is_stage_strengthen = False
        # 使用强化阶段的正确率要求
        self.target_core_q_accuracy = self._get_up_target_q_accuracy(strengthen_round_required)

    def _get_user_answered_stat_by_round(
            self,
            check_rounds,
            question_type: int,
            difficulty: int
    ):
        where = dict(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
            cycle_num=self.cycle_num,
        )
        if question_type != -1:
            where['question__question_type'] = question_type
        if difficulty != -1:
            where['question__difficulty'] = difficulty
        if check_rounds:
            answer_ids = list(STUserPaperAnswer.objects.filter(
                is_deleted=False,
                paper__subject_id=self.subject_id,
                paper__core_course_code=self.core_course_code,
                user_id=self.user_id,
                is_finished=True,
                cycle_num=self.cycle_num,
            ).order_by('-id').values_list('pk', flat=True))[:check_rounds]
            where['answer_id__in'] = answer_ids

        # print('user_answered_stat_by_round', where)
        total_q_num = STUserPaperQuestionAnswer.objects.filter(**where).count()

        where['score_rate__gte'] = 80
        right_q_num = STUserPaperQuestionAnswer.objects.filter(**where).count()

        return total_q_num, right_q_num

    def _get_user_answered_q_num_by_round(
            self,
            check_rounds,
            question_type: int,
            difficulty: int
    ):
        where = dict(
            is_deleted=False,
            subject_id=self.subject_id,
            core_course_code=self.core_course_code,
            user_id=self.user_id,
        )
        if question_type != -1:
            where['question__question_type'] = question_type
        if difficulty != -1:
            where['question__difficulty'] = difficulty
        if check_rounds:
            answer_ids = list(STUserPaperAnswer.objects.filter(
                is_deleted=False,
                paper__subject_id=self.subject_id,
                paper__core_course_code=self.core_course_code,
                user_id=self.user_id,
                is_finished=True,
                cycle_num=self.cycle_num,
            ).order_by('-id').values_list('pk', flat=True))[:check_rounds]
            where['answer_id__in'] = answer_ids

        return STUserPaperQuestionAnswer.objects.filter(**where).count()

    def _check_acc(self, val: float, acc: float | tuple | list) -> bool:
        if isinstance(acc, float):
            return acc <= val
        elif isinstance(acc, tuple) or isinstance(acc, list):
            return acc[0] <= val < acc[1]
