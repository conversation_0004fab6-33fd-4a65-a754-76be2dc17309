from app.constants.app import AppMode
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import PromptTemplate


class RedGeneratorWorkflow(BaseWorkflow):

    def _generate_response(self, prompt_template_code: str, query: str, stream=True):
        prompt_template = PromptTemplate.objects.filter(app_no=prompt_template_code).first()
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=self.application_generate_entity.inputs.copy(),
        )
        model_conf = get_llm_model_config(
            app_model_conf=self.application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        result_or_generator = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=query,
            stream=stream
        )
        return self._handle_node_response(
            result=result_or_generator,
            stream=stream,
            tracing_info={'tracing_type': prompt_template_code, 'query': query},
        )

    def _generate_title(self, subject: str, viewpoint: str) -> str:
        query = f'''主题：{subject}\n\n观点：{viewpoint}'''
        node_result = self._generate_response('red_generator_title', query, stream=False)
        title = node_result.message.content
        return title

    def _generate_article(self, subject: str, viewpoint: str, title: str):
        query = f'''主题：{subject}\n\n观点：{viewpoint}\n\n标题：{title}'''
        self._generate_response('red_generator_article', query)

    def run(self):
        user_inputs = self.application_generate_entity.inputs.copy()
        title = self._generate_title(user_inputs["subject"], user_inputs["viewpoint"])
        self._generate_article(user_inputs["subject"], user_inputs["viewpoint"], title=title)
        self._publish_workflow_end_message()
