from app.services.shuati_app.constants import QuestionType
from app.services.shuati_app.dto import QuestionSelectStrategy, LearningStageRequired, ModiUpRequired, UpgradeRequired

modi_strategy = QuestionSelectStrategy(
    choice_question_num=10,
    subjective_question_num=0,
    total_knowledge_num=6,
    wrong_knowledge_percent=0,
    high_freq_knowledge_threshold=2,
    high_freq_knowledge_percent=1,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 2, 2: 5, 3: 2, 4: 1, 5: 0},
    },
)


basic_strategy = QuestionSelectStrategy(
    choice_question_num=6,
    subjective_question_num=0,
    total_knowledge_num=5,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=2,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 3, 2: 2, 3: 1, 4: 0, 5: 0},
    },
)

basic_improve_l_strategy = QuestionSelectStrategy(
    choice_question_num=8,
    subjective_question_num=0,
    total_knowledge_num=5,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=2,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 1, 2: 5, 3: 2, 4: 0, 5: 0},
    },
)

basic_improve_l_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=8,
    subjective_question_num=0,
    total_knowledge_num=5,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=2,
    high_freq_knowledge_percent=0.7,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 1, 2: 5, 3: 2, 4: 0, 5: 0},
    },
)

basic_improve_m_strategy = QuestionSelectStrategy(
    choice_question_num=8,
    subjective_question_num=0,
    total_knowledge_num=5,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 1, 2: 4, 3: 2, 4: 1, 5: 0},
    },
)

basic_improve_m_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=8,
    subjective_question_num=0,
    total_knowledge_num=5,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 1, 2: 4, 3: 2, 4: 1, 5: 0},
    },
)

basic_improve_h_strategy = QuestionSelectStrategy(
    choice_question_num=4,
    subjective_question_num=2,
    total_knowledge_num=5,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 3, 3: 1, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 1, 4: 0, 5: 0},
    },
)

basic_improve_h_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=4,
    subjective_question_num=2,
    total_knowledge_num=5,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 3, 3: 1, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 1, 3: 1, 4: 0, 5: 0},
    },
)

core_improve_strategy = QuestionSelectStrategy(
    choice_question_num=3,
    subjective_question_num=3,
    total_knowledge_num=5,
    wrong_knowledge_percent=0.4,
    high_freq_knowledge_threshold=1,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 2, 3: 1, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 0, 3: 2, 4: 1, 5: 0},
    },
)

core_improve_strengthen_strategy = QuestionSelectStrategy(
    choice_question_num=3,
    subjective_question_num=3,
    total_knowledge_num=5,
    wrong_knowledge_percent=1,
    high_freq_knowledge_threshold=1,
    question_type_distribution={
        QuestionType.SINGLE.value: {1: 0, 2: 2, 3: 1, 4: 0, 5: 0},
        QuestionType.SUBJECTIVE.value: {1: 0, 2: 0, 3: 2, 4: 1, 5: 0},
    },
)

modi_up_required = ModiUpRequired(**dict(
    rounds_required=2,
    total_score_ratio=0.6,
    question_required=[
        {"question_type": 0, "difficulty": 2, "score_threshold": 6}
    ]
))

basic_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0.3},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 1, "num_rate": 0.44},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=3,
        question_required=[
            {"question_type": -1, "difficulty": -1, "accuracy": 0.6},
        ],
    )),
)

basic_improve_l_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.25},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.26, "accuracy": 0.35},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=3,
        question_required=[
            {"question_type": -1, "difficulty": -1, "accuracy": 0.6},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.26, "accuracy": (0.28, 0.35)},
        ],
    )),
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 2, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.26, "accuracy": (0, 0.28)},
        ],
    )),
)

basic_improve_m_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.50},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.43, "accuracy": 0.35},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=3,
        question_required=[
            {"question_type": -1, "difficulty": -1, "accuracy": 0.6},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.43, "accuracy": (0.28, 0.35)},
        ],
    )),
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 3, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.43, "accuracy": (0, 0.28)},
        ],
    )),
)

basic_improve_h_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.55},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.59, "accuracy": 0.50},
        ],
    )),
    up_again_required=UpgradeRequired(**dict(
        stage_rounds_required=4,
        question_required=[
            {"question_type": -1, "difficulty": -1, "accuracy": 0.6},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.59, "accuracy": (0.40, 0.49)},
        ],
    )),
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 2, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 2, "num_rate": 0.59, "accuracy": (0, 0.40)},
        ],
    )),
)

core_improve_required = LearningStageRequired(
    up_required=UpgradeRequired(**dict(
        knowledge_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.50},
        ],
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.67, "accuracy": 0.60},
        ],
    )),
    strengthen_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.67, "accuracy": (0.50, 0.60)},
        ],
    )),
    strengthen_round_required=UpgradeRequired(**dict(
        rounds_required=3,
        check_every_round=True,
        question_required=[
            {"question_type": 0, "difficulty": 3, "accuracy": 0.5},
        ],
    )),
    down_required=UpgradeRequired(**dict(
        question_required=[
            {"question_type": 0, "difficulty": 3, "num_rate": 0.67, "accuracy": (0, 0.50)},
        ],
    )),
)
