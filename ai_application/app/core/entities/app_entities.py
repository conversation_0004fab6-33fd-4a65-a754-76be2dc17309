from typing import Any

from pydantic import field_serializer, ConfigDict, Field

from app.constants.app import StreamEvent
from app.constants.common import FormParameterType
from app.core.entities.provider_entities import ProviderModelBundle
from app.core.model_runtime.entities.provider_entities import AIModelEntity
from app.errors import ParameterError
from app.models import InvokeFrom
from app.utils import validate_float, validate_int
from django_ext.base_dto_model import MyBaseModel


class AppParameterRule(MyBaseModel):
    code: str
    label: str
    type: FormParameterType
    desc: str = ''
    placeholder: str = ''
    required: bool = False
    max_length: int | None = None
    min: int | float | None = None
    max: int | float | None = None
    step: int | float | None = None
    options: list[str] | None = None
    default: Any | None = None

    @field_serializer('type')
    @classmethod
    def serialize_type(cls, v):
        return str(v.value)

    @classmethod
    def validate_field(cls, input_value: str, rule: 'AppParameterRule') -> Any:
        if rule.type in [FormParameterType.FLOAT, FormParameterType.INT]:
            return cls._validate_number(input_value, rule)
        elif rule.type == FormParameterType.BOOLEAN:
            return bool(input_value)
        elif rule.type in [FormParameterType.TEXT, FormParameterType.TEXT_MULTIROW]:
            validated_value = str(input_value)
            if rule.max_length is not None and len(validated_value) > rule.max_length:
                raise ParameterError(detail_err=f'{rule.code} max length {rule.max_length}')
            return validated_value

    @classmethod
    def _validate_number(cls, input_value: str, rule: 'AppParameterRule'):
        if rule.type == FormParameterType.FLOAT:
            validated_value = validate_float(input_value)
        else:
            validated_value = validate_int(input_value)

        if validated_value is None:
            raise ParameterError(detail_err=f'{rule.code} must be float')

        if rule.max is not None and validated_value > rule.max:
            raise ParameterError(detail_err=f'{rule.code} max value {rule.max}')

        if rule.min is not None and validated_value < rule.min:
            raise ParameterError(detail_err=f'{rule.code} min value {rule.min}')

        return validated_value


class ModelConfigEntity(MyBaseModel):
    provider: str
    model: str
    model_schema: AIModelEntity
    provider_model_bundle: ProviderModelBundle
    model_params: dict

    # pydantic configs
    model_config = ConfigDict(protected_namespaces=())


class AppGenerateEntity(MyBaseModel):
    app_no: str
    app_type: str = ''
    app_model_id: int
    app_model_config_id: int
    model_conf: ModelConfigEntity
    pre_prompt: str
    query: str
    inputs: dict
    conversation_hash: str
    context_limit: int
    message_type: str = 'normal'
    file_objs: list = Field(default=[])
    stream: bool = True
    dataset_id: int | None = None
    document_nos: list = Field(default=[])
    is_override: bool = False
    invoke_from: str = InvokeFrom.api.value
    query_in_prompt: bool = False
    support_tools: list = []

    # pydantic configs
    model_config = ConfigDict(protected_namespaces=())


class ChatAppGenerateEntity(AppGenerateEntity):
    pass


class CompletionAppGenerateEntity(AppGenerateEntity):
    pass


class StreamResponse(MyBaseModel):
    event: StreamEvent
    created_at: int


class ErrorStreamResponse(StreamResponse):
    event: StreamEvent = StreamEvent.ERROR
    err: str


class TokenExceedResponse(ErrorStreamResponse):
    event: StreamEvent = StreamEvent.TOKEN_EXCEED
    err: str


class MessageThinkingStreamResponse(StreamResponse):
    event: StreamEvent = StreamEvent.THINKING
    answer: str = ''
    reasoning_content: str = ''


class MessageThinkingEndStreamResponse(StreamResponse):
    event: StreamEvent = StreamEvent.THINKING_END
    stop_by: str = 'normal'


class MessageStreamResponse(StreamResponse):
    event: StreamEvent = StreamEvent.MESSAGE
    answer: str
    reasoning_content: str = ''
    workflow_chunk: dict = Field(default={})


class MessageEndStreamResponse(StreamResponse):
    event: StreamEvent = StreamEvent.MESSAGE_END
    metadata: dict = Field(default={})


class ChatbotAppBlockingResponse(MyBaseModel):
    """
    ChatbotAppBlockingResponse entity
    """

    class Data(MyBaseModel):
        """
        Data entity
        """
        message_id: str
        conversation_id: str
        answer: str
        usage: dict = {}
        is_success: bool = True
        is_sensitive: bool = False

    data: Data


class ChatbotAppStreamResponse(MyBaseModel):
    """
    ChatbotAppStreamResponse entity
    """
    conversation_id: str
    message_id: str
    stream_response: StreamResponse
