--
-- <PERSON>reate model KnowledgeLibrary
--
CREATE TABLE `ai_data_knowledge_library`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool         NOT NULL,
    `subject`          varchar(100) NOT NULL,
    `main_subject`     varchar(100) NOT NULL,
    `nature`           varchar(32)  NOT NULL,
    `name`             varchar(100) NOT NULL,
    `desc`             longtext NULL,
    `old_knowledge_id` varchar(32)  NOT NULL
);
--
-- Add field is_extract_knowledge to coursesectionknowledge
--
ALTER TABLE `ai_data_course_section_knowledge`
    ADD COLUMN `is_extract_knowledge` bool DEFAULT b'0' NOT NULL;
--
-- Add field is_extract_knowledge to questionknowledge
--
ALTER TABLE `ai_data_question_knowledge`
    ADD COLUMN `is_extract_knowledge` bool DEFAULT b'0' NOT NULL;
--
-- <PERSON>reate model QuestionKnowledgeMap
--
CREATE TABLE `ai_data_question_knowledge_map`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool         NOT NULL,
    `question_id`   varchar(100) NOT NULL,
    `knowledge_id`  bigint       NOT NULL
);
CREATE INDEX `ai_data_knowledge_library_add_time_8198d291` ON `ai_data_knowledge_library` (`add_time`);
CREATE INDEX `ai_data_question_knowledge_map_add_time_f75a4deb` ON `ai_data_question_knowledge_map` (`add_time`);
CREATE INDEX `ai_data_question_knowledge_map_knowledge_id_01853399` ON `ai_data_question_knowledge_map` (`knowledge_id`);