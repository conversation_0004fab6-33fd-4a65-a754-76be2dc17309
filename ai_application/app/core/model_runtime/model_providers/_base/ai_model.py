import os
from abc import ABC

from app.core.model_runtime.entities.provider_entities import AIModelEntity, ModelType
from app.core.utils.yaml_utils import load_yaml_file


class AIModel(ABC):
    model_type: ModelType
    model_schemas: list[AIModelEntity] | None = None
    started_at: float = 0

    def predefined_models(self) -> list[AIModelEntity]:
        if self.model_schemas is not None:
            return self.model_schemas

        model_schemas = []
        model_type = self.__class__.__module__.split('.')[-1]

        # get provider name
        provider_name = self.__class__.__module__.split('.')[-3]

        # get the path of current classes
        current_path = os.path.abspath(__file__)
        # get parent path of the current path
        provider_model_type_path = os.path.join(os.path.dirname(os.path.dirname(current_path)), provider_name, model_type)

        # get all yaml files path under provider_model_type_path that do not start with __
        model_schema_yaml_paths = [
            os.path.join(provider_model_type_path, model_schema_yaml)
            for model_schema_yaml in os.listdir(provider_model_type_path)
            if not model_schema_yaml.startswith('__')
               and not model_schema_yaml.startswith('_')
               and os.path.isfile(os.path.join(provider_model_type_path, model_schema_yaml))
               and model_schema_yaml.endswith('.yaml')
        ]

        for model_schema_yaml_path in model_schema_yaml_paths:
            # read yaml data from yaml file
            yaml_data = load_yaml_file(model_schema_yaml_path, ignore_error=True)

            new_parameter_rules = []
            for parameter_rule in yaml_data.get('parameter_rules', []):
                if 'label' not in parameter_rule:
                    parameter_rule['label'] = parameter_rule['name']
                new_parameter_rules.append(parameter_rule)

            yaml_data['parameter_rules'] = new_parameter_rules

            try:
                # yaml_data to entity
                model_schema = AIModelEntity(**yaml_data)
            except Exception as e:
                model_schema_yaml_file_name = os.path.basename(model_schema_yaml_path).rstrip(".yaml")
                raise Exception(f'Invalid model schema for {provider_name}.{model_type}.{model_schema_yaml_file_name}:'
                                f' {str(e)}')

            # cache model schema
            model_schemas.append(model_schema)

        # cache model schemas
        self.model_schemas = model_schemas

        return model_schemas

    def get_model_schema(self, model: str) -> AIModelEntity | None:
        """
        Get model schema by model name and credentials
        :param model: model name
        :return: model schema
        """
        # get predefined models (predefined_models)
        models = self.predefined_models()

        model_map = {model.model: model for model in models}
        if model in model_map:
            return model_map[model]

        return None
