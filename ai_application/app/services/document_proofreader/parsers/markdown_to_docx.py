import os
import tempfile
from pathlib import Path
from io import BytesIO
import shutil
import re
import logging
from bs4 import BeautifulSoup

try:
    import pypandoc
except ImportError:
    pypandoc = None

logger = logging.getLogger(__name__)


def convert_html_tables_to_markdown(markdown_content):
    """
    将HTML表格转换为标准Markdown表格，支持更复杂的表格结构
    """
    # 查找HTML表格
    table_pattern = r'<table[^>]*?>([\s\S]*?)<\/table>'
    
    def replace_table(match):
        html_table = match.group(0)
        try:
            # 使用BeautifulSoup解析HTML表格
            soup = BeautifulSoup(html_table, 'html.parser')
            table = soup.find('table')
            
            if not table:
                return html_table
                
            rows = table.find_all('tr')
            if not rows:
                return html_table
                
            # 构建Markdown表格
            md_table = []
            max_cols = 0
            
            # 首先计算最大列数
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if cells:
                    max_cols = max(max_cols, len(cells))
            
            if max_cols == 0:
                return html_table
            
            # 处理每一行
            for i, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if not cells:
                    continue
                
                # 构建表格行，确保所有行都有相同的列数
                cell_texts = []
                for j in range(max_cols):
                    if j < len(cells):
                        # 清理单元格内容，移除多余的空白和换行
                        cell_text = cells[j].get_text().strip()
                        cell_text = re.sub(r'\s+', ' ', cell_text)  # 将多个空白字符替换为单个空格
                        cell_texts.append(cell_text)
                    else:
                        cell_texts.append('')  # 空单元格
                
                # 构建Markdown表格行
                md_row = '| ' + ' | '.join(cell_texts) + ' |'
                md_table.append(md_row)
                
                # 第一行后添加分隔行
                if i == 0:
                    separator_row = '| ' + ' | '.join(['---' for _ in range(max_cols)]) + ' |'
                    md_table.append(separator_row)
            
            # 在表格前后添加空行，确保正确解析
            result = '\n\n' + '\n'.join(md_table) + '\n\n'
            return result
            
        except Exception as e:
            logger.error(f"表格转换错误: {e}")
            # 如果转换失败，尝试简单的文本提取
            try:
                soup = BeautifulSoup(html_table, 'html.parser')
                return soup.get_text()
            except:
                return html_table
    
    # 替换所有HTML表格
    return re.sub(table_pattern, replace_table, markdown_content, flags=re.DOTALL)


def _check_pypandoc_available():
    """检查pypandoc是否可用"""
    if pypandoc is None:
        return False
    
    try:
        # 尝试获取版本信息来验证pypandoc是否正常工作
        pypandoc.get_pandoc_version()
        return True
    except Exception as e:
        logger.error(f"pypandoc不可用: {e}")
        return False


def ensure_pandoc_installed():
    """确保pandoc已安装，如果没有则尝试安装"""
    if not _check_pypandoc_available():
        if pypandoc is None:
            raise RuntimeError(
                "pypandoc 未安装。请运行: pip install pypandoc\n"
                "注意：pypandoc还需要pandoc系统工具。如果没有安装，pypandoc会自动下载。"
            )
        
        try:
            # 尝试下载pandoc（如果系统没有安装的话）
            logger.info("尝试下载pandoc...")
            pypandoc.download_pandoc()
            logger.info("pandoc下载完成")
        except Exception as e:
            raise RuntimeError(f"无法安装pandoc: {e}")


def convert_markdown_to_docx(markdown_content, output_filename=None):
    """
    使用 pypandoc 将 markdown 内容转换为 Word 文档
    
    Args:
        markdown_content (str): markdown文本内容
        output_filename (str): 输出文件名（可选）
    
    Returns:
        BytesIO: Word文档的字节流
    """
    # 检查 pypandoc 是否可用
    ensure_pandoc_installed()
    
    # 预处理：将HTML表格转换为Markdown表格
    try:
        processed_content = convert_html_tables_to_markdown(markdown_content)
        logger.info(f"表格预处理完成，内容长度: {len(processed_content)}")
    except ImportError:
        logger.warning("BeautifulSoup未安装，无法预处理HTML表格")
        processed_content = markdown_content
    except Exception as e:
        logger.error(f"表格预处理失败: {e}")
        processed_content = markdown_content
    
    try:
        # 优化的转换参数，特别针对表格处理
        extra_args = [
            # === 输入输出格式 - 增强表格支持 ===
            '--from=markdown+pipe_tables+grid_tables+multiline_tables+simple_tables+fenced_code_blocks+backtick_code_blocks+table_captions',
            '--to=docx+native_numbering',
            '--standalone',
            
            # === 表格专用设置 ===
            '--columns=120',  # 设置列宽，有助于表格格式化
            '--wrap=preserve',  # 保持原有的文本换行
            
            # === 字体设置 ===
            '--variable=mainfont:Microsoft YaHei',  # 主字体使用微软雅黑
            '--variable=CJKmainfont:Microsoft YaHei',  # 中文字体
            '--variable=monofont:Consolas',  # 等宽字体（代码）
            '--variable=fontsize:12pt',  # 基础字体大小
            
            # === 页面布局 ===
            '--variable=geometry:margin=2.5cm',  # 页边距
            '--variable=geometry:a4paper',  # A4纸张
            '--variable=linestretch:1.5',  # 行间距1.5倍
            '--variable=indent:true',  # 段落首行缩进
            '--variable=parskip:6pt',  # 段落间距
            
            # === 标题样式 ===
            '--variable=title-color:#2c3e50',  # 标题颜色（深蓝灰）
            '--variable=header-color:#34495e',  # 页眉颜色
            
            # === 链接和颜色 ===
            '--variable=colorlinks:true',
            '--variable=linkcolor:#3498db',  # 链接颜色（蓝色）
            '--variable=urlcolor:#3498db',   # URL颜色
            '--variable=citecolor:#e74c3c',  # 引用颜色（红色）
            
            # === 表格样式优化 ===
            '--variable=tables:true',
            '--variable=table-use-row-colors:true',  # 表格行交替颜色
            '--variable=table-width:1.0',  # 表格宽度
            
            # === 代码高亮 ===
            '--highlight-style=pygments',  # 使用pygments风格的代码高亮
            
            # === 其他格式 ===
            '--variable=block-headings:true',  # 块级标题
            '--variable=section-divs:true',   # 章节分割
            '--variable=html-q-tags:true',    # HTML引用标签
            
            # === 文档元数据 ===
            '--variable=lang:zh-CN',  # 语言设置
            '--variable=documentclass:article',  # 文档类型
        ]
        
        # 如果指定了输出文件名，直接输出到文件
        if output_filename:
            pypandoc.convert_text(
                processed_content,
                'docx',
                format='md',
                outputfile=output_filename,
                extra_args=extra_args
            )
            
            # 读取生成的文件并返回字节流
            with open(output_filename, 'rb') as f:
                docx_content = f.read()
            
            docx_buffer = BytesIO(docx_content)
            docx_buffer.seek(0)
            return docx_buffer
        else:
            # 转换为临时文件然后读取
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_filename = temp_file.name
            
            try:
                pypandoc.convert_text(
                    processed_content,
                    'docx',
                    format='md',
                    outputfile=temp_filename,
                    extra_args=extra_args
                )
                
                # 读取生成的文件
                with open(temp_filename, 'rb') as f:
                    docx_content = f.read()
                
                docx_buffer = BytesIO(docx_content)
                docx_buffer.seek(0)
                return docx_buffer
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_filename)
                except OSError:
                    pass
            
    except Exception as e:
        error_msg = f"pypandoc 转换失败: {str(e)}"
        logger.error(error_msg)
        raise RuntimeError(error_msg)


def get_pandoc_version():
    """获取 pandoc 版本信息"""
    try:
        if not _check_pypandoc_available():
            return "pypandoc 不可用"
        
        version = pypandoc.get_pandoc_version()
        return f"pandoc {version}"
    except Exception as e:
        return f"无法获取版本信息: {e}"


def convert_markdown_to_docx_with_template(markdown_content, template_path=None, output_filename=None):
    """
    使用自定义模板将 markdown 转换为 Word 文档
    
    Args:
        markdown_content (str): markdown文本内容
        template_path (str): Word模板文件路径（可选）
        output_filename (str): 输出文件名（可选）
    
    Returns:
        BytesIO: Word文档的字节流
    """
    ensure_pandoc_installed()
    
    # 预处理内容
    try:
        processed_content = convert_html_tables_to_markdown(markdown_content)
    except ImportError:
        logger.warning("BeautifulSoup未安装，无法预处理HTML表格")
        processed_content = markdown_content
    
    try:
        # 基本转换参数，增强表格支持
        extra_args = [
            '--from=markdown+pipe_tables+grid_tables+multiline_tables+simple_tables+fenced_code_blocks+backtick_code_blocks',
            '--to=docx+native_numbering',
            '--standalone',
            '--highlight-style=pygments',
            '--columns=120',
            '--wrap=preserve',
        ]
        
        # 如果提供了模板，添加模板参数
        if template_path and os.path.exists(template_path):
            extra_args.append(f'--reference-doc={template_path}')
            logger.info(f"使用模板: {template_path}")
        
        # 转换文档
        if output_filename:
            pypandoc.convert_text(
                processed_content,
                'docx',
                format='md',
                outputfile=output_filename,
                extra_args=extra_args
            )
            
            with open(output_filename, 'rb') as f:
                docx_content = f.read()
            
            docx_buffer = BytesIO(docx_content)
            docx_buffer.seek(0)
            return docx_buffer
        else:
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_filename = temp_file.name
            
            try:
                pypandoc.convert_text(
                    processed_content,
                    'docx',
                    format='md',
                    outputfile=temp_filename,
                    extra_args=extra_args
                )
                
                with open(temp_filename, 'rb') as f:
                    docx_content = f.read()
                
                docx_buffer = BytesIO(docx_content)
                docx_buffer.seek(0)
                return docx_buffer
            finally:
                try:
                    os.unlink(temp_filename)
                except OSError:
                    pass
            
    except Exception as e:
        error_msg = f"模板转换失败: {str(e)}"
        logger.error(error_msg)
        raise RuntimeError(error_msg)


class MarkdownToDocxConverter:
    """
    Markdown到Word文档转换器类
    """
    
    def __init__(self):
        self.docx_buffer = None
        ensure_pandoc_installed()
    
    def convert(self, markdown_text):
        """转换markdown文本为docx"""
        self.docx_buffer = convert_markdown_to_docx(markdown_text)
        return self.docx_buffer
    
    def save_to_bytes(self):
        """返回docx文档的字节流"""
        if self.docx_buffer is None:
            raise ValueError("请先调用convert()方法")
        
        self.docx_buffer.seek(0)
        return self.docx_buffer.getvalue()
    
    def save_to_file(self, filename):
        """保存docx文档到文件"""
        if self.docx_buffer is None:
            raise ValueError("请先调用convert()方法")
        
        self.docx_buffer.seek(0)
        with open(filename, 'wb') as f:
            f.write(self.docx_buffer.getvalue())


# 为了向后兼容，保留一些原有的函数名
def markdown_to_docx(markdown_content, output_filename=None):
    """向后兼容的函数名"""
    return convert_markdown_to_docx(markdown_content, output_filename)


def test_conversion():
    """测试转换功能，包括表格测试"""
    test_markdown = """
# 测试文档

这是一个测试文档，用于验证pypandoc转换功能。

## 功能特性

- 支持中文字体
- 美观的格式
- 表格支持

| 项目 | 描述 | 状态 |
|------|------|------|
| pypandoc | Python包装器 | ✅ 已安装 |
| pandoc | 文档转换工具 | ✅ 可用 |
| 表格转换 | HTML到Word表格 | 🧪 测试中 |

## 复杂表格测试

| 学科分类 | 难度等级 | 推荐指数 | 备注说明 |
|----------|----------|----------|----------|
| 数学 | ⭐⭐⭐⭐⭐ | 必学 | 基础学科 |
| 英语 | ⭐⭐⭐ | 重要 | 国际交流 |
| 编程 | ⭐⭐⭐⭐ | 实用 | 就业技能 |

## 代码示例

```python
def hello():
    print("Hello, World!")
    return "成功"
```

这就是测试内容。

<table border="1" style="border-collapse: collapse;">
<tr>
<td style="text-align:center">HTML表格测试</td>
<td style="text-align:center">转换效果</td>
</tr>
<tr>
<td style="text-align:left">原始HTML</td>
<td style="text-align:left">应转为Word表格</td>
</tr>
</table>

测试完成！
"""
    
    try:
        logger.info("开始测试转换...")
        result = convert_markdown_to_docx(test_markdown, output_filename="app/services/document_proofreader/test-3.docx")
        logger.info(f"转换成功，生成了 {len(result.getvalue())} 字节的文档")
        return True
    except Exception as e:
        logger.error(f"转换测试失败: {e}")
        return False


if __name__ == "__main__":
    # 运行测试
    test_conversion() 