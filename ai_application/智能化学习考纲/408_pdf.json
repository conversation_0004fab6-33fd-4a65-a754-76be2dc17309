{"大纲名称": "2025年计算机学科专业基础考试大纲", "考试科目": {"数据结构": {"考察目标": "1.掌握数据结构的基本概念、基本原理和基本方法。\n2.掌握数据的逻辑结构、存储结构及基本操作的实现，能够对算法进行基本的时间复杂度与\n空间复杂度的分析。\n3.能够运用数据结构基本原理和方法进行问题的分析与求解，具备采用C或C++ 语言设计\n与实现算法的能力。", "考察内容": "一、基本概念\n一）数据结构的基本概念\n二）算法的基本概念\n二、线性表\n（一）线性表的基本概念\n（二）线性表的实现\n1.顺序存储\n2.链式存储\n（三）线性表的应用\n三、栈、队列和数组\n（一）栈和队列的基本概念\n（二）栈和队列的顺序存储结构\n（三）栈和队列的链式存储结构\n（四）多维数组的存储\n（五）特殊矩阵的压缩存储\n（六）栈、队列和数组的应用\n四、树与二叉树\n【一）树的基本概念\n（二）二叉树\n1.二叉树的定义及其主要特征\n2.二叉树的顺序存储结构和链式存储结构\n3.二叉树的遍历\n4.线索二叉树的基本概念和构造\n［三）树、森林\n1.树的存储结构\n2.森林与二叉树的转换\n3.树和森林的遍历\n【四｝树与二叉树的应用\n1.哈夫曼（Huffman）树和哈夫曼绵码\n2.并查集及其应用\n3.堆及其应用\n五、图\n（一］图的基本概念\n（二）图的存储及基本操作\n1. 邻接矩阵\n2.邻接表\n3.邻接多重表、十字链表\n（三）图的游历\n1.深度优先按索\n2.广度优先搜究\n（四）图的基本应用\n1.最小（代价）生成树\n2.最短路径\n3. 叛扑接序\n4.关键路径\n六、查找\n（一）查找的基本概念\n（二）顺序查找法\n（三｝分块查找法\n（四｝折半查找法\n（五）树型查找\n1.二叉树搜素树\n2.平衡二叉树\n3.红黑树\n（六）B 树及其基本操作、B+树的基本摄念\n（七］散列（Hash） 表\n（八）字符串模式匹配\n（九］查找算法的分析及应用\n七、接序\n〔一）排序的基本概念\n（二］直接插人排序\n（三｝折半插人排序\n〔四）起泡接序（bubble sort）\n（五｝简单选择排序\n（六）希尔排序［shell sort）\n〔七）快速排序\n（八］堆排序\n（九）二路归并排序（merge sort）\n（十）基数接序\n【十一）外部摔序\n【十二）摔序算法的分析和应用"}, "计算机组成原理": {"考察目标": "1.理解单处理器计算机系统中主要部件的工作原理、组成结构以及相互连接方式。\n2.掌握指令集体系结构的基本知识和基本实现方法，对计算机硬件相关问题进行分析，并能\n够对相关部件进行设计。\n3.理解计算机系统的螫机概念，能够综合运用计算机组成的基本原理和基本方法，对高级线\n程语言（C语言程序中的相关问题进行分析，具备软硬件协同分析和设计能力。", "考察内容": "一、计算机系统概述\n（一）计算机系统层次结构\n1.计算机系统的基本组成\n2.计算机硬件的基本组成\n3.计算机软件和硬件的关系\n4.计算机系统的工作原理\n“存储程序”工作方式，高级语言程序与机器语言程序之间的转换，程序和指令的执行过程\n|二）计算机性能指标\n吞吐量、响应时间：CPU 时钟周期、主频、CPI、CPU 执行时间；MIPS、MFLOPS\nGFLOPS -\nTFLOPS. PFLOPS. EFLOPS. ZFLOPS.\n二、数据的表示和运算\n（一｝数制与编码\n1.进位计数制及其数据之间的相互转换\n2.定点数的编码表示\n（二）运算方法和运算电路\n1.基本运算部件\n加法器，算术逻辑部件（ALU）\n2.加/减运算\n补码加/减运算器，标志位的生成。\n3.乘/除运算\n乘/除法运算的基本原理，乘法电路和除法电路的基本丝\n（三）整数的表示和运算\n1.无符号整数的表示和运算\n2.带符号整数的表示和运算\n（四）浮点数的表示和运算\n1.浮点数的表示\nIEEE 754 标准\n2浮点数的加/减运算\n三、存储器层次结构\n（一）存储器的分类\n（二）层次化存储器的基本结构\n（三）半导体随机存取存储器\n1.SRAM 存储器\n2.DRAM 存储器\n3.Flash 存储器\n（四）主存储器\n1.DRAM 芯片和内存条\n2.多模块存储器\n3.主存和CPU 之间的连接\n（五）外部存储器\n1.磁盘存储器\n2.固态硬盘 （SSD）\n（六）高速缓冲存储器（Cache）\n1.Cache 的基本原理\n2.Cach 和主存之间的映射方式\n3.Cache 中主存块的替换算法\n4.Cache 写策略\n（七）虚拟存储器\n1.虛拟存储器的基本概念\n2.页式虚拟存储器\n基本原理，页表，地址转换，TLB（块表）\n3.段式虚拟存储器\n4.段页式虛拟存储器\n四、指令系统\n（一指令系统的基本概念\n（二）指令格式\n（三）寻址方式\n（四）数据的对齐和大/小端存放方式\n（五）CISC 和RISC 的基本概念\n（六）高级语言程序与机器级代码之间的对应\n1.编译器，汇编器和链路器的基本概念\n2.选择结构语句的机器级表示\n3.循环结构语句的机器级表示\n4.过程（函数）调用对应的机器级表示\n五、中央处理器（CPU）\n（一）CPU 的功能和基本结构\n（二）指令执行过程\n（三）数据通路的功能和基本结构\n（四）控制器的功能和工作原理\n（五）异常和中断机制\n1.异常和中断的基本概念\n2.异常和中断的分类\n3.导常和中断的檢测与响应\n【六）指令流水线\n1.指令流水线的基本概念\n2. 指令流水线的基本实现\n3.结构冒险、数据冒险和控制冒险的处理\n4.超标量和动态流水线的基本概念\n【七）多处理器基本概念\n1.SISD、SIMD、MIMD、向量处理器的基本概念\n2.硬件多线程的基本概念\n3.多核处理器（multi-core）的基本概念\n4.共享内存多处理器（SMP）\n的基本\n六、总线和输人/输出系统\n（一）总线\n1.总线的基本概念\n2.总线的组成及性能指标\n3.总线事务和定时\n（二川O 接 口（O 控制器）\n1.1/O 接口的功能和基本结构\n2.1/0 端口及其编址\n（三）O 方式\n1.程序查询方式\n2.程序中断方式\n3.DMA 方式\nDMA 控制器的组成，DMA 传送过程\n中断的基本概念；中断响应过程；中断处理过程；多重中断和中断屏蔽的概念。"}, "操作系统": {"考察目标": "1.掌握操作系统的基本概念、方法和原理，了解操作系统的结构、功能和服务，理解操作系\n统所采用的的策略、算法和机制。\n2.能够从计算机系统的角度理解并描述应用程序、操作系统内核和计算机硬件协作完成任务\n的过程。\n3. 能够运用操作系统原理，分析并解决计算机系统中与操作系统相关的问题。", "考察内容": "一、操作系统概述\n（一）操作系统的基本概念\n（二）操作系统的发展历程\n（三）程序运行环境\n1.CPU 运行模式\n内核模式、用户模式\n2.中断和异常的处理\n3.系统调用\n4.程序的链接与装入\n5.程序运行时内存映像与地址空间\n（四）操作系统结构\n分层，模块化，宏内核，微内核，外核\n（五）操作系统引导\n（六）虛拟机\n二、进程管理\n（一）进程与线程\n1.进程与线程的基本概念\n2.进程/线程的状态与转换\n3.线程的实现\n内核支持的线程，线程库支持的线程\n4.进程与线程的组织与控制\n5.进程间通信\n共享内存，消息传递，管道，信号。\n（四｝操作系统结构\n分层，模块化，宏内核，微内核，外核\n（五）操作系统引导\n（六］虛拟机\n二、进程管理\n（一）进程与线程\n1.进程与线程的基本概念\n2.进程/线程的状态与转换\n3.线程的实现\n内核支持的线程，线程库支持的线程\n4.进程与线程的组织与控制\n5.进程间通信\n共享内存，消息传递，管道，信号。\n（二）CPU 调度与上下文切换\n1.调度的基本概念\n2.调度的目标\n3.调度的实现\n调度器/调度程序（scheduler），调度的时机与调度方式（抢占式/非抢占式），闲逛进程，内\n核级线程与用户级线程调度\n4.CPU调度算法\n5.多处理机调度\n6.上下文及其切换机制\n（三）同步与互斥\n1.同步于互斥的基本概念\n2.基本的实现方法\n软件方法；硬件方法。\n3.锁\n4.信号量\n5.条件变量\n6.经典同步问题\n生产者-消费者问题；读者-写者问题；哲学家进餐问题。\n（四）死锁\n1.死锁的基本概念\n2.死锁预防\n3.死锁避免\n4.死锁检测和解除\n三、内存管理\n（一）内存管理基础\n1.内存管理的基本概念\n逻辑地址空间与物理地址空间，地址变换，内存共享，内存保护，内存分配与回收\n2.连续分配管理方式\n3.页式管理\n4.段式管理\n5.段页式管理\n（二）虚拟存储管理\n1.虚拟内存基本概念\n2.请求页式管理\n3.页框分配与回收\n4.页置换算法\n5.内存映射文件（Memory-Mapped Files）\n6.虚拟存储器性能的影响因素及改进方式\n四、文件管理\n（一）文件\n1.文件的基本概念\n2.文件元数据和索引节点（inode）\n3.文件的操作\n建立，删除，打开，关闭，读，写\n4.文件的保护\n5.文件的逻辑结构\n6.文件的物理结构\n（二）目录\n1.目录的基本概念\n2.树形目录\n3.目录的操作\n4.硬链接和软链接\n（三）文件系统\n1.文件系统的全局结构 （layout）\n文件系统在外存中的结构，文件系统在内存中的结构\n2.外存空闲空间管理办法\n3.虚拟文件系统\n4.文件系统挂载（mounting）\n五、输入输出（1/O）管理\n（一川/O 管理基础\n1.设备\n设备的基本概念，设备的分类，1/O 接口，1/O 端口\n2./0 控制方式\n龄询方式、中浙方式\nDMA 方式\n3.1/0 软件层次结构\n中断处理程序，驱动程序，设备独立软件，用户层1/0 软件\n4.输入/输出应用程序接口\n字符设备接口，块设备接口，网络设备接口，阻塞/非阻塞1/0\n（二）设备独立软件\n1.缓冲区管理\n2.设备分配与回收\n3.假脱机技术（SPOOLing）\n4.设备驱动程序接口\n（三）外存管理\n1.磁盘\n磁盘结构，格式化，分区，磁盘调度方法\n2.固态硬盘\n读写性能特性，磨损均衡"}, "计算机网络": {"考察目标": "1.掌握计算机网络的基本概念、基本原理和基本方法。\n2.学握典型计算机网络的结构、协议、应用以及典型网络设备的工作原理\n3.能够运用计算机网络的基本概念、基本原理和基本方法进行网络系统的分析、设计和应用。", "考察内容": "一、计算机网络概述\n（一）计算机网络基本概念\n1.计算机网络的定义、组成与功能\n2.计算机网络的分类\n3.计算机网络主要性能指标\n（二）计算机网络体系结构\n1.计算机网络分层结构\n2.计算机网络协议、接口、服务等概念\n3.ISO/OSI 参考模型和TCP/IP 模型\n二、物理层\n（I通信基础\n1.信道、信号、带宽、码元、波特、速率、信源与信宿等基本概念\n2.奈奎斯特定理与香农定理\n3.编码与调制\n4.电路交换、报文交换与分组交换\n5.数据报与虚电路\n（二）传输介质\n1.双绞线、同轴电缆、光纤与无线传输介质\n2.物理层接口的特性\n（三）物理层设备\n1.中继器\n2.集线器\n三、数据链路层\n一）数据链路层的功能\n（二组帧\n（三）差锴控制\n1.检错编码\n2.纠错编码\n（四）流量控制与可靠传输机制\n1.流量控制、可靠传输与滑动窗口机制\n2.停止-等待协议\n3.后退 N 帧协议（GBN）\n4.选择重传协议（SR）\n（五）个质访问控制\n1.信道划分\n频分多路复用、时分多路复用、波分多路复用、码分多路复用的概念和基本原理。\n2.随即访问\n3.轮询访问\n令牌传递协议\nALOHA 协议；CSMA 协议；CSMA/CD 协议；CSMA/CA 协议。\n（六）局域网\n1.局域网的基本概念与体系结构\n2.以太网与 IEEE 802.3\n3.IEEE802.11 无线局域网\n4.VLAN 基本概念与基本原理\n（七）广域网\n1.广域网的基本概念\n2.PPP协议\n【八）数据链路层设备\n以太网交换机及其工作原理\n四、网络层\n（一）网络层的功能\n1.异构网络互联\n2.路由与转发\n3.SDN 基本概念\n4.拥塞控制\n（二）路由算法\n1.静态路由与动态路由\n2.距离-向量路由算法\n3.链路状态路由算法\n4.层次路由\n（三）PV4\n1.IPv4 分组\n2.IPv4 地址与 NAT\n3.子网划分、路由聚集、子网掩码与 CIDR\n4.ARP 协议、DHCP 协议与ICMP 协议\n(R4)IPv6\n1.IPv6 的主要特点\n2.IPv6 地址\n（五）路由协议\n1.自治系统\n2.域内路由与域间路由\n3.RIP 路由协议\n4.OSPF 路由协议\n5.BGP 路由协议\n（（六JIP 组掃\n1.组攝的概念\n2.IP 组摇地址\n（七）移动IP\n1.移动 IP 的概念\n2.移动IP 通信过程\n【八）网络层设备\n1.路由器的组成和功能\n2.路由表与分组转发\n五、传输层\n（一）传输层提供的服务\n1.传输层的功能\n2.传输层寻址与端口\n3.无连接服务与面向连接服务\n（二NUDP 协议\n1.UDP数据报\n2.UDP校验\n（三）TCP协议\n1.TCP段\n2.TCP连接管理\n3.TCP可靠传输\n4.TCP流量控制\n5.TCP拥塞控制\n六、应用层\n（一）网络应用模型\n1.客户/服务器（C/S）模型\n2.对等（P2P）模型\n（二JDNS 系统\n1.层次域名空间\n2.域名服务器\n3.域名解析过程\n（三）FIP\n1.FTP 协议的工作原理\n2.控制连接与数据连接\n（四）电子邮件\n1.电子邮件系统的组成结构\n2.电子邮件格式与 MIME\n3.SMTP\n协议与 POP3 协议\n（五）WWW\n1.WWW 的概念与组成结构\n2.HTTP 协议"}}}