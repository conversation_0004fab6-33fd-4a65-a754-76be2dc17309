from django.db import models


class BaseModel(models.Model):
    add_time = models.DateTimeField('创建时间', auto_now_add=True, db_index=True)
    modified_time = models.DateTimeField('修改时间', auto_now=True)
    is_deleted = models.BooleanField('删除状态', default=False)

    class Meta:
        abstract = True
        ordering = ['-id']

    def soft_delete(self, is_save=True):
        self.is_deleted = True
        if is_save:
            self.save()
