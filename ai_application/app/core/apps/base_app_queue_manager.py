import queue
import time
from abc import abstractmethod
from typing import Generator, Any

from django.core.cache import cache

from app.core.entities.queue_entities import (
    AppQueueEvent, QueueErrorEvent, QueueStopEvent
)


class AppQueueManager:
    # wait for listen_timeout seconds to stop_words listen
    listen_timeout = 600

    def __init__(self, task_id):
        self._task_id = task_id

        q = queue.Queue()
        self._q = q

    def listen(self) -> Generator:
        start_time = time.time()

        while True:
            try:
                message = self._q.get(timeout=1)
                if message is None:
                    break

                yield message
            except queue.Empty:
                continue
            finally:
                elapsed_time = time.time() - start_time
                if elapsed_time >= self.listen_timeout:
                    self.publish(
                        QueueStopEvent(stopped_by=QueueStopEvent.StopBy.USER_MANUAL),
                    )

    def is_stopped(self) -> bool:
        stopped_cache_key = AppQueueManager._generate_stopped_cache_key(self._task_id)
        result = cache.get(stopped_cache_key)
        if result is not None:
            return True

        return False

    def stop_listen(self) -> None:
        self._q.put(None)

    def publish(self, event: AppQueueEvent) -> None:
        self._publish(event)

    def publish_error(
            self,
            e: Any = None,
            e_type: QueueErrorEvent.ErrorType = QueueErrorEvent.ErrorType.OTHER
    ) -> None:
        self._publish(QueueErrorEvent(error=e, error_type=e_type))

    @classmethod
    def set_stop_flag(cls, task_id: str) -> None:
        stopped_cache_key = cls._generate_stopped_cache_key(task_id)
        cache.set(stopped_cache_key, 1, timeout=600)

    @classmethod
    def check_is_stopped(cls, task_id: str) -> bool:
        stopped_cache_key = cls._generate_stopped_cache_key(task_id)
        result = cache.get(stopped_cache_key)
        if result is not None:
            return True
        return False

    @abstractmethod
    def _publish(self, event: AppQueueEvent) -> None:
        raise NotImplementedError

    @classmethod
    def _generate_stopped_cache_key(cls, task_id: str) -> str:
        return f"generate_task_stopped:{task_id}"
