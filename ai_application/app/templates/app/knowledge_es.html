<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>知识点搜索</title>
<script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/marked/13.0.2/marked.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
<style>
  body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
  }
  .search-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
  .search-box {
    width: 300px;
    padding: 10px;
    margin-right: 10px;
  }
  .search-button {
    padding: 10px 20px;
  }
  .container {
    display: flex;
    justify-content: space-between;
  }
  .results-container {
    width: 55%;
  }
  .result-list {
    list-style: none;
    padding: 0;
  }
  .result-item {
    margin-bottom: 20px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 10px;
  }
  .result-title {
    font-size: 18px;
    color: #333;
  }
  .result-definition {
    font-size: 16px;
    color: #444;
  }
  .result-summary {
    {#font-size: 16px;#}
    {#white-space: pre-line;#}
  }
  .deep-result-list {
    font-size: 16px;
    {#white-space: pre-line;#}
  }
  #searchDetails{
    white-space: pre-line;
  }
  #searchDetails p{
      border-bottom: 1px solid red;
      margin-bottom: 5px;
  }
  .search-path-container {
    width: 20%;
    padding-left: 20px;
  }
  .search-path-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
  }
  .search-keywords {
    margin-bottom: 10px;
  }
  .search-keyword {
    display: inline-block;
    margin-right: 5px;
    padding: 2px 5px;
    background-color: #f0f0f0;
    border-radius: 3px;
  }
  .prompt-button, .confirm_prompt {
    padding: 5px 10px;
  }
  .prompt_input {
    resize: vertical;
    height: 300px; /* 设置一个初始高度 */
    width: 600px;
  }
    /* 新增弹出层样式 */
  .promptManagementModal {
    display: none;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 20px;
    border: 1px solid #ccc;
    z-index: 1000;
  }
  /* 遮罩层样式 */
  #overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
  .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
    .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
</style>
</head>
<body>

<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <p>正在请求...</p>
    </div>
</div>

<div class="search-container">
  <input type="text" id="searchInput" class="search-box" placeholder="输入搜索内容">
  <button onclick="search()" class="search-button">搜索</button>
</div>

<p><a href="{% url 'knowledge_list' %}" target="_blank">查看知识点</a></p>

<div class="container">
  <div class="results-container">
    <div class="search-path-title">搜索结果：</div>
    <div class="result-list" id="resultList">
      <!-- 搜索结果将在这里显示 -->
    </div>
  </div>
</div>
<!-- 新增遮罩层 -->
<div id="overlay"></div>

<script>

function search() {
    const input = $('#searchInput').val();
    if (!input) {
        alert('请输入搜索内容');
        return;
    }

    const resultList = document.getElementById('resultList');
    resultList.innerHTML = ''; // 清空之前的搜索结果

    $('#loadingOverlay').css('display', 'flex');

    message_id = null;
    const url = "{% url 'knowledge_es_result' %}";
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: input,
        })
    })
    .then(response => {
          $('#loadingOverlay').css('display', 'none');
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json()
    })
    .then(data => {
        data.data.result.forEach(function(item) {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.innerHTML = `
              <div class="result-title">${item}</div>
            `;
            resultList.appendChild(resultItem);
        });
    })
    .catch(error => {
        $('#loadingOverlay').css('display', 'none');
        console.error('Error:', error);
    });
}

$(function () {
    $('#searchInput').on('keydown', function(e){
        // 检查按下的键是否是回车键
        if (e.which === 13) {
            e.preventDefault();
            search();
        }
    });
})
</script>

</body>
</html>
