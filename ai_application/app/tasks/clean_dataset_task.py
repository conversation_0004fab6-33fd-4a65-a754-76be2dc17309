import logging
import time

from celery import shared_task
from django.utils import timezone

from app.core.rag.index_processor.index_processor_factory import IndexProcessorFactory
from app.models import Dataset, DatasetDocument, DocumentSegment, DatasetProcessRule, DatasetQuery

logger = logging.getLogger(__name__)


@shared_task(queue='dataset')
def clean_dataset_task(dataset_no: str, index_type: str):
    logger.info('Start clean dataset when dataset deleted: {}'.format(dataset_no))

    dataset: Dataset = Dataset.objects.filter(
        is_deleted=False, dataset_no=dataset_no).first()
    # 资料库不存在，则不处理
    if not dataset:
        return

    index_processor = IndexProcessorFactory(index_type).init_index_processor()
    index_processor.clean(dataset, None)

    DatasetDocument.objects.filter(dataset=dataset).update(is_deleted=True, modified_time=timezone.now())
    DocumentSegment.objects.filter(dataset=dataset).update(is_deleted=True, modified_time=timezone.now())
    DatasetProcessRule.objects.filter(dataset=dataset).update(is_deleted=True, modified_time=timezone.now())
    DatasetQuery.objects.filter(dataset=dataset).update(is_deleted=True)
