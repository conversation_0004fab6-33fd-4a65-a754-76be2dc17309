import uuid
import json
import requests
import re
import ast
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.shortcuts import render
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from app.models import App, PromptTemplate
# 添加API视图用于获取专业数据
from app.models import UndergraduateCollegeInfo, GraduateCollegeInfo
from app.models.main_subject import SuperViseInitStudentStatus
from app.models.model import PersonalizedExamSyllabus


class NewStudentProfileDemoView(View):
    """新版学生基础画像Demo页面"""

    def get(self, request, *args, **kwargs):
        app = App.objects.filter(
            is_deleted=False,
            app_type='supervise_init_undergraduated_st'
        ).first()

        # 修改：添加默认提示词内容
        default_prompt = "这是默认的提示词模板内容..."
        prompt_content = default_prompt

        if app:
            prompt_template = PromptTemplate.objects.filter(app_no=app.app_no).first()
            if prompt_template and prompt_template.prompt_content:
                prompt_content = prompt_template.prompt_content

        # 获取所有本科院校信息并去重
        colleges = UndergraduateCollegeInfo.objects.all().order_by('name')
        unique_colleges = []
        seen_names = set()
        for college in colleges:
            if college.name not in seen_names:
                unique_colleges.append(college)
                seen_names.add(college.name)

        # 获取研究生院校信息并去重
        graduate_colleges = GraduateCollegeInfo.objects.all().order_by('name')
        unique_graduate_colleges = []
        seen_graduate_names = set()
        for college in graduate_colleges:
            if college.name not in seen_graduate_names:
                unique_graduate_colleges.append(college)
                seen_graduate_names.add(college.name)

        return render(request, 'app/new_student_profile_demo.html', {
            'prompt_template': prompt_content,  # 修改：直接传递 prompt_content
            'undergraduate_colleges': unique_colleges,
            'graduate_colleges': unique_graduate_colleges,
        })


class SavePromptTemplateView(View):
    """保存提示词模板视图"""

    def post(self, request, *args, **kwargs):
        try:
            new_prompt_content = request.POST.get('prompt_content')
            
            # 获取应用信息
            from app.models import App, PromptTemplate
            app = App.objects.filter(
                is_deleted=False,
                app_type='supervise_init_undergraduated_st'
            ).first()

            if not app:
                return JsonResponse({'status': 'error', 'message': '未找到对应的应用'})

            # 查找或创建提示词模板
            prompt_template = PromptTemplate.objects.filter(app_no=app.app_no).first()
            if prompt_template:
                prompt_template.prompt_content = new_prompt_content
                prompt_template.save()
            else:
                # 如果没有提示词模板，则创建一个新的
                PromptTemplate.objects.create(
                    app_no=app.app_no,
                    prompt_content=new_prompt_content
                )

            return JsonResponse({'status': 'success', 'message': '提示词保存成功'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'提示词保存失败: {str(e)}'})


# 添加API视图用于获取院校代码
class UndergraduateCollegeCodeView(View):
    """根据院校名称获取院校代码"""

    def get(self, request):
        school_name = request.GET.get('school_name')
        if not school_name:
            return JsonResponse({'status': 'error', 'message': '缺少院校名称参数'})

        try:
            college = UndergraduateCollegeInfo.objects.get(name=school_name)
            return JsonResponse({
                'status': 'success',
                'data': {
                    'undergraduate_code': college.undergraduate_code
                }
            })
        except UndergraduateCollegeInfo.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '未找到该院校信息'})




@method_decorator(csrf_exempt, name='dispatch')
class KaoyanLearnQueryView(View):
    """代理考研学习查询API"""
    
    def get(self, request):
        try:
            # 获取请求参数
            school_code = request.GET.get('school_code')
            if not school_code:
                return JsonResponse({
                    'status': 'error',
                    'message': '缺少school_code参数'
                })
            
            # 构建外部API URL
            external_api_url = 'https://yantucs-data.yantucs.com/internal_api/v1/college/kaoyan_learn_query/'
            
            # 发送POST请求到外部API
            response = requests.post(external_api_url, data={'school_code': school_code}, timeout=15)
            
            # 检查响应状态
            if response.status_code == 200:
                # 返回外部API的响应数据
                data = response.json()
                return JsonResponse(data)
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': f'外部API请求失败: {response.status_code}'
                })
                
        except requests.exceptions.RequestException as e:
            return JsonResponse({
                'status': 'error',
                'message': f'请求外部API时出错: {str(e)}'
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'处理请求时出错: {str(e)}'
            })
            
    def post(self, request):
        try:
            # 获取请求参数
            school_code = request.POST.get('school_code') or request.GET.get('school_code')
            if not school_code:
                return JsonResponse({
                    'status': 'error',
                    'message': '缺少school_code参数'
                })
            
            # 构建外部API URL
            external_api_url = 'https://yantucs-data.yantucs.com/internal_api/v1/college/kaoyan_learn_query/'
            
            # 发送POST请求到外部API
            response = requests.post(external_api_url, data={'school_code': school_code}, timeout=10)
            
            # 检查响应状态
            if response.status_code == 200:
                # 返回外部API的响应数据
                data = response.json()
                return JsonResponse(data)
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': f'外部API请求失败: {response.status_code}'
                })
                
        except requests.exceptions.RequestException as e:
            return JsonResponse({
                'status': 'error',
                'message': f'请求外部API时出错: {str(e)}'
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'处理请求时出错: {str(e)}'
            })


class PersonalSyllabusView(View):
    def get(self, request):
        try:
            task_id = request.GET.get('task_id')
            if not task_id:
                return JsonResponse({
                    'status': 'error',
                    'message': '缺少task_id参数'
                })

            # 从ai_student_supervise_init_status数据库查询记录
            student_status = SuperViseInitStudentStatus.objects.filter(task_id=task_id).first()
            if not student_status:
                return JsonResponse({
                    'status': 'error',
                    'message': '未找到对应的任务记录'
                })

            if not student_status.analysis:
                return JsonResponse({
                    'status': 'error',
                    'message': '分析内容为空'
                })

            # 使用正则表达式找到"各科目标分:"后的"专业课："分数
            analysis_text = student_status.analysis
            target_score_pattern = r'各科目标分:[\s\S]*?\*\*(?:专业课|\(408\)计算机学科专业基础):\*\*\s*(\d+)'
            match = re.search(target_score_pattern, analysis_text)
            
            if not match:
                return JsonResponse({
                    'status': 'error',
                    'message': '未找到专业课目标分数'
                })
            
            student_score = int(match.group(1))
            print(f"火箭🚀", student_score)
            
            # 从ai_personalized_exam_syllabus数据库查询对应记录
            syllabus_record = PersonalizedExamSyllabus.objects.filter(
                student_score=student_score,
                subject='408计算机'
            ).first()
            
            if not syllabus_record or not syllabus_record.exam_syllabus:
                return JsonResponse({
                    'status': 'error',
                    'message': '未找到对应的个性化考纲'
                })

            return JsonResponse({
                'status': 'success',
                'data': syllabus_record.exam_syllabus
            })
            
        except Exception as e:
            return JsonResponse({
                'status': 'error', 
                'message': f'获取考纲失败: {str(e)}'
            })


class SyllabusGuideIndexView(View):
    """学生个性考纲Demo页面"""
    
    def get(self, request, *args, **kwargs):
        # 从请求中获取用户ID和任务ID
        user_id = request.GET.get('user_id', '')
        task_id = request.GET.get('task_id', '')
        
        # 初始化初试分数
        initial_score = None
        
        # 获取该用户的考纲数据
        syllabus_data = []
        if task_id:
            # 通过task_id查询学生状态记录
            student_status = SuperViseInitStudentStatus.objects.filter(task_id=task_id).first()
            if student_status:
                user_id = student_status.user_id
                print(f"⚡️", user_id)
                # 从query字段中提取初试分数
                if student_status.query:
                    try:
                        # 尝试解析query字段为JSON
                        query_data = ast.literal_eval(student_status.query)
                        print(f"555")
                        print(f"", query_data)
                        # 从目标院校信息中提取预估目标分数
                        # 根据实际数据结构调整提取逻辑
                        if "目标院校信息" in query_data and "预估目标分数" in query_data["目标院校信息"]:
                            initial_score = query_data["目标院校信息"]["预估目标分数"]
                        print(f"初试分数: {initial_score}")
                    except (json.JSONDecodeError, KeyError, TypeError):
                        # 如果JSON解析失败，尝试其他方式
                        pass

                
                # 从analysis字段中提取各科目分数
                analysis_text = student_status.analysis
                if analysis_text:
                    # 提取政治分数
                    politics_pattern = r'各科目标分:[\s\S]*?\*\*政治:\*\*\s*(\d+)'
                    politics_match = re.search(politics_pattern, analysis_text)
                    if politics_match:
                        syllabus_data.append({
                            'subject': '政治',
                            'score': int(politics_match.group(1))
                        })
                    
                    # 提取英语分数
                    english_pattern = r'各科目标分:[\s\S]*?\*\*英语:\*\*\s*(\d+)'
                    english_match = re.search(english_pattern, analysis_text)
                    if english_match:
                        syllabus_data.append({
                            'subject': '英语',
                            'score': int(english_match.group(1))
                        })
                    
                    # 提取数学/专业课1分数
                    math_pattern = r'各科目标分:[\s\S]*?\*\*(?:数学|\(408\)计算机学科专业基础):\*\*\s*(\d+)'
                    math_match = re.search(math_pattern, analysis_text)
                    if math_match:
                        syllabus_data.append({
                            'subject': '数学/专业课1',
                            'score': int(math_match.group(1))
                        })
                    
                    # 提取专业课2分数
                    major_pattern = r'各科目标分:[\s\S]*?\*\*(?:专业课|\(408\)计算机学科专业基础):\*\*\s*(\d+)'
                    major_match = re.search(major_pattern, analysis_text)
                    if major_match:
                        syllabus_data.append({
                            'subject': '专业课2',
                            'score': int(major_match.group(1))
                        })
        
        # 如果没有从analysis提取到数据，则使用原来的查询方式
        if not syllabus_data and user_id:
            syllabus_records = PersonalizedExamSyllabus.objects.filter(student_score__isnull=False).order_by('-add_time')[:5]
            for record in syllabus_records:
                syllabus_data.append({
                    'subject': record.subject,
                    'score': record.student_score,
                })

        return render(request, 'app/syllabus_guide_index.html', {
            'user_id': user_id,
            'task_id': task_id,
            'initial_score': initial_score,
            'syllabus_data': syllabus_data
        })