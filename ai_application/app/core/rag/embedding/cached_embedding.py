import base64
import logging
from typing import cast
import numpy as np

from app.core.model_manager import ModelInstance
from app.core.model_runtime.entities.provider_entities import Model<PERSON>ropertyKey
from app.core.model_runtime.model_providers._base.text_embedding_model import TextEmbeddingModel
from app.core.rag.embedding.base_embedding import Embeddings
from app.libs.redis import redis_default_client
from app.models import Embedding
from app.utils import generate_text_hash

logger = logging.getLogger(__name__)


class CacheEmbedding(Embeddings):
    def __init__(self, model_instance: ModelInstance) -> None:
        self._model_instance = model_instance

    def embed_documents(self, texts: list[str]) -> list[list[float]]:
        """Embed search docs in batches of 10."""
        # use doc embedding cache or store if not exists
        text_embeddings: list = [None for _ in range(len(texts))]
        embedding_queue_indices = []
        for i, text in enumerate(texts):
            text_hash = generate_text_hash(text)
            embedding_obj: Embedding = Embedding.objects.filter(
                is_deleted=False,
                model_name=self._model_instance.model,
                provider_name=self._model_instance.provider,
                text_hash=text_hash,
            ).first()
            if embedding_obj:
                text_embeddings[i] = embedding_obj.get_embedding()
            else:
                embedding_queue_indices.append(i)
        if embedding_queue_indices:
            embedding_queue_texts = [texts[i] for i in embedding_queue_indices]
            embedding_queue_embeddings = []
            try:
                model_type_instance = cast(TextEmbeddingModel, self._model_instance.model_type_instance)
                model_schema = model_type_instance.get_model_schema(self._model_instance.model)

                max_chunks = model_schema.model_properties[ModelPropertyKey.MAX_CHUNKS] \
                    if model_schema and ModelPropertyKey.MAX_CHUNKS in model_schema.model_properties else 1
                for i in range(0, len(embedding_queue_texts), max_chunks):
                    batch_texts = embedding_queue_texts[i:i + max_chunks]

                    embedding_result = self._model_instance.invoke_text_embedding(
                        texts=batch_texts,
                    )

                    for vector in embedding_result.embeddings:
                        vector_arr = np.array(vector)
                        try:
                            normalized_embedding = (vector_arr / np.linalg.norm(vector_arr)).tolist()
                            embedding_queue_embeddings.append(normalized_embedding)
                        except Exception as e:
                            logging.exception('Failed transform embedding: ', e)
                cache_embeddings = []
                embedding_cache_list = []

                for i, embedding in zip(embedding_queue_indices, embedding_queue_embeddings):
                    text_embeddings[i] = embedding
                    text_hash = generate_text_hash(texts[i])
                    if text_hash not in cache_embeddings:
                        embedding_cache = Embedding(
                            provider_name=self._model_instance.provider,
                            model_name=self._model_instance.model,
                            text_hash=text_hash,
                        )
                        embedding_cache.set_embedding(embedding)
                        embedding_cache_list.append(embedding_cache)
                        cache_embeddings.append(text_hash)
                Embedding.objects.bulk_create(embedding_cache_list)
            except Exception as ex:
                logger.error('Failed to embed documents: ', ex)
                raise ex

        return text_embeddings

    def embed_query(self, text: str) -> list[float]:
        """Embed query text."""
        # use doc embedding cache or store if not exists
        text_hash = generate_text_hash(text)
        embedding_cache_key = f'{self._model_instance.provider}_{self._model_instance.model}_{text_hash}'
        embedding = redis_default_client.get(embedding_cache_key)
        if embedding:
            redis_default_client.expire(embedding_cache_key, 600)
            return np.frombuffer(base64.b64decode(embedding), dtype="float").tolist()
        try:
            embedding_result = self._model_instance.invoke_text_embedding(texts=[text])

            embedding_results_arr = np.array(embedding_result.embeddings[0])
            embedding_results = (embedding_results_arr / np.linalg.norm(embedding_results_arr)).tolist()
        except Exception as ex:
            raise ex

        try:
            # encode embedding to base64
            embedding_vector = np.array(embedding_results)
            vector_bytes = embedding_vector.tobytes()
            # Transform to Base64
            encoded_vector = base64.b64encode(vector_bytes)
            # Transform to string
            encoded_str = encoded_vector.decode("utf-8")
            redis_default_client.setex(embedding_cache_key, 600, encoded_str)
        except:
            logging.exception('Failed to add embedding to redis')

        return embedding_results
