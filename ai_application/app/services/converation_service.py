from django.db.models import Sum

from app.api.dto import ConversationAdd
from app.constants.app import MessageStatus
from app.core.apps.chat.app_generator import ChatAppGenerator
from app.core.model_provider_manager import ModelProviderManager
from app.core.model_runtime.entities.provider_entities import ModelType, ModelPropertyKey
from app.errors import AppNotFoundError, ConversationNotFoundError
from app.models import Conversation, Account, App


class ConversationService:

    @classmethod
    def add_conversation(cls, dto: ConversationAdd, from_account: Account) -> Conversation:
        app_model: App = App.objects.filter(is_deleted=False, app_no=dto.app_id).first()
        if not app_model:
            raise AppNotFoundError()

        return ChatAppGenerator().add_conversation(app_model, dto, from_account)

    @classmethod
    def check_token_exceed(cls, conversation_id: int) -> bool:
        conversation: Conversation = Conversation.objects.filter(
            conversation_no=conversation_id, is_deleted=False).first()
        if not conversation:
            raise ConversationNotFoundError()

        res = conversation.message_set.filter(
            is_deleted=False,
            status=MessageStatus.NORMAL.value
        ).aggregate(total_tokens=Sum('total_tokens'))
        total_tokens = res.get('total_tokens')
        total_tokens = total_tokens if total_tokens else 0

        provider_model_bundle = ModelProviderManager().get_provider_model_bundle(
            provider=conversation.model_provider,
            model_type=ModelType.LLM
        )
        model_schema = provider_model_bundle.model_type_instance.get_model_schema(conversation.model_id)

        max_input_size = model_schema.model_properties.get(ModelPropertyKey.MAX_INPUT_SIZE)

        return total_tokens >= max_input_size
