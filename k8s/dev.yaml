apiVersion: apps/v1
kind: Deployment
metadata:
  name: yantucs-ai-rc
spec:
  selector:
    matchLabels:
      name: yantucs-ai
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        name: yantucs-ai
    spec:
      containers:
      - name: yantucs-ai
        image: registry.cn-shanghai.aliyuncs.com/yantu_k8s/yantucs-ai-app:<BUILD_TAG>
        ports:
        - containerPort: 8000
        command: ["/bin/sh"]
        args: ["-c", "gunicorn --workers=2 --threads=4 --keep-alive=10 ai_application.wsgi:application -b :8000 --log-level INFO --log-config ./logging.conf"]
        env:
        - name: DEBUG
          value: "1"
        - name: ENVIRONMENT
          value: "1"
        volumeMounts:
          - mountPath: /opt/ai_application/log
            name: log
          - name: yantucs-ai-config
            mountPath: /opt/ai_application/ai_application/config.py
            subPath: config.py
        resources:
          requests:
            cpu: 1
            memory: 800Mi
          limits:
            cpu: 2
            memory: 2000Mi
        livenessProbe:
          initialDelaySeconds: 20   #延迟加载时间
          periodSeconds: 5          #检查时间间隔
          timeoutSeconds: 10        #超时时间设置
          httpGet:
            scheme: HTTP
            port: 8000
            path: /check/beat/
      volumes:
        - name: log
          hostPath:
            path: /var/log/k8s/yantucs-ai
        - name: yantucs-ai-config
          configMap:
            name: yantucs-ai-config
            defaultMode: 420
      imagePullSecrets:
        - name: regsecret

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: yantucs-ai-celery-rc
spec:
  selector:
    matchLabels:
      name: yantucs-ai-celery
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        name: yantucs-ai-celery
    spec:
      containers:
      - name: yantucs-ai-celery-beat
        image: registry.cn-shanghai.aliyuncs.com/yantu_k8s/yantucs-ai-app:<BUILD_TAG>

        command: ["/bin/sh"]
        args: ["-c", "celery -A ai_application beat --loglevel=INFO"]
        env:
        - name: DEBUG
          value: "1"
        - name: ENVIRONMENT
          value: "1"
        volumeMounts:
          - mountPath: /opt/ai_application/log
            name: log
          - name: yantucs-ai-config
            mountPath: /opt/ai_application/ai_application/config.py
            subPath: config.py
        resources:
          requests:
            cpu: 1
            memory: 600Mi
          limits:
            cpu: 2
            memory: 2000Mi
      - name: yantucs-ai-celery
        image: registry.cn-shanghai.aliyuncs.com/yantu_k8s/yantucs-ai-app:<BUILD_TAG>

        command: ["/bin/sh"]
        args: ["-c", "celery -A ai_application worker -l INFO -f ./log/all.log -Q dataset,message_task -n ai_application_celery"]
        env:
        - name: DEBUG
          value: "1"
        - name: ENVIRONMENT
          value: "1"
        volumeMounts:
          - mountPath: /opt/ai_application/log
            name: log
          - name: yantucs-ai-config
            mountPath: /opt/ai_application/ai_application/config.py
            subPath: config.py
        resources:
          requests:
            cpu: 1
            memory: 1200Mi
          limits:
            cpu: 2
            memory: 2000Mi
      - name: yantucs-ai-celery-hot-article
        image: registry.cn-shanghai.aliyuncs.com/yantu_k8s/yantucs-ai-app:<BUILD_TAG>

        command: ["/bin/sh"]
        args: ["-c", "celery -A ai_application worker -l INFO -f ./log/all.log -Q hot_article -n ai_application_celery_hot_article"]
        env:
        - name: DEBUG
          value: "1"
        - name: ENVIRONMENT
          value: "1"
        volumeMounts:
          - mountPath: /opt/ai_application/log
            name: log
          - name: yantucs-ai-config
            mountPath: /opt/ai_application/ai_application/config.py
            subPath: config.py
        resources:
          requests:
            cpu: 1
            memory: 1200Mi
          limits:
            cpu: 2
            memory: 2000Mi
      - name: learn-status-check-celery
        image: registry.cn-shanghai.aliyuncs.com/yantu_k8s/yantucs-ai-app:<BUILD_TAG>

        command: ["/bin/sh"]
        args: ["-c", "celery -A ai_application worker -l INFO -f ./log/all.log -Q learn_status_check -n learn_status_check_celery"]
        env:
        - name: DEBUG
          value: "1"
        - name: ENVIRONMENT
          value: "1"
        volumeMounts:
          - mountPath: /opt/ai_application/log
            name: log
          - name: yantucs-ai-config
            mountPath: /opt/ai_application/ai_application/config.py
            subPath: config.py
        resources:
          requests:
            cpu: 1
            memory: 1200Mi
          limits:
            cpu: 2
            memory: 2000Mi
      - name: document-proofreader-celery
        image: registry.cn-shanghai.aliyuncs.com/yantu_k8s/yantucs-ai-app:<BUILD_TAG>

        command: ["/bin/sh"]
        args: ["-c", "celery -A ai_application worker -l INFO -f ./log/all.log --concurrency=1 -Q doc_proofreader_check -n doc_proofreader_check_celery"]
        env:
        - name: DEBUG
          value: "1"
        - name: ENVIRONMENT
          value: "1"
        volumeMounts:
          - mountPath: /opt/ai_application/log
            name: log
          - name: yantucs-ai-config
            mountPath: /opt/ai_application/ai_application/config.py
            subPath: config.py
        resources:
          requests:
            cpu: 1
            memory: 1200Mi
          limits:
            cpu: 2
            memory: 2000Mi
      volumes:
        - name: log
          hostPath:
            path: /var/log/k8s/yantucs-ai
        - name: yantucs-ai-config
          configMap:
            name: yantucs-ai-config
            defaultMode: 420
      imagePullSecrets:
        - name: regsecret

---

kind: Service
apiVersion: v1
metadata:
  name: yantucs-ai-svc
spec:
  selector:
    name: yantucs-ai
  type:  ClusterIP
  ports:
  - name:  http
    port:  8000
    targetPort:  8000

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: yantucs-ai-ing
  labels:
    name: yantucs-ai-ing
spec:
  rules:
  - host: ai-dev.yantucs.com
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: yantucs-ai-svc
            port:
              number: 8000
