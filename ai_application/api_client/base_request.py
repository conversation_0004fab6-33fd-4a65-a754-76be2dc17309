import logging

import requests
from retrying import retry

logger = logging.getLogger(__name__)


class RequestRetryError(Exception):
    pass


class InternalApiError(Exception):

    def __init__(self, internal_err):
        self.internal_err = internal_err


def retry_on_exception(exception):
    return isinstance(exception, RequestRetryError)


def retry_on_result(result):
    """Return True if we should retry (in this case when result is None), False otherwise"""
    return result is None


@retry(
    stop_max_attempt_number=3,
    wait_fixed=100,
    retry_on_exception=retry_on_exception,
    retry_on_result=retry_on_result
)
def make_request(url, method=None, headers=None, timeout=None, **kwargs):
    method = "GET" if not method else method
    headers = headers if headers else {}
    timeout = timeout if timeout else 3

    # 如果timeout是一个浮点数，那么这个值同时代表了连接超时（connect timeout）和读取超时（read timeout）的时间，单位是秒。
    # 如果timeout是一个元组，那么元组的第一个值是连接超时时间，第二个值是读取超时时间，单位同样是秒。。
    response = requests.request(
        method=method, url=url, headers=headers, timeout=(30, timeout), **kwargs)
    try:
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as err_h:
        # 如果 HTTP 请求返回了不成功的状态码， Response.raise_for_status() 会抛出一个 HTTPError 异常。
        logger.exception(f"HTTP Error: {err_h}")
        try:
            # 特殊处理非200状态码异常
            err_h_res = response.json()
            if 'code' in err_h_res:
                internal_err = response.text
            else:
                internal_err = err_h_res.get('internal_err', '未知错误')
            raise InternalApiError({
                'err': internal_err,
                'url': url,
                'method': method,
                **kwargs
            })
        except InternalApiError as i_e:
            raise i_e
        except Exception as e:
            logger.exception(e)
            internal_err = response.text[:100]
            raise InternalApiError({
                'err': internal_err,
                'url': url,
                'method': method,
                **kwargs
            })
    except requests.exceptions.ConnectTimeout as err_ct:
        # 连接超时
        logger.exception(f"ConnectTimeout Error: {err_ct}")
        raise RequestRetryError()
    except requests.exceptions.ConnectionError as err_c:
        logger.exception(f"Error Connecting: {err_c}")
        raise RequestRetryError()
    except requests.exceptions.ReadTimeout as err_rt:
        # 读取超时
        logger.exception(f"ReadTimeout Error: {err_rt}")
        raise err_rt
    except requests.exceptions.RequestException as err:
        logger.exception(f"Oops, Something went wrong: {err}")
        raise err
