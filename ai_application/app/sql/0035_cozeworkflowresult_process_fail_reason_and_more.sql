--
-- Add field process_fail_reason to cozeworkflowresult
--
ALTER TABLE `ai_coze_workflow_result` ADD COLUMN `process_fail_reason` longtext NULL;
--
-- Add field process_status to cozeworkflowresult
--
ALTER TABLE `ai_coze_workflow_result` ADD COLUMN `process_status` varchar(32) DEFAULT '' NOT NULL;
--
-- Add field is_answered to englishwordtestanswerdetail
--
ALTER TABLE `ai_data_english_word_test_answer_detail` ADD COLUMN `is_answered` bool DEFAULT b'0' NOT NULL;
--
-- Create model EnglishWordWrongQuestion
--
CREATE TABLE `ai_data_english_word_wrong_question` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `user_id` varchar(100) NOT NULL, `answer_num` integer NOT NULL, `is_right_again` bool NOT NULL, `question_id` bigint NOT NULL);
CREATE INDEX `ai_data_english_word_wrong_question_add_time_ca0d17e0` ON `ai_data_english_word_wrong_question` (`add_time`);
CREATE INDEX `ai_data_english_word_wrong_question_user_id_d4b181a0` ON `ai_data_english_word_wrong_question` (`user_id`);
CREATE INDEX `ai_data_english_word_wrong_question_question_id_c33eb183` ON `ai_data_english_word_wrong_question` (`question_id`);