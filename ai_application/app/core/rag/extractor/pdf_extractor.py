# fitz是pyMuPDF的包名
import logging

import fitz

from app.core.rag.extractor.extractor_base import BaseExtractor
from app.core.rag.models.document import Document

logger = logging.getLogger(__name__)


class PdfExtractor(BaseExtractor):

    def __init__(
            self,
            file_path: str,
            file_cache_key: str | None = None
    ):
        self._file_path = file_path
        self._file_cache_key = file_cache_key

    def extract(self) -> list[Document]:
        text = ""

        try:
            doc = fitz.open(self._file_path)
        except Exception as e:
            logger.exception(e)
            raise Exception('文件已损坏')

        for page_number, page in enumerate(doc, start=1):
            text += page.get_text("") + "\n"

        metadata = {"source": self._file_path}
        return [Document(page_content=text, metadata=metadata)]
