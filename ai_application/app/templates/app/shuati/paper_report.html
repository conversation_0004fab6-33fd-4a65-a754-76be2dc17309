<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告</title>
    <script src="https://cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
    <style>        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .report-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .report-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .user-desc {
            background-color: #f0f8ff;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .user-desc h3 {
            margin-top: 0;
        }
        .report-content {
            white-space: pre-wrap;
            line-height: 1.8;
        }
      .back-btn, .debug-btn, .ai-btn {
            display: inline-block;
            padding: 10px 20px;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            text-align: center;
        }
        .back-btn, .debug-btn, .ai-btn {
            background-color: #4CAF50;
            margin-right: 10px;
        }
        .back-btn:hover, .debug-btn:hover, .ai-btn:hover {
            background-color: #45a049;
        }
        .loading-report {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #666;
        }
        .error-message {
            color: red;
            text-align: center;
            padding: 20px;
        }
        .actions {
            margin-top: 20px;
        }

        /* 新增提示词编辑框样式 */
        .panel {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .panel-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
            color: #333;
        }
        .input-area textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: Arial, sans-serif;
        }
        .button-container {
            margin-top: 15px;
            text-align: right;
        }
        .secondary-button {
            padding: 8px 16px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .secondary-button:hover {
            background-color: #0b7dda;
        }
        /* 自定义alert样式 */
        .custom-alert {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: none;
            max-width: 80%;
            text-align: center;
        }
        .custom-alert-message {
            margin-bottom: 20px;
            font-size: 16px;
        }
        .custom-alert-button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .custom-alert-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 9999;
            display: none;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <h1 class="report-title">测试报告</h1>
        <!-- 新增提示词编辑框 -->
        <div class="panel">
            <div class="panel-title">报告生成提示词</div>
            <div class="input-area">
                <textarea placeholder="提示词输入..." id="report-prompt-input">{{ shuati_report_prompt }}</textarea>
            </div>
            <div class="button-container">
                <button id="btn-report-prompt-submit" class="secondary-button">保存提示词</button>
            </div>
        </div>
        <div id="report-content">
            <div class="loading-report">
                正在加载报告内容，请稍候...
            </div>
        </div>

        <div class="actions">
            <a target="_blank" href="{% url 'st_paper_dist_debug' %}" class="debug-btn">进行出题策略调试</a>
            <a href="javascript:void(0)" class="ai-btn">进入知舟问答</a>
<!--            <a href="/console/app/st_paper_index?user_id=" class="back-btn">直接继续出题</a>-->
        </div>
    </div>

    <!-- 自定义alert结构 -->
    <div class="custom-alert-overlay" id="custom-alert-overlay"></div>
    <div class="custom-alert" id="custom-alert">
        <div class="custom-alert-message" id="custom-alert-message"></div>
        <button class="custom-alert-button" id="custom-alert-button">确定</button>
    </div>

    <script>
        // 自定义alert函数
        function showCustomAlert(message) {
            $('#custom-alert-message').text(message);
            $('#custom-alert-overlay').show();
            $('#custom-alert').show();
        }

        $(document).ready(function() {
            // 绑定自定义alert按钮事件
            $('#custom-alert-button').click(function() {
                $('#custom-alert-overlay').hide();
                $('#custom-alert').hide();
            });

            // 保存提示词按钮点击事件
            $('#btn-report-prompt-submit').click(async () => {
                const prompt_content = $('#report-prompt-input').val();

                if (!prompt_content) {
                    showCustomAlert('请输入报告生成提示词');
                    return;
                }

                try {
                    const response = await fetch('{% url 'st_paper_dist_prompt_save' %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            app_no: 'shuati_answer_report',
                            prompt_content: prompt_content,
                        })
                    });

                    const result = await response.json();
                    if (result.code === 0) {
                        showCustomAlert('保存成功！');
                    } else {
                        showCustomAlert(result.msg);
                    }
                } catch (error) {
                    console.error('请求失败:', error);
                    showCustomAlert(`请求失败: ${error.message}`);
                }
            });

            // 从localStorage获取报告数据
            const reportDataStr = localStorage.getItem('reportData');
            console.log("从localStorage获取的数据:", reportDataStr); // 调试用

            if (reportDataStr) {
                try {
                    const reportData = JSON.parse(reportDataStr);
                    console.log("解析后的数据:", reportData); // 调试用

                    let contentHtml = '';

                    // 显示用户知识点掌握情况 (如果存在)
                    if (reportData.user_desc) {
                        contentHtml += `                        <div class="user-desc">
                            <h3>用户知识点掌握情况</h3>
                            <pre>${typeof reportData.user_desc === 'object' ? JSON.stringify(reportData.user_desc, null, 2) : reportData.user_desc}</pre>
                        </div>`;
                    }

                    // 显示报告内容并渲染Markdown
                    if (reportData.report_result) {
                        // 使用marked库渲染Markdown
                        const renderedReport = marked.parse(reportData.report_result);
                        contentHtml += `                        <div class="report-content markdown-body">
                            ${renderedReport}                        </div>`;
                    } else {
                        contentHtml += '<div class="error-message">未生成报告内容</div>';
                    }

                    $('#report-content').html(contentHtml);
                } catch (e) {
                    console.error("解析报告数据出错:", e);
                    $('#report-content').html(`<div class="error-message">报告数据解析失败: ${e.message}</div>`);
                }
            } else {
                $('#report-content').html('<div class="error-message">未找到报告数据</div>');
            }

            // 动态设置返回首页按钮的链接参数
            const userId = new URLSearchParams(window.location.search).get('user_id');
            $('.back-btn').attr('href', `/console/app/st_paper_index?user_id=${userId}`);
            // 为出题策略调试按钮也设置user_id参数
            $('.debug-btn').attr('href', `{% url 'st_paper_dist_debug' %}?user_id=${userId}`);
            // 为知舟问答按钮设置user_id参数
            $('.ai-btn').attr('href', `/console/app/st_ai_assistant_index?user_id=${userId}`);
        });
    </script>
</body>
</html>
