version: 1
disable_existing_loggers: False

formatters:
  simple:
    format: '[%(asctime)s: %(levelname)s/%(threadName)s:%(thread)d] [%(name)s:%(lineno)d] [%(module)s:%(funcName)s]: %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    formatter: simple
    level: INFO
    stream: ext://sys.stdout
  file:
    class: logging.FileHandler
    formatter: simple
    level: INFO
    filename: './log/all.log'  # 指定日志文件的路径
  error_file:
    class: logging.FileHandler
    formatter: simple
    level: INFO
    filename: './log/error.log'  # 指定日志文件的路径


loggers:
  uvicorn:
    level: INFO
    handlers: [file]
    propagate: no

  uvicorn.error:
    level: WARNING
    handlers: [error_file]
    propagate: no

root:
  level: INFO
  handlers: [error_file, console]
