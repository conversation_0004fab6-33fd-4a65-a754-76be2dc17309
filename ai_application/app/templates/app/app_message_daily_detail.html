{% extends 'app/app_message_list.html' %}

{% block title %}
        <span>【{{ app_name }}】{{ date }} 消息列表页面</span>
    {% endblock %}

{% block back %}
        <a style="font-size: 18px" href="{% url 'app_msg_daily_stats' app_id  %}">返回</a>
{% endblock %}



{% block stream_page %}
    
<div id="loading" style="display: none;">加载中...</div>

<script>
    function replaceLastZero(str, replacement) {
      const lastIndex = str.lastIndexOf('0');
      if (lastIndex === -1) return str;
      return str.substring(0, lastIndex) + replacement + str.substring(lastIndex + 1);
    }

    let currentPage = 1;
    let isLoading = false;
    let hasMore = true;
    
    // 初始加载第一页数据
    window.addEventListener('DOMContentLoaded', (event) => {
        currentPage = 1;
        setupInfiniteScroll();
    });
    
    function setupInfiniteScroll() {
        var dom = document.getElementById("table-container")
        dom.addEventListener('scroll', function() {
            console.log('xxxxxx')
            const { scrollTop, scrollHeight, clientHeight } = dom;
            
            // 当滚动到接近底部时加载更多
            if (scrollTop + clientHeight >= scrollHeight - 100 && !isLoading && hasMore) {
                loadMoreData();
            }
        });
    }
    
    function loadMoreData() {
        isLoading = true;
        document.getElementById('loading').style.display = 'block';
        
        currentPage++;
        
        const options = {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json',
            },
        };
        
        fetch(`?page=${currentPage}&date={{ date }}&app_id={{ app_id }}&message_type={{ message_type }}`, options)
        .then(response => response.json())
        .then(data => {
            if (data.message_list.length > 0) {
                const tableBody = document.getElementById('table-body');
                console.log(1111)
                console.log(data.message_list)
                
                // 添加新数据到表格
                data.message_list.forEach(msg => {
                    const row = document.createElement('tr');
                    
                    row.innerHTML = `
                        <td>${msg.user_id ? msg.user_id:''}</td>
                        <td>${msg.add_time ? msg.add_time:null}</td>
                    `
                    if ( !data.is_inner_app){
                        row.innerHTML += `
                            <td>${msg.app_name}</td>
                            <td>${msg.goods_name}</td>
                            <td>${msg.platform}</td>
                        `
                    }
                    if (msg.question_img){
                        row.innerHTML += `
                            <td>
                                <div class="message-content">${msg.query}</div>
                                <div class="image-preview-show"><img width="50" height="50" src="${msg.question_img}" alt=""></div>
                            </td>
                        `
                    }else {
                        row.innerHTML += `
                            <td>
                                <div class="message-content">${msg.query}</div>
                            </td>
                        `
                    }
                    row.innerHTML += `
                         <td>
                            <div class="markdown-body message-content">${msg.answer?marked.parse(msg.answer): ''}</div>
                        </td>
                        <td class="message-token">
                            <div>提问：${msg.message_tokens}</div>
                            <div>回答：${msg.answer_tokens}</div>
                        </td>
                        <td>
                            ${msg.is_exception ? '❌异常': '✅正常'}
                        </td>
                    `
                    if (msg.is_exception){
                        let exception_reason = null;
                        let exception_image = null;
                        if (msg.exception_reason){
                            exception_reason = `<div>失败原因：${msg.exception_reason}</div>`
                        }
                        if (msg.exception_image){
                            exception_image = `
                            <div class="fail-image image-preview-show">
                                <img width="50" height="50" src="${msg.exception_image}" alt="">
                            </div>
                            `
                        }
                        if (exception_reason && exception_image){
                            row.innerHTML += `
                                <td>
                                     ${exception_reason}
                                     ${exception_image}
                                </td>
                            `
                        } else if (exception_reason && !exception_image) {
                            row.innerHTML += `
                                <td>
                                     ${exception_reason}
                                </td>
                            `
                        } else if (!exception_reason && exception_image) {
                            row.innerHTML += `
                                <td>
                                     ${exception_image}
                                </td>
                            `
                        }
                    }else{
                        row.innerHTML += '<td></td>'
                    }
                    
                    let exception_button = null;
                    if (msg.is_exception){
                        let url = "{% url 'app_msg_mark_not_exception' app_id 0 %}"
                        let new_url = replaceLastZero(url, msg.id)
                        exception_button = `
                            <button postUrl="${new_url}" class="op_btn unMarkButton">
                                  取消异常
                            </button>
                        `
                    }else {
                        let url = "{% url 'app_msg_mark_exception' app_id 0 %}"
                        let new_url = replaceLastZero(url, msg.id)
                        exception_button = `
                            <button postUrl="${new_url}" class="op_btn markButton showModalButton">
                                 标记异常
                            </button>
                        `
                    }
                    let detail_button = null;
                    if (data.app_no === 'knowledge_query'){
                        let url = "{% url 'message_tracing_detail' %}"
                        let new_url = url += `?message_id=${msg.message_no}`
                        detail_button = `
                            <button postUrl="${new_url}" class="op_btn tracing_btn">
                                   查看详情
                            </button>
                        `
                    }
                    if (detail_button && exception_button){
                        row.innerHTML += `
                            <td>
                                <div class="opt-btn-group">
                                    ${detail_button}
                                    ${exception_button}
                                </div>
                            </td>
                        `
                    }else if (!detail_button && exception_button){
                        row.innerHTML += `
                            <td>
                                <div class="opt-btn-group">
                                    ${exception_button}
                                </div>
                            </td>
                        `
                    }
                
                    tableBody.appendChild(row);
                });
                
                hasMore = currentPage < data.next_page;
            } else {
                hasMore = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            currentPage--;  // 出错时回退页码
        })
        .finally(() => {
            isLoading = false;
            document.getElementById('loading').style.display = 'none';
        });
    }
</script>

{% endblock %}