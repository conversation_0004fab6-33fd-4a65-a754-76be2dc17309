from app.core.model_runtime.entities.llm_entities import LLMUsage, LLMResult
from app.core.prompt.utils.prompt_message_util import PromptMessageUtil
from app.models import Message, MessageTracing


class MessageTracingLog:

    def __init__(self, message: Message):
        self._message = message
        self.usage = LLMUsage.empty_usage()

    def add_tracing_by_llm_result(
            self,
            tracing_type: str,
            query: str,
            llm_result: LLMResult,
    ):
        log_content = PromptMessageUtil.prompt_messages_to_prompt_for_saving(llm_result.prompt_messages)
        if llm_result.message.reasoning_content:
            # 记录思考内容
            thinking_tracing_type = f'{tracing_type}_thinking'
            self.add_tracing(
                tracing_type=thinking_tracing_type,
                query=query,
                content=log_content,
                answer=llm_result.message.reasoning_content,
                model_provider=llm_result.model_provider,
                model_id=llm_result.model,
                model_params=llm_result.model_params,
            )

        self.add_tracing(
            tracing_type=tracing_type,
            query=query,
            content=log_content,
            answer=llm_result.message.content,
            message_tokens=llm_result.usage.prompt_tokens,
            answer_tokens=llm_result.usage.completion_tokens,
            total_tokens=llm_result.usage.total_tokens,
            latency=llm_result.usage.latency,
            model_provider=llm_result.model_provider,
            model_id=llm_result.model,
            model_params=llm_result.model_params,
        )

    def add_tracing(
            self,
            tracing_type: str,
            query: str,
            content: any = None,
            answer: str = '',
            message_tokens: int = 0,
            answer_tokens: int = 0,
            total_tokens: int = 0,
            latency: float = 0,
            model_provider: str = '',
            model_id: str = '',
            model_params: dict | None = None
    ):
        MessageTracing.objects.create(
            message=self._message,
            type=tracing_type,
            query=query,
            content=content,
            answer=answer,
            message_tokens=message_tokens,
            answer_tokens=answer_tokens,
            total_tokens=total_tokens,
            latency=latency,
            model_provider=model_provider,
            model_id=model_id,
            model_params=model_params,
        )

        self.usage.prompt_tokens += message_tokens
        self.usage.completion_tokens += answer_tokens
        self.usage.total_tokens += total_tokens
        self.usage.latency += latency
