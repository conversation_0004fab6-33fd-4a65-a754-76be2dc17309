import json
from langchain_community.chat_models import ChatOpenAI
from langchain_core.prompts import PromptTemplate

def ner(subject,query):
    default_prompt = """

        你是一个{subject}学科的知识专家。请根据以下的提问内容，准确提取出其中涉及到的知识点：

        {query}

        如果用户的知识点中有错别字请矫正后输出。

        你要输出具体的知识点，而不是范围较大的学科名称等，比如我输入“操作系统中的页面置换算法有哪些”,你应该只输出“页面置换算法”而忽略“操作系统”。

        你输出的知识点必须是提问内容中连续的字符，不可以自己生成新的内容。

        请按照如下列表形式输出，不要添加解释说明：
        ["知识点1","知识点2",...]

        比如：“请简述一下二叉搜索树和平衡二叉树的异同”
        输出：[“二叉搜索树”, “平衡二叉树”]

        """
    llm=ChatOpenAI(
        openai_api_key="fb72b841-9412-4f1a-8d4f-347a4d7938f4",
        temperature=0,
        base_url="https://ark.cn-beijing.volces.com/api/v3/",
        model="doubao-1-5-pro-256k-250115"
    )
    prompt_template = PromptTemplate.from_template(default_prompt)
    prompt = prompt_template.format(
        subject=subject,
        query=query
    )
    response = llm.invoke(prompt).content
    res=json.loads(response)
    return res

if __name__ == "__main__":
    ner(subject='计算机',query="")