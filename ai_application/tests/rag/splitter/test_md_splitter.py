from app.core.rag.extractor.text_extractor import TextExtractor
from app.core.rag.splitter.markdown_splitter import MarkdownHeaderTextSplitter
from app.core.rag.splitter.text_splitter import RecursiveCharacterTextSplitter


chunk_size = 500
chunk_overlap = 30


def test_md_splitter():
    file_path = '/Users/<USER>/Downloads/操作系统.md'

    docs = TextExtractor(file_path).extract()
    md_documents = []
    for d in docs:
        md_res = MarkdownHeaderTextSplitter(
            headers_to_split_on=[
                ("#", "h1"),
                ("##", "h2"),
                ("###", "h3"),
            ],
            strip_headers=False
        ).split_text(d.page_content)
        md_documents.extend(md_res)

    documents = md_documents

    # for i in documents[:20]:
    #     print(i.metadata, i.page_content)
    #     print('='*20)
    #
    # print('+=-'*20)

    for document in documents[:20]:
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size, chunk_overlap=chunk_overlap,
            separators=["\n", "。", ". ", " ", ""],
        )
        splits = text_splitter.split_documents([document])

        for i in splits[:20]:
            print(i.metadata, i.page_content)
            print('='*20)

        print('+=-'*20)
