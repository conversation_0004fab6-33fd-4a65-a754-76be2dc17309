import base64
from volcenginesdkarkruntime import Ark
from PIL import Image
import io


def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


def get_response(image_path):
    try:
        with Image.open(image_path) as img:
            img_format = img.format.lower()  # 获取图片格式
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format=img_format)
            base64_image = base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')

            # 根据图片格式生成正确的 url
            if img_format == 'png':
                url = f"data:image/png;base64,{base64_image}"
            elif img_format == 'jpeg' or img_format == 'jpg':
                url = f"data:image/jpeg;base64,{base64_image}"
            elif img_format == 'webp':
                url = f"data:image/webp;base64,{base64_image}"
            else:
                raise ValueError(f"Unsupported image format: {img_format}")

            client = Ark(
                api_key="9595878c-3684-44af-b775-7f8fa8847ea6",
                base_url="https://ark.cn-beijing.volces.com/api/v3",
            )

            # Image input:
            response = client.chat.completions.create(
                model="doubao-1.5-vision-pro-32k-250115",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "请识别图中内容并以markdown格式里面嵌latex公式格式返回,只需要返回识别出的题目，不用解答",
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": url
                                },
                            },
                        ],
                    }
                ],
            )

            result = response.choices[0].message.content
            return result
    except Exception as e:
        print(f"Error processing image: {e}")
        return None

# if __name__ == "__main__":
#     image_path = "C:/Users/<USER>/Desktop/test_5.PNG"
#
#     question = get_response(image_path)
#
#     print(question)