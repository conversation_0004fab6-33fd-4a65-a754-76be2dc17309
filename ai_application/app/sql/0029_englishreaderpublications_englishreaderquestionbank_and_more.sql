--
-- Create model EnglishReaderPublications
--
CREATE TABLE `ai_english_reader_publications`
(
    `id`                   bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`             datetime(6) NOT NULL,
    `modified_time`        datetime(6) NOT NULL,
    `is_deleted`           bool NOT NULL,
    `publications_content` longtext NULL,
    `article_translate`    longtext NULL,
    `publications_name`    varchar(32) NULL,
    `publications_source`  varchar(32) NULL
);
--
-- Create model EnglishReaderQuestionBank
--
CREATE TABLE `ai_english_reader_question_bank`
(
    `id`             bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`       datetime(6) NOT NULL,
    `modified_time`  datetime(6) NOT NULL,
    `is_deleted`     bool NOT NULL,
    `strategy_id`    longtext NULL,
    `publication_id` longtext NULL,
    `article`        longtext NULL,
    `option`         longtext NULL,
    `answer`         longtext NULL,
    `analysis`       longtext NULL
);
--
-- <PERSON>reate model EnglishReaderStrategy
--
CREATE TABLE `ai_english_reader_strategy`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool NOT NULL,
    `strategy_content` longtext NULL,
    `strategy_name`    varchar(32) NULL,
    `strategy_year`    varchar(32) NULL
);
--
-- Create model EnglishWaikanQuestionBank
--
CREATE TABLE `ai_english_waikan_question_bank`
(
    `id`             bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`       datetime(6) NOT NULL,
    `modified_time`  datetime(6) NOT NULL,
    `is_deleted`     bool   NOT NULL,
    `article`        longtext NULL,
    `publication_id` bigint NOT NULL,
    `strategy_id`    bigint NOT NULL
);
--
-- Create model UserQuestion
--
CREATE TABLE `ai_english_waikan_user_question`
(
    `id`            bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`      datetime(6) NOT NULL,
    `modified_time` datetime(6) NOT NULL,
    `is_deleted`    bool NOT NULL,
    `user_id`       longtext NULL,
    `question_id`   json NULL
);
--
-- Alter field message_type on message
--
-- (no-op)
--
-- Create model EnglishWaikanTestRecord
--
CREATE TABLE `ai_english_waikan_test_record`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool        NOT NULL,
    `user_id`          longtext NULL,
    `sub_question_ids` json NULL,
    `question_feature` varchar(16) NOT NULL,
    `question_id`      bigint      NOT NULL
);
--
-- Create model EnglishWaikanSubQuestion
--
CREATE TABLE `ai_english_waikan_sub_question`
(
    `id`                bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`          datetime(6) NOT NULL,
    `modified_time`     datetime(6) NOT NULL,
    `is_deleted`        bool        NOT NULL,
    `question_stem`     longtext NULL,
    `question_options`  json NULL,
    `question_answer`   varchar(8)  NOT NULL,
    `question_feature`  varchar(16) NOT NULL,
    `question_type`     json NULL,
    `question_analysis` longtext NULL,
    `question_id`       bigint      NOT NULL
);
CREATE INDEX `ai_english_reader_publications_add_time_2670233c` ON `ai_english_reader_publications` (`add_time`);
CREATE INDEX `ai_english_reader_question_bank_add_time_db6b4744` ON `ai_english_reader_question_bank` (`add_time`);
CREATE INDEX `ai_english_reader_strategy_add_time_89f23f3a` ON `ai_english_reader_strategy` (`add_time`);
CREATE INDEX `ai_english_waikan_question_bank_add_time_bb28a345` ON `ai_english_waikan_question_bank` (`add_time`);
CREATE INDEX `ai_english_waikan_question_bank_publication_id_766a7cbc` ON `ai_english_waikan_question_bank` (`publication_id`);
CREATE INDEX `ai_english_waikan_question_bank_strategy_id_36e5001f` ON `ai_english_waikan_question_bank` (`strategy_id`);
CREATE INDEX `ai_english_waikan_user_question_add_time_2d398857` ON `ai_english_waikan_user_question` (`add_time`);
CREATE INDEX `ai_english_waikan_test_record_add_time_3c0b1ee8` ON `ai_english_waikan_test_record` (`add_time`);
CREATE INDEX `ai_english_waikan_test_record_question_id_fc45ee25` ON `ai_english_waikan_test_record` (`question_id`);
CREATE INDEX `ai_english_waikan_sub_question_add_time_cba759cc` ON `ai_english_waikan_sub_question` (`add_time`);
CREATE INDEX `ai_english_waikan_sub_question_question_id_83ed102c` ON `ai_english_waikan_sub_question` (`question_id`);