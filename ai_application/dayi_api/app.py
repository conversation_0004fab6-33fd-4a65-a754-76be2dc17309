from fastapi import FastAPI
from langserve import add_routes
from chain import get_chain
import uvicorn

# 创建应用实例
dayiapp = FastAPI(
    title="知识解惑专家API",
    version="1.0",
    description="专业解答各类问题的AI服务"
)

# 添加LangChain路由
chain = get_chain()
add_routes(
    dayiapp,
    chain,
    path="/api/v1/ask",
    enabled_endpoints=["invoke", "stream"]
)

# 健康检查端点
@dayiapp.get("/health")
def health_check():
    return {"status": "healthy"}

import json
safe_string = json.dumps({"text": "特殊\n内容"})

if __name__ == "__main__":
    uvicorn.run(dayiapp, host="127.0.0.1", port=8000)