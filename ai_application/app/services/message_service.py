from django.conf import settings

from app.errors import MessageNotFoundError
from app.models import Message, RagMessage


class MessageService:

    @classmethod
    def get_message_ref_source(cls, message_no: str) -> list:
        message: Message = Message.objects.filter(
            is_deleted=False, message_no=message_no).first()
        if not message:
            return []

        rag_message: RagMessage = message.rag_message
        if not (rag_message and rag_message.model_score > settings.RAG_ANSWER_SCORE_THRESHOLD):
            return []

        ref_source_qs = message.datasetretrieverresource_set.filter(
            is_deleted=False).order_by('position')

        data = []
        for r in ref_source_qs:
            # if not r.content_with_context:
            #     continue
            # num = len(r.content_with_context)
            # for idx, c in enumerate(r.content_with_context, start=1):
            #     content = c['content'].replace('\n', '')
            #     c['content'] = content if num == idx else f'{content}\n'
            content = r.content.replace('\n', '')
            data.append({
                'name': r.dataset_document_name,
                'segments': [{
                    'is_hit': False,
                    'content': f'{content}...'
                }],
            })
        return data
