--
-- Create model UnifiedExaminationQuestion
--
CREATE TABLE `unified_examination_question`
(
    `id`               bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `add_time`         datetime(6) NOT NULL,
    `modified_time`    datetime(6) NOT NULL,
    `is_deleted`       bool       NOT NULL,
    `question_num`     integer    NOT NULL,
    `library`          integer    NOT NULL,
    `year`             varchar(4) NOT NULL,
    `lost_result`      bool       NOT NULL,
    `has_llm_analysis` bool       NOT NULL,
    `title`            longtext NULL,
    `answer_body`      longtext NULL,
    `choice_body`      longtext NULL,
    `analysis`         longtext NULL,
    `content`          json NULL,
    `llm_analysis`     longtext NULL
);
CREATE INDEX `unified_examination_question_add_time_83f6b986` ON `unified_examination_question` (`add_time`);