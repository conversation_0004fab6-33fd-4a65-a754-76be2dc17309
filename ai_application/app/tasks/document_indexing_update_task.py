import logging
import time

from django.utils import timezone

from app.core.indexing_runner import IndexingRunner
from app.core.rag.index_processor.index_processor_factory import IndexProcessorFactory
from app.models import Dataset, DatasetDocument, DocumentSegment

logger = logging.getLogger(__name__)


def document_indexing_update_task(dataset_no: str, document_no: str):
    logger.info('Start update document: {}'.format(document_no))
    start_at = time.perf_counter()

    dataset: Dataset = Dataset.objects.filter(
        is_deleted=False, dataset_no=dataset_no).first()
    dataset_document: DatasetDocument = DatasetDocument.objects.filter(
        is_deleted=False, document_no=document_no).first()
    # 资料库或者文档不存在，则不处理
    if not dataset or not dataset_document:
        return

    dataset_document.indexing_status = 'parsing'
    dataset_document.processing_started_at = timezone.now()
    dataset_document.save(update_fields=['indexing_status', 'processing_started_at'])

    # delete all document segment and index
    try:
        index_type = dataset_document.index_type
        index_processor = IndexProcessorFactory(index_type).init_index_processor()
        segments = DocumentSegment.objects.filter(dataset_document=dataset_document)
        if segments:
            index_node_ids = [segment.index_node_id for segment in segments]
            # delete from vector index
            index_processor.clean(dataset, index_node_ids)
            DocumentSegment.objects.filter(dataset_document=dataset_document).update(
                is_deleted=True, modified_time=timezone.now()
            )
    except Exception as e:
        logger.exception(e)
        logger.error("Cleaned document when document update data source or process rule failed")

    try:
        indexing_runner = IndexingRunner()
        indexing_runner.run(dataset, [dataset_document])
        end_at = time.perf_counter()
        logger.info('Processed dataset: {} latency: {}'.format(1, end_at - start_at))
    except Exception as e:
        logger.exception(e)
