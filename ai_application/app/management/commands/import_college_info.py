# import pandas as pd
# from django.core.management.base import BaseCommand
#
# from app.models import CollegeInfo
#
#
# class Command(BaseCommand):
#     help = 'Import college information from a CSV file'
#
#     def handle(self, *args, **options):
#         # 读取 CSV 文件
#         csv_file_path = '院校资料-组合版.csv'
#         df = pd.read_csv(csv_file_path)
#
#         # 遍历 DataFrame 的每一行
#         for index, row in df.iterrows():
#             college_info = CollegeInfo(
#                 discipline_category_code=row['学科门类编号'],
#                 discipline_category=row['学科门类名称'],
#                 primary_major_code=row['一级专业编号'],
#                 primary_major=row['一级专业名称'],
#                 secondary_major_code=row['二级专业编号'],
#                 secondary_major=row['二级专业名称'],
#                 suitable_population=row['适合人群'],
#                 description=row['说明'],
#                 employment_direction=row['就业方向/研究方向'],
#                 common_examination_major=row['常考专业推荐书籍'],
#                 recommended_colleges=row['院校推荐']
#             )
#             college_info.save()
#
#         self.stdout.write(self.style.SUCCESS('Successfully imported college information'))
