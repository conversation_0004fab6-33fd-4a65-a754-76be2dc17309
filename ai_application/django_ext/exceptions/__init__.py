from rest_framework.exceptions import APIException
from rest_framework import status
from .error_code import error_code

default_app_config = 'django_ext.exceptions.apps.ExceptionsConfig'


class InternalException(APIException):
    """
    程序内部处理错误
    """
    status_code = status.HTTP_200_OK
    code = None
    detail_err = None

    def __init__(self, code=None, detail=None, detail_err=None, internal_err=None):
        if code and self.code is None:
            self.code = code

        self.internal_err = internal_err
        self.code = self.code if self.code in error_code else '-2'
        self.detail_err = detail_err
        super(InternalException, self).__init__(
            detail=(detail or error_code[self.code]),
            code=self.code
        )

    def __str__(self):
        return self.detail
