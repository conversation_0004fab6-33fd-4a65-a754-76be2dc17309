from django.db import models


class QuestionType(models.IntegerChoices):
    SINGLE = 0, '单选题'
    MULTIPLE = 1, '多选题'
    SUBJECTIVE = 2, '主观题'
    MATERIAL = 3, '材料题'
    SHARE_TITLE = 4, '共享题干题'
    SHARE_OPTION = 5, '共享选项题'
    FILL_BLANK = 6, '填空题'


class LearningStage(models.TextChoices):
    MODI = 'modi', '摸底测试'
    BASIC = 'basic', '基础巩固'
    BASIC_IMPROVE_L = 'basic_improve_l', '基础提升_初级'
    BASIC_IMPROVE_M = 'basic_improve_m', '基础提升_中级'
    BASIC_IMPROVE_H = 'basic_improve_h', '基础提升_高级'
    CORE_BASIC = 'core_basic', '核心基础'
    CORE_IMPROVE_L = 'core_improve_l', '核心提升_初级'
    CORE_IMPROVE_M = 'core_improve_m', '核心提升_中级'
    CORE_IMPROVE_H = 'core_improve_h', '核心提升_高级'
    CORE_IMPROVE = 'core_improve', '核心提高'


class StageChangeType(models.TextChoices):
    UP = 'up', '升级'
    DOWN = 'down', '降级'
    STRENGTHEN = 'strengthen', '阶段强化'
    PASS_NEXT_CYCLE = 'pass_next_cycle', '通过后进入下一周期'
    FAIL_NEXT_CYCLE = 'fail_next_cycle', '失败后进入下一周期'


class PaperStage(models.IntegerChoices):
    MODI = 0, '摸底测试'
    BASIC = 1, '基础巩固'
    BASIC_IMPROVE_LOW = 2, '基础提升_初级'
    BASIC_IMPROVE_MIDDLE = 3, '基础提升-中级'
    BASIC_IMPROVE_HIGH = 4, '基础提升-高级'
    CORE_BASIC = 5, '核心基础'
    CORE_IMPROVE_LOW = 6, '核心提升_初级'
    CORE_IMPROVE_MIDDLE = 7, '核心提升_中级'
    CORE_IMPROVE_HIGH = 8, '核心提升_高级'
    CORE_IMPROVE = 9, '核心提高'


def get_paper_stage_by_learning_stage(paper_stage):
    mapping = {
        LearningStage.MODI: PaperStage.MODI,
        LearningStage.BASIC: PaperStage.BASIC,
        LearningStage.BASIC_IMPROVE_L: PaperStage.BASIC_IMPROVE_LOW,
        LearningStage.BASIC_IMPROVE_M: PaperStage.BASIC_IMPROVE_MIDDLE,
        LearningStage.BASIC_IMPROVE_H: PaperStage.BASIC_IMPROVE_HIGH,
        LearningStage.CORE_BASIC: PaperStage.CORE_BASIC,
        LearningStage.CORE_IMPROVE_L: PaperStage.CORE_IMPROVE_LOW,
        LearningStage.CORE_IMPROVE_M: PaperStage.CORE_IMPROVE_MIDDLE,
        LearningStage.CORE_IMPROVE_H: PaperStage.CORE_IMPROVE_HIGH,
        LearningStage.CORE_IMPROVE: PaperStage.CORE_IMPROVE,
    }
    return mapping.get(paper_stage)


# 升级弹窗，按钮使用
LEARNING_STAGE_LABELS = {
    LearningStage.MODI: '摸底测试',
    LearningStage.BASIC: '基础巩固',
    LearningStage.BASIC_IMPROVE_L: '基础提升-初级',
    LearningStage.BASIC_IMPROVE_M: '基础提升-中级',
    LearningStage.BASIC_IMPROVE_H: '基础提升-高级',
    LearningStage.CORE_BASIC: '核心基础',
    LearningStage.CORE_IMPROVE_L: '核心提升-初级',
    LearningStage.CORE_IMPROVE_M: '核心提升-中级',
    LearningStage.CORE_IMPROVE_H: '核心提升-高级',
    LearningStage.CORE_IMPROVE: '核心提高',
}
