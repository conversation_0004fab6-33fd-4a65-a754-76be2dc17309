import datetime
import logging
import json

from celery import shared_task
from django.conf import settings
from django.utils import timezone

from app.constants.app import AppMode
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models.model import PromptTemplate
from app.models.model import UnifiedExaminationQuestion as Ueq

logger = logging.getLogger(__name__)


CHOICE_MAP = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"]


def get_problem_and_answer(question: Ueq):
    choices_str = '\n\n'.join(question.choice)
    problem = f'{question.title}\n\n{choices_str}'

    analysis_arr = []
    if question.answer:
        answer_str = ','.join(question.answer)
        analysis_arr.append(f"答案为：{answer_str}")
    if question.analysis:
        analysis_arr.append(f"解析：{question.analysis}")
    analysis = '\n\n'.join(analysis_arr)

    return problem, analysis


@shared_task(queue='message_task')
def sync_question_data_task(question_id: int):
    logger.info(f'Start sync_question_data_task..., question_id: {question_id}')
    question = Ueq.objects.get(id=question_id)
    if question.is_deleted or question.lost_result:
        return

    problem, answer = get_problem_and_answer(question)

    user_inputs = {
        'problem': problem,
        'answer': answer,
    }
    prompt_template = PromptTemplate.objects.get(app_no='knowledge_deep_question')

    # 构建 prompt
    pre_prompt = prompt_template.assemble_prompt(
        query='',
        inputs=user_inputs,
        prompt_content=user_inputs.get('deep_question_prompt')
    )
    model_conf = get_llm_model_config(app_model_conf=None, prompt_template=prompt_template)
    # 调用模型
    try:
        result = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=False,
        )
        # update db
        question.has_llm_analysis = True
        question.llm_analysis = result.message.content
        question.save(update_fields=['has_llm_analysis', 'llm_analysis'])
    except Exception as e:
        logger.exception(e)


@shared_task(queue='message_task')
def auto_check_question_data_task():
    logger.info('Start auto_check_question_data_task...')

    if settings.ENVIRONMENT != settings.ENV_PRODUCT:
        return

    # 每次同步一个月以内的数据，5条数据
    timeout = timezone.now() - datetime.timedelta(days=30)
    for q in Ueq.objects.filter(
            is_deleted=False, lost_result=False, add_time__gt=timeout, has_llm_analysis=False,
    )[:5]:
        sync_question_data_task.delay(q.id)

