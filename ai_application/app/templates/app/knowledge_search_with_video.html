<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>知识点搜索_关联视频</title>
<script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
<style>
  body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
  }
  .search-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
  .search-box {
    width: 300px;
    padding: 10px;
    margin-right: 10px;
  }
  .search-button {
    padding: 10px 20px;
  }
  .container {
    display: flex;
    justify-content: space-between;
  }
  .results-container {
    width: 55%;
  }
  .result-list {
    list-style: none;
    padding: 0;
  }
  .result-item {
    margin-bottom: 20px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 10px;
  }
  .result-title {
    font-size: 18px;
    color: #333;
  }
  /* 遮罩层样式 */
  #overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
  .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
    .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
</style>
</head>
<body>

<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <p>正在请求...</p>
    </div>
</div>

<div class="search-container">
  <input type="text" id="searchInput" class="search-box" placeholder="输入知识点">
  <button onclick="search()" class="search-button">搜索</button>
  <div style="padding-top: 8px; margin-left: 5px"><span id="queryCount">0</span> 字符</div>
</div>

<p><a href="{% url 'knowledge_list' %}" target="_blank">查看知识点</a></p>

<div class="container">

  <div class="results-container">
    <div class="search-path-title">匹配结果：</div>
    <div class="result-list" id="resultList">
      <!-- 搜索结果将在这里显示 -->
    </div>
    <div class="deep-result-list markdown-body" id="deep-result-list">
      <!-- 搜索结果将在这里显示 -->
    </div>
  </div>
</div>
<!-- 新增遮罩层 -->
<div id="overlay"></div>

<script>
const resultList = document.getElementById('resultList');

function search() {
    const input = $('#searchInput').val();
    if (!input) {
        alert('请输入搜索内容');
        return;
    }

    $('#loadingOverlay').css('display', 'flex');

    resultList.innerHTML = ''; // 清空之前的搜索结果

    message_id = null;
    const url = "{% url 'knowledge_search_with_video_result' %}";
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: input,
        })
    })
      .then(response => {
          $('#loadingOverlay').css('display', 'none');
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
          $('#loadingOverlay').css('display', 'none');
          if (!(data.data && data.data.length)) {
              resultList.innerHTML = '<div class="result-item"><div class="result-title">未匹配到课节</div></div>'
              return
          }
          data.data.forEach(function(item) {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.innerHTML = `
              <div class="result-title">课节名称：【${item.name}】</div>
            `;
            resultList.appendChild(resultItem);
        });
      })
      .catch(error => {
          $('#loadingOverlay').css('display', 'none');
          console.error('Error:', error);
      });
}

$(function () {
    function updateCharCount() {
        $('#queryCount').text($('#searchInput').val().length);
    }

    // 绑定 input 事件到 textarea
    $('#searchInput').on('input', updateCharCount);
    // 初始调用以设置初始计数
    updateCharCount();

    $('#searchInput').on('keydown', function(e){
        // 检查按下的键是否是回车键
        if (e.which === 13) {
            e.preventDefault();
            search();
        }
    });
})
</script>

</body>
</html>
