import os
import django
import re
import ast
import json
from datetime import datetime, timedelta
import time
from typing import TypedDict, Dict, Any, List

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()

from app.models import KaoGangAnalysis, SuperViseInitStudentStatus
from langchain.agents import initialize_agent, Tool, AgentType
from langchain.tools import StructuredTool
from langchain_openai import ChatOpenAI
from django.conf import settings
from ai_application.settings import DAYI_TEXT_MODEL
from langgraph.graph import StateGraph, END

# =====================================================================
# 状态定义
# =====================================================================
class GraphState(TypedDict):
    user_data: Dict[str, Any]
    macro_plan: Dict[str, Any]  # 存储宏观规划结果
    status: str  # 'pass', 'fail', 'in_progress'


# =================================================================
# 宏观规划智能体 (MacroPlannerAgent)
# =================================================================
class MacroPlannerAgent:
    def generate_macro_plan(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成宏观规划，划分阶段并分配时间比例"""
        # 获取开始时间和结束日期
        start_date_str = user_data.get("开始时间")
        exam_date_str = user_data.get("结束日期")
        if not start_date_str or not exam_date_str:
            return {"error": "缺少开始时间或结束日期"}
        
        try:
            # 解析开始日期（支持两种格式）
            try:
                start_date = datetime.strptime(start_date_str, "%Y年%m月%d日")
            except:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
            
            # 解析结束日期
            try:
                exam_date = datetime.strptime(exam_date_str, "%Y-%m-%d")
            except:
                exam_date = datetime.strptime(exam_date_str, "%Y年%m月%d日")
            
            # 直接使用结束日期作为基础阶段结束日期
            base_end_date = exam_date
            
            # 计算基础阶段总天数
            total_days = (base_end_date - start_date).days
            if total_days <= 0:
                return {"error": "基础阶段结束日期早于开始日期"}
                
            total_months = total_days / 30.44  # 平均每月天数
        except Exception as e:
            return {"error": f"日期解析失败: {str(e)}"}

        # 根据总月数确定阶段划分比例
        if total_months > 6:
            ratio = "3:2:4:1"
            stages = [
                {"name": "早鸟先行", "duration_ratio": 3},
                {"name": "基础入门", "duration_ratio": 2},
                {"name": "基础进阶", "duration_ratio": 4},
                {"name": "知识梳理", "duration_ratio": 1}
            ]
        elif 3 <= total_months <= 6:
            ratio = "3:5:2"
            stages = [
                {"name": "基础入门", "duration_ratio": 3},
                {"name": "基础进阶", "duration_ratio": 5},
                {"name": "知识梳理", "duration_ratio": 2}
            ]
        else:
            ratio = "8:2"
            stages = [
                {"name": "基础筑基", "duration_ratio": 8},
                {"name": "知识梳理", "duration_ratio": 2}
            ]

        # 计算每个阶段的时间范围
        total_ratio = sum([stage["duration_ratio"] for stage in stages])
        current_date = start_date
        for stage in stages:
            # 计算该阶段的天数
            stage_days = int((stage["duration_ratio"] / total_ratio) * total_days)
            end_date = current_date + timedelta(days=stage_days)
            stage["start_date"] = current_date.strftime("%Y年%m月%d日")
            stage["end_date"] = end_date.strftime("%Y年%m月%d日")
            current_date = end_date

        return {
            "total_months": total_months,
            "ratio": ratio,
            "stages": stages
        }


# =================================================================
# 阶段表格智能体 (StageTableAgent)
# =================================================================
class StageTableAgent:
    def generate_stage_table(self, stages: List[Dict], user_data: Dict, feedback: str = "") -> str:
        llm = ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="doubao-seed-1-6-thinking-250615"
        )
        
        # 创建考纲获取工具（使用StructuredTool支持多参数）
        tools = [
            StructuredTool.from_function(
                func=FoundationalPeriodService.get_KaoGangAnalysis,
                name="get_kaogang_analysis",
                description=(
                    "根据科目名称获取考纲内容。输入参数应为单个科目名称字符串。"
                    "支持科目名：数学一, 英语一, 408计算机, 政治"
                ),
            )
        ]
        
        # 初始化结构化代理，明确指定输入模式
        agent = initialize_agent(
            tools,
            llm,
            agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True,
            handle_parsing_errors=True,
            input_key="input"  # 明确指定输入键
        )
        
        # 构建提示词（包含审核反馈）
        prompt = f"""
你是一位专业的考研基础阶段规划师，严格按照以下要求生成学习计划：

## 基础信息
1. 宏观规划阶段（均为基础阶段）：{json.dumps(stages, ensure_ascii=False)}
2. 用户基础情况：{user_data.get('初始画像', '')}
3. 历史反馈：{feedback if feedback else "无"}

## 输出格式要求（必须严格遵守）
每个阶段必须包含：
[阶段名称]（[开始日期] - [结束日期]）
**阶段目标**：
| 科目 | 核心目标 |
|------|----------|
| [科目全称] | [具体目标，10-20字] |

## 内容限制
1. 绝对不要包含：
   - 冲刺阶段、强化阶段等非基础阶段内容
   - 超出考纲范围的知识点
   - 与用户基础情况不符的目标
2. 必须基于：
   - 通过get_kaogang_analysis工具获取的最新考纲
   - 通过get_kaogang_analysis工具获取的学科逻辑
   - 用户初始画像中的薄弱环节

## 工具使用规范
1. 对每个科目必须调用工具：get_kaogang_analysis(科目名称)
2. 优先获取：
   - 考纲中的基础章节（标★内容）
   - 学科逻辑中的先后顺序要求
3. 工具返回结果必须体现在核心目标中

## 质量标准
1. 目标可衡量：使用“掌握”“完成”“理解”等动词
2. 内容针对性：直接关联考纲具体章节
3. 阶段递进性：后阶段目标必须建立在前阶段基础上
"""
        # 使用invoke方法替代run方法
        response = agent.invoke({"input": prompt})
        return response["output"]



# =================================================================
# 审核智能体 (ReviewerAgent)
# =================================================================
class ReviewerAgent:
    def __init__(self):
        self.llm = ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="doubao-seed-1-6-thinking-250615"
        )
    
    def review_plan(self, plan_content: str, exam_subjects: List[str]) -> Dict[str, Any]:
        prompt = f"""
## 角色
你是考研规划高级审核员，负责检查基础阶段学习计划的科目覆盖完整性

## 审核内容
{plan_content}

## 考试科目列表
{", ".join(exam_subjects)}

## 审核要求
1. 检查表格是否包含所有考试科目,但是可以允许其中某些表格不包含所有的考试科目，只需要保证所有表格中出现的所有科目都覆盖到即可
2. 忽略科目名称大小写差异（如"（408）计算机学科基础"和"408计算机"视为相同）
3. 每个科目至少出现一次即为覆盖
4. 格式是否是规范漂亮的markdown格式，表格是否对齐
5. 该计划是基础阶段的具体划分，核心目标中不要涉及一些在传统意义中 冲刺阶段，强化阶段才涉及的内容
## 输出格式
**严格要求**：必须使用以下XML标签，标签内不得有空格，标签必须正确闭合
<thinking>
1. 列出发现的科目
2. 对比考试科目列表
3. 得出结论
</thinking>
<verdict>pass</verdict> 或 <verdict>fail</verdict>
<feedback>若未通过，明确说明缺少的科目</feedback>
"""
        response = self.llm.invoke(prompt).content
        
        # 解析响应
        verdict = "fail"
        feedback = "审核格式错误"
        
        # 使用正则表达式匹配 verdict 标签，允许空格和大小写变化
        verdict_match = re.search(r"<verdict>\s*(pass|fail)\s*</verdict>", response, re.IGNORECASE)
        if verdict_match:
            verdict = verdict_match.group(1).lower()
            feedback = ""
            
            # 提取 feedback 标签内容，允许空格和大小写变化
            if verdict == "fail":
                feedback_match = re.search(r"<feedback>\s*([\s\S]*?)\s*</feedback>", response, re.IGNORECASE)
                feedback = feedback_match.group(1).strip() if feedback_match else "缺少科目未说明"
        else:
            verdict = "fail"
            feedback = "审核格式错误：未找到有效的<verdict>标签"
        
        return {"status": verdict, "feedback": feedback}


class FoundationalPeriodService:


    @staticmethod
    def get_KaoGangAnalysis(subject):
        """Query CoursePackageContent by package name"""
        # 提取subject字典中的title字段作为查询条件
        subject_title = subject.get('title') if isinstance(subject, dict) else subject
        queryset = KaoGangAnalysis.objects.filter(subject=subject_title).first()
        resD = {
            "考纲内容":queryset.kaogang_content,
            "学科备考逻辑":queryset.kaogang_logic
        }
        res = json.dumps(resD, ensure_ascii=False)
        return res

    @staticmethod
    def get_student_analysis(user_id):
        """Retrieve analysis content from SuperViseInitStudentStatus"""
        student_data = SuperViseInitStudentStatus.objects.filter(user_id=user_id).first()
        if not student_data:
            return None
            
        query = student_data.query
        pattern = r"'考试范围':\s*(\{[^{}]*\})"
        match = re.search(pattern, query)
        subjects = match.group(1) if match else "{}"
        exam_year = student_data.exam_date[:4]
        end_date = f"{exam_year}-06-30"

        return {
            "初始画像": student_data.analysis,
            "结束日期": end_date,
            "开始时间": student_data.date,
            "用户状态": student_data.study_status,
            "考试科目": subjects,
            "学习阶段": student_data.study_stage if student_data.study_stage != "无" else None
        }
    
    @staticmethod
    def _parse_exam_subjects(subjects_str: str) -> List[str]:
        """解析考试科目字符串"""
        try:
            exam_subjects = ast.literal_eval(subjects_str)
            return list(exam_subjects.keys())
        except:
            return []

    
    # =================================================================
    # 主工作流
    # =================================================================
    @staticmethod
    def test_llm(user_id):

        start_time = time.time()
        # 获取用户数据
        analysis = FoundationalPeriodService.get_student_analysis(user_id)
        
        if not analysis:
            return "未找到用户分析数据"
        
        # 生成宏观规划
        macro_planner = MacroPlannerAgent()
        macro_plan = macro_planner.generate_macro_plan(analysis)
        if "error" in macro_plan:
            return f"宏观规划失败: {macro_plan['error']}"
        
        # 解析考试科目
        exam_subjects = FoundationalPeriodService._parse_exam_subjects(
            analysis.get("考试科目", "{}")
        )
        if not exam_subjects:
            return "考试科目解析失败"
        
        # 实例化智能体
        table_agent = StageTableAgent()
        reviewer = ReviewerAgent()
        
        # 迭代优化流程
        MAX_ATTEMPTS = 2
        stage_table = ""
        feedback = ""
        
        for attempt in range(MAX_ATTEMPTS):
            print(f"生成阶段表格 (尝试{attempt+1}/{MAX_ATTEMPTS})")
            stage_table = table_agent.generate_stage_table(
                macro_plan['stages'], 
                analysis,
                feedback
            )
            
            print("生成结果：\n", stage_table[:500] + ("..." if len(stage_table) > 500 else ""))
            
            review_result = reviewer.review_plan(stage_table, exam_subjects)
            if review_result["status"] == "pass":
                print("✅ 审核通过")
                break
                
            feedback = review_result["feedback"]
            print(f"⚠️ 审核未通过：{feedback}")
        else:
            print(f"⚠️ 已达到最大重试次数{MAX_ATTEMPTS}，使用最终结果")
        end_time = time.time()
        print(f"生成耗时: {end_time - start_time:.2f} 秒")
        return stage_table

if __name__ == "__main__":

    user_id = "1221dadawf"
    print(f"开始为用户 {user_id} 生成考研规划报告...")
    result = FoundationalPeriodService.test_llm(user_id=user_id)
    
    if result:
        print("报告生成成功！")
        with open("foundational.md", "w", encoding="utf-8") as f:
            f.write(result)
        print(f"报告已保存至: foundational.md")
        
        # 在控制台显示报告摘要
        print("\n===== 报告摘要 =====")
        print(result[:500] + "..." if len(result) > 500 else result)
    else:
        print("报告生成失败，请检查错误信息")