from ai_application.settings import TEMPLATES
from app.api.dto import ModelManagementMessageDto
import json
import logging
import time
import uuid
from typing import Generator

from cozepy import ChatEventType, ChatUsage, MessageObjectString, MessageObjectStringType
from cozepy import Message as CozeMessage
from django.conf import settings
from django.core.cache import cache
from pydantic_core import ValidationError

from app.api.dto import CozeConversationAdd, CozeChatMessageDto
from app.api.invoke_llm import invoke_remote_llm
from app.constants.app import AppMode
from app.libs.coze_api import coze_api_client
from app.errors import AppNotFoundError, MessageNotFoundError
from app.models import App, Account, Message, InvokeFrom, Conversation, PromptTemplate

logger = logging.getLogger(__name__)


class ModelManagementGenerateService:
    @classmethod
    def create_message(cls, app_model, from_account: Account, params,userinfo) -> Message:
        conversation = Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app=app_model,
            app_model_config=app_model.app_model_config,
            name='New conversation',
            from_account=from_account,
            from_biz_id='',
        )

        return Message.objects.create(
            message_no=str(uuid.uuid4()),
            conversation=conversation,
            app=app_model,
            app_model_config=app_model.app_model_config,
            query=json.dumps(params, ensure_ascii=False),
            from_account=from_account,
            userinfo=userinfo,
        )


    @classmethod
    def generate(
            cls,
            dto: ModelManagementMessageDto,
            authentication_info,
            app_model,
            from_account,

    ) -> Generator | dict:
        prompt_model = PromptTemplate.objects.filter(is_deleted=False, app_no=dto.app_id).first()
        if not prompt_model:
            raise AppNotFoundError()

        # 输出参数构成
        prompt_content = prompt_model.prompt_content if prompt_model else ''
        # 处理自定义变量替换
        if prompt_model.custom_variables:
            custom_variable_list = prompt_model.custom_variables
            for custom_variable in custom_variable_list:
                value = dto.inputs.get(custom_variable)
                if not value:
                    raise ValueError(f'user inputs [{custom_variable}] is required')
                prompt_content = prompt_content.replace('{{' + custom_variable + '}}', value)

        if dto.image:
            prompt_messages = [
                {
                    "role": "system",
                    "content": prompt_content
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": dto.query},
                        {"type": "image_url", "url": dto.image}
                    ]
                }
            ]
        else:
            prompt_messages = [
                {
                    "role": "system",
                    "content": prompt_content
                },
                {
                    "role": "user",
                    "content": dto.query
                }
            ]

        call_params = {
            "user_id": dto.user_id,
            "model": prompt_model.model_id,
            "prompt_messages": prompt_messages,
            "model_parameters": prompt_model.model_params,
            "stream": dto.stream
        }
        userinfo = {"user_id" :dto.user_id}
        # 初始化message
        message = cls.create_message(app_model, from_account,call_params,userinfo)

        result = invoke_remote_llm(
            payload_data=call_params,
            authentication_info=authentication_info,
            stream=dto.stream,
            message=message
        )

        if dto.stream:
            return result
        else:
            return result


