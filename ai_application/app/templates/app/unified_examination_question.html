<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统考题目</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/marked/13.0.2/marked.min.js"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/contrib/auto-render.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
            position: sticky;
            top: -2px; /* 固定在顶部 */
            z-index: 10; /* 确保在其他内容之上 */
        }

        .markdown-body {
            max-height: 400px; /* 设置最大高度 */
            overflow-y: auto; /* 纵向滚动 */
        }

        .table-container {
            max-height: 80vh; /* 设置容器高度以启用滚动 */
            overflow-y: auto; /* 启用垂直滚动 */
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        #pagination {
            float: right;
            margin-right: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .modal-content {
            background-color: white;
            margin: 20% auto;
            padding: 20px;
            width: 50%;
            border: 1px solid #ccc;
        }

        #textInput {
            width: 70%;
        }
        
    </style>
</head>
<body>
<h1>
    {% block title %}
        <span>&nbsp;统考题目列表页面</span>
    {% endblock %}
    {% block back %}{% endblock %}
</h1>

<div style="margin: 10px">
    <form action="{% url 'unified_examination_question' %}">
        <input type="number" name="year" value="{{ year }}" placeholder="年份" style="height: 30px">
        <input type="number" name="question_no" value="{{ question_no }}" placeholder="题号" style="height: 30px">
        <input style="height: 30px" type="submit" value="搜索🔍">
    </form>
</div>

<div class="table-container">
    <table>
        <thead>
        <tr>
            <th style="width:50px">题目编号</th>
{#            <th>题库类型</th>#}
            <th style="width:50px">年份</th>
            <th style="width:30px">题号</th>
            <th>题干</th>
            <th>选项</th>
            <th style="width:100px">答案</th>
            <th style="width:100px">解析</th>
            <th>LLM解析结果</th>
        </tr>
        </thead>
        <tbody>
        {% for question in question_list %}
            <tr>
                <td>
                    <a href="{% url 'ueq_detail' question.question_num %}" target="_blank">{{ question.question_num }}</a>
                </td>
                <td>{{ question.year }}</td>
                <td>{{ question.question_no }}</td>
                <td>{{ question.title|default_if_none:"-" }}</td>
                <td>
                    {% for c in question.choice %}
                        <div class="markdown-body">{{ c }}</div>
                        {% empty %}
                        <span>-</span>
                    {% endfor %}
                </td>
                <td>
                    {% for a in question.answer %}
                        <div class="markdown-body">{{ a }}</div>
                        {% empty %}
                        <span>-</span>
                    {% endfor %}
                </td>
                <td>
                    <div class="markdown-body">{{ question.analysis }}</div>
                </td>
                <td>
                    <div class="markdown-body">{{ question.llm_analysis|default_if_none:"无" }}</div>
                </td>
            </tr>
        {% endfor %}
        </tbody>

    </table>
</div>

<br>


<p id="pagination">
    {#上一页#}
    {% if prev_page > 0 %}
        <button>
            <a href="{% url 'unified_examination_question' %}?page={{ prev_page }}&limit={{ limit }}&year={{ year }}&question_no={{ question_no }}">上一页</a>
        </button>
    {% else %}
        <button disabled>上一页</button>
    {% endif %}
    {#下一页#}
    {% if next_page > 0 %}
        <button>
            <a href="{% url 'unified_examination_question' %}?page={{ next_page }}&limit={{ limit }}&year={{ year }}&question_no={{ question_no }}">下一页</a>
        </button>
    {% else %}
        <button disabled>下一页</button>
    {% endif %}
</p>

<script>
    const md_list = document.querySelectorAll('.markdown-body');
    const katexOptions = {
        // customised options
        // • auto-render specific keys, e.g.:
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        // • rendering keys, e.g.:
        throwOnError : false
    }

    md_list.forEach(md_item => {
        md_item.innerHTML = marked.parse(md_item.innerHTML);
    });
    $('.markdown-body').each(function() {
        renderMathInElement(this, katexOptions)
    })

</script>

</body>
</html>