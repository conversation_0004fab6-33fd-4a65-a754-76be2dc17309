import logging
import uuid
import json
from django.conf import settings

from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.features.knowledge_analysis_query_db import KnowledgeQueryDBFeature 
from app.core.features.knowledge_analysis_query import KnowledgeQueryFactory
from app.core.features.knowledge_analysis_query_split import KnowledgeQuerySplitFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import KnowledgeNotResultError
from app.models import Message, Knowledge
from langchain.chains.llm import LLMChain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate

logger = logging.getLogger(__name__)


class KnowledgeAnalysisWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

    def run(self):
        query = self.application_generate_entity.query
        if not query:
            raise KnowledgeNotResultError(detail=settings.KNOWLEDGE_SEARCH_NO_RESULT)

        is_regenerate = self.inputs.get('is_regenerate','')

        # 如果是重新生成，则不需拆分知识点
        if is_regenerate:
            split_keywords = [query]
        else:
            # 查找是否存在该知识点
            if Knowledge.objects.filter(is_deleted=False, name=query).exists():
                split_keywords = [query]
            else:
                # 拆分用户搜索内容，提取知识点
                split_prompt = self.inputs.get('split_prompt','')
                split_keywords = KnowledgeQuerySplitFeature().run(
                    application_generate_entity=self.application_generate_entity,
                    query=query,
                    split_prompt=split_prompt,
                    tracing_log=self.tracing_log
                )
                # 处理知识点顺序
                split_keywords = self._sort_keywords(split_keywords, self.application_generate_entity.query)
                # 知识点最多获取3个
                split_keywords = split_keywords[:3]

        # 搜索关键词，得出已存储在数据库的知识点信息和没有存储的知识点名称列表
        kg_list = self._query_knowledge_by_keywords(query, split_keywords)
        # 优化一下搜索逻辑，如果检索出来的知识点包含在用户的输入中，则当作优先知识点展示

        if not kg_list:
            raise KnowledgeNotResultError(detail=settings.KNOWLEDGE_SEARCH_NO_RESULT)

        self._query_knowledge_desc(kg_list)

    def _sort_keywords(self, keywords: list, query: str) -> list:
        exist_keywords_map = {}
        exist_keywords = []
        for k in keywords:
            k_idx = query.find(k)
            if k_idx > -1:
                exist_keywords_map[k] = k_idx
                exist_keywords.append(k)
            else:
                # 知识点不在问题内则不处理
                continue
        sorted_keywords = sorted(exist_keywords, key=lambda x: exist_keywords_map[x])
        return sorted_keywords

    def _query_knowledge_by_keywords(self, query: str, split_keywords: list):
        if not split_keywords:
            return []

        search_type = self.inputs.get('search_type')
        document_no = self.inputs.get('document_no')
        recommend_prompt = self.inputs.get('recommend_prompt','')
        exist_kgs_map = {}
        if search_type == 'local' and document_no:
            exist_kgs_map = KnowledgeQueryDBFeature().run(
                query=query,
                keywords=split_keywords,
                original_knowledge_document_no=document_no,
                tracing_log=self.tracing_log
            )

        kg_list = []
        for kw in split_keywords:
            if kw in exist_kgs_map:
                exist_kg = exist_kgs_map.get(kw)
                # 如果exist_kg为空，则表示搜索到本地知识点，但是与其他重复
                if exist_kg:
                    others = exist_kg.get('others', [])
                    if len(others)<3:  # 如果others知识点少于三个，生成相关知识点

                        others = self._generate_related_knowledge(kw, recommend_prompt)
                    kg_list.append({
                        'name': exist_kg.get('name'),
                        'definition': exist_kg.get('definition', ''),
                        'others': others,
                    })
            else:
                others = self._generate_related_knowledge(kw, recommend_prompt)  # 新知识点直接生成相关点
                kg_list.append({
                    'name': kw,
                    'definition': '',
                    'others': others,
                })

        return kg_list

    def _query_knowledge_desc(self, kg_list: list):
        kg_query_factory = KnowledgeQueryFactory()
        is_regenerate=self.inputs.get('is_regenerate')
        stream=self.inputs.get('is_stream')

        knowledge_list=[]

        for kg in kg_list:
            kg_name = kg.get('name')
            definition = kg.get('definition', '')
            others = kg.get('others', [])
            kg_hash = uuid.uuid4().hex
            regenerate=1

            kg_query_feature = kg_query_factory.get_knowledge_query(
                application_generate_entity=self.application_generate_entity,
                knowledge=kg_name,
                definition=definition,
                stream=stream
            )
            kg_generator = kg_query_feature.run()
            if not stream:
                tracing_info = {
                    'tracing_type': 'supervise_learn_stat',
                    'query': kg_name
                }
                self.tracing_log.add_tracing_by_llm_result(
                    tracing_type=tracing_info.get('tracing_type'),
                    query=tracing_info.get('query'),
                    llm_result=kg_generator
                )
                knowledge={"name": kg_name, "desc": kg_generator.message.content, "recommend": others}
                knowledge_list.append(knowledge)
                node_answer = kg_generator.message.content
                completion_tokens = kg_generator.usage.completion_tokens
                is_saved=kg_query_feature.check_desc_usable(completion_tokens) and kg_query_feature.check_record_number()<3
                if node_answer and is_saved:
                    kg_query_feature.update_knowledge_store(node_answer,completion_tokens,is_regenerate)
                if kg_query_feature.check_record_number()==3:
                    regenerate=0
                    kg_query_feature.update_record()

            else:
                # 输出知识点详情
                # pos=body时，type为空
                self._publish_workflow_custom_message(' ', workflow_chunk={
                    'id': kg_hash, 'pos': 'head', 'type': '',
                })
                # 填入知识点名称
                self._publish_workflow_custom_message(kg_name, workflow_chunk={
                    'id': kg_hash, 'pos': 'body', 'type': 'title',
                })

                if kg_query_feature.is_llm_generate:
                    if definition:
                        tracing_type = 'knowledge_analysis_query_local'
                    else:
                        tracing_type = 'knowledge_analysis_query_llm'
                    node_result = self._handle_node_generate_response(
                        generator=kg_generator,
                        workflow_chunk={
                            'id': kg_hash, 'pos': 'body', 'type': 'detail',
                        }, tracing_info={
                            'tracing_type': tracing_type,
                            'query': kg_name,
                        }
                    )
                    node_answer = node_result.message.content
                    completion_tokens = node_result.usage.completion_tokens
                    is_saved=kg_query_feature.check_desc_usable(completion_tokens) and kg_query_feature.check_record_number()<3
                    if node_answer and is_saved:
                        kg_query_feature.update_knowledge_store(node_answer,completion_tokens,is_regenerate)
                    if kg_query_feature.check_record_number()==3:
                        regenerate=0
                        kg_query_feature.update_record()
                else:
                    for kg_detail in kg_generator:
                        self._publish_workflow_custom_message(kg_detail, workflow_chunk={
                            'id': kg_hash, 'pos': 'body', 'type': 'detail',
                        })

                for other_kg_name in others:
                    self._publish_workflow_custom_message(other_kg_name, workflow_chunk={
                        'id': kg_hash, 'pos': 'body', 'type': 'recommend',
                    })

                # 消息是否可用：token是否超出
                is_usable_str = '1' if kg_query_feature.is_usable else '0'
                self._publish_workflow_custom_message(is_usable_str, workflow_chunk={
                    'id': kg_hash, 'pos': 'body', 'type': 'is_usable',
                })
                # 如果是模型生成，则可以重新生成
                regeneration_str = '1' if kg_query_feature.is_llm_generate else '0'
                if regenerate == 0:
                    regeneration_str = '0'
                self._publish_workflow_custom_message(regeneration_str, workflow_chunk={
                    'id': kg_hash, 'pos': 'body', 'type': 'regeneration',
                })

                # 知识点尾部信息
                self._publish_workflow_custom_message(' ', workflow_chunk={
                    'id': kg_hash, 'pos': 'tail', 'type': '',
                })

        self._publish_workflow_custom_message(json.dumps(knowledge_list, ensure_ascii=False))
        self._publish_workflow_end_message()


    def _generate_related_knowledge(self, knowledge_name: str, recommend_prompt_str: str) -> list:
        """生成3个相关知识点"""
        default_prompt_str = """你是一位熟悉考研心理学知识体系的知识点解析专家。现在给出一个心理学知识点“{knowledge}”，请你列出3个与其最相关、在心理学考研中常见的重要知识点，用字符串列表json格式输出，不需要解释说明。
例如：
若输入为“马斯洛需求层次”，请输出：["动机理论","成就动机","自我实现理论"]
"""

        try:
            prompt_str = recommend_prompt_str if recommend_prompt_str else default_prompt_str

            prompt_obj = PromptTemplate(
                input_variables=["knowledge"],
                template=prompt_str
            )
            llm = ChatOpenAI(
                openai_api_key=settings.DOUBAO_API_KEY,
                openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
                model_name="doubao-1.5-pro-32k-250115",
            )
            chain = LLMChain(llm=llm, prompt=prompt_obj)
            result = chain.run(knowledge=knowledge_name)
            related = json.loads(result)
            # 确保返回3个结果，不足则补空字符串
            return list(related[:3])
        except Exception as e:
            logger.exception(e)
            print(f"生成相关知识点失败: {str(e)}")
            return ["", "", ""]  # 返回3个空字符串保持格式一致
