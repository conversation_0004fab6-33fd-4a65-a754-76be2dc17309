import logging
import re
import time
from typing import Generator

from django.conf import settings

from app.constants.app import MessageStatus
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import (
    ChatAppGenerateEntity, MessageEndStreamResponse, MessageStreamResponse,
    ErrorStreamResponse, StreamResponse, ChatbotAppStreamResponse, TokenExceedResponse, CompletionAppGenerateEntity,
    ChatbotAppBlockingResponse, MessageThinkingStreamResponse, MessageThinkingEndStreamResponse
)
from app.core.entities.queue_entities import (
    QueueErrorEvent, QueueStopEvent, QueueMessageEndEvent, QueueLLMChunkEvent
)
from app.core.model_runtime.entities.llm_entities import LLMResult, LLMUsage, LLMWorkflowChunk
from app.core.prompt.entities import AssistantPromptMessage
from app.core.prompt.utils.prompt_message_util import PromptMessageUtil
from app.errors import ConversationTokenExceedError, AppSystemError
from app.models import Conversation, Message, RagMessage, MessageToolCall

logger = logging.getLogger(__name__)


class ChatResponse:
    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            queue_manager: AppQueueManager,
            conversation: Conversation,
            message: Message,
            stream: bool = True,
    ):
        self._application_generate_entity = application_generate_entity
        self._model_config = application_generate_entity.model_conf
        self._queue_manager = queue_manager
        self._conversation = conversation
        self._message = message
        self._stopped_by = ''
        self._stream = stream
        self._is_queue_end = False

        self._llm_result = LLMResult(
            model=self._model_config.model,
            prompt_messages=[],
            message=AssistantPromptMessage(content=""),
            usage=LLMUsage.empty_usage(),
            tool_calls_response=[]
        )

    def process(self) -> Generator | ChatbotAppBlockingResponse:
        # TODO 修改conversation.name
        generator = self._process_stream_response()
        if self._stream:
            return self._to_stream_response(generator)
        else:
            return self._to_blocking_response(generator)

    def _to_blocking_response(
            self,
            generator: Generator[StreamResponse, None, None]
    ) -> ChatbotAppBlockingResponse:
        # 消息重试还是返回旧消息id
        message_id = self._message.message_no
        if self._message.replaced_message_no:
            message_id = self._message.replaced_message_no

        for stream_response in generator:
            if isinstance(stream_response, TokenExceedResponse):
                raise ConversationTokenExceedError()
            elif isinstance(stream_response, ErrorStreamResponse):
                raise AppSystemError(detail=stream_response.err)
            elif isinstance(stream_response, MessageEndStreamResponse):
                response = ChatbotAppBlockingResponse(
                    data=ChatbotAppBlockingResponse.Data(
                        message_id=message_id,
                        conversation_id=self._conversation.conversation_no,
                        answer=self._llm_result.message.content,
                        usage=self._llm_result.usage.model_dump(),
                        is_success=self._llm_result.is_success,
                        is_sensitive=self._llm_result.is_sensitive,
                    ),
                )
                return response
            else:
                continue
        raise Exception("Queue listening stopped unexpectedly.")

    def _to_stream_response(
            self,
            generator: Generator[StreamResponse, None, None]
    ) -> Generator[ChatbotAppStreamResponse, None, None]:
        # 消息重试还是返回旧消息id
        message_id = self._message.message_no
        if self._message.replaced_message_no:
            message_id = self._message.replaced_message_no

        for response in generator:
            yield ChatbotAppStreamResponse(
                conversation_id=self._conversation.conversation_no,
                message_id=message_id,
                stream_response=response
            )

    def _process_stream_response(self) -> Generator:
        has_thinking = False
        thinking_finished = False

        try:
            for message in self._queue_manager.listen():
                event = message.event

                if isinstance(event, QueueErrorEvent):
                    err_msg = answer_msg = str(event.error)
                    if event.error_type == QueueErrorEvent.ErrorType.LLM_REQUEST_ERROR:
                        answer_msg = settings.LLM_NETWORK_ERROR_ANSWER

                    self._llm_result.is_success = False
                    self._llm_result.message.content = answer_msg

                    self._handle_error(event.error_type, err_msg)

                    self._is_queue_end = True
                    # 除了知识解析和视频脚本，其他的异常当作正常message输出
                    if event.error_type == QueueErrorEvent.ErrorType.CONVERSATION_TOKEN_EXCEED:
                        yield self._error_to_stream_response(event.error_type, answer_msg)
                    else:
                        output_error_apps = ['knowledge_query', 'video_script', 'knowledge_analysis_simple']
                        if self._application_generate_entity.app_type in output_error_apps:
                            yield self._error_to_stream_response(event.error_type, answer_msg)
                        else:
                            yield self._message_to_stream_response(answer_msg)
                            yield self._message_end_to_stream_response()
                elif isinstance(event, QueueStopEvent | QueueMessageEndEvent):
                    if isinstance(event, QueueMessageEndEvent):
                        # TODO 临时特殊处理视频脚本，去除返回内容的---
                        if self._message.app.app_no == 'video_script':
                            content_ = re.sub(r'\n---+\n', '', event.llm_result.message.content)
                            event.llm_result.message.content = content_

                        self._llm_result = event.llm_result
                    else:
                        # 中止时，如果有思考过程，需要发送thinking_end命令
                        if self._llm_result.message.reasoning_content:
                            yield self._message_thinking_end_to_stream_response()

                        self._handle_stop(event)

                    self._save_message()
                    self._is_queue_end = True
                    yield self._message_end_to_stream_response()
                elif isinstance(event, QueueLLMChunkEvent):
                    chunk = event.chunk
                    delta_reasoning_content = chunk.delta.message.reasoning_content
                    delta_text = chunk.delta.message.content
                    if not delta_reasoning_content and delta_text is None:
                        continue

                    if delta_reasoning_content:
                        has_thinking = True
                        self._llm_result.message.reasoning_content += delta_reasoning_content
                        yield self._message_thinking_to_stream_response(delta_reasoning_content)
                        continue

                    if delta_text:
                        if has_thinking and not thinking_finished:
                            # 表示有思考过程，需要发送thinking_end
                            thinking_finished = True
                            yield self._message_thinking_end_to_stream_response()

                    if not self._llm_result.prompt_messages:
                        self._llm_result.prompt_messages = chunk.prompt_messages
                    if not self._llm_result.tool_calls_response:
                        self._llm_result.tool_calls_response = chunk.delta.tool_calls_response

                    self._llm_result.usage = chunk.delta.usage
                    self._llm_result.message.content += delta_text

                    yield self._message_to_stream_response(delta_text, chunk.workflow_chunk)
                else:
                    continue
        except (BrokenPipeError, ConnectionResetError, GeneratorExit):
            logger.warning('⚠️message_queue_connect_reset')
            # 客户端断开；清理资源（如关闭数据库连接、取消订阅）
            # 判断该队列是否已经结束
            if not self._is_queue_end:
                event = QueueStopEvent(stopped_by='system_exit')
                self._handle_stop(event)
                self._save_message()

    def _error_to_stream_response(self, err_type: QueueErrorEvent.ErrorType, err_reason: str) -> ErrorStreamResponse:
        if err_type == QueueErrorEvent.ErrorType.CONVERSATION_TOKEN_EXCEED:
            return TokenExceedResponse(
                created_at=int(time.time()),
                err=err_reason
            )

        return ErrorStreamResponse(
            created_at=int(time.time()),
            err=err_reason
        )

    def _message_thinking_to_stream_response(
            self,
            reasoning_content: str,
    ) -> MessageThinkingStreamResponse:
        return MessageThinkingStreamResponse(
            created_at=int(time.time()),
            reasoning_content=reasoning_content,
        )

    def _message_thinking_end_to_stream_response(
            self,
            stop_by: str = 'normal',
    ) -> MessageThinkingEndStreamResponse:
        return MessageThinkingEndStreamResponse(
            created_at=int(time.time()),
            stop_by=stop_by
        )

    def _message_to_stream_response(
            self,
            answer: str,
            workflow_chunk: LLMWorkflowChunk | None = None
    ) -> MessageStreamResponse:
        if not self._llm_result.is_success:
            workflow_chunk = LLMWorkflowChunk(type='exception')

        return MessageStreamResponse(
            created_at=int(time.time()),
            answer=answer,
            workflow_chunk=workflow_chunk.model_dump() if workflow_chunk else {}
        )

    def _message_end_to_stream_response(self) -> MessageEndStreamResponse:
        return MessageEndStreamResponse(
            created_at=int(time.time()),
            metadata={
                'message_tokens': self._llm_result.usage.prompt_tokens,
                'answer_tokens': self._llm_result.usage.completion_tokens,
                'total_tokens': self._llm_result.usage.total_tokens,
                'latency': self._llm_result.usage.latency,
            }
        )

    def _save_message(self) -> None:
        self._message.refresh_from_db()

        if self._llm_result.is_answer_token_exceed:
            self._message.is_answer_token_exceed = True

        self._message.status = MessageStatus.NORMAL.value
        self._message.stopped_by = self._stopped_by
        self.__save_message()

        if self._llm_result.tool_calls_response:
            for tool_call in self._llm_result.tool_calls_response:
                MessageToolCall.objects.create(
                    message=self._message,
                    tool_call_id=tool_call.tool_call_id,
                    name=tool_call.name,
                    arguments=tool_call.arguments,
                    content=tool_call.content,
                )

        if self._conversation.dataset:
            rag_message: RagMessage = self._message.rag_message
            if rag_message:
                rag_message.direct_content = self._message.message
                rag_message.direct_answer = self._message.answer
                rag_message.direct_message_tokens = self._message.message_tokens
                rag_message.direct_answer_tokens = self._message.answer_tokens
                rag_message.direct_latency = self._message.response_latency
                rag_message.save()

    def _handle_stop(self, event: QueueStopEvent):
        self._stopped_by = event.stopped_by.value

    def _handle_error(self, error_type: QueueErrorEvent.ErrorType, err_msg) -> None:
        self._message.refresh_from_db()
        self._message.status = MessageStatus.ERROR.value
        self._message.error = err_msg
        self._message.exception_reason = err_msg
        self._message.is_exception = True

        if error_type == QueueErrorEvent.ErrorType.CONVERSATION_TOKEN_EXCEED:
            self._message.exception_reason = '会话token达到上限'
        elif error_type == QueueErrorEvent.ErrorType.CONTAIN_SENSITIVE:
            self._message.is_sensitive = True
            self._message.exception_reason = '包含敏感词'
        elif error_type == QueueErrorEvent.ErrorType.SPECIAL_ERROR:
            self._message.is_exception = False
            self._message.exception_reason = ''

        self.__save_message()

    def __save_message(self):
        self._conversation.save()

        self._message.message = PromptMessageUtil.prompt_messages_to_prompt_for_saving(self._llm_result.prompt_messages)
        self._message.answer = self._llm_result.message.content
        self._message.message_tokens = self._llm_result.usage.prompt_tokens
        self._message.answer_tokens = self._llm_result.usage.completion_tokens
        self._message.total_tokens = self._llm_result.usage.total_tokens
        self._message.response_latency = self._llm_result.usage.latency
        self._message.is_sensitive = self._llm_result.is_sensitive

        # 检测到超纲攻击！判断为异常
        if '超纲攻击' in self._message.answer:
            self._message.status = MessageStatus.ERROR.value
            self._message.is_exception = True

        self._message.save()
