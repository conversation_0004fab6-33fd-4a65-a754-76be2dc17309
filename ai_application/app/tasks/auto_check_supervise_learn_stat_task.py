import datetime
import logging

from celery import shared_task
from django.utils import timezone

from app.models import SuperviseLearnStat
from app.services import supervise_learn_status_service

logger = logging.getLogger(__name__)


@shared_task(queue='learn_status_check')
def auto_check_supervise_learn_stat_gen():
    # 预发布环境压测先不处理
    # if settings.ENVIRONMENT == settings.ENV_PREVIEW:
    #     return

    qs = SuperviseLearnStat.objects.filter(is_deleted=False, status='NOT_START')[:10]
    for record in qs:
        check_supervise_learn_stat_gen(record)

    # 10分钟后未处理则修改为失败
    time_point = timezone.now() - datetime.timedelta(minutes=10)
    SuperviseLearnStat.objects.filter(
        is_deleted=False, status='ING', add_time__lt=time_point
    ).update(status='FAIL', fail_reason='超时')


@shared_task(queue='learn_status_check')
def delay_supervise_learn_stat_gen(record_id):
    logger.info(f"start_delay_supervise_learn_stat_gen: {record_id}")
    try:
        record = SuperviseLearnStat.objects.get(id=record_id)
        logger.info(f"Processing record: {record.id}")
        
        # 添加调试日志
        logger.info(f"Calling gen_analysis_report with record: {record}")
        result = supervise_learn_status_service.SuperviseLearnStatusService.gen_analysis_report(record)
        logger.info(f"Successfully processed record: {record.id}")
        return result
        
    except Exception as e:
        # 记录完整的异常堆栈
        logger.error(f"Error processing record_id {record_id}", exc_info=True)
        logger.error(f"Record details: {record.__dict__ if 'record' in locals() else 'Record not loaded'}")
        raise


def check_supervise_learn_stat_gen(record: SuperviseLearnStat):
    logger.info(f"pre_start_delay_supervise_learn_stat_gen: {record.id}")
    record.status = 'ING'
    record.save(update_fields=['status'])
    delay_supervise_learn_stat_gen.delay(record.id)
