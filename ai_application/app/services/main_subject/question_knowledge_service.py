import json

from app.api.dto import QuestionKnowledgeExtractDto, ChatMessageDto
from app.models import App, QuestionKnowledge, Account, QuestionKnowledgeMap, KnowledgeLibrary
from app.services.app_generate_service import AppGenerateService


class QuestionKnowledgeService:

    @classmethod
    def extract_question_knowledge(cls, dto: QuestionKnowledgeExtractDto):
        app_model: App = App.objects.filter(
            is_deleted=False, app_type='question_knowledge_extract').order_by('-id').first()
        if not app_model:
            raise ValueError('app_model is not exist')

        obj: QuestionKnowledge = QuestionKnowledge.objects.filter(
            is_deleted=False, question_id=dto.question_id).first()
        if obj and obj.is_extract_knowledge:
            return True, obj.knowledge_list

        if not obj:
            obj = QuestionKnowledge.objects.create(
                question_id=dto.question_id,
                subject=dto.subject
            )

        chat_dto = ChatMessageDto(
            app_id=app_model.app_no,
            query=dto.question_id,
            inputs={
                'subject': dto.subject,
            },
            stream=False
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account)
        try:
            knowledge_list = json.loads(response.get('answer'))
            is_success = True
            # 特殊处理错误回答的
            if not (isinstance(knowledge_list, list) and len(knowledge_list) > 0):
                knowledge_list = []
                is_success = False
        except:
            knowledge_list = []
            is_success = False

        if is_success:
            obj.knowledge_list = knowledge_list
            obj.is_extract_knowledge = True
            obj.save(update_fields=['knowledge_list', 'is_extract_knowledge'])

            #  批量创建question_map
            origin_knowledge_list = []
            for item in knowledge_list:
                obj = KnowledgeLibrary.objects.filter(
                    is_deleted=False, main_subject=item['subject'], name=item['knowledge']).first()
                if obj:
                    origin_knowledge_list.append(obj)
            kg_pks = [i.id for i in origin_knowledge_list]
            q_m_objs = []
            for kg_pk in kg_pks:
                q_m_objs.append(QuestionKnowledgeMap(
                    question_id=dto.question_id, knowledge_id=kg_pk
                ))
            QuestionKnowledgeMap.objects.bulk_create(q_m_objs)

        return is_success, knowledge_list
