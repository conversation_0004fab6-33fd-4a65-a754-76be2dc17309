from celery import shared_task

from app.models import STUserPaperAnswer, STUserPaperQuestionAnswer
from app.services.shuati_app.answer_report import (
    gen_answer_report, gen_subjective_answer_report, batch_after_paper_submit
)


@shared_task(queue='message_task')
def delay_after_paper_submit(paper_answer_id):
    paper_answer: STUserPaperAnswer = STUserPaperAnswer.objects.filter(
        id=paper_answer_id, report_status='not_start'
    ).first()
    if not paper_answer:
        return

    batch_after_paper_submit(paper_answer)


@shared_task(queue='message_task')
def delay_gen_answer_report(paper_answer_id):
    paper_answer: STUserPaperAnswer = STUserPaperAnswer.objects.filter(
        id=paper_answer_id, report_status='not_start'
    ).first()
    if not paper_answer:
        return
    gen_answer_report(paper_answer)


@shared_task(queue='message_task')
def auto_check_answer_report_task():
    qs = STUserPaperAnswer.objects.filter(is_deleted=False, report_status='fail', report_retry_count__lt=3)[:10]
    for answer in qs:
        answer.report_status = 'not_start'
        answer.report_retry_count += 1
        answer.save(update_fields=['report_status', 'report_retry_count'])

        delay_gen_answer_report.delay(answer.id)


@shared_task(queue='message_task')
def delay_gen_subjective_answer_report(question_answer_id):
    question_answer: STUserPaperQuestionAnswer = STUserPaperQuestionAnswer.objects.filter(
        id=question_answer_id, report_status='not_start').first()
    if not question_answer:
        return

    gen_subjective_answer_report(question_answer)


@shared_task(queue='message_task')
def auto_check_subjective_answer_report_task():
    qs = STUserPaperQuestionAnswer.objects.filter(is_deleted=False, report_status='fail', report_retry_count__lt=3)[:10]
    for answer in qs:
        answer.report_status = 'not_start'
        answer.report_retry_count += 1
        answer.save(update_fields=['report_status', 'report_retry_count'])

        delay_gen_subjective_answer_report.delay(answer.id)
