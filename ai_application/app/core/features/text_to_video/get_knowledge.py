import json
from langchain.chains.llm import LLMChain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from django.conf import settings

def get_knowledge_list(query):
    # 初始化 LLM
    llm = ChatOpenAI(
        openai_api_key=settings.DOUBAO_API_KEY,
        openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
        model_name="doubao-1.5-pro-32k-250115",
    )

    judge_prompt = """
    你是一位精通考研数据结构的知识点提取专家。请从以下提问内容中，准确提取出所有涉及的考研数据结构知识点：

    {query}
            
    请严格遵守以下规则：
        1.	仅提取考研数据结构的知识点，不要包含非数据结构知识点（如“现在进行时”、“游戏”）、泛学科名（如“操作系统”）。
        2.	若用户输入中存在错别字或拼写错误的知识点，请自动纠正后再提取。
        3.	只能提取提问文本中连续出现的内容，不得擅自生成或补充未出现的知识点。
        4.	输出的知识点必须为具体的专业术语，而非章节、学科名或主题范围。
        5.	请按如下格式输出，不要添加任何解释说明：

                ["知识点1","知识点2",...]

    示例：
    输入：请简述一下迪杰斯特拉算法和弗洛伊德算法的异同。
    输出：[“迪杰斯特拉算法”, “弗洛伊德算法”]
    """
    prompt_obj = PromptTemplate(
        input_variables=["query"],
        template=judge_prompt.strip()
    )
    chain = LLMChain(llm=llm, prompt=prompt_obj)
    result = chain.run(
        query=query
    )
    return json.loads(result)

