import time

from celery import shared_task

from app.models import KnowledgeLibrary
from app.tasks.auto_check_llm_request_task import _direct_query_llm


def fill_data_knowledge_content(subject_name, main_subject_name):
    qs = KnowledgeLibrary.objects.filter(
        is_deleted=False, content__isnull=True,
        subject_domain__subject_name=subject_name,
        subject_domain__main_subject_name=main_subject_name,
        status='NOT_START',
        nature='major',
    )

    full_subject_name = f'{subject_name}_{main_subject_name}'
    for i in qs:
        i.status = 'ING'
        i.save(update_fields=['status'])
        print('🚀======', i.id, i.name)

        pre_prompt = """以下[说明]中是对【{{knowledge}}】知识点的阐述，根据{{full_subject_name}}研究生入学考试的相关教材要求，提供对这个知识点的更全面理解。输出要求如下：
- 不要重复输出定义
- 深入理解：根据上述定义和{{full_subject_name}}研究生入学考试要求，生成辅助理解知识点的内容。要求所生成的内容通俗易懂。
[文本]
{{knowledge}}
[说明]
{{definition}}"""

        pre_prompt = (pre_prompt.replace('{{knowledge}}', i.name)
                      .replace('{{definition}}', i.desc)
                      .replace('{{full_subject_name}}', full_subject_name))
        res = _direct_query_llm(
            model_provider='tongyi',
            model_id='qwen-turbo',
            model_params={"max_tokens": 1500, "temperature": 0.2},
            pre_prompt=pre_prompt,
        )
        i.content = res.message.content
        i.status = 'SUCCESS'
        i.save()
        time.sleep(0.5)


def delay_gen_knowledge_detail(subject_name, main_subject_name):
    fill_data_knowledge_content(subject_name, main_subject_name)

