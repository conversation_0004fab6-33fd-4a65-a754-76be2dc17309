import json
import time
from app.api.college_analysis.target_college_information import get_target_college_information
from app.api.dto import ModelManagementMessageDto
from app.errors import ParameterError
from app.models import KaoYanStudentInfo, KaoYanTeacherBook, KaoYanCourse, Account
from app.services.model_management_generate import ModelManagementGenerateService


class KaoYanReviewReportService:
    @classmethod
    def run(cls,inputs,app_model,from_account: Account):

        report_id = inputs.get('report_id')
        stream = inputs.get('stream')
        if not report_id:
            raise ParameterError(detail='缺少 report_id')

        target_college_level = inputs.get('target_college_level', '')
        start_review_time = inputs.get('start_review_time', '')
        final_start_review_time = f"开始复习时间为:{start_review_time}"

        # 通过user_id筛选数据库获取用户基本信息
        student_info_record = KaoYanStudentInfo.objects.filter(report_id=report_id).first()
        if not student_info_record:
            raise ParameterError(detail='未找到对应 user_id 的学生信息')

        student_info_dict = student_info_record.student_info
        intensive_choice = student_info_record.intensive_choice
        steady_choice = student_info_record.steady_choice
        basic_info = student_info_dict.get('基本信息', {})
        academic_background = student_info_dict.get('成绩背景', {})
        major_ranking = academic_background.get('专业排名', '')
        gpa_range = academic_background.get('总GPA区间', '')
        ability_assessment = student_info_dict.get('能力评估', {})
        preparation_status = student_info_dict.get('备考状态', {})
        daily_study_time = preparation_status.get('日均学习时间', '')

        final_student_info = {
            'ability_assessment': ability_assessment,
            'basic_info': basic_info,
            'major_ranking': major_ranking,
            'gpa_range': gpa_range,
            'daily_study_time': daily_study_time,
            'start_review_time': final_start_review_time
        }

        def parse_choices(choices):
            parsed_choices = []
            if choices:
                if isinstance(choices, str):
                    try:
                        choices_list = json.loads(choices)
                    except json.JSONDecodeError:
                        raise ParameterError(detail=f'choices 字段格式错误: {choices}')
                elif isinstance(choices, list):
                    choices_list = choices
                else:
                    raise ParameterError(detail=f'choices 字段格式错误: {choices}')

                for choice in choices_list:
                    major_name_with_code = choice.get('专业名')
                    college_name_with_code = choice.get('院校名')

                    # 提取专业代码
                    major_code = major_name_with_code.split('(')[-1].strip(')')

                    # 提取院校代码
                    college_code = college_name_with_code.split('(')[-1].strip(')')

                    # 将结果添加到列表中
                    parsed_choices.append({
                        'major_code': major_code,
                        'college_code': college_code
                    })
            return parsed_choices

        if target_college_level == 'intensive':
            parsed_intensive_choices = parse_choices(intensive_choice)
            target_college_information = get_target_college_information(parsed_intensive_choices)

        else:
            parsed_steady_choices = parse_choices(steady_choice)
            target_college_information = get_target_college_information(parsed_steady_choices)

        # 增加考研课程信息和老师，考研书籍信息
        teacher_book_information = list(KaoYanTeacherBook.objects.all().values(
            'teacher_name',
            'book_name',
            'teacher_profile'
        ))

        course_information = list(KaoYanCourse.objects.values_list('name', flat=True))

        student_info_str = json.dumps(final_student_info, ensure_ascii=False)
        target_college_information_str = json.dumps(target_college_information, ensure_ascii=False)
        course_information_str = json.dumps(course_information, ensure_ascii=False)
        teacher_book_information_str = json.dumps(teacher_book_information, ensure_ascii=False)

        inputs = {
            "target_college_information": target_college_information_str,
            "course_information": course_information_str,
            "teacher_book_information": teacher_book_information_str
        }

        model_chat_dto = ModelManagementMessageDto(
            user_id=4,
            app_id='kaoyan_review_plan',
            query=student_info_str,
            image=[],
            stream=stream,
            inputs=inputs,
            is_async=False,
        )

        authentication_info = {
            "timestamp": str(int(time.time())),
            "api_key": "ak_f246e7826901419a",
            "signature": "5ce9db4eb0bed34b299af779c4f652c641f47cd7e369e31f857e939bcde9d7c6",
            "nonce": "c2lwif7y"
        }

        # 调模型管理api接口
        response = ModelManagementGenerateService.generate(model_chat_dto,authentication_info,app_model,from_account)

        return response