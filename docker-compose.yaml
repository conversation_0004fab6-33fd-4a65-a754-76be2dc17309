services:
  ai-application:
    container_name: ai-application
    restart: always
    image: ${image}
    volumes:
      - /opt/config/ai-app/config-ai.py:/opt/ai_application/ai_application/config.py
    expose:
      - 8002:8000
    entrypoint: ["/bin/sh","-c","gunicorn --workers=2 --threads=4 --keep-alive=10 ai_application.wsgi:application -b :8000 --log-level INFO --log-config ./logging.conf"]

  ai-application-celery:
    container_name: ai-application-celery
    restart: always
    image: ${image}
    volumes:
      - /opt/config/ai-app/config-ai.py:/opt/ai_application/ai_application/config.py
    entrypoint: ["/bin/sh","-c","celery -A ai_application worker -B -l DEBUG -f ./log/all.log -Q dataset -n ai_application_celery"]
