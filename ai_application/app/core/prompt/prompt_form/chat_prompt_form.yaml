parameter_rules:
  - name: role
    label: 角色
    type: text_multirow
    max_length: 200
    default: '你是 XXX 领域的专家  (例如：Python编程语言专家)'
    help: 指定大模型的角色。
    placeholder: 请输入角色内容，不超过200个字符
  - name: task
    label: 任务
    type: text_multirow
    max_length: 300
    default: '1. 分解问题，提炼出问题的知识点；\n2. 解释每个知识点的含义；\n3. 根据问题，综合各个知识点内容详细回答问题，并且给出 XXX 示例。'
    help: 对任务背景、目标、或想要探讨的主题的描述。
    placeholder: 请输入任务内容，不超过300个字符
  - name: target
    label: 目标受众
    type: text_multirow
    default: '- 对编程感兴趣但从未接触过编程的人士。\n- 希望通过学习 Python 提升自身技能的学生或在职人士。\n- 打算转行至IT行业的职场人。'
    max_length: 300
    help: 明确对话对象或者读者，比如特定人群等。
    placeholder: 请输入目标受众内容，不超过300个字符
  - name: instructions
    label: 说明
    type: text_multirow
    max_length: 300
    default: '首先要判断问题是否属于 XXX 领域范畴，如果不属于则返回信息：此问题不属于  XXX 领域范畴，暂不回答。'
    help: 提供背景信息、已知数据、讨论总结或其他上下文以帮助理解对话内容。
    placeholder: 请输入说明内容，不超过300个字符
  - name: note
    label: 注意
    type: text_multirow
    max_length: 300
    default: '1.仅回答与 XXX 领域有关的问题。\n2. 如果是其他问题，仅仅回复：此问题不属于 XXX 领域范畴，暂不回答。不要再回复其他信息。'
    help: 说明回复中特别注意的事项、要求、约束等。
    placeholder: 请输入注意内容，不超过300个字符
  - name: tone
    label: 情感基调
    type: text_multirow
    max_length: 300
    default: ''
    help: 指定对话应采用的情感色彩或语气。
    placeholder: 请输入情感基调内容，不超过300个字符
  - name: examples
    label: 示例
    type: text_multirow
    max_length: 300
    default: ''
    help: 问题和回复内容的举例。
    placeholder: 请输入示例内容，不超过300个字符
  - name: output
    label: 输出
    type: text_multirow
    max_length: 300
    default: '输出格式约束\n根据以下内容输出'
    help: 说明回复结果的式样和要求，比如“以列表形式输出”。
    placeholder: 请输入输出内容，不超过300个字符
  - name: closings
    label: 结束语
    type: text_multirow
    max_length: 300
    default: '若对以上回复不满意，请重新描述问题，再次提问。'
    help: 提示词末尾重复关键指令或者要求。
    placeholder: 请输入结束语内容，不超过300个字符
