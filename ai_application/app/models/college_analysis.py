from django.db import models
from ._base import BaseModel


class CollegeInfo(BaseModel):

    discipline_category_code = models.CharField(max_length=10, verbose_name='学科门类编号')
    discipline_category = models.CharField(max_length=255, verbose_name='学科门类')
    primary_major_code = models.CharField(max_length=10, verbose_name='一级专业编号')
    primary_major = models.CharField(max_length=255, verbose_name='一级专业')
    secondary_major_code = models.CharField(max_length=10, verbose_name='二级专业编号')
    secondary_major = models.CharField(max_length=255, verbose_name='二级专业')
    suitable_population = models.TextField(verbose_name='适合人群')
    description = models.TextField(verbose_name='说明')
    employment_direction = models.TextField(verbose_name='就业方向')
    common_examination_major = models.TextField(verbose_name='常考专业')
    recommended_colleges = models.TextField(verbose_name='院校推荐')

    class Meta:
        verbose_name = verbose_name_plural = '院校资料'
        db_table = 'ai_college_info'



class KaoYanStudentInfo(BaseModel):
    report_id = models.CharField(verbose_name='报告id',max_length=255,null=True,blank=True)
    student_info = models.JSONField(verbose_name='学生信息',null=True,blank=True)
    college_analysis = models.JSONField(verbose_name='院校分析结果',null=True,blank=True)
    intensive_choice = models.JSONField(verbose_name='冲刺档',null=True,blank=True)
    steady_choice = models.JSONField(verbose_name='稳妥档',null=True,blank=True)
    safety_choice = models.JSONField(verbose_name='保底档',null=True,blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '考研学生信息'
        db_table = 'ai_kao_yan_student_info'

class KaoYanTeacherBook(BaseModel):
    teacher_name = models.CharField(max_length=255, verbose_name='老师姓名')
    book_name = models.CharField(max_length=255, verbose_name='书籍名称')
    teacher_profile = models.TextField(verbose_name='个人简介')

    class Meta:
        verbose_name = verbose_name_plural = '考研老师_书籍'
        db_table = 'ai_kao_yan_teacher_book'


class KaoYanCourse(BaseModel):

    name = models.CharField(max_length=255, verbose_name='课程名称')


    class Meta:
        verbose_name = verbose_name_plural = '考研课程'
        db_table = 'ai_kao_yan_course'


class KaoYanQueryResult(BaseModel):
    undergraduate_secondary_major = models.CharField(
        max_length=255,
        verbose_name='本科二级专业名',
        null=True,
        blank=True
    )
    related_college_majors = models.JSONField(
        verbose_name='关联度高的院校专业信息',
        null=True,
        blank=True,
    )

    class Meta:
        verbose_name = '关联度高的院校专业信息查询结果'
        db_table = 'ai_kao_yan_query_result'

