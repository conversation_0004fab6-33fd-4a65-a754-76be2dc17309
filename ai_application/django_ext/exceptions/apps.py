from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _
from django.conf import settings


class ExceptionsConfig(AppConfig):
    name = 'django_ext.exceptions'
    verbose_name = _("异常处理")

    def ready(self):
        if not hasattr(settings, 'REST_FRAMEWORK'):
            settings.REST_FRAMEWORK = {}

        settings.REST_FRAMEWORK.setdefault(
            'EXCEPTION_HANDLER',
            'django_ext.exceptions.handlers.rest_handler'
        )
