# Generated by Django 4.2 on 2025-04-10 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0040_merge_20250410_1437'),
    ]

    operations = [
        migrations.CreateModel(
            name='GaoshuUserQuestionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(max_length=255)),
                ('question', models.TextField(blank=True, null=True)),
                ('pic_url', models.TextField(blank=True, null=True)),
                ('knowledge_list', models.J<PERSON><PERSON>ield(default=list)),
            ],
            options={
                'verbose_name': '高数截屏答疑用户记录',
                'verbose_name_plural': '高数截屏答疑用户记录',
                'db_table': 'ai_gaoshu_screenshot_user_record',
            },
        ),
    ]
