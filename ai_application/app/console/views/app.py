import logging
import tempfile
from contextlib import suppress
from datetime import datetime, timedelta
from itertools import chain
import json
from typing import Any

from django.core.cache import cache
from django.conf import settings
from django.db import transaction
from django.db.models import QuerySet
from django.http import Http404
from django.http.response import JsonResponse
from django.shortcuts import render, redirect
from django.views.generic import TemplateView, DetailView, View
from django.utils import timezone
import requests

from api_client.data.vector_data_client import vector_data_client
from app.api.college_analysis.college_information import get_college_and_major_info
from app.api.college_analysis.huolande_test import huolande_test
from app.api.dto import ChatMessageDto, ProblemSolvingDto, ComplexSentenceAnalysisDto
from app.api.validators import ProblemSolvingValidator, ComplexSentenceAnalysisValidator, \
    ChapterNoteGeneratorValidator, LectureNoteGeneratorValidator
from app.api.dto import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ProblemSolvingDto, ComplexSentenceAnalysisDto
from app.api.waikan_reader_api.get_question_detail import get_question_detail
from app.console.services import extract_docx_content

from app.console.serializers import MessageListSerializer, UnifiedExaminationQuestionSerializer
from app.constants.app import AppMode, CHAT_MESSAGE_TYPES_MAP, OUTER_APP_TYPES, OUTER_APP_NOS
from app.core.entities.app_entities import ModelConfigEntity
from app.core.indexing_runner import IndexingRunner
from app.core.model_manager import ModelInstance
from app.core.model_provider_manager import ModelProviderManager
from app.core.model_runtime.entities.provider_entities import ModelType
from app.core.prompt.params_prompt import comb_prompt_by_params
from app.core.prompt.simple_prompt_transform import SimplePromptTransform
from app.core.rag.embedding.cached_embedding import CacheEmbedding
from app.core.rag.models.document import Document
from app.core.rag.splitter.semantic_splitter import SemanticSplitter
from app.libs.baidu_ocr import BaiduOcr
from app.models import PromptTemplate, Account, App, Conversation, Message, MessageTracing, PromptTemplateChangeLog, \
    AppModelConfig, CourseNoteTask, CourseNoteTaskChangeDetail, CourseNoteTaskDebug, STUserPaperAnswer, STQuestion

from app.models.document_proofreader import DocumentProofreader, DocumentProofreaderError
from app.models import UnifiedExaminationQuestion as Ueq
from app.models import EnglishWaikanTestRecord, EnglishWordTestAnswerDetail, EnWordReciteBasicAnswerDetail
from app.models import SuperviseLearnStat
from app.services.app_generate_service import AppGenerateService
from app.services.course_note.course_note_service import CourseNoteService
from app.services.course_note.debug_course_note_service import DebugCourseNoteService
from app.services.course_note.utils import get_chapter_names
from app.services.document_proofreader import convert_markdown_to_docx, WordParser
from app.services.document_proofreader.document_proofreader_fun import proofread_document_file
from app.services.document_summary_service import DocumentSummaryService
from app.services.prompt_optimize_service import PromptOptimizeService

from app.console.services import MessageStatsWaikanQuestion, MessageStatsEnWordTestQuestion
from app.console.services import MessageStatsEnWordReciteQuestion
from app.console.services import MessageStatsSuperviseLearnStat
from app.console.serializers import WaikanQuestionSerializer, EnWordTestSerializer, EnWordReciteSerializer
from app.console.serializers import SuperviseLearnStatSerializer
from django_ext import base_view
from django_ext.base_view import BaseView
from django_ext.paginator import queryset_paginate
from django_ext.response import make_response, make_stream_response
from helpers.upload_helper import upload_file
from django_ext.utils.date_utils import local2utc, utc2local, utc2str
import os
import uuid
from django.http import JsonResponse, FileResponse
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.views import APIView
from docx import Document
from helpers.upload_helper import upload_file
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse
import csv

from app.models.dayiapp import SubjectPrompt
# 导入流式处理视图
from ...services.college_analysis_new_service import CollegeAnalysisNewService


logger = logging.getLogger(__name__)


class CommonUploadImage(BaseView):

    def post(self, request, *args, **kwargs):
        image_file = request.data.get('image')
        if not image_file:
            raise Exception('请上传图片')

        try:
            image_url = upload_file(image_file, file_name=image_file.name, sub_path='console_images')
        except Exception as e:
            logger.exception(e)
            raise Exception('上传失败')

        return make_response({'url': image_url})


class SplitEstimateView(TemplateView):
    template_name = 'app/split_estimate.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class SplitEstimateSubmitView(BaseView):
    permission_classes = []

    def _split_by_markdown(self):
        request = self.request
        text = request.data['content']
        remove_hyperlinks = request.data.get('remove_hyperlinks')
        remove_images = request.data['remove_images']
        processing_rule = {
            'segmentation': {
                'chunk_size': request.data['chunk_size'],
                'chunk_overlap': request.data['chunk_overlap'],
                'separator': '\n',
            },
            'remove_hyperlinks': remove_hyperlinks,
            'remove_images': remove_images,
        }
        documents = IndexingRunner().run_markdown_estimate(
            text,
            processing_rule,
        )
        data = []
        for d in documents:
            title = d.metadata.get('h1')
            if not title:
                title = d.metadata.get('h2')
            if not title:
                title = d.metadata.get('h3')

            data.append({
                'title': title,
                'page_content': d.page_content,
                'doc_id': d.metadata['doc_id'],
                'char_len': len(d.page_content)
            })
        return data

    def _split_by_semantic(self):
        request = self.request
        text = request.data['content']
        buffer_size = request.data.get('buffer_size')
        breakpoint_threshold_type = request.data.get('breakpoint_threshold_type')
        breakpoint_threshold_amount = request.data.get('breakpoint_threshold_amount')
        sentence_split_symbol = request.data.get('sentence_split_symbol')
        if not sentence_split_symbol:
            sentence_split_symbol = '。？！\n'
        sentence_split_regex = rf'(?<=[{sentence_split_symbol}])\s*'

        model_manager = ModelProviderManager()
        embedding_model = model_manager.get_model_instance(
            provider='tongyi',
            model_type=ModelType.TEXT_EMBEDDING,
            model='text-embedding-v2'
        )
        embeddings = CacheEmbedding(embedding_model)

        splitter = SemanticSplitter(
            embeddings=embeddings,
            buffer_size=buffer_size,
            breakpoint_threshold_type=breakpoint_threshold_type,
            breakpoint_threshold_amount=breakpoint_threshold_amount,
            sentence_split_regex=sentence_split_regex,
        )

        text_docs = [Document(page_content=text)]
        documents = splitter.split_documents(text_docs)

        data = []
        for d in documents:
            data.append({
                'title': '',
                'page_content': d.page_content,
                'char_len': len(d.page_content)
            })
        return data

    def post(self, request, *args, **kwargs):
        splitter_method = request.data.get('splitter')
        if splitter_method == 'markdown':
            data = self._split_by_markdown()
        elif splitter_method == 'semantic':
            data = self._split_by_semantic()
        else:
            raise ValueError('Unknown splitter method')

        return make_response(data)


class DemoAppListView(TemplateView):
    template_name = 'app/demo_app_list.html'


class AppListView(TemplateView):
    template_name = 'app/app_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class MessageTracingView(BaseView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        message_id = request.query_params.get('message_id')
        if not message_id:
            return make_response({'tracing': []})

        tracing_qs = MessageTracing.objects.filter(
            message__message_no=message_id
        )
        tracing_logs = []
        total_latency = 0
        total_answer_tokens = 0
        for t in tracing_qs:
            tracing_logs.append(t.get_log_display())
            total_latency += t.latency
            total_answer_tokens += t.answer_tokens
        tracing_logs.append(f'总回答tokens: {total_answer_tokens}，全部耗时: {total_latency}')
        return make_response({
            'tracing': tracing_logs
        })


class MessageTracingDetailView(BaseView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        message_id = request.query_params.get('message_id')
        if not message_id:
            return make_response({'tracing': []})

        message: Message = Message.objects.filter(message_no=message_id).first()
        if not message:
            return make_response({'tracing': []})

        tracing_qs = MessageTracing.objects.filter(
            message__message_no=message_id
        ).order_by('id')

        tracing_logs = [t.get_log_info() for t in tracing_qs]
        if message.app.app_type == 'chat_app2':
            MALICIOUS_ATTACK_TYPE_MAP = {
                0: "无恶意意图",
                1: "系统越狱攻击",
                2: "越权请求检测",
                3: "角色扮演攻击",
                4: "间接提示词注入",
                5: "分段注入",
                6: "恶意代码注入"
            }

            new_tracing_logs = []
            for t in tracing_logs:
                if t['log_type_code'].endswith('_thinking'):
                    continue
                if t['log_type_code'] == 'chat2_answer' and not t['model_id']:
                    t['model_id'] = settings.DAYI_TEXT_MODEL
                new_tracing_logs.append(t)
                if t['log_type_code'] == 'chat2_scan_llm':
                    attack_type_code = int(t['output_content']) if t['output_content'].isdigit() else 0
                    attack_type_desc = MALICIOUS_ATTACK_TYPE_MAP.get(attack_type_code, "未知类型")
                    t['output_content'] = attack_type_desc
                if t['log_type_code'] == 'chat2_determine_subject':
                    res = SubjectPrompt.objects.filter(subject_id=t['output_content']).values('name').first()
                    t['output_content'] = res['name'] if res else ''
        else:
            new_tracing_logs = tracing_logs

        return make_response({
            'tracing': new_tracing_logs
        })


class DebugPromptChangeLogView(BaseView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        app_no = request.query_params.get('app_no')
        if not app_no:
            return make_response({'change_logs': []})

        prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no=app_no).first()
        if not prompt_template:
            return make_response({'change_logs': []})

        logs = prompt_template.prompttemplatechangelog_set.filter(is_deleted=False, is_debug=True).order_by('-id')
        data = [{
            'id': log.id,
            'add_time': utc2str(log.add_time),
        } for log in logs]
        return make_response({
            'change_logs': data
        })


class DebugPromptLogView(BaseView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        log_id = request.query_params.get('log_id')
        if not log_id:
            return make_response({'log': ''})

        log = PromptTemplateChangeLog.objects.filter(id=log_id).first()
        if not log:
            return make_response({'log': ''})
        return make_response({'log': log.prompt_content})


class AppEstimateView(TemplateView):
    template_name = 'app/app_estimate.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        app_no = kwargs['app_no']
        app: App = App.objects.get(app_no=app_no)
        prompt_template_ids = app.app_model_config.prompt_template_list

        t = PromptTemplate.objects.filter(id__in=prompt_template_ids)
        pt_list = [{
            'id': i.id,
            'name': i.name,
            'content': i.get_debug_prompt_content(),
            'special_variables': i.special_variable_list,
        } for i in t]
        context.update({
            'env': '生产' if settings.ENVIRONMENT == settings.ENV_PRODUCT else '测试',
            'pt_list': pt_list,
            'app_no': app.app_no,
            'app_name': app.name,
        })
        return context


class AppEstimateSubmitView(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        app_no = kwargs['app_no']

        template_id = request.data['template_id']
        template: PromptTemplate = PromptTemplate.objects.get(id=template_id)

        template.debug_prompt_content = request.data['prompt']
        template.save(update_fields=['debug_prompt_content'])

        query = ''
        special_variables = request.data.get('special_variables', {})
        if 'query' in special_variables:
            query = special_variables.get('query')

        if app_no == 'document_summary':
            query = DocumentSummaryService.remove_table_style(query)

        dto = ChatMessageDto(
            app_id=app_no,
            inputs={
                'max_tokens': request.data['max_tokens'],
                'temperature': request.data['temperature'],
                'top_p': 0.5,
                'prompt_template': template.id,
                'is_debug': 1
            },
            query=query,
            stream=False
        )

        account = Account.objects.first()
        response = AppGenerateService.generate(dto, account, invoke_from='console')

        return make_response({
            'answer': response['answer'],
            'usage': response['usage'],
        })


class AppPromptPublishView(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        template_id = request.data['template_id']
        template: PromptTemplate = PromptTemplate.objects.get(id=template_id)

        template.prompt_content = request.data['prompt']
        template.save(update_fields=['prompt_content'])

        return make_response()


class PromptEstimateSubmitView(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        model_provider = 'tongyi'
        model_id = 'qwen-plus'

        template_id = request.data['template_id']
        template: PromptTemplate = PromptTemplate.objects.filter(id=template_id).first()
        if template:
            template.debug_prompt_content = request.data['prompt']
            template.save(update_fields=['debug_prompt_content'])

        provider_model_bundle = ModelProviderManager().get_provider_model_bundle(
            provider=model_provider,
            model_type=ModelType.LLM
        )
        model_schema = provider_model_bundle.model_type_instance.get_model_schema(model_id)
        model_conf = ModelConfigEntity(
            provider=model_provider,
            model=model_id,
            model_schema=model_schema,
            provider_model_bundle=provider_model_bundle,
            model_params={
                'max_tokens': request.data['max_tokens'],
                'temperature': request.data['temperature'],
            },
        )

        query = DocumentSummaryService.remove_table_style(request.data.get('query'))
        prompt_messages = SimplePromptTransform().get_prompt(
            app_mode=AppMode.CHAT,
            pre_prompt=request.data['prompt'],
            query=query,
        )

        model_instance = ModelInstance(
            provider_model_bundle=model_conf.provider_model_bundle,
            model=model_conf.model
        )
        invoke_result = model_instance.invoke_llm(
            prompt_messages=prompt_messages,
            model_parameters=model_conf.model_params,
            stream=False
        )
        return make_response({
            'answer': invoke_result.message.content,
            'usage': invoke_result.usage.model_dump(),
            'char_len': len(invoke_result.message.content),
        })


class BaowenEstimateView(TemplateView):
    template_name = 'app/baowen_estimate.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        templates = PromptTemplate.objects.filter(
            is_deleted=False, app_no__in=['content_extraction', 'article_generation']
        ).order_by('id')
        templates = [{
            'id': str(t.id),
            'name': t.name,
            'content': t.debug_prompt_content if t.debug_prompt_content else t.prompt_content
        } for t in templates]
        context['templates'] = json.dumps(templates, ensure_ascii=False)
        return context


class PromptOptimizeView(TemplateView):
    template_name = 'app/prompt_optimize.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        app_no = 'prompt_optimize'
        app: App = App.objects.filter(app_no=app_no).first()

        return context


class PromptOptimizeSubmit1View(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        account = Account.objects.first()
        response = PromptOptimizeService.get_optimized_prompt(request.data, account, invoke_from='console')
        conversation = Conversation.objects.get(conversation_no=response.get('conversation_id'))

        prompt_str = response.get('answer', '')
        prompt_dict = PromptOptimizeService.parse_instructions(prompt_str)

        return make_response({
            'pre_prompt': conversation.pre_prompt,
            'prompt_str': prompt_str,
            'prompt_dict': prompt_dict
        })


class PromptOptimizeSubmit2View(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        pre_prompt = comb_prompt_by_params(request.data)

        dto = ChatMessageDto(
            app_id='app_d5e981a1d4294214',
            conversation_id=request.data.get('conversation_id') or '',
            pre_prompt=pre_prompt,
            query=request.data.get('query'),
            stream=False
        )

        account = Account.objects.first()
        response = AppGenerateService.generate(dto, account, invoke_from='console')

        return make_response({
            'conversation_id': response['conversation_id'],
            'answer': response['answer'],
            'usage': response['usage'],
        })


class DocumentSummaryView(TemplateView):
    template_name = 'app/document_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        style = kwargs.get('style', 'sale')
        app_no = 'document_summary_sale' if style == 'sale' else 'document_summary_edu'

        t2 = PromptTemplate.objects.get(app_no='content_extraction')
        t = PromptTemplate.objects.get(app_no=app_no)
        context.update({
            'style_name': '销售' if style == 'sale' else '教务',
            'prompt_template_id2': t2.id,
            'prompt_template_id': t.id,
            'debug_prompt_content': t.debug_prompt_content if t.debug_prompt_content else t.prompt_content,
            'debug_prompt_content2': t2.debug_prompt_content if t2.debug_prompt_content else t2.prompt_content,
        })
        return context


class DocumentSummaryUpload(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        file = request.data.get('file')
        if not file:
            raise ValueError('file is required')
        extracted_content, content_blocks = extract_docx_content(file)
        return make_response({
            'content_blocks': content_blocks
        })


class DocumentSummarySubmitView(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        template_id = request.data['template_id']
        template: PromptTemplate = PromptTemplate.objects.filter(id=template_id).first()
        if template:
            template.debug_prompt_content = request.data['prompt']
            template.save(update_fields=['debug_prompt_content'])

        account = Account.objects.first()
        query = DocumentSummaryService.remove_table_style(request.data.get('query'))

        dto = ChatMessageDto(
            app_id='document_summary',
            inputs={
                'max_tokens': request.data['max_tokens'],
                'temperature': request.data['temperature'],
                'top_p': 0.5,
                'prompt_template': template.id,
                'is_debug': 1
            },
            query=query,
            stream=False
        )

        response = AppGenerateService.generate(dto, account, invoke_from='console')

        return make_response({
            'answer': response['answer'],
            'usage': response['usage'],
            'char_len': len(response['answer']),
        })

def fetch_waikan_question(request):
    data = get_question_detail()
    if data:
        return JsonResponse({'data': data})
    else:
        return JsonResponse({'error': 'Response does not contain data field or request failed'}, status=400)


class UpdateDebugPromptView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        app_no = request.data.get('app_no')
        prompt_content = request.data.get('prompt_content')
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no=app_no).first()
        if not prompt_template:
            raise ValueError('提示词模板不存在')

        prompt_template.update_debug_content(app_no, prompt_content)


class ChatAppSubmitView(base_view.BaseView):
    validator_class = ProblemSolvingValidator

    def get_user_question(self):
        image_file = self.request.data.get('image')
        if not image_file:
            return ''
        image_data = image_file.read()
        res = BaiduOcr().basic_accurate(image_file=image_data)
        words = [i['words'] for i in res]
        return '\n'.join(words)

    def save_debug_prompt(self, prompts: dict):

        for app_no, prompt_content in prompts.items():
            PromptTemplate.objects.filter(app_no=app_no).update(debug_prompt_content=prompt_content)

    def post(self, request, *args, **kwargs):
        conversation_id = request.data.get('conversation_id')
        user_query = request.data.get('user_query')
        chat_prompt_debug = request.data.get('chat_prompt_debug', '')

        user_question = self.get_user_question()
        message_type = 'normal'
        inputs = {'is_debug': True}

        inputs.update({
            'debug_chat_prompts': {
                'chat_prompt_debug': chat_prompt_debug,
            },
        })
        self.save_debug_prompt(inputs['debug_chat_prompts'])

        if request.data.get('chat_question'):
            message_type = 'question'
            inputs.update({
                'debug_question_prompts': {
                    'evaluate_ocr_text_completeness': request.data.get('evaluate_ocr_text_completeness'),
                    'compare_three_inputs': request.data.get('compare_three_inputs'),
                    'compare_user_input': request.data.get('compare_user_input'),
                    'question_found_prompt': request.data.get('process_comparison'),

                },
                'debug_user_question': user_question,
            })
            # self.save_debug_prompt(inputs['debug_question_prompts'])
        elif request.data.get('chat_code'):
            message_type = 'code'
            inputs.update({
                'debug_code_prompts': {
                    'code_optimization': request.data.get('code_optimization'),
                },
            })
            user_query = json.dumps({'content': user_query, 'lang_code': request.data.get('code_lang')})
            # self.save_debug_prompt(inputs['debug_code_prompts'])
        elif request.data.get('chat_grammar'):
            message_type = 'grammar'
            inputs.update({
                'debug_grammar_prompts': {
                    'complex_sentence_analysis': request.data.get('complex_sentence_analysis'),
                },
            })
            # self.save_debug_prompt(inputs['debug_grammar_prompts'])
        elif request.data.get('chat_waikan'):
            message_type = 'waikan'
            user_query = json.dumps({'content': user_query, 'waikan_answer': request.data.get('waikan_answer'), 'waikan_analysis': request.data.get('waikan_analysis'),'waikan_article': request.data.get('waikan_article')})
            # inputs = {
            #         'waikan_answer': request.data.get('waikan_answer'),
            #         'waikan_analysis': request.data.get('waikan_analysis'),
            #     }

        chat_dto = ChatMessageDto(
            app_id='app_28482099ed2945e2',
            query=user_query,
            stream=True,
            conversation_id=conversation_id,
            message_type=message_type,
            inputs=inputs,
            pre_prompt=chat_prompt_debug
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)


class ComplexSentenceAnalysisView(base_view.BaseView):
    validator_class = ComplexSentenceAnalysisValidator

    def post(self, request, *args, **kwargs):
        user_question = request.data.get('user_question')
        updated_prompt = request.data.get('complex_sentence_analysis')

        #构建 inputs 字典，仅包含必要的信息
        inputs = {
            'complex_sentence_analysis':updated_prompt,

        }
        PromptTemplate.objects.filter(app_no='complex_sentence_analysis').update(debug_prompt_content=updated_prompt)
        chat_dto = ChatMessageDto(
            app_id='complex_sentence_analysis',
            query=user_question,
            stream=True,
            inputs=inputs,
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)


class ComplexSentenceAnalysisIndexView(TemplateView):
    template_name = 'app/complex_sentence_analysis.html'

    def get_context_data(self,**kwargs):
        context = super().get_context_data(**kwargs)

        templates = PromptTemplate.objects.filter(
            app_no__in=[
                'complex_sentence_analysis',
            ]
        )
        complex_sentence_analysis = ''

        for t in templates:
            if t.app_no == 'complex_sentence_analysis':
                complex_sentence_analysis = t.get_debug_prompt_content()

        context.update({
            'complex_sentence_analysis': complex_sentence_analysis,
        })
        return context


class LearningReportGeneratorView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        answer_id = request.data.get('answer_id')
        updated_prompt = request.data.get('learning_report_generator')
        # math_video_content = request.data.get('video_subtitles_content')
        subject = request.data.get('subject')
        generate_mode = request.data.get('generate_mode')
        #构建 inputs 字典，仅包含必要的信息
        inputs = {
            'learning_report_generator' : updated_prompt,
            # 'math_video_content' : math_video_content,
            'subject': subject,
            'answer_id':answer_id,
            'generate_mode':generate_mode

        }
        # 根据生成方式选择不同的提示词模板
        if generate_mode == 'only_paper':
            template_name = 'learning_report_generator_single'
        else:
            template_name = 'learning_report_generator_mul'
        PromptTemplate.objects.filter(app_no=template_name).update(debug_prompt_content=updated_prompt)
        chat_dto = ChatMessageDto(
            app_id='learning_report_generator',
            query=answer_id,
            stream=True,
            inputs=inputs,
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)


class LearningReportGeneratorIndexView(TemplateView):
    template_name = 'app/learning_report_generator.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)


        templates = PromptTemplate.objects.filter(
            app_no__in=[
                'learning_report_generator_single',
                'learning_report_generator_mul'
            ]
        )
        learning_report_generator_single = ''
        learning_report_generator_mul = ''

        for t in templates:
            if t.app_no == 'learning_report_generator_single':
                learning_report_generator_single = t.get_debug_prompt_content()
            elif t.app_no == 'learning_report_generator_mul':
                learning_report_generator_mul = t.get_debug_prompt_content()

        context.update({
            'learning_report_generator_single': learning_report_generator_single,
            'learning_report_generator_mul': learning_report_generator_mul,
        })
        return context


class CollegeAnalysisView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        report_id = request.data.get('report_id')
        student_info = request.data.get('student_info')
        all_fields = request.data.get('all_fields')
        updated_prompt = request.data.get('college_analysis')
        print(f"student_info在这边:{student_info}")
        print(f"all_fields在这边：{all_fields}")

        # # 提取目标院校和专业
        # target_colleges = []
        # for i in range(1, 4):
        #     college_name_key = f'target_college_{i}_name'
        #     major_name_key = f'target_college_{i}_major'
        #
        #     college_name = all_fields.get(college_name_key, '')
        #     major_name = all_fields.get(major_name_key, '')
        #
        #     if college_name and major_name:
        #         target_colleges.append({
        #             'college_name': college_name,
        #             'major_name': major_name
        #         })

        # 从 all_fields 中提取 get_college_and_major_info 需要的参数
        target_major_direction = all_fields.get('target_major_direction',[])
        bachelor_level = all_fields.get('bachelor_level', '')
        master_degree_type = all_fields.get('master_degree_type', '')
        regions = all_fields.get('regions', [])
        priority_order = all_fields.get('priority_order', '')

        college_result = get_college_and_major_info(target_major_direction if target_major_direction else [], bachelor_level,
                                                    master_degree_type,regions,priority_order)
        # query = json.dumps(all_fields, ensure_ascii=False)


        # 提取霍兰德测试结果
        holland_test_result = all_fields.get('holland_test_result', '')
        query = json.dumps(student_info, ensure_ascii=False)
        PromptTemplate.objects.filter(app_no='college_analysis').update(prompt_content=updated_prompt)
        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            'holland_test_result': holland_test_result,
            'college_analysis':updated_prompt,
            "report_id": report_id,
            'college_result': college_result,
        }
        print(f"inputs: {inputs}")

        chat_dto = ChatMessageDto(
            app_id='college_analysis',
            query=query,
            stream=True,
            inputs=inputs,
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)



class CollegeAnalysisIndexView(TemplateView):
    template_name = 'app/college_analysis.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        templates = PromptTemplate.objects.filter(
            app_no__in=[
                'college_analysis',
            ]
        )
        college_analysis = ''

        for t in templates:
            if t.app_no == 'college_analysis':
                college_analysis = t.get_debug_prompt_content()

        context.update({
            'college_analysis': college_analysis,
        })

        return context

class CollegeAnalysisNewView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        # report_id = request.data.get('report_id')
        all_fields = request.data.get('all_fields')
        updated_prompt = request.data.get('college_analysis_new')
        print(f"all_fields在这边：{all_fields}")
        student_info = request.data.get('student_info')
        holland_test_result = all_fields.get('holland_test_result', '')
        print(f"学生基本信息在这边🔥：{student_info}")
        bachelor_college_name = all_fields.get('bachelor_college', '')
        print (f"本科院校名称为🔥: {bachelor_college_name}")
        # 从 all_fields 中提取需要的参数本科院校代码，专业二级门类代码，主观避开条件
        bachelor_college_code = all_fields.get('bachelor_level', '')
        bachelor_major_code = all_fields.get('bachelor_major', '')
        personal_needs = all_fields.get('personal_needs', [])
        preferred_regions = all_fields.get('preferred_regions', [])
        cross_exam = all_fields.get('cross_exam', {})
        print(f"是否跨考数据在这边🔥:{cross_exam}")
        print(f"用户输入的意向地区在这🔥:{preferred_regions}")
        # 调用筛选逻辑service
        recommend_college_major_info = CollegeAnalysisNewService.filter_by_bachelor_major_code(bachelor_major_code,personal_needs,bachelor_college_code,preferred_regions,cross_exam)
        PromptTemplate.objects.filter(app_no='college_analysis_new').update(prompt_content=updated_prompt)
        student_info_str = json.dumps(student_info, ensure_ascii=False)

        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            'holland_test_result': holland_test_result,
            'college_analysis':updated_prompt,
            # "report_id": report_id,
            'recommend_college_major_info': recommend_college_major_info,

        }

        chat_dto = ChatMessageDto(
            app_id='college_analysis_new',
            query=student_info_str,
            stream=True,
            inputs=inputs,
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)


# 含目标院校和专业的考研规划分析
class CollegeAnalysisNewWithGoalView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        # report_id = request.data.get('report_id')
        all_fields = request.data.get('all_fields')
        updated_prompt = request.data.get('college_analysis_new_with_goal')
        print(f"all_fields在这边：{all_fields}")
        student_info = request.data.get('student_info_for_goal')
        print(f"student_info在这边🔥：{student_info}")
        # 从 all_fields 中提取需要的参数本科院校代码，专业二级门类代码，目标院校和目标专业
        bachelor_college_code = all_fields.get('bachelor_level', '')
        bachelor_major_code = all_fields.get('bachelor_major', '')
        # 目前支持一个目标院校和专业 进行测试
        target_college_code = request.data.get('target_college_code', '')
        target_major_code = request.data.get('target_major_code', '')
        priority_order = all_fields.get('priority_order', '')
        master_type = all_fields.get('master_type', '')
        print(f"priority_order在这边🔥:{priority_order}")
        print(f"master_type在这边🔥:{master_type}")
        # 调用筛选逻辑service
        analysis_result = CollegeAnalysisNewService.college_analysis_with_goal(bachelor_major_code,bachelor_college_code,target_college_code,target_major_code,priority_order)

        PromptTemplate.objects.filter(app_no='college_analysis_new_with_goal').update(prompt_content=updated_prompt)
        student_info_str = json.dumps(student_info, ensure_ascii=False)
        # 构建 inputs 字典，仅包含必要的信息
        inputs = {
            # 'holland_test_result': holland_test_result,
            'college_analysis':updated_prompt,
            # "report_id": report_id,
            'analysis_result': analysis_result,

        }

        chat_dto = ChatMessageDto(
            app_id='college_analysis_new_with_goal',
            query=student_info_str,
            stream=True,
            inputs=inputs,
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)


class CollegeAnalysisNewIndexView(TemplateView):
    template_name = 'app/college_analysis_new.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        templates = PromptTemplate.objects.filter(
            app_no__in=[
                'college_analysis_new',
                'college_analysis_new_with_goal'
            ]
        )
        college_analysis_new = ''
        college_analysis_new_with_goal = ''

        for t in templates:
            if t.app_no == 'college_analysis_new':
                college_analysis_new = t.get_debug_prompt_content()
            if t.app_no == 'college_analysis_new_with_goal':
                college_analysis_new_with_goal = t.get_debug_prompt_content()

        context.update({
            'college_analysis_new': college_analysis_new,
            'college_analysis_new_with_goal': college_analysis_new_with_goal,
        })

        return context


class CollegeAnalysisNewCollegeView(APIView):
    @staticmethod
    def get(request):
        """返回所有本科院校数据（仅包含 name 和 code 字段）"""
        try:
            college_data = []
            csv_path = '本科院校_更新2.csv'

            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.reader(file)
                next(reader)  # 跳过标题行
                for row in reader:
                    if len(row) >= 2:
                        college_data.append({
                            'name': row[0],  # name 字段
                            'code': row[1]   # code 字段
                        })

            return Response({
                'status': 'success',
                'data': college_data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class KaoYanReviewPlanView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        report_id = request.data.get('report_id')
        target_college_level = request.data.get('target_college_level', '')
        updated_prompt = request.data.get('kaoyan_review_plan')
        print(f"updated_prompt在这:{updated_prompt}")
        # 获取当前时间
        start_review_time = datetime.now().strftime('%Y-%m-%d')
        inputs = {
            'target_college_level': target_college_level,
            'kaoyan_review_plan': updated_prompt,
            "report_id": report_id,
            "start_review_time":start_review_time
        }
        PromptTemplate.objects.filter(app_no='kaoyan_review_plan').update(prompt_content=updated_prompt)

        chat_dto = ChatMessageDto(
            app_id='kaoyan_review_plan',
            query='',
            stream=True,
            inputs=inputs,
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)


class KaoYanReviewPlanIndexView(TemplateView):
    template_name = 'app/kaoyan_review_plan.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        templates = PromptTemplate.objects.filter(
            app_no__in=[
                'kaoyan_review_plan',
            ]
        )
        kaoyan_review_plan = ''

        for t in templates:
            if t.app_no == 'kaoyan_review_plan':
                kaoyan_review_plan = t.get_debug_prompt_content()

        context.update({
            'kaoyan_review_plan': kaoyan_review_plan,
        })

        return context


class Huolande_TestView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        content = request.data.get('content')

        response = huolande_test(content)
        return make_response(response)



class Huolande_Test_IndexView(TemplateView):
    template_name = 'app/huolande_test.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class FileUploadView(APIView):
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        file_obj = request.FILES.get('file')
        if not file_obj:
            return JsonResponse({'error': 'No file uploaded'}, status=400)

        file_content = parse_file(file_obj)
        if isinstance(file_content, str):
            return JsonResponse({'content': file_content})
        else:
            return JsonResponse({'error': 'Failed to parse file'}, status=400)


def parse_file(file_obj):

    file_extension = os.path.splitext(file_obj.name)[1].lower()

    if file_extension == '.md':
        content = file_obj.read().decode()
        return content
    elif file_extension == '.docx':
        doc = Document(file_obj)
        full_text = []
        for para in doc.paragraphs:
            full_text.append(para.text)
        return '\n'.join(full_text)
    elif file_extension == '.txt':
        return file_obj.read().decode()
    else:
        return "Unsupported file type"


class ChapterNoteGeneratorView(base_view.BaseView):
    validator_class = ChapterNoteGeneratorValidator

    def post(self, request, *args, **kwargs):
        user_question = request.data.get('user_question')
        updated_chapter_note_prompt = request.data.get('chapter_note_generation')
        model_provider = request.data.get("modelProvider", "deepseek")

        if model_provider == "tongyi":
            PromptTemplate.objects.filter(app_no="chapter_note_generation").update(
                model_provider="tongyi", model_id="qwen-turbo"
            )
            AppModelConfig.objects.filter(app_no="chapter_note_generation").update(
                model_provider="tongyi", model_id="qwen-turbo"
            )
        else:
            PromptTemplate.objects.filter(app_no="chapter_note_generation").update(
                model_provider="deepseek", model_id="deepseek-chat"
            )
            AppModelConfig.objects.filter(app_no="chapter_note_generation").update(
                model_provider="deepseek", model_id="deepseek-chat"
            )

        inputs = {
                'chapter_note_generation': updated_chapter_note_prompt,
                'lecture_slides': user_question
            }

        PromptTemplate.update_debug_content('chapter_note_generation', updated_chapter_note_prompt)

        chat_dto = ChatMessageDto(
            app_id='chapter_note_generation',
            query=user_question,
            stream=True,
            inputs=inputs,
        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)


class LectureNoteGeneratorChapterView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        file_obj = request.FILES.get('file')
        chapter_name = request.data.get('chapter_name')
        lecture_content = parse_file(file_obj)

        model_provider = request.data.get("modelProvider", "")
        updated_lecture_note_prompt = request.data.get('lecture_note_generation')
        if model_provider == "tongyi":
            PromptTemplate.objects.filter(app_no="lecture_note_generation").update(
                model_provider="tongyi", model_id="qwen-turbo",
                debug_prompt_content=updated_lecture_note_prompt
            )
            AppModelConfig.objects.filter(app_no="lecture_note_generation").update(
                model_provider="tongyi", model_id="qwen-turbo"
            )
        else:
            PromptTemplate.objects.filter(app_no="lecture_note_generation").update(
                model_provider="deepseek", model_id="deepseek-chat",
                debug_prompt_content=updated_lecture_note_prompt
            )
            AppModelConfig.objects.filter(app_no="lecture_note_generation").update(
                model_provider="deepseek", model_id="deepseek-chat"
            )

        DebugCourseNoteService.debug_create_chapter_note_task_chapter(chapter_name, lecture_content)
        return make_response()


class LectureNoteGeneratorBatchView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        file_obj = request.FILES.get('file')
        lecture_content = parse_file(file_obj)

        model_provider = request.data.get("modelProvider", "")
        updated_lecture_note_prompt = request.data.get('lecture_note_generation')
        if model_provider == "tongyi":
            PromptTemplate.objects.filter(app_no="lecture_note_generation").update(
                model_provider="tongyi", model_id="qwen-turbo",
                debug_prompt_content=updated_lecture_note_prompt
            )
            AppModelConfig.objects.filter(app_no="lecture_note_generation").update(
                model_provider="tongyi", model_id="qwen-turbo"
            )
        else:
            PromptTemplate.objects.filter(app_no="lecture_note_generation").update(
                model_provider="deepseek", model_id="deepseek-chat",
                debug_prompt_content=updated_lecture_note_prompt
            )
            AppModelConfig.objects.filter(app_no="lecture_note_generation").update(
                model_provider="deepseek", model_id="deepseek-chat"
            )

        DebugCourseNoteService.debug_create_chapter_note_task_batch(lecture_content)
        return make_response()


class NoteGeneratorTasksView(TemplateView):
    template_name = 'app/note_generator_tasks.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取所有 CourseNoteTaskDebug 对象
        debug_tasks = CourseNoteTaskDebug.objects.filter(is_deleted=False).order_by('-id')
        context['debug_tasks'] = debug_tasks
        return context


class SingleNoteDetailView(DetailView):
    model = CourseNoteTaskChangeDetail
    template_name = 'app/single_note_detail.html'
    context_object_name = 'note_detail'

    def get_object(self, queryset=None):
        task_id = self.kwargs.get('pk')
        try:
            return CourseNoteTaskChangeDetail.objects.get(id=task_id)
        except CourseNoteTaskChangeDetail.DoesNotExist:
            raise Http404("Note does not exist")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        note_detail: CourseNoteTaskChangeDetail = self.object

        context['message'] = note_detail.message
        context['full_note_content'] = note_detail.video_note
        return context


class NoteDetailView(DetailView):
    model = CourseNoteTaskChangeDetail
    template_name = 'app/note_detail.html'
    context_object_name = 'note_detail'

    def get_object(self, queryset=None):
        task_id = self.kwargs.get('pk')
        try:
            return CourseNoteTaskDebug.objects.get(id=task_id)
        except CourseNoteTaskDebug.DoesNotExist:
            raise Http404("Note does not exist")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        task_debug: CourseNoteTaskDebug = self.object

        tasks = CourseNoteTask.objects.filter(
            task_debug=task_debug
        ).order_by('id').prefetch_related('coursenotetaskchangedetail_set')

        full_note_content = []

        for task in tasks:
            details = task.coursenotetaskchangedetail_set.all()
            for d in details:
                full_note_content.append(d.video_note)

        full_note_content = '\n'.join(full_note_content)
        context['full_note_content'] = full_note_content

        return context


class TaskDetailView(TemplateView):
    template_name = 'app/task_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        note_id = self.kwargs.get('note_id')

        # 获取所有 CourseNoteTask 对象
        tasks = CourseNoteTask.objects.filter(
            task_debug_id=note_id
        ).order_by('id').prefetch_related('coursenotetaskchangedetail_set')

        # 将 video_note 添加到每个任务的字典中
        task_detail_list = []
        for task in tasks:
            details = task.coursenotetaskchangedetail_set.all()
            for d in details:
                task_detail_list.append({
                    'id': d.id,
                    'processing_started_at': d.processing_started_at,
                    'completed_at': d.completed_at,
                    'status': d.status,
                    'message': d.message,
                    'lecture_word_len': len(d.video_lecture) if d.video_lecture else 0,
                    'note_word_len': len(d.video_note) if d.video_note else 0,
                    'response_latency': int(d.message.response_latency) if d.message else 0,
                    'name': d.video_content.name
                })

        context['tasks'] = task_detail_list
        return context


class NoteGeneratorIndexView(TemplateView):
    template_name = 'app/note_generator.html'

    def get_context_data(self,**kwargs):
        context = super().get_context_data(**kwargs)

        templates = PromptTemplate.objects.filter(
            app_no__in=[
                'chapter_note_generation',
                'lecture_note_generation'
            ]
        )
        chapter_note_generation = ''
        lecture_note_generation = ''

        for t in templates:
            if t.app_no == 'chapter_note_generation':
                chapter_note_generation = t.get_debug_prompt_content()
            elif t.app_no == 'lecture_note_generation':
                lecture_note_generation = t.get_debug_prompt_content()

        chapter_names = get_chapter_names()
        context.update({
            'chapter_note_generation': chapter_note_generation,
            'lecture_note_generation': lecture_note_generation,
            'chapter_names': chapter_names,
        })
        return context


class ChatAppIndexView(TemplateView):
    template_name = 'app/chat_app.html'

    def get_context_data(self,**kwargs):
        context = super().get_context_data(**kwargs)

        templates = PromptTemplate.objects.filter(
            app_no__in=[
                'chat_prompt_debug',
                'evaluate_ocr_text_completeness',
                'compare_three_inputs',
                'compare_user_input',
                'knowledge_deep_question',
                # 'process_comparison',
                'code_optimization',
                'chat_prompt_debug'
        ]
        )
        chat_prompt_debug = ''
        evaluate_ocr_text_completeness = ''
        compare_three_inputs = ''
        compare_user_input = ''
        process_comparison = ''
        code_optimization = ''
        chat_prompt_debug = ''

        for t in templates:
            if t.app_no == 'chat_prompt_debug':
                chat_prompt_debug = t.get_debug_prompt_content()
            if t.app_no == 'evaluate_ocr_text_completeness':
                evaluate_ocr_text_completeness = t.get_debug_prompt_content()
            elif t.app_no == 'compare_three_inputs':
                compare_three_inputs = t.get_debug_prompt_content()
            elif t.app_no == 'compare_user_input':
                compare_user_input = t.get_debug_prompt_content()
            elif t.app_no == 'knowledge_deep_question':
                process_comparison = t.get_debug_prompt_content()
            elif t.app_no == 'code_optimization':
                code_optimization = t.get_debug_prompt_content()
            elif t.app_no == 'chat_prompt_debug':
                chat_prompt_debug = t.get_debug_prompt_content()
        context.update({
            'chat_prompt_debug': chat_prompt_debug,
            'evaluate_ocr_text_completeness': evaluate_ocr_text_completeness,
            'compare_three_inputs': compare_three_inputs,
            'compare_user_input': compare_user_input,
            'process_comparison': process_comparison,
            'code_optimization': code_optimization,
            'chat_prompt_debug': chat_prompt_debug,
        })
        return context


class EnglishReadingComprehensionIndexView(TemplateView):
    template_name = 'app/English_reading_comprehension.html'


class EnglishReadingComprehensionView(base_view.BaseView):
    validator_class = ProblemSolvingValidator

    def post(self, request, *args, **kwargs):
        conversation_id = request.data.get('conversation_id')
        user_query = request.data.get('user_query')
        message_type = 'normal'
        if request.data.get('chat_waikan'):
            message_type = 'waikan'
            user_query = json.dumps({'content': user_query, 'waikan_answer': request.data.get('waikan_answer'), 'waikan_analysis': request.data.get('waikan_analysis'),'waikan_article': request.data.get('waikan_article')})

        elif request.data.get('chat_waikan_again'):
            message_type = 'waikan_again'
            user_query = json.dumps({'content': user_query, 'waikan_option': request.data.get('waikan_option'), 'weak_points': request.data.get('suggestion'),'waikan_article': request.data.get('waikan_article'),'system_messages':request.data.get('system_messages')})
        chat_dto = ChatMessageDto(
            app_id='app_28482099ed2945e2',
            query=user_query,
            stream=True,
            conversation_id=conversation_id,
            message_type=message_type,


        )
        account = Account.objects.first()
        response = AppGenerateService.generate(chat_dto, account, 'console')
        return make_stream_response(response)


class AppMessageStatsView(TemplateView):
    template_name = 'app/app_message_stats.html'

    OUTER_APP_NOS = (
        'app_28482099ed2945e2',         # 知舟问答
        'app_c04f55acabe94352',         # 智学问答
        'knowledge_query',              # 知识点解析
        'complex_sentence_analysis',    # 英语长难句语法分析
        'app_55e30fe6009c4668',         # 数学解题助手
        'app_ad5a77ca92574d1f',         # 高数截屏答疑
        'app_d66b4aaedc70469e',         # 英语单词出题解答
        'app_667937b76115410c',         # 小程序-英语单词背诵
        'waikan_question',              # 外刊模拟出题  
        'knowledge_analysis_simple',     # 知识解析基础版
    )

    OUTER_APP_TYPES = {
        'chat_app2': '知舟问答2.0',
        'shuati_answer_report': '智学刷题-答卷报告',
        'shuati_subjective_report': '智学刷题-主观题报告',
        'chat_app': '知舟问答',
        'problem_solving': '408解题助手',
        'code_optimization': '代码优化',
        'local_chat_app': '智学问答',
        'knowledge_query': '知识解析',
        'complex_sentence_analysis': '英语长难句语法分析',
        'math_problem_solving': '数学丨解题助手',
        'en_article_judgment': '作文批改',
        'test_answer_report': '数学测试解读',
        'screenshot_gaoshu': '高数截屏答疑',
        'student_learn_stat': '学情分析',
        'college_analysis': '院校专业推荐',
        'en_word_recite': '英语单词背诵',
        'en_word_test': '英语词汇训练',
        'waikan_question': '外刊模拟出题',
        'dsx_learning_stat': '动手学-学情分析',
        'study_guides': '动手学-学习指导',
        'study_guides_app': 'app-学习指导报告',
        'dsx_code_exercise': '动手学-代码练习解析',
        'zhihenRadar': '智痕雷达',
        'knowledge_analysis_simple': '知识解析（基础版）',
        'supervise_learn_stat': '教务督学',
    }
    CHAT_MESSAGE_TYPES_MAP = {
        'chat_app': 'normal',
        'problem_solving': 'question',
        'code_optimization': 'code',
        'complex_sentence_analysis': 'normal',
        'math_problem_solving': 'math_question',
        'en_article_judgment': 'normal',
        'screenshot_gaoshu': 'normal',
        'student_learn_stat': 'normal',
        # 'en_wort_test': 'normal',
        # 'en_wort_recite': 'normal'  , 
        # 'waikan_question': 'normal',
    }

    INNER_APP_NOS = (
        'content_extraction',           # 爆文-内容提炼
        'article_generation',           # 爆文-文章生成
        'prompt_optimize',              # prompt优化
        'document_summary',             # 文档转换助手-内容转换
        'document_extraction',          # 文档转换助手-核心提炼
        'video_script',                 # 视频脚本生成器
    )

    def _get_context_data(self, app_no: str, message_type: str = None) -> dict:
        app = App.objects.filter(app_no=app_no).first()

        today = timezone.now().date()
        start_of_today = timezone.make_aware(timezone.datetime.combine(today, timezone.datetime.min.time()))
        end_of_today = timezone.make_aware(timezone.datetime.combine(today, timezone.datetime.max.time()))

        messages_queryset = Message.objects.filter(app=app)
        if message_type is not None:
            messages_queryset = messages_queryset.filter(message_type=message_type)

        exceptions_queryset = messages_queryset.filter(is_exception=True)
        today_exceptions_queryset = exceptions_queryset.filter(add_time__range=(start_of_today, end_of_today))

        return {
            'id': app.id,
            'name': app.name,
            'message_type': message_type,
            'total_count': messages_queryset.count(),
            'exception_count': exceptions_queryset.count(),
            'today_exception_count': today_exceptions_queryset.count(),
        }

    def _get_app_type_data(self, app_type):
        name = self.OUTER_APP_TYPES.get(app_type, '')
        today = timezone.now().date()
        start_of_today = timezone.make_aware(timezone.datetime.combine(today, timezone.datetime.min.time()))
        end_of_today = timezone.make_aware(timezone.datetime.combine(today, timezone.datetime.max.time()))

        # 兼容非消息应用
        if app_type == 'waikan_question':
            return MessageStatsWaikanQuestion.get_stats(app_type, name, start_of_today, end_of_today)
        elif app_type == 'en_word_test':
            return MessageStatsEnWordTestQuestion.get_stats(app_type, name, start_of_today, end_of_today)
        elif app_type == 'en_word_recite':
            return MessageStatsEnWordReciteQuestion.get_stats(app_type, name, start_of_today, end_of_today)
        elif app_type == 'supervise_learn_stat':
            return MessageStatsSuperviseLearnStat.get_stats(app_type, name, start_of_today, end_of_today)

        chat_app_types = self.CHAT_MESSAGE_TYPES_MAP.keys()
        if app_type in chat_app_types:
            app: App = App.objects.filter(app_type=app_type).first()
            message_type = self.CHAT_MESSAGE_TYPES_MAP.get(app_type)
            messages_queryset = Message.objects.filter(
                # app__run_type='native',
                app__run_type=app.run_type, 
                # app__app_type__in=chat_app_types, # 区分不同的chatapp类型
                app__app_type=app_type,
                message_type=message_type,
            )
        else:
            message_type = 'normal'
            messages_queryset = Message.objects.filter(
                app__run_type='native',
                app__app_type=app_type,
                message_type=message_type,
            )

        exceptions_queryset = messages_queryset.filter(is_exception=True)
        today_exceptions_queryset = exceptions_queryset.filter(add_time__range=(start_of_today, end_of_today))

        return {
            'name': name,
            'app_type': app_type,
            'message_type': message_type,
            'total_count': messages_queryset.count(),
            'exception_count': exceptions_queryset.count(),
            'today_exception_count': today_exceptions_queryset.count(),
        }

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        outer_app_list = []
        inner_app_list = []

        for app_type, _ in self.OUTER_APP_TYPES.items():
            data = self._get_app_type_data(app_type)
            outer_app_list.append(data)

        for app_no in self.INNER_APP_NOS:
            try:
                data = self._get_context_data(app_no)
                inner_app_list.append(data)
            except App.DoesNotExist:
                continue

        context.update({
            'outer_app_list': outer_app_list,
            'inner_app_list': inner_app_list,
            'today': timezone.now().date(),
        })
        return context


class PaginatorMixin:

    def _call_api_get_data(self, app_type, message_no_list, goods_id_list):
        response = requests.post(
            f'{settings.YANTUCS_API_URL}/internal_api/ai_application_api/get_user_id_by_msg_id',
            json={
                'app_type': app_type,
                'goods_id_list': goods_id_list,
                'msg_id_list': message_no_list,
            },
            timeout=10,
        )
        if response.status_code == 200:
            response_data = response.json()['data']
            return response_data
        return {}

    def _fill_user_id(self, data, app_type):
        # 获取用户账号ID
        message_no_list = []
        goods_id_set = set()
        for d in data['data']:
            # 有记录用户昵称的不去yantucs查询
            if d['userinfo']:
                if isinstance(d['userinfo'], str):
                    d['userinfo'] = json.loads(d['userinfo'])
            if d['userinfo'] and d['userinfo'].get('nickname'):
                continue
            message_no_list.append(d['message_no'])
            if from_biz_id := d.get('from_biz_id', ''):
                try:
                    goods_id_set.add(int(from_biz_id.split('-')[0]))
                except Exception:
                    pass

        platform_map = {
            'zhihzou': '知舟端',
            'zhizhou_debug': '知舟端预览',
            'yt-app': '研途APP端',
        }

        try:
            response_data = self._call_api_get_data(app_type, message_no_list, list(goods_id_set))
            data_map = response_data.get('data_map', {})
            goods_info_map = response_data.get('goods_info_map', {})

            for d in data['data']:
                if d['userinfo'] and d['userinfo'].get('nickname'):
                    d['user_id'] = d['userinfo'].get('nickname')
                    d['platform'] = ''
                    d['goods_name'] = ''
                    continue

                with suppress(Exception):
                    if not data_map.get(d['message_no']):
                        continue
                    uid, name = data_map.get(d['message_no'])
                    d['user_id'] = f'{uid} - ({name})'
                    goods_info = goods_info_map.get(d['from_biz_id'].split('-')[0], {})
                    d['goods_name'] = goods_info.get('goods_name', '')
                    platform = goods_info.get('platform', '')
                    if platform == 'zhihzou':
                        if 'debug' in d['from_biz_id']:
                            platform = 'zhizhou_debug'
                    d['platform'] = platform_map.get(platform, '')
        except Exception as e:
            # 如果获取账号的三方请求失败，避免报错阻碍大流程
            print(e)
            pass
        return data

    def get_page_page(self, queryset, request, app_type) -> dict[str, Any]:
        platform_map = {
            'zhihzou': '知舟端',
            'zhizhou_debug': '知舟端预览',
            'yt-app': '研途APP端',
        }

        data = queryset_paginate(queryset, MessageListSerializer, request.GET)
        if app_type == 'knowledge':
            goods_id_set = set()
            for d in data['data']:
                # 有记录用户昵称的不去yantucs查询
                if d['userinfo'] and d['userinfo'].get('nickname'):
                    continue
                if from_biz_id := d.get('from_biz_id', ''):
                    try:
                        goods_id_set.add(int(from_biz_id.split('-')[0]))
                    except Exception:
                        pass
            response_data = self._call_api_get_data('knowledge', [], list(goods_id_set))
            goods_info_map = response_data.get('goods_info_map', {})
            for d in data['data']:
                if d['userinfo'] and d['userinfo'].get('nickname'):
                    d['user_id'] = d['userinfo'].get('nickname')
                    d['platform'] = ''
                    d['goods_name'] = ''
                    continue

                with suppress(Exception):
                    userinfo = d['userinfo'] or {}
                    uid, uname, utype = (
                        userinfo.get('user_id', 0),
                        userinfo.get('user_name', ''),
                        userinfo.get('user_type', '')
                    )
                    user_id_text = f'{uid} - ({uname})'
                    if utype == 'admin':
                        user_id_text = f'{uid} - ({uname}[管理员])'
                    d['user_id'] = user_id_text
                    goods_info = goods_info_map.get(d['from_biz_id'].split('-')[0], {})
                    d['goods_name'] = goods_info.get('goods_name', '')
                    platform = goods_info.get('platform', '')
                    if platform == 'zhihzou':
                        if 'debug' in d['from_biz_id']:
                            platform = 'zhizhou_debug'
                    d['platform'] = platform_map.get(platform, '')
        else:
            data = self._fill_user_id(data, app_type)
        total, size, page = data['page']['count'], data['page']['size'], data['page']['current_page']

        return {
            'page': page,
            'limit': size,
            'prev_page': page - 1,
            'next_page': page + 1 if page * size < total else 0,
            'message_list': data['data'],

        }

    def get_page_data(self, request, queryset, serializer):
        data = queryset_paginate(queryset, serializer, request.GET)
        total, size, page = data['page']['count'], data['page']['size'], data['page']['current_page']
        return {
            'page': page,
            'limit': size,
            'prev_page': page - 1,
            'next_page': page + 1 if page * size < total else 0,
            'message_list': data['data'],
        }


class AppMessageListView(PaginatorMixin, TemplateView):
    template_name = 'app/app_message_list.html'

    DISPLAY_PICTURE_APP_NO_LIST = [
        'math_problem_solving'.capitalize(),
        'en_article_judgment'.capitalize(),
        'screenshot_gaoshu'.capitalize(),
    ]

    def handle_queryset(self, queryset, message_type=None):
        if message_type and message_type in self.DISPLAY_PICTURE_APP_NO_LIST:
            return queryset
        if message_type:
            queryset = queryset.filter(message_type=message_type)
        return queryset

    def _get_queryset(self, app: App, message_type=None) -> QuerySet:
        queryset = Message.objects.filter(app_id=app.pk).order_by('-pk')
        queryset = self.handle_queryset(queryset, message_type)
        return queryset

    @staticmethod
    def get_app_type(app: App) -> str:
        if app.app_no in ['app_28482099ed2945e2', 'app_c04f55acabe94352', 'math_problem_solving']:
            return 'chat'
        elif app.app_no == 'knowledge_query':
            return 'knowledge'
        elif app.app_no == 'complex_sentence_analysis':
            return 'complex_sentence'
        return ''

    def get_waikan_question_data(self, app_id, queryset):
        page_data = self.get_page_data(self.request, queryset, WaikanQuestionSerializer)
        return {
            'app_id': app_id,
            'app_no': 'app_type',
            'app_name': AppMessageStatsView.OUTER_APP_TYPES.get(app_id),
            'message_type': 'message_type',
            'is_inner_app': False,
            **page_data,
        }

    def get_en_word_test_data(self, app_id, queryset):
        page_data = self.get_page_data(self.request, queryset, EnWordTestSerializer)
        return {
            'app_id': app_id,
            'app_no': 'app_type',
            'app_name': AppMessageStatsView.OUTER_APP_TYPES.get(app_id),
            'message_type': 'message_type',
            'is_inner_app': False,
            **page_data,
        }

    def get_en_word_recite_data(self, app_id, queryset):
        page_data = self.get_page_data(self.request, queryset, EnWordReciteSerializer)
        return {
            'app_id': app_id,
            'app_no': 'app_type',
            'app_name': AppMessageStatsView.OUTER_APP_TYPES.get(app_id),
            'message_type': 'message_type',
            'is_inner_app': False,
            **page_data,
        }

    def get_supervise_learn_stat_data(self, app_id, queryset):
        page_data = self.get_page_data(self.request, queryset, SuperviseLearnStatSerializer)
        return {
            'app_id': app_id,
            'app_no': 'app_type',
            'app_name': AppMessageStatsView.OUTER_APP_TYPES.get(app_id),
            'message_type': 'message_type',
            'is_inner_app': False,
            **page_data,
        }

    def _get_data(self, **kwargs):
        app_id = kwargs['app_id']

        # 兼容非消息应用
        if app_id == 'waikan_question':
            queryset = MessageStatsWaikanQuestion.ge_queryset()
            return self.get_waikan_question_data(app_id, queryset)
        if app_id == 'en_word_test':
            queryset = MessageStatsEnWordTestQuestion.ge_queryset()
            return self.get_en_word_test_data(app_id, queryset)
        if app_id == 'en_word_recite':
            queryset = MessageStatsEnWordReciteQuestion.ge_queryset()
            return self.get_en_word_recite_data(app_id, queryset)
        if app_id == 'supervise_learn_stat':
            queryset = MessageStatsSuperviseLearnStat.ge_queryset()
            return self.get_supervise_learn_stat_data(app_id, queryset)

        is_inner_app = False
        if app_id.isdigit():
            is_inner_app = True
            app: App = App.objects.filter(id=kwargs['app_id']).first()
            app_name = app.name
            app_type = app.app_no
            queryset = self._get_queryset(app)
            message_type = 'normal'
        else:
            # 兼容处理教辅应用
            chat_app_types = CHAT_MESSAGE_TYPES_MAP.keys()
            if app_id in chat_app_types:
                message_type = CHAT_MESSAGE_TYPES_MAP.get(app_id)
                app_types = chat_app_types
            else:
                message_type = 'normal'
                app_types = [app_id]
            app_name = OUTER_APP_TYPES.get(app_id, '')
            app_type = app_id
            app: App = App.objects.filter(app_type=kwargs['app_id']).first()
            queryset = Message.objects.filter(
                # app__run_type='native',
                app__run_type=app.run_type,
                app__app_type__in=app_types,
                message_type=message_type,
            ).order_by('-pk')

        queryset = queryset.select_related('app').order_by('-pk')
        # message_type = self.kwargs.get('message_type')

        # queryset = self._get_queryset(app, message_type)
        # page_data = self.get_page_page(queryset, self.request, app_type=self.get_app_type(app))
        page_data = self.get_page_page(queryset, self.request, app_type=self.get_app_type(app))

        if app_id == 'chat_app2':
            message_list = page_data['message_list']
            m_ids = [m['id'] for m in message_list]
            tracing_qs = MessageTracing.objects.filter(
                message_id__in=m_ids,
                type='chat2_answer_thinking'
            )
            tracing_map = {t.message_id: t for t in tracing_qs}
            for m in message_list:
                if m['id'] not in tracing_map:
                    continue
                tracing_info = tracing_map.get(m['id'])
                m['reasoning'] = tracing_info.answer

        if app_id == 'shuati_answer_report':
            for m in page_data['message_list']:
                query = []
                st_paper_answer_id = int(m['query'])
                answer = STUserPaperAnswer.objects.filter(id=int(st_paper_answer_id)).first()
                if not answer:
                    answered_question_ids = []
                else:
                    answered_question_ids = answer.get_answered_question_ids()
                for q in STQuestion.objects.filter(id__in=answered_question_ids).all():
                    query.append(q.get_format_question_content().dict())

                query_str = ''
                for q in query:
                    query_str += f'#### 题干:\n{q["title"]}\n#### 选项:\n{q["choices"]}\n#### 答案:\n{q["choices_answer"]}\n\n------\n\n'
                m['query'] = query_str
                m['user_id'] = answer.user_id if answer else ''

        return {
            'app_id': app_id,
            'app_no': app_type,
            'app_name': app_name,
            'message_type': message_type,
            'is_inner_app': is_inner_app,
            **page_data,
        }

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        data = self._get_data(**kwargs)
        context.update(**data)
        return context

    def post(self, request, *args, **kwargs):
        data = self._get_data(**kwargs)
        return JsonResponse(data)


class AppExceptionMessageListView(AppMessageListView):
    template_name = 'app/app_message_exception_list.html'

    def _get_queryset(self, app: App, message_type=None) -> QuerySet:
        queryset = Message.objects.filter(app_id=app.pk, is_exception=True).order_by('-pk')
        queryset = self.handle_queryset(queryset, message_type)
        return queryset

    def _get_data(self, **kwargs):
        # app: App = App.objects.filter(id=kwargs['app_id']).first()
        # message_type = self.kwargs.get('message_type')

        app_id = kwargs['app_id']
        # 兼容非消息应用
        if app_id == 'waikan_question':
            queryset = MessageStatsWaikanQuestion.ge_queryset().filter(is_exception=True)
            return self.get_waikan_question_data(app_id, queryset)
        if app_id == 'en_word_test':
            queryset = MessageStatsEnWordTestQuestion.ge_queryset().filter(is_exception=True)
            return self.get_en_word_test_data(app_id, queryset)
        if app_id == 'en_word_recite':
            queryset = MessageStatsEnWordReciteQuestion.ge_queryset().filter(is_exception=True)
            return self.get_en_word_recite_data(app_id, queryset)
        if app_id == 'supervise_learn_stat':
            queryset = MessageStatsSuperviseLearnStat.ge_queryset().filter(status='FAIL')
            return self.get_supervise_learn_stat_data(app_id, queryset)

        is_inner_app = False
        if app_id.isdigit():
            is_inner_app = True
            app: App = App.objects.filter(id=kwargs['app_id']).first()
            app_name = app.name
            app_type = app.app_no
            queryset = self._get_queryset(app)
            message_type = 'normal'
        else:
            # 兼容处理教辅应用
            chat_app_types = CHAT_MESSAGE_TYPES_MAP.keys()
            if app_id in chat_app_types:
                message_type = CHAT_MESSAGE_TYPES_MAP.get(app_id)
                app_types = chat_app_types
            else:
                message_type = 'normal'
                app_types = [app_id]
            app_name = OUTER_APP_TYPES.get(app_id, '')
            app_type = app_id
            app: App = App.objects.filter(app_type=kwargs['app_id']).first()
            queryset = Message.objects.filter(
                app__run_type=app.run_type,
                app__app_type__in=app_types,
                message_type=message_type,
                is_exception=True
            ).order_by('-pk')

        # queryset = self._get_queryset(app, message_type)
        # page_data = self.get_page_page(queryset, self.request, app_type=self.get_app_type(app))
        page_data = self.get_page_page(queryset, self.request, app_type=self.get_app_type(app))
        return {
            'app_id': app_id,
            'app_name': app_name,
            'message_type': message_type,
            **page_data,
        }


class AppDailyExceptionMessageListView(AppMessageListView):
    template_name = 'app/app_daily_exception_list.html'

    @staticmethod
    def _get_today_start_end():
        today = timezone.now().date()
        start_of_today = timezone.make_aware(timezone.datetime.combine(today, timezone.datetime.min.time()))
        end_of_today = timezone.make_aware(timezone.datetime.combine(today, timezone.datetime.max.time()))
        return [start_of_today, end_of_today]

    def _get_queryset(self, app: App, message_type=None) -> QuerySet:
        start_of_today, end_of_today = self._get_today_start_end()
        queryset = Message.objects.filter(
            app_id=app.pk,
            is_exception=True,
            add_time__range=(start_of_today, end_of_today)
        ).order_by('-pk')
        queryset = self.handle_queryset(queryset, message_type)
        return queryset

    def _get_data(self, **kwargs):
        # app: App = App.objects.filter(id=kwargs['app_id']).first()
        # message_type = self.kwargs.get('message_type')  # 获取URL参数中的message_type

        app_id = kwargs['app_id']
        # 兼容非消息应用
        start_of_today, end_of_today = self._get_today_start_end()
        if app_id == 'waikan_question':
            queryset = MessageStatsWaikanQuestion.ge_queryset().filter(
                is_exception=True, add_time__range=(start_of_today, end_of_today)
            )
            return self.get_waikan_question_data(app_id, queryset)
        if app_id == 'en_word_test':
            queryset = MessageStatsEnWordTestQuestion.ge_queryset().filter(
                is_exception=True, add_time__range=(start_of_today, end_of_today)
            )
            return self.get_en_word_test_data(app_id, queryset)
        if app_id == 'en_word_recite':
            queryset = MessageStatsEnWordReciteQuestion.ge_queryset().filter(
                is_exception=True, add_time__range=(start_of_today, end_of_today)
            )
            return self.get_en_word_recite_data(app_id, queryset)
        if app_id == 'supervise_learn_stat':
            queryset = MessageStatsSuperviseLearnStat.ge_queryset().filter(
                status='FAIL', add_time__range=(start_of_today, end_of_today)
            )
            return self.get_supervise_learn_stat_data(app_id, queryset)

        is_inner_app = False
        if app_id.isdigit():
            is_inner_app = True
            app: App = App.objects.filter(id=kwargs['app_id']).first()
            app_name = app.name
            app_type = app.app_no
            queryset = self._get_queryset(app)
            message_type = 'normal'
        else:
            # 兼容处理教辅应用
            chat_app_types = CHAT_MESSAGE_TYPES_MAP.keys()
            if app_id in chat_app_types:
                message_type = CHAT_MESSAGE_TYPES_MAP.get(app_id)
                app_types = chat_app_types
            else:
                message_type = 'normal'
                app_types = [app_id]
            app_name = OUTER_APP_TYPES.get(app_id, '')
            app_type = app_id
            app: App = App.objects.filter(app_type=kwargs['app_id']).first()
            today = timezone.now().date()
            start_of_today = timezone.make_aware(timezone.datetime.combine(today, timezone.datetime.min.time()))
            end_of_today = timezone.make_aware(timezone.datetime.combine(today, timezone.datetime.max.time()))
            queryset = Message.objects.filter(
                app__run_type=app.run_type,
                app__app_type__in=app_types,
                message_type=message_type,
                is_exception=True,
                add_time__range=(start_of_today, end_of_today)
            ).order_by('-pk')

        # queryset = self._get_queryset(app, message_type)
        # queryset = self._get_queryset(app, message_type)
        page_data = self.get_page_page(queryset, self.request, app_type=self.get_app_type(app))

        return {
            'app_id': app_id,
            'app_name': app_name,
            'message_type': message_type,  # 将消息类型也加入到上下文中
            **page_data,
        }


class AppMsgMarkExceptionView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        app_id = kwargs['app_id']
        msg_id = kwargs['msg_id']
        reason = request.data.get('reason')
        image_file = request.data.get('image')
        image_file_url = None
        if image_file:
            image_file_url = upload_file(image_file, file_name=image_file.name, sub_path='message_exception')

        if app_id == 'waikan_question':
            queryset = EnglishWaikanTestRecord.objects.filter(id=msg_id)
        elif app_id == 'en_word_test':
            queryset = EnglishWordTestAnswerDetail.objects.filter(id=msg_id)
        elif app_id == 'en_word_recite':
            queryset = EnWordReciteBasicAnswerDetail.objects.filter(id=msg_id)
        elif app_id == 'supervise_learn_stat':
            queryset = SuperviseLearnStat.objects.filter(id=msg_id)
            queryset.update(status='FAIL')
            return make_response()
        else:
            queryset = Message.objects.filter(id=msg_id)
        # update
        queryset.update(is_exception=True, exception_reason=reason, exception_image=image_file_url)
        return make_response()


class AppMsgMarkNotExceptionView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        app_id = kwargs['app_id']
        msg_id = kwargs['msg_id']

        if app_id == 'waikan_question':
            queryset = EnglishWaikanTestRecord.objects.filter(id=msg_id)
        elif app_id == 'en_word_test':
            queryset = EnglishWordTestAnswerDetail.objects.filter(id=msg_id)
        elif app_id == 'en_word_recite':
            queryset = EnWordReciteBasicAnswerDetail.objects.filter(id=msg_id)
        elif app_id == 'supervise_learn_stat':
            queryset = SuperviseLearnStat.objects.filter(id=msg_id)
            queryset.update(status='SUCCESS')
            return make_response()
        else:
            queryset = Message.objects.filter(id=msg_id)

        # update
        queryset.update(is_exception=False, exception_reason='', exception_image='')
        return make_response()


class AppMsgDailyDetailView(PaginatorMixin, TemplateView):
    template_name = 'app/app_message_daily_detail.html'

    def _get_data(self, **kwargs):
        date = self.request.GET.get('date')
        # app_id = int(self.request.GET.get('app_id'))
        app_id = self.request.GET.get('app_id')
        message_type = self.request.GET.get('message_type')

        start_dt = timezone.localtime(
            timezone.make_aware(datetime.strptime(date, "%Y-%m-%d"), timezone.get_default_timezone())
        ).replace(hour=0, minute=0, second=0, microsecond=0)
        end_dt = start_dt + timedelta(days=1) + timedelta(microseconds=-1)

        # 兼容非消息应用
        if app_id == 'waikan_question':
            queryset = MessageStatsWaikanQuestion.ge_queryset()
            queryset = queryset.filter(add_time__range=(local2utc(start_dt), local2utc(end_dt)))
            page_data = self.get_page_data(self.request, queryset, WaikanQuestionSerializer)
            return {
                'app_id': app_id,
                'app_no': 'app_type',
                'app_name': AppMessageStatsView.OUTER_APP_TYPES.get(app_id),
                'date': date,
                'message_type': 'message_type',
                'is_inner_app': False,
                **page_data,
            }
        if app_id == 'en_word_test':
            queryset = MessageStatsEnWordTestQuestion.ge_queryset()
            queryset = queryset.filter(add_time__range=(local2utc(start_dt), local2utc(end_dt)))
            page_data = self.get_page_data(self.request, queryset, EnWordTestSerializer)
            return {
                'app_id': app_id,
                'app_no': 'app_type',
                'app_name': AppMessageStatsView.OUTER_APP_TYPES.get(app_id),
                'date': date,
                'message_type': 'message_type',
                'is_inner_app': False,
                **page_data,
            }
        if app_id == 'en_word_recite':
            queryset = MessageStatsEnWordReciteQuestion.ge_queryset()
            queryset = queryset.filter(add_time__range=(local2utc(start_dt), local2utc(end_dt)))
            page_data = self.get_page_data(self.request, queryset, EnWordReciteSerializer)
            return {
                'app_id': app_id,
                'app_no': 'app_type',
                'app_name': AppMessageStatsView.OUTER_APP_TYPES.get(app_id),
                'date': date,
                'message_type': 'message_type',
                'is_inner_app': False,
                **page_data,
            }
        if app_id == 'supervise_learn_stat':
            queryset = MessageStatsSuperviseLearnStat.ge_queryset()
            queryset = queryset.filter(add_time__range=(local2utc(start_dt), local2utc(end_dt)))
            page_data = self.get_page_data(self.request, queryset, SuperviseLearnStatSerializer)
            return {
                'app_id': app_id,
                'app_no': 'app_type',
                'app_name': AppMessageStatsView.OUTER_APP_TYPES.get(app_id),
                'date': date,
                'message_type': 'message_type',
                'is_inner_app': False,
                **page_data,
            }

        is_inner_app = False
        if app_id.isdigit():
            is_inner_app = True
            app: App = App.objects.filter(id=kwargs['app_id']).first()
            app_name = app.name
            app_type = app.app_no
            query_params = {
                'app': app,
                'add_time__range': [local2utc(start_dt), local2utc(end_dt)]
            }
            if message_type and message_type.capitalize() not in AppMessageListView.DISPLAY_PICTURE_APP_NO_LIST:
                query_params['message_type'] = message_type

            queryset = Message.objects.filter(**query_params).order_by('-pk')
            message_type = 'normal'
        else:
            # 兼容处理教辅应用
            chat_app_types = CHAT_MESSAGE_TYPES_MAP.keys()
            if app_id in chat_app_types:
                message_type = CHAT_MESSAGE_TYPES_MAP.get(app_id)
                app_types = chat_app_types
            else:
                message_type = 'normal'
                app_types = [app_id]
            app_name = OUTER_APP_TYPES.get(app_id, '')
            app_type = app_id
            app: App = App.objects.filter(app_type=app_id).first()
            queryset = Message.objects.filter(
                app__run_type=app.run_type,
                app__app_type__in=app_types,
                message_type=message_type,
                add_time__range=(local2utc(start_dt), local2utc(end_dt))
            ).order_by('-pk')

        page_data = self.get_page_page(queryset, self.request, app_type=AppMessageListView.get_app_type(app))
        data = {
            'app_id': app_id,
            'app_name': app_name,
            'date': date,
            'message_type': message_type,
            **page_data,
        }
        return data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        data = self._get_data(**kwargs)
        context.update(**data)
        return context

    def post(self, request, *args, **kwargs):
        data = self._get_data(**kwargs)
        return JsonResponse(data)


class KnowledgeEsView(TemplateView):
    template_name = 'app/knowledge_es.html'


class KnowledgeEsResultView(BaseView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        query = request.data.get('query')

        url = 'https://es.kaoyan-vip.cn/test_knowledge/_search'
        data = {"query": {
            "match": {
              "name": query
            }
        }}
        res = requests.post(url, data=json.dumps(data), headers={'Content-Type': 'application/json'}).json()
        search_arr = []
        if res.get('took'):
            hits = res['hits']['hits']
            for h in hits:
                search_arr.append(h['_source'].get('name'))

        return make_response({
            'result': search_arr[:10]
        })


class UnifiedExaminationQuestionView(TemplateView):
    template_name = 'app/unified_examination_question.html'
    queryset = Ueq.objects.filter(is_deleted=False).order_by('year', 'question_no')

    def get_queryset(self):
        queryset = self.queryset
        year = self.request.GET.get('year')
        question_no = self.request.GET.get('question_no')
        if year and year.strip().isdigit():
            queryset = queryset.filter(year=int(year.strip()))
        if question_no and question_no.strip().isdigit():
            queryset = queryset.filter(question_no=int(question_no.strip()))
        return queryset

    @property
    def page_data(self) -> dict[str, Any]:
        data = queryset_paginate(self.get_queryset(), UnifiedExaminationQuestionSerializer, self.request.GET)
        total, size, page = data['page']['count'], data['page']['size'], data['page']['current_page']

        return {
            'page': page,
            'limit': size,
            'prev_page': page - 1,
            'next_page': page + 1 if page * size < total else 0,
            'question_list': data['data'],
            'year': self.request.GET.get('year'),
            'question_no': self.request.GET.get('question_no'),
        }

    def get_context_data(self, **kwargs):
        return self.page_data


class UeqDetailView(TemplateView):
    template_name = 'app/ueq_detail.html'

    @staticmethod
    def _get_question_detail(question_num: int) -> dict:
        cache_key = f'unified_examination_question_{question_num}'

        # catch
        if result := cache.get(cache_key):
            return result

        # call api
        params = {
            'questionIdList': [str(question_num)]
        }
        result = vector_data_client.get_detail(params=params)
        cache.set(cache_key, result, 5 * 60)
        return result

    def get_context_data(self, **kwargs):
        question_num = kwargs['question_num']
        result = self._get_question_detail(question_num)
        result = result.get('data', {}).get('data', [])
        assert isinstance(result, list) and len(result) == 1

        context = {'question_num': question_num, 'error': ''}
        try:
            context.update({
                'master_title': result[0].get('master_title', ''),
                'analysis': result[0]['sub_question_info'][0]['analysis'],
                'choice_body': result[0]['sub_question_info_format'][0]['choice_body'] or [],
                'answer_body': result[0]['sub_question_info_format'][0]['answer_body'] or [],
            })
        except Exception:
            context['error'] = '数据出问题了，请联系管理员检查处理'

        return context


