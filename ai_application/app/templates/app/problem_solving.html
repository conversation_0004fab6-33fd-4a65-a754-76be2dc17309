<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解题助手</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/marked/13.0.2/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        .container {
            max-width: 1500px;
            width: 100%;
        }
        label {
            font-weight: bold;
            margin-top: 15px;
        }
        textarea, input {
            width: 95%;
            padding: 10px;
            margin-top: 5px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        /* Adjusting the height of the textareas */
        #user-question {
            height: 150px; /* Increase the height of the input textarea */
        }
        button {
            width: 100%;
            padding: 10px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .prompt_input {
            resize: vertical;
            height: 300px; /* 设置一个初始高度 */
            width: 600px;
        }
        /* 新增弹出层样式 */
      .promptManagementModal {
        display: none;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background-color: #fff;
        padding: 20px;
        border: 1px solid #ccc;
        z-index: 1000;
      }
        /* 遮罩层样式 */
      #overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
      }
       .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
       }
       .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
       }
        #ocrLoadingOverlay {
        display: none; /* 初始时隐藏 */
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
        z-index: 1000; /* 确保加载框在最上层 */
        justify-content: center;
        align-items: center;
        }

        #ocrLoadingOverlay .loading-content p {
            font-size: 16px;
            color: #333;
        }

        .request_container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px
        }
        .upload_container {
            width: 25%;
        }
        .query_container {
            width: 70%;
        }
        .results-container {
            display: flex;
            justify-content: space-between;
        }
        .result-view {
            width: 50%;
        }
        .tracing-detail {
            width: 45%;
        }
        #tracingDetails{
            white-space: pre-line;
        }
        #tracingDetails p{
            border-bottom: 1px solid red;
            margin-bottom: 5px;
        }

    </style>
</head>
<body>

<div class="container">
    <h2>解题助手</h2>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>奋力搜索中...</p>
        </div>
    </div>

    <div class="loading-overlay" id="ocrLoadingOverlay">
        <div class="loading-content">
            <p>努力识别中...</p>
        </div>
    </div>
    <!-- 新增遮罩层 -->
    <div id="overlay"></div>

    <div style="display: flex; margin-bottom: 20px">
        <div>
            <button id="compare_three_inputs_btn" class="prompt-button">题目匹配</button>
            <div id="compare_three_inputs_modal" class="promptManagementModal">
              <h2>拆分搜索内容提示词</h2>
                <textarea id="compare_three_inputs_prompt" class="prompt_input" name="compare_three_inputs">{{ compare_three_inputs }}</textarea><br>
                <button id="confirm_compare_three_inputs" type="button">确定</button>
                <button id="cancel_compare_three_inputs" type="button">取消</button>
            </div>
        </div>

        <div style="margin-left: 40px">
            <button id="compare_user_input_btn" class="prompt-button">原题比对</button>
            <div id="compare_user_input_modal" class="promptManagementModal">
              <h2>拆分搜索内容提示词</h2>
                <textarea id="compare_user_input_prompt" class="prompt_input" name="compare_user_input">{{ compare_user_input }}</textarea><br>
                <button id="confirm_compare_user_input" type="button">确定</button>
                <button id="cancel_compare_user_input" type="button">取消</button>
            </div>
        </div>

        <div style="margin-left: 40px">
            <button id="process_comparison_btn" class="prompt-button">结果生成</button>
            <div id="process_comparison_modal" class="promptManagementModal">
              <h2>本地文档搜索提示词</h2>
                <textarea id="process_comparison_prompt" class="prompt_input" name="process_comparison">{{ process_comparison }}</textarea><br>
                <button id="confirm_process_comparison" type="button">确定</button>
                <button id="cancel_process_comparison" type="button">取消</button>
            </div>
        </div>
        <div style="margin-left: 40px">
            <button id="evaluate_ocr_text_completeness_btn" class="prompt-button">ocr返回内容完整度识别</button>
            <div id="evaluate_ocr_text_completeness_modal" class="promptManagementModal">
              <h2>ocr返回内容完整度识别提示词</h2>
                <textarea id="evaluate_ocr_text_completeness_prompt" class="prompt_input" name="evaluate_ocr_text_completeness">{{ evaluate_ocr_text_completeness }}</textarea><br>
                <button id="confirm_evaluate_ocr_text_completeness" type="button">确定</button>
                <button id="cancel_evaluate_ocr_text_completeness" type="button">取消</button>
            </div>
        </div>

    </div>

    <div class="request_container">
        <div class="upload_container">
            <label for="user-question">上传图片：</label>
            <form id="uploadForm" action="{% url 'ocr_result' %}">
                <!-- 使用capture属性指定输入类型为摄像头 -->
                <div class="form-group" style="margin-bottom: 20px">
                    <input id="image" type="file" name="image" accept="image/*">
                </div>

                <button id="uploadBtn" type="button">OCR识别</button>
            </form>
            <!-- 用于显示拍摄的照片 -->
            <img id="photo" src="" alt="Your photo will appear here"
                 style="display:none; margin-bottom: 20px; margin-top: 20px; max-width: 95%; width: 200px">
        </div>

        <div class="query_container">
            <div>
                <label for="user-question">用户问题：</label>
                <textarea id="user-question" placeholder="输入问题"></textarea>
            </div>
            <div>
                <label for="user-requirement">用户需求：</label>
                <textarea id="user-requirement" placeholder="输入您的需求" style="height: 30px;"></textarea>
            </div>
        </div>
    </div>

    <button onclick="submitQuestion()">提交</button>

    <h3>返回结果:</h3>
    <div class="results-container">
        <div class="result-view" id="resultView">
          <!-- 搜索结果将在这里显示 -->
        </div>
        <div class="tracing-detail" id="tracingDetails">
          <!-- 搜索细节将在这里显示 -->
        </div>
    </div>

</div>

<script>
    let temp_compare_three_inputs_prompt = $('#compare_three_inputs_prompt').val()
    let temp_compare_user_input_prompt = $('#compare_user_input_prompt').val()
    let temp_process_comparison_prompt = $('#process_comparison_prompt').val()
    let temp_evaluate_ocr_text_completeness_prompt = $('#evaluate_ocr_text_completeness_prompt').val()
    let message_id = null;

    const questionInput = document.getElementById("user-question");
    const resultView = document.getElementById('resultView');
    const tracingDetails = document.getElementById('tracingDetails');

    $('#compare_three_inputs_btn').click(function() {
        $('#compare_three_inputs_modal').show();
        $('#overlay').show();
        $('#compare_three_inputs_prompt').val(temp_compare_three_inputs_prompt)
    });
    $('#compare_user_input_btn').click(function() {
        $('#compare_user_input_modal').show();
        $('#overlay').show();
        $('#compare_user_input_prompt').val(temp_compare_user_input_prompt)
    });
    $('#process_comparison_btn').click(function() {
        $('#process_comparison_modal').show();
        $('#overlay').show();
        $('#process_comparison_prompt').val(temp_process_comparison_prompt)
    });

    $('#evaluate_ocr_text_completeness_btn').click(function() {
        $('#evaluate_ocr_text_completeness_modal').show();
        $('#overlay').show();
        $('#evaluate_ocr_text_completeness_prompt').val(temp_evaluate_ocr_text_completeness_prompt)
    });

    $('#confirm_compare_three_inputs').click(function() {
        temp_compare_three_inputs_prompt = $('#compare_three_inputs_prompt').val()
        $('#compare_three_inputs_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_compare_three_inputs').click(function() {
        $('#compare_three_inputs_modal').hide();
        $('#overlay').hide();
    });
    $('#confirm_compare_user_input').click(function() {
        temp_compare_user_input_prompt = $('#compare_user_input_prompt').val()
        $('#compare_user_input_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_compare_user_input').click(function() {
        $('#compare_user_input_modal').hide();
        $('#overlay').hide();
    });
    $('#confirm_process_comparison').click(function() {
        temp_process_comparison_prompt = $('#process_comparison_prompt').val()
        $('#process_comparison_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_process_comparison').click(function() {
        $('#process_comparison_modal').hide();
        $('#overlay').hide();
    });

    $('#confirm_evaluate_ocr_text_completeness').click(function() {
        temp_evaluate_ocr_text_completeness_prompt = $('#evaluate_ocr_text_completeness_prompt').val()
        $('#evaluate_ocr_text_completeness_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_evaluate_ocr_text_completeness').click(function() {
        $('#evaluate_ocr_text_completeness_modal').hide();
        $('#overlay').hide();
    });

    // 监听用户输入事件，移除空白和换行符
    questionInput.addEventListener('input', function() {
        this.value = this.value.replace(/\s+/g, ''); // 移除所有空白和换行符
    });

    document.querySelector('input[type="file"]').addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const photo = document.getElementById('photo');
                photo.src = e.target.result;
                photo.style.display = 'block'; // 显示照片

                $('#user-question').val('')
            };
            reader.readAsDataURL(file);
        }
    });

    $('#uploadBtn').click(function() {
        // 显示 OCR 识别加载中提示
        $('#ocrLoadingOverlay').css('display', 'flex');
        const formData = new FormData($('#uploadForm')[0]);

        // 将 evaluate_ocr_text_completeness 的值添加到 FormData 中
        formData.append('evaluate_ocr_text_completeness', $('#evaluate_ocr_text_completeness_prompt').val());

        $.ajax({
            url: $('#uploadForm').attr('action'), // 服务器端处理上传的URL
            type: 'POST',
            data: formData,

            processData: false,  // 告诉jQuery不要处理发送的数据
            contentType: false,  // 告诉jQuery不要设置Content-Type请求头
            success: function(response) {
                // 文件上传成功后的回调
                $('#user-question').val(response.data.content)
                $('#ocrLoadingOverlay').css('display', 'none');
            },
            error: function() {
                // 文件上传失败后的回调
                console.error('File upload failed.');
                // 隐藏 OCR 加载提示
                $('#ocrLoadingOverlay').css('display', 'none');
            }
        });

    });

    function submitQuestion() {
        const userQuestion = questionInput.value.trim();
        const userRequirement = document.getElementById("user-requirement").value.trim()  || ""; // 获取用户需求

        if (!userQuestion) {
            alert("请输入问题！");
            return;
        }

        // 清空返回结果的内容
        resultView.innerHTML = ''; // 清空之前的结果
        tracingDetails.innerHTML = ''; // 清空之前的细节

        $('#loadingOverlay').css('display', 'flex');

        message_id = null;
        const url = "{% url 'problem_solving' %}";
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                compare_three_inputs: $('#compare_three_inputs_prompt').val(),
                compare_user_input: $('#compare_user_input_prompt').val(),
                process_comparison: $('#process_comparison_prompt').val(),
                evaluate_ocr_text_completeness: $('#evaluate_ocr_text_completeness_prompt').val(),
                user_question: userQuestion,
                user_requirement: userRequirement,// 将用户需求添加到发送的数据中
            })
        })
        .then(response => {
            $('#loadingOverlay').css('display', 'none');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.body;
        })
        .then(stream => {
            $('#loadingOverlay').css('display', 'none');
            let streamComplete = false;
            const reader = stream.getReader();
            const decoder = new TextDecoder(); // 创建TextDecoder实例
            let result_text = '';
            function readChunk() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        streamComplete = true;
                        console.log('Stream complete, returning collected data');
                        find_tracing()
                        return;
                    }
                    const textChunk = decoder.decode(value, { stream: true });

                    if (textChunk.startsWith("data: ")) {
                        // 去除前缀"data: "
                        const json_str = textChunk.slice(6);
                        console.log('json_str', json_str)
                        try {
                            // 将匹配到的字符串转换为JSON对象
                            const jsonData = JSON.parse(json_str);
                            if (!message_id) {
                                message_id = jsonData.message_id
                            }
                            if (jsonData.answer) {
                                result_text += jsonData.answer;
                                resultView.innerHTML = marked.parse(result_text);
                            } else if (jsonData.err) {
                                resultView.innerHTML = jsonData.err;
                            }
                        } catch (error) {
                            console.error('Error parsing JSON', json_str, error);
                        }
                    }
                    readChunk();
                }).catch(error => {
                    console.error('Error collecting stream data', error);
                });
            }
            readChunk();
        })
        .catch(error => {
            $('#loadingOverlay').css('display', 'none');
            console.error('Error:', error);
        });
    }

function find_tracing() {
    if (!message_id) return
    $.ajax({
        type: 'get',
        url: "{% url 'message_tracing' %}",
        contentType: 'application/json',
        data: JSON.stringify({
            message_id,
        }),
        success: function(response) {
            $('#loadingOverlay').css('display', 'none');
            const results = response.data;

            results.tracing.forEach(function(traceDetail) {
                const detailElement = document.createElement('p');
                detailElement.textContent = traceDetail;
                tracingDetails.appendChild(detailElement);
            });
        },
        error: function(xhr, status, error) {
            alert('请求失败');
            $('#loadingOverlay').css('display', 'none');
        }
    });
}

</script>

</body>
</html>


