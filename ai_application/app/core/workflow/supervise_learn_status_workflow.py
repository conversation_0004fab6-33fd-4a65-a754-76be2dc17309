from app.core.features.supervise_learn_status import Supervise_Learn_Status_Feature
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow


class SuperviseLearnStatusWorkflow(BaseWorkflow):

    def run(self):
        # 获取并处理数据
        data = self.application_generate_entity.inputs
        # 生成最终报告
        result = self._report_generator(data)
        self._publish_workflow_end_message()
        return result
    
    def _report_generator(self, data) -> LLMResult:
        watch_lessons_feature = Supervise_Learn_Status_Feature(app_no='supervise_learn_stat')
        query = str(data)

        watch_lessons_result = watch_lessons_feature.run(
            self.application_generate_entity,
            query=query,
            stream=False
        )


        tracing_info = {
            'tracing_type': 'supervise_learn_stat',
            'query': query
        }
        return self._handle_node_response(watch_lessons_result, stream=False, tracing_info=tracing_info)

class SuperviseLearnStatus2Workflow(BaseWorkflow):

    def run(self):
        # 获取并处理数据
        data = self.application_generate_entity.inputs
        # 生成最终报告
        result = self._report_generator(data)
        self._publish_workflow_end_message()
        return result
    
    def _report_generator(self, data) -> LLMResult:
        tests_feature = Supervise_Learn_Status_Feature(app_no='supervise_learn_stat2')
        query = str(data)

        tests_result = tests_feature.run(
            self.application_generate_entity,
            query=query,
            stream=False
        )

        tracing_info = {
            'tracing_type': 'supervise_learn_stat2',
            'query': query
        }
        return self._handle_node_response(tests_result, stream=False, tracing_info=tracing_info)

class SuperviseLearnStatusNoPlanWorkflow(BaseWorkflow):

    def run(self):
        # 获取并处理数据
        data = self.application_generate_entity.inputs
        # 生成最终报告
        result = self._report_generator(data)
        self._publish_workflow_end_message()
        return result
    
    def _report_generator(self, data) -> LLMResult:
        watch_lessons_feature = Supervise_Learn_Status_Feature(app_no='supervise_learn_stat_no_plan')
        query = str(data)

        watch_lessons_result = watch_lessons_feature.run(
            self.application_generate_entity,
            query=query,
            stream=False
        )


        tracing_info = {
            'tracing_type': 'supervise_learn_stat_no_plan',
            'query': query
        }
        return self._handle_node_response(watch_lessons_result, stream=False, tracing_info=tracing_info)

class SuperviseLearnStatusNoPlan2Workflow(BaseWorkflow):

    def run(self):
        # 获取并处理数据
        data = self.application_generate_entity.inputs
        # 生成最终报告
        result = self._report_generator(data)
        self._publish_workflow_end_message()
        return result
    
    def _report_generator(self, data) -> LLMResult:
        tests_feature = Supervise_Learn_Status_Feature(app_no='supervise_learn_stat_no_plan2')
        query = str(data)

        tests_result = tests_feature.run(
            self.application_generate_entity,
            query=query,
            stream=False
        )

        tracing_info = {
            'tracing_type': 'supervise_learn_stat_no_plan2',
            'query': query
        }
        return self._handle_node_response(tests_result, stream=False, tracing_info=tracing_info)