import re

from app.api.dto import ChatMessageDto, ExercisesLearnStatusDto
from app.core.rag.splitter.markdown_splitter import MarkdownHeaderTextSplitter
from app.errors import AppNotFoundError
from app.models import App, Account
from django.conf import settings
import json
import os

from app.services.app_generate_service import AppGenerateService


class ExercisesLearnStatusService:

    @classmethod
    def gen_analysis_report(cls, dto: ExercisesLearnStatusDto, account: Account):
        app: App = App.objects.filter(is_deleted=False, app_type='dsx_learning_stat').first()
        if not app:
            raise AppNotFoundError()

        req_dict = dto.model_dump()
        req_dict = cls.pre_process_req_data(req_dict)
        # 添加知识点近五年考察次数
        req_dict = cls.add_knowledge_counts(req_dict)
        processed_inputs = cls.convert_keys_to_chinese(req_dict)
        chat_dto = ChatMessageDto(
            app_id=app.app_no,
            query=dto.model_dump_json(),
            stream=False,
            inputs=processed_inputs,
            userinfo=dto.userinfo,
        )
        res = AppGenerateService.generate(chat_dto, account)
        content = res.get('answer')
        question_wrong_types = cls.extract_question_info(content, dto)

        return {
            'content': content,
            'question_wrong_types': question_wrong_types,
        }
    
    @classmethod
    def convert_keys_to_chinese(cls, data):
        key_mapping = {
            "section_name": "课节名称",
            "section_kgs": "知识点列表",
            "video_duration": "视频时长",
            "learn_duration": "学习时长",
            "video_kg_distribute": "视频知识点分布",
            "class_exercise": "随堂测",
            "class_test": "课后练习",
            "questions": "题目列表",
            "title": "题目",
            "right_answer": "正确答案",
            "user_answer": "用户答案",
            "is_right": "是否正确",
            "kg": "知识点",
            "start": "开始时间"
        }

        def convert(obj):
            if isinstance(obj, dict):
                return {
                    key_mapping.get(k, k): convert(v)
                    for k, v in obj.items()
                }
            elif isinstance(obj, list):
                return [convert(i) for i in obj]
            else:
                return obj

        return convert(data)
    
    @classmethod
    def get_knowledge_counts(cls,subject_id = "408"):
        """
        从 408_question_distribute.json 文件中读取知识点的近五年考察次数
        """
        if subject_id == "408":
            file_path = settings.BASE_DIR.joinpath('408_question_distribute.json')
        else:
            subject_list_path = settings.BASE_DIR.joinpath('data_product_subject_subject.json')

            with open(subject_list_path,'r',encoding='utf-8') as f:
                data = json.load(f)
            subject_dict = {item['subject_code']:item['subject_name'] for item in data}
            subject = subject_dict.get(subject_id)
            filename = f"{subject}_question_distribute.json"
            file_path = settings.BASE_DIR.joinpath(filename)
        if not os.path.exists(file_path):
            return {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return {item['knowledge']: item['num'] for item in data}
        except Exception as e:
            print(f"读取文件 {file_path} 出错: {e}")
            return {}

    @classmethod
    def pre_process_req_data(cls, data):
        # 为了避免question_id干扰，需要去除一下
        if 'class_exercise' in data and data['class_exercise'] and data['class_exercise'].get('questions'):
            for i in data['class_exercise'].get('questions'):
                i.pop('question_id', None)
        if 'class_test' in data and data['class_test'] and data['class_test'].get('questions'):
            for i in data['class_test'].get('questions'):
                i.pop('question_id', None)
        return data

    @classmethod
    def add_knowledge_counts(cls, dto):
        """
        仅为 video_kg_distribute 中的知识点添加近五年考察次数
        """
        knowledge_counts = cls.get_knowledge_counts(dto['subject_id'])
        if 'video_kg_distribute' in dto:
            for item in dto['video_kg_distribute']:
                if 'kg' in item:
                    knowledge = item['kg']
                    item['近五年考察次数'] = knowledge_counts.get(knowledge, 0)
        return dto

    @classmethod
    def extract_question_info(cls, content: str, dto: ExercisesLearnStatusDto):
        class_exercise_questions = dto.class_exercise.get('questions', []) if dto.class_exercise else []
        class_test_questions = dto.class_test.get('questions', []) if dto.class_test else []

        md_documents = MarkdownHeaderTextSplitter(
            headers_to_split_on=[
                ("#", "h1"),
                ("##", "h2"),
                ("###", "h3"),
                ("####", "h4"),
            ],
            strip_headers=True
        ).split_text(content)

        question_content = ''
        for d in md_documents:
            # 判断标题包含“错题分析”
            for title in d.metadata.values():
                if '错题分析' in title:
                    question_content = d.page_content
        if not question_content:
            return

        question_content_arr = question_content.split('\n')

        question_wrong_types = []
        for q in question_content_arr:
            # 获取测试类型（随堂测/课后练习）
            test_type = None
            if '随堂测' in q:
                test_type = '随堂测'
            elif '课后练习' in q:
                test_type = '课后练习'

            # 获取题号
            question_number = None
            pattern = r"第\s*(\d+)\s*题"
            match = re.search(pattern, q)
            if match:
                question_number = int(match.group(1))

            # 获取类型
            question_wrong_type = None
            pattern = r"\*\*(.*?)\*\*"
            match = re.search(pattern, q)
            if match:
                question_wrong_type = match.group(1)

            if question_number is None:
                continue

            _questions = class_exercise_questions if test_type == '随堂测' else class_test_questions
            if _questions and len(_questions) >= question_number:
                question = _questions[question_number - 1]
                question_wrong_types.append({
                    'question_id': question['question_id'],
                    'question_wrong_type': question_wrong_type,
                })

        return question_wrong_types
