from typing import Generator

from app.constants.app import AppMode
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate, KnowledgeStore


class KnowledgeQuery(BaseFeature):

    def __init__(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            is_deep: bool,
            knowledge: str,
            definition: str,
            local_prompt_template: PromptTemplate,
            llm_prompt_template: PromptTemplate,
    ):
        self.application_generate_entity = application_generate_entity
        self.is_deep = is_deep
        self.knowledge = knowledge
        self.definition = definition
        self.local_prompt_template = local_prompt_template
        self.llm_prompt_template = llm_prompt_template

        # 预处理inputs
        self.user_inputs = application_generate_entity.inputs.copy()
        self.user_inputs['knowledge'] = knowledge
        self.user_inputs['definition'] = definition

        self.is_usable = True
        # 是否通过模型生成
        self.is_llm_generate = False

    def run(self):
        if not self.is_deep:
            cached_kg_desc = self._get_cached_kg_desc()
            if self.definition:
                prompt_content = self.user_inputs.get('local_prompt')
            else:
                prompt_content = self.user_inputs.get('llm_prompt')
            self.is_llm_generate = False if cached_kg_desc else True
        else:
            cached_kg_desc = ''
            if self.definition:
                prompt_content = self.user_inputs.get('deep_local_prompt')
            else:
                prompt_content = self.user_inputs.get('deep_llm_prompt')
            self.is_llm_generate = True

        prompt_template = self._get_prompt_template()
        if cached_kg_desc:
            return self._get_message_generation(cached_kg_desc)
        else:
            pre_prompt = prompt_template.assemble_prompt(
                query='',
                inputs=self.user_inputs,
                prompt_content=prompt_content
            )
            model_conf = get_llm_model_config(
                app_model_conf=self.application_generate_entity.model_conf,
                prompt_template=prompt_template
            )
            # 处理成流式返回
            res = query_llm_by_prompt(
                model_conf=model_conf,
                app_mode=AppMode.COMPLETION,
                pre_prompt=pre_prompt,
                query='',
                stream=True
            )
            return res

    def check_desc_usable(self, completion_tokens: int):
        # 不是模型生成则默认可用
        if self.is_llm_generate:
            prompt_template = self._get_prompt_template()
            # 判断详解输出是否完整，目前采用token的差距在一定的阈值内
            is_usable = True
            max_tokens = prompt_template.model_params.get('max_tokens')
            if max_tokens and abs(max_tokens - completion_tokens) <= 5:
                is_usable = False
            self.is_usable = is_usable
        else:
            self.is_usable = True

    def update_knowledge_store(self, kg_desc):
        course_id = self.user_inputs.get('course_id')

        if 'debug' not in course_id:
            KnowledgeStore.objects.update_or_create(
                course_id=course_id,
                name=self.knowledge,
                defaults={
                    'definition': self.definition,
                    'desc': kg_desc,
                    'is_usable': self.is_usable,
                }
            )

    def _get_cached_kg_desc(self):
        course_id = self.user_inputs.get('course_id')

        # 临时设置后台管理不记录知识点定义
        if 'debug' in course_id:
            return ''

        is_regenerate = self.user_inputs.get('is_regenerate')
        if not is_regenerate:
            cached_kg: KnowledgeStore = KnowledgeStore.objects.filter(
                course_id=course_id,
                is_deleted=False,
                name=self.knowledge
            ).first()

            # 如果数据库存储的知识点可用，则直接返回
            if cached_kg and cached_kg.is_usable:
                return cached_kg.desc
        return ''

    def _get_prompt_template(self):
        if self.definition:
            return self.local_prompt_template
        else:
            return self.llm_prompt_template

    def _get_message_generation(self, msg: str) -> Generator:
        yield msg


class KnowledgeQueryFactory:

    def __init__(self, is_deep: bool):
        self.is_deep = is_deep

        local_prompt_template = None
        llm_prompt_template = None
        if self.is_deep:
            prompt_templates = PromptTemplate.objects.filter(
                app_no__in=['knowledge_deep_query_local', 'knowledge_deep_query_llm'])
            for p in prompt_templates:
                if p.app_no == 'knowledge_deep_query_local':
                    local_prompt_template = p
                elif p.app_no == 'knowledge_deep_query_llm':
                    llm_prompt_template = p

        else:
            prompt_templates = PromptTemplate.objects.filter(
                app_no__in=['knowledge_query_local', 'knowledge_query_llm'])
            for p in prompt_templates:
                if p.app_no == 'knowledge_query_local':
                    local_prompt_template = p
                elif p.app_no == 'knowledge_query_llm':
                    llm_prompt_template = p

        if not local_prompt_template or not llm_prompt_template:
            raise Exception('prompt template not found')

        self.local_prompt_template = local_prompt_template
        self.llm_prompt_template = llm_prompt_template

    def get_knowledge_query(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            knowledge: str,
            definition: str
    ) -> KnowledgeQuery:
        return KnowledgeQuery(
            application_generate_entity=application_generate_entity,
            is_deep=self.is_deep,
            knowledge=knowledge,
            definition=definition,
            local_prompt_template=self.local_prompt_template,
            llm_prompt_template=self.llm_prompt_template,
        )
