from django.conf import settings
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
import os
import uuid
import json
import logging
from api_client.yantucs_data.client import yantucs_data_client
from app.services.student_learn_stat.learn_stat_service import LearnStatService
from app.models.main_subject import CourseSectionKnowledge
from app.models import Conversation, Account, App ,Message
from app.api.dto import StudyGuidesDto, StudyGuidesAppDto
from django.db import transaction
from app.constants.app import AppMode
from app.errors import AppNotFoundError, ConversationNotFoundError
from app.services.exercises_learn_status_service import ExercisesLearnStatusService
from app.models.model import PromptTemplate as PromptTemp
from datetime import datetime


logger = logging.getLogger(__name__)

def get_ExerInfo(userID,course_sectionIds, subject_id):

    user_id = userID
    course_section_ids = course_sectionIds

    try:
        # 假设 res 是你从 yantucs_data_client.get_exercise 得到的结果
        res = yantucs_data_client.get_exercise(user_id, course_section_ids)

        # 提取 question_id 列表, 获取问题intid
        question_ids = [q['question_intid'] for q in res['questions']]
        # 调用 get_questions_kgs 方法获取知识点
        knowledge_data = LearnStatService.get_questions_kgs(question_ids, subject_id)
        # 将知识点添加到 res 中
        knowledge_map = {item['question_id']: item['knowledge_list'] for item in knowledge_data}
        # 创建一个新的列表，只包含有知识点的题目

        unique_knowledge = set()

        # 遍历 res['questions']，只保留有知识点的题目
        for question in res['questions']:
            question_id = question['question_intid']

            if question_id in knowledge_map and (question.get('answer_status') == 'wrong' or question.get('answer_status') == 'not_answered' ):  # 只有当question_id在knowledge_map中时才处理
                for knowledge in knowledge_map[question_id]:
                    unique_knowledge.add(knowledge['knowledge'])

        knowledge_list = list(unique_knowledge)
        return knowledge_list


    except Exception as e:
        logger.exception(e)
        res = {'wrong_question_ids': []}

def get_videoInfo(userId, course_sectionIds):

    user_id = userId
    course_section_ids = course_sectionIds

    res = yantucs_data_client.get_video(user_id, course_section_ids)
    return res

def get_ExamFreq(subject_id, start_year, end_year, knowledge_names):

    res = yantucs_data_client.get_ExamFrequency(subject_id, start_year, end_year, knowledge_names)
    # print(res)
    return res

def get_weakness(user_id, subject_id):
    all_knowledge_list = get_videoInfo(user_id, subject_id)

    wrong_knowledge_list, exe_knowledge_list = get_ExerInfo(user_id, subject_id, 2025)

    weak_knowledge_list = list(set(all_knowledge_list) - set(exe_knowledge_list) | set(wrong_knowledge_list))

    return weak_knowledge_list


class StudyGuidesService:

    @classmethod
    def add_conversation(cls, dto: StudyGuidesDto,app_model,from_account: Account,) -> Conversation:
        """创建新对话并保存到数据库"""
        return Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app=app_model,
            app_model_config=app_model.app_model_config,
            name='StudyGuides_conversation',
            from_account=from_account,
            from_biz_id=dto.biz_id,
        )

    @classmethod
    @transaction.atomic
    def generate(
            cls,
            dto: StudyGuidesDto,
            from_account: Account,
    ) -> dict:
        """处理非流式消息生成并保存完整记录"""
        # 获取应用配置
        app_model = App.objects.filter(is_deleted=False, app_type="study_guides").first()
        if not app_model:
            raise AppNotFoundError()

        # 获取或创建对话
        if not dto.conversation_id:
            conversation = cls.add_conversation(dto, app_model, from_account)
        else:
            conversation = Conversation.objects.filter(
                conversation_no=dto.conversation_id
            ).first()
            if not conversation:
                raise ConversationNotFoundError()

        # 创建初始消息记录
        new_message = Message.objects.create(
            message_no=str(uuid.uuid4()),
            conversation=conversation,
            app=app_model,
            app_model_config=app_model.app_model_config,
            query=dto.model_dump_json(),  # 存储原始参数
            from_account=from_account,
            userinfo=json.dumps(dto.userinfo, ensure_ascii=False) if dto.userinfo else None,
        )

        try:
            # 验证应用模式
            if app_model.mode != AppMode.COMPLETION.value:
                raise ValueError(f'Invalid app mode {app_model.mode}')

            result = StudyGuidesService.getResponse(dto.weakness_knowledge,dto.section_learn_stat, dto.subject_id)

            with open("result.txt", "w", encoding="utf-8") as file:
                file.write(result.content)

            if hasattr(result, 'content'):
                # 如果是直接的LLM响应
                new_message.answer = result.content
                new_message.message_tokens = result.usage_metadata.get('input_tokens',0)
                new_message.answer_tokens = result.usage_metadata.get('output_tokens', 0)
                new_message.total_tokens = result.usage_metadata.get('total_tokens', 0)
            else:
                print("Not LLM Response")

            # 更新消息状态和内容
            new_message.status = 'normal'
            new_message.save()

            return {'content': result.content}

        except Exception as e:
            # 错误处理
            new_message.status = 'failed'
            new_message.error = str(e)
            new_message.save()
            raise  # 重新抛出异常以便上层处理

    @classmethod
    def getResponse(cls, weakness_knowledge, section_learn_stat, subject_id):

        llm = ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="doubao-1.5-pro-32k-250115",
        )

        template_obj = PromptTemp.objects.get(app_no="dsx_study_guide")

        template = template_obj.prompt_content

        prompt = PromptTemplate.from_template(template)

        subjectId = subject_id

        input_data = {
            "weak_point": weakness_knowledge,
            "lession_info": section_learn_stat,
            "point_freq": ExercisesLearnStatusService.get_knowledge_counts(subjectId)
        }

        llm_chain = prompt | llm

        result = llm_chain.invoke(input_data)

        return result


class StudyGuidesAppService:

    @classmethod
    def add_conversation(cls, dto: StudyGuidesAppDto,app_model,from_account: Account,) -> Conversation:
        """创建新对话并保存到数据库"""
        return Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app=app_model,
            app_model_config=app_model.app_model_config,
            name='StudyGuidesApp_conversation',
            from_account=from_account,
            from_biz_id=dto.biz_id,
        )

    @classmethod
    @transaction.atomic
    def generate(
            cls,
            dto: StudyGuidesAppDto,
            from_account: Account,
    ) -> dict:
        """处理非流式消息生成并保存完整记录"""
        # 获取应用配置
        app_model = App.objects.filter(is_deleted=False, app_type="study_guides_app").first()
        if not app_model:
            raise AppNotFoundError()

        # 获取或创建对话
        if not dto.conversation_id:
            conversation = cls.add_conversation(dto, app_model, from_account)
        else:
            conversation = Conversation.objects.filter(
                conversation_no=dto.conversation_id
            ).first()
            if not conversation:
                raise ConversationNotFoundError()

        # 创建初始消息记录
        new_message = Message.objects.create(
            message_no=str(uuid.uuid4()),
            conversation=conversation,
            app=app_model,
            app_model_config=app_model.app_model_config,
            query=dto.model_dump_json(),  # 存储原始参数
            from_account=from_account,
            userinfo=json.dumps(dto.userinfo, ensure_ascii=False) if dto.userinfo else None,
        )

        try:
            # 验证应用模式
            if app_model.mode != AppMode.COMPLETION.value:
                raise ValueError(f'Invalid app mode {app_model.mode}')

            section_learn_stat = get_videoInfo(dto.user_id,dto.course_section_ids)
            # print(f'section_learn_stat: {section_learn_stat}')
            weakness = get_ExerInfo(dto.user_id,dto.course_section_ids,dto.subject_id)

            end_year = datetime.now().year
            start_year = end_year - 5
            exam_freq = get_ExamFreq(dto.subject_id, start_year, end_year, weakness)

            result = StudyGuidesAppService.getResponse(weakness,section_learn_stat,exam_freq)

            if hasattr(result, 'content'):
                # 如果是直接的LLM响应
                new_message.answer = result.content
                new_message.message_tokens = result.usage_metadata.get('input_tokens',0)
                new_message.answer_tokens = result.usage_metadata.get('output_tokens', 0)
                new_message.total_tokens = result.usage_metadata.get('total_tokens', 0)
            else:
                print("Not LLM Response")

            # 更新消息状态和内容
            new_message.status = 'normal'
            new_message.save()

            return {'answer': result.content}

        except Exception as e:
            # 错误处理
            new_message.status = 'failed'
            new_message.error = str(e)
            new_message.save()
            raise  # 重新抛出异常以便上层处理

    @classmethod
    def getResponse(cls,weakness_knowledge,section_learn_stat,exam_freq):

        llm = ChatOpenAI(
            openai_api_key = settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="doubao-1.5-pro-32k-250115",
        )

        template_obj = PromptTemp.objects.get(app_no="dsx_study_guideApp")

        template = template_obj.prompt_content

        prompt = PromptTemplate.from_template(template)

        input_data = {
            "weak_point" : weakness_knowledge,
            "lession_info" : section_learn_stat,
            "point_freq" : exam_freq,
        }

        llm_chain = prompt | llm

        result = llm_chain.invoke(input_data)

        return result
