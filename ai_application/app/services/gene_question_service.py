from app.api.dto import GeneQuestionDto
from app.models import Conversation, Account, App ,Message
import uuid
from django.db import transaction
from app.errors import AppNotFoundError, ConversationNotFoundError
import json
from app.constants.app import AppMode
from langchain_openai import ChatOpenAI
from django.conf import settings
from app.models.model import PromptTemplate as PromptTemp
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from app.models import GenerateQuestion

class GeneQuestionService:

    @classmethod
    def add_conversation(cls, dto: GeneQuestionDto,app_model,from_account: Account,) -> Conversation:
        """创建新对话并保存到数据库"""
        return Conversation.objects.create(
            conversation_no=str(uuid.uuid4()),
            app=app_model,
            app_model_config=app_model.app_model_config,
            name='genequestion_conversation',
            from_account=from_account,
            from_biz_id=dto.biz_id,
        )

    @classmethod
    @transaction.atomic
    def generate(
            cls,
            dto: GeneQuestionDto,
            from_account: Account,
    ) -> dict:
        """处理非流式消息生成并保存完整记录"""
        # 获取应用配置
        app_model = App.objects.filter(is_deleted=False, app_type="gene_question").first()
        if not app_model:
            raise AppNotFoundError()

        # 获取或创建对话
        if not dto.conversation_id:
            conversation = cls.add_conversation(dto, app_model, from_account)
        else:
            conversation = Conversation.objects.filter(
                conversation_no=dto.conversation_id
            ).first()
            if not conversation:
                raise ConversationNotFoundError()

        # 创建初始消息记录
        new_message = Message.objects.create(
            message_no=str(uuid.uuid4()),
            conversation=conversation,
            app=app_model,
            app_model_config=app_model.app_model_config,
            query=dto.model_dump_json(),  # 存储原始参数
            from_account=from_account
        )

        try:
            # 验证应用模式
            if app_model.mode != AppMode.COMPLETION.value:
                raise ValueError(f'Invalid app mode {app_model.mode}')

            chapter = dto.chapter
            knowledge = dto.knowledge
            subject_id = dto.subject_id



            result = GeneQuestionService.getResponse(subject_id, chapter, knowledge)

            # if hasattr(result, 'content'):
            #     # 如果是直接的LLM响应
            #     new_message.answer = result.content
            #     new_message.message_tokens = result.usage_metadata.get('input_tokens',0)
            #     new_message.answer_tokens = result.usage_metadata.get('output_tokens', 0)
            #     new_message.total_tokens = result.usage_metadata.get('total_tokens', 0)
            # else:
            #     print("Not LLM Response")

            # 更新消息状态和内容
            new_message.answer = result['result']
            new_message.status = 'normal'
            new_message.save()

            return {'content': result['result']}

        except Exception as e:
            # 错误处理
            new_message.status = 'failed'
            new_message.error = str(e)
            new_message.save()
            raise  # 重新抛出异常以便上层处理

    @classmethod
    def getResponse(cls, subject_id, chapter, knowledge):

        llm = ChatOpenAI(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
            model_name="doubao-1.5-pro-32k-250115",
        )

        template_obj = PromptTemp.objects.get(app_no="gene_question")
        template = template_obj.prompt_content
        prompt = PromptTemplate.from_template(template)
        parser = JsonOutputParser()

        subject_list_path = settings.BASE_DIR.joinpath('data_product_subject_subject.json')

        # 从根据subject_id读取subject_name
        with open(subject_list_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        subject_dict = {item['subject_code']: item['subject_name'] for item in data}
        subject = subject_dict.get(subject_id)

        input_data = {
            "subject": subject,
            "chapter": chapter,
            "knowledge": knowledge
        }

        llm_chain = prompt | llm | parser
        res = llm_chain.invoke(input_data)

        GenerateQuestion.objects.create(
            subject_id=subject_id,
            question=res["question"],
            options=res["options"],
            answer=res["answer"],
            analysis=res["explanation"],
            error_point=res["mistake"],
            knowledge=knowledge,
            chapter=chapter,
            main_subject="普通心理学"
        )
        print(res)

        return {'result':res}