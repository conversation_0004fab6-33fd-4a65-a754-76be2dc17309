import json
import time

from app.constants.app import AppMode
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate
from app.services.question_service import QuestionService
from django_ext.utils.string_utils import parse_int


class ChatQuestionJudgeFeature(BaseFeature):

    def __init__(self):
        compare_three_inputs = None
        compare_user_input = None
        prompt_templates = PromptTemplate.objects.filter(
            app_no__in=['compare_three_inputs', 'compare_user_input'])

        for p in prompt_templates:
            if p.app_no == 'compare_three_inputs':
                compare_three_inputs = p
            elif p.app_no == 'compare_user_input':
                compare_user_input = p
        if not compare_three_inputs or not compare_user_input:
            raise Exception('prompt template not found')

        self.compare_three_inputs_tp = compare_three_inputs
        self.compare_user_input_tp = compare_user_input

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            original_question_query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        # 判断结果是否完整
        if not self._judge_ocr_result(application_generate_entity, original_question_query, tracing_log):
            return None

        questions = self._query_three_questions(original_question_query, tracing_log)
        if not questions:
            return None

        question_info = self._compare_three_inputs(
            application_generate_entity, original_question_query, questions, tracing_log)
        if not question_info:
            return None

        return self._compare_user_input(
            application_generate_entity, original_question_query, question_info, tracing_log)

    def _query_three_questions(
            self,
            original_question_query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        start_time = time.perf_counter()

        questions = QuestionService.get_questions_by_vector(original_question_query, size=3)

        latency = time.perf_counter() - start_time

        tracing_log.add_tracing(
            tracing_type='query_three_questions',
            query=original_question_query,
            answer=json.dumps(questions, ensure_ascii=False),
            latency=latency,
        )

        return questions

    def _judge_ocr_result(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            original_question_query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        user_inputs = application_generate_entity.inputs.copy()
        user_inputs['ocr_text'] = original_question_query

        # 目前只在demo上体现
        is_debug = user_inputs.get('is_debug', False)
        if not is_debug:
            return True

        debug_question_prompts = user_inputs.get('debug_question_prompts')
        prompt_content = debug_question_prompts.get('evaluate_ocr_text_completeness') if debug_question_prompts else None
        if not prompt_content:
            return True

        prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no='evaluate_ocr_text_completeness').first()
        if not prompt_template:
            return True

        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=prompt_content
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        result = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=False
        )

        if tracing_log:
            tracing_log.add_tracing_by_llm_result(
                tracing_type='evaluate_ocr_text_completeness',
                query=original_question_query,
                llm_result=result
            )

        answer_int = parse_int(result.message.content, d=-1)
        return 1 <= answer_int <= 3

    def _compare_three_inputs(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            original_question_query: str,
            questions: list,
            tracing_log: MessageTracingLog | None = None
    ) -> dict | None:
        """
        :param application_generate_entity:
        :param original_question_query:
        :param questions:
        [{
            'question': 完整题干和选项,
            'analysis': 完整答案和解析,
        }]
        :return:
        """
        if not questions:
            return None

        # TODO 此处需要处理检索出来的题目不足3道，目前先暂时取第一道题目
        if len(questions) < 3:
            return questions[0]

        user_inputs = application_generate_entity.inputs.copy()
        query_dict = {
            'user_question': original_question_query,
            'first_title_combined': questions[0]['question'],
            'second_title_combined': questions[1]['question'],
            'third_title_combined': questions[2]['question'],
        }
        user_inputs.update(query_dict)

        prompt_template = self.compare_three_inputs_tp

        prompt_content = None
        is_debug = user_inputs.get('is_debug', False)
        if is_debug:
            debug_question_prompts = user_inputs.get('debug_question_prompts')
            prompt_content = debug_question_prompts.get('compare_three_inputs') if debug_question_prompts else None

        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=prompt_content
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        result = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=False
        )

        if tracing_log:
            tracing_log.add_tracing_by_llm_result(
                tracing_type='compare_three_inputs',
                query=json.dumps(query_dict, ensure_ascii=False),
                llm_result=result
            )

        answer_int = parse_int(result.message.content, d=-1)
        if 1 <= answer_int <= 3:
            return questions[answer_int - 1]
        return None

    def _compare_user_input(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            original_question_query: str,
            question_info: dict,
            tracing_log: MessageTracingLog | None = None
    ):
        """
        :param application_generate_entity:
        :param question_info:
        {
            'question': 完整题干和选项,
            'analysis': 完整答案和解析,
        }
        :param tracing_log:
        :return:
        """
        user_inputs = application_generate_entity.inputs.copy()
        query_dict = {
            'user_question': original_question_query,
            'title_combined': question_info['question'],
        }
        user_inputs.update(query_dict)

        prompt_template = self.compare_user_input_tp

        prompt_content = None
        is_debug = user_inputs.get('is_debug', False)
        if is_debug:
            debug_question_prompts = user_inputs.get('debug_question_prompts')
            prompt_content = debug_question_prompts.get('compare_three_inputs') if debug_question_prompts else None

        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=prompt_content,
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        result = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
            stream=False
        )

        if tracing_log:
            tracing_log.add_tracing_by_llm_result(
                tracing_type='compare_user_input',
                query=json.dumps(query_dict, ensure_ascii=False),
                llm_result=result
            )

        answer_int = parse_int(result.message.content, d=-1)
        if answer_int > -1:
            return question_info
        return None
