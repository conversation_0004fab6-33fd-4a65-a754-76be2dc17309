import re
from django.conf import settings
from langchain.chains.llm import LL<PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate

# 初始化大模型
llm = ChatOpenAI(
    openai_api_key=settings.DOUBAO_API_KEY,
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-pro-256k-241115",
)

correct_prompt_template = """
你是一个专业的数据结构领域字幕文本校对器。

请对以下视频字幕内容进行修正，任务包括：
1. 纠正所有错别字；
2. 修正所有不符合数据结构学术领域的术语，例如“数据解构”应改为“数据结构”；
3. 输出必须为完整、连贯、纠正后的字幕文本；
4. 严禁添加任何解释、说明、注释、标点补充或“此文本没有错别字”等内容；
5. 如果字幕文本本身无误，也必须**逐字原样输出**，不要添加任何多余内容。

示例：
输入：二叉数是一种数据解构类型  
输出：二叉树是一种数据结构类型

限制：
输出必须只包含修改后的文本，不准添加任何额外的信息！
不允许添加任何注解，也不要解释修改的原因！

现在请对以下字幕文本进行纠错：
{text}
"""

prompt_obj = PromptTemplate(
    input_variables=["text"],
    template=correct_prompt_template
)

chain = LLMChain(llm=llm, prompt=prompt_obj)

# Step1: 读取 SRT 文件
def parse_srt(srt_text):
    pattern = r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3})\n(.+?)(?=\n{2,}|\Z)'
    matches = re.findall(pattern, srt_text, flags=re.DOTALL)
    parsed = [{"index": m[0], "time": m[1], "text": m[2].replace('\n', ' ')} for m in matches]
    return parsed

# Step2: 调用大模型进行纠错
def correct_text(text):
    result = chain.run(text=text)
    return result.strip()

# Step3: 写入新 SRT 文件
def format_srt(blocks):
    result = ""
    for block in blocks:
        result += f"{block['index']}\n{block['time']}\n{block['text']}\n\n"
    return result.strip()

# 主函数：自动纠正错别字
def correct_srt_file(input_path, output_path):
    with open(input_path, "r", encoding="utf-8") as f:
        srt_text = f.read()

    blocks = parse_srt(srt_text)

    for block in blocks:
        corrected = correct_text(block["text"])
        block["text"] = corrected

    new_srt_text = format_srt(blocks)

    with open(output_path, "w", encoding="utf-8") as f:
        f.write(new_srt_text)

    print(f"✅ 已纠错并保存到：{output_path}")

