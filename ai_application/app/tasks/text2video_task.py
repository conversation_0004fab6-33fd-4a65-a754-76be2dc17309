import logging

from celery import shared_task
from django.utils import timezone

from app.services.text_to_video import process_knowledge
from app.models import KnowledgeVideo

logger = logging.getLogger(__name__)


# @shared_task(queue='message_task')
def auto_check_process_know_video_task():
    time_point = timezone.now() - timezone.timedelta(minutes=1)
    # 一分钟后还未开始支持的任务自动执行
    tasks = KnowledgeVideo.objects.filter(
        is_deleted=False,
        status='not_start',
        add_time__lt=time_point
    )[:10]
    for task in tasks:
        # delay_process_know_video_task.delay(task.id)
        delay_process_know_video_task(task.id)

    # 处理超时
    time_point = timezone.now() - timezone.timedelta(minutes=30)
    KnowledgeVideo.objects.filter(
        is_deleted=False,
        status__in=['not_start', 'processing'],
        add_time__lt=time_point
    ).update(
        status='fail',
        fail_reason='timeout',
        modified_time=timezone.now()
    )


# @shared_task(queue='message_task')
def delay_process_know_video_task(video_id):
    task: KnowledgeVideo = KnowledgeVideo.objects.filter(
        is_deleted=False,
        id=video_id,
        status='not_start'
    ).first()
    if not task:
        return

    task.status = 'processing'
    task.save(update_fields=['status', 'modified_time'])

    try:
        url = process_knowledge(task.core_course_name,{'name': task.knowledge_name})

        task.status = 'success'
        task.video = url
        task.save(update_fields=['status', 'video'])
    except Exception as e:
        logger.exception(e)
        logger.error(f'process_knowledge failed, task_id: {video_id}')

        task.status = 'fail'
        task.fail_reason = str(e)
        task.save(update_fields=['status', 'fail_reason'])
