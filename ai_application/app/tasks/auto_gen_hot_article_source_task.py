import json
import time

from celery import shared_task
from django.conf import settings
from django.db import transaction

from api_client.yantucs_bk.client import yantucs_client
from app.core.rag.models.document import Document
from app.core.rag.splitter.text_splitter import RecursiveCharacterTextSplitter
from app.models import HotArticleSource, HotArticleSegment
from django_ext.utils.string_utils import parse_int


@shared_task(queue='hot_article')
def auto_gen_hot_article_source_task():
    # 暂时非正式环境不处理
    if settings.ENVIRONMENT != settings.ENV_PRODUCT:
        return

    # 处理增量数据
    media_contents = _bulk_get_media_contents()

    doc_contents = _bulk_get_doc_contents()
    all_contents = [*media_contents, *doc_contents]
    with transaction.atomic():
        for i in all_contents:
            segment_objs_ = []
            article_source = HotArticleSource.objects.create(
                source=i['source'],
                status=i['status'],
                biz_id=i['biz_id'],
                content=i['content']
            )
            for s in i['segments']:
                segment_objs_.append(HotArticleSegment(
                    source=article_source,
                    content=s,
                    word_count=len(s),
                ))
            HotArticleSegment.objects.bulk_create(segment_objs_)

    # 处理旧数据
    _update_no_content_article_source()


def _update_no_content_article_source():
    no_content_source_qs = HotArticleSource.objects.filter(status=HotArticleSource.Status.no_content)
    for s in no_content_source_qs:
        if s.source == HotArticleSource.Source.media_subtitle:
            segment_contents = _get_media_contents_by_biz_ids([s.biz_id])
        elif s.source == HotArticleSource.Source.document:
            segment_contents = _get_doc_contents_by_biz_ids([s.biz_id])
        else:
            continue

        for i in segment_contents:
            if i['status'] == HotArticleSource.Status.success:
                segment_objs_ = []
                for segment_str in i['segments']:
                    segment_objs_.append(HotArticleSegment(
                        source=s,
                        content=segment_str,
                        word_count=len(segment_str),
                    ))
                HotArticleSegment.objects.bulk_create(segment_objs_)

        # 舒缓一下
        time.sleep(0.1)


def _bulk_get_media_contents():
    last_video_subtitle_id = _get_last_video_subtitle_id()
    res = yantucs_client.bulk_query_video_subtitle(last_video_subtitle_id)
    file_list = res.get('file_list')
    return _parse_media_subtitle_contents(file_list)


def _get_media_contents_by_biz_ids(biz_ids: list):
    file_ids = []
    for i in biz_ids:
        biz_id_int = parse_int(i)
        if biz_id_int:
            file_ids.append(biz_id_int)
    res = yantucs_client.get_video_subtitle_by_file_ids(file_ids)
    file_list = res.get('file_list')
    return _parse_media_subtitle_contents(file_list)


def _parse_media_subtitle_contents(file_list: list) -> list:
    media_contents = []
    for f in file_list:
        subtitles = f.get('subtitle')
        media_item = {
            'source': HotArticleSource.Source.media_subtitle,
            'status': HotArticleSource.Status.no_content,
            'biz_id': str(f.get('file_id', '')),
            'content': '',
            'segments': [],
        }

        if f.get('is_ok') and subtitles:
            media_item['content'] = json.dumps(subtitles, ensure_ascii=False)
            media_item['status'] = HotArticleSource.Status.success
            media_segments = _cut_media_subtitle(subtitles)
            for i in media_segments:
                media_item['segments'].append(i)

        media_contents.append(media_item)
    return media_contents


def _bulk_get_doc_contents():
    last_doc_id = _get_last_doc_id()
    res = yantucs_client.bulk_query_doc_content(last_doc_id)
    file_list = res.get('file_list')
    return _parse_doc_contents(file_list)


def _get_doc_contents_by_biz_ids(biz_ids: list):
    file_ids = []
    for i in biz_ids:
        biz_id_int = parse_int(i)
        if biz_id_int:
            file_ids.append(biz_id_int)
    res = yantucs_client.get_video_subtitle_by_file_ids(file_ids)
    file_list = res.get('file_list')
    return _parse_media_subtitle_contents(file_list)


def _parse_doc_contents(file_list: list):
    doc_contents = []
    for f in file_list:
        contents = f.get('content')
        doc_item = {
            'source': HotArticleSource.Source.document,
            'status': HotArticleSource.Status.no_content,
            'biz_id': str(f.get('file_id', '')),
            'content': '',
            'segments': [],
        }

        if f.get('is_ok') and contents:
            doc_item['content'] = json.dumps(contents, ensure_ascii=False)
            doc_item['status'] = HotArticleSource.Status.success
            doc_segments = _cut_doc_content(contents)
            for i in doc_segments:
                doc_item['segments'].append(i)

        doc_contents.append(doc_item)
    return doc_contents


def _cut_media_subtitle(subtitle: list) -> list:
    subtitle_str = '\n'.join(subtitle)
    document = Document(page_content=subtitle_str)
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=300,
        chunk_overlap=0,
        separators=["\n"],
    )
    splits = text_splitter.split_documents([document])
    return [i.page_content for i in splits]


def _cut_doc_content(doc_content_arr: list) -> list:
    subtitle_str = '\n'.join([i['chapter_content'] for i in doc_content_arr])
    document = Document(page_content=subtitle_str)
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=500,
        chunk_overlap=0,
        separators=["\n", "。", ". ", " ", ""],
    )
    splits = text_splitter.split_documents([document])
    return [i.page_content for i in splits]


def _get_last_video_subtitle_id() -> int:
    last_source: HotArticleSource = HotArticleSource.objects.filter(
        source=HotArticleSource.Source.media_subtitle
    ).last()
    return parse_int(last_source.biz_id) if last_source else 0


def _get_last_doc_id() -> int:
    last_source: HotArticleSource = HotArticleSource.objects.filter(
        source=HotArticleSource.Source.document
    ).last()
    return parse_int(last_source.biz_id) if last_source else 0
