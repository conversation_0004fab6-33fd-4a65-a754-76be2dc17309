import datetime
import decimal
from collections import defaultdict

import pandas as pd

from app.models import (
    ExamAnalysisExamQuestion, ExamAnalysisKnowledgePointAnalysis, ExamAnalysisKnowledgePoint,
    ExamAnalysisKnowledgePointWithStats
)
from django_ext import base_view
from django_ext.response import make_response


class ExamAnalysisSubjectView(base_view.BaseView):

    def get(self, request, *args, **kwargs):
        subject_name_map = defaultdict(list)
        subject_year_range = defaultdict(set)
        subject_disciplines = defaultdict(set)
        subject_year_difficulty = defaultdict(lambda: defaultdict(list))
        years = set()
        for question in ExamAnalysisExamQuestion.objects.filter(
                is_deleted=False, subject_code__enable=True
        ).select_related('subject_code'):
            x = subject_name_map[question.subject_code.subject_name]
            if question.subject_code not in x:
                x.append(question.subject_code)

            years.add(question.year)
            subject_year_range[question.subject_code_id].add(question.year)
            subject_disciplines[question.subject_code_id].add(question.subject)
            subject_year_difficulty[question.subject_code.subject_name][question.year].append(question.difficulty)

        idx = 0
        data = []
        for subject_name, sub_subject in subject_name_map.items():
            idx += 1
            sub_subject_list = []
            for subject in sub_subject:
                min_year = min(subject_year_range.get(subject.id, [2000]))
                max_year = max(subject_year_range.get(subject.id, [datetime.datetime.now().year]))
                sub_subject_list.append({
                    'id': subject.id,
                    'subject_code': subject.subject_code,
                    'subject_name': subject.subject_code,
                    'year_range': [min_year, max_year],
                    'disciplines': list(subject_disciplines[subject.id])
                })
            data.append({
                'id': idx,
                'subject_name': subject_name,
                'sub_subject': sub_subject_list,
            })

        subject_year_data = defaultdict(list)
        year_list = list(sorted(list(years)))
        for subject_name in subject_name_map.keys():
            for year in year_list:
                diff_list = subject_year_difficulty[subject_name].get(year, [])
                ave_diff = round(sum(diff_list) / len(diff_list), 2) if diff_list else None
                subject_year_data[subject_name].append(ave_diff)

        return make_response({
            'subject_list': data,
            'subject_year_difficulty': {
                'year_list': year_list,
                'subject_year_data': subject_year_data,
            },
            'subject_year_with_question': {s: list(sorted(ys.keys())) for s, ys in subject_year_difficulty.items()}
        })


class ExamAnalysisDataWordCloudView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        subject_code = request.data.get('subject_code', '408')
        discipline = request.data.get('discipline')

        # 获取请求参数中的年份范围
        start_year = request.data.get('start_year')
        end_year = request.data.get('end_year')

        queryset = ExamAnalysisExamQuestion.objects.filter(
            is_deleted=False, subject_code__subject_code=subject_code, subject=discipline
        )
        if start_year and end_year:
            queryset = queryset.filter(year__gte=start_year, year__lte=end_year)

        kp_dict = defaultdict(int)
        for item in queryset.all():
            for kp in item.knowledge_points:
                kp_dict[kp] += 1

        # 按次数排序取前300个
        kp_weight_list = [[k, count] for k, count in kp_dict.items()]
        kp_weight_list.sort(key=lambda x: x[1], reverse=True)

        return make_response({
            'data': kp_weight_list[:200]
        })


class ExamAnalysisDataHeatMapView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        subject_code = request.data.get('subject_code', '408')
        discipline = request.data.get('discipline')

        # 获取请求参数中的年份范围
        start_year = request.data.get('start_year')
        end_year = request.data.get('end_year')

        queryset = ExamAnalysisKnowledgePointAnalysis.objects.filter(
            is_deleted=False, subject_code__subject_code=subject_code, subject=discipline
        )
        if start_year and end_year:
            queryset = queryset.filter(year__gte=start_year, year__lte=end_year)

        data = []
        xs = set()
        ys = defaultdict(int)
        for item in queryset.all():
            xs.add(item.year)
            ys[item.point_name] += item.question_count
            data.append([
                item.year,
                item.point_name,
                item.question_count,
            ])

        ys = [(k, v) for k, v in ys.items()]
        ys.sort(key=lambda x: x[1])

        return make_response({
            'data': data,
            'x_axis': list(sorted(list(xs))),
            'y_axis': [i[0] for i in ys],
            'min': min(data, key=lambda x: x[2])[2] if data else 0,
            'max': max(data, key=lambda x: x[2])[2] if data else 0,
        })


class ExamAnalysisDataKpStatsView(base_view.BaseView):

    @staticmethod
    def load_data(subject_code, sub_subject, start_year, end_year):
        # 从数据库加载知识点
        knowledge_points = {}
        for point in ExamAnalysisKnowledgePoint.objects.filter(subject_code__subject_code=subject_code, subject=sub_subject):
            if point.subject not in knowledge_points:
                knowledge_points[point.subject] = []
            knowledge_points[point.subject].append(point.point_name)

        # 从数据库加载分析结果
        analysis_data = defaultdict(lambda: defaultdict(dict))
        query = ExamAnalysisKnowledgePointAnalysis.objects.filter(subject_code__subject_code=subject_code, subject=sub_subject)
        if start_year and end_year:
            query = query.filter(year__gte=start_year, year__lte=end_year)

        for item in query:
            analysis_data[item.year][item.subject][item.point_name] = {
                '考查题目数': item.question_count,
                '题号难度': item.question_difficulty
            }
        return knowledge_points, analysis_data

    @staticmethod
    def calculate_stats(knowledge_points, analysis_data, subject_code, sub_subject):
        stats = {
            subject: {
                point: {
                    'years': defaultdict(int),
                    'difficulties': [],
                    'choice_questions': 0,
                    'comprehensive_questions': 0
                } for point in points
            } for subject, points in knowledge_points.items()
        }

        question_map = defaultdict(list)
        for q in ExamAnalysisExamQuestion.objects.filter(
            subject_code__subject_code=subject_code,
            subject=sub_subject,
        ):
            question_map[f'{q.year}-{q.question_number}'].append(q)

        for year, subjects in analysis_data.items():
            for subject, points in subjects.items():
                for point, data in points.items():
                    stats[subject][point]['years'][year] = data['考查题目数']
                    for q_num, difficulty in data['题号难度'].items():
                        stats[subject][point]['difficulties'].append(difficulty)
                        # 从ExamQuestion获取题目类型
                        if question := question_map.get(f'{year}-{q_num}', [0])[0]:
                            if question.question_type == '选择题':
                                stats[subject][point]['choice_questions'] += 1
                            else:
                                stats[subject][point]['comprehensive_questions'] += 1
        return stats

    @staticmethod
    def generate_tables(stats, start_year=None, end_year=None):
        tables = {}
        # 确定年份范围
        if start_year is None or end_year is None:
            # 如果没有指定年份范围，则使用所有可用年份
            all_years = set()
            for subject, points in stats.items():
                for point, data in points.items():
                    all_years.update(data['years'].keys())
            if not all_years:
                all_years = {'2009'}  # 默认值
            min_year = min(int(year) for year in all_years)
            max_year = max(int(year) for year in all_years)
        else:
            min_year = int(start_year)
            max_year = int(end_year)

        for subject, points in stats.items():
            table_data = []
            for point, data in points.items():
                # 处理难度数据为列表形式
                difficulties = sorted(list(set(data['difficulties']))) if data['difficulties'] else [0]

                row = {
                    '知识点': point,
                    '考查次数': sum(data['years'].values()),
                    '难度范围': difficulties,
                    '难度平均值': round(sum(data['difficulties']) / len(data['difficulties']), 2) if data[
                        'difficulties'] else 0,
                    '选择题数量': data['choice_questions'],
                    '综合题数量': data['comprehensive_questions']
                }
                # 添加每年考查次数，未考到的设为0
                for year in range(min_year, max_year + 1):
                    row[f"{year}年"] = data['years'].get(str(year), 0)

                table_data.append(row)
            tables[subject] = table_data

        cols = ['知识点', '考查次数', '难度范围', '难度平均值', '选择题数量', '综合题数量']
        years_cols = [f"{year}年" for year in range(min_year, max_year + 1)]
        return {
            'tables': tables,
            'cols': cols,
            'years_cols': years_cols,
        }

    def post(self, request, *args, **kwargs):
        subject_code = request.data.get('subject_code', '408')
        discipline = request.data.get('discipline')

        # 获取请求参数中的年份范围
        start_year = request.data.get('start_year')
        end_year = request.data.get('end_year')

        knowledge_points, analysis_data = self.load_data(subject_code, discipline, start_year, end_year)
        stats = self.calculate_stats(knowledge_points, analysis_data, subject_code, discipline)
        tables_data = self.generate_tables(stats, start_year, end_year)

        data = tables_data['tables'].get(discipline, [])
        data.sort(key=lambda x: -1 * x['考查次数'])

        return make_response({
            'cols': tables_data['cols'],
            'years_cols': tables_data['years_cols'],
            'data': data,
        })


class ExamAnalysisDataScoreDisView(base_view.BaseView):

    @staticmethod
    def get_multi_year_analysis(subject_code):
        queryset = ExamAnalysisExamQuestion.objects.filter(subject_code__subject_code=subject_code)
        rows = []
        for question in queryset:
            # 处理学科字段，拆分为多个学科
            subjects = [s.strip() for s in question.subject.split(',')]
            for subject in subjects:
                rows.append({
                    '年份': question.year,
                    '题号': question.question_number,
                    '题干': question.content,
                    '题型': question.question_type,
                    '学科': subject,
                    '知识点': ', '.join(question.knowledge_points),
                    '难度': question.difficulty,
                    '分值': question.score
                })
        return pd.DataFrame(rows)

    def post(self, request, *args, **kwargs):
        subject_code = request.data.get('subject_code', '408')
        discipline = request.data.get('discipline')

        # 获取请求参数中的年份范围
        start_year = request.data.get('start_year')
        end_year = request.data.get('end_year')

        data = self.get_multi_year_analysis(subject_code)
        subject_data = data[data['学科'] == discipline]
        # 根据年份范围过滤数据
        if start_year and end_year:
            subject_data = subject_data[(subject_data['年份'] >= str(start_year)) & (subject_data['年份'] <= str(end_year))]

        # 计算当前学科每个难度等级的总分值和比例
        subject_stats = subject_data.groupby('难度').agg(
            total_score=('分值', 'sum'),
            count=('分值', 'count')
        ).reset_index()

        # 计算当前学科总分值
        total_score_subject = subject_stats['total_score'].sum()

        # 计算比例
        subject_stats['score_percentage'] = round(subject_stats['total_score'] * 100 / total_score_subject, 2)
        data = {
            '难度': subject_stats['难度'].values,
            'score_percentage': [round(d, 2) for d in subject_stats['score_percentage'].values],
        }
        return make_response(data)


class ExamAnalysisDataKpDifficultyView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        subject_code = request.data.get('subject_code', '408')
        discipline = request.data.get('discipline')

        # 获取请求参数中的年份范围
        start_year = request.data.get('start_year')
        end_year = request.data.get('end_year')

        queryset = ExamAnalysisExamQuestion.objects.filter(is_deleted=False, subject_code__subject_code=subject_code)
        if discipline:
            queryset = queryset.filter(subject=discipline)
        if start_year and end_year:
            queryset = queryset.filter(year__gte=start_year, year__lte=end_year)

        difficulty_kp_map = defaultdict(list)
        difficulty_question_map = defaultdict(list)
        for question in queryset.all():
            difficulty_question_map[question.difficulty].append(question)
            difficulty_kp_map[question.difficulty].extend(question.knowledge_points)

        data = []
        for diff in list(sorted(difficulty_kp_map.keys())):
            question_type_dict = defaultdict(int)
            for question in difficulty_question_map.get(diff, []):
                question_type_dict[question.question_type] += 1
            row = {
                'difficulty': diff,
                'kp_list': ','.join(list(set(difficulty_kp_map.get(diff, [])))),
                'question_type': question_type_dict,
            }
            data.append(row)
        return make_response(data)


class ExamAnalysisDataTotalView(base_view.BaseView):

    def post(self, request, *args, **kwargs):
        subject_code = request.data.get('subject_code', '408')
        start_year = request.data.get('start_year')
        end_year = request.data.get('end_year')

        data = {
            'subject_difficulty': self.get_subject_difficulty(subject_code),
            'score_difficulty_distribution': self.get_score_difficulty_distribution(subject_code, start_year, end_year),
            'yearly_subject_difficulty': self.get_yearly_subject_difficulty(subject_code, start_year, end_year),
        }
        return make_response(data)

    @staticmethod
    def get_subject_difficulty(subject_code):
        # 各学科难度分布箱线图
        queryset = ExamAnalysisKnowledgePointWithStats.objects.filter(subject_code__subject_code=subject_code)

        data = defaultdict(list)
        for item in queryset:
            if item.avg_difficulty == 0:
                continue
            data[item.subject].append(item.avg_difficulty)

        return data

    @staticmethod
    def get_score_difficulty_distribution(subject_code, start_year, end_year):
        data = ExamAnalysisDataScoreDisView.get_multi_year_analysis(subject_code)
        if start_year and end_year:
            data = data[
                (data['年份'] >= str(start_year)) & (data['年份'] <= str(end_year))]

        # 计算当前学科每个难度等级的总分值和比例
        subject_stats = data.groupby('难度').agg(
            total_score=('分值', 'sum'),
            count=('分值', 'count')
        ).reset_index()

        # 计算当前学科总分值
        total_score_subject = subject_stats['total_score'].sum()
        # 计算比例
        subject_stats['score_percentage'] = round(subject_stats['total_score'] * 100 / total_score_subject, 2)

        data = {
            '难度': subject_stats['难度'].values,
            'score_percentage': [round(d, 2) for d in subject_stats['score_percentage'].values],
        }
        return data

    @staticmethod
    def get_yearly_subject_difficulty(subject_code, start_year, end_year):
        queryset = ExamAnalysisExamQuestion.objects.filter(subject_code__subject_code=subject_code)
        if start_year and end_year:
            queryset = queryset.filter(year__gte=start_year, year__lte=end_year)

        data = defaultdict(lambda: defaultdict(list))
        for question in queryset:
            weight_difficulty = question.difficulty * question.score / question.score
            data[question.subject][question.year].append(weight_difficulty)

        years = set()
        for year_data in data.values():
            for year in year_data.keys():
                years.add(year)
        years = list(sorted(list(years)))

        yearly_subject_difficulty = defaultdict(list)
        for subject, year_data in data.items():
            for year in years:
                d = year_data.get(year, [0])
                yearly_subject_difficulty[subject].append(round(sum(d) / len(d), 2))

        return {
            'years': years,
            'yearly_subject_difficulty': yearly_subject_difficulty,
        }

