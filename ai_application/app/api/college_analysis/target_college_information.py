import requests
import json

from django.conf import settings


def get_target_college_information(college_major_code):
    if settings.ENVIRONMENT == settings.ENV_PRODUCT:
        host = 'http://yantucs-data-svc:8000'
    else:
        host = 'https://yantucs-data.yantucs.com'
    url = f'{host}/internal_api/v1/college/kaoyan_learn_query/'

    if not isinstance(college_major_code, list):
        raise ValueError("college_major_code 应该是一个列表")

    choice = college_major_code[0]
    major_code = choice.get('major_code')
    college_code = choice.get('college_code')

    if not major_code or not college_code:
        raise ValueError("major_code 和 college_code 不能为空")

    payload = json.dumps({
        "school_code": college_code or "",
        "major_code": major_code or ""
    })
    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    if response.status_code == 200:
        response_data = response.json()
        if response_data.get("code") == 20000:
            data = response_data.get("data")
            if not data:
                print("接口返回空数据")
                return {}
            # print(f"筛选出的数据:{data}")
            return translate_data(data)
        else:
            print(f"接口返回错误: {response_data.get('msg')}")
            return {}  # 直接返回空字典并继续执行
    else:
        print(f"请求失败: {response.status_code} {response.text}")
        return {}  # 直接返回空字典并继续执行




def translate_data(data):
    if not data:
        print("数据为空，无法翻译")
        return {}

    # 翻译主键
    translation_map = {
        "exam_range": "考试范围",
        "major_score_info": "专业复试分数线",
        "major_re_exam_info": "专业复试平均分",
        "major_enroll_info": "专业拟录取平均分",
        "re_exam_num": "复试人数",
        "enroll_num": "拟录取人数"
    }

    # 翻译嵌套键
    score_info_translation_map = {
        "special_plan": "专项计划",
        "score_sys_display": "制分",
        "polity": "政治分数线",
        "language": "外语分数线",
        "subject1": "科目一分数线",
        "subject2": "科目二分数线",
        "total": "总分数线",
        "lowest": "最低分",
        "highest": "最高分",
        "average": "平均分"
    }

    # 翻译 exam_range 中的键
    exam_range_translation_map = {
        "polity": "政治",
        "language": "外语",
        "subject1": "科目一",
        "subject2": "科目二"
    }

    # 翻译主键
    translated_data = {translation_map.get(k, k): v for k, v in data.items()}

    # 翻译 exam_range
    if translated_data.get("考试范围") is not None:
        translated_data["考试范围"] = [
            {exam_range_translation_map.get(k, k): v for k, v in item.items()}
            for item in translated_data["考试范围"]
        ]

    # 翻译 major_score_info
    if translated_data.get("专业复试分数线") is not None:
        translated_data["专业复试分数线"] = {
            score_info_translation_map.get(k, k): v for k, v in translated_data["专业复试分数线"].items()
        }

    # 翻译 major_re_exam_info
    if translated_data.get("专业复试平均分") is not None:
        translated_data["专业复试平均分"] = {
            score_info_translation_map.get(k, k): v for k, v in translated_data["专业复试平均分"].items()
        }

    # 翻译 major_enroll_info
    if translated_data.get("专业拟录取平均分") is not None:
        translated_data["专业拟录取平均分"] = {
            score_info_translation_map.get(k, k): v for k, v in translated_data["专业拟录取平均分"].items()
        }

    return translated_data


