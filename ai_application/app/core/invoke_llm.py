import logging
import time
from typing import Generator

from django.conf import settings
from langchain_core.messages import AIMessage
from langchain_core.outputs import LLMResult as LC_LLMResult

from app.constants.app import AppMode
from app.core.entities.app_entities import ModelConfigEntity
from app.core.langchain_openai_thinking import Cha<PERSON><PERSON><PERSON><PERSON><PERSON>hinking
from app.core.model_manager import ModelInstance
from app.core.model_provider_manager import ModelProviderManager
from app.core.model_runtime.entities.llm_entities import LLMResult, LLMUsage, LLMResultChunk, LLMResultChunkDelta
from app.core.model_runtime.entities.provider_entities import ModelType
from app.core.prompt.conversation_buffer_memory import ConversationBufferMemory
from app.core.prompt.entities import PromptMessage, AssistantPromptMessage
from app.core.prompt.simple_prompt_transform import SimplePromptTransform
from app.core.utils.latex_utils import replace_latex
from app.errors import ContainSensitiveError
from app.models import PromptTemplate

logger = logging.getLogger(__name__)


def get_llm_model_config(
        app_model_conf: ModelConfigEntity | None = None,
        prompt_template: PromptTemplate | None = None,
        custom_model_conf: dict | None = None
) -> ModelConfigEntity:
    if not prompt_template and not custom_model_conf:
        return app_model_conf

    model_provider = ''
    model_id = ''
    model_params = None

    if prompt_template:
        model_provider = prompt_template.model_provider
        model_id = prompt_template.model_id
        model_params = prompt_template.model_params

    if isinstance(custom_model_conf, dict) and custom_model_conf:
        if 'model_provider' in custom_model_conf:
            model_provider = custom_model_conf['model_provider']
        if 'model_id' in custom_model_conf:
            model_id = custom_model_conf['model_id']
        if 'model_params' in custom_model_conf:
            model_params = custom_model_conf['model_params']

    if not (model_provider and model_id and model_params):
        return app_model_conf

    provider_model_bundle = ModelProviderManager().get_provider_model_bundle(
        provider=model_provider,
        model_type=ModelType.LLM
    )
    model_schema = provider_model_bundle.model_type_instance.get_model_schema(model_id)
    return ModelConfigEntity(
        provider=model_provider,
        model=model_id,
        model_schema=model_schema,
        provider_model_bundle=provider_model_bundle,
        model_params=model_params,
    )


def query_llm_by_prompt(
        model_conf: ModelConfigEntity,
        app_mode: AppMode,
        pre_prompt: str,
        query: str,
        files: list | None = None,
        memory: ConversationBufferMemory | None = None,
        history_messages: list[PromptMessage] | None = None,
        query_in_prompt: bool = False,
        tools: list[str] | None = None,
        stream: bool = False,
) -> LLMResult | Generator:
    prompt_messages = SimplePromptTransform().get_prompt(
        app_mode=app_mode,
        pre_prompt=pre_prompt,
        query=query,
        memory=memory,
        files=files,
        history_messages=history_messages,
        query_in_prompt=query_in_prompt,
    )

    model_instance = ModelInstance(
        provider_model_bundle=model_conf.provider_model_bundle,
        model=model_conf.model
    )

    invoke_result = model_instance.invoke_llm(
        prompt_messages=prompt_messages,
        model_parameters=model_conf.model_params,
        tools=tools,
        stream=stream
    )
    return invoke_result


def query_llm_blocking(
        model_conf: ModelConfigEntity,
        app_mode: AppMode,
        pre_prompt: str,
        query: str,
        memory: ConversationBufferMemory | None = None,
        history_messages: list[PromptMessage] | None = None,
        query_in_prompt: bool = False,
) -> LLMResult:
    try:
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=app_mode,
            pre_prompt=pre_prompt,
            query=query,
            memory=memory,
            history_messages=history_messages,
            query_in_prompt=query_in_prompt,
            stream=False
        )
    except Exception as e:
        prompt_messages = SimplePromptTransform().get_prompt(
            app_mode=app_mode,
            pre_prompt=pre_prompt,
            query=query,
            memory=memory,
            history_messages=history_messages,
            query_in_prompt=query_in_prompt,
        )

        llm_result = LLMResult(
            model_provider=model_conf.provider,
            model=model_conf.model,
            message=AssistantPromptMessage(),
            prompt_messages=prompt_messages,
            usage=LLMUsage.empty_usage(),
            is_success=False,
        )
        if isinstance(e, ContainSensitiveError):
            llm_result.message.content = settings.LLM_SENSITIVE_ANSWER
            llm_result.is_sensitive = True
        else:
            llm_result.message.content = settings.LLM_NETWORK_ERROR_ANSWER
        return llm_result


class LangChainLlmWrapper:

    def __init__(
            self,
            model_provider: str,
            model_id: str,
            model_params=None
    ):
        if model_params is None:
            model_params = {}
        self.model_provider = model_provider
        self.model_id = model_id
        self.model_params = model_params

        temperature = model_params.get('temperature')
        max_tokens = model_params.get('max_tokens')

        # 目前只处理豆包
        self.llm = ChatOpenAIThinking(
            openai_api_key=settings.DOUBAO_API_KEY,
            openai_api_base=settings.DOUBAO_API_BASE,
            model_name=model_id,
            temperature=temperature,
            max_tokens=max_tokens,
            model_kwargs={
                'stream_options': {"include_usage": True}
            },
        )

    def get_llm(self):
        return self.llm

    def handle_non_stream_response(
            self,
            lc_result: LC_LLMResult | AIMessage,
            latency: float
    ):
        if isinstance(lc_result, LC_LLMResult):
            full_answer = lc_result.generations[0][0].text

            additional_kwargs = lc_result.generations[0][0].message.additional_kwargs
            reasoning_content = additional_kwargs.get('reasoning_content', '')

            token_usage = lc_result.llm_output.get("token_usage", {})
            message_tokens = token_usage.get("prompt_tokens", 0)
            answer_tokens = token_usage.get("completion_tokens", 0)
            total_tokens = token_usage.get("total_tokens", 0)
        else:
            full_answer = lc_result.content

            additional_kwargs = lc_result.additional_kwargs
            reasoning_content = additional_kwargs.get('reasoning_content', '')

            token_usage = lc_result.response_metadata.get("token_usage", {})
            message_tokens = token_usage.get("prompt_tokens", 0)
            answer_tokens = token_usage.get("completion_tokens", 0)
            total_tokens = token_usage.get("total_tokens", 0)

        usage = LLMUsage(
            prompt_tokens=message_tokens,
            completion_tokens=answer_tokens,
            total_tokens=total_tokens,
            latency=latency
        )

        # 处理LaTeX公式
        answer = replace_latex(full_answer)

        return LLMResult(
            model_provider=self.model_provider,
            model=self.model_id,
            model_params=self.model_params,
            message=AssistantPromptMessage(content=answer, reasoning_content=reasoning_content),
            # TODO prompt_messages暂不处理
            prompt_messages=[],
            usage=usage,
            is_success=True,
        )

    def handle_stream_response(self, response_generator: Generator) -> Generator:
        started_at = time.perf_counter()

        full_reasoning_content = ''
        full_assistant_content = ""

        has_thinking = False
        thinking_finished = False
        reasoning_content = ''

        prompt_tokens = 0
        completion_tokens = 0
        final_chunk = LLMResultChunk(
            model_provider=self.model_provider,
            model=self.model_id,
            model_params=self.model_params,
            prompt_messages=[],
            delta=LLMResultChunkDelta(
                index=0,
                message=AssistantPromptMessage(content="")
            ),
        )

        # 豆包需要处理latex公式
        is_last_cut = False
        temp_exp_str = ''

        start_index = 0
        for chunk in response_generator:
            if hasattr(chunk, "usage_metadata") and chunk.usage_metadata:
                usage = chunk.usage_metadata

                prompt_tokens = usage.get("input_tokens", 0)
                completion_tokens = usage.get("output_tokens", 0)

            if 'reasoning_content' in chunk.additional_kwargs:
                has_thinking = True
                reasoning_chunk_content = chunk.additional_kwargs.get('reasoning_content', '')

                # 处理公式转换
                if is_last_cut:
                    reasoning_chunk_content = f'{temp_exp_str}{reasoning_chunk_content}'
                    is_last_cut = False
                    temp_exp_str = ''
                if reasoning_chunk_content and reasoning_chunk_content[-1] == '\\':
                    is_last_cut = True
                    temp_exp_str = reasoning_chunk_content
                    continue
                reasoning_chunk_content = replace_latex(reasoning_chunk_content)

                full_reasoning_content += reasoning_chunk_content

                assistant_thinking_message = AssistantPromptMessage(
                    content='', reasoning_content=reasoning_chunk_content
                )
                yield LLMResultChunk(
                    model_provider=self.model_provider,
                    model=self.model_id,
                    model_params=self.model_params,
                    prompt_messages=[],
                    delta=LLMResultChunkDelta(
                        index=start_index,
                        message=assistant_thinking_message,
                        usage=LLMUsage.empty_usage(),
                    ),
                )
                start_index += 1
                continue

            if chunk.content:
                if has_thinking and not thinking_finished:
                    thinking_finished = True

                    # 处理豆包公式渲染，剩余数据处理
                    if temp_exp_str:
                        reasoning_content = f'{reasoning_content}{temp_exp_str}'
                        is_last_cut = False
                        temp_exp_str = ''

                        assistant_thinking_message = AssistantPromptMessage(
                            content='', reasoning_content=reasoning_content
                        )
                        yield LLMResultChunk(
                            model_provider=self.model_provider,
                            model=self.model_id,
                            model_params=self.model_params,
                            prompt_messages=[],
                            delta=LLMResultChunkDelta(
                                index=start_index,
                                message=assistant_thinking_message,
                                usage=LLMUsage.empty_usage(),
                            ),
                        )
                        start_index += 1

                resp_content = chunk.content

                # 处理公式转换
                if is_last_cut:
                    resp_content = f'{temp_exp_str}{resp_content}'
                    is_last_cut = False
                    temp_exp_str = ''
                if resp_content and resp_content[-1] == '\\':
                    is_last_cut = True
                    temp_exp_str = resp_content
                    continue
                resp_content = replace_latex(resp_content)

                full_assistant_content += resp_content

                assistant_prompt_message = AssistantPromptMessage(content=resp_content or "")
                yield LLMResultChunk(
                    model_provider=self.model_provider,
                    model=self.model_id,
                    model_params=self.model_params,
                    prompt_messages=[],
                    delta=LLMResultChunkDelta(
                        index=start_index,
                        message=assistant_prompt_message,
                        usage=LLMUsage.empty_usage(),
                    ),
                )
                start_index += 1

        # 处理豆包公式渲染，剩余数据处理
        if temp_exp_str:
            full_assistant_content = f'{full_assistant_content}{temp_exp_str}'
            assistant_prompt_message = AssistantPromptMessage(content=temp_exp_str)
            yield LLMResultChunk(
                model_provider=self.model_provider,
                model=self.model_id,
                model_params=self.model_params,
                prompt_messages=[],
                delta=LLMResultChunkDelta(
                    index=start_index,
                    message=assistant_prompt_message,
                    usage=LLMUsage.empty_usage(),
                ),
            )

        usage = LLMUsage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=prompt_tokens + completion_tokens,
            latency=time.perf_counter() - started_at
        )
        final_chunk.delta.usage = usage

        yield final_chunk
