import os
from pathlib import Path

from ai_application.env import env


BASE_DIR = Path(__file__).resolve().parent.parent

SECRET_KEY = 'django-insecure-!!vpd*khwefpb!=4n0xkku!+@8(+t&7gjo5#=l&&nquh9^0=(s'

ENV_LOCAL = 0
ENV_TEST = 1
ENV_PRODUCT = 2
ENV_PREVIEW = 3

DEBUG = int(os.environ.get("DEBUG", 1)) == 1
ENVIRONMENT = int(os.environ.get('ENVIRONMENT', 0))

ALLOWED_HOSTS = ['*']

LOCAL_APPS = [
    'django_ext',
    'django_ext.logger',
    'django_ext.exceptions',
    'app',
]

THIRD_PARTY_APPS = [
    'rest_framework',
]

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    *THIRD_PARTY_APPS,
    *LOCAL_APPS,
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'ai_application.middlewares.LoggerMiddleware',
]

ROOT_URLCONF = 'ai_application.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ai_application.wsgi.application'


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env('DATABASES_NAME'),
        'USER': env('DATABASES_USER'),
        'PASSWORD': env('DATABASES_PASSWORD'),
        'HOST': env('DATABASES_HOST'),
        'PORT': env('DATABASES_PORT'),
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
        'CONN_MAX_AGE': 300
    }
}


AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = 'zh-Hans'
USE_I18N = True

TIME_ZONE = 'Asia/Shanghai'
USE_TZ = True

# 处理静态文件
STATIC_URL = '/static/'
STATICFILES_DIRS = (BASE_DIR.joinpath('static'),)
STATIC_ROOT = BASE_DIR.joinpath('collected_static')

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 尾部斜杠（/）处理
# APPEND_SLASH = False

REST_FRAMEWORK = {
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S',
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
    ),
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'app.auth.authentication.MyAuthAuthentication',
    ),
}

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        # 必选，格式为：redis://username:password@host:port(redis端口默认6379)/db
        # 无验证就是：redis://127.0.0.1:6379/1
        # 仅密码验证就是：redis://:password@127.0.0.1:6379/1
        # 完全验证就是：redis://username:password@127.0.0.1:6379/1
        "LOCATION": env('CACHE_REDIS_DEFAULT'),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "SERIALIZER": "django_redis.serializers.json.JSONSerializer",
        }
    },
    "sessions": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": env('CACHE_REDIS_SESSIONS'),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    },
    'local': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'TIMEOUT': 3600,
    }
}

# Celery 配置
CELERY_BROKER_URL = env('CELERY_BROKER_URL')
CELERY_RESULT_BACKEND = env('CELERY_RESULT_BACKEND')


DIFY_API_HOST = env('DIFY_API_HOST')
DASHSCOPE_API_KEY = env('DASHSCOPE_API_KEY')
DEEPSEEK_API_KEY = env('DEEPSEEK_API_KEY')
DOUBAO_API_KEY = env('DOUBAO_API_KEY')
DOUBAO_API_BASE = 'https://ark.cn-beijing.volces.com/api/v3'
# DOUBAO_API_KEY = 'ae85b453- 62c2-4bce-a370-d82007ad3149'
# DOUBAO_API_KEY = '9595878c-3684-44af-b775-7f8fa8847ea6'

MODEL_PROVIDER_NAME = 'tongyi'
MODEL_NAME = 'qwen-turbo'

EMBEDDING_MODEL_PROVIDER = 'tongyi'
EMBEDDING_MODEL = 'text-embedding-v2'

# 云原生数据仓库 AnalyticDB PostgreSQL版
ANALYTIC_PG = {
    'HOST': env('ANALYTIC_PG_HOST'),
    'PORT': env('ANALYTIC_PG_PORT'),
    'NAME': env('ANALYTIC_PG_NAME'),
    'USER': env('ANALYTIC_PG_USER'),
    'PASSWORD': env('ANALYTIC_PG_PASSWORD'),
}

RAG_MAX_FILE_NUM = 5
RAG_ALLOW_FILE_TYPES = ['txt', 'pdf', 'md', 'markdown']
RAG_ANSWER_SCORE_THRESHOLD = 60
RAG_DEFAULT_ANSWER = '抱歉，超出我的知识范围，请重新提问。'
RAG_DOCUMENT_OVERTIME = 1800  # 单位秒

LLM_NETWORK_ERROR_ANSWER = '短暂的网络波动让我们暂时失联，若您之前的问题未能解决请重新提交问题，我们将全力以赴。'
SENSITIVE_ANSWER = '抱歉，超出我的知识范围，请重新提问。'
LLM_SENSITIVE_ANSWER = '非常抱歉，可能我还没能完全理解您的意思，我们不妨先换个话题好吗？'

KNOWLEDGE_SEARCH_NO_RESULT = '未搜索到符合要求的知识点'

# 解题助手判断是否是一个完整题目，通过判断字符数量
JUDGE_QUESTION_MIN_LEN = 3

PROMPT_PASSWORD = env('PROMPT_PASSWORD')

# 钉钉机器人配置
DINGTALK_WEBHOOK = env('DINGTALK_WEBHOOK')
DINGTALK_SECRET = env('DINGTALK_SECRET')

# BAIDU ORC处理
BAIDU_OCR_API_ID = env('BAIDU_OCR_API_ID')
BAIDU_OCR_API_KEY = env('BAIDU_OCR_API_KEY')
BAIDU_OCR_SECRET_KEY = env('BAIDU_OCR_SECRET_KEY')

# 计算机408题库
QUESTION_LIBRARY_UID_408 = env('QUESTION_LIBRARY_UID_408')

BAIDU_NLP_API_ID = env('BAIDU_NLP_API_ID')
BAIDU_NLP_API_KEY = env('BAIDU_NLP_API_KEY')
BAIDU_NLP_SECRET_KEY = env('BAIDU_NLP_SECRET_KEY')

YANTUCS_API_URL = env('YANTUCS_API_URL')

# OSS配置
OSS_ACCESS_KEY_ID = env('OSS_ACCESS_KEY_ID')
OSS_ACCESS_KEY_SECRET = env('OSS_ACCESS_KEY_SECRET')
OSS_ACCESS_KEY_BUCKET = env('OSS_ACCESS_KEY_BUCKET')
OSS_ACCESS_ROLE = env('OSS_ACCESS_ROLE')
OSS_HTTP = env('OSS_HTTP')

# 博查API
BOCHA_API_KEY = env('BOCHA_API_KEY')

SUBTITLE_DATASET = 'c71288f6-503a-47b2-866d-60264f9ecfda'


# COZE基础配置
COZE_API_BASE = env('COZE_API_BASE')
COZE_JWT_OAUTH_CLIENT_ID = env('COZE_JWT_OAUTH_CLIENT_ID')
COZE_JWT_OAUTH_PRIVATE_KEY = env('COZE_JWT_OAUTH_PRIVATE_KEY')
COZE_JWT_OAUTH_PUBLIC_KEY_ID = env('COZE_JWT_OAUTH_PUBLIC_KEY_ID')
COZE_JWT_TOKEN_EXPIRE_TIME = env('COZE_JWT_TOKEN_EXPIRE_TIME') or 3600


# 字节跳动的语音识别API
BYTE_SPEECH_APP_ID = env('BYTE_SPEECH_APP_ID')
BYTE_SPEECH_TOKEN = env('BYTE_SPEECH_TOKEN')
BYTE_SPEECH_CLUSTER = env('BYTE_SPEECH_CLUSTER')

BI_API_APP_CODE = env('BI_API_APP_CODE')

# 邮件配置 - 简化版本
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = env('EMAIL_HOST') or 'smtp.qq.com'
EMAIL_PORT = int(env('EMAIL_PORT') or '587')
EMAIL_USE_TLS = (env('EMAIL_USE_TLS') or 'True').lower() == 'true'
EMAIL_HOST_USER = env('EMAIL_HOST_USER') or '<EMAIL>'
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD') or 'iznbzefuhzjkecfe'
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL') or 'AI校对系统 <<EMAIL>>'

# 站点URL配置
SITE_URL = env('SITE_URL') or 'http://localhost:8000'

DAYI_TEXT_MODEL = env('DAYI_TEXT_MODEL')
DAYI_VISUAL_MODEL = env('DAYI_VISUAL_MODEL')
DAYI_VISUAL_OCR_MODEL = env('DAYI_VISUAL_OCR_MODEL')
DAYI_MAX_CONTEXT_TOKENS = env('DAYI_MAX_CONTEXT_TOKENS')

CHAT2_DATASET = env('CHAT2_DATASET')

USE_THINKING_RESPONSE = env('USE_THINKING_RESPONSE')
