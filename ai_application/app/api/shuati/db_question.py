# from collections import defaultdict
# from app.models.shuati import STFirstRoundPaper
#
#
# def count_is_secondary_true():
#     # 获取所有第一轮试卷
#     papers = STFirstRoundPaper.objects.all()
#
#     # 用于统计不同 secondary 数量的试卷数量
#     secondary_count_distribution = defaultdict(int)
#
#     # 用于存储每份试卷的详细信息
#     paper_details = []
#
#     for paper in papers:
#         paper_content = paper.paper_content
#         if not paper_content:
#             secondary_count = 0
#         else:
#             # 提取 single_choice 部分
#             single_choices = paper_content.get("paper_details", {}).get("single_choice", [])
#
#             # 统计 is_secondary 为 true 的题目
#             secondary_count = sum(1 for question in single_choices if question.get("is_secondary") is True)
#
#         # 记录该试卷的 secondary 数量
#         secondary_count_distribution[secondary_count] += 1
#         paper_details.append({
#             'id': paper.id,
#             'secondary_count': secondary_count
#         })
#
#     # 输出每份试卷的 secondary 题目数量
#     print("各试卷中 is_secondary 为 true 的题目数量:")
#     for detail in paper_details:
#         print(f"试卷ID: {detail['id']}, is_secondary 为 true 的题目数量: {detail['secondary_count']}")
#
#     # 输出分布统计
#     print("\n不同 secondary 数量的试卷分布:")
#     for count in sorted(secondary_count_distribution.keys()):
#         print(f"{count} 个 is_secondary 为 true 的试卷有 {secondary_count_distribution[count]} 份")
#
#     return secondary_count_distribution
# from app.models.shuati import STQuestion
#
#
# def get_question_types_by_ids():
#     # 给定的题目ID列表
#     question_ids = [1166, 1477, 1045, 1337, 1671, 876, 1522, 1887, 1538, 2158]
#
#     # 查询这些ID对应的题目类型
#     questions = STQuestion.objects.filter(id__in=question_ids)
#
#     # 创建ID到题型的映射
#     question_type_map = {}
#     for question in questions:
#         question_type_map[question.id] = question.question_type
#
#     # 输出结果
#     print("题目ID及其对应的question_type:")
#     for qid in question_ids:
#         if qid in question_type_map:
#             question_type = question_type_map[qid]
#             # 获取题型的可读名称
#             type_name = STQuestion.Type(question_type).label if question_type in dict(
#                 STQuestion.Type.choices) else "未知类型"
#             print(f"题目ID: {qid}, question_type: {question_type} ({type_name})")
#         else:
#             print(f"题目ID: {qid}, 未找到该题目")
#
#     return question_type_map

