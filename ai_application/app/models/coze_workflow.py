from django.db import models

from ._base import BaseModel


class CozeWorkflow(BaseModel):
    workflow_id = models.Char<PERSON>ield('流水线id', max_length=100, blank=True, db_index=True)
    code = models.Char<PERSON><PERSON>('流水线code', max_length=100, blank=True)
    name = models.CharField('流水线名称', max_length=100, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = 'coze流水线'
        db_table = 'ai_coze_workflow'


class CozeWorkflowResult(BaseModel):
    class ExecuteStatus(models.TextChoices):
        RUNNING = ('RUNNING', '执行中')
        SUCCESS = ('SUCCESS', '成功')
        FAIL = ('FAIL', '失败')

    workflow_id = models.CharField('流水线id', max_length=100, blank=True, db_index=True)
    parameters = models.JSONField('流水线参数', null=True)
    execute_id = models.Char<PERSON>ield('流水线异步id', max_length=100, blank=True, db_index=True)
    err_log_id = models.CharField('错误log_id', max_length=100, blank=True)
    output_str = models.TextField('输出内容字符串', null=True)
    status = models.CharField('执行状态', max_length=16,
                              choices=ExecuteStatus.choices, default=ExecuteStatus.RUNNING)
    ext_params = models.JSONField('额外参数', null=True)
    process_status = models.CharField('处理流水线结果状态', max_length=32, blank=True)
    process_fail_reason = models.TextField('处理流水线失败原因', null=True)

    class Meta:
        verbose_name = verbose_name_plural = 'coze流水线执行结果'
        db_table = 'ai_coze_workflow_result'

