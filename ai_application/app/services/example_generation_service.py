# 阿纪
# 阿纪
import re
import json
import http.client
from django.http import JsonResponse
from app.api.dto import ChatMessageDto
from app.models import Account, InvokeFrom, App, PromptTemplate
from app.services.app_generate_service import AppGenerateService


class ExampleGenerationService:
    @classmethod
    def get_answer(cls, user_question: str, account: Account):

        # 设置请求负载
        payload = json.dumps({
            "word": user_question,
            "type": "question",
            "from": 1,
            "size": 2
        })

        # 建立数据库API连接
        conn = http.client.HTTPSConnection("all-search.kaoyan-vip.cn")
        headers = {
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Content-Type': 'application/json'
        }


        # 发送POST请求
        conn.request("POST", "/api/v1/dataSearch", payload, headers)
        res = conn.getresponse()
        data = res.read()
        data_json = json.loads(data.decode("utf-8"))
        # 初始化格式化问题列表
        format_questions = []

        # 遍历结果，构造每个问题的信息
        for item in data_json['result']['data']['data']:
            master_title = item['master_title_format']
            choice_body = item['sub_question_info_format'][0]['choice_body']
            answer_body = item['sub_question_info_format'][0]['answer_body']
            answer_analysis = item['sub_question_info_format'][0]['analysis']

            # 构造单个问题的字典
            question_info = {
                'question': f"{master_title}\n{choice_body}",
                'answers': answer_body,
                'analysis': answer_analysis
            }

            # 将构造好的问题信息加入到列表中
            format_questions.append(question_info)

        return format_questions
        # # 提取数据
        # first_master_title = data_json['result']['data']['data'][0]['master_title_format']
        # second_master_title = data_json['result']['data']['data'][1]['master_title_format']
        # # third_master_title = data_json['result']['data']['data'][2]['master_title_format']
        #
        # first_choice_body = data_json['result']['data']['data'][0]['sub_question_info_format'][0]['choice_body']
        # second_choice_body = data_json['result']['data']['data'][1]['sub_question_info_format'][0]['choice_body']
        # # third_choice_body = data_json['result']['data']['data'][2]['sub_question_info_format'][0]['choice_body']
        #
        # # 拼接
        # first_title_combined = f"{first_master_title}\n{first_choice_body}"
        # second_title_combined = f"{second_master_title}\n{second_choice_body}"
        # # third_title_combined = f"{third_master_title}\n{third_choice_body}"
        #
        # # 答案+解析
        # first_answer_body = data_json['result']['data']['data'][0]['sub_question_info_format'][0]['answer_body']
        # first_answer_analysis = data_json['result']['data']['data'][0]['sub_question_info_format'][0]['analysis']
        #
        # second_answer_body = data_json['result']['data']['data'][1]['sub_question_info_format'][0]['answer_body']
        # second_answer_analysis = data_json['result']['data']['data'][1]['sub_question_info_format'][0]['analysis']
        #
        # # third_answer_body = data_json['result']['data']['data'][2]['sub_question_info_format'][0]['answer_body']
        # # third_answer_analysis = data_json['result']['data']['data'][2]['sub_question_info_format'][0]['analysis']
        #
        # first_answer_combined = f"{first_answer_body}\n{first_answer_analysis}"
        # second_answer_combined = f"{second_answer_body}\n{second_answer_analysis}"
        # # third_answer_combined = f"{third_answer_body}\n{third_answer_analysis}"
        #
        # template = PromptTemplate.objects.filter(app_no='example_generation').first()
        #
        # dto = ChatMessageDto(
        #     app_id='example_generation',
        #     inputs={
        #         "max_tokens": 1500,
        #         "temperature": 0.1,
        #         'top_p': 0.5,
        #         'prompt_template': template.id,
        #         'first_title_combined': first_title_combined,
        #         'first_answer_combined': first_answer_combined,
        #         'second_title_combined': second_title_combined,
        #         'second_answer_combined': second_answer_combined,
        #         'is_debug': 1,
        #     },
        #     # query=user_question,
        #     stream=False,
        # )
        # response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.api.value)
        # answer = response.get('answer', '')
        #
        # return {"result": answer}


