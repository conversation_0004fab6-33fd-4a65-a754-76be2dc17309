import pandas as pd
from dataclasses import dataclass

from django.db import transaction

from app.models.main_subject import SubjectDomain, KnowledgeLibrary


@dataclass
class SubjectDomainD:
    subject_code: str
    subject_name: str
    main_subject_code: str
    main_subject_name: str


@dataclass
class KnowledgeLibraryD:
    subject_domain: SubjectDomainD
    nature: str
    name: str
    desc: str = ''


file_list = [
    '/Users/<USER>/Desktop/0407学科专业术语/金融/专业术语/投资学1.csv',


    # '/Users/<USER>/Desktop/刑法学1.csv',
]


sd = SubjectDomainD(
    subject_code='finance',
    subject_name='金融',
    main_subject_code='investment',
    main_subject_name='投资学'
)
items_map: dict[str, KnowledgeLibraryD] = {}


def parse_data():
    exist_items_name = set()
    for i in KnowledgeLibrary.objects.filter(main_subject=sd.main_subject_name).all():
        if i.subject_domain_id:
            continue
        exist_items_name.add(i.name)

    for file in file_list:
        if file.endswith('csv'):
            file_df = pd.read_csv(file)
        elif file.endswith('xls') or file.endswith('xlsx'):
            file_df = pd.read_excel(file)
        else:
            raise Exception('类型错不支持')

        for idx, row in file_df.iterrows():
            item = row.to_dict()
            desc = '' if isinstance(item['名词解释'], float) else item['名词解释']
            name = item['名词']
            if isinstance(name, float):
                continue
            if name.startswith('**'):
                name = name.replace('**', '')
            if name in exist_items_name:
                continue
            kl = KnowledgeLibraryD(
                subject_domain=sd,
                nature='major' if desc else 'common',
                name=item['名词'],
                desc=desc,
            )
            items_map[kl.name] = kl


def insert_db():
    with transaction.atomic():
        sd_object = SubjectDomain.objects.filter(
            subject_name=sd.subject_name,
            main_subject_name=sd.main_subject_name
        ).first()
        print(111)
        print(sd_object)
        sd_object = sd_object or SubjectDomain.objects.create(
            subject_code=sd.subject_code,
            subject_name=sd.subject_name,
            main_subject_code=sd.main_subject_code,
            main_subject_name=sd.main_subject_name,
        )

        obj_list = []
        for item in items_map.values():
            obj_list.append(KnowledgeLibrary(
                subject_domain=sd_object,
                subject=sd.subject_name,
                main_subject=sd.main_subject_name,
                nature=item.nature,
                name=item.name,
                desc=item.desc,
            ))

        KnowledgeLibrary.objects.bulk_create(obj_list, batch_size=100)




