import json
from app.api.waikan_reader_api.get_question_detail import get_question_detail
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.features.WaikanFeature import WaikanFeature
from app.core.features.code_optimization import CodeOptimizationFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError
from app.models import Message, UserQuestion


class WaikanQuestionFirstReportWorkflow(BaseWorkflow):

    def run(self):
        try:

            inputs = self.application_generate_entity.inputs
            print(f"inputs在这边{inputs}")
            if not inputs:
                raise ParameterError(detail='inputs 参数为空')

            question_id = inputs.get('question_id')
            answer_detail = inputs.get('answer_detail')
            userinfo = inputs.get('userinfo')
            print(f"userinfo在这边{userinfo}")
            if not userinfo or 'user_id' not in userinfo:
                raise ParameterError(detail='userinfo 中缺少 user_id')

            user_id = userinfo['user_id']
            # # 更新 UserQuestion 表
            # try:
            #     user_question_record = UserQuestion.objects.get(user_id=user_id)
            #     if user_question_record.question_id is None:
            #         user_question_record.question_id = []
            #     if question_id not in user_question_record.question_id:
            #         user_question_record.question_id.append(question_id)
            # except UserQuestion.DoesNotExist:
            #     user_question_record = UserQuestion(user_id=user_id, question_id=[question_id])
            #
            #
            # user_question_record.save()
            # 存储错误题目的 question_type
            wrong_question_types = set()  # 使用 set 去重
            sub_question_ids = []
            # 遍历 answer_detail 列表并提取字段
            for sub_question in answer_detail:
                sub_question_id = sub_question.get('sub_question_id')
                user_answer = sub_question.get('user_answer')
                is_right = sub_question.get('is_right')
                question_type = sub_question.get('question_type')

                if not sub_question_id or not user_answer or is_right is None:
                    raise ParameterError(detail='answer_detail 中的子题参数不完整')
                sub_question_ids.append(sub_question_id)

                # 如果题目错误，提取 question_type
                if not is_right:
                    wrong_question_types.update(question_type)  # 将 question_type 列表中的元素添加到 set 中

            # 将 set 转换为列表
            wrong_question_types = list(wrong_question_types)
            wrong_question_types_text = ', '.join(wrong_question_types)

        except Exception:
            raise ParameterError(detail='参数错误')

        #更新用户答题记录表

        result = WaikanFeature().run(
            application_generate_entity=self.application_generate_entity,
            wrong_question_types =  wrong_question_types_text ,
            stream=True,

        )

        tracing_info = {
            'tracing_type': 'waikan_question_first_report', 'query':  wrong_question_types
        }
        self._handle_node_generate_response(result, tracing_info=tracing_info)
        self._publish_workflow_end_message()
