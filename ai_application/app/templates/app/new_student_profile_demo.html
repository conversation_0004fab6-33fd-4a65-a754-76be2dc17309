<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>学生基础画像分析 - 新版Demo</title>
    <script src="https://cdn.kaoyanvip.cn/marked/13.0.2/marked.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            width: 100%;
            background-color: white;
            padding: 20px;
        }
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        .column {
            flex: 1;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }
        .form-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        label {
            display: inline-block;
            margin-bottom: 5px;
            font-weight: bold;
            white-space: nowrap;
            margin-right: 10px;
            width: auto;
        }
        input, select, textarea {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a87;
        }
        .result-content {
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            font-size: 14px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 100%;
            box-sizing: border-box;
        }

        /* 添加Markdown内容样式 */
        .result-content h1 {
            font-size: 2em;
            margin: 0.67em 0;
        }

        .result-content h2 {
            font-size: 1.5em;
            margin: 0.83em 0;
        }

        .result-content h3 {
            font-size: 1.17em;
            margin: 1em 0;
        }

        .result-content h4 {
            font-size: 1em;
            margin: 1.33em 0;
        }

        .result-content h5 {
            font-size: 0.83em;
            margin: 1.67em 0;
        }

        .result-content h6 {
            font-size: 0.67em;
            margin: 2.33em 0;
        }

        .result-content p {
            margin: 1em 0;
        }

        .result-content ul, .result-content ol {
            margin: 1em 0;
            padding-left: 40px;
        }

        .result-content ul li {
            list-style-type: disc;
        }

        .result-content ol li {
            list-style-type: decimal;
        }

        .result-content code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }

        .result-content pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }

        .result-content pre code {
            background-color: transparent;
            padding: 0;
        }

        .result-content blockquote {
            border-left: 4px solid #ddd;
            padding-left: 16px;
            margin: 1em 0;
            color: #666;
        }

        .result-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        .result-content table, .result-content th, .result-content td {
            border: 1px solid #ddd;
        }

        .result-content th, .result-content td {
            padding: 8px 12px;
        }

        .result-content th {
            background-color: #f8f8f8;
        }

        .task-info {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .error {
            color: #d63638;
            background-color: #f8e6e6;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .success {
            color: #008a20;
            background-color: #e6f8e9;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .loading {
            color: #dba617;
            background-color: #fff8e6;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .edit-buttons {
            margin-top: 10px;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        .edit-button {
            background-color: #dba617;
            font-size: 14px;
            padding: 5px 10px;
        }
        .save-button {
            background-color: #008a20;
            font-size: 14px;
            padding: 5px 10px;
        }
        .cancel-button {
            background-color: #d63638;
            font-size: 14px;
            padding: 5px 10px;
        }
        .db-save-button {
            background-color: #005a87;
            font-size: 14px;
            padding: 5px 10px;
            margin-left: 10px;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .message.success {
            background-color: #e6f8e9;
            color: #008a20;
        }
        .message.error {
            background-color: #f8e6e6;
            color: #d63638;
        }
        /* 修改: 统一编辑区域和展示区域的样式 */
        .prompt-edit-area {
            display: none;
        }
        .prompt-textarea {
            width: 100%;
            min-height: 800px; /* 修改：增加提示词区域最小高度 */
            font-family: monospace;
            box-sizing: border-box;
            padding: 15px;
            border: 1px solid #ddd;
            border-left: 4px solid #007cba;
            background-color: #f9f9f9;
            resize: vertical;
            margin: 0;
            height: 100%;
        }
        #prompt-display, #prompt-edit {
            position: relative;
        }

        .input-section {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .form-wrapper {
            flex: 1;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
        }
        /* 新增：确保提示词编辑区域与输入区域高度一致 */
        .section.equal-height {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .section.equal-height .prompt-container,
        .section.equal-height .input-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .section.equal-height .prompt-content-wrapper,
        .section.equal-height .form-wrapper {
            flex: 1;
        }
        /* 新增：专业选择容器样式 */
        .major-selection-container {
            display: flex;
            gap: 10px;
            flex: 1;
        }
        .major-selection-container select {
            flex: 1;
        }
        /* 新增：院校和代码同行显示的样式 */
        .school-code-group {
            display: flex;
            gap: 10px;
            flex: 1;
        }
        .school-code-group .school-field {
            flex: 3;
        }
        .school-code-group .code-field {
            flex: 1;
        }
        /* 新增：数学科目复选框样式 */
        #math_subjects_container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            flex: 1;
        }

        #math_subjects_container label {
            color: #666;
            font-size: 14px;
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 5px;
        }

        #math_subjects_container input[type="checkbox"] {
            margin-right: 5px;
            vertical-align: middle;
        }

        #math_subjects_container label span {
            vertical-align: middle;
        }

        /* 新增：底部左右布局样式 */
        .bottom-row {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .bottom-column {
            flex: 1;
        }

        .bottom-column:first-child {
            height: 1500px;
        }

        /* 新增：表单字段同行显示样式 */
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            align-items: center;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
            display: flex;
            align-items: center;
        }

        /* 特殊处理数学科目字段 */
        .math-subjects-group {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .math-subjects-group .form-group {
            flex: 3;
        }

        .math-subjects-group .math-master-group {
            flex: 2;
        }

        .math-subjects-group .political-master-group {
            flex: 2;
        }

        /* 新增：定义小宽度字段样式 */
        .small-field {
            flex: 0.5; /* 原来默认是1，现在设置为0.5使其更窄 */
        }

        /* 新增：定义大宽度字段样式 */
        .large-field {
            flex: 1.5; /* 原来默认是1，现在设置为1.5使其更宽 */
        }

        /* 新增：定义固定宽度字段样式 */
        .fixed-width-80 {
            flex: 0 0 80px;
        }

        .fixed-width-100 {
            flex: 0 0 100px;
        }

        .fixed-width-120 {
            flex: 0 0 120px;
        }

        .fixed-width-150 {
            flex: 0 0 150px;
        }

        .fixed-width-200 {
            flex: 0 0 200px;
        }

        /* 统一所有下拉框样式 */
        select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            background-color: white;
            height: 36px;
        }

        /* 新增：定义下拉框容器样式，确保在整个页面中显示 */
        .custom-dropdown {
            position: fixed !important;
            z-index: 9999 !important;
            max-height: 300px !important;
            overflow-y: auto !important;
            width: auto !important; /* 移除固定宽度 */
        }

        /* 新增：固定院校下拉框宽度 */
        .school-field-large {
            flex: 1;
            width: 300px; /* 固定宽度 */
        }

        .school-field-large input[type="text"] {
            width: 100%; /* 使输入框占满容器 */
        }

        .school-code-group .school-field {
            flex: 3;
            width: 300px; /* 固定宽度 */
        }

        .school-input-container {
            position: relative;
        }

        .school-suggestions {
            position: fixed; /* 改为固定定位 */
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            width: auto;
            min-width: 200px;
        }

        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .submit-btn {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }

        .submit-btn:hover {
            background-color: #005a87;
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading-container {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }

        .loading-progress {
            font-size: 14px;
            color: #999;
        }

        .result-container {
            display: none;
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 100%;
            box-sizing: border-box;
            overflow-wrap: break-word;
        }

        .result-content {
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            font-size: 14px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 100%;
            box-sizing: border-box;
        }

        .error-container {
            display: none;
            background: #fff5f5;
            border: 2px solid #fed7d7;
            border-radius: 10px;
            padding: 20px;
            color: #c53030;
            max-width: 100%;
            box-sizing: border-box;
        }

        .error-title {
            font-weight: bold;
            margin-bottom: 10px;
        }

        .hidden {
            display: none !important;
        }

        .two-column {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }

        .code-readonly {
            background-color: #f8f9fa;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>学生基础画像-目标分数预测</h1>

        <!-- 输入内容区域 -->
        <div class="section input-section equal-height">
            <div class="section-title">输入信息</div>
            <div class="form-wrapper">
                <form id="studentForm">
                    <!-- 修改：将姓名、毕业院校、毕业专业、专业代码放在同一行 -->
                    <div class="form-row">
                        <div class="form-group fixed-width-150">
                            <label for="user_id">姓名:</label>
                            <input type="text" id="user_id" name="user_id" required>
                        </div>

                        <div class="form-group" style="flex: 1;">
                            <label for="graduation_school">毕业院校:</label>
                            <div class="school-code-group">
                                <div class="school-field-large" style="flex: 1; width: 300px;">
                                    <div style="position: relative;">
                                        <input type="text" id="graduation_school" name="graduation_school"
                                               placeholder="输入院校名称进行搜索" required>
                                        <div id="graduation_school_suggestions" class="school-suggestions"></div>
                                    </div>
                                </div>
                                <div class="code-field-small">
                                    <input type="text" id="graduation_school_code" name="graduation_school_code" readonly placeholder="毕业院校代码" maxlength="5">
                                </div>
                            </div>
                        </div>

                        <div class="form-group" style="flex: 3;">
                            <label for="graduation_major_category">毕业专业:</label>
                            <div class="major-selection-container">
                                <select id="graduation_major_category" name="graduation_major_category">
                                    <option value="">请选择一级学科</option>
                                </select>
                                <select id="graduation_major_discipline" name="graduation_major_discipline" disabled>
                                    <option value="">请选择二级门类</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group fixed-width-120">
                            <label for="graduation_major_code">专业代码:</label>
                            <input type="text" id="graduation_major_code" name="graduation_major_code" readonly>
                        </div>
                    </div>

                    <!-- 修改：将教育程度、学习状态、毕业年份、考研年份、学业表现放在同一行 -->
                    <div class="form-row">
                        <div class="form-group fixed-width-120">
                            <label for="education_level">教育程度:</label>
                            <select id="education_level" name="education_level" required>
                                <option value=>请选择</option>
                                <option value=0>本科</option>
                                <option value=1>专科</option>
                                <option value=2>自考本</option>
                            </select>
                        </div>

                        <div class="form-group fixed-width-120">
                            <label for="study_status">学习状态:</label>
                            <select id="study_status" name="study_status" required>
                                <option value=>请选择</option>
                                <option value=0>在校生</option>
                                <option value=1>已毕业</option>
                            </select>
                        </div>

                        <div class="form-group fixed-width-120">
                            <label for="current_study_status">在读状态:</label>
                            <select id="current_study_status" name="current_study_status" disabled>
                                <option value=>请选择</option>
                                <option value=0>大一</option>
                                <option value=1>大二</option>
                                <option value=2>大三</option>
                                <option value=3>大四</option>
                                <option value=4>大五</option>                                
                            </select>
                        </div>

                        <div class="form-group fixed-width-120">
                            <label for="graduation_years">毕业年限:</label>
                            <select id="graduation_years" name="graduation_years" disabled>
                                <option value=>请选择</option>
                                <option value=0>1年</option>
                                <option value=1>2-3年</option>
                                <option value=2>4-5年</option>
                                <option value=3>6年及以上</option>

                            </select>
                        </div>

                        <div class="form-group fixed-width-120">
                            <label for="exam_date">考研年份:</label>
                            <select id="exam_date" name="exam_date" required>
                                <option value="">请选择</option>
                                <option value="2025-12-20">2026</option>
                                <option value="2026-12-26">2027</option>
                                <option value="2027-12-25">2028</option>
                                <option value="2028-12-23">2029</option>
                                <option value="2029-12-22">2030</option>
                                <option value="2030-12-21">2031</option>
                            </select>
                        </div>

                        <div class="form-group fixed-width-120">
                            <label for="academic_performance">学业表现:</label>
                            <select id="academic_performance" name="academic_performance">
                                <option value="">请选择</option>
                                <option value=0>专业前10%</option>
                                <option value=1>专业前30%</option>
                                <option value=2>专业前50%</option>
                                <option value=3>专业后50%</option>
                            </select>
                        </div>

                        <div class="form-group fixed-width-100">
                            <label for="english_level">英语水平:</label>
                            <select id="english_level" name="english_level">
                                <option value="">请选择</option>
                                <option value=0>四级</option>
                                <option value=1>六级</option>
                                <option value=2>专四</option>
                                <option value=3>专八</option>
                                <option value=4>托雅</option>
                                <option value=5>其他</option>
                            </select>
                        </div>
                    </div>

                    <!-- 修改：将是否考数学、数学科目、数学掌握程度、政治掌握程度放在同一行 -->
                    <div class="form-row">
                        <div class="form-group fixed-width-100" style="flex: 0.25;">
                            <label for="if_math">是否考数学:</label>
                            <select id="if_math" name="if_math" style="width: 30px;">
                                <option value="">请选择</option>
                                <option value="是">是</option>
                                <option value="否">否</option>
                            </select>
                        </div>

                        <div class="math-subjects-group">
                            <div class="form-group" style="flex: 3;">
                                <label>是否学过数学科目:</label>
                                <div id="math_subjects_container">
                                    <label><input type="checkbox" name="math_subjects" value=0> 高等数学</label>
                                    <label><input type="checkbox" name="math_subjects" value=1> 线性代数</label>
                                    <label><input type="checkbox" name="math_subjects" value=2> 概率统计</label>
                                    <label><input type="checkbox" name="math_subjects" value=3> 都没学过</label>
                                </div>
                            </div>

                            <div class="form-group fixed-width-120">
                                <label for="math_mastery">数学整体掌握程度:</label>
                                <select id="math_mastery" name="math_mastery" disabled>
                                    <option value=0>优秀</option>
                                    <option value=1>良好</option>
                                    <option value=2>一般</option>
                                    <option value=3>较差</option>
                                </select>
                            </div>

                        </div>
                    </div>

                    <!-- 修改：将目标院校、目标专业、目标专业代码放在同一行 -->
                    <div class="form-row">
                        <div class="form-group" style="flex: 1;">
                            <label for="target_school">目标院校:</label>
                            <div class="school-code-group">
                                <div class="school-field-large" style="flex: 1; width: 300px;">
                                    <div style="position: relative;">
                                        <input type="text" id="target_school" name="target_school"
                                               placeholder="输入院校名称进行搜索">
                                        <div id="target_school_suggestions" class="school-suggestions"></div>
                                    </div>
                                </div>
                                <div class="code-field-small" style="flex: 1;">
                                    <input type="text" id="target_school_code" name="target_school_code" readonly placeholder="目标院校代码" maxlength="5">
                                </div>
                            </div>
                        </div>

                        <div class="form-group" style="flex: 1.75;">
                            <label for="target_major_category">目标专业:</label>
                            <div class="major-selection-container">
                                <select id="target_major_category" name="target_major_category">
                                    <option value="">请选择一级学科</option>
                                </select>
                                <select id="target_major_discipline" name="target_major_discipline" disabled>
                                    <option value="">请选择二级门类</option>
                                </select>
                                <select id="target_major" name="target_major" disabled>
                                    <option value="">请选择专业</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group fixed-width-120" style="flex: 1;">
                            <label for="target_major_code">目标专业代码:</label>
                            <input type="text" id="target_major_code" name="target_major_code" placeholder="目标专业代码">
                        </div>
                    </div>

                    <!-- 隐藏的task_id字段 -->
                    <input type="hidden" id="current_task_id" name="current_task_id" value="">
                    <input type="hidden" id="check_task" name="check_task" value="">

                    <button type="submit" class="submit-btn" id="submitBtn">
                        提交分析
                    </button>
                </form>
            </div>
        </div>

        <!-- 底部行：提示词模版区域和任务信息+分析结果区域 -->
        <div class="bottom-row">
            <!-- 提示词显示区域 -->
            <div class="bottom-column">
                <div class="section equal-height">
                    <div class="section-title">提示词模板</div>
                    <div id="prompt-edit" class="prompt-container">
                        <div class="prompt-content-wrapper">
                            <!-- 修改：添加默认值和空值处理 -->
                            <textarea id="prompt-textarea" class="prompt-textarea">{{ prompt_template|default:"" }}</textarea>
                        </div>
                        <div class="edit-buttons">
                            <button class="db-save-button" onclick="savePromptToDB()">保存</button>
                        </div>
                    </div>
                    <div id="message-area"></div>
                    <!-- 添加跳转到个性考纲分析指导页面的按钮 -->
                    <div style="margin-top: 20px;">
                        <button id="syllabusGuideBtn" class="submit-btn" style="background-color: #17a2b8;" onclick="goToSyllabusGuide()">个性考纲分析指导</button>
                    </div>
                    <!-- 添加408个性化考纲按钮和显示区域 -->
                    <div style="margin-top: 20px; display: none;">
                        <button id="syllabusBtn" class="submit-btn" style="background-color: #28a745;" onclick="get408Syllabus()"></button>

                        <div class="result-container" id="syllabusContentContainer" style="display: none; max-width: 100%;">
                            <div class="result-content" id="syllabusContent" style="max-width: 100%;"></div>
                        </div>
                        <div class="error-container" id="syllabusErrorContainer" style="display: none; max-width: 100%;">
                            <div class="error-title">获取失败</div>
                            <div id="syllabusError" style="max-width: 100%; word-wrap: break-word; overflow-wrap: break-word;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务信息和分析结果区域 -->
            <div class="bottom-column">
                <div class="section">
                    <div class="section-title">任务信息</div>


                    <!-- 输出内容区域 -->
                    <div class="section">
                        <div class="section-title">分析结果</div>
                        <div id="initialPrompt" style="text-align: center; color: #999; padding: 40px;">
                            请填写上方信息并点击"提交分析"按钮
                        </div>

                        <!-- 加载状态 -->
                        <div class="loading-container" id="loadingContainer">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">正在分析学习状态...</div>
                            <div class="loading-progress" id="loadingProgress">预计需要2-3分钟，请耐心等待</div>
                            <div class="loading-task-id" id="loadingTaskId" style="font-size: 12px; color: #999; margin-top: 10px;"></div>
                        </div>

                        <!-- 结果显示 -->
                        <div class="result-container" id="resultContainer">
                            <div class="result-content" id="resultContent"></div>
                        </div>

                        <!-- 错误显示 -->
                        <div class="error-container" id="errorContainer">
                            <div class="error-title">分析失败</div>
                            <div id="errorMessage"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCollegeData();
            initializeMajorData();
            setupFormHandlers();

            // 页面加载时恢复用户自定义的提示词
            // 修改：优先使用数据库中的提示词，只有在数据库没有提示词时才使用localStorage中的提示词
            const promptTextarea = document.getElementById('prompt-textarea');
            const savedPrompt = localStorage.getItem('customPrompt');
            
            // 只有当文本域为空且localStorage中有保存的提示词时才使用localStorage中的提示词
            if (!promptTextarea.value && savedPrompt) {
                promptTextarea.value = savedPrompt;
            }
        });

        // 院校数据 - 从后端获取
        let undergraduateColleges = [];
        let graduateColleges = [];

        // 专业数据
        const undergraduateData = {
            "01哲学": ["0101哲学类"],
            "02经济学": ["0201经济学类", "0202财政学类", "0203金融学类", "0204经济与贸易类"],
            "03法学": ["0301法学类", "0302政治学类", "0303社会学类", "0304民族学类", "0305马克思主义理论类", "0306公安学类"],
            "04教育学": ["0401教育学类", "0402体育学类"],
            "05文学": ["0501中国语言文学类", "0502外国语言文学类", "0503新闻传播类"],
            "06历史学": ["0601历史学类"],
            "07理学": ["0701数学类", "0702物理类", "0703化学类", "0704天文学类", "0705地理科学类", "0706大气科学类", "0708地球物理类", "0709地质学类", "0710生物科学类", "0711心理学类", "0712统计学类"],
            "08工学": ["0801力学类", "0802机械类", "0803仪器类", "0804材料类", "0805能源动力类", "0806电气类", "0807电子信息类", "0808自动化类", "0809计算机类", "0810土木类", "0811水利类", "0812测绘类", "0813化工与制药类", "0814地质类", "0815矿业类", "0816纺织类", "0817轻工类", "0818交通运输类", "0819海洋工程类", "0820航空航天类", "0821兵器类", "0822核工程类", "0823农业工程类", "0824林业工程类", "0825环境科学与工程类", "0826生物医学工程类", "0827食品科学与工程类", "0828建筑类", "0829安全科学与工程类", "0830生物工程类", "0831公安技术类", "0832交叉工程类"],
            "09农学": ["0901植物生产类", "0902自然保护与环境生态类", "0903动物生产类", "0904动物医学类", "0905林学类", "0906水产类", "0907草学类"],
            "10医学": ["1001基础医学类", "1002临床医学类", "1003口腔医学类", "1004公共卫生与预防医学类", "1005中医学类", "1006中西医结合类", "1007药学类", "1008中药学类", "1009法医学类", "1010医学技术类", "1011护理学类"],
            "12管理学": ["1201管理科学与工程", "1202工商管理类", "1203农业经济管理类", "1204公共管理类", "1205图书情报与档案管理类", "1206物流管理与工程类", "1207工业工程类", "1208电子商务类", "1209旅游管理类"],
            "13艺术学": ["1301艺术学理论类", "1302音乐与舞蹈学类", "1303戏剧与影视学类", "1304美术学类", "1305设计学类"]
        };
        const graduateData = {
            "01哲学": ["0101哲学","0151应用伦理"],
            "02经济学": ["0201理论经济学", "0202应用经济学", "0251金融", "0252应用统计","0253税务","0254国际商务","0255保险","0256资产评估","0258数字经济","0270统计学","0271区域国别学"],
            "03法学": ["0301法学", "0302政治学", "0303社会学", "0304民族学", "0305马克思主义理论", "0306公安学", "0307中共党史党建学", "0308纪检监察学", "0351法律", "0352社会工作", "0353警务", "0354知识产权", "0355国际事务", "0370国家安全学", "0371区域国别学"],
            "04教育学": ["0401教育学", "0402心理学", "0403体育学", "0451教育", "0452体育", "0453国际中文教育", "0454应用心理", "0471教育经济与管理"],
            "05文学": ["0501中国语言文学", "0502外国语言文学", "0503新闻传播", "0551翻译", "0552新闻与传播", "0553出版"],
            "06历史学": ["0601考古学","0602中国史","0601世界史","0651博物馆","0671区域国别学"],
            "07理学": ["0701数学", "0702物理", "0703化学", "0704天文学", "0705地理学", "0706大气科学", "0707海洋科学", "0708地球物理学", "0709地质学", "0710生物学", "0711系统科学", "0712科学技术史", "0713生态学", "0714统计学", "0715气象"],
            "08工学": ["0801力学", "0802机械工程", "0803光学工程", "0804仪器科学与技术", "0805材料科学与工程", "0806冶金工程", "0807动力工程与工程热物理", "0808电气工程", "0809电子科学与技术", "0810信息与通信工程", "0811控制科学与工程", "0812计算机科学与技术", "0813建筑学", "0814土木工程", "0815水利工程", "0816测绘科学与技术", "0817化学工程与技术", "0818地质资源与地质工程", "0819矿业工程", "0820石油与天然气工程", "0821纺织科学与工程", "0822轻工技术与工程", "0823交通运输工程", "0824船舶与海洋工程", "0825航空宇航科学与技术", "0826兵器科学与技术", "0827核科学与技术", "0828农业工程", "0829林业工程", "0830环境科学与工程", "0831生物医学工程", "0832食品科学与工程", "0833城乡规划学", "0835软件工程", "0836生物工程", "0837安全科学与工程", "0838公安技术", "0839网络空间安全", "0851建筑", "0853城乡规划", "0854电子信息", "0855机械", "0856材料与化工", "0857资源与环境", "0858能源动力", "0859土木水利", "0860生物与医药", "0861交通运输", "0862风景园林"],
            "09农学": ["0901作物学", "0902园艺学", "0903农业资源与环境", "0904植物保护", "0905畜牧学", "0906兽医学", "0907林学", "0908水产", "0909草学", "0910水土保持与荒漠化防治学", "0951农业", "0952兽医", "0954林学", "0955食品与营养"],
            "10医学": ["1001基础医学", "1002临床医学", "1003口腔医学", "1004公共卫生与预防医学", "1005中医学", "1006中西医结合", "1007药学", "1008中药学", "1009特种医学", "1011护理学", "1012法医学", "1051临床医学", "1052口腔医学", "1053公共卫生", "1054护理", "1055药学", "1056中药学", "1057中医", "1058医学技术", "1059针灸"],
            "12管理学": ["1201管理科学与工程", "1202工商管理学", "1203农林经济管理", "1204公共管理学", "1205信息资源管理", "1251工商管理", "1252公共管理", "1253会计", "1254旅游管理", "1255图书情报", "1256工程管理", "1257审计"],
            "13艺术学": ["1301艺术学", "1352音乐", "1353舞蹈", "1354戏剧与影视", "1355戏曲与曲艺", "1356美术与书法", "1357设计"],
            "14交叉学科": ["1401集成电路科学与工程", "1402国家安全学", "1403设计学", "1404遥感科学与技术", "1405智能科学与技术", "1406纳米科学与技术", "1407区域国别学", "1451文物", "1452密码"],
        };


        // 初始化院校数据
        async function initializeCollegeData() {
            try {
                // 从模板中获取本科院校数据
                undergraduateColleges = [
                    {% for college in undergraduate_colleges %}
                    {name: "{{ college.name|escapejs }}", code: "{{ college.undergraduate_code|escapejs }}"},
                    {% endfor %}
                ];

                // 从模板中获取研究生院校数据
                graduateColleges = [
                    {% for college in graduate_colleges %}
                    {name: "{{ college.name|escapejs }}", code: "{{ college.code|escapejs }}"},
                    {% endfor %}
                ];
            } catch (error) {
                console.error('获取院校数据失败:', error);
                // 使用备用数据
                undergraduateColleges = [
                    {name: "北京大学", code: "10001"},
                    {name: "清华大学", code: "10003"},
                    {name: "中国人民大学", code: "10002"},
                    {name: "北京理工大学", code: "10007"},
                    {name: "北京航空航天大学", code: "10006"},
                    {name: "复旦大学", code: "10246"},
                    {name: "上海交通大学", code: "10248"},
                    {name: "浙江大学", code: "10335"},
                    {name: "南京大学", code: "10284"},
                    {name: "中山大学", code: "10558"}
                ];
                graduateColleges = undergraduateColleges; // 备用数据
            }
        }

        // 初始化专业数据
        function initializeMajorData() {
            // 初始化毕业专业一级学科
            const graduationCategorySelect = document.getElementById('graduation_major_category');
            Object.keys(undergraduateData).forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                graduationCategorySelect.appendChild(option);
            });

            // 初始化目标专业一级学科（从API获取）
            initializeTargetMajorData();
        }

        // 初始化目标专业数据
        async function initializeTargetMajorData() {
            try {
                // 修改：使用固定的graduateData数据而不是从API获取
                const targetCategorySelect = document.getElementById('target_major_category');
                Object.keys(graduateData).forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    targetCategorySelect.appendChild(option);
                });
            } catch (error) {
                console.error('初始化目标专业数据失败:', error);
            }
        }

        // 设置表单处理器
        function setupFormHandlers() {
            const graduationSchoolInput = document.getElementById('graduation_school');
            const graduationSchoolSuggestions = document.getElementById('graduation_school_suggestions');
            const graduationSchoolCode = document.getElementById('graduation_school_code');
            const targetSchoolInput = document.getElementById('target_school');
            const targetSchoolSuggestions = document.getElementById('target_school_suggestions');
            const targetSchoolCode = document.getElementById('target_school_code');
            const form = document.getElementById('studentForm');

            // 毕业院校输入处理
            graduationSchoolInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                if (query.length < 1) {
                    graduationSchoolSuggestions.style.display = 'none';
                    graduationSchoolCode.value = '';
                    // 清理事件监听器
                    if (this.scrollHandler) {
                        window.removeEventListener('scroll', this.scrollHandler);
                        this.scrollHandler = null;
                    }
                    if (this.resizeHandler) {
                        window.removeEventListener('resize', this.resizeHandler);
                        this.resizeHandler = null;
                    }
                    return;
                }

                const filtered = undergraduateColleges.filter(college =>
                    college.name.toLowerCase().includes(query)
                );

                showSuggestions(filtered, graduationSchoolSuggestions, graduationSchoolInput, graduationSchoolCode);
            });

            // 目标院校输入处理
            targetSchoolInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                if (query.length < 1) {
                    targetSchoolSuggestions.style.display = 'none';
                    targetSchoolCode.value = '';
                    // 清理事件监听器
                    if (this.scrollHandler) {
                        window.removeEventListener('scroll', this.scrollHandler);
                        this.scrollHandler = null;
                    }
                    if (this.resizeHandler) {
                        window.removeEventListener('resize', this.resizeHandler);
                        this.resizeHandler = null;
                    }
                    return;
                }

                const filtered = graduateColleges.filter(college =>
                    college.name.toLowerCase().includes(query)
                );

                showSuggestions(filtered, targetSchoolSuggestions, targetSchoolInput, targetSchoolCode);
            });

            // 点击外部隐藏建议
            document.addEventListener('click', function(e) {
                if (!graduationSchoolInput.contains(e.target) && !graduationSchoolSuggestions.contains(e.target) &&
                    !targetSchoolInput.contains(e.target) && !targetSchoolSuggestions.contains(e.target)) {
                    graduationSchoolSuggestions.style.display = 'none';
                    targetSchoolSuggestions.style.display = 'none';
                    
                    // 清理事件监听器
                    if (graduationSchoolInput.scrollHandler) {
                        window.removeEventListener('scroll', graduationSchoolInput.scrollHandler);
                        graduationSchoolInput.scrollHandler = null;
                    }
                    if (graduationSchoolInput.resizeHandler) {
                        window.removeEventListener('resize', graduationSchoolInput.resizeHandler);
                        graduationSchoolInput.resizeHandler = null;
                    }
                    if (targetSchoolInput.scrollHandler) {
                        window.removeEventListener('scroll', targetSchoolInput.scrollHandler);
                        targetSchoolInput.scrollHandler = null;
                    }
                    if (targetSchoolInput.resizeHandler) {
                        window.removeEventListener('resize', targetSchoolInput.resizeHandler);
                        targetSchoolInput.resizeHandler = null;
                    }
                }
            });

            // 学习状态变化处理
            const studyStatusSelect = document.getElementById('study_status');
            const currentStudyStatusSelect = document.getElementById('current_study_status');
            const graduationYearsSelect = document.getElementById('graduation_years');

            studyStatusSelect.addEventListener('change', function() {
                if (this.value === '0') {  // 在校生
                    currentStudyStatusSelect.disabled = false;
                    graduationYearsSelect.disabled = true;
                    graduationYearsSelect.value = '';
                } else if (this.value === '1') {  // 已毕业
                    currentStudyStatusSelect.disabled = true;
                    currentStudyStatusSelect.value = '';
                    graduationYearsSelect.disabled = false;
                } else {  // 未选择
                    currentStudyStatusSelect.disabled = true;
                    graduationYearsSelect.disabled = true;
                }
            });
            
            // 页面加载时触发一次change事件以初始化状态
            studyStatusSelect.dispatchEvent(new Event('change'));

            // 是否考数学变化处理
            const ifMathSelect = document.getElementById('if_math');
            const mathSubjectsContainer = document.getElementById('math_subjects_container');
            const mathMasterySelect = document.getElementById('math_mastery');

            ifMathSelect.addEventListener('change', function() {
                const mathCheckboxes = mathSubjectsContainer.querySelectorAll('input[type="checkbox"]');
                if (this.value === '是') {
                    mathSubjectsContainer.style.display = 'block';
                    mathMasterySelect.disabled = false;
                    mathCheckboxes.forEach(cb => cb.disabled = false);
                } else {
                    mathSubjectsContainer.style.display = 'none';
                    mathMasterySelect.disabled = true;
                    mathCheckboxes.forEach(cb => {
                        cb.disabled = true;
                        cb.checked = false;
                    });
                }
            });

            // 数学科目互斥处理
            const mathNoneCheckbox = document.querySelector('input[value="都没学过"]');
            const otherMathCheckboxes = mathSubjectsContainer.querySelectorAll('input[type="checkbox"]:not([value="都没学过"])');

            if (mathNoneCheckbox) {
                mathNoneCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        otherMathCheckboxes.forEach(cb => cb.checked = false);
                    }
                });
            }

            otherMathCheckboxes.forEach(cb => {
                cb.addEventListener('change', function() {
                    if (this.checked && mathNoneCheckbox) {
                        mathNoneCheckbox.checked = false;
                    }
                });
            });

            // 专业选择处理
            setupMajorSelections();

            // 目标专业代码变化处理
            const targetMajorCodeInput = document.getElementById('target_major_code');
            const syllabusBtn = document.getElementById('syllabusBtn');
            
            targetMajorCodeInput.addEventListener('input', function() {
                checkMajorCodeAndToggleSyllabusButton();
            });
            
            // 初始化时检查一次
            checkMajorCodeAndToggleSyllabusButton();

            // 初始化状态
            studyStatusSelect.dispatchEvent(new Event('change'));
            ifMathSelect.dispatchEvent(new Event('change'));

            // 表单提交处理
            form.addEventListener('submit', handleFormSubmit);
        }

        // 检查目标专业代码并切换408考纲按钮状态
        function checkMajorCodeAndToggleSyllabusButton() {
            const targetMajorCode = document.getElementById('target_major_code').value;
            const syllabusBtn = document.getElementById('syllabusBtn');
            const allowedMajorCodes = ['085400', '081200', '083500', '085404', '085410', '083900'];
            
            if (targetMajorCode && !allowedMajorCodes.includes(targetMajorCode)) {
                syllabusBtn.disabled = true;
                syllabusBtn.title = '该专业不考408';
            } else {
                syllabusBtn.disabled = false;
                syllabusBtn.title = '';
            }
        }

        // 设置专业选择处理
        function setupMajorSelections() {
            // 毕业专业选择
            const graduationCategorySelect = document.getElementById('graduation_major_category');
            const graduationDisciplineSelect = document.getElementById('graduation_major_discipline');
            const graduationMajorCodeInput = document.getElementById('graduation_major_code');

            graduationCategorySelect.addEventListener('change', function() {
                updateMajorDisciplines('graduation');
            });

            graduationDisciplineSelect.addEventListener('change', function() {
                graduationMajorCodeInput.value = this.value;
            });

            // 目标专业选择
            const targetCategorySelect = document.getElementById('target_major_category');
            const targetDisciplineSelect = document.getElementById('target_major_discipline');
            const targetMajorSelect = document.getElementById('target_major');
            const targetMajorCodeInput = document.getElementById('target_major_code');

            targetCategorySelect.addEventListener('change', function() {
                updateGraduateMajorDisciplines();
            });

            targetDisciplineSelect.addEventListener('change', function() {
                updateGraduateMajors();
            });

            targetMajorSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                const majorCode = selectedOption.dataset.code || '';
                const numericCode = majorCode.replace(/[^\d]/g, '');
                targetMajorCodeInput.value = numericCode;
                
                // 检查专业代码并更新408考纲按钮状态
                checkMajorCodeAndToggleSyllabusButton();
            });
        }

        // 更新毕业专业二级门类
        function updateMajorDisciplines(type) {
            const categorySelect = document.getElementById(`${type}_major_category`);
            const disciplineSelect = document.getElementById(`${type}_major_discipline`);
            const selectedCategory = categorySelect.value;

            disciplineSelect.innerHTML = '<option value="">请选择二级门类</option>';

            if (selectedCategory && undergraduateData[selectedCategory]) {
                undergraduateData[selectedCategory].forEach(discipline => {
                    const option = document.createElement('option');
                    option.value = discipline;
                    option.textContent = discipline;
                    disciplineSelect.appendChild(option);
                });
                disciplineSelect.disabled = false;
            } else {
                disciplineSelect.disabled = true;
            }

            if (type === 'graduation') {
                document.getElementById('graduation_major_code').value = '';
            }
        }

        // 更新研究生专业二级门类
        async function updateGraduateMajorDisciplines() {
            const categorySelect = document.getElementById('target_major_category');
            const disciplineSelect = document.getElementById('target_major_discipline');
            const majorSelect = document.getElementById('target_major');
            const selectedCategory = categorySelect.value;

            disciplineSelect.innerHTML = '<option value="">请选择二级门类</option>';
            majorSelect.innerHTML = '<option value="">请选择专业</option>';
            disciplineSelect.disabled = true;
            majorSelect.disabled = true;

            if (!selectedCategory) return;

            try {
                // 修改：使用固定的graduateData数据而不是从API获取
                if (graduateData[selectedCategory]) {
                    graduateData[selectedCategory].forEach(item => {
                        const option = document.createElement('option');
                        option.value = item;
                        option.textContent = item;
                        disciplineSelect.appendChild(option);
                    });
                    disciplineSelect.disabled = false;
                }
            } catch (error) {
                console.error('获取二级门类数据失败:', error);
            }
        }

        // 显示院校建议
        function showSuggestions(colleges, suggestionsElement, inputElement, codeElement) {
            suggestionsElement.innerHTML = '';

            if (colleges.length === 0) {
                suggestionsElement.style.display = 'none';
                return;
            }

            colleges.slice(0, 10).forEach(college => {
                const item = document.createElement('div');
                item.className = 'suggestion-item';
                item.textContent = college.name;
                item.addEventListener('click', function() {
                    inputElement.value = college.name;
                    codeElement.value = college.code;
                    suggestionsElement.style.display = 'none';

                    if (codeElement.id === 'target_school_code') {
                        prefetchAllGraduateMajors(college.code);
                    }
                    // 清理事件监听器
                    if (inputElement.scrollHandler) {
                        window.removeEventListener('scroll', inputElement.scrollHandler);
                        inputElement.scrollHandler = null;
                    }
                    if (inputElement.resizeHandler) {
                        window.removeEventListener('resize', inputElement.resizeHandler);
                        inputElement.resizeHandler = null;
                    }
                });
                suggestionsElement.appendChild(item);
            });

            // 跨容器显示：将下拉框移动到body下
            if (!suggestionsElement._appendedToBody) {
                document.body.appendChild(suggestionsElement);
                suggestionsElement._appendedToBody = true;
            }
            suggestionsElement.style.display = 'block';

            // 更新下拉框位置
            updateSuggestionsPosition(inputElement, suggestionsElement);

            // 添加滚动事件监听器
            const scrollHandler = function() {
                if (suggestionsElement.style.display === 'block') {
                    updateSuggestionsPosition(inputElement, suggestionsElement);
                } else {
                    window.removeEventListener('scroll', scrollHandler);
                }
            };
            if (inputElement.scrollHandler) {
                window.removeEventListener('scroll', inputElement.scrollHandler);
            }
            inputElement.scrollHandler = scrollHandler;
            window.addEventListener('scroll', scrollHandler);

            // 添加窗口大小改变事件监听
            const resizeHandler = function() {
                if (suggestionsElement.style.display === 'block') {
                    updateSuggestionsPosition(inputElement, suggestionsElement);
                }
            };
            if (inputElement.resizeHandler) {
                window.removeEventListener('resize', inputElement.resizeHandler);
            }
            inputElement.resizeHandler = resizeHandler;
            window.addEventListener('resize', resizeHandler);
        }

        // 更新下拉框位置（始终紧贴输入框，跨容器显示）
        function updateSuggestionsPosition(inputElement, suggestionsElement) {
            const inputRect = inputElement.getBoundingClientRect();
            suggestionsElement.style.position = 'fixed';
            suggestionsElement.style.top = (inputRect.bottom) + 'px';
            suggestionsElement.style.left = (inputRect.left) + 'px';
            suggestionsElement.style.width = inputRect.width + 'px';
            suggestionsElement.style.zIndex = '9999';
        }
        
        // 预获取所有研究生专业数据
        async function prefetchAllGraduateMajors(schoolCode) {
            try {
                // 调用我们自己的后端代理接口获取专业数据
                const response = await fetch(`/console/app/kaoyan-learn-query?school_code=${encodeURIComponent(schoolCode)}`);

                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }

                const result = await response.json();
                if (result.code !== 20000) {
                    throw new Error(`API返回错误: ${result.message || '未知错误'}`);
                }

                // 保存数据到全局变量供后续使用
                window.allGraduateMajors = result.data || [];
            } catch (error) {
                console.error('预获取专业数据失败:', error);
                window.allGraduateMajors = []; // 重置数据
            }
        }

        // 更新研究生专业三级专业
        async function updateGraduateMajors() {
            const categorySelect = document.getElementById('target_major_category');
            const disciplineSelect = document.getElementById('target_major_discipline');
            const majorSelect = document.getElementById('target_major');
            const targetMajorCodeInput = document.getElementById('target_major_code');
            const targetSchoolCodeInput = document.getElementById('target_school_code');
            const selectedCategory = categorySelect.value;
            const selectedDiscipline = disciplineSelect.value;

            majorSelect.innerHTML = '<option value="">请选择专业</option>';
            majorSelect.disabled = true;
            targetMajorCodeInput.value = ''; // 清空专业代码

            if (!selectedCategory || !selectedDiscipline) return;

            try {
                // 获取目标院校代码
                const schoolCode = targetSchoolCodeInput.value;
                let majorsData = window.allGraduateMajors;

                // 如果还没有预获取数据，则现场获取
                if (!majorsData || majorsData.length === 0) {
                    if (!schoolCode) {
                        console.warn('目标院校代码为空');
                        return;
                    }

                    // 调用我们自己的后端代理接口获取专业数据
                    const response = await fetch(`/console/app/kaoyan-learn-query?school_code=${encodeURIComponent(schoolCode)}`);

                    if (!response.ok) {
                        throw new Error(`API请求失败: ${response.status}`);
                    }

                    const result = await response.json();
                    if (result.code !== 20000) {
                        throw new Error(`API返回错误: ${result.message || '未知错误'}`);
                    }

                    majorsData = result.data || [];
                }

                // 根据第二级下拉框选项的前四位数字筛选数据
                const disciplineCode = selectedDiscipline.substring(0, 4);
                let filteredMajors = majorsData.filter(item => {
                    // 使用major_code字段进行筛选
                    return item.major_code && item.major_code.startsWith(disciplineCode);
                });

                // 对专业名称去重
                const uniqueMajors = [];
                const seenNames = new Set();

                filteredMajors.forEach(item => {
                    if (!seenNames.has(item.major_name)) {
                        seenNames.add(item.major_name);
                        uniqueMajors.push(item);
                    }
                });

                // 填充第三级下拉框
                uniqueMajors.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.major_name || ''; // 使用major_name字段
                    option.textContent = item.major_name || ''; // 使用major_name字段
                    option.dataset.code = item.major_code || ''; // 使用major_code字段
                    majorSelect.appendChild(option);
                });

                majorSelect.disabled = uniqueMajors.length === 0;

                // 添加专业选择事件监听器
                majorSelect.onchange = function() {
                    const selectedOption = this.options[this.selectedIndex];
                    const majorCode = selectedOption.dataset.code || '';
                    targetMajorCodeInput.value = majorCode;
                };
            } catch (error) {
                console.error('获取专业数据失败:', error);
                // 显示错误信息给用户
                alert('获取专业数据失败: ' + error.message);
            }
        }

    // 处理表单提交
    async function handleFormSubmit(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('submitBtn');
        const loadingContainer = document.getElementById('loadingContainer');
        const resultContainer = document.getElementById('resultContainer');
        const errorContainer = document.getElementById('errorContainer');
        const initialPrompt = document.getElementById('initialPrompt');

        // 重置显示状态
        loadingContainer.style.display = 'none';
        resultContainer.style.display = 'none';
        errorContainer.style.display = 'none';
        initialPrompt.style.display = 'none';

        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.textContent = '提交中...';

        try {
            // 收集表单数据 - 使用新的数据结构
            const formData = {
                user_id: document.getElementById('user_id').value,
                exam_date: document.getElementById('exam_date').value,
                graduation_info: {
                    graduation_school: document.getElementById('graduation_school').value,
                    graduation_school_code: document.getElementById('graduation_school_code').value,
                    graduation_major: document.querySelector('[name="graduation_major_discipline"]').value,
                    graduation_major_code: document.getElementById('graduation_major_code').value
                },
                education_level: parseInt(document.getElementById('education_level').value),
                study_status: parseInt(document.getElementById('study_status').value),
                graduation_years: document.getElementById('graduation_years').value ? 
                                  parseInt(document.getElementById('graduation_years').value) : null,
                study_stage: document.getElementById('current_study_status').value ? 
                             parseInt(document.getElementById('current_study_status').value) : null,
                academic_performance: document.getElementById('academic_performance').value ? 
                                      parseInt(document.getElementById('academic_performance').value) : null,
                english_level: document.getElementById('english_level').value ? 
                               parseInt(document.getElementById('english_level').value) : null,
                math_subjects: Array.from(document.querySelectorAll('input[name="math_subjects"]:checked'))
                               .map(cb => parseInt(cb.value)),
                math_mastery: document.getElementById('math_mastery').value ? 
                              parseInt(document.getElementById('math_mastery').value) : null
            };

            // 验证必填字段
            if (!validateFormData(formData)) {
                throw new Error('请填写所有必填字段');
            }

            // 显示加载状态
            loadingContainer.style.display = 'block';

            // 提交分析任务
            const taskId = await submitAnalysisTask(formData);

            // 保存task_id到隐藏字段
            document.getElementById('current_task_id').value = taskId;

            // 显示task_id
            const loadingTaskId = document.getElementById('loadingTaskId');
            loadingTaskId.textContent = `任务ID: ${taskId}`;

            // 开始轮询任务状态
            await pollTaskStatus(taskId);

        } catch (error) {
            showError(error.message);
        } finally {
            // 恢复提交按钮
            submitBtn.disabled = false;
            submitBtn.textContent = '提交分析';
        }
    }

    // 验证表单数据
    function validateFormData(data) {
        const required = [
            data.user_id, 
            data.exam_date, 
            data.education_level,
            data.study_status
        ];
        
        // 检查必填字段
        const isValid = required.every(value => value !== null && value !== undefined && value !== '');
        
        // 检查学习状态相关字段
        if (data.study_status === 0 && data.study_stage === null) {
            return false; // 在校生必须填写在读状态
        }
        if (data.study_status === 1 && data.graduation_years === null) {
            return false; // 已毕业必须填写毕业年限
        }
        
        return isValid;
    }

        // 提交分析任务
        async function submitAnalysisTask(formData) {
            // 构建目标院校信息
            let targetInfo = {}; // 改为对象而不是数组
            const targetSchool = document.getElementById('target_school').value;
            if (targetSchool) {
                targetInfo = {
                    target_school: targetSchool,
                    target_school_code: document.getElementById('target_school_code').value || '',
                    target_major: document.getElementById('target_major').value || '',
                    target_major_code: document.getElementById('target_major_code').value || ''
                };
            }

            // 构建API请求数据
            const apiData = {
                user_id: formData.user_id,
                start_date: new Date().toISOString().split('T')[0],
                exam_date: formData.exam_date,
                graduation_info: {
                    graduation_school: formData.graduation_info.graduation_school,
                    graduation_school_code: formData.graduation_info.graduation_school_code || '',
                    graduation_major: formData.graduation_info.graduation_major || '',
                    graduation_major_code: formData.graduation_info.graduation_major_code || ''
                },
                target: targetInfo, // 现在是对象而不是数组
                education_level: formData.education_level,
                study_status: formData.study_status,
                graduation_years: formData.graduation_years ,
                study_stage: formData.study_stage || null,
                academic_performance: formData.academic_performance ,
                english_level: formData.english_level ,
                math_subjects: formData.math_subjects,
                math_mastery: formData.math_mastery ,
                political_subjects: [],
                political_mastery: null
            };

            console.log('提交的API数据:', JSON.stringify(apiData, null, 2));
            if (Object.keys(apiData.target).length > 0) { // 检查对象是否为空
                console.log('目标院校信息:', apiData.target);
            }

            const response = await fetch('/api/v1/learning_status/supervise_init/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify(apiData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`提交失败: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('API响应:', result);
            const taskId = result.data?.task_id || result.task_id;
            console.log('解析的task_id:', taskId);
            return taskId;
        }

        // 轮询任务状态
        async function pollTaskStatus(taskId) {
            const maxAttempts = 120; // 10分钟 (120 * 5秒)
            let attempts = 0;

            return new Promise((resolve, reject) => {
                const checkStatus = async () => {
                    try {
                        attempts++;
                        updateLoadingProgress(attempts, maxAttempts);

                        const response = await fetch(`/api/v1/learning_status/supervise_init/check?task_id=${taskId}`);

                        if (!response.ok) {
                            throw new Error(`状态检查失败: ${response.status}`);
                        }

                        const result = await response.json();
                        console.log(`轮询结果 (第${attempts}次):`, result);
                        console.log('当前状态:', result.data.status)

                        if (result.data.status === 'SUCCESS') {
                            console.log('任务完成，显示结果');
                            showResult(result.data.analysis);
                            resolve(result);
                        } else if (result.code === -2) {
                            throw new Error(`分析失败: ${result.message || '未知错误'}`);
                        } else if (attempts >= maxAttempts) {
                            throw new Error('分析超时，请稍后重试');
                        } else {
                            // 继续轮询，每5秒检查一次
                            console.log(`任务进行中，${5}秒后再次检查...`);
                            setTimeout(checkStatus, 5000);
                        }
                    } catch (error) {
                        reject(error);
                    }
                };

                checkStatus();
            });
        }

        // 更新加载进度
        function updateLoadingProgress(attempts, maxAttempts) {
            const progress = Math.min((attempts / maxAttempts) * 100, 100);
            const minutes = Math.floor(attempts * 5 / 60);
            const seconds = (attempts * 5) % 60;

            const loadingProgress = document.getElementById('loadingProgress');
            loadingProgress.textContent = `已等待 ${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        // 显示分析结果
        // 显示分析结果
        function showResult(analysis) {
            const loadingContainer = document.getElementById('loadingContainer');
            const resultContainer = document.getElementById('resultContainer');
            const resultContent = document.getElementById('resultContent');

            loadingContainer.style.display = 'none';
            resultContainer.style.display = 'block';
            // 使用marked渲染Markdown内容
            resultContent.innerHTML = marked.parse(analysis);
        }

        // 显示错误信息
        function showError(message) {
            const loadingContainer = document.getElementById('loadingContainer');
            const errorContainer = document.getElementById('errorContainer');
            const errorMessage = document.getElementById('errorMessage');

            loadingContainer.style.display = 'none';
            errorContainer.style.display = 'block';
            errorMessage.textContent = message;
        }

        // 获取CSRF Token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // 获取或创建CSRF Token
        function getCSRFToken() {
            let token = getCookie('csrftoken');
            if (!token) {
                // 如果没有CSRF token，尝试从meta标签获取
                const metaToken = document.querySelector('meta[name="csrf-token"]');
                if (metaToken) {
                    token = metaToken.getAttribute('content');
                }
            }
            return token;
        }

        // 保存提示词到数据库
        function savePromptToDB() {
            const newPrompt = document.getElementById('prompt-textarea').value;

            // 发送POST请求保存到数据库
            fetch('/console/app/save_prompt_template', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: 'prompt_content=' + encodeURIComponent(newPrompt)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // 更新页面显示
                    localStorage.setItem('customPrompt', newPrompt);
                    showMessage('提示词已保存到数据库', 'success');
                } else {
                    showMessage('保存失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('保存失败: ' + error.message, 'error');
            });
        }

        // 显示消息
        function showMessage(message, type) {
            const messageArea = document.getElementById('message-area');
            messageArea.innerHTML = '<div class="message ' + type + '">' + message + '</div>';

            // 3秒后自动隐藏消息
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }

        // 检查目标专业代码并切换408考纲按钮状态
        function checkMajorCodeAndToggleSyllabusButton() {
            const targetMajorCode = document.getElementById('target_major_code').value;
            const syllabusBtn = document.getElementById('syllabusBtn');
            const allowedMajorCodes = ['085400', '081200', '083500', '085404', '085410', '083900'];
            
            if (targetMajorCode && !allowedMajorCodes.includes(targetMajorCode)) {
                syllabusBtn.disabled = true;
                syllabusBtn.title = '该专业不考408';
            } else {
                syllabusBtn.disabled = false;
                syllabusBtn.title = '';
            }
        }

        // 跳转到个性考纲分析指导页面
        function goToSyllabusGuide() {
            const taskId = document.getElementById('current_task_id').value;
            if (!taskId) {
                alert('请先提交分析任务并等待完成');
                return;
            }
            
            // 检查是否已经存在名为'syllabusGuide'的标签页
            if (window.syllabusGuideTab && !window.syllabusGuideTab.closed) {
                // 如果标签页已存在且未关闭，则更新标签页中的内容
                window.syllabusGuideTab.location.href = `/console/app/syllabus_guide_index?task_id=${taskId}`;
                window.syllabusGuideTab.focus(); // 聚焦到该标签页
            } else {
                // 如果标签页不存在或已关闭，则创建新标签页
                window.syllabusGuideTab = window.open(`/console/app/syllabus_guide_index?task_id=${taskId}`, 'syllabusGuide');
            }
        }

        // 获取408个性化考纲
        async function get408Syllabus() {
            const taskId = document.getElementById('current_task_id').value;
            if (!taskId) {
                alert('请先提交分析任务并等待完成');
                return;
            }

            // 检查目标专业代码是否符合要求
            const targetMajorCode = document.getElementById('target_major_code').value;
            const allowedMajorCodes = ['085400', '081200', '083500', '085404', '085410', '083900'];
            
            if (!allowedMajorCodes.includes(targetMajorCode)) {
                alert('该专业不考408');
                return;
            }

            const syllabusBtn = document.getElementById('syllabusBtn');
            const syllabusLoading = document.getElementById('syllabusLoading');
            const syllabusContentContainer = document.getElementById('syllabusContentContainer');
            const syllabusContent = document.getElementById('syllabusContent');
            const syllabusErrorContainer = document.getElementById('syllabusErrorContainer');
            const syllabusError = document.getElementById('syllabusError');

            // 重置显示状态
            syllabusContentContainer.style.display = 'none';
            syllabusErrorContainer.style.display = 'none';
            syllabusLoading.style.display = 'block';
            syllabusBtn.disabled = true;

            try {
                const response = await fetch(`/console/app/408_personal_syllabus?task_id=${encodeURIComponent(taskId)}`);
                
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.status === 'success') {
                    // 处理内容以确保不会超出边界
                    const content = result.data || '';
                    // 使用marked渲染Markdown内容
                    syllabusContent.innerHTML = marked.parse(content);
                    syllabusContentContainer.style.display = 'block';
                } else {
                    const errorMessage = result.message || '获取考纲失败';
                    syllabusError.textContent = errorMessage;
                    syllabusErrorContainer.style.display = 'block';
                }
            } catch (error) {
                const errorMessage = `获取考纲失败: ${error.message}`;
                syllabusError.textContent = errorMessage;
                syllabusErrorContainer.style.display = 'block';
            } finally {
                syllabusLoading.style.display = 'none';
                syllabusBtn.disabled = false;
            }
        }
    </script>
</body>
</html>