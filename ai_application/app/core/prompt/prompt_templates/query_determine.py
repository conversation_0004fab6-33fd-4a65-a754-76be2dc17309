import re

from app.core.output_parsers.regex import <PERSON>exParser
from app.core.prompt.utils.prompt_template_parser import PromptTemplateParser

output_parser = RegexParser(
    regex=re.compile(r"Result: (\d*)", re.IGNORECASE),
    output_keys=["result"],
    default_output_key='result'
)

PROMPT_TMPL_EN = """Determine whether the user input is a valid question. This should be in the following format:

Input: [input here]
Result: [result 1 or 0]

How to determine the result:
- if it is a greeting, result is 0
- If the input is information or a topic, it is considered a consultation on that information, result is 1
- If it is a request for help, it is also considered a question, result is 1

Input: {{question}}
"""

PROMPT = PromptTemplateParser(PROMPT_TMPL_EN)

