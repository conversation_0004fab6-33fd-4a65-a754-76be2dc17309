import json
import logging
import re
from urllib.parse import urlparse, parse_qs

from bs4 import BeautifulSoup
from mathml2latex.mathml import process_mathml

logger = logging.getLogger(__name__)


def safe_parse_json_response(response_text: str):
    """安全解析JSON响应，处理各种格式问题"""
    try:
        return json.loads(response_text)
    except json.JSONDecodeError as e:
        json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
        if json_match:
            json_text = json_match.group(1).strip()

            try:
                return json.loads(json_text)
            except Exception as e:
                logger.error(f"JSON解析错误: {e}")
        return None


def parse_question_title(title) -> str:
    pattern = r'<span\s*[^>]*?></span>'
    # 执行替换，将所有匹配的空span标签替换为空字符串
    title = re.sub(pattern, '', title, flags=re.IGNORECASE).strip()

    pattern = r'<p\s*[^>]*?></p>'
    title = re.sub(pattern, '', title, flags=re.IGNORECASE).strip()
    title = title.strip()

    # 按照p标签分隔
    pattern = r'<p[^>]*>(.*?)</p>'
    contents = re.findall(pattern, title, re.DOTALL)
    return '\n'.join(contents) if contents else title


def parse_objective_question(question_content_str: str):
    choice_map = ["A", "B", "C", "D", "E", "F"]

    question_content = json.loads(question_content_str)

    title = parse_question_title(question_content['title'])
    answer = question_content['answer']
    right_answer = []
    choices = []
    for c_idx, c in enumerate(question_content['choices']):
        label = choice_map[c_idx]
        c_id = c['id']
        if c_id in answer:
            right_answer.append(label)

        # 去除body开头结尾的p标签
        body = c['body']
        if body.startswith('<p>'):
            body = body[3:]
        if body.endswith('</p>'):
            body = body[:-4]
        body = body.strip()
        while body.endswith('&nbsp;'):
            body = body[:-6]
            body = body.strip()

        # +-符合会被识别成列表，而选项中不会出现，需要转义
        if body.startswith('-') or body.startswith('+'):
            body = f'\\{body}'
        choices.append({
            'id': label,
            'body': body,
        })

    return {
        'title': title,
        'choices': choices,
        'right_answer': right_answer,
    }


def parse_question_str(question_type: int, question_content_str: str):
    if question_type in [0, 1]:
        q_info = parse_objective_question(question_content_str)
        title = replace_html_latex(q_info['title'])
        choices = q_info['choices']
        for c in choices:
            c['body'] = replace_html_latex(c['body'])

        choices_str = '\n'.join([f'{c["id"]}. {c["body"]}' for c in choices])
        return f'{title}\n{choices_str}\n答案：{"".join(q_info["right_answer"])}'
    else:
        question_content = json.loads(question_content_str)
        return parse_question_title(question_content['title'])


def replace_html_latex(html_string):
    pattern = r'<img[^>]+>'
    matches = re.findall(pattern, html_string)
    for m in matches:
        pattern = r'latex="([^"]+)"'
        latex_values = re.findall(pattern, m)
        if latex_values:
            html_string = html_string.replace(m, f'${latex_values[0]}$')
            continue

        # 解析mml格式
        pattern = r'src="([^"]+)"'
        mml_src_values = re.findall(pattern, m)
        if mml_src_values:
            # 解析URL
            parsed_url = urlparse(mml_src_values[0])
            # 解析查询参数
            query_params = parse_qs(parsed_url.query)
            if 'mml' not in query_params:
                continue
            mml_value = query_params['mml'][0]
            try:
                soup = BeautifulSoup(mml_value, 'xml')
                mml_parsed_value = process_mathml(soup)
                html_string = html_string.replace(m, f'${mml_parsed_value}$')
            except:
                pass

    return html_string


def wash_question_content():
    from app.models import STQuestion
    # qs = STQuestion.objects.filter(question_type=0)
    # qs = STQuestion.objects.filter(question_type__gt=0)
    qs = STQuestion.objects.all()

    for q in qs:
        # 提取question_content内img标签内容
        if q.question_type == 0:
            res = parse_objective_question(q.question_content)
            body = replace_html_latex(res['title'])
            choices = []
            for c in res['choices']:
                choice = replace_html_latex(c['body'])
                choices.append(choice)

            q.format_question_content = {
                'title': body,
                'choices': choices,
                'choices_answer': res['right_answer']
            }
            q.save()
        else:
            question_content = json.loads(q.question_content)
            body = parse_question_title(question_content['title'])
            body = replace_html_latex(body)
            q.format_question_content = {
                'title': body,
            }
            q.save()
