from PIL import Image, ImageDraw, ImageFont
import re
import textwrap
import matplotlib.pyplot as plt
import io
import os

# 渲染LaTeX成图片
def render_latex_to_image(latex: str, base_height=28, max_height=60, dpi=200):
    try:
        complexity = (
            latex.count('\\') +
            latex.count('{') +
            latex.count('^') +
            latex.count('_') +
            latex.count('frac') +
            len(latex) // 10
        )
        target_height = min(max_height, max(base_height, 20 + complexity * 2))
        fontsize = 25 + int(complexity * 0.8)

        fig = plt.figure(figsize=(0.01, 0.01))
        fig.text(0, 0, f"${latex}$", fontsize=fontsize)
        buf = io.BytesIO()
        plt.axis('off')
        fig.savefig(buf, format='png', dpi=dpi, bbox_inches='tight', transparent=True, pad_inches=0.1)
        plt.close(fig)
        buf.seek(0)

        img = Image.open(buf).convert("RGBA")
        w, h = img.size
        scale = target_height / h
        new_w, new_h = int(w * scale), int(h * scale)
        resized_img = img.resize((new_w, new_h), Image.LANCZOS)
        return resized_img, (new_w, new_h)
    except Exception as e:
        print(f"公式渲染失败: {latex}\n错误: {e}")
        return None, (0, 0)

# 绘制文字（有间距）
def draw_text_with_spacing(draw, position, text, font, fill, spacing):
    x, y = position
    for char in text:
        draw.text((x, y), char, font=font, fill=fill)
        char_width = font.getbbox(char)[2]
        x += char_width + spacing
    return x

# 计算字符串宽度（包含间距）
def get_text_width(text, font, spacing):
    return sum(font.getbbox(c)[2] + spacing for c in text)

# 主函数
def geneContent(section_title, knowledge_definition, save_path, vid):
    title = section_title
    definition = knowledge_definition

    titlefont_path = "app/core/features/text_to_video/fonts/SourceHanSansSC-Bold.otf"
    textfont_path = "app/core/features/text_to_video/fonts/SourceHanSansSC-Regular.otf"
    title_font = ImageFont.truetype(titlefont_path, 50)
    text_font = ImageFont.truetype(textfont_path, 25)

    title_spacing = 3
    text_spacing = 2

    final_width = 1280
    final_height = 720
    max_line_pixel_width = 1120

    image = Image.new("RGB", (final_width, final_height), color=(255, 255, 255))
    draw = ImageDraw.Draw(image)

    title_img_path = f"{save_path}/{vid}.png"
    title_x = 75
    title_y = 150  # 定义标题图片的起始y坐标
    if os.path.exists(title_img_path):
        title_img = Image.open(title_img_path).convert("RGBA")
        tw, th = title_img.size
        if th<300:
        # 居中绘制
            title_img_x = (final_width - tw) // 2  # 计算居中x坐标
            image.paste(title_img, (title_img_x, title_y), title_img)
            y = title_y + th + 50  # 调整下一个段落的起始y
        else:
            title_y = 200
            # 原始文字标题绘制
            draw_text_with_spacing(draw, (title_x, title_y), title, font=title_font, fill=(0, 0, 0), spacing=title_spacing)
            y = title_y + 100
    else:
        title_y = 200
        # 原始文字标题绘制
        draw_text_with_spacing(draw, (title_x, title_y), title, font=title_font, fill=(0, 0, 0), spacing=title_spacing)
        y = title_y + 100

    bbox = text_font.getbbox("示例")
    base_line_height = bbox[3] - bbox[1] + 15
    x = title_x

    # 切分成公式/文本片段
    pattern = re.compile(r'(\$\$.*?\$\$|\$.*?\$)', re.DOTALL)
    parts = pattern.split(definition)
    parts = [p for p in parts if p.strip() != '']

    current_line_height = base_line_height

    for i in range(len(parts)):
        parts[i] = parts[i].strip()

        # 块级公式
        if parts[i].startswith('$$') and parts[i].endswith('$$'):
            formula = parts[i][2:-2].strip()
            formula_img, (fw, fh) = render_latex_to_image(formula, base_height=50, max_height=90)
            if formula_img:
                x = title_x
                y += current_line_height  # 上一段完成后再插入块公式
                fx = (final_width - fw) // 2
                image.paste(formula_img, (fx, y), formula_img)
                y += fh + base_line_height
                current_line_height = base_line_height
            continue

        # 行内公式
        if parts[i].startswith('$') and parts[i].endswith('$'):
            formula = parts[i][1:-1].strip()
            formula_img, (fw, fh) = render_latex_to_image(formula)
            if formula_img:
                if x + fw > title_x + max_line_pixel_width:
                    x = title_x
                    y += current_line_height
                    current_line_height = base_line_height
                image.paste(formula_img, (x, y + (current_line_height - fh) // 2), formula_img)
                x += fw + 5
                current_line_height = max(current_line_height, fh + 5)
            else:
                fallback_width = get_text_width(parts[i], text_font, text_spacing)
                if x + fallback_width > title_x + max_line_pixel_width:
                    x = title_x
                    y += current_line_height
                    current_line_height = base_line_height
                x = draw_text_with_spacing(draw, (x, y), parts[i], text_font, (0, 0, 0), text_spacing)
            continue

        # 普通文本段落
        lines = parts[i].split('\n')
        for line in lines:
            print(line+'\n\n')
            word_buffer = ''
            for ch in line:
                word_buffer += ch
                word_width = get_text_width(word_buffer, text_font, text_spacing)
                if x + word_width > title_x + max_line_pixel_width:
                    print(word_buffer)
                    draw_text_with_spacing(draw, (x, y), word_buffer[:-1], text_font, (0, 0, 0), text_spacing)
                    x = title_x
                    y += current_line_height
                    current_line_height = base_line_height
                    word_buffer = ch
                    print(word_buffer+'\n')
            # 剩余部分
            x = draw_text_with_spacing(draw, (x, y), word_buffer, text_font, (0, 0, 0), text_spacing)
            if parts[i].startswith('$$') and parts[i].endswith('$$'):
                y += current_line_height
                x = title_x
            elif parts[i].startswith('$') and parts[i].endswith('$'):
                x =title_x+word_width
            current_line_height = base_line_height

    # 保存图像
    image.save(f"{save_path}/scene{vid}.jpeg")
    print(f"✅ 已保存图片 scene{vid}.jpeg")
