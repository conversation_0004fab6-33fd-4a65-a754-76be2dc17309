--
-- Create model AssistantRequest
--
CREATE TABLE `app_assistantrequest`
(
    `id`             bigint AUTO_INCREMENT NOT NULL PRIMARY KEY,
    `system_prompt`  longtext    NOT NULL,
    `user_input`     longtext    NOT NULL,
    `model_response` longtext    NOT NULL,
    `created_at`     datetime(6) NOT NULL,
    `subject_id`     varchar(50) NOT NULL
);
--
-- Create model SubjectPrompt
--
CREATE TABLE `ai_subject_prompt`
(
    `subject_id`  varchar(50)  NOT NULL PRIMARY KEY,
    `name`        varchar(100) NOT NULL,
    `prompt`      longtext     NOT NULL,
    `real_prompt` longtext NULL,
    `updated_at`  datetime(6) NOT NULL
);