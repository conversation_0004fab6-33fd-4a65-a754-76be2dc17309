from django.db import models

from ._base import BaseModel


class EnWordReciteQuestion(BaseModel):
    class QuestionType(models.TextChoices):
        en2ch = ('en2ch', '英译中')
        multi_define = ('multi_define', '一词多义')
        relate_define = ('relate_define', '同义反义')

    word = models.ForeignKey('EnglishWordLibrary', on_delete=models.CASCADE, db_constraint=False)
    question_type = models.CharField(
        '问题类型', max_length=16,
        choices=QuestionType.choices, default=QuestionType.en2ch
    )
    question = models.TextField('题干', null=True)
    options = models.JSONField('选项', null=True)
    analysis = models.TextField('解析', null=True)
    answer = models.TextField('答案', null=True)
    example_sentence = models.TextField('例句', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '英语背单词题目'
        db_table = 'ai_english_word_recite_question'


class EnWordReciteBasicPaper(BaseModel):
    class PaperType(models.TextChoices):
        basic_test = ('basic_test', '摸底测')
        week_test = ('week_test', '周测')

    paper_type = models.CharField(
        '试卷类型', max_length=32, choices=PaperType.choices, default=PaperType.basic_test)
    user_id = models.CharField('用户id', max_length=100, db_index=True)
    question_num = models.IntegerField('题目数量', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '摸底测试卷'
        db_table = 'ai_english_word_recite_basic_paper'


class EnWordReciteBasicPaperDetail(BaseModel):
    basic_paper = models.ForeignKey(EnWordReciteBasicPaper, on_delete=models.CASCADE, db_constraint=False)
    question = models.ForeignKey(EnWordReciteQuestion, on_delete=models.CASCADE, db_constraint=False)

    class Meta:
        verbose_name = verbose_name_plural = '摸底测试卷详情'
        db_table = 'ai_english_word_recite_basic_paper_detail'


class EnWordReciteBasicAnswer(BaseModel):
    user_id = models.CharField('用户id', max_length=100, db_index=True)
    basic_paper = models.ForeignKey(EnWordReciteBasicPaper, on_delete=models.CASCADE, db_constraint=False)
    right_num = models.IntegerField('正确数量', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '摸底测试答卷'
        db_table = 'ai_english_word_recite_basic_answer'


class EnWordReciteBasicAnswerDetail(BaseModel):
    basic_paper = models.ForeignKey(EnWordReciteBasicPaper, on_delete=models.CASCADE, db_constraint=False)
    basic_answer = models.ForeignKey(EnWordReciteBasicAnswer, on_delete=models.CASCADE, db_constraint=False)
    question = models.ForeignKey(EnWordReciteQuestion, on_delete=models.CASCADE, db_constraint=False)
    word = models.ForeignKey('EnglishWordLibrary', on_delete=models.CASCADE, db_constraint=False)
    user_answer = models.CharField('用户答案', max_length=16, blank=True)
    is_right = models.BooleanField('是否正确', default=False)
    is_answered = models.BooleanField('是否答题', default=False)
    is_exception = models.BooleanField(default=False, verbose_name='是否异常')
    exception_reason = models.TextField(null=True, verbose_name='异常原因')
    exception_image = models.TextField(null=True, verbose_name='异常图片')

    class Meta:
        verbose_name = verbose_name_plural = '摸底测试答卷详情'
        db_table = 'ai_english_word_recite_basic_answer_detail'


class EnWordRecitePlan(BaseModel):
    user_id = models.CharField('用户id', max_length=100, db_index=True)
    basic_answer = models.ForeignKey(EnWordReciteBasicAnswer, on_delete=models.CASCADE, db_constraint=False)
    gen_plan_content = models.TextField('生成计划内容', null=True)
    # {"high":24,"low":12,"middle":24,"total":60,"study_plan":"每天背诵 60 个单词，其中高频词汇 24 个，中频词汇 24 个，低频词汇 12 个"}
    plan_content = models.JSONField('计划详情', null=True)
    # ING SUCCESS FAIL
    gen_status = models.CharField('生成状态', max_length=16, blank=True)
    last_plan_gen_date = models.DateField('上一个计划生成日期', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '背单词计划'
        db_table = 'ai_english_word_recite_plan'


class EnWordRecitePlanRecord(BaseModel):
    class UseStatus(models.TextChoices):
        init = ('init', '未处理')
        used = ('used', '已使用')
        refused = ('refused', '已拒绝')

    plan = models.ForeignKey(EnWordRecitePlan, on_delete=models.CASCADE, db_constraint=False)
    gen_plan_content = models.TextField('生成计划内容', null=True)
    plan_content = models.JSONField('计划详情', null=True)
    # ING SUCCESS FAIL
    gen_status = models.CharField('生成状态', max_length=16, blank=True)
    gen_fail_reason = models.TextField('生成错误原因', null=True)
    use_status = models.CharField('使用状态', max_length=32, choices=UseStatus.choices, default=UseStatus.init)
    is_manual = models.BooleanField('是否手动生成', default=False)

    class Meta:
        verbose_name = verbose_name_plural = '背单词计划调整记录'
        db_table = 'ai_english_word_recite_plan_record'


class EnWordReciteDayPlan(BaseModel):
    plan = models.ForeignKey(EnWordRecitePlan, on_delete=models.CASCADE, db_constraint=False)
    plan_record = models.ForeignKey(EnWordRecitePlanRecord, on_delete=models.CASCADE, db_constraint=False)
    day = models.DateField('背诵日期')
    recite_words = models.JSONField('背诵单词列表', null=True)
    review_words = models.JSONField('复习单词列表', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '背单词当日计划单词表'
        db_table = 'ai_english_word_recite_day_plan'


class EnWordReciteDayRecord(BaseModel):
    plan = models.ForeignKey(EnWordRecitePlan, on_delete=models.CASCADE, db_constraint=False)
    plan_record = models.ForeignKey(EnWordRecitePlanRecord, on_delete=models.CASCADE, db_constraint=False)
    day_plan = models.ForeignKey(EnWordReciteDayPlan, on_delete=models.CASCADE, db_constraint=False, null=True)
    question = models.ForeignKey(EnWordReciteQuestion, on_delete=models.CASCADE, db_constraint=False)
    word = models.ForeignKey('EnglishWordLibrary', on_delete=models.CASCADE, db_constraint=False)
    day = models.DateField('背诵日期')
    day_seq = models.IntegerField('第几日', default=1)  # 暂无用
    user_answer = models.CharField('用户答案', max_length=16, blank=True)
    is_right = models.BooleanField('是否正确', default=False)

    class Meta:
        verbose_name = verbose_name_plural = '用户每日背诵记录'
        db_table = 'ai_english_word_recite_day_record'


class EnWordReciteReviewRecord(BaseModel):
    plan = models.ForeignKey(EnWordRecitePlan, on_delete=models.CASCADE, db_constraint=False)
    plan_record = models.ForeignKey(EnWordRecitePlanRecord, on_delete=models.CASCADE, db_constraint=False)
    day_plan = models.ForeignKey(EnWordReciteDayPlan, on_delete=models.CASCADE, db_constraint=False, null=True)
    question = models.ForeignKey(EnWordReciteQuestion, on_delete=models.CASCADE, db_constraint=False)
    word = models.ForeignKey('EnglishWordLibrary', on_delete=models.CASCADE, db_constraint=False)
    review_day = models.DateField('复习日期')
    user_answer = models.CharField('用户答案', max_length=16, blank=True)
    is_right = models.BooleanField('是否正确', default=False)

    class Meta:
        verbose_name = verbose_name_plural = '用户复习记录'
        db_table = 'ai_english_word_recite_review_record'


class EnWordReciteReviewPlan(BaseModel):
    word = models.ForeignKey('EnglishWordLibrary', on_delete=models.CASCADE, db_constraint=False)
    first_add_day = models.DateField('首次记录日期')
    review_num = models.IntegerField('复习次数', default=0)
    is_review_continue = models.BooleanField('是否继续复习', default=True)
    next_review_day = models.DateField('下次记录日期', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '用户复习计划'
        db_table = 'ai_english_word_recite_review_plan'
