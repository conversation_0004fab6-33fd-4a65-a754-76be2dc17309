import json

from app.core.apps.base_app_queue_manager import AppQueue<PERSON>anager
from app.core.entities.app_entities import ChatAppGenerateEntity
from app.core.features.code_optimization import CodeOptimizationFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError
from app.models import Message


class ChatCodeWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

        self.code_prompts = self.inputs.get('code_prompts') or {}
        is_debug = self.inputs.get('is_debug', False)
        if is_debug:
            self.code_prompts = self.inputs.get('debug_code_prompts')

    def run(self):
        try:
            query_dict = json.loads(self.application_generate_entity.query)
            query = query_dict.get('content')
            lang_code = query_dict.get('lang_code')
        except Exception:
            raise ParameterError(detail='代码优化参数错误')

        result = CodeOptimizationFeature().run(
            application_generate_entity=self.application_generate_entity,
            lang_code=lang_code,
            lang_content=query,
            stream=True,
            prompt_content=self.code_prompts.get('code_optimization') or ''
        )

        tracing_info = {
            'tracing_type': 'code_optimization', 'query': query
        }
        self._handle_node_generate_response(result, tracing_info=tracing_info)
        self._publish_workflow_end_message()
