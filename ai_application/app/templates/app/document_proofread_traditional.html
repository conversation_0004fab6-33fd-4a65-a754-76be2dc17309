<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .page-title {
            margin: 20px auto;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .container {
            display: flex;
            max-width: 1400px;
            min-height: 650px;
            margin: 20px auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .left-column, .right-column {
            padding: 20px;
            box-sizing: border-box;
        }
        .left-column {
            width: 45%;
            background-color: #f9f9f9;
            border-right: 1px solid #eaeaea;
        }
        .right-column {
            width: 55%;
            min-height: 650px;
            overflow-y: auto;
            border-left: 1px solid #eaeaea;
        }
        .left-column h2 {
            color: #333;
            margin-bottom: 15px;
        }
        .left-column label {
            display: block;
            margin-bottom: 5px;
            color: #666;
            font-weight: 500;
        }
        .left-column form {
            display: flex;
            flex-direction: column;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group textarea, .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
        }
        .form-group textarea {
            resize: vertical;
            min-height: 150px;
        }
        .form-group input[type="text"] {
            height: 40px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            width: auto;
        }
        .checkbox-group label {
            margin: 0;
            color: #666;
            cursor: pointer;
        }
        .left-column button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .left-column button:hover {
            background-color: #0056b3;
        }
        .left-column button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        /* 结果展示区域 */
        .result-section {
            margin-bottom: 20px;
        }
        .result-section h3 {
            color: #333;
            margin-bottom: 10px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .stats-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .stats-info .stat-item {
            display: inline-block;
            margin-right: 20px;
            font-weight: 500;
        }
        .error-list {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .error-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-left: 4px solid #dc3545;
            border-radius: 3px;
        }
        .error-item .error-id {
            font-weight: bold;
            color: #dc3545;
        }
        .error-item .error-text {
            color: #721c24;
            text-decoration: line-through;
        }
        .error-item .suggestion {
            color: #155724;
            font-weight: 500;
        }
        .labeled-text {
            background-color: #f8f9fa;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: "Courier New", monospace;
            line-height: 1.6;
        }
        /* 错误标记样式 */
        .labeled-text .error-mark {
            background-color: #ffcccc;
            text-decoration: line-through;
            color: #d32f2f;
            font-weight: bold;
        }
        .labeled-text .error-annotation {
            background-color: #e3f2fd;
            color: #1976d2;
            font-size: 0.9em;
            padding: 2px 4px;
            margin: 0 2px;
            border-radius: 3px;
            border: 1px solid #bbdefb;
            font-family: monospace;
        }
        /* 加载覆盖层 */
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            color: #333333;
            text-align: center;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 加载覆盖层 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在智能审校中，请稍候...</p>
        </div>
    </div>

    <div class="page-title">
        <i class="fas fa-spell-check"></i> {{ page_title }}
    </div>
    
    <div class="container">
        <!-- 左侧输入区域 -->
        <div class="left-column">
            <h2><i class="fas fa-edit"></i> 审校设置</h2>
            <form id="proofreadForm">
                <div class="form-group">
                    <label for="content">
                        <i class="fas fa-file-text"></i> 待审校文本：
                    </label>
                    <textarea id="content" name="content" placeholder="请输入要审校的文本内容..." required>{{ default_text }}</textarea>
                    <div class="help-text">支持中文文档校对，包括错别字、标点符号、数字用法等</div>
                </div>

                <div class="form-group">
                    <label for="customPrompt">
                        <i class="fas fa-cog"></i> 自定义提示词（可选）：
                    </label>
                    <textarea id="customPrompt" name="custom_prompt" rows="6" placeholder="在此输入自定义的审校要求...">{{ default_prompt }}</textarea>
                    <div class="help-text">可自定义审校规则和要求</div>
                </div>

                <div class="form-group">
                    <label><i class="fas fa-robot"></i> 模型配置：</label>
                    <select id="modelSelect" style="margin-bottom:8px;">
                        <option value="deepseek-v3-250324">deepseek-v3-250324</option>
                        <option value="deepseek-r1-250528">deepseek-r1-250528</option>
                        <option value="doubao-1.5-pro-256k-250115">doubao-1.5-pro-256k-250115</option>
                        <option value="doubao-1.5-pro-32k-250115">doubao-1.5-pro-32k-250115</option>
                        <option value="doubao-1.5-thinking-pro-250415">doubao-1.5-thinking-pro-250415</option>
                        <option value="__custom__">自定义…</option>
                    </select>
                    <input type="text" id="customModelInput" placeholder="输入自定义模型名称" style="display:none;margin-bottom:8px;" />

                    <input type="number" step="0.1" min="0" max="1" id="temperature" name="temperature" placeholder="temperature (0~1)" value="0.1" />
                </div>

                <button type="submit" id="submitBtn">
                    <i class="fas fa-play"></i> 开始审校
                </button>
            </form>
        </div>

        <!-- 右侧结果区域 -->
        <div class="right-column">
            <div class="result-section">
                <h2><i class="fas fa-list-alt"></i> 审校结果</h2>
                <div id="resultContainer" style="display: none;">
                    <!-- 统计信息 -->
                    <div class="stats-info" id="statsInfo">
                        <div class="stat-item">
                            <i class="fas fa-exclamation-circle"></i> 
                            发现错误：<span id="errorCount">0</span> 个
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-file-text"></i> 
                            原文长度：<span id="originalLength">0</span> 字符
                        </div>
                    </div>
                    <!-- 标记文本 -->
                    <div class="result-section">
                        <h3><i class="fas fa-highlighter"></i> 标记文本</h3>
                        <div class="labeled-text" id="labeledText">
                            <!-- 带标记的文本将在这里显示 -->
                        </div>
                    </div>
                    
                    <!-- 错误列表 -->
                    <div class="result-section">
                        <h3><i class="fas fa-bug"></i> 错误详情</h3>
                        <div class="error-list" id="errorList">
                            <!-- 错误列表将在这里动态生成 -->
                        </div>
                    </div>


                </div>

                <div id="emptyResult">
                    <p style="text-align: center; color: #666; margin-top: 50px;">
                        <i class="fas fa-info-circle"></i> 请输入文本并点击"开始审校"查看结果
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 表单提交处理
            $('#proofreadForm').on('submit', function(e) {
                e.preventDefault();
                performProofread();
            });

            // 字符计数
            $('#content').on('input', function() {
                const length = $(this).val().length;
                // 可以在这里添加字符计数显示
            });

            // 切换自定义模型输入显示
            $('#modelSelect').on('change', function() {
                if ($(this).val() === '__custom__') {
                    $('#customModelInput').show();
                } else {
                    $('#customModelInput').hide();
                }
            });
        });

        function performProofread() {
            const content = $('#content').val().trim();
            const customPrompt = $('#customPrompt').val().trim();
            let modelName = $('#modelSelect').val();
            if (modelName === '__custom__') {
                modelName = $('#customModelInput').val().trim();
            }
            const temperature = $('#temperature').val();

            if (!content) {
                alert('请输入要审校的文本内容');
                return;
            }

            // 显示加载层
            $('#loadingOverlay').show();
            $('#submitBtn').prop('disabled', true);

            // 发送请求
            $.ajax({
                url: '{% url "document_proofread_traditional_submit" %}',
                method: 'POST',
                data: {
                    content: content,
                    custom_prompt: customPrompt,
                    model_name: modelName,
                    temperature: temperature,
                    csrfmiddlewaretoken: '{{ csrf_token }}'
                },
                success: function(response) {
                    console.log(response);
                    if (response.data.success) {
                        displayResults(response.data);
                    } else {
                        alert('审校失败：' + (response.error || '未知错误'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('请求失败：', error);
                    alert('请求失败，请检查网络连接或联系管理员');
                },
                complete: function() {
                    // 隐藏加载层
                    $('#loadingOverlay').hide();
                    $('#submitBtn').prop('disabled', false);
                }
            });
        }

        function displayResults(data) {
            // 显示结果容器
            $('#emptyResult').hide();
            $('#resultContainer').show();

            // 更新统计信息
            $('#errorCount').text(data.error_count || 0);
            $('#originalLength').text(data.original_length || 0);

            // 显示错误列表
            const errorListHtml = generateErrorListHtml(data.error_list || []);
            $('#errorList').html(errorListHtml);

            // 显示标记文本
            const processedText = processLabeledText(data.labeled_text || '');
            $('#labeledText').html(processedText);
        }

        function generateErrorListHtml(errorList) {
            if (errorList.length === 0) {
                return '<p style="color: #28a745;"><i class="fas fa-check-circle"></i> 恭喜！未发现错误</p>';
            }

            let html = '';
            errorList.forEach(function(error) {
                html += `
                    <div class="error-item">
                        <div class="error-id">错误 #${error.id}</div>
                        <div><strong>错误文本：</strong><span class="error-text">${error.error_text}</span></div>
                        <div><strong>修改建议：</strong><span class="suggestion">${error.error_suggestion}</span></div>
                        <div><strong>错误理由：</strong>${error.error_reason}</div>
                        <div><strong>错误类型：</strong>${error.error_type}</div>
                    </div>
                `;
            });
            return html;
        }

        function processLabeledText(labeledText) {
            // 保留完整的错误标记格式，并添加样式
            return labeledText
                .replace(/~~([^~]+?)~~(👉[^👈]*👈)/g, '<span class="error-mark">~~$1~~</span><span class="error-annotation">$2</span>')
                .replace(/\n/g, '<br>');
        }
    </script>
</body>
</html> 