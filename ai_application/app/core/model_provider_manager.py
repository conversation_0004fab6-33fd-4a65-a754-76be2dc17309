from django.conf import settings

from app.core.entities.app_entities import AppParameterRule
from app.core.entities.provider_entities import ProviderModelBundle
from app.core.model_manager import ModelInstance
from app.core.model_runtime import model_provider_factory
from app.core.model_runtime.entities.provider_entities import ModelType, AIModelEntity, ParameterRule


class ModelProviderManager:

    def get_provider_model_bundle(
            self,
            provider: str,
            model_type: ModelType
    ) -> ProviderModelBundle:
        model_provider = model_provider_factory.get_provider_instance(provider)
        model_type_instance = model_provider.get_model_instance(model_type)
        return ProviderModelBundle(
            provider_instance=model_provider,
            model_type_instance=model_type_instance,
        )

    def get_model_instance(self, provider: str, model_type: ModelType, model: str) -> ModelInstance:
        provider_model_bundle = self.get_provider_model_bundle(
            provider=provider,
            model_type=model_type
        )
        return ModelInstance(provider_model_bundle, model)

    def get_model_parameter_rules(
            self,
            model_provider_name: str,
            model_name: str
    ) -> list[ParameterRule]:
        return self.get_model_schema(model_provider_name, model_name).parameter_rules

    def get_model_schema(
            self,
            model_provider_name: str,
            model_name: str
    ) -> AIModelEntity:
        model_provider = model_provider_factory.get_provider_instance(model_provider_name)
        model_instance = model_provider.get_model_instance(ModelType.LLM)
        return model_instance.get_model_schema(model_name)

    def get_model_params(self) -> list[AppParameterRule]:
        model_provider_name = settings.MODEL_PROVIDER_NAME
        model_name = settings.MODEL_NAME
        model_parameter_rules = self.get_model_parameter_rules(model_provider_name, model_name)
        model_params = []
        for rule in model_parameter_rules:
            model_params.append(AppParameterRule(
                code=rule.name,
                label=rule.label,
                type=rule.type,
                desc=rule.help,
                required=rule.required,
                default=rule.default,
                min=rule.min,
                max=rule.max,
                step=rule.step,
            ))
        return model_params
