import matplotlib
matplotlib.use('Agg') 
import matplotlib.pyplot as plt
from collections import defaultdict
from django.db.models import F
from app.models.exam_analyis import ExamAnalysisKnowledgePointAnalysis
import os
from matplotlib import font_manager
from matplotlib.ticker import MaxNLocator

# 设置中文字体（使用 macOS 系统自带字体 Hiragino Sans GB）
font_path = "/System/Library/Fonts/Hiragino Sans GB.ttc"
title_font = font_manager.FontProperties(fname=font_path, size=24)
text_font = font_manager.FontProperties(fname=font_path, size=18)

def compute_avg_difficulty(difficulty_dict):
    if not difficulty_dict:
        return None
    values = list(difficulty_dict.values())
    return sum(values) / len(values) if values else None

def plot_kp_analysis(kp_name, save_path=""):
    data = ExamAnalysisKnowledgePointAnalysis.objects.filter(
        point_name=kp_name
    ).values('year', 'question_difficulty')
    year_data = defaultdict(lambda: {'difficulties': []})
    print('\n\n\n\n',year_data,'\n\n\n\n')

    for row in data:
        year = int(row['year'])
        avg_diff = compute_avg_difficulty(row['question_difficulty'])
        if avg_diff is not None:
            year_data[year]['difficulties'].append(avg_diff)

    if not year_data:
        print("无数据")
        return False

    all_years = list(range(min(year_data.keys()), max(year_data.keys()) + 1))
    difficulty_per_year = []

    for year in all_years:
        if year in year_data:
            diffs = year_data[year]['difficulties']
            difficulty_per_year.append(round(sum(diffs) / len(diffs), 2) if diffs else 0)
        else:
            difficulty_per_year.append(0)

    fig, ax1 = plt.subplots(figsize=(12, 6))

    # 柱状图：平均难度
    ax1.bar(all_years, difficulty_per_year, color='#0670E2', alpha=0.6, label='平均难度', width=0.6)
    ax1.set_ylabel('平均难度', fontsize=16, color="#000000", fontproperties=text_font)
    ax1.tick_params(axis='y', labelcolor="#000000")

    # 标题
    plt.title(f"【{kp_name}】考查平均难度分析", fontproperties=title_font)

    fig.tight_layout()

    # 保存图像
    filename = f"{kp_name}.png"
    if save_path:
        filename = os.path.join(save_path, filename)

    plt.savefig(filename, dpi=300)
    plt.close()

    print(f"图像已保存至: {filename}")
    return True
