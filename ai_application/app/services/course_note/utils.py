from django.conf import settings


def parse_subtitle_dict() -> list:
    subtitle_dict_path = str(settings.BASE_DIR.joinpath('app/subtitle_dict.txt'))
    with open(subtitle_dict_path, 'r') as f:
        subtitle_content = f.read()
    subtitle_arr = subtitle_content.split('\n')

    subtitle_list = []
    for s in subtitle_arr:
        line_arr = s.split(' ')
        if len(line_arr) > 0 and line_arr[0]:
            subtitle_list.append(line_arr[0])
    return subtitle_list


def get_chapter_names():
    return [
        '第 1 章 绪论',
        '第 2 章 算法分析',
        '第 3 章 数据结构',
        '第 4 章 线性表',
        '第 5 章 栈和队列',
        '第 6 章 递归',
        '第 7 章 串',
        '第 8 章 数组和广义表',
        '第 9 章 树和二叉树',
        '第 10 章 图',
        '第 11 章 查找',
        '第 12 章 排序',
    ]


def get_chapter_video_content_ids(chapter_name: str) -> list:
    chapter_names = get_chapter_names()

    video_content_id_range = [
        [1, 5], [6, 18], [19, 20],
        [21, 40], [41, 54], [55, 57],
        [58, 63], [64, 72], [73, 87],
        [88, 100], [101, 112], [113, 124]
    ]
    if chapter_name not in chapter_names:
        return []

    chapter_idx = chapter_names.index(chapter_name)
    id_range = video_content_id_range[chapter_idx]
    return list(range(id_range[0], id_range[1] + 1))
