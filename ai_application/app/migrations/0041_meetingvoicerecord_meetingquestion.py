# Generated by Django 4.2 on 2025-04-09 08:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0040_merge_20250407_2123'),
    ]

    operations = [
        migrations.CreateModel(
            name='MeetingVoiceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('origin', models.CharField(blank=True, max_length=100, verbose_name='文件来源')),
                ('msg_time', models.DateTimeField(null=True, verbose_name='时间')),
                ('voice_file', models.CharField(blank=True, max_length=100, verbose_name='音频文件url')),
                ('voice_content', models.TextField(null=True, verbose_name='音频内容')),
                ('is_convert_voice', models.BooleanField(default=False, verbose_name='是否转换音频文件')),
                ('is_extract_question', models.BooleanField(default=False, verbose_name='是否提取问题')),
                ('extract_question_status', models.CharField(blank=True, max_length=100, verbose_name='提取问题状态')),
                ('extract_question_content', models.TextField(null=True, verbose_name='提取问题原始文本')),
            ],
            options={
                'verbose_name': '音频文件表',
                'verbose_name_plural': '音频文件表',
                'db_table': 'ai_meeting_voice_record',
            },
        ),
        migrations.CreateModel(
            name='MeetingQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_type', models.CharField(blank=True, max_length=100, verbose_name='问题类型')),
                ('question_sub_type', models.CharField(blank=True, max_length=100, verbose_name='问题子类型')),
                ('question_content', models.TextField(null=True, verbose_name='问题内容')),
                ('meeting_record', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.meetingvoicerecord')),
            ],
            options={
                'verbose_name': '音频问题表',
                'verbose_name_plural': '音频问题表',
                'db_table': 'ai_meeting_question',
            },
        ),
    ]
