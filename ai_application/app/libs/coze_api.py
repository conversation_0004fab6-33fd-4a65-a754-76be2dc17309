from cozepy import Co<PERSON>, J<PERSON><PERSON><PERSON>uthA<PERSON>, COZE_CN_BASE_URL
from cozepy.auth import J<PERSON><PERSON><PERSON>

from django.conf import settings


__all__ = [
    'CozeApiClient',
    'coze_api_client',
]

class CozeApiClient:
    def __init__(
            self,
            coze_api_base: str,
            jwt_oauth_client_id: str,
            jwt_oauth_private_key: str,
            jwt_oauth_public_key_id: str,
            expire_time=3600,
    ):
        assert coze_api_base
        assert jwt_oauth_client_id
        assert jwt_oauth_private_key
        assert jwt_oauth_public_key_id

        self.expire_time = expire_time
        self.coze_api_base = coze_api_base
        self.jwt_oauth_app = JWTOAuthApp(
            client_id=jwt_oauth_client_id,
            private_key=jwt_oauth_private_key,
            public_key_id=jwt_oauth_public_key_id,
            base_url=self.coze_api_base,
        )

    @property
    def coze(self) -> Coze:

        return Coze(auth=JWTAuth(oauth_app=self.jwt_oauth_app, ttl=self.expire_time), base_url=self.coze_api_base)



# client
coze_api_client = CozeApiClient(
    coze_api_base=settings.COZE_API_BASE,
    jwt_oauth_client_id=settings.COZE_JWT_OAUTH_CLIENT_ID,
    jwt_oauth_private_key=settings.COZE_JWT_OAUTH_PRIVATE_KEY,
    jwt_oauth_public_key_id=settings.COZE_JWT_OAUTH_PUBLIC_KEY_ID,
    expire_time=settings.COZE_JWT_TOKEN_EXPIRE_TIME,
).coze





coze_api_client_stream = CozeApiClient(
    coze_api_base=settings.COZE_API_BASE,
    jwt_oauth_client_id=settings.COZE_JWT_OAUTH_CLIENT_ID,
    jwt_oauth_private_key=settings.COZE_JWT_OAUTH_PRIVATE_KEY,
    jwt_oauth_public_key_id=settings.COZE_JWT_OAUTH_PUBLIC_KEY_ID,
    expire_time=settings.COZE_JWT_TOKEN_EXPIRE_TIME,
).coze