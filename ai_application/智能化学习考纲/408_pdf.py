import os
import django
import logging
import base64
import io
from typing import List
from pathlib import Path
from django.conf import settings
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入正确的上传函数
from helpers.upload_helper import upload_file

# 设置 Django 环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_application.settings")
django.setup()

# 添加日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置视觉大模型
llm_vision = ChatOpenAI(
    openai_api_key=settings.DOUBAO_API_KEY,
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-seed-1-6-250615"
)

def process_images_with_vision_model(image_paths):
    """
    使用视觉大模型处理文件夹中的所有图片
    
    Args:
        image_paths (List[str]): 图片文件路径列表
    
    Returns:
        object: 视觉大模型处理结果
    """
    if not image_paths:
        raise ValueError("没有图片可供处理")
    
    print(f"正在使用视觉大模型处理 {len(image_paths)} 张图片...")
    
    try:
        # 构造提示词和图片内容
        content = [{
            "type": "text", 
            "text": """你是一个专业的408考纲分析专家。请仔细分析这些图片中的内容，
请按以下要求进行分析：
1. 识别所有图片中的文本内容并准确提取
2. 请把所有图片当成一个整体进行分析，并根据图片下方的页码顺序分析，
3. 帮我深度整理一下408计算机大纲，包括（数据结构、计算机组成原理、计算机网络、操作系统）
4. 并针对历年考试的要求和真题分析，对大纲涉及到的章节考点进行重要程度和难度进行区分，用合适的标签标注，并以列表形式输出。
"""
        }]
        
        # 添加所有图片到内容中
        for image_path in image_paths:
            # 获取文件名
            file_name = Path(image_path).name
            
            # 上传图片并获取URL
            with open(image_path, 'rb') as image_file:
                image_url = upload_file(
                    file=image_file,
                    file_name=file_name,
                    sub_path='pdf_images'
                )
            
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": image_url
                }
            })
        
        # 创建消息
        message = HumanMessage(content=content)
        
        # 调用大模型处理
        response = llm_vision.invoke([message])
        return response
        
    except Exception as e:
        print(f"视觉大模型处理失败: {e}")
        raise e

def save_result_as_markdown(result, filename):
    """
    将结果保存为Markdown文件

    Args:
        result: 模型生成的结果
        filename: 保存的文件名

    Returns:
        str: 保存的文件路径
    """
    # 确保文件以.md结尾
    if not filename.endswith('.md'):
        filename += '.md'

    # 提取结果内容
    if hasattr(result, 'content'):
        content = result.content
    else:
        content = str(result)

    # 保存到文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

    return filename

def get_all_images_from_folder(folder_path):
    """
    获取文件夹中的所有图片文件路径
    
    Args:
        folder_path (str): 文件夹路径
    
    Returns:
        List[str]: 图片文件路径列表
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.gif'}
    image_paths = []
    
    folder = Path(folder_path)
    if not folder.exists():
        raise FileNotFoundError(f"文件夹不存在: {folder_path}")
    
    for file_path in folder.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_paths.append(str(file_path))
    
    # 按文件名排序，确保处理顺序一致
    image_paths.sort()
    return image_paths

if __name__ == "__main__":
    # 处理pdf_images文件夹中的所有图片
    pdf_images_folder = os.path.join(os.path.dirname(__file__), "../智能化学习考纲/pdf_images")
    try:
        image_paths = get_all_images_from_folder(pdf_images_folder)
        print(f"找到 {len(image_paths)} 张图片")
        
        if image_paths:
            # 直接处理所有图片
            result = process_images_with_vision_model(image_paths)
            
            if result:
                # 保存结果为文件
                saved_file = save_result_as_markdown(result, "408考纲分析报告.md")
                print(f"图片分析报告已保存到: {saved_file}")
                
                # 打印结果内容
                content = result.content if hasattr(result, 'content') else str(result)
                print("生成的图片分析报告内容:")
                print(content)
            else:
                print("使用视觉大模型分析图片内容失败")
        else:
            print("pdf_images文件夹中没有找到图片文件")
            
    except FileNotFoundError as e:
        print(f"文件夹错误: {e}")
    except Exception as e:
        print(f"处理图片时出错: {e}")