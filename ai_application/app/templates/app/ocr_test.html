<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>OCR识别测试</title>
<script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
</head>
<body>

<h2>OCR识别测试</h2>

<form id="myForm" action="{% url 'ocr_result' %}">
    <!-- 使用capture属性指定输入类型为摄像头 -->
    <div class="form-group" style="margin-bottom: 20px">
        <input id="image" type="file" name="image" accept="image/*">
    </div>
    <!-- 用于显示拍摄的照片 -->
    <img id="photo" src="" alt="Your photo will appear here" style="display:none; margin-bottom: 20px; width: 200px;">

{#    <div class="form-group" style="margin-bottom: 20px">#}
{#        <select name="process_type" id="process_type">#}
{#            <option value="baidu">百度OCR</option>#}
{#            <option value="ali">阿里智能文档</option>#}
{#        </select>#}
{#    </div>#}

    <button id="uploadBtn" type="button">提交</button>
</form>

<h2>返回结果：</h2>
<div id="uploadDetails"></div>

<script>
// 当用户选择文件后，这个函数会被调用
document.querySelector('input[type="file"]').addEventListener('change', function(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const photo = document.getElementById('photo');
            photo.src = e.target.result;
            photo.style.display = 'block'; // 显示照片
        };
        reader.readAsDataURL(file);
    }
});
$(function() {
    $('#uploadBtn').click(function() {
        const uploadDetails = document.getElementById('uploadDetails');
        uploadDetails.innerHTML = '';

        const formData = new FormData($('#myForm')[0]);
        $.ajax({
            url: $('#myForm').attr('action'), // 服务器端处理上传的URL
            type: 'POST',
            data: formData,
            processData: false,  // 告诉jQuery不要处理发送的数据
            contentType: false,  // 告诉jQuery不要设置Content-Type请求头
            success: function(response) {
                // 文件上传成功后的回调
                response.data.forEach(function(traceDetail) {
                    const detailElement = document.createElement('p');
                    detailElement.textContent = traceDetail;
                    uploadDetails.appendChild(detailElement);
                });

                /*content_blocks = response.data.content_blocks
                let tocHtml = ''
                for (let i = 0; i < content_blocks.length; i++) {
                    tocHtml += `<li class="toc_item" data-id="${i}">${content_blocks[i].title}</li>`
                }
                $('#toc_list').html(tocHtml)
                updateQueryContent(0)*/
            },
            error: function() {
                // 文件上传失败后的回调
                console.error('File upload failed.');
            }
        });
    });
})
</script>

</body>
</html>