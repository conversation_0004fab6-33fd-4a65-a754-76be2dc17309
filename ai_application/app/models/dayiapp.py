from django.db import models

from ._base import BaseModel

class AssistantRequest(models.Model):
    system_prompt = models.TextField('系统提示词', help_text='用户自定义的提示词内容')
    user_input = models.TextField('用户输入', help_text='用户提交的问题内容')
    model_response = models.TextField('模型响应', help_text='大模型返回的完整答案')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    subject_id = models.CharField(max_length=50, verbose_name='学科ID')  # 新增字段

    class Meta:
        verbose_name = '助手请求记录'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

    def __str__(self):
        return f"请求 {self.id} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class keyword(BaseModel):
    keyword_no = models.Char<PERSON>ield(max_length=255,unique=True)
    keyword = models.TextField('关键词名称', help_text='关键词名称')
    keyword_content = models.TextField('关键词内容', help_text='触发关键词使用的具体内容')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '助手请求记录'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        db_table = 'ai_dayiapp_keywords'




class SubjectPrompt(models.Model):
    SUBJECT_CHOICES = [
        ('math', '数学'),
        ('law', '法硕'),
        ('english', '英语'),
        ('english_translation', '英语翻硕'),
        ('Psychology', '心理学'),
        ('Mechanical Engineering', '机械工程'),
        ('Electrical Engineering', '电气工程'),
        ('Computer Science', '计算机'),
        ('education', '教育'),
        ('politics', '政治'),
        ('P.E', '体育'),
        ('finance', '金融'),
        ('Nursing Comprehensive 308', '308护理综合'),
        ('The Management Comprehensive Examination 199', '199管理类联考'),
        ('art', '艺术'),
        ('Comprehensive Examination of Western Medicine 306', '306西医综合'),
        ('other', '其他'),
    ]  # 与前端学科ID保持一致

    subject_id = models.CharField(
        max_length=50,
        primary_key=True,
        choices=SUBJECT_CHOICES,
        verbose_name='学科ID'
    )
    name = models.CharField(max_length=100, verbose_name='学科名称', editable=False)  # 从choices自动填充
    prompt = models.TextField(verbose_name='学科提示词', help_text='该学科的默认提示词')
    real_prompt = models.TextField(verbose_name='正式学科提示词', help_text='该学科的默认提示词', null=True)
    updated_at = models.DateTimeField('最后更新时间', auto_now=True)

    class Meta:
        verbose_name = '学科提示词配置'
        verbose_name_plural = verbose_name
        ordering = ['-updated_at']  # 按更新时间倒序排列
        db_table = 'ai_subject_prompt'

    def save(self, *args, **kwargs):
        # 自动填充学科名称（从choices中获取）
        self.name = dict(self.SUBJECT_CHOICES).get(self.subject_id, '未知学科')
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} - {self.subject_id}"
