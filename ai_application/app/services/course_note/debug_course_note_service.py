import json

from django.db import transaction
from django.utils import timezone

from app.api.dto import ChatMessageDto
from app.core.rag.splitter.markdown_splitter import MarkdownHeaderTextSplitter
from app.models import (
    CourseNoteTask, DatasetDocument, CourseNoteTaskDebug, Account, PromptTemplate
)
from app.services.app_generate_service import AppGenerateService
from app.services.course_note.utils import get_chapter_video_content_ids, parse_subtitle_dict, get_chapter_names


class DebugCourseNoteService:

    @classmethod
    def debug_gen_single_lecture_note(cls, video_content_id):
        document = DatasetDocument.objects.filter(data_source_info__video_content_id=video_content_id).first()
        if not document:
            return

        with open('lecture.json', 'r') as f:
            lecture_list = json.loads(f.read())

        lecture_info = None
        for item in lecture_list:
            if item['video_content_id'] == video_content_id:
                lecture_info = item
                break

        subtitle_list = parse_subtitle_dict()

        inputs = {
            'chapter_name': lecture_info['chapter_name'],
            'section_name': lecture_info['section_name'],
            'chapter_note': lecture_info['lecture'],
            'subtitle': lecture_info['subtitle'],
            'subtitle_dict': ','.join(subtitle_list),
        }
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no='lecture_note_generation').first()
        inputs['lecture_note_generation'] = prompt_template.get_debug_prompt_content()

        chat_dto = ChatMessageDto(
            app_id='lecture_note_generation',
            query="",
            stream=True,
            inputs=inputs,
        )
        account = Account.objects.first()
        return AppGenerateService.generate(chat_dto, account, 'console')

    @classmethod
    def debug_create_chapter_note_task_chapter(cls, chapter_name, lecture_content):
        chapter_document_nos = cls._debug_get_chapter_document_nos(chapter_name)
        if not chapter_document_nos:
            return

        parsed_lecture_list = [{
            'chapter_name': chapter_name,
            'chapter_lecture': lecture_content,
            'document_nos': chapter_document_nos,
        }]

        cls._debug_create_chapter_note_task_batch(chapter_name, parsed_lecture_list)

    @classmethod
    def debug_create_chapter_note_task_batch(cls, lecture_content):
        md_documents = MarkdownHeaderTextSplitter(
            headers_to_split_on=[
                ("#", "h1"),
            ],
            strip_headers=True
        ).split_text(lecture_content)

        chapter_names = get_chapter_names()

        parsed_lecture_list = []

        for doc in md_documents:
            title = doc.metadata.get('h1')
            if title and title in chapter_names:
                chapter_document_nos = cls._debug_get_chapter_document_nos(title)
                if not chapter_document_nos:
                    continue

                parsed_lecture_list.append({
                    'chapter_name': title,
                    'chapter_lecture': doc.page_content,
                    'document_nos': chapter_document_nos,
                })

        cls._debug_create_chapter_note_task_batch('全量数据', parsed_lecture_list)

    @classmethod
    def _debug_get_chapter_document_nos(cls, chapter_name) -> list:
        video_content_ids = get_chapter_video_content_ids(chapter_name)
        if not video_content_ids:
            return []

        documents = DatasetDocument.objects.filter(data_source_type='course_video_file')
        chapter_document_nos = []
        for i in documents:
            if i.data_source_info['video_content_id'] in video_content_ids:
                chapter_document_nos.append(i.document_no)
        return chapter_document_nos

    @classmethod
    def _debug_create_chapter_note_task_batch(cls, name, parsed_lecture_list: list):
        course_id = '数据结构'
        with transaction.atomic():
            task_debug = CourseNoteTaskDebug.objects.create(
                course_id=course_id,
                status='processing',
                processing_started_at=timezone.now(),
                name=name,
            )

            for idx, item in enumerate(parsed_lecture_list):
                chapter_id = f'chapter_id_{idx}'

                chapter_name = item['chapter_name']
                chapter_lecture = item['chapter_lecture']
                document_nos = item['document_nos']
                CourseNoteTask.objects.create(
                    course_id=course_id,
                    chapter_id=chapter_id,
                    chapter_name=chapter_name,
                    chapter_lecture=chapter_lecture,
                    document_nos=document_nos,
                    task_debug=task_debug,
                )
