<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知舟问答2.0（AI刷题版）</title>
    <link href="https://cdn.kaoyanvip.cn/katex@0.16.9/katex.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <script src="https://cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/marked/13.0.2/marked.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/katex@0.16.9/katex.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/katex@0.16.9/contrib/auto-render.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        .column {
            padding: 0 20px 0 20px; /* 调整顶部padding为0 */
            overflow-y: auto;
            height: 100vh;
            box-sizing: border-box;
        }

        .column-1 {
            width: 12.5%;
            background-color: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            padding-top: 20px; /* 单独设置第一列的顶部padding */
        }

        .column-2 {
            width: 37.5%;
            background-color: #ffffff;
        }
        .column-3 {
            width: 50%;
            background-color: #f8f9fa;
            padding-top: 0; /* 添加顶部padding为0 */
        }
        .determine-content {
            margin: 20px;
            border: 1px solid #f0f0f0;
            border-radius: 20px;
            padding: 10px 15px;
        }
        .section-title {
            color: #1a73e8;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
            margin-bottom: 25px;
            margin-top: 0;
            padding-top: 25px; /* 增加顶部内边距 */
            position: relative; /* 添加相对定位 */
            top: -3px; /* 调整位置 */
        }

        /* 新增第三列标题专属样式 */
        .column-3 .section-title {
            margin-bottom: 40px; /* 增加底部间距 */
            padding-top: 43px;   /* 同步调整内边距保持视觉平衡 */
            border-bottom: 2px solid #e0e0e0;
        }

        .subject-buttons {
            max-height: calc(100vh - 100px);
            overflow-y: auto;
            scrollbar-width: thin;
        }

        .subject-btn {
            padding: 14px 20px;
            background: #ffffff;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 10px;
            text-align: left;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            width: 100%;
        }

        .subject-btn:hover {
            background: #e9f2ff;
            transform: translateX(4px);
        }

        .subject-btn.active {
            background: #1a73e8;
            color: white;
            box-shadow: 0 3px 8px rgba(26, 115, 232, 0.2);
            transform: translateX(2px);
        }

        .prompt-preview {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.03);
            margin-bottom: 20px;
            overflow: hidden; /* 添加此属性防止内容溢出 */
        }

        .prompt-preview textarea {
            width: 100%;
            min-height: 200px;
            max-height: 56vh; /* 限制最大高度为视口高度的75% */
            padding: 14px;
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            font-size: 15px;
            resize: vertical;
            background: #f8f9fa;
            overflow-y: auto; /* 添加滚动条 */
            box-sizing: border-box;
        }

        .input-area textarea {
            width: 100%;
            min-height: 150px;
            padding: 14px;
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            font-size: 15px;
            resize: vertical;
            background: #f8f9fa;
        }

        #image-preview img {
            width: 38px;
            height: 38px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #eee;
            box-shadow: 0 1px 4px rgba(0,0,0,0.08);
            background: #fff;
        }

        button {
            padding: 12px 28px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 500;
            transition: transform 0.2s ease;
            margin-top: 10px;
        }

        .stats-bar {
            margin: 20px 0;
            padding: 12px 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            display: flex;
            gap: 15px;
            justify-content: flex-start;
            align-items: center;
        }

        .stat-item {
            color: #666;
            font-size: 0.95rem;
            display: flex;
            gap: 5px;
        }

        .stat-item::before {
            content: "●";
            color: #1a73e8;
            font-size: 0.8rem;
            margin-right: 5px;
        }

        #save-prompts-btn {
            background: #4CAF50;
            color: white;
            margin-left: 0;
            justify-self: end;
        }

        #save-prompts-btn:hover {
            transform: scale(1.03);
        }

        #prompt-stats {
            padding: 6px 12px;
            background-color: #f5f5f5;
            border-radius: 15px;
            border: 1px solid #e0e0e0;
            font-size: 0.9rem;
        }

        #char-count {
            font-weight: 500;
            color: #1a73e8;
        }

        #submit-btn {
            background: #1a73e8;
            color: white;
        }

        #submit-btn:hover {
            transform: scale(1.03);
        }

        .loading {
            display: none; /* 默认隐藏加载状态 */
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 15px 0;
        }

        .typing-effect {
            animation: typing 0.8s steps(40, end) infinite;
            white-space: nowrap;
            overflow: hidden;
            border-right: 2px solid #333;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        .history-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.03);
        }
        .history-item {
            border-bottom: 1px solid #f0f0f0;
            padding: 16px 0;
            margin-bottom: 12px;
        }
        .history-question {
            font-weight: 700;
            color: #1a73e8;
            margin-bottom: 10px;
            padding: 12px;
            background-color: #f5f9ff;
            border-radius: 8px;
            border-left: 4px solid #1a73e8;
            font-size: 1rem;
        }

        .history-answer {
            color: #333;
            font-size: 1rem;
            padding: 12px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f0f0f0;
            margin: 8px 0;
        }

        .history-thinking {
            color: #333;
            font-size: 0.9rem;
            padding: 12px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f0f0f0;
            margin: 8px 0;
        }

        .history-images {
            display: flex;
            gap: 8px;
            margin: 8px 0;
            flex-wrap: wrap;
        }
        .history-images img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #eee;
            cursor: pointer;
        }
        .history-stats {
            font-size: 0.85rem;
            color: #666;
            margin-top: 8px;
            display: flex;
            gap: 12px;
        }

        .output-area {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.03);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 第一列：学科按钮区域 -->
        <div class="column column-1">
            <h2 class="section-title">学科选择</h2>
            <div class="subject-buttons" id="subject-buttons"></div>
        </div>

        <!-- 第二列：提示词、输入区域和加载状态 -->
        <div class="column column-2">
            <!-- 提示词预览区域 -->
            <div class="prompt-preview">
                <h3 class="section-title">学科提示词</h3>
                <textarea id="system-prompt" placeholder="选择学科后显示提示词"></textarea>
                <input type="hidden" id="subject-id" name="subject_id">
                <!-- 添加字符统计栏 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                    <div id="prompt-stats" style="
                        background: linear-gradient(90deg, #f5f5f5, #ffffff);
                        border-radius: 15px;
                        padding: 8px 16px;
                        border: 1px solid #e0e0e0;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                        font-size: 14px;
                        color: #666;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    ">
                        字符数: <span id="char-count" style="
                            font-weight: 600;
                            font-size: 15px;
                            color: #1a73e8;
                            min-width: 30px;
                            text-align: center;
                        ">0</span>/<span style="color: #999;">3000</span>
                    </div>
                    <button id="save-prompts-btn" style="
                        background: linear-gradient(135deg, #4CAF50, #2E7D32);
                        color: white;
                        border: none;
                        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
                        transition: all 0.2s ease;
                        position: relative;
                    ">
                        <span id="save-btn-text">保存当前提示词</span>
                        <span id="save-btn-loading" style="
                            display: none;
                            position: absolute;
                            right: 10px;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 16px;
                            height: 16px;
                            border: 2px solid rgba(255,255,255,0.3);
                            border-radius: 50%;
                            border-top-color: white;
                            animation: spin 1s linear infinite;
                        "></span>
                    </button>
                </div>
            </div>

            <!-- 输入内容区 -->
            <div class="input-area" style="position: relative;">
                <h3 class="section-title">输入内容</h3>
                <div style="position: relative;">
                        <div id="image-preview" style="display:flex; gap:6px; margin-bottom:8px;"></div>

                    <div id="custom-input" contenteditable="true"
                         style="width:100%; min-height:150px; padding:14px; border:1px solid #f0f0f0; border-radius:10px; font-size:15px; background:#f8f9fa; outline:none; position:relative;box-sizing:border-box;">
                    </div>
                    <input type="hidden" id="user-input" name="user_input">
                    <input type="hidden" id="user-id" name="user_id">
                </div>
                <div style="margin-top: 12px;">
                    <label>上传图片（支持多选）：</label>
                    <input type="file" id="image-input" accept="image/*" multiple>
                </div>
                <div style="display: flex; gap: 10px; margin-top: 10px;">
                    <button id="submit-btn">发送问题</button>
                    <!-- 修改后的用户选择器按钮 -->
                    <div style="position: relative; margin-left: auto;">
                        <button id="user-selector-btn" type="button" style="
                            background: linear-gradient(135deg, #f5f7fa, #e4e8eb);
                            color: #333;
                            border: none;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                            transition: all 0.2s ease;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 12px 20px;
                            border-radius: 12px;
                            font-weight: 500;
                        ">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            <span id="user-selector-text">选择身份</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </button>
                        <div id="user-dropdown" style="
                            display: none;
                            position: absolute;
                            z-index: 1000;
                            background: white;
                            border: 1px solid rgba(0,0,0,0.08);
                            border-radius: 12px;
                            box-shadow: 0 12px 28px rgba(0,0,0,0.12);
                            padding: 8px;
                            width: 280px;
                            margin-top: 8px;
                            right: 0;
                            opacity: 0;
                            transform: translateY(-10px);
                            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                        ">
                            <div style="
                                display: flex;
                                margin-bottom: 12px;
                                gap: 8px;
                                padding: 8px;
                                background: #f8f9fa;
                                border-radius: 8px;
                            ">
                                <input type="text" id="new-user-input" placeholder="输入新用户ID" style="
                                    flex: 1;
                                    padding: 10px 12px;
                                    border: 1px solid rgba(0,0,0,0.08);
                                    border-radius: 8px;
                                    font-size: 14px;
                                    outline: none;
                                    transition: border 0.2s ease;
                                    background: white;
                                ">
                                <button id="add-user-btn" style="
                                    padding: 10px 16px;
                                    background: linear-gradient(135deg, #4CAF50, #2E7D32);
                                    color: white;
                                    border: none;
                                    border-radius: 8px;
                                    font-size: 14px;
                                    transition: all 0.2s ease;
                                    font-weight: 500;
                                    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
                                ">添加</button>
                            </div>
                            <div id="user-list" style="
                                max-height: 300px;
                                overflow-y: auto;
                                scrollbar-width: thin;
                                padding: 4px;
                            ">
                                <!-- 新增固定选项 -->
                                <div class="user-item" style="
                                    padding: 12px 16px;
                                    cursor: pointer;
                                    border-radius: 8px;
                                    margin-bottom: 4px;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    transition: all 0.2s ease;
                                    background: white;
                                ">
                                    <span onclick="selectUser('82')" style="font-weight: 500; flex: 1;">青青</span>
                                    <span style="color:#666; margin-right:8px; white-space: nowrap;">删除</span>
                                    <span class="delete-user-btn" onclick="deleteUser(event, '82')" style="
                                        color: #ff4d4f;
                                        cursor: pointer;
                                        font-size: 18px;
                                        width: 24px;
                                        height: 24px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        border-radius: 50%;
                                        transition: all 0.2s;
                                        background: rgba(255,77,79,0.1);
                                    ">×</span>
                                </div>
                                <div class="user-item" style="
                                    padding: 12px 16px;
                                    cursor: pointer;
                                    border-radius: 8px;
                                    margin-bottom: 4px;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    transition: all 0.2s ease;
                                    background: white;
                                ">
                                    <span onclick="selectUser('72')" style="font-weight: 500;flex:1;">青青小号</span>
                                    <span style="color:#666; margin-right:8px; white-space: nowrap;">删除</span>
                                    <span class="delete-user-btn" onclick="deleteUser(event,'72')" style="
                                        color: #ff4d4f;
                                        cursor: pointer;
                                        font-size: 18px;
                                        width: 24px;
                                        height: 24px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        border-radius: 50%;
                                        transition: all 0.2s;
                                        background: rgba(255,77,79,0.1);
                                    ">×</span>
                                </div>
                                <div class="user-item" style="
                                    padding: 12px 16px;
                                    cursor: pointer;
                                    border-radius: 8px;
                                    margin-bottom: 4px;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    transition: all 0.2s ease;
                                    background: white;
                                ">
                                    <span onclick="selectUser('57')" style="font-weight: 500; flex: 1;">纳兰</span>
                                    <span style="color:#666; margin-right:8px; white-space: nowrap;">删除</span>
                                    <span class="delete-user-btn" onclick="deleteUser(event, '57')" style="
                                        color: #ff4d4f;
                                        cursor: pointer;
                                        font-size: 18px;
                                        width: 24px;
                                        height: 24px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        border-radius: 50%;
                                        transition: all 0.2s;
                                        background: rgba(255,77,79,0.1);
                                    ">×</span>
                                </div>
                                <div class="user-item" style="
                                    padding: 12px 16px;
                                    cursor: pointer;
                                    border-radius: 8px;
                                    margin-bottom: 4px;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    transition: all 0.2s ease;
                                    background: white;
                                ">
                                    <span onclick="selectUser('421')" style="font-weight: 500; flex: 1;">茹梦</span>
                                    <span style="color:#666; margin-right:8px; white-space: nowrap;">删除</span>
                                    <span class="delete-user-btn" onclick="deleteUser(event, '421')" style="
                                        color: #ff4d4f;
                                        cursor: pointer;
                                        font-size: 18px;
                                        width: 24px;
                                        height: 24px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        border-radius: 50%;
                                        transition: all 0.2s;
                                        background: rgba(255,77,79,0.1);
                                    ">×</span>
                                </div>
                                <div class="user-item" style="
                                    padding: 12px 16px;
                                    cursor: pointer;
                                    border-radius: 8px;
                                    margin-bottom: 4px;
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    transition: all 0.2s ease;
                                    background: white;
                                ">
                                    <span onclick="selectUser('427')" style="font-weight: 500; flex: 1;">余鹏</span>
                                    <span style="color:#666; margin-right:8px; white-space: nowrap;">删除</span>
                                    <span class="delete-user-btn" onclick="deleteUser(event, '427')" style="
                                        color: #ff4d4f;
                                        cursor: pointer;
                                        font-size: 18px;
                                        width: 24px;
                                        height: 24px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        border-radius: 50%;
                                        transition: all 0.2s;
                                        background: rgba(255,77,79,0.1);
                                    ">×</span>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
                <!-- 判断科目过程 -->
                <div class="determine-content">

                </div>
                <!-- 统计信息栏 - 移动到按钮旁边 -->
                <div id="stats-bar" class="stats-bar" style="margin-left: 15px; display: inline-flex;">
                    <span class="stat-item" id="response-time"></span>
                    <span class="stat-item" id="tokens-used"></span>
                    <span class="stat-item" id="character-count"></span>
                </div>
            </div>

            <div id="allocation-modal" style="
                display: none;
                position: fixed;
                z-index: 10000;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                min-width: 240px;
                max-width: 90vw;
                background: white;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                padding: 20px;
                text-align: center;
                word-break: break-all;
            ">
                <h3 style="margin-bottom: 20px;">分配过程</h3>
                <div id="allocation-content" style="
                    font-size: 1rem;
                    color: #333;
                    line-height: 1.6;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                "></div>
                <button id="close-allocation-modal" style="
                    margin-top: 20px;
                    padding: 8px 16px;
                    background: #ff4d4f;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                ">关闭</button>
            </div>

            <!-- 删除原统计信息栏位置 -->
            <!-- 删除:
            <div id="stats-bar" class="stats-bar">
                <span class="stat-item" id="response-time"></span>
                <span class="stat-item" id="tokens-used"></span>
                <span class="stat-item" id="character-count"></span>
            </div>
            -->

            <!-- 加载状态 - 移至第二列最下方 -->
            <div class="loading" id="loading">
                <p class="typing-effect">正在生成回答，请稍候...</p>
            </div>
        </div>

        <!-- 第三列：合并后的对话区域 -->
        <div class="column column-3">
            <!-- 对话显示区 -->
            <h2 class="section-title">回答记录</h2>
            <div class="output-area" id="chat-container">
                <div id="chat-history" style="display: flex; flex-direction: column; gap: 16px; height: calc(100vh - 150px); overflow-y: auto; padding-right: 10px;"></div>
            </div>
        </div>
    </div>

    <script>
        const katexOptions = {
            delimiters: [
                {left: '$$', right: '$$', display: true},
                {left: '$', right: '$', display: false},
                {left: '\\(', right: '\\)', display: false},
                {left: '\\[', right: '\\]', display: true}
            ],
            throwOnError: false,
            trust: true
        };
        const determineEl = document.querySelectorAll('.determine-content')[0]
        let messageId = null;
        let DetermineTimeoutId = null;
        let isLoading = false;

        document.addEventListener('DOMContentLoaded', async function () {
            const baseSubjects = [
                { id: 'other', name: '其他'},
                { id: 'math', name: '数学' },
                { id: 'law', name: '法硕'},
                { id: 'english', name: '英语'},
                { id: 'english_translation', name: '英语翻硕' },
                { id: 'psychology', name: '心理学'},
                { id: 'mechanical engineering', name: '机械工程'},
                { id: 'electrical engineering', name: '电气工程'},
                { id: 'computer science', name: '计算机'},
                { id: 'education', name: '教育'},
                { id: 'politics', name: '政治'},
                { id: 'p.e', name: '体育'},
                { id: 'finance', name: '金融'},
                { id: 'nursing comprehensive 308', name: '308护理综合'},
                { id: 'the management comprehensive examination 199', name: '199管理类联考'},
                { id: 'art', name: '艺术'},
                { id: 'comprehensive examination of western medicine 306', name: '306西医综合'},
            ];

            const submitBtn = document.getElementById('submit-btn');
            const userInput = document.getElementById('user-input');
            const systemPrompt = document.getElementById('system-prompt');
            const charCountSpan = document.getElementById('char-count');
            const loadingIndicator = document.getElementById('loading');
            const subjectIdInput = document.getElementById('subject-id');
            const saveBtn = document.getElementById('save-prompts-btn');
            const closeModalBtn = document.getElementById('close-allocation-modal');
            let subjects = [];
            const conversationId = "{{ conversation_id }}";
            // 从URL中获取user_id参数
            const urlParams = new URLSearchParams(window.location.search);
            const urlUserId = urlParams.get('user_id');
            // 如果URL中有user_id参数，则设置到隐藏字段中
            /*if (urlUserId) {
                document.getElementById('user-id').value = urlUserId;
                // 更新用户选择器显示
                document.getElementById('user-selector-text').textContent = `选择身份: ${urlUserId}`;
            }*/
            async function checkDetermineTracing() {
                if (!messageId) return
                const url = `/console/app/ai_chat_determine_tracing?message_id=${messageId}`
                const response = await fetch(url);
                const res = await response.json();
                console.log(res)
                if (res.code ===0 && res.data.has_tracing) {
                    const answer = res.data.answer
                    determineEl.innerHTML = marked.parse(answer);
                    renderMathInElement(determineEl, katexOptions);
                    console.log(DetermineTimeoutId)
                    clearInterval(DetermineTimeoutId);
                }
            }

            // 初始化字符统计
            function updateCharCount() {
                const charCount = systemPrompt.value.length;
                charCountSpan.textContent = charCount;

                // 超过3000字时显示警告
                if (charCount > 3000) {
                    charCountSpan.style.color = 'red';
                    charCountSpan.style.fontWeight = 'bold';
                } else {
                    charCountSpan.style.color = '#666';
                    charCountSpan.style.fontWeight = 'normal';
                }
            }

            // 初始化学科按钮
            function initSubjectButtons() {
                const container = document.getElementById('subject-buttons');
                container.innerHTML = '';

                // 文理分科分组
                const liberalSubjects = [
                    'other','law', 'english', 'english_translation', 'psychology',
                    'education', 'politics', 'art', 'the management comprehensive examination 199'
                ];

                const scienceSubjects = [
                    'math', 'computer science', 'mechanical engineering',
                    'electrical engineering', 'comprehensive examination of western medicine 306',
                    'nursing comprehensive 308', 'p.e', 'finance'
                ];

                // 创建文科分组 (现在先添加到容器中)
                const liberalGroup = document.createElement('div');
                liberalGroup.className = 'mb-6';
                liberalGroup.innerHTML = `<h3 class="text-lg font-medium text-neutral-800 mb-3">文科</h3>`;
                container.appendChild(liberalGroup);

                // 创建理科分组 (现在后添加到容器中)
                const scienceGroup = document.createElement('div');
                scienceGroup.className = 'mb-6';
                scienceGroup.innerHTML = `<h3 class="text-lg font-medium text-neutral-800 mb-3">理科</h3>`;
                container.appendChild(scienceGroup);

                // 按文理分科添加按钮
                subjects.forEach(subject => {
                    const btn = document.createElement('button');
                    btn.className = 'subject-btn';
                    btn.innerHTML = `<i class="fa fa-book text-primary mr-3"></i><span>${subject.name}</span>`;
                    btn.dataset.subjectId = subject.id;
                    btn.dataset.prompt = subject.prompt;

                    btn.addEventListener('click', () => {
                        document.querySelectorAll('.subject-btn').forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        systemPrompt.value = btn.dataset.prompt;
                        subjectIdInput.value = btn.dataset.subjectId;
                        updateCharCount(); // 新增：切换学科后立即更新字符数
                    });

                    if (liberalSubjects.includes(subject.id)) {
                        liberalGroup.appendChild(btn);
                    } else {
                        scienceGroup.appendChild(btn);
                    }
                });

                // 默认选中第一个学科
                if (subjects.length > 0) {
                    const firstBtn = document.querySelector('.subject-btn');
                    if (firstBtn) {
                        firstBtn.click();
                        updateCharCount(); // 新增：默认选中后立即更新字符数
                    }
                }
            }

            // 监听输入变化
            systemPrompt.addEventListener('input', updateCharCount);

            // 初始化统计
            updateCharCount(); // 确保页面加载时立即执行一次

            // 处理键盘事件
            userInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    submitBtn.click();
                }
            });

            // 加载提示词
            async function loadSubjectPrompts() {
                try {
                    const response = await fetch("/console/app/save_subject_prompt");
                    const dbPrompts = await response.json();
                    const dbPromptMap = new Map(dbPrompts.map(item => [item.subject_id, item.prompt]));
                    subjects = baseSubjects.map(subject => ({
                        ...subject,
                        prompt: dbPromptMap.get(subject.id) || '该学科提示词未配置'
                    }));
                    initSubjectButtons();
                } catch (error) {
                    subjects = baseSubjects;
                    initSubjectButtons();
                    alert('提示词加载失败，使用默认配置');
                }
            }

            // 保存提示词
            saveBtn.addEventListener('click', async () => {
                const subjectId = subjectIdInput.value;
                const prompt = systemPrompt.value.trim();

                if (!subjectId || !prompt) return alert('请选择学科并填写提示词');
                if (prompt.length > 3000) {
                    alert(`提示词长度超过限制（3000字），当前长度：${prompt.length}`);
                    return;
                }

                // 禁用按钮并显示加载状态
                document.getElementById('save-btn-text').textContent = '保存中...';
                document.getElementById('save-btn-loading').style.display = 'block';

                try {
                    const response = await fetch("/console/app/save_subject_prompt", {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCSRFToken()
                        },
                        body: JSON.stringify([{ subject_id: subjectId, prompt }])
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || '保存失败');
                    }

                    alert('提示词保存成功');
                    subjects.find(s => s.id === subjectId).prompt = prompt;
                } catch (error) {
                    console.error('保存失败:', error);
                    alert('保存失败，请重试');
                } finally {
                    // 恢复按钮状态
                    saveBtn.disabled = false;
                    document.getElementById('save-btn-text').textContent = '保存当前提示词';
                    document.getElementById('save-btn-loading').style.display = 'none';
                }
            });

            // 在脚本顶部添加加载状态计数器
            let loadingCount = 0; // 用于生成唯一的加载状态ID

            // 新增：隐藏所有加载状态
            function hideAllLoadingStatus() {
                const loadingElements = document.querySelectorAll('.loading');
                loadingElements.forEach(el => {
                    el.style.display = 'none';
                });
            }

            // 处理提交
            submitBtn.addEventListener('click', async function () {
                // 防止重复提交
                if (isLoading) return;
                isLoading = true;

                // 获取输入内容
                const inputText = userInput.value.trim();
                const customInputText = document.getElementById('custom-input').innerText.trim();
                const actualInput = inputText || customInputText;
                // 获取用户选择的user_id
                // 构建scene_info对象
                const sceneInfo = {
                    scene: "ai_shuati",
                    subject_id: "TcT7x2dUZfG7BPyAeRNGFM"
                };
                // 验证输入内容
                if (!actualInput && allImages.length === 0 && imageInput.files.length === 0) {
                    isLoading = false;
                    return alert('请输入问题或上传图片');
                }

                const allPrompts = subjects.reduce((acc, s) => ({ ...acc, [s.id]: s.prompt }), {});
                const subjectId = subjectIdInput.value;
                const apiUrl = "/console/app/ai_assistant";
                // const userId = document.getElementById('user-id').value; // 获取选择的user_id

                // 提交时隐藏所有加载状态
                hideAllLoadingStatus();

                // 重置统计信息
                document.getElementById('response-time').textContent = '';
                document.getElementById('tokens-used').textContent = '';
                document.getElementById('character-count').textContent = '';

                // 构建 FormData
                const formData = new FormData();
                formData.append('input', inputText);
                formData.append('system_prompt', JSON.stringify(allPrompts));
                formData.append('subject_id', subjectId);
                formData.append('conversation_id', conversationId);
                // formData.append('user_id', document.getElementById('user-id').value);  // 确保此处取值正确
                formData.append('user_id', urlUserId);  // 确保此处取值正确
                formData.append('scene_info', JSON.stringify(sceneInfo));
                allImages.forEach(file => {
                    formData.append('images', file);
                });

                let buffer = ''; // 事件缓冲区
                const eventSeparator = '\n\n'; // SSE事件分隔符
                messageId = null;

                loadingIndicator.style.display = 'block'; // 发送时显示加载状态
                try {
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCSRFToken()
                        },
                        body: formData
                    });

                    // 先添加问题到历史记录 - 初始化时显示空统计信息
                    addHistoryItem(actualInput, '', {
                        response_latency: 0,
                        total_tokens: 0,
                        characters: 0
                    });

                    if (!response.ok) throw new Error(`服务器错误: ${response.status}`);

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder('utf-8', { fatal: true });
                    let fullQuestion = actualInput;
                    let fullResponse = '';

                    // DetermineTimeoutId = setInterval(() => {
                    //    checkDetermineTracing(); // 检查确定追踪
                    // }, 3000);

                    let thinkingDone = false; // 标记思考过程是否结束
                    let fullThinking = '';

                    while (true) {
                        const {done, value} = await reader.read();
                        if (done) {
                            hideAllLoadingStatus(); // 回答完成后隐藏所有加载状态
                            isLoading = false;
                            break;
                        }

                        // 解码并累积到缓冲区
                        buffer += decoder.decode(value, {stream: true});

                        // 分割完整的SSE事件
                        let eventEndIndex;
                        while ((eventEndIndex = buffer.indexOf(eventSeparator)) !== -1) {
                            const eventStr = buffer.slice(0, eventEndIndex).trim();
                            buffer = buffer.slice(eventEndIndex + eventSeparator.length);

                            // 处理每一行data
                            const lines = eventStr.split('\n');
                            for (const line of lines) {
                                if (line.startsWith('data: ')) {
                                    const jsonStr = line.slice(6).trim(); // 移除"data: "前缀
                                    if (!jsonStr) continue;

                                    try {
                                        const data = JSON.parse(jsonStr);
                                        if (data.message_id && !messageId) {
                                            messageId = data.message_id;
                                        }

                                        // 流式输出思考过程
                                        if (data.event === 'thinking' && !thinkingDone) {
                                            fullThinking += data.reasoning_content;

                                            // 将 \n 转换为 <br> 用于 HTML 中换行显示
                                            const displayThinking = fullThinking.replace(/\n/g, '<br>');

                                            // 更新最后一条的思考过程
                                            const answers = document.querySelectorAll('.history-item');
                                            if (answers.length > 0) {
                                                const lastAnswer = answers[answers.length - 1];
                                                const thinkingDiv = lastAnswer.querySelector('.history-thinking');
                                                if (thinkingDiv) {
                                                    thinkingDiv.innerHTML = '思考过程：' + displayThinking;
                                                }
                                            }
                                            continue;
                                        }

                                        // 处理思考过程完成标志
                                        if (data.event === 'thinking_end') {
                                            thinkingDone = true;
                                            continue;
                                        }

                                        // 只有思考过程结束后才输出最终回答
                                        if (thinkingDone && data.event === 'message') {
                                            // 拼接答案内容
                                            fullResponse += data.answer || ''; // 确保只拼接 answer 字段

                                            // 更新页面中的回答区域
                                            const answers = document.querySelectorAll('.history-item');
                                            if (answers.length > 0) {
                                                const lastAnswer = answers[answers.length - 1];
                                                const answerDiv = lastAnswer.querySelector('.history-answer');
                                                if (answerDiv) {
                                                    // 渲染为 Markdown 格式
                                                    answerDiv.innerHTML = marked.parse(fullResponse);
                                                    renderMathInElement(answerDiv, katexOptions);
                                                }
                                            }
                                        }

                                        // ...在 message_end 事件处理内...
                                        if (data.event === 'message_end') {
                                            hideAllLoadingStatus();
                                            isLoading = false;

                                            // 更新统计信息栏
                                            document.getElementById('response-time').textContent = `响应时长: ${data.metadata.response_latency?.toFixed(2) || '0.00'}秒`;
                                            document.getElementById('tokens-used').textContent = `消耗 Tokens: ${data.metadata.total_tokens || '0'}`;
                                            document.getElementById('character-count').textContent = `字符数: ${data.metadata.characters || '0'}`;


                                            const answers = document.querySelectorAll('.history-item');
                                            if (answers.length > 0) {
                                                const lastAnswer = answers[answers.length - 1];
                                                const questionDiv = lastAnswer.querySelector('.history-question');
                                                lastAnswer.querySelector('.response-time').textContent = `响应时长: ${data.metadata.response_latency?.toFixed(2) || '0.00'}秒`;
                                                lastAnswer.querySelector('.tokens-used').textContent = `消耗 Tokens: ${data.metadata.total_tokens || '0'}`;
                                                lastAnswer.querySelector('.character-count').textContent = `字符数: ${data.metadata.characters || '0'}`;

                                                // 设置为flex布局，内容横向排列
                                                questionDiv.style.setProperty('display', 'flex');
                                                questionDiv.style.setProperty('justify-content', 'space-between');
                                                questionDiv.style.setProperty('align-items', 'center');

                                                // 获取原问题文本
                                                const questionText = questionDiv.textContent;

                                                // 清空原内容
                                                questionDiv.innerHTML = '';

                                                // 左侧：问题文本
                                                const questionSpan = document.createElement('span');
                                                questionSpan.textContent = questionText;
                                                questionSpan.style.flex = '1';

                                                // 中间：分配过程按钮
                                                const allocationBtn = document.createElement('button');
                                                allocationBtn.textContent = '分配过程';
                                                allocationBtn.className = 'allocation-btn';
                                                allocationBtn.style.padding = '6px 12px';
                                                allocationBtn.style.background = '#1a73e8';
                                                allocationBtn.style.color = 'white';
                                                allocationBtn.style.border = 'none';
                                                allocationBtn.style.borderRadius = '6px';
                                                allocationBtn.style.fontSize = '0.85rem';
                                                allocationBtn.style.cursor = 'pointer';
                                                allocationBtn.style.transition = 'all 0.2s ease';
                                                allocationBtn.style.marginLeft = '12px';
                                                allocationBtn.onclick = async function() {
                                                    const modal = document.getElementById('allocation-modal');
                                                    const content = document.getElementById('allocation-content');
                                                    content.innerHTML = '加载中...';
                                                    modal.style.display = 'block';
                                                    try {
                                                        const resp = await fetch(`/console/app/ai_chat_tracing_list?message_id=${data.message_id}`);
                                                        const result = await resp.json();
                                                        if (result.code === 0 && Array.isArray(result.data) && result.data.length > 0) {
                                                            // 构造自适应行数的表格（无表头）
                                                            let html = `<table style="border-collapse:collapse;max-width:90vw;table-layout:auto;margin:auto;">`;
                                                            result.data.forEach(row => {
                                                                html += `<tr>
                                                                    <td style="border:1px solid #eee;padding:6px 12px;white-space:nowrap;">${row.add_time || ''}</td>
                                                                    <td style="border:1px solid #eee;padding:6px 12px;white-space:nowrap;">${row.type_name || ''}</td>
                                                                </tr>`;
                                                            });
                                                            html += `</table>`;
                                                            content.innerHTML = html;
                                                        } else {
                                                            content.innerHTML = '暂无分配过程信息';
                                                        }
                                                    } catch (e) {
                                                        content.innerHTML = '分配过程获取失败';
                                                    }
                                                };

                                                // 右侧：提问时间
                                                const timeSpan = document.createElement('span');
                                                timeSpan.style.color = '#888';
                                                timeSpan.style.fontSize = '0.95rem';
                                                timeSpan.style.marginLeft = '18px';
                                                timeSpan.style.whiteSpace = 'nowrap';

                                                // 设置初始时间为当前时间
                                                timeSpan.textContent = new Date().toLocaleString();

                                                // 异步获取专家回答时间并更新显示
                                                if (data.message_id) {
                                                    fetch(`/console/app/ai_chat_tracing_list?message_id=${data.message_id}`)
                                                        .then(resp => resp.json())
                                                        .then(result => {
                                                            if (result.code === 0 && Array.isArray(result.data)) {
                                                                const expertAnswer = result.data.find(item =>
                                                                    item.type_name && item.type_name.includes('专家回答问题')
                                                                );
                                                                if (expertAnswer && expertAnswer.add_time) {
                                                                    timeSpan.textContent = expertAnswer.add_time;
                                                                }
                                                            }
                                                        })
                                                        .catch(e => {
                                                            console.error('获取tracing数据失败:', e);
                                                        });
                                                }

                                                // 依次添加到问题栏
                                                questionDiv.appendChild(questionSpan);
                                                questionDiv.appendChild(allocationBtn);
                                                questionDiv.appendChild(timeSpan);
                                            }
                                        }
                                    } catch (parseError) {
                                        console.error('解析失败的JSON数据:', jsonStr);
                                        console.error('解析错误:', parseError);
                                    }


                                }
                            }
                        }
                    }

                    // 处理剩余不完整的事件
                    if (buffer) {
                        console.warn('检测到未完整的SSE事件:', buffer);
                    }

                    // clearInterval(DetermineTimeoutId);
                    // checkDetermineTracing();
                } catch (error) {
                    clearInterval(DetermineTimeoutId);

                    console.error('请求失败:', error);
                    hideAllLoadingStatus(); // 错误时隐藏所有加载状态
                    isLoading = false;
                    alert(`请求失败: ${error.message}`);
                }
            });

            closeModalBtn.addEventListener('click', async function () {
                document.getElementById('allocation-modal').style.display = 'none';
            })

            // 获取CSRF令牌
            function getCSRFToken() {
                return document.cookie.split('; ').find(row => row.startsWith('csrftoken='))?.split('=')[1];
            }

            // 初始化
            await loadSubjectPrompts();
            // 图片缩略图预览
        const imageInput = document.getElementById('image-input');
        const previewDiv = document.getElementById('image-preview');
        const customInput = document.getElementById('custom-input');
        const userInputHidden = document.getElementById('user-input');

        // 存储所有图片文件（包括粘贴和选择）
        let allImages = [];

        // 绑定缩略图点击放大
        function bindImagePreview() {
            const imgs = previewDiv.querySelectorAll('img');
            imgs.forEach(img => {
                img.style.cursor = 'pointer';
                img.onclick = function () {
                    document.getElementById('modal-img').src = img.src;
                    document.getElementById('image-modal').style.display = 'flex';
                };
            });
        }

        // 删除缩略图时同步 input[type=file]
        function syncImageInputFiles() {
            const dt = new DataTransfer();
            allImages.forEach(file => dt.items.add(file));
            imageInput.files = dt.files;
        }

        // 生成缩略图并添加删除按钮
        function addImagePreview(file, index) {
            const reader = new FileReader();
            reader.onload = function (e) {
                const imgWrapper = document.createElement('div');
                imgWrapper.style.position = 'relative';
                imgWrapper.style.display = 'inline-block';
                imgWrapper.style.margin = '0 6px 6px 0';
                imgWrapper.style.borderRadius = '6px';
                imgWrapper.style.overflow = 'hidden';

                const img = document.createElement('img');
                img.src = e.target.result;
                img.dataset.index = index;
                img.style.width = '38px';
                img.style.height = '38px';
                img.style.objectFit = 'cover';

                // 删除按钮
                const delBtn = document.createElement('div');
                delBtn.innerHTML = '×';
                delBtn.style.position = 'absolute';
                delBtn.style.top = '0';
                delBtn.style.right = '0';
                delBtn.style.background = 'rgba(255,0,0,0.7)';
                delBtn.style.color = 'white';
                delBtn.style.borderRadius = '0 0 0 6px';
                delBtn.style.cursor = 'pointer';
                delBtn.style.width = '16px';
                delBtn.style.height = '16px';
                delBtn.style.display = 'flex';
                delBtn.style.alignItems = 'center';
                delBtn.style.justifyContent = 'center';
                delBtn.style.fontSize = '12px';
                delBtn.style.lineHeight = '1';
                delBtn.onclick = function (e) {
                    e.stopPropagation();
                    allImages.splice(index, 1);
                    renderAllImagePreviews();
                };

                imgWrapper.appendChild(img);
                imgWrapper.appendChild(delBtn);
                previewDiv.appendChild(imgWrapper);
                bindImagePreview();
            };
            reader.readAsDataURL(file);
        }

        // 渲染所有图片缩略图
        function renderAllImagePreviews() {
            previewDiv.innerHTML = '';
            allImages.forEach((file, idx) => addImagePreview(file, idx));
            previewDiv.style.display = allImages.length ? 'flex' : 'none';
            syncImageInputFiles(); // 每次渲染后同步
        }

        // 监听粘贴事件
        customInput.addEventListener('paste', function (e) {
            const MAX_IMAGE_COUNT = 3;

            // 先检查是否有文本内容
            const text = e.clipboardData.getData('text/plain');
            if (text) {
                e.preventDefault();
                // 插入纯文本，去除格式
                document.execCommand('insertText', false, text);
            }

            // 然后处理图片
            if (allImages.length >= MAX_IMAGE_COUNT) {
                alert(`最多只能上传${MAX_IMAGE_COUNT}张图片`);
                e.preventDefault();
                return;
            }
            const items = (e.clipboardData || window.clipboardData).items;
            let hasImage = false;
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                if (item.kind === 'file' && item.type.startsWith('image/')) {
                    hasImage = true;
                    const file = item.getAsFile();
                    allImages.push(file); // 保存图片文件
                }
            }
            if (hasImage) {
                renderAllImagePreviews();
                e.preventDefault();
            }
        });

        // 监听 input[type=file] 变化，同步 allImages
        imageInput.addEventListener('change', function () {
            const MAX_IMAGE_COUNT = 3;
            const newFiles = Array.from(this.files);
            if (this.files.length + allImages.length > MAX_IMAGE_COUNT) {
                alert(`最多只能上传${MAX_IMAGE_COUNT}张图片`);
                this.value = '';  // 清空文件选择
                return;
            }
            // 将新文件追加到现有文件列表
            allImages.push(...newFiles);
            renderAllImagePreviews();
            {#this.value = ''; // 清空文件选择以备下次使用#}
        });

        // 关闭预览
        document.getElementById('image-modal').onclick = function () {
            this.style.display = 'none';
            document.getElementById('modal-img').src = '';
        };

        // 修改 customInput 的 keydown 事件处理逻辑
        customInput.addEventListener('keydown', function (e) {
            if (e.key === 'Backspace') {
                const selection = window.getSelection();
                if (!selection.rangeCount) return;

                const range = selection.getRangeAt(0);
                const container = range.startContainer;

                // 检查是否光标位于图片或删除按钮附近
                const imgWrapperElement = container.closest('div');
                if (imgWrapperElement && imgWrapperElement.querySelector('img')) {
                    const imgElement = imgWrapperElement.querySelector('img');
                    const indexToRemove = parseInt(imgElement.dataset.index);

                    if (!isNaN(indexToRemove)) {
                        // 移除对应的文件
                        allImages.splice(indexToRemove, 1);

                        // 同步删除文件上传按钮区域的文件
                        const dataTransfer = new DataTransfer();
                        allImages.forEach(file => dataTransfer.items.add(file));
                        imageInput.files = dataTransfer.files;

                        // 清空并重新渲染预览
                        previewDiv.innerHTML = '';
                        renderAllImagePreviews();

                        e.preventDefault();
                        return;
                    }
                }

                // 检查是否光标位于两个缩略图之间
                const previewChildren = Array.from(previewDiv.children);
                for (let i = 0; i < previewChildren.length; i++) {
                    const child = previewChildren[i];
                    if (range.startOffset >= child.nextSibling?.nodeType === Node.TEXT_NODE ?
                        child.nextSibling.textContent.indexOf(child) : 0) {

                        allImages.splice(i, 1);

                        // 同步删除文件上传按钮区域的文件
                        const dataTransfer = new DataTransfer();
                        allImages.forEach(file => dataTransfer.items.add(file));
                        imageInput.files = dataTransfer.files;

                        previewDiv.innerHTML = '';
                        renderAllImagePreviews();

                        e.preventDefault();
                        break;
                    }
                }
            }
        });

        // 处理键盘事件 - 回车键发送
        customInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                submitBtn.click();
            }
        });


        // 占位符逻辑
        customInput.addEventListener('input', function () {
            const text = customInput.innerText.trim();
            userInputHidden.value = text;
        });
        /*customInput.addEventListener('focus', function () {
            if (!customInput.innerText.trim()) placeholder.style.display = 'none';
        });
        customInput.addEventListener('blur', function () {
            if (!customInput.innerText.trim()) placeholder.style.display = 'block';
        });*/

        // 添加历史记录项 - 修改为包含统计信息
        function addHistoryItem(question, answer, metadata = {}) {
            const chatHistory = document.getElementById('chat-history');

            // 创建问题消息
            const questionDiv = document.createElement('div');
            questionDiv.className = 'history-item';

            // 添加思考过程、问答结果和统计信息区域
            questionDiv.innerHTML = `
                <div class="history-question">${question}</div>
                <div class="history-thinking" style="font-size: 0.9rem; color: #666; font-style: italic; margin-bottom: 8px;">思考过程：</div>
                <div class="history-answer">${answer || ''}</div>
                <div class="history-stats">
                    <span class="stat-item response-time">响应时长: ${metadata.response_latency?.toFixed(2) || '0.00'}秒</span>
                    <span class="stat-item tokens-used">消耗 Tokens: ${metadata.total_tokens || '0'}</span>
                    <span class="stat-item character-count">字符数: ${metadata.characters || '0'}</span>
                </div>
            `;

            // 添加到聊天历史
            chatHistory.appendChild(questionDiv);

            // 滚动到底部
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }

        // 显示完整图片
        window.showFullImage = function(src) {
            document.getElementById('modal-img').src = src;
            document.getElementById('image-modal').style.display = 'flex';
        };

        // 添加旋转动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: translateY(-50%) rotate(0deg); }
                100% { transform: translateY(-50%) rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // 用户选择器功能
        const userSelectorBtn = document.getElementById('user-selector-btn');
        const userDropdown = document.getElementById('user-dropdown');
        const newUserInput = document.getElementById('new-user-input');
        const addUserBtn = document.getElementById('add-user-btn');
        const userList = document.getElementById('user-list');
        const userIdInput = document.getElementById('user-id');

        // 初始化用户列表
        function initUserList() {
            const savedUsers = JSON.parse(localStorage.getItem('userList') || '[]');
            renderUserList(savedUsers);
        }

        // 渲染用户列表
        function renderUserList(users) {
            userList.innerHTML = '';

            // 先渲染固定选项
            const fixedUsers = [
                {id: '82', name: '青青'},
                {id: '72', name: '青青小号'},
                {id: '57', name: '纳兰'},
                {id: '421', name: '茹梦'},
                {id: '427', name: '余鹏'}
            ];
            fixedUsers.forEach(user => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.style.padding = '12px 16px';
                userItem.style.cursor = 'pointer';
                userItem.style.borderRadius = '8px';
                userItem.style.marginBottom = '4px';
                userItem.style.display = 'flex';
                userItem.style.justifyContent = 'space-between';
                userItem.style.alignItems = 'center';
                userItem.style.transition = 'all 0.2s ease';
                userItem.style.background = 'white';

                userItem.innerHTML = `
                    <span onclick="selectUser('${user.id}')" style="font-weight: 500; flex: 1;">${user.name}</span>
                    <span style="color:#666; margin-right:8px; white-space: nowrap;">删除</span>
                    <span class="delete-user-btn" onclick="deleteUser(event, '${user.id}')" style="
                        color: #ff4d4f;
                        cursor: pointer;
                        font-size: 18px;
                        width: 24px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        transition: all 0.2s;
                        background: rgba(255,77,79,0.1);
                    ">×</span>
                `;

                // 添加悬停效果
                userItem.addEventListener('mouseenter', () => {
                    userItem.style.background = '#f5f5f5';
                    userItem.style.transform = 'translateX(4px)';
                });
                userItem.addEventListener('mouseleave', () => {
                    userItem.style.background = 'white';
                    userItem.style.transform = 'none';
                });

                userList.appendChild(userItem);
            });

            // 再渲染动态添加的用户
            users.forEach(userId => {
                // 检查是否已经是固定用户
                if (fixedUsers.some(u => u.id === userId)) return;

                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.style.padding = '12px 16px';
                userItem.style.cursor = 'pointer';
                userItem.style.borderRadius = '8px';
                userItem.style.marginBottom = '4px';
                userItem.style.display = 'flex';
                userItem.style.justifyContent = 'space-between';
                userItem.style.alignItems = 'center';
                userItem.style.transition = 'all 0.2s ease';
                userItem.style.background = 'white';

                userItem.innerHTML = `
                    <span onclick="selectUser('${userId}', '${userId}')" style="font-weight: 500; flex: 1;">${userId}</span>
                    <span class="delete-user-btn" onclick="deleteUser(event, '${userId}')" style="
                        color: #ff4d4f;
                        cursor: pointer;
                        font-size: 18px;
                        width: 24px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        transition: all 0.2s;
                        background: rgba(255,77,79,0.1);
                    ">×</span>
                `;

                // 添加悬停效果
                userItem.addEventListener('mouseenter', () => {
                    userItem.style.background = '#f5f5f5';
                    userItem.style.transform = 'translateX(4px)';
                });
                userItem.addEventListener('mouseleave', () => {
                    userItem.style.background = 'white';
                    userItem.style.transform = 'none';
                });

                userList.appendChild(userItem);
            });
        }

        // 选择用户函数
        window.selectUser = function(userId, displayName) {
            document.getElementById('user-id').value = userId;
            // 修改这里，优先使用displayName，如果没有则使用userId
            const displayText = displayName || getFriendlyName(userId);
            document.getElementById('user-selector-text').textContent = `选择身份: ${displayText}`;
            document.getElementById('user-dropdown').style.display = 'none';

            // 如果用户是自定义的且不在列表中，则添加到本地存储
            const savedUsers = JSON.parse(localStorage.getItem('userList') || '[]');
            if (!savedUsers.includes(userId) && !['82','72','57','421','427'].includes(userId)) {
                savedUsers.push(userId);
                localStorage.setItem('userList', JSON.stringify(savedUsers));
                renderUserList(savedUsers);
            }
        };

        // 添加获取友好名称的函数
        function getFriendlyName(userId) {
            const nameMap = {
                '82': '青青',
                '72': '青青小号',
                '57': '纳兰',
                '421': '茹梦',
                '427': '余鹏'
            };
            return nameMap[userId] || userId;
        }

        // 添加新用户
        addUserBtn.addEventListener('click', () => {
            const newUser = newUserInput.value.trim();
            if (!newUser) return;

            const savedUsers = JSON.parse(localStorage.getItem('userList') || '[]');
            if (!savedUsers.includes(newUser)) {
                savedUsers.push(newUser);
                localStorage.setItem('userList', JSON.stringify(savedUsers));
                renderUserList(savedUsers);
            }

            newUserInput.value = '';
            userIdInput.value = newUser;
            // 修改这里，显示自定义用户ID
            document.getElementById('user-selector-text').textContent = `选择身份: ${newUser}`;
            userDropdown.style.display = 'none';
        });

        // 删除用户选项
        window.deleteUser = function(event, userId) {
            event.stopPropagation();
            const savedUsers = JSON.parse(localStorage.getItem('userList') || '[]');
            const updatedUsers = savedUsers.filter(user => user !== userId);
            localStorage.setItem('userList', JSON.stringify(updatedUsers));
            renderUserList(updatedUsers);

            // 如果删除的是当前选中的用户，清空选择
            if (document.getElementById('user-id').value === userId) {
                document.getElementById('user-id').value = '';
                document.getElementById('user-selector-btn').textContent = '选择用户';
            }
        };

        // 切换下拉菜单
        userSelectorBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const isVisible = userDropdown.style.display === 'block';
            userDropdown.style.display = isVisible ? 'none' : 'block';
            if (!isVisible) {
                // 确保下拉菜单可见时设置正确的位置
                userDropdown.style.opacity = '1';
                userDropdown.style.transform = 'translateY(0)';
            }
        });

        // 点击外部关闭下拉
        document.addEventListener('click', (e) => {
            if (!userDropdown.contains(e.target) && e.target !== userSelectorBtn) {
                userDropdown.style.display = 'none';
            }
        });

        // 初始化
        initUserList();
    });

    </script>
    <div id="image-modal" style="display:none;position:fixed;z-index:9999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.7);justify-content:center;align-items:center;">
        <img id="modal-img" src="" style="max-width:90vw;max-height:90vh;border-radius:10px;box-shadow:0 4px 24px rgba(0,0,0,0.3);background:#fff;">
    </div>
</body>
</html>