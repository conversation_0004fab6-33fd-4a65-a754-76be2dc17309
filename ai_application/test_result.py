#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速单元测试：验证   extract_errors_from_labeled_text / _generate_corrected_text_from_labeled
"""
import os
import django
import re

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()


from app.services.document_proofreader.document_proofreader_fun import (
    extract_errors_from_labeled_text,
    _generate_corrected_text_from_labeled,
)
def _id_reset(labeled_text: str) -> str:
    """
    重置错误标记的ID，从1开始累加
    """
    id_counter = 1
    
    def reset_error_id(match):
        nonlocal id_counter
        error_text = match.group(1)
        old_id = match.group(2)
        suggestion = match.group(3)
        reason = match.group(4)
        error_type = match.group(5)
        
        # 重新组装错误标记，使用新的ID
        new_error_mark = (
            f"~~{error_text}~~👉id: {id_counter} "
            f"修改建议: {suggestion} "
            f"错误理由: {reason} "
            f"错误类型: {error_type}👈"
        )
        
        id_counter += 1
        return new_error_mark
    
    # 使用正则表达式重置所有错误标记的ID
    labeled_text = re.sub(
        r'~~([^~👉👈]+?)~~👉id:\s*(\d+)\s*修改建议:\s*([^👈]*)\s*错误理由:\s*([^👈]*?)\s*错误类型:\s*([^👈]+?)👈',
        reset_error_id,
        labeled_text
    )
    return labeled_text

def normalize_error_types(labeled_text: str) -> str:
    """
    将labeled_text中每个错误标记的错误类型强制转换为四种标准类型之一
    """
    
    # 更精确的正则表达式，支持可选的错误文本
    norm_pattern = (
        r'~~([^~👉👈]*?)~~[^👉👈]*👉id:\s*(\d+)\s*'
        r'修改建议:\s*([\s\S]*?)\s*'
        r'错误理由:\s*([\s\S]*?)\s*'
        r'错误类型:\s*([^👈]+?)👈'
    )

    def _normalize_type(error_type: str) -> str:
        """错误类型标准化映射"""
        et = error_type.strip()
        
        # 错别字类
        if et in {"音近字", "形近字", "错别字", "的地得错误", "标点符号错误", "错别字类"}:
            return "错别字类"
        
        # 语法/句法错误类
        elif et in {"用词不当", "语法错误", "搭配不当", "逻辑不通顺", "重复内容", 
                   "句子不通顺", "语法/句法错误类"}:
            return "语法/句法错误类"
        
        # 常识错误类
        elif et in {"常识性错误", "常见地名错误", "常识错误类"}:
            return "常识错误类"
        
        # 其他类型统一归为专业术语错误类
        else:
            return "专业术语错误类"

    def _replace_error_type(match: re.Match) -> str:
        """替换错误类型的回调函数"""
        error_text = match.group(1)
        error_id = match.group(2)
        suggestion = match.group(3).strip()
        reason = match.group(4).strip()
        old_type = match.group(5)
        
        # 标准化错误类型
        new_type = _normalize_type(old_type)
        
        # 重新拼装错误标记（保持原格式）
        return (
            f"~~{error_text}~~👉id: {error_id} "
            f"修改建议: {suggestion} "
            f"错误理由: {reason} "
            f"错误类型: {new_type}👈"
        )

    # 执行替换
    result = re.sub(norm_pattern, _replace_error_type, labeled_text, flags=re.DOTALL)
    return result
def _reorder_error_ids(labeled_text: str) -> str:
    """
    重新编号错误ID，确保连续（只需8行代码）
    """
    # 找到所有错误标记
    # pattern = r'(~~[^~]+?~~👉id:\s*)(\d+)(.*?👈)'
    pattern = r'(~~[^~👉👈]+?~~[^~👉👈]*👉id:\s*)(\d+)(.*?👈)'
    matches = list(re.finditer(pattern, labeled_text))
    
    # 重新编号
    result = labeled_text
    for i, match in enumerate(matches, 1):
        old_mark = match.group()
        new_mark = f"{match.group(1)}{i}{match.group(3)}"
        result = result.replace(old_mark, new_mark, 1)  # 只替换第一个匹配
    
    return result
def main() -> None:
    labeled_text = """
    2.依照《中华民国训政时期约法》的规定，训政时期中华民国最高的训政者是（    ）（2015-非法学-44）~~👉id: 73 修改建议: C.撒打算大 D.立法院 错误理由: 选项间缺少空格分隔 错误类型:无👈
    A.国民全体B.国民大会
    ~~C.国民党D.立法院~~👉id: 73 修改建议: C.国民党 D.立法院 错误理由: 选项间缺少空格分隔 错误类型:无👈

    2.南京国民政府的成文法主要由六部法律及其相关单行法律构成，人们习惯将这一法律称为六法体系。下列关于六法体系的表述，正确的是（    ）（2016-非法学-63） 
    A.六法体系的构建实现了中国法律形式上的近代化~~C.国民党D.立法院~~
    B.《六法全书》的编纂~~C.国民党D.立法院~~标志着国民政府六法体系的构建完成~~C.撒打算大撒打算大撒.立法撒打算大院~~~~👉id: 73 修改建阿斯顿议: C.国民党 D.立法院 错误理由: 选项间缺少空格分隔 错误类型:无👈
    C.六法体系采取"以法典为纲，以相关法规为目"的编纂方式
    D.六法体系是仿照大陆体系国家构建的~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈的法律体系~~C.国民党D.立法院~~
    """
    # 去除异常标记
    # temp_text = labeled_text
    # temp_text = re.sub(
    #     r'~~([^~👉👈]+?)~~[^👉👈~]*👉id:\s*(\d+)\s*修改建议:\s*([^👈]*)\s*错误理由:\s*([^👈]*?)\s*错误类型:\s*([^👈]+?)👈', 
    #     r'\1', 
    #     temp_text
    # )
    # print("temp_text\n", temp_text)
    # # 获取完整的错误标记文本
    # error_marks = re.finditer(
    #     r'~~👉id:\s*(\d+)\s+修改建议:\s*([^👈]+?)\s+错误理由:\s*([^👈]+?)\s+错误类型:\s*([^👈]+?)👈',
    #     temp_text
    # )
    # # 提取完整的错误标记并从labeled_text中清理
    # for match in error_marks:
    #     error_mark = match.group(0)  # 完整的错误标记
    #     # 从labeled_text中移除这个错误标记
    #     labeled_text = labeled_text.replace(error_mark, '')
    # print("labeled_text1\n", labeled_text)
    
    # error_marks_wave = re.finditer(
    #     r'~~([^~👉👈]+?)~~',
    #     temp_text
    # )
    # for match in error_marks_wave:
    #     error_mark = match.group(0)  # 完整的错误标记
    #     error_content = match.group(1) if match.group(1) else ''  # 错误内容
    #     # 使用错误内容替换完整的错误标记
    #     labeled_text = labeled_text.replace(error_mark, error_content)    
    # print("labeled_text2\n", labeled_text)




    # print("=== 原始文本 ===")
    # print(labeled_text)
    
    # print("\n=== 标准化后的文本 ===")
    # normalized_text = normalize_error_types(labeled_text)
    # print(normalized_text)
    
    # # 验证结果：提取所有错误类型
    # error_types = re.findall(r'错误类型:\s*([^👈]+?)👈', normalized_text)
    # print(f"\n=== 提取到的错误类型 ===")
    # for i, et in enumerate(error_types, 1):
    #     print(f"错误 {i}: {et.strip()}")

    # 重置错误标记的ID
    labeled_text = """
    2.依照《中华民国训政时期约法》的规定，训政时期中华民国最高的训政者是
    A.国民全体B.国民大会
    ~~C.国民党D.立法院~~👉id: 73 修改建议: C.国民党 D.立法院 错误理由: 选项间缺少空格分隔 错误类型:无👈

    2.南京国民政府的成文法主要由六部法律及其相关单行法律构成，人们习惯将这一法律称为六法体系。下列关于六法体系的表述，正确的是
    A.六法体系的构建实现了中国法律形式上的近代化
    B.《六法全书》的编纂标志着国民政府六法体
    C.六法体系采取"以法典为纲，以相关法规为目"的编纂方式
    D.六法体系是仿照大陆体系国家构建的~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈的法律体系
    ~~以典为核心~~👉id: 74 修改建议:以法典1为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典2为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为3核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典4为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为5核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为6核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为7核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典8为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为9核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为10核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为11核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为12核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为13核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
    ~~以典为核心~~👉id: 74 修改建议:以法典为核心 错误理由:专业术语表述不准确 错误类型:形近字👈
"""
    print("labeled_text1\n", labeled_text)
    labeled_text = _reorder_error_ids(labeled_text)
    print("labeled_text2\n", labeled_text)


if __name__ == "__main__":
    main()