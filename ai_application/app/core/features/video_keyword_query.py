from collections import defaultdict

from django.conf import settings

from app.core.features.base_feature import BaseFeature
from app.core.rag.models.document import Document
from app.core.rag.retrieval.dataset_retrieval import DatasetRetrieval
from app.models import Dataset, CourseVideoKeyword


class VideoKeywordQueryFeature(BaseFeature):

    def __init__(self):
        self.dataset = Dataset.objects.get(dataset_no=settings.SUBTITLE_DATASET)
        self.dataset_retrieval = DatasetRetrieval()
        self.score_threshold = 0.5
        # 关键词向量匹配总分占比
        self.weight_rate = 0.6

    def run(self, kg_name, size: int = 2) -> list:
        documents = self.query_keywords_by_kg(kg_name)
        if not documents:
            return []

        keyword_score_list = []
        for d in documents:
            # 向量搜索返回的score越小相似度越高
            doc_score = 1 - d.metadata.get('score')
            video_kws = CourseVideoKeyword.objects.filter(keyword=d.page_content)
            for i in video_kws:
                # 目前使用权重和分数相加的方法
                subtitle_score = self.cal_subtitle_score(i.weight, doc_score)
                keyword_score_list.append({
                    'document_no': i.dataset_document.document_no,
                    'keyword': i.keyword,
                    'score': subtitle_score,
                })
        if not keyword_score_list:
            return []

        subtitle_document_score_map = defaultdict(float)
        for i in keyword_score_list:
            subtitle_document_score_map[i['document_no']] += i['score']

        sorted_documents = sorted(subtitle_document_score_map.items(), key=lambda item: item[1], reverse=True)

        # 按照分数排序，获取分数最高的
        return [i[0] for i in sorted_documents[:size]]

    def query_keywords_by_kg(self, kg_name) -> list[Document]:
        context = self.dataset_retrieval.single_retrieve(
            self.dataset, [], kg_name, {'top_k': 2})
        docs = []
        for d in context:
            score = d.metadata.get('score')
            if score == 0:
                # 完全一样则直接返回
                return [d]
            if score is not None and score < self.score_threshold:
                docs.append(d)
        return docs

    def cal_subtitle_score(self, weight, doc_score):
        return (weight * self.weight_rate + doc_score * (1 - self.weight_rate)) / 2
