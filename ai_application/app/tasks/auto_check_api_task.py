import base64
import json
import logging
import time
import uuid

import requests
import hashlib
import hmac
import urllib.parse

from celery import shared_task

logger = logging.getLogger(__name__)


api_list = {
    'pc_login': {
        "name": "pc_用户登录",
        "url": "https://yantucs.com/manageApi/login",
        "method": "PUT",
        "headers": {
            "Content-Type": "application/json"
        },
        "body": {
            "username": "gaoyang",
            "password": "0823d8c4f8ce16d7fe8996ae0621e50f",
            "__sign": "CA8C11BB3804E1A2D0C14F225D839B4B"
        }
    },
    'pc_chat_add_conversation': {
        "name": "知舟问答-创建会话",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/chat_conversation/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "app_id": "3",
            "goods_sn": "DT100144",
            "is_math_question_helper": False
        }
    },
    'pc_chat_send_normal': {
        "name": "知舟问答-发送普通消息",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/chat_message/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "cid": 0,
            "query": "什么是数组",
            "message_type": "normal",
            "file_objs": [""]
        },
        "is_stream": True
    },
    'pc_chat_send_question': {
        "name": "知舟问答-发送408解题助手消息",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/chat_message/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "cid": 0,
            "query": "请解答这个题目",
            "retry": False,
            "message_type": "question",
            "file_objs": ["application/0.6221592919495844.jpeg"]
        },
        "is_stream": True
    },
    'pc_chat_send_code': {
        "name": "知舟问答-发送代码优化消息",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/chat_message/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "cid": 0,
            "query":"print(\"hello\")",
            "message_type": "code",
            "lang_code": "python",
            "file_objs": [],
        },
        "is_stream": True
    },
    'pc_chat_delete_conversation': {
        "name": "删除会话",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/chat_conversation/{cid}/",
        "method": "DELETE",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
    },
    'pc_knowledge_query': {
        "name": "知舟问答-知识解析",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/knowledge-search/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "query": "数组",
            "is_recommend": 0,
            "knowledge_config_id": 28
        },
        "is_stream": True
    },
    'pc_english_sentence': {
        "name": "长难句解析",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/long-sentence-parse-text/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "app_id": 9,
            "goods_sn": "DT100144",
            "text": "They began to believe that their way of doing business was failing, and that their incomes would therefore shortly begin to fall as well."
        },
        "is_stream": True
    },
    'pc_math_chat_add_conversation': {
        "name": "数学解题助手-创建会话",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/chat_conversation/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "app_id": "3",
            "goods_sn": "DT100203",
            "is_math_question_helper": True
        }
    },
    'pc_math_chat_send_message': {
        "name": "数学解题助手-发送消息",
        "url": "https://yantucs.com/yantucs/pcApi/v1/ai/chat_message/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "cid": 0,
            "query": "请解答这个题目",
            "message_type": "math_question_helper",
            "file_objs": ["application/0.8311545594830092.jpeg"]
        },
        "is_stream": True
    },
    'api_chat_add_conversation': {
        "name": "知舟API-知舟问答-创建会话",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_400e0451122f41d3/conversation_add",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {}
    },
    'api_chat_send_message': {
        "name": "知舟API-知舟问答-发送消息",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_400e0451122f41d3/chat_messages",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "customer_id": "",
            "conversation_id": "",
            "query": "什么是考研计算机中的队列",
            "userinfo": {
                "user_id": "00",
                "nickname": "测试用户"
            }
        }
    },
    'api_chat_408_add_conversation': {
        "name": "知舟API-408解题助手-创建会话",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_7d023b8f22ad44e9/conversation_add",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {}
    },
    'api_chat_408_send_message': {
        "name": "知舟API-408解题助手-发送消息",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_7d023b8f22ad44e9/solve_question",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "conversation_id": "",
            "question_image": "https://oss.kaoyanvip.cn/attach/file1740452782554.PNG",
            "question_content": "请解答这个题目",
            "custom_user_id": "",
            "userinfo": {
                "user_id": "00",
                "nickname": "测试用户"
            }
        }
    },
    'api_chat_code_add_conversation': {
        "name": "知舟API-代码优化-创建会话",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_013f3c60675f4c5a/conversation_add",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {}
    },
    'api_chat_code_send_message': {
        "name": "知舟API-代码优化-发送消息",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_013f3c60675f4c5a/code_optimize",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "conversation_id": "",
            "code_type": "python",
            "code_content": "print('hello')",
            "custom_user_id": "",
            "userinfo": {
                "user_id": "00",
                "nickname": "测试用户"
            }
        }
    },
    'api_chat_math_add_conversation': {
        "name": "知舟API-数学解题助手-创建会话",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_55e30fe6009c4668/conversation_add",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {}
    },
    'api_chat_math_send_message': {
        "name": "知舟API-数学解题助手-发送消息",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_55e30fe6009c4668/math_solve_question",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "conversation_id": "",
            "question_image": "https://gaotu-qiwei.oss-cn-shanghai.aliyuncs.com/application/0.8377965525424389.jpeg",
            "question_content": "请解答这个题目",
            "custom_user_id": "",
            "userinfo": {
                "user_id": "00",
                "nickname": "测试用户"
            }
        }
    },
    'api_en_sentence_add_conversation': {
        "name": "知舟API-长难句解析-创建会话",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_f4b512c27b2145bc/conversation_add",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {}
    },
    'api_en_sentence_analysis': {
        "name": "知舟API-长难句解析",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_f4b512c27b2145bc/en_sentence_analysis",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "custom_user_id": "",
            "conversation_id": "",
            "sentence_img": "",
            "sentence_content": "Now there is none: Zenith was bought by South Korea’s LG Electronics in July.",
            "userinfo": {
                "user_id": "00",
                "nickname": "测试用户"
            }
        }
    },
    'api_en_article_analysis': {
        "name": "知舟API-作文批改",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/coze/7483831956434681894/chat_messages",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "conversation_id": "",
            "query": "",
            "image": "https://oss.kaoyanvip.cn/attach/file1742453704160.png",
            "userinfo": {
                "user_id": "00",
                "nickname": "测试用户"
            }
        }
    },
    'api_math_test_report': {
        "name": "知舟API-数学测试解读",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/test_answer_report_generator",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "answer_id": "YkpR5h8gtGryYucDsE7FLc",
            "user_id": "2VspVAzUwtNhm29fgND7KE"
        }
    },
    'api_gaoshu_screenshot_question': {
        "name": "知舟API-高数截屏答疑",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/gaoshu_screenshot_question",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "course_section_id": "10153101",
            "user_id": "93",
            "screenshot_img": "https://oss.kaoyanvip.cn/attach/file1744275761451.png",
            "query": "这个题怎么做"
        }
    },
    'api_study_planning': {
        "name": "知舟API-学习规划",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/college/kaoyan_review_plan",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "report_id": "report_id_001",
            "target_college_level": "intensive"
        }
    },

    'video_script_generator': {
        "name": "视频脚本生成器",
        "url": "https://yantucs.com/yantucs/manageApi/v1/ai/video_script/generate/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "params": {
                "subject": "考研or就业"
            },
            "token": "00b888ab-58ac-40fd-bb55-e22d17be9a9c"
        }
    },
    'hot_text_refining': {
        "name": "爆文-提炼总结",
        "url": "https://yantucs.com/yantucs/manageApi/v1/ai/hot_text/file/298/refining/?id=298",
        "method": "GET",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {}
    },
    'hot_text_reset_conversation': {
        "name": "爆文-重置会话",
        "url": "https://yantucs.com/yantucs/manageApi/v1/ai/hot_text/file/298/reset-conversation/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "id": "298",
            "prompt_template_id": "3"
        }
    },
    'hot_text_generate_article': {
        "name": "爆文-生成文章",
        "url": "https://yantucs.com/yantucs/manageApi/v1/ai/hot_text/file/298/gc/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "cid": 65,
            "type": "click",
            "text": "- 人工智能是一个领域而非具体专业，本科阶段学习人工智能专业内容广泛且杂，涉及计算机、电子、通信、控制、电路等多个方向。\n- 计算机、电子信息等可为人工智能赋能，但本科阶段难以深入接触真正的人工智能产品，研究生阶段更为重要。\n- 不建议过度迷信本科阶段的人工智能专业，很多只是概念炒作，真正的应用能力需要在研究生阶段通过深入学习才能获得。",
            "prompt_template_id": "3",
            "file_id": "298"
        }
    },
    'course_manual_conversion_assistant': {
        "name": "课程手册转换助手",
        "url": "https://yantucs.com/yantucs/manageApi/v1/ai/cmc/file/conversion/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "file_id": 13,
            "output": "sale",
            "__sign": "88A4CF2C6DAEA4C66C6A79EC90EB09F9"
        }
    },
    'prompt_optimize': {
        "name": "提示词优化",
        "url": "https://yantucs.com/yantucs/manageApi/v1/ai/feat-prompt/",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "User-Token": ""
        },
        "body": {
            "role": "计算机老师",
        }
    },
    'api_learn_analysis_submit': {
        "name": "学情分析-提交",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/learning_analysis_submit",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "user_id": "T4bKBYvM8kUATv8Fxw2ZzD",
        }
    },
    'api_learn_analysis_status': {
        "name": "学情分析-查看状态",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/learning_analysis_status?task_id=bdcfcefa-d37f-4b05-baee-057947890248",
        "method": "GET",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
        }
    },
    'api_english_paper_explain': {
        "name": "英语试卷解读-提交",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/english_paper_explain",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "user_id": "82",
            "answer_id": "udDrKCc7BRAd2rwtdziXaa"
        }
    },
    'api_english_paper_explain_status': {
        "name": "英语试卷解读-查看状态",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/english_paper_explain_status?task_id=20b324e9-eece-4162-97b5-734d09ce7228",
        "method": "GET",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
        }
    },
    'api_english_reading_question': {
        "name": "英语阅读理解出题",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_ffb67f8dd64c4196/english_reading_question",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "userinfo": {
                "user_id": "17",
                "nickname": "尉迟斌"
            }
        }
    },
    'api_english_reading_question_again': {
        "name": "英语阅读理解再出题",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_ffb67f8dd64c4196/english_reading_question_again",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "userinfo": {
                "user_id": "17",
                "nickname": "尉迟斌"
            },
            "question_id": "209",
            "wrong_sub_question_ids": [
                "1973"
            ]
        }
    },
    'api_english_word_question': {
        "name": "英语单词测试_出题",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_d66b4aaedc70469e/english/word_question",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "userinfo": {
                "user_id": "user_01",
                "nickname": "测试用户"
            }
        }
    },
    'api_english_word_question_submit': {
        "name": "英语单词测试_提交测试",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_d66b4aaedc70469e/english/word_question_submit",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "record_id": "36",
            "answer_detail": {
                "question_id": 355,
                "user_answer": "A"
            },
            "userinfo": {
                "user_id": "user_01"
            }
        }
    },
    'api_english_word_wrong_question_count': {
        "name": "英语单词测试_错题数量",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_d66b4aaedc70469e/english/word_wrong_question_count",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "userinfo": {
                "user_id": "user_01",
                "nickname": "测试用户"
            }
        }
    },
    'api_english_word_wrong_question': {
        "name": "英语单词测试_错题出题",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_d66b4aaedc70469e/english/word_wrong_question",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "userinfo": {
                "user_id": "user_01",
                "nickname": "测试用户"
            }
        }
    },
    'api_english_word_wrong_question_submit': {
        "name": "英语单词测试_错题答题",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_d66b4aaedc70469e/english/word_wrong_question_submit",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "userinfo": {
                "user_id": "user_01",
                "nickname": "测试用户"
            },
            "question_id": 355,
            "user_answer": "A"
        }
    },
    'api_college_analysis': {
        "name": "知舟API-院校专业推荐",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/college/college_analysis",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "report_id": 'report_id_001', "bachelor_level": "985工程", "bachelor_major": "计算机", "gpa_range": "3.8+",
            "major_ranking": "前10%", "research_experience": "", "competition_experience": "无",
            "english_ability": "六级550", "math_basis_select": "基础一般", "candidate_status": "应届生",
            "concurrent_preparation": ["无"], "regions": ["华北"],
            "tuition_sensitivity": "可承担3万/年", "priority_order": "院校层次>地区",
            "master_degree_type": "全日制", "exam_year": "2026年",
            "target_college_major": [
                {"college_name": "中国人民大学", "college_code": "10002", "major_name": "计算机科学与技术", "major_code": "081200"},
                {"college_name": "北京大学", "college_code": "10001", "major_name": "计算机科学与技术", "major_code": "081200"}
            ],
            "target_major_direction": [
                {"name": "计算机应用技术", "code": "077503"},
                {"name": "计算机软件与理论", "code": "081202"}
            ]
        }
    },
    'api_question_check': {
        "name": "智痕雷达",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/faq/question_check",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json",
            "X-SIGN-DEBUG": "1111"
        },
        "body": {
            "assistant_id": "XgLt2fbLwei26GRD8zmBdL",
            "subject_id": "TcT7x2dUZfG7BPyAeRNGFM",
            "question_id": "Qjoce9zyCYbkkwzYLG4KHn",
            "answer_id": "fUjRDzj6jQswyT63XzZB8S",
            "question": "传输层的通信和网络层的传输有什么区别？",
            "answer": "复用就是只有一天线路，但是可以有很多主机同时利用这个线路传输数据。分用就是因为有很多主机用同一个链路，这些数据是掺杂在一块的，分用是怎么把这些信号分开。作用就是增大数据传输效率，试想如果一条效率同一时间只能传输一个数据，那么效率会大大降低。",
            "answer_duration": "221"
        }
    },
    'api_chat2_add_conversation': {
        "name": "知舟API-知舟问答2.0-创建会话",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/app_343e8f89c3f64fac/conversation_add",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {}
    },
    'api_chat2_send_message': {
        "name": "知舟API-知舟问答2.0-发送消息",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v2/app_343e8f89c3f64fac/chat_messages",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "custom_user_id": "10001",
            "conversation_id": "",
            "query": "法硕考什么",
            "images": [],
            "userinfo": {
                "user_id": "53",
                "nickname": "测试用户"
            }
        }
    },
    'api_knowledge_analysis_basic': {
        "name": "知识解析-基础版",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/knowledge_psychology/knowledge_analysis_basic",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json",
            "X-SIGN-DEBUG": "111"
        },
        "body": {
            "custom_user_id": "66",
            "query": "大五人格理论是什么",
            "is_accurate": False,
            "userinfo": {
                "user_id": "23",
                "nickname": "濮民"
            },
            "stream": False
        }
    },
    'api_user_study_plan_stat': {
        "name": "教务督学分析-提交",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/learn/user_study_plan_stat",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "user_id": "Y2pYpbteWpyLrV2AhV3Mhe",
            "course_id": "CP000360000",
            "subject_id": "M8HpvpTXrS88tQq3uYeKYQ",
            "start_date": "2025-02-01",
            "end_date": "2025-02-10"
        }
    },
    'api_user_study_plan_stat_check': {
        "name": "教务督学分析-查看状态",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/learn/user_study_plan_stat_check?task_id=e761210a-9b56-4618-af0e-4546c7847fc3",
        "method": "GET",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
        }
    },
    'api_dsx_learning_stat': {
        "name": "动手学-课后练习-学情分析",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/dsx/learning_stat",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "userinfo": {
                "user_id": 1,
                "nickname": "测试用户"
            },
            "class_test": {
                "questions": [
                    {
                        "kg": "递归",
                        "title": "<p>设有一个递归算法如下：</p><p style=\"text-indent: 24.0000pt;\">int fact(int n){</p><p style=\"text-indent: 48.0000pt;\">if(n<=0)</p><p style=\"text-indent: 72.0000pt;\">return 1;</p><p style=\"text-indent: 48.0000pt;\">else</p><p style=\"text-indent: 72.0000pt;\">return n*fact(n-1);</p><p style=\"text-indent: 24.0000pt;\">}</p><p style=\"text-indent: 24.0000pt;\">则计算fact(n)需要调用该函数的次数为（&nbsp;）。</p> \n\n ['<p>n&nbsp;</p>', '<p>n+1&nbsp;</p>', '<p>n+2&nbsp;</p>', '<p>n-1 </p>']",
                        "is_right": False,
                        "question_id": 830,
                        "user_answer": "B",
                        "right_answer": "A"
                    }
                ]
            },
            "section_kgs": [
                "递归"
            ],
            "section_name": "什么是递归",
            "class_exercise": {
                "questions": [
                    {
                        "kg": "递归",
                        "title": "下列关于递归的描述中，哪一项是错误的？ \n\n ['递归函数必须有一个明确的终止条件', '递归函数在每次调用时都会创建一个新的栈帧', '递归函数的效率总是高于迭代实现', '递归可以用来解决分治类问题']",
                        "is_right": True,
                        "question_id": 151,
                        "user_answer": "C",
                        "right_answer": "C"
                    }
                ]
            },
            "learn_duration": 14,
            "video_duration": 1246,
            "video_kg_distribute": [
                {
                    "kg": "递归",
                    "start": 1
                }
            ]
        }
    },
    'api_dsx_learning_guide': {
        "name": "动手学-学习指导",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/dsx/learning_guide",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "userinfo": {
                "user_id": 1,
                "nickname": "测试用户"
            },
            "chapter_name": "第 3 章 数据结构",
            "section_learn_stat": [
                {
                    "percent": 1.0,
                    "section_name": "什么是数据结构"
                },
                {
                    "percent": 0,
                    "section_name": "数据类型和抽象数据类型"
                }
            ],
            "weakness_knowledge": [
                "顺序存储结构",
                "逻辑结构",
                "数据",
                "数据对象",
                "结构体类型",
                "线性结构",
                "树结构",
                "图结构",
                "数据类型"
            ]
        }
    },
    'api_shuati_create_question': {
        "name": "智学刷题-学习指导",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/ai/pq_create_question",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "user_id": "test_user_1",
            "subject_id": "TcT7x2dUZfG7BPyAeRNGFM",
            "core_course_code": "CC_SJJG"
        }
    },
    'api_shuati_post_question': {
        "name": "智学刷题-学习指导",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/ai/pq_post_question",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "paper_id": 0,
            "user_id": "test_user_1",
            "question_id": 0,
            "choice_answer": ["D"],
            "subjective_answer": {"images": [], "text": "不会"}
        }
    },
    'api_shuati_get_result': {
        "name": "智学刷题-学习指导",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/ai/pq_get_result",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "answer_id": 0
        }
    },
    'api_shuati_get_report': {
        "name": "智学刷题-学习指导",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/ai/pq_get_report",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "answer_id": 0
        }
    },
    'api_shuati_get_subjective_report': {
        "name": "智学刷题-学习指导",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/ai/pq_get_subjective_report",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "answer_id": 0,
            "question_id": 0
        }
    },
    'api_shuati_get_stage_change_info': {
        "name": "智学刷题-学习指导",
        "url": "https://yantucs.com/yantucs/api/zhizhou/v1/ai/pg_get_stage_change_info",
        "method": "POST",
        "headers": {
            "x-api-key": "{{api_key}}",
            "x-signature": "{{signature}}",
            "timestamp": "{{timestamp}}",
            "nonce": "{{nonce}}",
            "Content-Type": "application/json"
        },
        "body": {
            "answer_id": 0
        }
    },
}


def send_dingtalk_message(message, at_all=False):
    webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=264108311407e73af95525263c1a55f6cfc330327e3e6ec4d4acfe29420b5d96'
    # 构建钉钉消息
    data = {
        "msgtype": "text",
        "text": {"content": message},
        "at": {"isAtAll": at_all}
    }
    # 设置请求头
    headers = {
        "Content-Type": "application/json;charset=utf-8"
    }
    sign, timestamp = sign_dingtalk_message()
    # 发送POST请求
    response = requests.post(
        webhook_url,
        params={'timestamp': timestamp, 'sign': sign},
        headers=headers,
        data=json.dumps(data)
    )
    return response.json()


def sign_dingtalk_message():
    timestamp = str(round(time.time() * 1000))
    secret = 'SEC57ecd3f8d792fc4b4f8b4493f78d312ab91224111b0e6fb088f6a72c275d408b'
    secret_enc = secret.encode('utf-8')
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    string_to_sign_enc = string_to_sign.encode('utf-8')
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))

    return sign, timestamp


def gen_sign(params: dict, secret_key: str):
    string_to_sign = "&".join([f"{key}={value}" for key, value in sorted(params.items())])
    return hmac.new(secret_key.encode(), string_to_sign.encode(), hashlib.sha256).hexdigest()


def gen_sign_dict(app_key=None, secret_key=None):
    nonce = 'ld8h259m'
    api_key = app_key or 'ak_bc1ea37f68a84d8b'
    secret_key = secret_key or 'jbbhzq8kdu21wxn7qjp9nbgrattn2tnx'
    timestamp = str(int(time.time()))
    sign_dict = {
        'api_key': api_key,
        'timestamp': timestamp,
        'nonce': nonce,
    }
    sign_dict['signature'] = gen_sign(sign_dict, secret_key)
    return sign_dict


def build_request(api_config):
    is_stream = api_config.get("is_stream", False)
    method = api_config['method']
    url = api_config['url']
    headers = api_config['headers']
    response = None
    if method == "POST":
        response = requests.post(url, json=api_config['body'], headers=headers, stream=is_stream)
    elif method == "GET":
        response = requests.get(url, headers=headers)
    elif method == "PUT":
        response = requests.put(url, json=api_config['body'], headers=headers)
    elif method == "DELETE":
        response = requests.delete(url, headers=headers)
    return response


def handle_response(api_config, response):
    try:
        response.raise_for_status()
        data = response.json()
    except Exception as e:
        err_msg = f'{api_config["name"]}调用失败，错误：{str(e)}'
        raise Exception(err_msg)

    if data.get('code') != 200:
        err_msg = f'{api_config["name"]}调用失败，错误：{data.get("message")}'
        raise Exception(err_msg)

    return data.get('data')


def handle_stream_response(response):
    response.raise_for_status()

    for chunk in response.iter_lines():
        print(f'🚀chunk={chunk}')
        # print(json.loads(chunk))


def gen_pc_token():
    api_config = api_list.get('pc_login')

    response = build_request(api_config)
    response.raise_for_status()
    data = response.json()
    if data.get('code') != 200:
        raise Exception('PC登录接口失败')
    return data.get('data').get('User_Token')


def run_pc_app():
    err_list = []

    try:
        # 获取用户token
        token = gen_pc_token()
    except Exception as e:
        err_list.append(str(e))
        return err_list

    # 检查知舟问答
    api_config = api_list.get('pc_chat_add_conversation')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)

    cid = 0
    try:
        res = handle_response(api_config, response)
        cid = res.get('cid')
    except Exception as e:
        err_list.append(str(e))

    if cid:
        # 发送普通消息
        api_config = api_list.get('pc_chat_send_normal')
        api_config['headers']['User-Token'] = token
        api_config['body']['cid'] = cid
        response = build_request(api_config)
        try:
            handle_stream_response(response)
        except Exception as e:
            err_list.append(str(e))

        # 发送408解题助手消息
        api_config = api_list.get('pc_chat_send_question')
        api_config['headers']['User-Token'] = token
        api_config['body']['cid'] = cid
        response = build_request(api_config)
        try:
            handle_stream_response(response)
        except Exception as e:
            err_list.append(str(e))

        # 发送代码优化消息
        api_config = api_list.get('pc_chat_send_code')
        api_config['headers']['User-Token'] = token
        api_config['body']['cid'] = cid
        response = build_request(api_config)
        try:
            handle_stream_response(response)
        except Exception as e:
            err_list.append(str(e))

        # 删除会话
        # 直接替换url需要先把参数copy一下
        api_config = api_list.get('pc_chat_delete_conversation').copy()
        api_config['headers']['User-Token'] = token
        api_config['url'] = api_config['url'].replace('{cid}', str(cid))
        response = build_request(api_config)
        try:
            handle_response(api_config, response)
        except Exception as e:
            err_list.append(str(e))

    # 知识解析
    api_config = api_list.get('pc_knowledge_query')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)
    try:
        handle_stream_response(response)
    except Exception as e:
        err_list.append(str(e))

    # 长难句解析
    api_config = api_list.get('pc_english_sentence')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)
    try:
        handle_stream_response(response)
    except Exception as e:
        err_list.append(str(e))

    # 数学解题助手-创建会话
    api_config = api_list.get('pc_math_chat_add_conversation')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)

    cid = 0
    try:
        res = handle_response(api_config, response)
        cid = res.get('cid')
    except Exception as e:
        err_list.append(str(e))

    if cid:
        # 数学解题助手-发送消息
        api_config = api_list.get('pc_math_chat_send_message')
        api_config['headers']['User-Token'] = token
        api_config['body']['cid'] = cid
        response = build_request(api_config)
        try:
            handle_stream_response(response)
        except Exception as e:
            err_list.append(str(e))

        # 删除会话
        # 直接替换url需要先把参数copy一下
        api_config = api_list.get('pc_chat_delete_conversation').copy()
        api_config['headers']['User-Token'] = token
        api_config['url'] = api_config['url'].replace('{cid}', str(cid))
        response = build_request(api_config)
        try:
            handle_response(api_config, response)
        except Exception as e:
            err_list.append(str(e))

    # 视频脚本生成器
    api_config = api_list.get('video_script_generator')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)
    try:
        handle_stream_response(response)
    except Exception as e:
        err_list.append(str(e))

    # 爆文-提炼总结
    api_config = api_list.get('hot_text_refining')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)
    try:
        handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))

    # 爆文-重置会话
    api_config = api_list.get('hot_text_reset_conversation')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)

    cid = 0
    try:
        res = handle_response(api_config, response)
        cid = res.get('cid')
    except Exception as e:
        err_list.append(str(e))

    if cid:
        # 爆文-生成文章
        api_config = api_list.get('hot_text_generate_article')
        api_config['headers']['User-Token'] = token
        api_config['body']['cid'] = cid
        response = build_request(api_config)
        try:
            handle_stream_response(response)
        except Exception as e:
            err_list.append(str(e))

    # 课程手册转换助手
    api_config = api_list.get('course_manual_conversion_assistant')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)
    try:
        handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))

    # 课程手册转换助手
    api_config = api_list.get('prompt_optimize')
    api_config['headers']['User-Token'] = token
    response = build_request(api_config)
    try:
        handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))

    return err_list


def fill_api_config_sign(api_config, app_key=None, secret_key=None):
    sign_dict = gen_sign_dict(app_key=app_key, secret_key=secret_key)
    api_config['headers']['x-api-key'] = sign_dict['api_key']
    api_config['headers']['x-signature'] = sign_dict['signature']
    api_config['headers']['timestamp'] = sign_dict['timestamp']
    api_config['headers']['nonce'] = sign_dict['nonce']
    api_config['headers']['x-sign-debug'] = '1'


def run_api():
    err_list = []

    # 知舟API-知舟问答
    api_config = api_list.get('api_chat_add_conversation')
    fill_api_config_sign(api_config)
    response = build_request(api_config)

    cid = 0
    try:
        res = handle_response(api_config, response)
        cid = res.get('id')
    except Exception as e:
        err_list.append(str(e))

    if cid:
        # 知舟API-知舟问答-发送消息
        api_config = api_list.get('api_chat_send_message')
        fill_api_config_sign(api_config)
        api_config['body']['conversation_id'] = cid
        response = build_request(api_config)
        try:
            handle_stream_response(response)
        except Exception as e:
            err_list.append(str(e))

    # # 知舟API-408解题助手
    # api_config = api_list.get('api_chat_408_add_conversation')
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    #
    # cid = 0
    # try:
    #     res = handle_response(api_config, response)
    #     cid = res.get('id')
    # except Exception as e:
    #     err_list.append(str(e))
    #
    # if cid:
    #     # 知舟API-408解题助手-发送消息
    #     api_config = api_list.get('api_chat_408_send_message')
    #     fill_api_config_sign(api_config)
    #     api_config['body']['conversation_id'] = cid
    #     response = build_request(api_config)
    #     try:
    #         handle_stream_response(response)
    #     except Exception as e:
    #         err_list.append(str(e))

    # 知舟API-代码优化
    api_config = api_list.get('api_chat_code_add_conversation')
    fill_api_config_sign(api_config)
    response = build_request(api_config)

    cid = 0
    try:
        res = handle_response(api_config, response)
        cid = res.get('id')
    except Exception as e:
        err_list.append(str(e))

    if cid:
        # 知舟API-代码优化-发送消息
        api_config = api_list.get('api_chat_code_send_message')
        fill_api_config_sign(api_config)
        api_config['body']['conversation_id'] = cid
        response = build_request(api_config)
        try:
            handle_stream_response(response)
        except Exception as e:
            err_list.append(str(e))

    # # 知舟API-数学解题助手
    # api_config = api_list.get('api_chat_math_add_conversation')
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    #
    # cid = 0
    # try:
    #     res = handle_response(api_config, response)
    #     cid = res.get('id')
    # except Exception as e:
    #     err_list.append(str(e))
    #
    # if cid:
    #     # 知舟API-数学解题助手-发送消息
    #     api_config = api_list.get('api_chat_math_send_message')
    #     fill_api_config_sign(api_config)
    #     api_config['body']['conversation_id'] = cid
    #     response = build_request(api_config)
    #     try:
    #         handle_stream_response(response)
    #     except Exception as e:
    #         err_list.append(str(e))

    # 知舟API-长难句解析
    # api_config = api_list.get('api_en_sentence_add_conversation')
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    #
    # cid = 0
    # try:
    #     res = handle_response(api_config, response)
    #     cid = res.get('id')
    # except Exception as e:
    #     err_list.append(str(e))
    #
    # if cid:
    #     # 知舟API-发送消息
    #     api_config = api_list.get('api_en_sentence_analysis')
    #     fill_api_config_sign(api_config)
    #     api_config['body']['conversation_id'] = cid
    #     response = build_request(api_config)
    #     try:
    #         handle_stream_response(response)
    #     except Exception as e:
    #         err_list.append(str(e))

    # 知舟API-作文批改
    # api_config = api_list.get('api_en_article_analysis')
    # fill_api_config_sign(api_config, app_key='ak_bc1ea37f68a84d8c', secret_key='jbbhzq8kdu21wxn7qjp9nbgrattn2tny')
    # response = build_request(api_config)
    # try:
    #     handle_stream_response(response)
    # except Exception as e:
    #     err_list.append(str(e))

    # 知舟API-数学测试解读
    api_config = api_list.get('api_math_test_report')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_stream_response(response)
    except Exception as e:
        err_list.append(str(e))

    # 知舟API-高数截屏答疑
    api_config = api_list.get('api_gaoshu_screenshot_question')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_stream_response(response)
    except Exception as e:
        err_list.append(str(e))

    # 学情分析-提交
    api_config = api_list.get('api_learn_analysis_submit')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_response(api_config, response)  # 改为handle_response
    except Exception as e:
        err_list.append(str(e))

    # 学情分析-查看状态
    api_config = api_list.get('api_learn_analysis_status')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_response(api_config, response)  # 改为handle_response
    except Exception as e:
        err_list.append(str(e))

    # # 英语试卷解读-提交
    # api_config = api_list.get('api_english_paper_explain')
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))
    #
    # # 英语试卷解读-查看状态
    # api_config = api_list.get('api_english_paper_explain_status')
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))

    # 用户ID生成
    user_id = 'user_' + str(uuid.uuid4())

    # 英语阅读理解出题
    # api_config = api_list.get('api_english_reading_question')
    # api_config['body']['userinfo']['user_id'] = user_id
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))

    # 英语阅读理解再出题
    # api_config = api_list.get('api_english_reading_question_again')
    # api_config['body']['userinfo']['user_id'] = user_id
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))

    # 英语单词测试_出题
    # api_config = api_list.get('api_english_word_question')
    # api_config['body']['userinfo']['user_id'] = user_id
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))

    # 英语单词测试_提交测试
    # api_config = api_list.get('api_english_word_question_submit')
    # api_config['body']['userinfo']['user_id'] = user_id
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))

    # 英语单词测试_错题数量
    # api_config = api_list.get('api_english_word_wrong_question_count')
    # api_config['body']['userinfo']['user_id'] = user_id
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))

    # 英语单词测试_错题出题
    # api_config = api_list.get('api_english_word_wrong_question')
    # api_config['body']['userinfo']['user_id'] = user_id
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))

    # 英语单词测试_错题提交
    # api_config = api_list.get('api_english_word_wrong_question_submit')
    # api_config['body']['userinfo']['user_id'] = user_id
    # fill_api_config_sign(api_config)
    # response = build_request(api_config)
    # try:
    #     handle_response(api_config, response)  # 改为handle_response
    # except Exception as e:
    #     err_list.append(str(e))

    # 院校专业推荐
    api_config = api_list.get('api_college_analysis')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_stream_response(response)  # 改为handle_response
    except Exception as e:
        err_list.append(str(e))

    # 知舟API-学习规划
    api_config = api_list.get('api_study_planning')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_stream_response(response)
    except Exception as e:
        err_list.append(str(e))

    # 智痕雷达
    api_config = api_list.get('api_question_check')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_response(api_config, response)  # 改为handle_response
    except Exception as e:
        err_list.append(str(e))

    # 知舟API-知舟问答2.0
    api_config = api_list.get('api_chat2_add_conversation')
    fill_api_config_sign(api_config, app_key='ak_bc1ea37f68a84d8c', secret_key='jbbhzq8kdu21wxn7qjp9nbgrattn2tny')
    response = build_request(api_config)

    cid = 0
    try:
        res = handle_response(api_config, response)
        cid = res.get('id')
    except Exception as e:
        err_list.append(str(e))

    if cid:
        # 知舟API-知舟问答-发送消息
        api_config = api_list.get('api_chat2_send_message')
        fill_api_config_sign(api_config, app_key='ak_bc1ea37f68a84d8c', secret_key='jbbhzq8kdu21wxn7qjp9nbgrattn2tny')
        api_config['body']['conversation_id'] = cid
        response = build_request(api_config)
        try:
            handle_stream_response(response)
        except Exception as e:
            err_list.append(str(e))

    # 知识解析-基础版
    api_config = api_list.get('api_knowledge_analysis_basic')
    fill_api_config_sign(api_config, app_key='ak_bc1ea37f68a84d8c', secret_key='jbbhzq8kdu21wxn7qjp9nbgrattn2tny')
    response = build_request(api_config)
    try:
        handle_response(api_config, response)  # 改为handle_response
    except Exception as e:
        err_list.append(str(e))

    # 教务督学分析-提交
    api_config = api_list.get('api_user_study_plan_stat')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_response(api_config, response)  # 改为handle_response
    except Exception as e:
        err_list.append(str(e))

    # 教务督学分析-查看状态
    api_config = api_list.get('api_user_study_plan_stat_check')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_response(api_config, response)  # 改为handle_response
    except Exception as e:
        err_list.append(str(e))

    # 动手学-课后练习-学情分析
    api_config = api_list.get('api_dsx_learning_stat')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))

    # 动手学-学习指导
    api_config = api_list.get('api_dsx_learning_guide')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))

    return err_list


def run_shuati_api():
    err_list = []

    api_config = api_list.get('api_shuati_create_question')
    fill_api_config_sign(api_config)
    response = build_request(api_config)
    try:
        res = handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))
        return err_list

    paper_id = res.get('paper_id')
    question_list = res.get('question_list', [])
    if not paper_id or not question_list:
        return err_list

    answer_id = None
    is_finished = False
    for q in question_list:
        api_config = api_list.get('api_shuati_post_question')
        fill_api_config_sign(api_config)
        api_config['body']['paper_id'] = paper_id
        api_config['body']['question_id'] = q['question_id']
        response = build_request(api_config)
        try:
            res = handle_response(api_config, response)
            answer_id = res.get('answer_id')
            is_finished = res.get('is_finished', False)
            if is_finished:
                break
        except Exception as e:
            err_list.append(str(e))
            return err_list

    if not is_finished:
        return

    api_config = api_list.get('api_shuati_get_result')
    fill_api_config_sign(api_config)
    api_config['body']['answer_id'] = answer_id
    response = build_request(api_config)
    try:
        res = handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))
        return err_list

    api_config = api_list.get('api_shuati_get_report')
    fill_api_config_sign(api_config)
    api_config['body']['answer_id'] = answer_id
    response = build_request(api_config)
    try:
        res = handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))
        return err_list

    api_config = api_list.get('api_shuati_get_stage_change_info')
    fill_api_config_sign(api_config)
    api_config['body']['answer_id'] = answer_id
    response = build_request(api_config)
    try:
        res = handle_response(api_config, response)
    except Exception as e:
        err_list.append(str(e))
        return err_list


@shared_task(queue='message_task')
def auto_check_api_task():
    logger.info('auth_check_api_task begin...')
    err_list = run_pc_app()
    if err_list:
        err_msg = '\n'.join(err_list)
        send_dingtalk_message(err_msg)

    err_list = run_api()
    if err_list:
        err_msg = '\n'.join(err_list)
        send_dingtalk_message(err_msg)

    err_list = run_shuati_api()
    if err_list:
        err_msg = '\n'.join(err_list)
        send_dingtalk_message(err_msg)
