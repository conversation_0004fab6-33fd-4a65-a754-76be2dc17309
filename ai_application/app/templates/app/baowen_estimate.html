<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>【测试环境】爆文生成器_纯净版 Demo</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .page-title {
            width: 200px;
            margin: 20px auto;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }
        .container {
            display: flex;
            max-width: 1200px;
            min-height: 650px; /* 固定高度 */
            margin: 20px auto;
            background-color: #fff;
            {#box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);#}
        }
        .left-column, .right-column {
            padding: 20px;
            box-sizing: border-box;
        }
        .left-column {
            width: 40%;
            background-color: #f9f9f9;
            border-right: 1px solid #eaeaea;
        }
        .right-column {
            width: 60%;
            min-height: 650px; /* 固定高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            border-left: 1px solid #eaeaea; /* 为了美观，可以添加分隔线 */
        }
        .left-column h2 {
            color: #333;
        }
        .left-column label {
            display: block;
            margin-bottom: 5px;
            color: #666;
        }
        .left-column form {
            display: flex;
            flex-direction: column;
        }
        .check-form-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .check-form-group label {
            margin-right: 10px;
        }
        input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="number"]:focus, textarea:focus {
            border-color: #007bff;
            outline: none;
        }
        .left-column textarea {
            resize: vertical;
            height: 200px; /* 设置一个初始高度 */
        }
        /* 下拉框样式 */
        .left-column select {
            width: 100%; /* 宽度与输入框一致 */
            padding: 10px; /* 内边距 */
            margin-bottom: 20px; /* 与输入框相同的外边距 */
            border: 1px solid #ddd; /* 边框颜色 */
            border-radius: 4px; /* 边框圆角 */
            box-sizing: border-box; /* 盒模型 */
            background-color: white; /* 背景颜色 */
            color: #666; /* 文本颜色 */
            font-size: 16px; /* 字体大小 */
            cursor: pointer; /* 鼠标悬停时的光标样式 */
        }

        .left-column select:focus {
            border-color: #007bff; /* 聚焦时的边框颜色 */
            outline: none; /* 移除聚焦时的轮廓 */
        }

        /* 下拉框选项样式 */
        .left-column select option {
            padding: 5px; /* 选项的内边距 */
            background-color: #fff; /* 选项的背景颜色 */
            color: #333; /* 选项的文本颜色 */
        }

        .left-column select option:hover {
            background-color: #f0f0f0; /* 选项悬停时的背景颜色 */
        }
        .left-column button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .left-column button:hover {
            background-color: #0056b3;
        }
        #preview-content .result {
            margin: 0;
            font-size: 14px;
            white-space: pre-line;
        }
        #preview-content .usage {
            margin: 0;
            font-size: 14px;
        }
        /* 加载框样式 */
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>正在请求...</p>
        </div>
    </div>
    <div class="page-title">爆文生成器_纯净版 Demo</div>
    <div class="container">
        <div class="left-column">
            <form id="myForm" action="{% url 'prompt_estimate_submit' %}">
                <div class="form-group">
                    <label for="max_tokens">max_tokens:</label>
                    <input type="number" id="max_tokens" name="max_tokens" placeholder="Enter max_tokens here..." value="500">
                </div>
                <div class="form-group">
                    <label for="temperature">temperature:</label>
                    <input type="number" id="temperature" name="temperature" placeholder="Enter temperature here..."
                           value="0.3" step="0.1" min="0" max="0.9">
                </div>
                <div class="form-group">
                    <label for="template">选择提示词模板:</label>
                    <select id="template" name="template"></select>
                </div>
                <div class="form-group">
                    <label for="prompt">提示词:</label>
                    <textarea id="prompt" name="prompt" rows="3" required placeholder="Enter prompt here..."></textarea>
                </div>

                <div class="form-group">
                    <label for="query">query:</label>
                    <textarea id="query" name="query" rows="3" required placeholder="Enter query here..."></textarea>
                </div>

                <button type="submit">Submit</button>
            </form>
        </div>
        <div class="right-column">
            <h2>返回结果：</h2>
            <div id="preview-content">
                <div class="result"></div>
                <div class="usage"></div>
            </div>
        </div>
    </div>
    <script>
        $(function() {
            const templates = {{ templates | safe }}
            $('#template').html(templates.map(function(item) {
                return '<option value="' + item.id + '">' + item.name + '</option>'
            }).join(''))
            if (templates.length > 0) {
                $('#prompt').val(templates[0].content)
            }

            $('#template').on('change', () => {
                templates.map(function(item) {
                    if (item.id == $('#template').val()) {
                        $('#prompt').val(item.content)
                    }
                })
            })

            $('#myForm').submit(function(e) {
                e.preventDefault(); // 阻止表单的默认提交行为

                const formData = {
                    max_tokens: parseInt($('#max_tokens').val()),
                    temperature: parseFloat($('#temperature').val()),
                    template_id: parseInt($('#template').val()),
                    prompt: $('#prompt').val(),
                    query: $('#query').val(),
                };
                if (formData.max_tokens < 0 || formData.max_tokens > 1500) {
                    alert('max_tokens值错误【0-1500】')
                    return
                }
                if (formData.temperature < 0 || formData.temperature > 0.9) {
                    alert('temperature值错误【0-0.9】')
                    return
                }
                if (!formData.prompt) {
                    alert('提示词不能为空')
                    return
                }
                if (!formData.query) {
                    alert('query不能为空')
                    return
                }

                $('#loadingOverlay').css('display', 'flex')
                $.ajax({
                    type: 'POST',
                    url: $('#myForm').attr('action'), // 替换为你的服务器端点
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        // 处理响应数据
                        $('#preview-content').html(
                            '<div class="result">' + response.data.answer + '</div>' +
                            '<hr><div class="usage">' +
                            '<div>耗时：' + response.data.usage.latency + '秒</div>' +
                            '<div>请求token：' + response.data.usage.prompt_tokens + '</div>' +
                            '<div>响应token：' + response.data.usage.completion_tokens + '</div>' +
                            '<div>总token：' + response.data.usage.total_tokens + '</div>' +
                            '<div>字符长度：' + response.data.char_len + '</div>' +
                            '</div>'
                        );
                        $('#loadingOverlay').css('display', 'none')
                    },
                    error: function(xhr, status, error) {
                        // 处理错误情况
                        $('#preview-content').html('Error submitting form.');
                        $('#loadingOverlay').css('display', 'none')
                    }
                });

                console.log(formData)
            })

        })

    </script>
</body>
</html>