from celery import shared_task
from django.conf import settings

from app.api.dto import ChatMessageDto
from app.models import HotArticleSegment, PromptTemplate, Account, InvokeFrom
from app.services.app_generate_service import AppGenerateService


@shared_task(queue='hot_article')
def auto_gen_hot_article_viewpoint_task():
    # 暂时非正式环境不处理
    if settings.ENVIRONMENT != settings.ENV_PRODUCT:
        return

    segment_qs = HotArticleSegment.objects.filter(is_deleted=False, is_gen_viewpoint=False)[:10]

    template = PromptTemplate.objects.filter(app_no='content_extraction').first()
    if not template:
        return

    for segment in segment_qs:
        viewpoint = _gen_viewpoint(template, segment.content)
        if viewpoint:
            if '不予处理' in viewpoint:
                segment.is_deleted = True

            segment.is_gen_viewpoint = True
            segment.viewpoint = viewpoint
            segment.save()


def _gen_viewpoint(template: PromptTemplate, content_str: str):
    account = Account.objects.first()

    dto = ChatMessageDto(
        app_id='content_extraction',
        inputs={
            'prompt_template': template.id
        },
        query=content_str,
        stream=False,
    )

    response = AppGenerateService.generate(dto, account, invoke_from=InvokeFrom.console.value)
    return response.get('answer', '')
