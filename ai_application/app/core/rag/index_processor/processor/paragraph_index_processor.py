import uuid

from app.core.rag.cleaner.clean_processor import CleanProcessor
from app.core.rag.extractor.entities.extract_setting import ExtractSetting
from app.core.rag.extractor.extract_processor import ExtractProcessor
from app.core.rag.index_processor.index_processor_base import BaseIndexProcessor
from app.core.rag.models.document import Document
from app.core.rag.splitter.markdown_splitter import MarkdownHeaderTextSplitter
from app.core.rag.vdb.vector_factory import Vector
from app.models import Dataset, DatasetDocument
from app.utils import generate_text_hash


class ParagraphIndexProcessor(BaseIndexProcessor):

    def extract(self, extract_setting: ExtractSetting, **kwargs) -> list[Document]:
        text_docs = ExtractProcessor.extract(extract_setting)
        return text_docs

    def transform_special(self, documents: list[Document], **kwargs) -> list[Document]:
        # TODO pdf提取比较粗糙，分块时需要特殊处理
        document_no = kwargs.get('document_no', '')
        processing_rule = kwargs.get('process_rule')
        file_extension = kwargs.get('file_extension')

        # markdown文件预处理，按照标题分割文件
        if file_extension in ('md', 'markdown'):
            md_documents = []
            for d in documents:
                md_res = MarkdownHeaderTextSplitter(
                    headers_to_split_on=[
                        ("#", "h1"), ("##", "h2"), ("###", "h3"),
                    ],
                    strip_headers=False,
                    remove_images=processing_rule.get('remove_images', False),
                    remove_hyperlinks=processing_rule.get('remove_hyperlinks', False),
                ).split_text(d.page_content)
                md_documents.extend(md_res)
            documents = md_documents

        splitter = self._get_splitter(
            processing_rule=processing_rule,
            file_extension=file_extension
        )
        all_documents = []
        for document in documents:
            # document clean
            document.page_content = CleanProcessor.clean(document.page_content, kwargs.get('process_rule'))
            # parse document to nodes
            document_nodes = splitter.split_documents([document])
            split_documents = []
            for document_node in document_nodes:

                if document_node.page_content.strip():
                    document_node.metadata['document_no'] = document_no
                    document_node.metadata['doc_id'] = str(uuid.uuid4())
                    document_node.metadata['doc_hash'] = generate_text_hash(document_node.page_content)
                    # delete Spliter character
                    page_content = document_node.page_content
                    if page_content.startswith(".") or page_content.startswith("。"):
                        page_content = page_content[1:].strip()
                    if len(page_content) > 0:
                        document_node.page_content = page_content
                        split_documents.append(document_node)
            all_documents.extend(split_documents)
        return all_documents

    def transform(self, documents: list[Document], **kwargs) -> list[Document]:
        # TODO pdf提取比较粗糙，分块时需要特殊处理
        processing_rule = kwargs.get('process_rule')
        dataset_document: DatasetDocument = kwargs.get('dataset_document')

        # Split the text documents into nodes.
        document_no = dataset_document.document_no
        file_extension = dataset_document.file_extension

        # markdown文件预处理，按照标题分割文件
        if file_extension in ('md', 'markdown'):
            md_documents = []
            for d in documents:
                md_res = MarkdownHeaderTextSplitter(
                    headers_to_split_on=[
                        ("#", "h1"), ("##", "h2"), ("###", "h3"),
                    ],
                    strip_headers=False,
                    remove_images=processing_rule.get('remove_images', False),
                    remove_hyperlinks=processing_rule.get('remove_hyperlinks', False),
                ).split_text(d.page_content)
                md_documents.extend(md_res)
            documents = md_documents

        splitter = self._get_splitter(
            processing_rule=processing_rule,
            file_extension=file_extension
        )
        all_documents = []
        for document in documents:
            # document clean
            document.page_content = CleanProcessor.clean(document.page_content, kwargs.get('process_rule'))
            # parse document to nodes
            document_nodes = splitter.split_documents([document])
            split_documents = []
            for document_node in document_nodes:

                if document_node.page_content.strip():
                    document_node.metadata['document_no'] = document_no
                    document_node.metadata['doc_id'] = str(uuid.uuid4())
                    document_node.metadata['doc_hash'] = generate_text_hash(document_node.page_content)
                    # delete Spliter character
                    page_content = document_node.page_content
                    if page_content.startswith(".") or page_content.startswith("。"):
                        page_content = page_content[1:].strip()
                    if len(page_content) > 0:
                        document_node.page_content = page_content
                        split_documents.append(document_node)
            all_documents.extend(split_documents)
        return all_documents

    def load(self, dataset: Dataset, documents: list[Document]):
        vector = Vector(dataset)
        vector.create(documents)

    def clean(self, dataset: Dataset, node_ids: list[str] | None):
        vector = Vector(dataset)
        if node_ids:
            vector.delete_by_doc_ids(node_ids)
        else:
            vector.delete()
