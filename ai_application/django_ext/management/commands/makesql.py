import os

from django.conf import settings
from django.core.management import Base<PERSON>ommand
from django.db import connections, DEFAULT_DB_ALIAS
from django.db.migrations.loader import MigrationLoader


class Command(BaseCommand):
    help = "Creates new sql migration(s) for apps."

    def add_arguments(self, parser):
        parser.add_argument(
            'args', metavar='app_label', nargs='*',
            help='Specify the app label(s) to create migrations for.',
        )
        parser.add_argument(
            '--database', default=DEFAULT_DB_ALIAS,
            help='Nominates a database to create SQL for. Defaults to the "default" database.',
        )

    def handle(self, *app_labels, **options):
        connection = connections[options['database']]
        loader = MigrationLoader(connection, replace_migrations=False)

        if not app_labels:
            app_labels = settings.LOCAL_APPS

        for app_label in app_labels:
            if app_label not in settings.LOCAL_APPS:
                continue

            app_path = settings.BASE_DIR.joinpath(app_label)
            migrations_directory = app_path.joinpath('migrations')
            if not os.path.isdir(migrations_directory):
                continue

            migration_file_list = self.__get_dir_files(migrations_directory)
            if not migration_file_list:
                continue

            sql_directory = app_path.joinpath('sql')
            if not os.path.exists(sql_directory):
                os.mkdir(sql_directory)

            sql_file_list = os.listdir(sql_directory)
            sql_file_list.sort(key=lambda x: int(x.split('_')[0]))
            max_sql_seq = int(sql_file_list[-1].split('_')[0]) if sql_file_list else 0

            migration_file_list.sort(key=lambda x: int(x.split('_')[0]))
            for migration_file in migration_file_list:
                migration_name = migration_file[:-3]
                migration_seq = int(migration_file.split('_')[0])
                if migration_seq <= max_sql_seq:
                    continue

                migration = loader.get_migration_by_prefix(app_label, migration_name)
                target = (app_label, migration.name)
                # plan = [(loader.graph.nodes[target], options['backwards'])]
                plan = [(loader.graph.nodes[target], False)]
                sql_statements = loader.collect_sql(plan)

                sql_file = sql_directory.joinpath(f'{migration_name}.sql')
                with open(sql_file, 'w') as f:
                    f.write('\n'.join(sql_statements))

                self.stdout.write(f'{migration_name}.sql success')

    def __get_dir_files(self, directory):
        all_file_list = os.listdir(directory)

        file_list = []
        for _f in all_file_list:
            if _f.endswith('.py') and _f != '__init__.py':
                file_list.append(_f)
        return file_list
