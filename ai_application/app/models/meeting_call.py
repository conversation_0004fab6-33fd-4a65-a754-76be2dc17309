from django.db import models

from ._base import BaseModel


class MeetingVoiceRecord(BaseModel):
    origin = models.Char<PERSON>ield('文件来源', max_length=100, blank=True)
    origin_id = models.CharField('文件来源id', max_length=100, blank=True, db_index=True)

    msg_time = models.DateTimeField('时间', null=True)
    voice_file = models.CharField('音频文件url', max_length=100, blank=True)
    voice_content = models.TextField('音频内容', null=True)
    voice_duration = models.IntegerField('持续时长(秒)', default=0)
    is_convert_voice = models.BooleanField('是否转换音频文件', default=False)
    is_extract_question = models.BooleanField('是否提取问题', default=False)
    # NOT_START | ING | SUCCESS | FAIL
    extract_question_status = models.CharField('提取问题状态', max_length=100, default='NOT_START')
    extract_question_content = models.TextField('提取问题原始文本', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '音频文件表'
        db_table = 'ai_meeting_voice_record'


class MeetingQuestion(BaseModel):
    meeting_record = models.ForeignKey(MeetingVoiceRecord, on_delete=models.CASCADE, db_constraint=False)
    question_type = models.CharField('问题类型', max_length=100, blank=True)
    question_sub_type = models.CharField('问题子类型', max_length=100, blank=True)
    question_content = models.TextField('问题内容', null=True)
    student_features = models.TextField('学生提问', null=True)
    teacher_answer = models.TextField('咨询师回答内容', null=True)
    stage = models.TextField('所处阶段', null=True)
    purpose = models.TextField('咨询目的', null=True)
    analyse = models.TextField('分析', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '音频问题表'
        db_table = 'ai_meeting_question'
