import re

from app.api.dto import ChatMessageDto
from app.models.main_subject import SuperviseLearnStat
from api_client.yantucs_data.client import yantucs_data_client
from app.errors import AppNotFoundError
from app.models import App, Account
import json

from app.services.app_generate_service import AppGenerateService
from app.services.student_learn_stat.learn_stat_service import LearnStatService
from app.services.question_service import QuestionService
from django.db import connection
from django.forms.models import model_to_dict
import logging

from datetime import date

logger = logging.getLogger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, date):
            return obj.isoformat()
        return super().default(obj)


class SuperviseLearnStatusService:

    @classmethod
    def gen_analysis_report(cls, record: SuperviseLearnStat):
        processed_inputs = None  # 初始化为None
        logger.info(f"Starting analysis for record: {record.id}")
        try:
            logger.info("Getting account and app instances")
            from_account = Account.objects.first()
            # 获取看课对应的 App 实例
            watch_lessons_app: App = App.objects.filter(is_deleted=False, app_type='supervise_learn_stat').first()
            if not watch_lessons_app:
                raise AppNotFoundError("未找到看课对应的 App 实例")

            # 获取测试对应的 App 实例
            tests_app: App = App.objects.filter(is_deleted=False, app_type='supervise_learn_stat2').first()
            if not tests_app:
                raise AppNotFoundError("未找到测试对应的 App 实例")
            
            # 获取无学习计划看课对应的 App 实例
            no_plan_watch_lessons_app: App = App.objects.filter(is_deleted=False, app_type='supervise_learn_stat_no_plan').first()
            if not no_plan_watch_lessons_app:
                raise AppNotFoundError("未找到无学习计划看课对应的 App 实例")

            # 获取无学习计划测试对应的 App 实例
            no_plan_tests_app: App = App.objects.filter(is_deleted=False, app_type='supervise_learn_stat_no_plan2').first()
            if not no_plan_tests_app:
                raise AppNotFoundError("未找到无学习计划测试对应的 App 实例")

            logger.info(f"Calling yantucs_data_client.get_user_supervise_learn_stat")
            # Convert date objects to ISO format strings
            start_date_str = record.start_date.isoformat() if record.start_date else None
            end_date_str = record.end_date.isoformat() if record.end_date else None
            data1 = yantucs_data_client.get_user_supervise_learn_stat(
                record.user_id,
                record.course_id,
                record.subject_id,
                start_date_str,
                end_date_str
            )
            logger.info(f"Received data from yantucs_data_client: {data1}")
            # print("__"*5)
            # print(data1)

            has_plan = data1.get("has_plan", False)

            if has_plan:
                processed_inputs = cls.parse_learning_data(data1, record.subject_id)
                # print("______________________2______________", processed_inputs)
                
                # 分别获取看课和测试的数据
                watch_lessons_data = processed_inputs.get("看课", [])
                tests_data = processed_inputs.get("测试", [])
                # Convert list data to dictionary
                watch_lessons_inputs = {"看课": watch_lessons_data}
                tests_inputs = {"测试": tests_data}

                userinfo = getattr(record, 'userinfo', None)  # 安全获取属性
                record_dict = model_to_dict(record)

                # 创建看课模型的 ChatMessageDto 对象
                watch_lessons_chat_dto = ChatMessageDto(
                    app_id=watch_lessons_app.app_no,
                    query=json.dumps(record_dict, ensure_ascii=False, cls=DateTimeEncoder), 
                    stream=False,
                    inputs=watch_lessons_inputs,
                    userinfo=json.dumps(userinfo, ensure_ascii=False, cls=DateTimeEncoder) if userinfo else None,
                )

                # 创建测试模型的 ChatMessageDto 对象
                tests_chat_dto = ChatMessageDto(
                    app_id=tests_app.app_no,
                    query=json.dumps(record_dict, ensure_ascii=False, cls=DateTimeEncoder),
                    stream=False,
                    inputs=tests_inputs,
                    userinfo=json.dumps(userinfo, ensure_ascii=False, cls=DateTimeEncoder) if userinfo else None,
                )

                # 分别调用模型生成结果
                watch_lessons_res = AppGenerateService.generate(watch_lessons_chat_dto, from_account)
                tests_res = AppGenerateService.generate(tests_chat_dto, from_account)
                
                # 获取两个模型的答案
                watch_lessons_content = watch_lessons_res.get('answer', '')
                tests_content = tests_res.get('answer', '')
                
                watch_lessons_dict = cls.parse_content(watch_lessons_content)
                tests_dict = cls.parse_content(tests_content)
                
                # 合并结果
                # content = {
                #     "lessons_content": watch_lessons_dict.get('lessons_content', ''),
                #     "tests_content": tests_dict.get('tests_content', '')
                # }
                content = {
                    "lessons_content": watch_lessons_content,
                    "tests_content": tests_content
                }
            else:
                processed_inputs = cls.parse_learning_data_no_plan(data1, record.subject_id)
                # print("______________________2______________", processed_inputs)
                
                # 分别获取看课和测试的数据
                watch_lessons_data = processed_inputs.get("看课", [])
                tests_data = processed_inputs.get("测试", [])

                # Convert list data to dictionary
                watch_lessons_inputs = {"看课": watch_lessons_data}
                tests_inputs = {"测试": tests_data}

                userinfo = getattr(record, 'userinfo', None)  # 安全获取属性
                record_dict = model_to_dict(record)
                
                # 创建无学习计划看课模型的 ChatMessageDto 对象
                no_plan_watch_lessons_chat_dto = ChatMessageDto(
                    app_id=no_plan_watch_lessons_app.app_no,
                    query=json.dumps(record_dict, ensure_ascii=False, cls=DateTimeEncoder), 
                    stream=False,
                    inputs=watch_lessons_inputs,
                    userinfo=json.dumps(userinfo, ensure_ascii=False, cls=DateTimeEncoder) if userinfo else None,
                )

                # 创建无学习计划测试模型的 ChatMessageDto 对象
                no_plan_tests_chat_dto = ChatMessageDto(
                    app_id=no_plan_tests_app.app_no,
                    query=json.dumps(record_dict, ensure_ascii=False, cls=DateTimeEncoder),
                    stream=False,
                    inputs=tests_inputs,
                    userinfo=json.dumps(userinfo, ensure_ascii=False, cls=DateTimeEncoder) if userinfo else None,
                )

                # 分别调用模型生成结果
                no_plan_watch_lessons_res = AppGenerateService.generate(no_plan_watch_lessons_chat_dto, from_account)
                no_plan_tests_res = AppGenerateService.generate(no_plan_tests_chat_dto, from_account)
                
                # 获取两个模型的答案
                no_plan_watch_lessons_content = no_plan_watch_lessons_res.get('answer', '')
                no_plan_tests_content = no_plan_tests_res.get('answer', '')        

                no_plan_watch_lessons_dict = cls.parse_content(no_plan_watch_lessons_content)
                no_plan_tests_dict = cls.parse_content(no_plan_tests_content)

                # 合并结果
                content = {
                    "lessons_content": no_plan_watch_lessons_content,
                    "tests_content": no_plan_tests_content
                }
            result = {
                    'processed_inputs': processed_inputs,
                    'content': content,
                }
            if record:
                record.query = processed_inputs
                record.analysis = json.dumps(content, ensure_ascii=False)
                record.status = "SUCCESS"
                record.save()
            return result
        except Exception as e:
            logger.error(f"Error in gen_analysis_report for record {record.id}", exc_info=True)
            if record:
                record.query = processed_inputs if processed_inputs is not None else {}
                record.fail_reason = str(e)
                record.status = "FAIL"
                record.save()
            raise

    @classmethod
    def parse_content(cls, content_str):
        try:
            # 先打印原始内容以便调试
            print(f"Raw content to parse: {content_str}")
            
            # 更全面的引号替换处理
            content_str = content_str.replace("'", '"')
            # 移除可能的非法字符
            content_str = re.sub(r'[\x00-\x1f\x7f]', '', content_str)
            
            # 尝试解析
            content_dict = json.loads(content_str)
            print(f"Parsed content: {content_dict}")
            return content_dict
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            return {}

    @classmethod
    def parse_learning_data(cls, response_data, subject_id):
        ""
        """
        解析学习数据，提取计划生成时间和学习计划相关信息。

        :param response_data: 从 API 获取的原始响应数据
        :return: 包含中文键的解析后数据
        """

        data = response_data
        parsed_data = {
            "计划生成时间": data.get('plan_start_date'),
            "看课": [],
            "测试": []
        }

        plan_info_list = data.get('plan_info', {})
        for plan_info in plan_info_list:
            course_sections = plan_info.get('course_sections', [])

            for course_section in course_sections:
                course_section_name = course_section.get('course_section_name')
                course_section_type = cls._get_course_section_type(course_section.get('course_section_type'))
                # 转换为更简洁的课节类型表述
                if course_section_type == "录播":
                    course_section_type = "录播课"
                elif course_section_type == "直播":
                    course_section_type = "直播课"
                elif course_section_type == "测试":
                    course_section_type = "测试"
                end_date = plan_info.get('day')
                is_learned = "已学" if course_section.get('is_learned', False) else "未学"
                course_section_info = {
                    "课节名": course_section_name,
                    "类型": course_section_type,
                    "是否学": is_learned
                }

                if course_section.get('course_section_type') == 'video':
                    video_info = course_section.get('video_info', {}) or {}
                    # 将秒转换为分钟，保留两位小数
                    video_duration_minutes = round(video_info.get('duration', 0) / 3600, 2)
                    study_time_minutes = round(video_info.get('study_time', 0) / 3600, 2)
                    course_section_info.update({
                        "视频时长": f"{video_duration_minutes} h",
                        "看课时长": f"{study_time_minutes} h",
                        "看课次数": video_info.get('study_cnt', 0),
                        "截止时间": end_date,
                        "完成时间": video_info.get('finished_time', '').split(' ')[0],
                        "进度": f"{video_info.get('percent', '0')}%"
                    })
                    # test_list = course_section.get('test_list', [])
                    # if test_list:
                    #     first_test = test_list[0]
                    #     total_questions = int(first_test.get('question_mark_sum', 0))
                    #     total_score = int(first_test.get('score_sum', 0))
                    #     correct_rate = (total_score / total_questions * 100) if total_questions else 0
                    #     course_section_info.update({
                    #         "随堂测": [{
                    #             "试卷名称": first_test.get('paper_name', ''),
                    #             "总分": total_questions,
                    #             "得分": total_score,
                    #             "正确率": correct_rate,
                    #             "完成时间": first_test.get('answer_end_time', '').split(' ')[0],
                    #             "测试题目": cls._parse_questions(first_test.get('questions', []), subject_id)
                    #         }]
                    #     })
                    # else:
                    #     course_section_info.update({"随堂测": []})
                    parsed_data["看课"].append(course_section_info)
                elif course_section.get('course_section_type') == 'living':
                    living_info = course_section.get('living_info', {}) or {}
                    # 将秒转换为小时，保留两位小数
                    watch_living_time = round(living_info.get('watch_living_time', 0) / 3600, 2)
                    watch_record_time = round(living_info.get('watch_record_time', 0) / 3600, 2)
                    duration = round(living_info.get('living_duration', 0) / 3600, 2)

                    # 解析直播观看明细
                    watch_living_records = living_info.get('watch_living_records', [])
                    living_details = []
                    for record in watch_living_records:
                        watch_start_time = record.get('watch_start_time', '')
                        # 将秒转换为小时，保留两位小数
                        watch_duration = round(record.get('watch_duration', 0) / 3600, 2)
                        living_details.append({
                            "观看开始时间": watch_start_time,
                            "观看持续时间": f"{watch_duration} h"
                        })
                    
                    # 解析直播回放观看明细
                    watch_record_records = living_info.get('watch_record_records', [])
                    record_details = []
                    for record in watch_record_records:
                        watch_start_time = record.get('watch_start_time', '')
                        # 将秒转换为小时，保留两位小数
                        watch_duration = round(record.get('watch_duration', 0) / 3600, 2)
                        record_details.append({
                            "观看开始时间": watch_start_time,
                            "观看持续时间": f"{watch_duration} h"
                        })

                    course_section_info.update({
                        "直播开始时间": living_info.get('live_start_time', ''),
                        "直播时长": f"{duration} h",
                        "直播观看时长": f"{watch_living_time} h",
                        "直播观看明细": living_details,
                        "直播回放观看时长": f"{watch_record_time} h",
                        "直播回放观看明细": record_details
                    })
                    parsed_data["看课"].append(course_section_info)
                elif course_section.get('course_section_type') == 'test':
                    test_list = course_section.get('test_list', [])
                    if test_list:
                        first_test = test_list[0]
                        total_questions = int(first_test.get('question_mark_sum', 0))
                        total_score = int(first_test.get('score_sum', 0))
                        correct_rate = (total_score / total_questions * 100) if total_questions else 0
                        course_section_info.update({
                            "测试类型": cls._get_test_type(first_test.get('test_type')),
                            "试卷名称": first_test.get('paper_name', ''),
                            "总分": total_questions,
                            "得分": total_score,
                            "正确率": correct_rate,
                            "截止时间": end_date,
                            "完成时间": first_test.get('answer_end_time', '').split(' ')[0],
                            "测试题": cls._parse_questions(first_test.get('questions', []), subject_id)
                        })

                    parsed_data["测试"].append(course_section_info)

        return parsed_data
    
    @classmethod
    def parse_learning_data_no_plan(cls, response_data, subject_id):
        ""
        """
        解析无学习计划学习数据，提取相关信息。

        :param response_data: 从 API 获取的原始响应数据
        :return: 包含中文键的解析后数据
        """

        data = response_data
        parsed_data = {
            "看课": [],
            "测试": []
        }

        study_info_list = data.get('no_plan_info', [])
        for study_info in study_info_list:
            course_section_name = study_info.get('course_section_name')
            course_section_type = cls._get_course_section_type(study_info.get('course_section_type'))
            # 转换为更简洁的课节类型表述
            if course_section_type == "录播":
                course_section_type = "录播课"
            elif course_section_type == "直播":
                course_section_type = "直播课"
            elif course_section_type == "测试":
                course_section_type = "测试"
            is_learned = "已学" if study_info.get('is_learned', False) else "未学"
            course_section_info = {
                "课节名": course_section_name,
                "类型": course_section_type,
                "是否学": is_learned
            }

            if study_info.get('course_section_type') == 'video':
                video_info = study_info.get('video_info', {}) or {}
                # 将秒转换为小时，保留两位小数
                video_duration_minutes = round(video_info.get('duration', 0) / 3600, 2)
                study_time_minutes = round(video_info.get('study_time', 0) / 3600, 2)
                course_section_info.update({
                    "视频时长": f"{video_duration_minutes} h",
                    "看课时长": f"{study_time_minutes} h",
                    "看课次数": video_info.get('study_cnt', 0),
                    "完成时间": video_info.get('finished_time', '').split(' ')[0],
                    "进度": f"{video_info.get('percent', '0')}%"
                })
                parsed_data["看课"].append(course_section_info)
            elif study_info.get('course_section_type') == 'living':
                living_info = study_info.get('living_info', {}) or {}
                # 将秒转换为小时，保留两位小数
                watch_living_time = round(living_info.get('watch_living_time', 0) / 3600, 2)
                watch_record_time = round(living_info.get('watch_record_time', 0) / 3600, 2)
                duration = round(living_info.get('living_duration', 0) / 3600, 2)

                # 解析直播观看明细
                watch_living_records = living_info.get('watch_living_records', [])
                living_details = []
                for record in watch_living_records:
                    watch_start_time = record.get('watch_start_time', '')
                    # 将秒转换为小时，保留两位小数
                    watch_duration = round(record.get('watch_duration', 0) / 3600, 2)
                    living_details.append({
                        "观看开始时间": watch_start_time,
                        "观看持续时间": f"{watch_duration} h"
                    })

                # 解析直播回放观看明细
                watch_record_records = living_info.get('watch_record_records', [])
                record_details = []
                for record in watch_record_records:
                    watch_start_time = record.get('watch_start_time', '')
                    # 将秒转换为小时，保留两位小数
                    watch_duration = round(record.get('watch_duration', 0) / 3600, 2)
                    record_details.append({
                        "观看开始时间": watch_start_time,
                        "观看持续时间": f"{watch_duration} h"
                    })

                course_section_info.update({
                    "直播开始时间": living_info.get('live_start_time', ''),
                    "直播时长": f"{duration} h",
                    "直播观看时长": f"{watch_living_time} h",
                    "直播观看明细": living_details,
                    "直播回放观看时长": f"{watch_record_time} h",
                    "直播回放观看明细": record_details
                })
                parsed_data["看课"].append(course_section_info)
            elif study_info.get('course_section_type') == 'test':
                test_list = study_info.get('test_list', [])
                if test_list:
                    first_test = test_list[0]
                    total_questions = int(first_test.get('question_mark_sum', 0))
                    total_score = int(first_test.get('score_sum', 0))
                    correct_rate = (total_score / total_questions * 100) if total_questions else 0
                    course_section_info.update({
                        "测试类型": cls._get_test_type(first_test.get('test_type')),
                        "试卷名称": first_test.get('paper_name', ''),
                        "总分": total_questions,
                        "得分": total_score,
                        "正确率": correct_rate,
                        "完成时间": first_test.get('answer_end_time', '').split(' ')[0],
                        "测试题": cls._parse_questions(first_test.get('questions', []), subject_id)
                    })

                parsed_data["测试"].append(course_section_info)

        return parsed_data

    @classmethod
    def get_exam_info(cls, knowledge_point):
        """
        根据知识点名称查询 MySQL 数据库，获取考察次数和平均难度。

        :param knowledge_point: 知识点名称
        :return: 考察次数和平均难度，若未找到则返回 None, None
        """
        try:
            with connection.cursor() as cursor:
                # 执行 SQL 查询
                query = "SELECT exam_count, avg_difficulty FROM ai_exam_analysis_knowledge_point_with_stats WHERE point_name = %s AND subject_code_id = 1"
                cursor.execute(query, (knowledge_point,))

                # 获取查询结果
                result = cursor.fetchone()

            if result:
                exam_count, difficulty = result
                return exam_count, difficulty
            else:
                return None, None
        except Exception as e:
            print(f"数据库查询出错: {e}")
            return None, None

    @classmethod
    def _get_course_section_type(cls, course_section_type):
        """将英文课节类型转换为中文"""
        type_mapping = {
            "living": "直播",
            "test": "测试",
            "video": "录播"
        }
        return type_mapping.get(course_section_type, "未知类型")
    
    @classmethod
    def _get_test_type(cls, test_type):
        """将测试类型转换为中文"""
        type_mapping = {
            "clazz_test": "随堂测",
            "chapter_test": "章节测",
            "stage_test": "阶段测"
        }
        return type_mapping.get(test_type, "未知类型")

    @classmethod
    def _parse_questions(cls, questions, subject_id):
        """解析测试题目信息"""
        parsed_questions = []

        question_ids = []  # 问题intid
        for question in questions:
            question_ids.append(question['question_intid'])

        # 调用 QuestionService.get_question_by_ids 获取题目内容信息
        question_content_info_list = QuestionService.get_question_by_ids(question_ids)
        question_content_info_map = {info['q_id']: info for info in question_content_info_list}

        # 调用外部函数获取题目知识点
        question_info_list = LearnStatService.get_questions_kgs(question_ids, subject_id)
        # 将题目信息列表转换为以 question_id 为键的字典，方便查找
        question_info_map = {info['question_id']: info for info in question_info_list}

        for question in questions:
            question_id = question.get('question_intid', '')
            question_content_info = question_content_info_map.get(question_id, {})
            question_info = question_info_map.get(question_id, {})

            # 提取知识点名称
            knowledge_points = [kg['knowledge'] for kg in question_info.get('knowledge_list', [])]
            knowledge_str = ', '.join(knowledge_points)

            # 提取 title 和 choice_right_answer
            title = question_content_info.get('title', '未知')
            choice_right_answer = question_content_info.get('choice_right_answer', [])
            answer_str = ', '.join(choice_right_answer)

            knowledge_exam_info = []
            for knowledge_point in knowledge_points:
                exam_count, difficulty = cls.get_exam_info(knowledge_point)
                knowledge_exam_info.append({
                    # "知识点名称": knowledge_point,
                    "次数": exam_count,
                    "难度": difficulty
                })

            # 获取用户答案
            user_answer_list = question.get('answer_choices', [])
            # 去除 HTML 标签
            clean_user_answers = [re.sub(r'<[^>]*?>', '', answer) for answer in user_answer_list]
            user_answer_str = ', '.join(clean_user_answers)

            parsed_questions.append({
                # "题目id": question_id,
                "题干": title, 
                "正确答案": answer_str,
                "用户答案": user_answer_str, 
                "状态": cls._get_answer_status(question.get('answer_status')),
                "知识点": knowledge_str,
                "知识点考察情况": knowledge_exam_info
            })
        return parsed_questions

    @classmethod
    def _get_answer_status(cls, answer_status):
        """将英文答题状态转换为中文"""
        status_mapping = {
            "not_answered": "未作答",
            "right": "正确",
            "subjective": "主观题",
            "wrong": "错误"
        }
        return status_mapping.get(answer_status, "未知")
    
