from collections import defaultdict
from decimal import Decimal

from django.db import transaction
from django.utils import timezone

from app.api.api_dto.ai_practice import (
    AIPracticePaperSubmitDto, AIPracticePaperQuestionSubmitDto
)
from app.errors import ParameterError
from app.models import (
    STUserPaperAnswer, STUserPaper, STQuestion, STUserPaperQuestionAnswer
)
from app.services.shuati_app.answer_report import (
    gen_subjective_answer_report, batch_after_paper_submit, update_answer_stat
)
from app.services.shuati_app.values import question_score_rules
from app.tasks.ai_practice_task import delay_after_paper_submit, delay_gen_subjective_answer_report


class PaperSubmitClient:

    def __init__(self, paper: STUserPaper, is_debug: bool = False):
        self.paper = paper
        self.user_id = paper.user_id
        self.subject_id = paper.subject_id
        self.core_course_code = paper.core_course_code

        question_score_rules_map = defaultdict(lambda: defaultdict(int))
        for i in question_score_rules:
            question_score_rules_map[i.question_type][i.difficulty] = i.score
        self.question_score_rules_map = question_score_rules_map

        self.is_debug = is_debug

    def submit(self, dto: AIPracticePaperSubmitDto) -> STUserPaperAnswer:
        question_ids = self.paper.get_question_ids()
        if not question_ids:
            raise ParameterError(detail='试卷不存在!')

        if STUserPaperAnswer.objects.filter(paper=self.paper).exists():
            raise ParameterError(detail='答卷已提交')

        answered_question_map = {a.question_id: a for a in dto.answer_detail}

        if set(question_ids) - set(answered_question_map.keys()):
            raise ParameterError(detail='请答完全部题目')

        answered_question_ids = question_ids
        paper_answer = STUserPaperAnswer.objects.create(
            user_id=self.user_id,
            paper=self.paper,
            answered_question_ids=answered_question_ids,
            cycle_num=self.paper.cycle_num,
        )

        # 获取题目信息
        questions = STQuestion.objects.filter(id__in=question_ids)
        question_map = {q.id: q for q in questions}

        subjective_question_ids = []
        user_question_answer_objs = []
        answered_score = Decimal(0)
        for q_answer in dto.answer_detail:
            # 记录主观题问题ID列表
            if q_answer.subjective_answer:
                subjective_question_ids.append(q_answer.question_id)

            question = question_map.get(q_answer.question_id)
            answer_status, score_rate, answer_score = self._judge_user_answer(question, q_answer.choice_answer)

            subjective_answer_json = q_answer.subjective_answer.model_dump() if q_answer.subjective_answer else None
            user_question_answer_objs.append(STUserPaperQuestionAnswer(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                paper=self.paper,
                answer=paper_answer,
                question=question,
                answer_status=answer_status,
                choice_answer=q_answer.choice_answer,
                subjective_answer=subjective_answer_json,
                score_rate=score_rate,
                answer_score=answer_score,
                cycle_num=self.paper.cycle_num,
            ))

            answered_score += answer_score

        STUserPaperQuestionAnswer.objects.bulk_create(user_question_answer_objs)

        paper_answer.answered_score = round(answered_score, 1)
        self._submit_paper(paper_answer)

        if subjective_question_ids:
            # 有主观题时，需要在处理后再生成报告
            subjective_answers = STUserPaperQuestionAnswer.objects.filter(
                is_deleted=False, answer=paper_answer, question_id__in=subjective_question_ids)
            for sa in subjective_answers:
                gen_subjective_answer_report(sa)
        else:
            # 整体提交时，主观题生成报告后会立刻生成试卷报告
            update_answer_stat(paper_answer)
            batch_after_paper_submit(paper_answer)

        return paper_answer

    def submit_question(self, dto: AIPracticePaperQuestionSubmitDto) -> STUserPaperQuestionAnswer:
        question_ids = self.paper.get_question_ids()
        if not question_ids:
            raise ParameterError(detail='试卷不存在!')
        if dto.question_id not in question_ids:
            raise ParameterError(detail='该题不在试卷范围内')

        question: STQuestion = STQuestion.objects.filter(id=dto.question_id).first()
        if not question:
            raise ParameterError(detail='题目不存在')

        paper_answer, _ = STUserPaperAnswer.objects.get_or_create(
            paper=self.paper, user_id=dto.user_id,
            defaults={'cycle_num': self.paper.cycle_num}
        )

        # 处理问题已提交的情况
        question_answer = paper_answer.stuserpaperquestionanswer_set.filter(
            is_deleted=False, question_id=dto.question_id
        ).first()
        if question_answer:
            # raise ParameterError(detail='该题已回答')
            return question_answer

        answered_question_ids = paper_answer.get_answered_question_ids()
        answered_question_ids.append(dto.question_id)

        # 提交问题答案
        answer_status, score_rate, answer_score = self._judge_user_answer(question, dto.choice_answer)

        with transaction.atomic():
            subjective_answer_json = dto.subjective_answer.model_dump() if dto.subjective_answer else None
            question_answer = STUserPaperQuestionAnswer.objects.create(
                subject_id=self.subject_id,
                core_course_code=self.core_course_code,
                user_id=self.user_id,
                paper=self.paper,
                answer=paper_answer,
                question=question,
                answer_status=answer_status,
                choice_answer=dto.choice_answer,
                subjective_answer=subjective_answer_json,
                score_rate=score_rate,
                answer_score=answer_score,
                cycle_num=self.paper.cycle_num,
            )

            paper_answer.answered_question_ids = answered_question_ids
            answered_score = paper_answer.answered_score + answer_score
            paper_answer.answered_score = round(answered_score, 1)
            paper_answer.save(update_fields=['answered_question_ids', 'answered_score'])

        if question.question_type == 2:
            delay_gen_subjective_answer_report.delay(question_answer.id)

        # 判断是否是最后一题
        is_finished = paper_answer.check_is_finished()
        if is_finished:
            self._submit_paper(paper_answer)

            # 判断最后一题是不是主观题，是主观题需要等报告出完再出整套试卷
            is_last_subjective = question.question_type == STQuestion.Type.SUBJECTIVE
            if not is_last_subjective:
                update_answer_stat(paper_answer)
                delay_after_paper_submit.delay(paper_answer.id)
                # delay_after_paper_submit(paper_answer.id)

        return question_answer

    def _judge_user_answer(self, question, choice_answer):
        q_type = question.question_type
        if q_type in [0, 1]:
            q_d = question.difficulty
            question_score = self.question_score_rules_map[q_type][q_d]
            format_question_content = question.get_format_question_content()
            right_answer = format_question_content.choices_answer
            is_correct = set(right_answer) == set(choice_answer)
            answer_status = 'right' if is_correct else 'wrong'
            score_rate = 100 if is_correct else 0
            answer_score = question_score if is_correct else 0
            return answer_status, score_rate, Decimal(answer_score)
        else:
            # 处理主观题逻辑, -1 表示未生成分数
            return 'subjective', -1, Decimal(0)

    def _submit_paper(self, paper_answer: STUserPaperAnswer):
        with transaction.atomic():
            paper_answer.is_finished = True
            paper_answer.finished_time = timezone.now()
            paper_answer.save()

            self.paper.is_answered = True
            self.paper.save(update_fields=["is_answered"])


def format_user_desc_to_text(user_desc):

    lines = []
    for item in user_desc:
        line = f"知识点：{item.knowledge_name}，正确率：{item.accuracy}%"
        lines.append(line)
    return "\n".join(lines)
