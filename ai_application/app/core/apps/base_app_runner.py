import logging
from typing import Generator, cast

from app.constants.app import AppMode
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import ModelConfigEntity, ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.entities.queue_entities import (
    QueueLLMChunkEvent, QueueMessageEndEvent, QueueStopEvent
)
from app.core.features.context_answer_score.context_answer_score import ContextAnswerScore
from app.core.features.query_determine.query_determine import QueryDetermineFeature
from app.core.features.query_rewrite.query_rewrite import QueryRewriteFeature
from app.core.model_manager import ModelInstance
from app.core.model_runtime.entities.llm_entities import LLMResultChunk, LLMResult, LLMUsage, LLMResultChunkDelta
from app.core.model_runtime.entities.provider_entities import Model<PERSON>ropertyKey
from app.core.prompt.entities import PromptMessage, AssistantPromptMessage
from app.core.prompt.conversation_buffer_memory import Conversation<PERSON>ufferMemory
from app.core.prompt.simple_prompt_transform import SimplePromptTransform
from app.errors import ConversationTokenExceedError
from app.models import Message, Conversation

logger = logging.getLogger(__name__)


class AppRunner:

    def get_pre_calculate_rest_tokens(
            self,
            model_conf: ModelConfigEntity,
            prompt_messages: list[PromptMessage]
    ) -> int:
        model_instance = ModelInstance(
            provider_model_bundle=model_conf.provider_model_bundle,
            model=model_conf.model
        )

        max_input_size = model_conf.model_schema.model_properties.get(ModelPropertyKey.MAX_INPUT_SIZE)
        if max_input_size is None:
            logger.error("max_input_size is None")
            raise ConversationTokenExceedError()

        prompt_tokens = model_instance.get_llm_num_tokens(prompt_messages)

        rest_tokens = max_input_size - prompt_tokens
        if rest_tokens < 0:
            logger.error("prompt_tokens > max_input_size")
            raise ConversationTokenExceedError()

        return rest_tokens

    def check_conversation_token_exceed(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            conversation: Conversation,
            last_message: Message | None = None,
    ):
        memory = None
        if conversation and conversation.id:
            memory = ConversationBufferMemory(
                conversation=conversation,
                last_message=last_message,
                context_limit=0
            )

        prompt_messages = self.organize_prompt_messages(
            app_mode=AppMode.CHAT,
            query=application_generate_entity.query,
            pre_prompt=application_generate_entity.pre_prompt,
            memory=memory
        )

        self.get_pre_calculate_rest_tokens(application_generate_entity.model_conf, prompt_messages)

    def organize_prompt_messages(
            self,
            app_mode: AppMode,
            pre_prompt: str,
            query: str,
            files: list | None = None,
            memory: ConversationBufferMemory | None = None,
            query_in_prompt: bool = False
    ) -> list[PromptMessage]:
        return SimplePromptTransform().get_prompt(
            app_mode=app_mode,
            pre_prompt=pre_prompt,
            query=query,
            files=files,
            memory=memory,
            query_in_prompt=query_in_prompt,
        )

    def _handle_invoke_result(
        self,
        invoke_result: LLMResult | Generator,
        queue_manager: AppQueueManager,
        stream: bool,
    ) -> None:
        """
        :param invoke_result: invoke result
        :param queue_manager: application queue manager
        :param stream: stream
        :return:
        """
        if not stream:
            self._handle_invoke_result_direct(invoke_result=invoke_result, queue_manager=queue_manager)
        else:
            self._handle_invoke_result_stream(invoke_result=invoke_result, queue_manager=queue_manager)

    def _handle_invoke_result_direct(
            self,
            invoke_result: LLMResult,
            queue_manager: AppQueueManager,
    ):
        queue_manager.publish(
            QueueMessageEndEvent(
                llm_result=invoke_result,
            ),
        )

    def _handle_invoke_result_stream(
            self,
            invoke_result: Generator,
            queue_manager: AppQueueManager,
    ):
        model = None
        model_params = {}
        prompt_messages = []
        text = ''
        usage = None
        tool_calls_response = []
        for result in invoke_result:
            result = cast(LLMResultChunk, result)
            queue_manager.publish(
                QueueLLMChunkEvent(chunk=result)
            )

            text += result.delta.message.content

            if not model:
                model = result.model
            if not model_params:
                model_params = result.model_params

            if not prompt_messages:
                prompt_messages = result.prompt_messages

            if not tool_calls_response:
                tool_calls_response = result.delta.tool_calls_response

            if result.delta.usage:
                usage = result.delta.usage

            if queue_manager.is_stopped():
                queue_manager.publish(
                    QueueStopEvent(stopped_by=QueueStopEvent.StopBy.USER_MANUAL),
                )
                return

        if not usage:
            usage = LLMUsage.empty_usage()

        llm_result = LLMResult(
            model=model,
            model_params=model_params,
            prompt_messages=prompt_messages,
            message=AssistantPromptMessage(content=text),
            usage=usage,
            tool_calls_response=tool_calls_response,
            is_answer_token_exceed=LLMResult.judge_answer_token_exceed(model_params, usage)
        )
        self._publish_end_result(llm_result, queue_manager)

    def _publish_end_result(self, llm_result: LLMResult, queue_manager: AppQueueManager, with_chunk: bool = False):
        if with_chunk:
            queue_manager.publish(
                QueueLLMChunkEvent(
                    chunk=LLMResultChunk(
                        model=llm_result.model,
                        prompt_messages=llm_result.prompt_messages,
                        delta=LLMResultChunkDelta(index=0, message=llm_result.message)
                    )
                )
            )

        queue_manager.publish(
            QueueMessageEndEvent(llm_result=llm_result)
        )

    def _handle_invoke_full_result(
            self,
            model: str,
            prompt_messages: list[PromptMessage],
            answer: str,
            queue_manager: AppQueueManager,
            is_sensitive: bool = False
    ):
        assistant_message = AssistantPromptMessage(content=answer)
        llm_result = LLMResult(
            model=model,
            prompt_messages=prompt_messages,
            message=assistant_message,
            usage=LLMUsage.empty_usage(),
            is_sensitive=is_sensitive,
            is_success=not is_sensitive,
        )
        queue_manager.publish(
            QueueLLMChunkEvent(
                chunk=LLMResultChunk(
                    model=model,
                    prompt_messages=prompt_messages,
                    delta=LLMResultChunkDelta(index=0, message=assistant_message),
                    is_sensitive=is_sensitive
                )
            )
        )
        self._publish_end_result(llm_result, queue_manager)

    def _rewrite_query(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            message: Message,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        # TODO 校验token上限
        return QueryRewriteFeature().rewrite(
            application_generate_entity=application_generate_entity,
            message=message,
            query=query,
            tracing_log=tracing_log
        )

    def _determine_query(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        return QueryDetermineFeature().determine(
            application_generate_entity=application_generate_entity,
            query=query,
            tracing_log=tracing_log
        )

    def _get_context_answer_score(
            self,
            application_generate_entity: ChatAppGenerateEntity,
            context: str,
            question: str,
            answer: str,
            tracing_log: MessageTracingLog | None = None
    ) -> int:
        return ContextAnswerScore().get_score(
            application_generate_entity=application_generate_entity,
            context=context,
            question=question,
            answer=answer,
            tracing_log=tracing_log
        )
