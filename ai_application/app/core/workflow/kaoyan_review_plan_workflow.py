from app.api.college_analysis.internal_api import get_college_code
from app.api.college_analysis.target_college_information import get_target_college_information
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import CompletionAppGenerateEntity, ChatAppGenerateEntity
from app.core.features.kaoyan_review_plan import KaoYanReviewPlanFeature
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError
from app.models import Message, HollandTestResult, CollegeInfo, KaoYanStudentInfo, KaoYanCourse, KaoYanTeacherBook
import json

class KaoYanReviewPlanWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

    def run(self):

        inputs = self.application_generate_entity.inputs
        report_id = inputs.get('report_id')

        if not report_id:
            raise ParameterError(detail='缺少 report_id')

        target_college_level = inputs.get('target_college_level', '')
        start_review_time = inputs.get('start_review_time', '')
        final_start_review_time = f"开始复习时间为:{start_review_time}"
        stream = self.application_generate_entity.stream
        # 通过user_id筛选数据库获取用户基本信息
        student_info_record = KaoYanStudentInfo.objects.filter(report_id=report_id).first()
        # print(f"student_info_record:{student_info_record}")
        if not student_info_record:
            raise ParameterError(detail='未找到对应 user_id 的学生信息')

        student_info_dict = student_info_record.student_info
        print(student_info_dict)
        intensive_choice = student_info_record.intensive_choice
        steady_choice = student_info_record.steady_choice

        basic_info = student_info_dict.get('基本信息', {})
        academic_background = student_info_dict.get('成绩背景', {})
        major_ranking = academic_background.get('专业排名', '')
        gpa_range = academic_background.get('总GPA区间', '')
        ability_assessment = student_info_dict.get('能力评估', {})
        preparation_status = student_info_dict.get('备考状态', {})
        daily_study_time = preparation_status.get('日均学习时间', '')

        final_student_info = {
            'ability_assessment': ability_assessment,
            'basic_info': basic_info,
            'major_ranking': major_ranking,
            'gpa_range': gpa_range,
            'daily_study_time': daily_study_time,
            'start_review_time': final_start_review_time
        }
        # 打印或使用提取的信息
        # print(f"基本信息: {basic_info}")
        # print(f"专业排名: {major_ranking}")
        # print(f"总GPA区间: {gpa_range}")
        # print(f"日均学习时间: {daily_study_time}")
        # print(f"final_student_info:{final_student_info}")

        # 解析 intensive_choice, steady_choice, safety_choice
        def parse_choices(choices):
            parsed_choices = []
            if choices:
                if isinstance(choices, str):
                    try:
                        choices_list = json.loads(choices)
                    except json.JSONDecodeError:
                        raise ParameterError(detail=f'choices 字段格式错误: {choices}')
                elif isinstance(choices, list):
                    choices_list = choices
                else:
                    raise ParameterError(detail=f'choices 字段格式错误: {choices}')

                for choice in choices_list:
                    major_name_with_code = choice.get('专业名')
                    college_name_with_code = choice.get('院校名')

                    # 提取专业代码
                    major_code = major_name_with_code.split('(')[-1].strip(')')

                    # 提取院校代码
                    college_code = college_name_with_code.split('(')[-1].strip(')')

                    # 将结果添加到列表中
                    parsed_choices.append({
                        'major_code': major_code,
                        'college_code': college_code
                    })
            return parsed_choices


        if target_college_level == 'intensive':
            parsed_intensive_choices = parse_choices(intensive_choice)
            target_college_information = get_target_college_information(parsed_intensive_choices)

        else:
            parsed_steady_choices = parse_choices(steady_choice)
            target_college_information = get_target_college_information(parsed_steady_choices)

        # 增加考研课程信息和老师，考研书籍信息
        teacher_book_information = list(KaoYanTeacherBook.objects.all().values(
            'teacher_name',
            'book_name',
            'teacher_profile'
        ))

        course_information = list(KaoYanCourse.objects.values_list('name', flat=True))
        result = self._get_review_plan(final_student_info, target_college_information, course_information,
                                           teacher_book_information,stream)
        self._publish_workflow_end_message()

        return result

    def _get_review_plan(self, student_info: dict, target_college_information: dict, course_information: list,
                         teacher_book_information: list,stream:bool) -> LLMResult:
        student_info_str = json.dumps(student_info, ensure_ascii=False)
        target_college_information_str = json.dumps(target_college_information, ensure_ascii=False)
        course_information_str = json.dumps(course_information, ensure_ascii=False)
        teacher_book_information_str = json.dumps(teacher_book_information, ensure_ascii=False)
        print(f"target_college_information_str:{target_college_information_str}")
        self.kaoyan_review_plan_feature = KaoYanReviewPlanFeature(app_no='kaoyan_review_plan')
        result = self.kaoyan_review_plan_feature.run(
            self.application_generate_entity,
            stream=stream,
            query=student_info_str,
            target_college_information=target_college_information_str,
            course_information=course_information_str,
            teacher_book_information=teacher_book_information_str
        )

        tracing_info = {
            'tracing_type': 'kaoyan_review_plan',
            'query': student_info_str
        }

        return self._handle_node_generate_response(result, tracing_info=tracing_info)
