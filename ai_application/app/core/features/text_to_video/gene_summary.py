from PIL import Image, ImageDraw, ImageFont
import textwrap
import re
# 图像尺寸
final_width = 1280
final_height = 720

def convert_markdown_text(raw_text: str) -> str:
    """
    将带有转义换行符的 Markdown 文本（如 '\\n' 或 '\n'）转换为实际换行，
    同时保留标题、列表等格式，方便渲染阅读。
    """

    # 将 \n 或 \\n 都替换为真正的换行符
    text = raw_text.replace('\\n', '\n').replace('\n\n', '\n')

    # 清理 Markdown 中的多余空格
    text = re.sub(r' +\n', '\n', text)  # 行末空格清除
    text = re.sub(r'\n{2,}', '\n\n', text)  # 多余的空行只保留两个

    # 美化处理（可根据需要扩展）
    lines = text.splitlines()
    result_lines = []
    for line in lines:
        # 有序或无序列表前加缩进
        if line.strip().startswith(('-', '*')):
            result_lines.append('    ' + line.strip())
        else:
            result_lines.append(line)

    return '\n'.join(result_lines)

def summaryPage(section_title,knowledge_definition,save_path,nu):
    title = section_title
    definition = knowledge_definition

    # 加载思源黑体字体
    titlefont_path = "app/core/features/text_to_video/fonts/SourceHanSansSC-Bold.otf"
    textfont_path = "app/core/features/text_to_video/fonts/SourceHanSansSC-Regular.otf"
    title_font = ImageFont.truetype(titlefont_path, 60)
    text_font = ImageFont.truetype(textfont_path, 25)

    # 创建图像
    image = Image.new("RGB", (final_width, final_height), color=(255, 255, 255))
    draw = ImageDraw.Draw(image)

    # 绘制标题
    title_x = 100
    title_y = 200
    draw.text((title_x, title_y), title, font=title_font, fill=(0, 0, 0))

    # 计算正文行高
    bbox = text_font.getbbox("示例")
    line_height = bbox[3] - bbox[1] + 10
    start_y = title_y + 100  # 正文开始 y 坐标

    # # 绘制定义
    # define_y = start_y
    # draw.text((title_x+15, start_y), Define, font=text_font, fill=(0, 0, 255))

    # 保留手动换行，再 wrap 每行
    definition_lines = []
    for para in definition.split("\n"):
        wrapped = textwrap.wrap(para, width=40)
        definition_lines.extend(wrapped)

    # 绘制正文
    for line in definition_lines:
        start_y += line_height
        draw.text((title_x+15, start_y), line, font=text_font, fill=(0, 0, 0))

    # 加载 logo
    logo = Image.open(f"app/core/features/text_to_video/logo.jpg").convert("RGBA")
    logo_width = 150
    logo_height = int(logo.size[1] * (logo_width / logo.size[0]))
    logo = logo.resize((logo_width, logo_height))
    image.paste(logo, (20, 10), logo)

    # 保存图像
    image.save(f"{save_path}/scene{nu}.jpeg")
