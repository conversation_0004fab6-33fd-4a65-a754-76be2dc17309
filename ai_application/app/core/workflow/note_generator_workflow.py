# 阿纪
import re
from ai_application import settings
from api_client.ner.client import ner_client
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import  CompletionAppGenerateEntity
from app.core.features.note_generator import NoteGeneratorFeature
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError
from app.models import Message


class ChapterNoteWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

        self.chapter_note_generation_prompt = self.inputs.get('chapter_note_generation')
        self.chapter_note_generator = NoteGeneratorFeature(app_no='chapter_note_generation')
        self.not_lecture_err = '请先输入课件内容'
        self.exam_requirement_section = ''

    def _split_lecture_slides(self, lecture_slides: str) -> list:
        # 定义一个正则表达式模式，匹配二级标题以及它之后的所有非一级或二级标题的内容
        pattern = r'(##\s+.+?)(?=(?:\n#{1,2}\s)|\Z)'

        matches = re.findall(pattern, lecture_slides, flags=re.DOTALL)

        sections = []
        start_content = re.match(r'^(.*?)(?=##\s+|$)', lecture_slides, flags=re.DOTALL)
        if start_content and start_content.group(1).strip():
            pre_content = start_content.group(1).strip()
        else:
            pre_content = None

        # “考纲要求” 原样输出；“知识导图” 忽略
        for i, match in enumerate(matches):
            section = match.strip()
            if section.startswith('## 考纲要求'):
                self.exam_requirement_section = section
                continue
            if section.startswith('## 知识导图'):
                continue
            sections.append(section)

        if pre_content is not None and sections:
            sections[0] = pre_content + '\n\n' + sections[0]

        end_content = re.search(r'(##\s+.+?)(?!.*##\s+)', lecture_slides, flags=re.DOTALL)
        if end_content:
            remaining_text = lecture_slides[end_content.end():].strip()
            if remaining_text:
                sections[-1] += '\n' + remaining_text  # 将剩余文本附加到最后一个部分

        filtered_sections = []

        for section in sections:
            if section:
                filtered_sections.append(section)

        return filtered_sections

    def run(self):
        user_question = self.application_generate_entity.query

        if not user_question:
            raise ParameterError(detail=self.not_lecture_err)

        lecture_slides = self.application_generate_entity.inputs.get('lecture_slides', '')
        if not lecture_slides:
            raise ParameterError(detail=self.not_lecture_err)

        sections = self._split_lecture_slides(lecture_slides)

        all_results = []

        if self.exam_requirement_section:
            self._publish_workflow_custom_message(f'{self.exam_requirement_section}\n\n')

        for section in sections:
            result = self._chapter_note_generation(section)
            all_results.append(result)

        self._publish_workflow_end_message()

        return all_results

    def _chapter_note_generation(self, lecture_slides: str) -> LLMResult:
        result = self.chapter_note_generator.run(
            application_generate_entity=self.application_generate_entity,
            lecture_slides=lecture_slides,
            chapter_note='',
            subtitle='',
            stream=True,
            prompt_content=self.chapter_note_generation_prompt
        )

        tracing_info = {
            'tracing_type': 'chapter_note_generation',
            'query': lecture_slides
        }
        return self._handle_node_generate_response(result, tracing_info=tracing_info)


class LectureNoteWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

        self.lecture_note_generation_prompt = self.inputs.get('lecture_note_generation')
        self.lecture_note_generator = NoteGeneratorFeature(app_no='lecture_note_generation')
        self.chapter_note = self.inputs.get('chapter_note')
        self.not_subtitle_err = '请先输入字幕内容'

        print("完整的 inputs:", self.inputs)
    def run(self):
        subtitle_list = self.application_generate_entity.inputs.get('subtitle', [])
        chapter_note = self.inputs.get('chapter_note')
        subtitle_dict = self.inputs.get('subtitle_dict')

        if not subtitle_list:
            raise ParameterError(detail=self.not_subtitle_err)

        results = []  # 存储所有章节的笔记结果
        for subtitle in subtitle_list:
            title = subtitle.get('title', '').strip()
            content = subtitle.get('content', '').strip()

            print(f"需要匹配的标题是：{title}")
            matched_note = self._match_chapter_note(chapter_note, title)

            print(f"匹配到的笔记是：\n{matched_note}")
            if not matched_note:
                raise ParameterError(detail=f"未匹配到标题: {title}")

            # 生成笔记
            note_result = self._lecture_note_generation(matched_note, content)
            results.append(note_result)

        self._publish_workflow_end_message()

        # 拼接所有章节的结果并返回
        return results

    def _match_chapter_note(self, chapter_note: str, subtitle_title: str) -> str:
        subtitle_title_text = subtitle_title.lstrip('#').strip()

        # 提取所有标题及其内容
        pattern = re.compile(r"^\s*(#{1,6})\s*(.+)$", re.MULTILINE)
        matches = list(pattern.finditer(chapter_note))

        for i, match in enumerate(matches):
            title_level = len(match.group(1))
            title_text = match.group(2).strip()
            start_idx = match.end()

            end_idx = len(chapter_note)
            for j in range(i + 1, len(matches)):
                next_title_level = len(matches[j].group(1))
                if next_title_level == title_level:
                    end_idx = matches[j].start()
                    break

            content = chapter_note[start_idx:end_idx].strip()
            if title_text == subtitle_title_text:
                return f"{match.group(0)}\n{content}"

        return ""

    def _lecture_note_generation(self, chapter_note: str, subtitle_content: str) -> LLMResult:
        cleaned_subtitle_content = subtitle_content.replace('\n', ' ').strip()
        combined_content = f"{chapter_note}\n\n{cleaned_subtitle_content}"
        subtitle_dict = self.inputs.get('subtitle_dict')

        result = self.lecture_note_generator.run(
            application_generate_entity=self.application_generate_entity,
            chapter_note=chapter_note,
            subtitle=cleaned_subtitle_content,
            lecture_slides='',
            stream=True,
            prompt_content=self.lecture_note_generation_prompt,
            subtitle_dict=subtitle_dict
        )

        tracing_info = {
            'tracing_type': 'lecture_note_generation',
            'query': combined_content
        }
        return self._handle_node_generate_response(result, tracing_info=tracing_info)

