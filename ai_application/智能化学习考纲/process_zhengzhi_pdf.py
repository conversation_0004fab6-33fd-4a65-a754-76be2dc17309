import os
import django
import json
import logging

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_application.settings")
django.setup()

from django.conf import settings
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate

# 添加日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置文本大模型
llm_text = ChatOpenAI(
    openai_api_key=settings.DOUBAO_API_KEY,
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-seed-1-6-250615"
)


def process_json_content(json_file_path):
    """
    读取并处理JSON文件内容，传递给大模型生成结果
    
    Args:
        json_file_path: JSON文件路径
    
    Returns:
        str: 处理结果
    """
    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # 构造提示词模板
        template = """你是一个专业的考研政治大纲分析专家。请仔细分析以下考研政治考试大纲内容，
并按以下要求进行分析：
1. 深入分析各个科目的考试内容和要求
2. 对比各科目在试卷中的分值分布，分析其重要程度
3. 结合历年真题，对各个科目中的章节考点进行重要程度和难度区分
4. 用合适的标签（如：重要、核心、基础、了解等）标注各个考点
5. 对知识点或章节内容标注对应的考察题型
6. 以清晰的列表形式输出分析结果

以下是考研政治考试大纲内容：
{content}
"""

        prompt = PromptTemplate.from_template(template)
        llm_chain = prompt | llm_text

        # 将JSON数据转换为字符串传递给模型
        content_str = json.dumps(data, ensure_ascii=False, indent=2)
        
        # 调用大模型生成分析结果
        result = llm_chain.invoke({
            "content": content_str
        })

        return result
    except FileNotFoundError:
        logger.error(f"文件未找到: {json_file_path}")
        return None
    except json.JSONDecodeError:
        logger.error(f"JSON文件格式错误: {json_file_path}")
        return None
    except Exception as e:
        logger.error(f"处理文件时出错: {e}")
        return None



def save_result_as_markdown(result, filename):
    """
    将结果保存为Markdown文件

    Args:
        result: 模型生成的结果
        filename: 保存的文件名

    Returns:
        str: 保存的文件路径
    """
    # 确保文件以.md结尾
    if not filename.endswith('.md'):
        filename += '.md'

    # 提取结果内容
    if hasattr(result, 'content'):
        content = result.content
    else:
        content = str(result)

    # 保存到文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)

    return filename


if __name__ == "__main__":
    # 处理JSON文件内容并生成分析报告
    json_file_path = "zhengzhi_pdf.json"
    result = process_json_content(json_file_path)

    if result:
        # 保存结果为文件
        saved_file = save_result_as_markdown(result, "政治考试大纲分析报告.md")
        print(f"政治考试大纲分析报告已保存到: {saved_file}")

        # 打印结果内容
        content = result.content if hasattr(result, 'content') else str(result)
        print("生成的政治考试大纲分析报告内容:")
        print(content)
    else:
        print("生成政治考试大纲分析报告失败")