import json
import re
import time

from app.constants.app import AppMode
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.core.prompt.utils.prompt_message_util import PromptMessageUtil
from app.libs.baidu_nlp import <PERSON><PERSON><PERSON>lp
from app.libs.stopword_service import stopword_service
from app.models import PromptTemplate


class KnowledgeQuerySplitFeature(BaseFeature):

    def __init__(self):
        self.prompt_template = PromptTemplate.objects.filter(app_no='knowledge_query_split').first()
        if not self.prompt_template:
            raise Exception('prompt template not found')

    def run(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        return self.run_with_ner(query, tracing_log)

    def run_with_ner(
            self,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        start_time = time.perf_counter()
        # 放入空字符串，表示未知词性，先放入
        entities = BaiduNlp().lexer_analysis(query, ['n', 'nz', 'vn', 'v', ''])
        # 过滤一些停止词
        entities = stopword_service.filter_stopwords(entities)

        latency = time.perf_counter() - start_time

        if tracing_log:
            tracing_log.add_tracing(
                tracing_type='knowledge_query_split_ner',
                query=query,
                answer=json.dumps(entities, ensure_ascii=False),
                latency=latency,
            )

        return entities

    def run_with_llm(
            self,
            application_generate_entity: CompletionAppGenerateEntity,
            query: str,
            tracing_log: MessageTracingLog | None = None
    ):
        user_inputs = application_generate_entity.inputs.copy()

        pre_prompt = self.prompt_template.assemble_prompt(
            query=query,
            inputs=user_inputs,
            prompt_content=user_inputs.get('split_prompt')
        )

        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=self.prompt_template
        )

        invoke_result = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query='',
        )

        original_answer = invoke_result.message.content

        search_res = re.search(r'\{.*?\}', original_answer, re.DOTALL)
        json_string = search_res.group(0) if search_res else '{"data": []}'

        try:
            json_data = json.loads(json_string).get('data', [])
        except:
            json_data = []

        if tracing_log:
            log_content = PromptMessageUtil.prompt_messages_to_prompt_for_saving(invoke_result.prompt_messages)
            tracing_log.add_tracing(
                tracing_type='knowledge_query_split',
                query=query,
                content=log_content,
                answer=invoke_result.message.content,
                message_tokens=invoke_result.usage.prompt_tokens,
                answer_tokens=invoke_result.usage.completion_tokens,
                total_tokens=invoke_result.usage.total_tokens,
                latency=invoke_result.usage.latency,
                model_provider=model_conf.provider,
                model_id=model_conf.model,
                model_params=model_conf.model_params,
            )
            tracing_log.add_tracing(
                tracing_type='knowledge_query_split_json',
                query=original_answer,
                answer=json.dumps(json_data, ensure_ascii=False),
            )
        return json_data
