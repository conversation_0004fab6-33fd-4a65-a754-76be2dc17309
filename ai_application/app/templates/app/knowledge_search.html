<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>知识点搜索</title>
<script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
<style>
  body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
  }
  .search-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
  .search-box {
    width: 300px;
    padding: 10px;
    margin-right: 10px;
  }
  .search-button {
    padding: 10px 20px;
  }
  .container {
    display: flex;
    justify-content: space-between;
  }
  .results-container {
    width: 55%;
  }
  .result-list {
    list-style: none;
    padding: 0;
  }
  .result-item {
    margin-bottom: 20px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 10px;
  }
  .result-title {
    font-size: 18px;
    color: #333;
  }
  .result-definition {
    font-size: 16px;
    color: #444;
  }
  .result-summary {
    {#font-size: 16px;#}
    {#white-space: pre-line;#}
  }
  .deep-result-list {
    font-size: 16px;
    {#white-space: pre-line;#}
  }
  #searchDetails{
    white-space: pre-line;
  }
  #searchDetails p{
      border-bottom: 1px solid red;
      margin-bottom: 5px;
  }
  .search-path-container {
    width: 20%;
    padding-left: 20px;
  }
  .search-path-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
  }
  .search-keywords {
    margin-bottom: 10px;
  }
  .search-keyword {
    display: inline-block;
    margin-right: 5px;
    padding: 2px 5px;
    background-color: #f0f0f0;
    border-radius: 3px;
  }
  .prompt-button, .confirm_prompt {
    padding: 5px 10px;
  }
  .prompt_input {
    resize: vertical;
    height: 300px; /* 设置一个初始高度 */
    width: 600px;
  }
    /* 新增弹出层样式 */
  .promptManagementModal {
    display: none;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 20px;
    border: 1px solid #ccc;
    z-index: 1000;
  }
  /* 遮罩层样式 */
  #overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
  .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2); /* 半透明的白色背景 */
            z-index: 1000; /* 确保加载框在最上层 */
            justify-content: center;
            align-items: center;
        }
    .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff; /* 白色背景 */
            color: #333333; /* 深色文本 */
        }
</style>
</head>
<body>

<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <p>正在请求...</p>
    </div>
</div>

<div class="search-container">
  <input type="text" id="searchInput" class="search-box" placeholder="输入搜索内容">
  <button onclick="search()" class="search-button">搜索</button>
  <div style="padding-top: 8px; margin-left: 5px"><span id="queryCount">0</span> 字符</div>
</div>

<p><a href="{% url 'knowledge_list_2' %}" target="_blank">查看知识点</a></p>

<div class="container">
  <div class="search-path-container">

    <div class="form-group" style="margin-bottom: 20px">
        <label for="search_mode">搜索方式:</label>
        <select id="search_mode" name="search_mode">
            <option value="general">通用模式</option>
            <option value="deep">深度模式</option>
        </select>
    </div>

    <div class="form-group" style="margin-bottom: 20px">
        <label for="search_type">查询方式:</label>
        <select id="search_type" name="search_type">
            <option value="local">调用本地文档</option>
            <option value="llm">LLM直接回答</option>
        </select>
    </div>

    <div class="search-path-title">提示词管理：</div>
    <!-- 新增提示词管理按钮 -->

    <div>
        <button id="split_prompt_btn" class="prompt-button">拆分搜索内容__提示词</button>
        <div id="split_prompt_modal" class="promptManagementModal">
          <h2>拆分搜索内容提示词</h2>
            <textarea id="split_prompt" class="prompt_input" name="split_prompt">{{ knowledge_query_split }}</textarea><br>
            <button id="confirm_prompt_split" type="button">确定</button>
            <button id="cancel_prompt_split" type="button">取消</button>
        </div>
    </div>

    <div id="local_prompt_wrap" style="margin-top: 40px; display: none">
        <button id="local_prompt_btn" class="prompt-button">通用__本地文档搜索__提示词</button>
        <div id="local_prompt_modal" class="promptManagementModal">
          <h2>本地文档搜索提示词</h2>
            <textarea id="local_prompt" class="prompt_input" name="local_prompt">{{ knowledge_with_local }}</textarea><br>
            <button id="confirm_prompt_local" type="button">确定</button>
            <button id="cancel_prompt_local" type="button">取消</button>
        </div>
    </div>

  <div id="llm_prompt_wrap" style="margin-top: 40px; display: none">
    <button id="llm_prompt_btn" class="prompt-button">通用__LLM搜索__提示词</button>
    <div id="llm_prompt_modal" class="promptManagementModal">
      <h2>LLM搜索提示词</h2>
        <textarea id="llm_prompt" class="prompt_input" name="llm_prompt">{{ knowledge_with_llm }}</textarea><br>
        <button id="confirm_prompt_llm" type="button">确定</button>
        <button id="cancel_prompt_llm" type="button">取消</button>
    </div>
    </div>

    <div id="deep_local_prompt_wrap" style="margin-top: 40px; display: none">
        <button id="deep_local_prompt_btn" class="prompt-button">深度__本地文档搜索__提示词</button>
        <div id="deep_local_prompt_modal" class="promptManagementModal">
          <h2>深度本地文档搜索提示词</h2>
            <textarea id="deep_local_prompt" class="prompt_input" name="deep_local_prompt">{{ knowledge_deep_query_local }}</textarea><br>
            <button id="confirm_deep_prompt_local" type="button">确定</button>
            <button id="cancel_deep_prompt_local" type="button">取消</button>
        </div>
    </div>

  <div id="deep_llm_prompt_wrap" style="margin-top: 40px; display: none">
    <button id="deep_llm_prompt_btn" class="prompt-button">深度__LLM搜索__提示词</button>
    <div id="deep_llm_prompt_modal" class="promptManagementModal">
      <h2>深度LLM搜索提示词</h2>
        <textarea id="deep_llm_prompt" class="prompt_input" name="deep_llm_prompt">{{ knowledge_deep_query_llm }}</textarea><br>
        <button id="confirm_deep_prompt_llm" type="button">确定</button>
        <button id="cancel_deep_prompt_llm" type="button">取消</button>
    </div>
    </div>

  <div id="deep_question_prompt_wrap" style="margin-top: 40px; display: none">
    <button id="deep_question_prompt_btn" class="prompt-button">深度__检索到题目__提示词</button>
    <div id="deep_question_prompt_modal" class="promptManagementModal">
      <h2>深度-检索到题目提示词</h2>
        <textarea id="deep_question_prompt" class="prompt_input" name="deep_question_prompt">{{ knowledge_deep_question }}</textarea><br>
        <button id="confirm_deep_question_prompt" type="button">确定</button>
        <button id="cancel_deep_question_prompt" type="button">取消</button>
    </div>
    </div>

    <div id="deep_no_question_prompt_wrap" style="margin-top: 40px; display: none">
    <button id="deep_no_question_prompt_btn" class="prompt-button">深度__未检索到题目__提示词</button>
    <div id="deep_no_question_prompt_modal" class="promptManagementModal">
      <h2>深度-未检索到题目提示词</h2>
        <textarea id="deep_no_question_prompt" class="prompt_input" name="deep_no_question_prompt">{{ knowledge_deep_no_question }}</textarea><br>
        <button id="confirm_deep_no_question_prompt" type="button">确定</button>
        <button id="cancel_deep_no_question_prompt" type="button">取消</button>
    </div>
    </div>

  </div>

  <div class="results-container">
    <div class="search-path-title">搜索结果：</div>
    <div class="result-list" id="resultList">
      <!-- 搜索结果将在这里显示 -->
    </div>
    <div class="deep-result-list markdown-body" id="deep-result-list">
      <!-- 搜索结果将在这里显示 -->
    </div>
  </div>
  <div class="search-path-container">
    <div class="search-path-title">搜索过程：</div>
    <div class="search-keywords" id="searchKeywords">
      <!-- 关键词拆分将在这里显示 -->
    </div>
    <div id="searchDetails">
      <!-- 搜索细节将在这里显示 -->
    </div>
  </div>
</div>
<!-- 新增遮罩层 -->
<div id="overlay"></div>

<script>
let temp_split_prompt = $('#split_prompt').val();
let temp_local_prompt = $('#local_prompt').val();
let temp_llm_prompt = $('#llm_prompt').val();
let temp_deep_local_prompt = $('#deep_local_prompt').val();
let temp_deep_llm_prompt = $('#deep_llm_prompt').val();
let temp_deep_question_prompt = $('#deep_question_prompt').val();
let temp_deep_no_question_prompt = $('#deep_no_question_prompt').val();
let message_id = null;

function search() {
    const search_mode = $('#search_mode').val()
    if (search_mode === 'deep') {
        return deep_search()
    }

    const input = $('#searchInput').val();
    if (!input) {
        alert('请输入搜索内容');
        return;
    }

    const search_type = $('#search_type').val();
    const local_prompt = $('#local_prompt').val();
    const llm_prompt = $('#llm_prompt').val();
    const split_prompt = $('#split_prompt').val();

    const resultList = document.getElementById('resultList');
    const deepResultList = document.getElementById('deep-result-list');
    resultList.innerHTML = ''; // 清空之前的搜索结果
    deepResultList.innerHTML = ''; // 清空之前的搜索结果

    const searchKeywords = document.getElementById('searchKeywords');
    searchKeywords.innerHTML = ''; // 清空之前的关键词拆分

    const searchDetails = document.getElementById('searchDetails');
    searchDetails.innerHTML = ''; // 清空之前的搜索细节

    $('#loadingOverlay').css('display', 'flex');

    message_id = null;
    const url = "{% url 'knowledge_search_result_2' %}";
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: input,
            search_mode,
            search_type,
            split_prompt,
            local_prompt,
            llm_prompt,
        })
    })
      .then(response => {
          $('#loadingOverlay').css('display', 'none');
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.body;
      })
      .then(stream => {
          $('#loadingOverlay').css('display', 'none');
        let streamComplete = false;
        const reader = stream.getReader();
        const decoder = new TextDecoder(); // 创建TextDecoder实例
          let result_text = '';
        function readChunk() {
          reader.read().then(({ done, value }) => {
            if (done) {
              streamComplete = true;
              console.log('Stream complete, returning collected data');
              find_tracing()
              return;
            }
            const textChunk = decoder.decode(value, { stream: true });

            if (textChunk.startsWith("data: ")) {
                // 去除前缀"data: "
                const json_str = textChunk.slice(6);
                console.log('json_str', json_str)
                try {
                    // 将匹配到的字符串转换为JSON对象
                    const jsonData = JSON.parse(json_str);
                    if (!message_id) {
                        message_id = jsonData.message_id
                    }
                    if (jsonData.answer) {
                        result_text += jsonData.answer;
                        deepResultList.innerHTML = marked.parse(result_text);
                    }
                } catch (error) {
                    console.error('Error parsing JSON', json_str, error);
                }
            }
            readChunk();
          }).catch(error => {
            console.error('Error collecting stream data', error);
          });
        }
        readChunk();
      })
      .catch(error => {
          $('#loadingOverlay').css('display', 'none');
          console.error('Error:', error);
      });
}

function find_tracing() {
    if (!message_id) return
    $.ajax({
        type: 'get',
        url: "{% url 'message_tracing' %}",
        contentType: 'application/json',
        data: JSON.stringify({
            message_id,
        }),
        success: function(response) {
            $('#loadingOverlay').css('display', 'none');
            const results = response.data;

            results.tracing.forEach(function(traceDetail) {
                const detailElement = document.createElement('p');
                detailElement.textContent = traceDetail;
                searchDetails.appendChild(detailElement);
            });
        },
        error: function(xhr, status, error) {
            alert('请求失败');
            $('#loadingOverlay').css('display', 'none');
        }
    });

}

function deep_search() {
    const search_mode = $('#search_mode').val()

    const input = $('#searchInput').val();
    if (!input) {
        alert('请输入搜索内容');
        return;
    }

    const search_type = $('#search_type').val();
    const deep_local_prompt = $('#deep_local_prompt').val();
    const deep_llm_prompt = $('#deep_llm_prompt').val();
    const deep_question_prompt = $('#deep_question_prompt').val();
    const deep_no_question_prompt = $('#deep_no_question_prompt').val();

    const resultList = document.getElementById('resultList');
    const deepResultList = document.getElementById('deep-result-list');
    resultList.innerHTML = ''; // 清空之前的搜索结果
    deepResultList.innerHTML = ''; // 清空之前的搜索结果

    const searchKeywords = document.getElementById('searchKeywords');
    searchKeywords.innerHTML = ''; // 清空之前的关键词拆分

    const searchDetails = document.getElementById('searchDetails');
    searchDetails.innerHTML = ''; // 清空之前的搜索细节


    $('#loadingOverlay').css('display', 'flex');

    message_id = null;
    const url = "{% url 'knowledge_deep_search_result_2' %}"
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: input,
            search_mode,
            search_type,
            deep_local_prompt,
            deep_llm_prompt,
            deep_question_prompt,
            deep_no_question_prompt,
        })
    })
      .then(response => {
          $('#loadingOverlay').css('display', 'none');
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.body;
      })
      .then(stream => {
          $('#loadingOverlay').css('display', 'none');
        let streamComplete = false;
        const reader = stream.getReader();
        const decoder = new TextDecoder(); // 创建TextDecoder实例
          let result_text = '';
        function readChunk() {
          reader.read().then(({ done, value }) => {
            if (done) {
              streamComplete = true;
              console.log('Stream complete, returning collected data');
              find_tracing()
              return;
            }
            const textChunk = decoder.decode(value, { stream: true });

            if (textChunk.startsWith("data: ")) {
                // 去除前缀"data: "
                const json_str = textChunk.slice(6);
                try {
                    // 将匹配到的字符串转换为JSON对象
                    const jsonData = JSON.parse(json_str);
                    if (!message_id) {
                        message_id = jsonData.message_id
                    }
                    if (jsonData.answer) {
                        result_text += jsonData.answer;
                        deepResultList.innerHTML = marked.parse(result_text);
                    }
                } catch (error) {
                    console.error('Error parsing JSON', json_str, error);
                }
            }
            readChunk();
          }).catch(error => {
            console.error('Error collecting stream data', error);
          });
        }
        readChunk();
      })
      .catch(error => {
          $('#loadingOverlay').css('display', 'none');
        console.error('Error:', error);
      });

    /*const ajaxRequest = $.ajax({
        url: "",
        type: 'POST',
        dataType: 'text/event-stream', // 设置为服务器发送事件
        cache: false, // 禁用缓存
        contentType: 'application/json',
        data: JSON.stringify({
            query: input,
            search_mode,
            search_type,
            deep_local_prompt,
            deep_llm_prompt,
            deep_question_prompt,
            deep_no_question_prompt,
        }),
    });
    // 处理服务器发送的每条数据
    ajaxRequest.on('message', function(event) {
        const data = event.data; // 获取服务器发送的数据
        console.log(data)
        // 处理数据，例如将其添加到DOM中
        {#$('#data').append(data + '<br>');#}
    });

    // 处理错误
    ajaxRequest.fail(function(jqXHR, textStatus) {
        console.error('Stream error:', textStatus);
        alert('Stream error:' + textStatus)
    });

    // 处理完成
    ajaxRequest.always(function() {
        console.log('Stream completed');
    });

    $.ajax({
        type: 'POST',
        url: "",
        contentType: 'application/json',
        data: JSON.stringify({
            query: input,
            search_mode,
            search_type,
            deep_local_prompt,
            deep_llm_prompt,
            deep_question_prompt,
            deep_no_question_prompt,
        }),
        success: function(response) {
            $('#loadingOverlay').css('display', 'none');
            const results = response.data;

            if (!results.is_success) {
                const resultItem = document.createElement('div');
                resultItem.className = 'result-item';
                resultItem.innerHTML = results.reason;
                resultList.appendChild(resultItem);
                return;
            }

            results.results.forEach(function(result) {
                const resultItem = document.createElement('div');
                resultItem.className = 'result-item';
                resultItem.innerHTML = `
                  <div class="result-title">${result.name}</div>
                  <div class="result-definition">定义：${result.definition}</div>
                  <div class="result-summary">详情：${result.detail}</div>
                  <div class="result-questions">问题：${result.questions}</div>
                `;
                resultList.appendChild(resultItem);
            });

            // 展示关键词
            results.tracing.forEach(function(traceDetail) {
                const detailElement = document.createElement('p');
                detailElement.textContent = traceDetail;
                searchDetails.appendChild(detailElement);
            });
        },
        error: function(xhr, status, error) {
            alert('请求失败');
            $('#loadingOverlay').css('display', 'none');
        }
    });*/
}


$(function () {
    function togglePrompt() {
        const search_mode = $('#search_mode').val()
        const search_type = $('#search_type').val()

        if (search_mode === 'general'){
            if (search_type === 'local') {
                $('#local_prompt_wrap').show()
                $('#llm_prompt_wrap').show()
                $('#deep_local_prompt_wrap').hide()
                $('#deep_llm_prompt_wrap').hide()
                $('#deep_question_prompt_wrap').hide()
                $('#deep_no_question_prompt_wrap').hide()
            } else if (search_type === 'llm') {
                $('#local_prompt_wrap').hide()
                $('#llm_prompt_wrap').show()
                $('#deep_local_prompt_wrap').hide()
                $('#deep_llm_prompt_wrap').hide()
                $('#deep_question_prompt_wrap').hide()
                $('#deep_no_question_prompt_wrap').hide()
            }

        } else if (search_mode === 'deep'){
            if (search_type === 'local') {
                $('#local_prompt_wrap').hide()
                $('#llm_prompt_wrap').hide()
                $('#deep_local_prompt_wrap').show()
                $('#deep_llm_prompt_wrap').show()
                $('#deep_question_prompt_wrap').show()
                $('#deep_no_question_prompt_wrap').show()
            } else if (search_type === 'llm') {
                $('#local_prompt_wrap').hide()
                $('#llm_prompt_wrap').hide()
                $('#deep_local_prompt_wrap').hide()
                $('#deep_llm_prompt_wrap').show()
                $('#deep_question_prompt_wrap').show()
                $('#deep_no_question_prompt_wrap').show()
            }
        }
    }

    $('#search_mode').on('change', togglePrompt)
    $('#search_type').on('change', togglePrompt)

    togglePrompt()

    function updateCharCount() {
        $('#queryCount').text($('#searchInput').val().length);
    }

    // 绑定 input 事件到 textarea
    $('#searchInput').on('input', updateCharCount);
    // 初始调用以设置初始计数
    updateCharCount();

    $('#searchInput').on('keydown', function(e){
        // 检查按下的键是否是回车键
        if (e.which === 13) {
            e.preventDefault();
            search();
        }
    });

    $('#split_prompt_btn').click(function() {
        $('#split_prompt_modal').show();
        $('#overlay').show();
        $('#split_prompt').val(temp_split_prompt)
    });

    $('#local_prompt_btn').click(function() {
        $('#local_prompt_modal').show();
        $('#overlay').show();
        $('#local_prompt').val(temp_local_prompt)
    });
    $('#llm_prompt_btn').click(function() {
        $('#llm_prompt_modal').show();
        $('#overlay').show();
        $('#llm_prompt').val(temp_llm_prompt)
    });

    $('#deep_local_prompt_btn').click(function() {
        $('#deep_local_prompt_modal').show();
        $('#overlay').show();
        $('#deep_local_prompt').val(temp_deep_local_prompt)
    });
    $('#deep_llm_prompt_btn').click(function() {
        $('#deep_llm_prompt_modal').show();
        $('#overlay').show();
        $('#deep_llm_prompt').val(temp_deep_llm_prompt)
    });

    $('#deep_question_prompt_btn').click(function() {
        $('#deep_question_prompt_modal').show();
        $('#overlay').show();
        $('#deep_question_prompt').val(temp_deep_question_prompt)
    });
    $('#deep_no_question_prompt_btn').click(function() {
        $('#deep_no_question_prompt_modal').show();
        $('#overlay').show();
        $('#deep_no_question_prompt').val(temp_deep_no_question_prompt)
    });

    $('#confirm_prompt_split').click(function() {
        temp_split_prompt = $('#split_prompt').val()
        $('#split_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_prompt_split').click(function() {
        $('#split_prompt_modal').hide();
        $('#overlay').hide();
    });

    $('#confirm_prompt_local').click(function() {
        temp_local_prompt = $('#local_prompt').val()
        $('#local_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_prompt_local').click(function() {
        $('#local_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#confirm_prompt_llm').click(function() {
        temp_llm_prompt = $('#llm_prompt').val()
        $('#llm_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_prompt_llm').click(function() {
        $('#llm_prompt_modal').hide();
        $('#overlay').hide();
    });

    $('#confirm_deep_prompt_local').click(function() {
        temp_deep_local_prompt= $('#deep_local_prompt').val()
        $('#deep_local_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_deep_prompt_local').click(function() {
        $('#deep_local_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#confirm_deep_prompt_llm').click(function() {
        temp_deep_llm_prompt = $('#deep_llm_prompt').val()
        $('#deep_llm_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_deep_prompt_llm').click(function() {
        $('#deep_llm_prompt_modal').hide();
        $('#overlay').hide();
    });

    $('#confirm_deep_question_prompt').click(function() {
        temp_deep_question_prompt= $('#deep_question_prompt').val()
        $('#deep_question_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_deep_question_prompt').click(function() {
        $('#deep_question_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#confirm_deep_no_question_prompt').click(function() {
        temp_deep_no_question_prompt = $('#deep_no_question_prompt').val()
        $('#deep_no_question_prompt_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_deep_no_question_prompt').click(function() {
        $('#deep_no_question_prompt_modal').hide();
        $('#overlay').hide();
    });

})
</script>

</body>
</html>
