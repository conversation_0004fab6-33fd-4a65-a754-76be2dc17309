# 阿纪
from app.api.college_analysis.huolande_test import huolande_test
from app.api.college_analysis.internal_api import get_college_code
from app.core.apps.base_app_queue_manager import AppQueueManager
from app.core.entities.app_entities import CompletionAppGenerateEntity, ChatAppGenerateEntity
from app.core.features.college_analysis import CollegeAnalysisFeature
from app.core.features.kaoyan_review_plan import Ka<PERSON>YanReviewPlanFeature
from app.core.model_runtime.entities.llm_entities import LLMResult
from app.core.workflow.base_workflow import BaseWorkflow
from app.errors import ParameterError
from app.models import Message, HollandTestResult, CollegeInfo, KaoYanStudentInfo
import json
import re

class CollegeAnalysisWorkflow(BaseWorkflow):

    def __init__(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            message: Message,
            queue_manager: AppQueueManager
    ):
        super().__init__(application_generate_entity, message, queue_manager)

    def run(self):
        student_info = self.application_generate_entity.query
        inputs = self.application_generate_entity.inputs
        stream = self.application_generate_entity.stream
        report_id = inputs.get('report_id')

        if not report_id:
            raise ParameterError(detail='缺少 report_id')

        target_colleges = inputs.get('target_colleges', [])
        holland_test_result = inputs.get('holland_test_result', '')
        college_result = inputs.get('college_result', '')

        # 获取霍兰德测试信息
        holland_test_record = HollandTestResult.objects.filter(genre=holland_test_result).first()
        holland_test_data = holland_test_record.result if holland_test_record else '无'

        # 获取目标专业建议
        combined_major_info = []
        for college in target_colleges:
            major_name = college.get('major_name', '')
            if major_name:
                major_info = CollegeInfo.objects.filter(secondary_major=major_name)
                for info in major_info:
                    combined_major_info.append({
                        'discipline_category_code': info.discipline_category_code,
                        'discipline_category': info.discipline_category,
                        'primary_major_code': info.primary_major_code,
                        'primary_major': info.primary_major,
                        'secondary_major_code': info.secondary_major_code,
                        'secondary_major': info.secondary_major,
                        'suitable_population': info.suitable_population,
                        'description': info.description,
                        'employment_direction': info.employment_direction,
                        'common_examination_major': info.common_examination_major,
                        'recommended_colleges': info.recommended_colleges
                    })

        result = self._get_result(student_info, college_result, holland_test_data, combined_major_info, stream)
        college_information = result.message.content

        # 解析不同档的院校和专业
        parsed_college_choices = self._parse_college_choices(college_information)

        # 保存解析结果到数据库
        self._save_to_kao_yan_student_info(
            report_id=report_id,
            student_info=student_info,
            college_analysis=college_information,
            intensive_choice=parsed_college_choices['冲刺档'],
            steady_choice=parsed_college_choices['稳妥档'],
            safety_choice=parsed_college_choices['保底档']
        )

        self._publish_workflow_end_message()

        return result

    def _get_result(self, student_info: str, college_info: list, holland_test_data: str, combined_major_info: list,
                    stream: bool) -> LLMResult:
        self.college_analysis_feature = CollegeAnalysisFeature(app_no='college_analysis')
        combined_college_info_str = json.dumps(college_info, ensure_ascii=False)
        combined_major_info_str = json.dumps(combined_major_info, ensure_ascii=False)

        result = self.college_analysis_feature.run(
            self.application_generate_entity,
            stream=True,
            query=student_info,
            college_info=combined_college_info_str,
            holland_test_data=holland_test_data,
            combined_major_info=combined_major_info_str
        )

        tracing_info = {
            'tracing_type': 'college_analysis',
            'query': student_info
        }

        return self._handle_node_generate_response(result, tracing_info=tracing_info)

    def _parse_college_choices(self, text: str) -> dict:
        """
        解析文本中的冲刺档、稳妥档和保底档的院校和专业信息。
        """
        # 修改后的正则表达式
        pattern = r'### (冲刺档|稳妥档|保底档)\n\n1\. \*\*目标院校\*\*：(.*?)\s+\*\*目标专业\*\*：(.*?)\n'
        matches = re.findall(pattern, text)
        print(f"text在这里:{text}")
        college_choices = {
            '冲刺档': [],
            '稳妥档': [],
            '保底档': []
        }

        for match in matches:
            choice_type, university, major = match
            college_choices[choice_type].append({
                '院校名': university,
                '专业名': major
            })

        print(f"college_choices在这里:{college_choices}")

        return college_choices

    def _save_to_kao_yan_student_info(
            self,
            report_id: str,
            student_info: str,
            college_analysis: str,
            intensive_choice: list,
            steady_choice: list,
            safety_choice: list
    ):
        """
        将学生信息和解析后的院校选择保存到数据库。
        """
        student_info_json = json.loads(student_info)

        KaoYanStudentInfo.objects.update_or_create(
            report_id=report_id,
            defaults={
                'student_info': student_info_json,
                'college_analysis': college_analysis,
                'intensive_choice': intensive_choice,
                'steady_choice': steady_choice,
                'safety_choice': safety_choice
            }
        )