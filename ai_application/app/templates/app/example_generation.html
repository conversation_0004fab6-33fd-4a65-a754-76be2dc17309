<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解题助手</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            width: 100%;
        }
        label {
            font-weight: bold;
            margin-top: 15px;
        }
        textarea, input {
            width: 100%;
            padding: 10px;
            margin-top: 5px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        /* Adjusting the height of the textareas */
        #user-question {
            height: 200px; /* Increase the height of the input textarea */
        }
        #result {
            height: 400px; /* Increase the height of the result textarea */
        }
        button {
            width: 100%;
            padding: 10px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .prompt_input {
            resize: vertical;
            height: 300px; /* 设置一个初始高度 */
            width: 600px;
        }
        /* 新增弹出层样式 */
      .promptManagementModal {
        display: none;
        position: fixed;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background-color: #fff;
        padding: 20px;
        border: 1px solid #ccc;
        z-index: 1000;
      }
        /* 遮罩层样式 */
      #overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
      }
    </style>
</head>
<body>

<div class="container">
    <h2>解题助手</h2>

    <!-- 新增遮罩层 -->
    <div id="overlay"></div>
    <div style="display: flex; margin-bottom: 20px">
        <div>
            <button id="example_generation_btn" class="prompt-button">例题生成提示词</button>
            <div id="example_generation_modal" class="promptManagementModal">
              <h2>例题生成提示词</h2>
                <textarea id="example_generation_prompt" class="prompt_input" name="compare_three_inputs">{{ example_generation }}</textarea><br>
                <button id="confirm_example_generation" type="button">确定</button>
                <button id="cancel_example_generation" type="button">取消</button>
            </div>
        </div>

    </div>

    <label for="user-question">输入您的问题：</label>
    <textarea id="user-question" placeholder="请输入您的问题"></textarea>

    <button onclick="submitQuestion()">提交</button>

    <label for="result">返回结果：</label>
    <textarea id="result" readonly placeholder="这里将显示答案"></textarea>
</div>

<script>
    let temp_example_generation_prompt = $('#example_generation_prompt').val()

    $('#example_generation_btn').click(function() {
        $('#example_generation_modal').show();
        $('#overlay').show();
        $('#example_generation_prompt').val(temp_example_generation_prompt)
    });


    $('#confirm_example_generation').click(function() {
        temp_example_generation_prompt = $('#example_generation_prompt').val()
        $('#example_generation_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_example_generation').click(function() {
        $('#example_generation_modal').hide();
        $('#overlay').hide();
    });

    // 获取输入框和返回结果框
    const questionInput = document.getElementById("user-question");
    const resultBox = document.getElementById("result");

    // 监听用户输入事件，移除空白和换行符
    questionInput.addEventListener('input', function() {
        this.value = this.value.replace(/\s+/g, ''); // 移除所有空白和换行符
    });

    async function submitQuestion() {
        const userQuestion = questionInput.value.trim();

        // 清空返回结果文本框的内容
        resultBox.value = '';

        if (!userQuestion) {
            alert("请输入问题！");
            return;
        }

        const headers = {
            'Content-Type': 'application/json',
            'x-api-key': 'ak_f288a20832ab417d',
            'x-signature': '91e9b4e95b65e5fc14013baeb7e5bfe252b1db58071c01b5b19b889fe85ddfdb',
            'timestamp': '1720405843',
            'nonce': 'ld8h259m',
            'x-sign-debug': '1',
        };

        try {
            const response = await fetch('{% url "example_generation" %}', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    example_generation: $('#example_generation_prompt').val(),

                    user_question: userQuestion
                })
            });

            if (response.ok) {
                const data = await response.json();

                // 检查返回的数据结构是否符合预期
                if (data.code === 0 && data.data && data.data.answer) {
                    resultBox.value = data.data.answer; // 显示答案
                } else {
                    resultBox.value = "无法获取有效的答案，请稍后再试。";
                }
            } else {
                resultBox.value = "抱歉，无法获取答案，请稍后再试。";
            }
        } catch (error) {
            console.error("Error:", error);
            resultBox.value = "请求失败，请检查网络连接。";
        }
    }
</script>

</body>
</html>


