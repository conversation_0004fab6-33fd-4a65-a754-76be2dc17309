<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目详情</title>
        <script src="https://cdn.bootcdn.net/ajax/libs/marked/13.0.2/marked.min.js"></script>
        <link rel="stylesheet"
              href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
            position: sticky;
            top: 0; /* 固定在顶部 */
            z-index: 10; /* 确保在其他内容之上 */
        }

        .table-container {
            max-height: 80vh; /* 设置容器高度以启用滚动 */
            overflow-y: auto; /* 启用垂直滚动 */
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        #pagination {
            float: right;
            margin-right: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .modal-content {
            background-color: white;
            margin: 20% auto;
            padding: 20px;
            width: 50%;
            border: 1px solid #ccc;
        }

        #textInput {
            width: 70%;
        }
        
    </style>
</head>
<body>
<h1>
    {% block title %}
        <span>题目: {{ question_num }}</span>
    {% endblock %}
    {% block back %}{% endblock %}
</h1>

<div>
    <p style="font-weight: bolder">题干</p>
    {{ master_title|safe }}
    
    <ul>
        {% for c in choice_body %}
        	<li>{{ c }}</li>
        {% endfor %}
    </ul>
    <br>
    <p style="font-weight: bolder">答案</p>
    <ul>
        {% for a in answer_body %}
        	<li>{{ a }}</li>
        {% endfor %}
    </ul>
    <br>
    <p style="font-weight: bolder">解析</p>
    {{ analysis|safe }}
</div>

<div>{{ error }}</div>

<script>
    const md_list = document.querySelectorAll('.markdown-body');
    md_list.forEach(md_item => {
        md_item.innerHTML = marked.parse(md_item.innerHTML);
    });
</script>

</body>
</html>