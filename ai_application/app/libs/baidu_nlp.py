import logging

from aip import AipNlp
from django.conf import settings

logger = logging.getLogger(__name__)


class BaiduNlp:

    def __init__(self):
        """ 你的 APPID AK SK """
        APP_ID = settings.BAIDU_NLP_API_ID
        API_KEY = settings.BAIDU_NLP_API_KEY
        SECRET_KEY = settings.BAIDU_NLP_SECRET_KEY

        self.client = AipNlp(APP_ID, API_KEY, SECRET_KEY)
        # 由于sdk问题，这里需要修改词法分析的url
        lexer_url = 'https://aip.baidubce.com/rpc/2.0/nlp/v1/lexer?charset=UTF-8'
        setattr(self.client, '_AipNlp__lexerUrl', lexer_url)

    def lexer_analysis(self, text: str, filter_pos: list | None = None) -> list:
        """
        https://cloud.baidu.com/doc/NLP/s/fk6z52f2u
        :param text:
        :param filter_pos:
        :return:
        """

        try:
            res = self.client.lexer(text)
        except Exception as e:
            logger.exception(e)
            return []

        if 'error_code' in res:
            logger.error(f'baidu_lexer_analysis_error:{res}')
            return []

        items = []
        for i in res['items']:
            if filter_pos:
                if i['pos'] in filter_pos:
                    items.append(i['item'])
            else:
                items.append(i['item'])
        return items
