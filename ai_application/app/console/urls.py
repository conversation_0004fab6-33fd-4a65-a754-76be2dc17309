from django.urls import path
from . import views
from .views import document_proofreader_views as dpviews

urlpatterns = [
    path('common_upload_image', views.CommonUploadImage.as_view(), name='common_upload_image'),

    path('prompt_list', views.PromptListView.as_view(), name='prompt_list'),
    path('prompt_detail/<str:app_no>', views.PromptDetailView.as_view(), name='prompt_detail'),
    path('prompt_detail/<str:app_no>/publish', views.PromptPublishView.as_view(), name='prompt_publish'),
]

urlpatterns += [
    path('demo_app_list', views.DemoAppListView.as_view(), name='demo_app_list'),
    path('app_list', views.AppListView.as_view(), name='app_list'),
    path('message_tracing', views.MessageTracingView.as_view(), name='message_tracing'),
    path('message_tracing_detail', views.MessageTracingDetailView.as_view(), name='message_tracing_detail'),
    path('debug_prompt_change_log', views.DebugPromptChangeLogView.as_view(), name='debug_prompt_change_log'),
    path('debug_prompt_log', views.DebugPromptLogView.as_view(), name='debug_prompt_log'),

    # 通用应用处理
    path('app_estimate/<str:app_no>', views.AppEstimateView.as_view(), name='app_estimate'),
    path('app_estimate/<str:app_no>/submit', views.AppEstimateSubmitView.as_view(), name='app_estimate_submit'),
    path('app_prompt/publish', views.AppPromptPublishView.as_view(), name='app_prompt_publish'),
    path('prompt_estimate_submit', views.PromptEstimateSubmitView.as_view(), name='prompt_estimate_submit'),

    # prompt优化器
    path('prompt_optimize', views.PromptOptimizeView.as_view(), name='prompt_optimize'),
    path('prompt_optimize_submit1', views.PromptOptimizeSubmit1View.as_view(), name='prompt_optimize_submit1'),
    path('prompt_optimize_submit2', views.PromptOptimizeSubmit2View.as_view(), name='prompt_optimize_submit2'),

    # 爆文相关内容
    path('baowen_estimate', views.BaowenEstimateView.as_view(), name='baowen_estimate'),

    # 文档转换助手
    path('document_summary/<str:style>', views.DocumentSummaryView.as_view(), name='document_summary'),
    path('document_summary_upload', views.DocumentSummaryUpload.as_view(), name='document_summary_upload'),
    path('document_summary_submit', views.DocumentSummarySubmitView.as_view(), name='document_summary_submit'),

    # 知舟问答复合应用
    path('chat_app_submit', views.ChatAppSubmitView.as_view(), name="chat_app_submit"),
    path('update_debug_prompt', views.UpdateDebugPromptView.as_view(), name='update_debug_prompt'),
    path('chat_app_index', views.ChatAppIndexView.as_view(), name="chat_app_index"),
    path('english_reading_comprehension_index', views.EnglishReadingComprehensionIndexView.as_view(), name="english_reading_comprehension_index"),
    path('english_reading_comprehension', views.EnglishReadingComprehensionView.as_view(), name="english_reading_comprehension"),

    # 知舟·应用统计看板
    path('app_message_stats', views.AppMessageStatsView.as_view(), name='app_message_stats'),

    path('app_message_list/<str:app_id>', views.AppMessageListView.as_view(), name='app_message_list'),
    path('app_message_type_list/<str:app_id>/<str:message_type>', views.AppMessageListView.as_view(),
         name='app_message_type_list'),

    path(
        'app_exception_message_list/<str:app_id>', views.AppExceptionMessageListView.as_view(),
        name='app_exception_message_list'
    ),
    path(
        'app_exception_message_type_list/<str:app_id>/<str:message_type>', views.AppExceptionMessageListView.as_view(),
        name='app_exception_message_type_list'
    ),

    path(
        'app_daily_exception_message_list/<str:app_id>/', views.AppDailyExceptionMessageListView.as_view(),
        name='app_daily_exception_message_list'
    ),
    path(
        'app_daily_exception_message_type_list/<str:app_id>/<str:message_type>', views.AppDailyExceptionMessageListView.as_view(),
        name='app_daily_exception_message_type_list'
    ),

    path('app_msg_mark_exception/<str:app_id>/<int:msg_id>', views.AppMsgMarkExceptionView.as_view(), name='app_msg_mark_exception'),
    path('app_msg_mark_not_exception/<str:app_id>/<int:msg_id>',
         views.AppMsgMarkNotExceptionView.as_view(), name='app_msg_mark_not_exception'),

    path('app_msg_daily_stats/<str:app_id>', views.AppMsgDailyStatsView.as_view(), name='app_msg_daily_stats'),
    path('app_msg_daily_type_stats/<int:app_id>/<str:message_type>', views.AppMsgDailyStatsView.as_view(), name='app_msg_daily_type_stats'),
    path('app_msg_daily', views.AppMsgDailyDetailView.as_view(), name='app_msg_daily_detail'),
    path('app_msg_type_daily', views.AppMsgDailyDetailView.as_view(), name='app_msg_daily_type_detail'),
    # 统考题目
    path(
        'unified_examination_question', views.UnifiedExaminationQuestionView.as_view(),
        name='unified_examination_question'
    ),
    path('ueq_detail/<int:question_num>', views.UeqDetailView.as_view(), name='ueq_detail'),
]

urlpatterns += [
    path('split_estimate', views.SplitEstimateView.as_view(), name='split_estimate'),
    path('split_estimate_submit', views.SplitEstimateSubmitView.as_view(), name='split_estimate_submit'),
]

# 知识解析相关
urlpatterns += [
    path('knowledge_list', views.KnowledgeListView.as_view(), name='knowledge_list'),
    path('knowledge_search', views.KnowledgeSearchView.as_view(), name='knowledge_search'),
    path('knowledge_search_result', views.KnowledgeSearchResultView.as_view(), name='knowledge_search_result'),
    path('knowledge_deep_search_result', views.KnowledgeDeepSearchResultView.as_view(), name='knowledge_deep_search_result'),
    path('knowledge_es', views.KnowledgeEsView.as_view(), name='knowledge_es'),
    path('knowledge_es_result', views.KnowledgeEsResultView.as_view(), name='knowledge_es_result'),
    path('knowledge_search_with_video', views.KnowledgeSearchWithVideoView.as_view(), name='knowledge_search_with_video'),
    path('knowledge_search_with_video_result', views.KnowledgeSearchWithVideoResultView.as_view(), name='knowledge_search_with_video_result'),
]

urlpatterns += [
    path('ocr_test', views.OcrTestView.as_view(), name='ocr_test'),
    path('ocr_result', views.OcrResultView.as_view(), name='ocr_result'),
    path('complex_sentence_analysis', views.ComplexSentenceAnalysisView.as_view(), name="complex_sentence_analysis"),
    path('complex_sentence_analysis_index', views.ComplexSentenceAnalysisIndexView.as_view(),name="complex_sentence_analysis_index"),
    path('chapter_note_generation', views.ChapterNoteGeneratorView.as_view(), name="chapter_note_generation"),
    path('lecture_note_generation_chapter', views.LectureNoteGeneratorChapterView.as_view(), name="lecture_note_generation_chapter"),
    path('lecture_note_generation_batch', views.LectureNoteGeneratorBatchView.as_view(), name="lecture_note_generation_batch"),
    path('note_generator_index', views.NoteGeneratorIndexView.as_view(),name="note_generator_index"),
    path('note_generator_tasks', views.NoteGeneratorTasksView.as_view(),name="note_generator_tasks"),
    path('note-detail/<int:pk>/',views.NoteDetailView.as_view(),name='note_detail'),
    path('task-details/<int:note_id>/', views.TaskDetailView.as_view(), name='task_details'),
    path('single-note-detail/<int:pk>/', views.SingleNoteDetailView.as_view(), name='single_note_detail'),
    path('parse_file', views.FileUploadView.as_view(), name='parse_file'),
    path('learning_report_generator', views.LearningReportGeneratorView.as_view(), name="learning_report_generator"),
    path('learning_report_generator_index', views.LearningReportGeneratorIndexView.as_view(),name="learning_report_generator_index"),
    path('college_analysis', views.CollegeAnalysisView.as_view(), name="college_analysis"),
    path('college_analysis_index', views.CollegeAnalysisIndexView.as_view(),name="college_analysis_index"),
    path('huolande_test', views.Huolande_TestView.as_view(),name="huolande_test"),
    path('huolande_test_index', views.Huolande_Test_IndexView.as_view(),name="huolande_test_index"),
    path('kaoyan_review_plan', views.KaoYanReviewPlanView.as_view(), name="kaoyan_review_plan"),
    path('kaoyan_review_plan_index', views.KaoYanReviewPlanIndexView.as_view(),name="kaoyan_review_plan_index"),
]
#知舟问答2.0
urlpatterns += [
    # path('',views.DayiappView.as_view(), name='dayiapp_index'),
    path('ai_assistant',views.AIAssistantView.as_view(),name='ai_assistant'),
    path('ai_assistant_index',views.AIAssistantIndexView.as_view(),name='ai_assistant_index'),
    path('save_subject_prompt',views.SubjectPromptView.as_view(), name='save_subject_prompt'),
]


# 智能化学习指导
urlpatterns += [
    # 新版学生画像Demo
    path('new_student_profile_demo', views.NewStudentProfileDemoView.as_view(), name='new_student_profile_demo'),
    # 添加保存提示词的URL路径
    path('save_prompt_template', views.SavePromptTemplateView.as_view(), name='save_prompt_template'),
    # 添加代理外部API的路由
    path('kaoyan-learn-query', views.KaoyanLearnQueryView.as_view(), name='kaoyan_learn_query'),
    path('syllabus_guide_index', views.SyllabusGuideIndexView.as_view(), name='syllabus_guide_index'),
    path('408_personal_syllabus', views.PersonalSyllabusView.as_view(), name='408_personal_syllabus'),
]

# 学科关联度
urlpatterns += [
    path('categories/', views.CategoryListView.as_view(), name='category-list'),
    path('category-correlations/', views.CategoryCorrelationView.as_view(), name='category-correlation'),
    path('majors/', views.MajorListView.as_view(), name='major-list'),
    path('major-correlations/', views.MajorCorrelationView.as_view(), name='major-correlation'),
    path('export-excel/', views.ExportExcelView.as_view(), name='export-excel'),
    path('category_index', views.CategoryCorrelationsIndexView.as_view(),name="category_index"),
]

# 文档校正
urlpatterns += [
    path('document_proofreader/', dpviews.DocumentProofreaderView.as_view(), name="document_proofreader"),
    # 支持通过proofreader_id访问结果页面的新路由
    path('document_proofreader/result/<int:proofreader_id>/', dpviews.DocumentProofreaderResultView.as_view(), name="document_proofreader_result"),
    path('document_proofreader/<str:task_id>/<str:filename>/', dpviews.DocumentProofreaderView.as_view()),
    path('document_proofreader/<str:task_id>/<str:filename>/<str:format_type>/', dpviews.DocumentProofreaderView.as_view()),
    path('document_proofreader/delete_suggestion/', dpviews.DocumentProofreaderDeleteSuggestionView.as_view(), name="document_proofreader_delete_suggestion"),
    path('document_proofreader/save_edit/', dpviews.DocumentProofreaderSaveEditView.as_view(), name="document_proofreader_save_edit"),
    
    # 普通审校进度路由
    path('document_proofreader_progress/', dpviews.DocumentProofreaderProgressView.as_view(), name="document_proofreader_progress"),
    path('document_proofreader_progress_status/', dpviews.DocumentProofreaderProgressStatusView.as_view(), name="document_proofreader_progress_status"),
    
    # 校对词库管理路由
    path('proofreading_dictionary/', dpviews.ProofreadingDictionaryView.as_view(), name="proofreading_dictionary"),
    path('proofreading_dictionary/add_from_error/', dpviews.ProofreadingDictionaryAddFromErrorView.as_view(), name="proofreading_dictionary_add_from_error"),
    path('proofreading_dictionary_manage/', dpviews.ProofreadingDictionaryManageView.as_view(), name="proofreading_dictionary_manage"),
    
    # 缓存管理路由
    path('document_proofreader/cache/<int:proofreader_id>/', dpviews.DocumentProofreaderCacheManageView.as_view(), name="document_proofreader_cache_manage"),
    
    # 异步任务管理路由
    path('document_proofreader_async_manage/', dpviews.DocumentProofreaderAsyncManageView.as_view(), name="document_proofreader_async_manage"),
    path('document_proofreader_async_manage/api/', dpviews.DocumentProofreaderAsyncManageApiView.as_view(), name="document_proofreader_async_manage_api"),
    path('document_proofreader_async_manage/retry/', dpviews.DocumentProofreaderAsyncRetryView.as_view(), name="document_proofreader_async_retry"),
    path('document_proofreader_async_manage/delete/', dpviews.DocumentProofreaderAsyncDeleteView.as_view(), name="document_proofreader_async_delete"),
    
    # 历史记录路由
    path('document_proofreader/history/', dpviews.DocumentProofreaderHistoryView.as_view(), name="document_proofreader_history"),
]


#知识解析2.0
urlpatterns += [
    # path('',views.DayiappView.as_view(), name='dayiapp_index'),
    path('knowledge_list_2', views.KnowledgeListView2.as_view(), name='knowledge_list_2'),
    path('knowledge_search_2', views.KnowledgeSearchView2.as_view(), name='knowledge_search_2'),
    path('save_knowledge', views.Knowledgesave_knowledge_view.as_view(), name='save_knowledge'),
    path('knowledge_search_result_2', views.KnowledgeSearchResultView2.as_view(), name='knowledge_search_result_2'),
    path('knowledge_deep_search_result_2', views.KnowledgeDeepSearchResultView2.as_view(),name='knowledge_deep_search_result_2'),
    path('knowledge_search_with_video_2', views.KnowledgeSearchWithVideoView2.as_view(),name='knowledge_search_with_video_2'),
    path('knowledge_search_with_video_result_2', views.KnowledgeSearchWithVideoResultView2.as_view(),name='knowledge_search_with_video_result_2'),
]

# 文生视频
urlpatterns +=[
    path('text_to_video',views.TextToVideoView.as_view(),name='text_to_video'),
    path('text_to_video_search',views.TextToVideoViewSearch.as_view(),name='text_to_video_search'),
    path('text_to_video_search/save_text',views.TextToVideoViewSearch.as_view(),name='text_to_video_save_text'),
    path('text_to_video_search/save_audio',views.TextToVideoViewSearch.as_view(),name='text_to_video_save_audio'),
    path('text_to_video_search/generate_video',views.TextToVideoViewSearch.as_view(),name='text_to_video_generate_video'),
    path('text_to_video_search/get_history',views.TextToVideoViewSearch.as_view(),name='text_to_video_get_history')
]


# 院校规划迭代版demo
urlpatterns += [
    path('college_analysis_new', views.CollegeAnalysisNewView.as_view(), name="college_analysis_new"),
    path('college_analysis_index_new', views.CollegeAnalysisNewIndexView.as_view(), name="college_analysis_index_new"),
    path('college_analysis_new_with_goal', views.CollegeAnalysisNewWithGoalView.as_view(), name="college_analysis_new_with_goal"),
    path('get_colleges', views.CollegeAnalysisNewCollegeView.as_view(), name="get_colleges"),
]

# 讲义审校
urlpatterns += [
    path('document_proofread_traditional/', views.DocumentProofreadTraditionalIndexView.as_view(), name="document_proofread_traditional"),
    path('document_proofread_traditional/submit/', views.DocumentProofreadTraditionalSubmitView.as_view(), name="document_proofread_traditional_submit"),
]


# 知舟宝典
urlpatterns += [
    path('knowledge/', views.DataStructureKnowledgeView.as_view(), name='knowledge_index'),
    path('add_kp/', views.AddKnowledgePointView.as_view(), name='add_knowledge_point'),
    path('delete_kp/', views.DeleteKnowledgePointView.as_view(), name='delete_knowledge_point'),
    path('get_video/', views.GetVideoView.as_view(), name='get_video'),
    path('add_video/', views.AddVideoView.as_view(), name='add_video'),
    path('text_to_video_remind/', views.TextToVideoRemindView.as_view(), name='text_to_video_remind'),
    path('question_type_difficulty_overview/', views.QuestionTypeDifficultyOverviewView.as_view(), name='question_type_difficulty_overview'),
    path('question_list/', views.QuestionListByTypeDifficultyView.as_view(), name='question_list_by_type_difficulty'),
    path('question_detail/', views.QuestionDetailView.as_view(), name='question_detail'),
    path('question_bulk_delete/', views.QuestionBulkDeleteView.as_view(), name='question_bulk_delete'),
    path('knowledge_point_overview/', views.KnowledgePointOverviewView.as_view(), name='knowledge_point_overview'),
    path('knowledge_question_list/', views.KnowledgeQuestionListView.as_view(), name='knowledge_question_list'),
]

# 刷题相关
urlpatterns += [
    path('st_paper_index', views.STPaperIndexView.as_view(), name='st_paper_index'),
    path('st_paper_detail', views.STPaperDetailView.as_view(), name='st_paper_detail'),
    path('st_paper_submit', views.STPaperSubmitView.as_view(), name='st_paper_submit'),
    path('st_paper_report', views.STPaperReportView.as_view(), name='st_paper_report'),
    path('st_paper_dist_debug', views.STPaperDistDebugView.as_view(), name='st_paper_dist_debug'),
    path('st_paper_dist_gen', views.STPaperDistGenView.as_view(), name='st_paper_dist_gen'),
    path('st_paper_dist_param_gen', views.STPaperDistParamGenView.as_view(), name='st_paper_dist_param_gen'),
    path('st_paper_dist_prompt_save', views.STPaperDistPromptSaveView.as_view(), name='st_paper_dist_prompt_save'),
    path('st_ai_assistant_index', views.STAIAssistantIndexView.as_view(),name='st_ai_assistant_index'),
]
