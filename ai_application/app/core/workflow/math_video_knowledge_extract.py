import json
import os
import re

from django.conf import settings

from app.core.features.math_video_knowledge_extract.math_video_judge import MathVideoJudgeFeature
from app.core.features.math_video_knowledge_extract.video_knowledge_extract import VideoKnowledgeExtractFeature
from app.core.workflow.base_workflow import BaseWorkflow
from app.models import KnowledgeLibrary


class MathVideoKnowledgeExtractWorkflow(BaseWorkflow):

    def run(self):
        main_subject_map = {
            'high_math': '高等数学',
            'linear_algebra': '线性代数',
            'math_prob': '概率论与数理统计',
            'data_structure': '数据结构',
            'organization': '组成原理',
            'network': '计算机网络',
            'os': '操作系统',
        }

        main_subject = self.application_generate_entity.inputs.get('main_subject')
        if not (main_subject and main_subject in main_subject_map):
            raise Exception('main_subject is valid')
        video_content = self.application_generate_entity.query
        if not video_content:
            raise Exception('video_content is empty')

        if main_subject in ['high_math', 'linear_algebra', 'math_prob']:
            knowledge_json_file = settings.BASE_DIR.joinpath(f'knowledge_list/{main_subject}.json')
            if not os.path.exists(knowledge_json_file):
                raise Exception('main_subject path is valid')

            try:
                with open(knowledge_json_file, 'r') as f:
                    knowledge_list = json.loads(f.read())
            except:
                raise Exception('knowledge_list is valid')
            knowledge_list = [i['name'] for i in knowledge_list]
        else:
            qs = KnowledgeLibrary.objects.filter(
                subject_domain__main_subject_code=main_subject,
                is_deleted=False, nature='major'
            )
            knowledge_list = []
            for i in qs:
                knowledge_list.append(i.name)

        if self._judge_math_video():
            kg_list = []
        else:
            res = self._extract_video_knowledge(knowledge_list)
            # 处理返回json
            try:
                kg_list = self._parse_json_list(res)
            except:
                kg_list = []

        # 过滤不在知识点列表中的, 处理知识点，weight<6的不返回
        data = [i['knowledge'] for i in kg_list if i['weight'] > 5 and i['knowledge'] in knowledge_list]
        self._publish_workflow_custom_message(json.dumps(data, ensure_ascii=False))
        self._publish_workflow_end_message()

    def _parse_json_list(self, text):
        pattern = r'\[.*?\]'
        match = re.search(pattern, text, re.DOTALL)

        if match:
            json_str = match.group()
            try:
                # 将提取的 JSON 字符串解析为 Python 对象
                return json.loads(json_str)
            except json.JSONDecodeError as e:
                return []
        else:
            return []

    def _judge_math_video(self) -> int:
        main_subject = self.application_generate_entity.inputs.get('main_subject')
        if main_subject in ['high_math', 'linear_algebra', 'math_prob']:
            app_no = 'math_video_judge'
        else:
            app_no = '408_video_judge'

        math_video_content = self.application_generate_entity.query
        result = MathVideoJudgeFeature(app_no).run(
            self.application_generate_entity,
            math_video_content=math_video_content
        )

        tracing_info = {
            'tracing_type': app_no,
            'query': math_video_content
        }
        self.tracing_log.add_tracing_by_llm_result(
            tracing_type=tracing_info.get('tracing_type'),
            query=tracing_info.get('query'),
            llm_result=result
        )

        try:
            return int(result.message.content)
        except:
            return 0

    def _extract_video_knowledge(self, knowledge_list):
        math_video_content = self.application_generate_entity.query
        main_subject = self.application_generate_entity.inputs.get('main_subject')
        if main_subject in ['high_math', 'linear_algebra', 'math_prob']:
            app_no = 'math_video_knowledge_extract'
        else:
            app_no = '408_video_knowledge_extract'

        result = VideoKnowledgeExtractFeature(app_no).run(
            self.application_generate_entity,
            main_subject=main_subject,
            knowledge_list=knowledge_list,
            math_video_content=math_video_content,
        )

        tracing_info = {
            'tracing_type': app_no,
            'query': math_video_content
        }
        self.tracing_log.add_tracing_by_llm_result(
            tracing_type=tracing_info.get('tracing_type'),
            query=tracing_info.get('query'),
            llm_result=result
        )

        return result.message.content
