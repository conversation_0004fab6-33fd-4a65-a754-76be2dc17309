import json
import random
import string


def parse_int(s: str, d=0) -> int:
    try:
        return int(s)
    except Exception:
        return d


def parse_json(s: str, d=None):
    try:
        return json.loads(s)
    except Exception:
        return d


def fill_int_str(i: int, min_len: int) -> str:
    s = str(i)
    s_len = len(s)
    if s_len > min_len:
        return s

    return '{}{}'.format('0'*(min_len-s_len), s)


def generate_random_code(length):
    # 选择只包含数字的字符集合
    characters = string.digits  # 只包含数字的字符集合
    # 生成随机验证码
    code = ''.join(random.choice(characters) for _ in range(length))
    return code


def generate_random_string(length):
    letters = string.ascii_lowercase + string.digits
    return ''.join(random.choice(letters) for _ in range(length))
