<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记生成助手</title>
    <script src="//cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/15.0.4/marked.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.css" rel="stylesheet">
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/katex.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.16.9/contrib/auto-render.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background-color: #f6f8fa;
        }

        .container {
            max-width: 1500px;
            width: 100%;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        label {
            font-weight: bold;
            margin-top: 15px;
        }

        textarea, input {
            width: 95%;
            padding: 10px;
            margin-top: 5px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }

        #user-question {
        }
        #chapter_lecture {
            display: none;
            height: 200px;
        }

        button {
            padding: 10px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        button.save-button {
            margin-top: 10px;
            padding: 10px 20px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        button.save-button:hover {
            background-color: #0056b3;
        }
        .prompt-button {
            display: block;
            margin-bottom: 5px;
        }
        .history-btn {
            background-color: green;
        }
        .history-btn:hover {
            background-color: green;
        }

        .prompt_input {
            resize: vertical;
            height: 300px;
            width: 100%;
        }

        .modal {
            display: none;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ccc;
            z-index: 1000;
            width: 60%;
            max-width: 800px;
            border-radius: 8px;
        }

        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            color: #333333;
        }

        .results-container {
            display: grid;
            grid-template-columns: 70% 25%;
            gap: 20px;
            margin-top: 20px;
        }

        .result-view, .tracing-detail {
            background-color: #fff;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .button-group {
            display: flex;
            gap: 10px;
        }

        .section {
            margin-bottom: 20px;
        }

        .button-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            gap: 10px;
        }

        .file-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }

        .textarea-container {
            width: 48%;
        }
        input[type="file"] {
            width: 75%;
            margin-right: 15px;
        }
        .history_view {
            padding: 0 0 20px;
        }
        .history_item {
            display: flex;
            padding: 8px 0;
            border-bottom: 1px solid #ccc;
        }
        .history_item_detail {
            margin-left: 20px;
            cursor: pointer;
            border: 1px solid #bbb;
            padding: 3px 10px;
            font-size: 14px;
        }
        .history_detail {
            white-space: pre-line;
            padding-bottom: 5px;
            margin-bottom: 20px;
            height: 450px;
            overflow-y: scroll;
        }

    </style>
</head>
<body>
<div class="container">
    <h2>笔记生成助手</h2>
    <!-- HTML部分 -->
    <form id="questionForm">
      <label for="modelProviderSelect">选择章节笔记模型:</label>
      <select name="modelProvider" id="modelProviderSelect">
        <option value="tongyi">TongYi</option>
        <option value="deepseek">DeepSeek</option>
      </select>

    </form>

    <form id="lectureQuestionForm">
      <label for="lectureModelProviderSelect">选择随堂笔记模型:</label>
      <select name="modelProvider" id="lectureModelProviderSelect">
        <option value="tongyi">TongYi</option>
        <option value="deepseek">DeepSeek</option>
      </select>

    </form>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <p>笔记生成中...</p>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div id="overlay"></div>

    <!-- 编辑按钮和模态框 -->
    <div class="button-group">
        <div>
            <button id="chapter_note_edit_btn" class="prompt-button">章节笔记提示词编辑</button>
            <button class="prompt-button history-btn"
                    data-app-no="chapter_note_generation"
                    data-app-name="章节笔记提示词"
            >章节笔记提示词修改记录</button>
        </div>
        <div>
            <button id="lecture_note_edit_btn" class="prompt-button">随堂笔记提示词编辑</button>
            <button class="prompt-button history-btn"
                    data-app-no="lecture_note_generation"
                    data-app-name="随堂笔记提示词"
            >随堂笔记提示词修改记录</button>
        </div>
    </div>

    <div id="chapter_note_edit_modal" class="modal">
        <h3>章节笔记编辑</h3>
        <textarea id="chapter_note_prompt_input" class="prompt_input">{{ chapter_note_generation }}</textarea><br>
        <div class="button-group">
            <button id="confirm_chapter_note_edit">确定</button>
            <button id="cancel_chapter_note_edit">取消</button>
        </div>
    </div>

    <div id="lecture_note_edit_modal" class="modal">
        <h3>随堂笔记编辑</h3>
        <textarea id="lecture_note_prompt_input" class="prompt_input">{{ lecture_note_generation }}</textarea><br>
        <div class="button-group">
            <button id="confirm_lecture_note_edit">确定</button>
            <button id="cancel_lecture_note_edit">取消</button>
        </div>
    </div>

    <div id="prompt_change_log_modal" class="modal">
        <h3 class="prompt_name"></h3>
        <div class="history_view"></div>
        <div class="button-group">
            <button class="close-modal">关闭</button>
        </div>
    </div>

    <div id="prompt_change_log_detail_modal" class="modal">
        <h3 class="prompt_name">修改记录详情</h3>
        <div class="history_detail">11123232323232\n2222dsddsd</div>
        <div class="button-group">
            <button class="close-only-modal">关闭</button>
        </div>
    </div>

    <!-- 上传课件和生成章节笔记 -->
    <div class="file-row">
        <label for="lecture-slides-upload">上传课件：</label>
        <input type="file" id="lecture-slides-upload" accept=".md,.docx,.txt"/>
        <button onclick="parseLectureSlides()">解析课件</button>
        <button onclick="submitQuestion()">生成章节笔记</button>
    </div>
    <textarea id="user-question" placeholder="课件内容"></textarea>

    <h3>生成随堂笔记</h3>
    <!-- 生成随堂笔记 -->
    <div style="margin-bottom: 20px">
        <select name="course_chapter" id="course_chapter">
            <option value="">请选择章</option>
            {% for chapter_name in chapter_names %}
                <option value="{{ chapter_name }}">{{ chapter_name }}</option>
            {% endfor %}
        </select>
    </div>
    <div>
        <label for="lecture-upload">上传课件：</label>
        <input type="file" id="lecture-upload" accept=".md"/>
    </div>

    <div>
        <button onclick="genChapterLecture()">生成单章笔记</button>
        <button onclick="genLectureBatch()">生成全部笔记</button>
    </div>

    <div style="margin-top: 20px">
        <a href="{% url 'note_generator_tasks' %}">查看随堂笔记生成任务</a>
    </div>

    <!-- 返回结果 -->
    <div class="section">
        <h3>章节笔记返回结果:</h3>
        <div class="results-container">
            <div class="result-view" id="resultView">
                <!-- 搜索结果将在这里显示 -->
            </div>
            <div class="tracing-detail" id="tracingDetails">
                <!-- 搜索细节将在这里显示 -->
            </div>
        </div>
        <!-- 添加保存按钮容器 -->
        <div id="saveChapterNoteContainer" style="display: none;">
            <button class="save-button" onclick="saveChapterNote()">保存章节笔记</button>
        </div>
    </div>

</div>

<script>
    let temp_chapter_note_prompt = $('#chapter_note_prompt_input').val();
    let temp_lecture_note_prompt = $('#lecture_note_prompt_input').val();

    let message_id = null;

    const questionInput = document.getElementById("user-question");
    const resultView = document.getElementById('resultView');
    const tracingDetails = document.getElementById('tracingDetails');
    const lectureResultView = document.getElementById('lectureResultView');
    const lectureTracingDetails = document.getElementById('lectureTracingDetails');
    const katexOptions = {
        // customised options
        // • auto-render specific keys, e.g.:
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        // • rendering keys, e.g.:
        throwOnError : false
    }
    const $historyModal = $('#prompt_change_log_modal');
    const $historyDetailModal = $('#prompt_change_log_detail_modal');
    const $historyView = $('.history_view');

    $('.history-btn').on('click', function() {
        const appNo = $(this).data('app-no')
        const appName = $(this).data('app-name')
        $historyModal.find('.prompt_name').html(`${appName}修改记录`)
        $historyView.html('')

        $('#overlay').show();

        const url = `{% url 'debug_prompt_change_log' %}?app_no=${appNo}`;
        fetch(url, {method: 'get'})
        .then(response => response.json())
        .then(data => {
            let html = ''
            const changeLogs = data.data.change_logs;
            for (let i = 0; i < changeLogs.length; i++) {
                const changeLog = changeLogs[i];
                html += `
                    <div class="history_item">
                        <div class="history_time">修改时间：${changeLog.add_time}</div>
                        <div class="history_item_detail" data-id="${changeLog.id}">查看</div>
                    </div>
               `;
            }
            $historyView.html(html)
            $historyModal.show()
        })
        .catch(error => {
            $('#overlay').hide();
            console.error('Error:', error)
        });
    })
    $historyView.on('click', '.history_item_detail', function() {
        const logId = $(this).data('id');
        const url = `{% url 'debug_prompt_log' %}?log_id=${logId}`;
        fetch(url, {method: 'get'})
        .then(response => response.json())
        .then(data => {
            $('.history_detail').html(data.data.log)
            $historyDetailModal.show();
        })
        .catch(error => console.error('Error:', error));
    })
    $('.close-modal').on('click', function() {
        $('#overlay').hide();
        $(this).parents('.modal').hide()
    })
    $('.close-only-modal').on('click', function() {
        $(this).parents('.modal').hide()
    })

    // 章节笔记编辑按钮点击事件
    $('#chapter_note_edit_btn').click(function() {
        $('#chapter_note_edit_modal').show();
        $('#overlay').show();
        $('#chapter_note_prompt_input').val(temp_chapter_note_prompt);
    });

    $('#confirm_chapter_note_edit').click(function() {
        temp_chapter_note_prompt = $('#chapter_note_prompt_input').val();
        $('#chapter_note_edit_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_chapter_note_edit').click(function() {
        $('#chapter_note_edit_modal').hide();
        $('#overlay').hide();
    });

    // 随堂笔记编辑按钮点击事件
    $('#lecture_note_edit_btn').click(function() {
        $('#lecture_note_edit_modal').show();
        $('#overlay').show();
        $('#lecture_note_prompt_input').val(temp_lecture_note_prompt);
    });

    $('#confirm_lecture_note_edit').click(function() {
        temp_lecture_note_prompt = $('#lecture_note_prompt_input').val();
        $('#lecture_note_edit_modal').hide();
        $('#overlay').hide();
    });
    $('#cancel_lecture_note_edit').click(function() {
        $('#lecture_note_edit_modal').hide();
        $('#overlay').hide();
    });

    // 监听用户输入事件，移除空白和换行符
    questionInput.addEventListener('input', function() {
       // 移除所有空白和换行符
    });

    function parseFile(fileInputId, resultTextAreaId) {
        const fileInput = document.getElementById(fileInputId);
        const resultTextArea = document.getElementById(resultTextAreaId);

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);
        const url = "{% url 'parse_file' %}";
        fetch(url, {
            method: 'POST',
            body: formData,
        })
        .then(response => response.json())
        .then(data => {
            if (data.content) {
                resultTextArea.value = data.content;
            } else if (data.error) {
                alert(data.error);
            }
        })
        .catch(error => console.error('Error:', error));
    }

    function parseLectureSlides() {
        parseFile('lecture-slides-upload', 'user-question');
    }

    function submitQuestion() {
        const userQuestion = questionInput.value.trim();
        const modelProvider = document.getElementById('modelProviderSelect').value;
        if (!userQuestion) {
            alert("请输入内容！");
            return;
        }

        // 清空返回结果的内容
        resultView.innerHTML = ''; // 清空之前的结果
        tracingDetails.innerHTML = ''; // 清空之前的细节
        document.getElementById('saveChapterNoteContainer').style.display = 'none'; // 隐藏保存按钮

        $('#loadingOverlay').css('display', 'flex');

        message_id = null;
        const url = "{% url 'chapter_note_generation' %}";
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                chapter_note_generation: $('#chapter_note_prompt_input').val(),
                user_question: userQuestion,
                modelProvider: modelProvider
            })
        })
        .then(response => {
            $('#loadingOverlay').css('display', 'none');
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.body;
        })
        .then(stream => {
            $('#loadingOverlay').css('display', 'none');
            let streamComplete = false;
            const reader = stream.getReader();
            const decoder = new TextDecoder(); // 创建TextDecoder实例
            rawChapterNoteResult = ''; // 清空存储的原始数据
            let result_text = '';
            function readChunk() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        streamComplete = true;
                        console.log('Stream complete, returning collected data');
                        console.log('find_tracing function is called');
                        find_tracing()
                        // 显示保存按钮
                    document.getElementById('saveChapterNoteContainer').style.display = 'block';
                        return;
                    }
                    const textChunkStr = decoder.decode(value, { stream: true });

                    const chunkArr = textChunkStr.split("\n\n")
                    for (let i = 0; i < chunkArr.length; i++) {
                        const textChunk = chunkArr[i]
                        if (textChunk.startsWith("data: ")) {
                            // 去除前缀"data: "
                            const json_str = textChunk.slice(6);
                            console.log('json_str', json_str)
                            try {
                                // 将匹配到的字符串转换为JSON对象
                                const jsonData = JSON.parse(json_str);
                                if (!message_id) {
                                    message_id = jsonData.message_id
                                }
                                if (jsonData.answer) {
                                    rawChapterNoteResult += jsonData.answer; // 存储未渲染的内容
                                    result_text += jsonData.answer;
                                    resultView.innerHTML = marked.parse(result_text);
                                    renderMathInElement(resultView, katexOptions)
                                } else if (jsonData.err) {
                                    resultView.innerHTML = jsonData.err;
                                }
                            } catch (error) {
                                console.error('Error parsing JSON', json_str, error);
                            }
                        }
                    }

                    readChunk();
                }).catch(error => {
                    console.error('Error collecting stream data', error);
                });
            }
            readChunk();
        })
        .catch(error => {
            $('#loadingOverlay').css('display', 'none');
            console.error('Error:', error);
        });
    }

    let rawChapterNoteResult = ''; // 全局变量存储未渲染的章节笔记结果

    function find_tracing() {
        if (!message_id) {
            console.warn('Message ID is not set, cannot fetch tracing information.');
            return;
        }
        const $el = $('#tracingDetails');  // 指定展示追踪信息的容器

        console.log('Preparing to send AJAX request with Message ID:', message_id);
        $.ajax({
            type: 'GET',
            url: "{% url 'message_tracing' %}",
            data: { message_id: message_id },
            success: function(response) {
                $('#loadingOverlay').css('display', 'none');

                const results = response.data;

                $el.empty(); // 确保清空之前的内容

                if (results && results.tracing) {
                    results.tracing.forEach(function(traceDetail) {
                        console.log('Adding tracing detail:', traceDetail);
                        const detailElement = document.createElement('p');
                        detailElement.textContent = traceDetail;
                        $el.append(detailElement);
                    });
                } else {
                    $el.append('<p>没有找到追踪信息。</p>');
                }
            },
            error: function(xhr, status, error) {
                alert('请求失败');
                $('#loadingOverlay').css('display', 'none');
                console.error('AJAX Error:', xhr.responseText);
            }
        });
    }

    function find_lecture_tracing() {
        if (!message_id) return;
        const $el = $('#lectureTracingDetails');  // 指定展示追踪信息的容器
        $.ajax({
            type: 'GET',
            url: "{% url 'message_tracing' %}",
            data: { message_id: message_id },
            success: function(response) {
                $('#loadingOverlay').css('display', 'none');
                const results = response.data;

                $el.empty(); // 确保清空之前的内容

                if (results && results.tracing) {
                    results.tracing.forEach(function(traceDetail) {
                        const detailElement = document.createElement('p');
                        detailElement.textContent = traceDetail;
                        $el.append(detailElement);
                    });
                } else {
                    $el.append('<p>没有找到追踪信息。</p>');
                }
            },
            error: function(xhr, status, error) {
                alert('请求失败');
                $('#loadingOverlay').css('display', 'none');
                console.error('AJAX Error:', xhr.responseText);
            }
        });
    }

    function saveChapterNote() {
        const chapterNoteContent = document.getElementById('resultView').innerText; // 获取章节笔记内容
        saveToFile(chapterNoteContent, 'chapter_note.docx'); // 保存为文件
    }

    function saveLectureNote() {
        const lectureNoteContent = document.getElementById('lectureResultView').innerText; // 获取随堂笔记内容
        saveToFile(lectureNoteContent, 'lecture_note.docx'); // 保存为文件
    }

    function saveToFile(content, filename) {
        // 确保内容以 UTF-8 编码
        const blob = new Blob([new Uint8Array([0xEF, 0xBB, 0xBF]), content], { type: 'text/plain;charset=UTF-8' });

        // 如果是 .docx 文件，则应使用对应的 MIME 类型
        if (filename.endsWith('.docx')) {
            const blob = new Blob([content], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
        }

        const url = URL.createObjectURL(blob); // 生成下载链接

        // 创建隐藏的 <a> 标签并触发下载
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();

        // 清理资源
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    const $chapterLectureTextarea = $('#chapter_lecture')
    // 处理随堂笔记生成
    function genChapterLecture() {
        let courseChapterName = $('#course_chapter').val()
        if (!courseChapterName) {
            alert('请选择章！')
            return
        }

        const modelProvider = document.getElementById('lectureModelProviderSelect').value;
        const fileInput = document.getElementById('lecture-upload');
        const lecture_note_prompt = $('#lecture_note_prompt_input').val()

        if (!fileInput.files.length) {
            alert('请选择文件！')
            return
        }

        if (!confirm('确定要生成该章的随堂笔记吗？')) {
            return
        }

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);
        formData.append('chapter_name', courseChapterName);
        formData.append('modelProvider', modelProvider);
        formData.append('lecture_note_generation', lecture_note_prompt);

        $('#loadingOverlay').css('display', 'flex');

        const url = '{% url "lecture_note_generation_chapter" %}'
        _requestFormData(url, formData)
    }

    function genLectureBatch() {
        const fileInput = document.getElementById('lecture-upload');
        if (!fileInput.files.length) {
            alert('请选择文件！')
            return
        }

        if (!confirm('确定要生成全部随堂笔记吗？')) {
            return
        }

        const modelProvider = document.getElementById('lectureModelProviderSelect').value;
        const lecture_note_prompt = $('#lecture_note_prompt_input').val()

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);
        formData.append('modelProvider', modelProvider);
        formData.append('lecture_note_generation', lecture_note_prompt);

        $('#loadingOverlay').css('display', 'flex');

        const url = '{% url "lecture_note_generation_batch" %}'
        _requestFormData(url, formData)
    }

    function _requestJson(url, data) {
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data) // 将要发送的数据放在这里
        })
            .then(response => {
                if (response.ok) {
                    alert('提交成功')
                }
                throw new Error('网络请求失败');
            })
    }

    function _requestFormData(url, formData) {
        fetch(url, {
            method: 'POST',
            body: formData,
        })
            .then(response => {
                $('#loadingOverlay').css('display', 'none');
                if (response.ok) {
                    alert('提交成功')
                }
                throw new Error('网络请求失败');
            })
    }

</script>

</body>
</html>