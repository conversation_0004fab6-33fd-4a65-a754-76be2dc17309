from app.constants.app import AppMode
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.features.base_feature import BaseFeature
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate


class CodeOptimizationFeature(BaseFeature):

    def __init__(self):
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(app_no='code_optimization').first()
        if not prompt_template:
            raise Exception('prompt template not found')

        self.prompt_template = prompt_template

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            lang_code: str,
            lang_content: str,
            stream: bool,
            prompt_content: str = '',
    ):

        user_inputs = application_generate_entity.inputs.copy()
        user_inputs['code_lang'] = lang_code

        prompt_template = self.prompt_template
        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=prompt_content
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )
        # 调用模型
        return query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=lang_content,
            stream=stream
        )
