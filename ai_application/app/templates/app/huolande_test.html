<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>霍兰德职业兴趣测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2 {
            color: #2c3e50;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .activity {
            margin-bottom: 20px;
        }
        .question {
            margin-bottom: 10px;
            padding: 10px;
            background-color: white;
            border-radius: 3px;
        }
        .options {
            display: flex;
            gap: 15px;
        }
        .option {
            display: flex;
            align-items: center;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .ability-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin-top: 20px;
        }
        .ability-column {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .ability-name {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        .ability-option {
            margin: 5px 0;
            cursor: pointer;
        }
        .selected {
            color: #e74c3c;
            font-weight: bold;
        }
        .category-title {
            font-weight: bold;
            margin: 15px 0 10px 0;
            color: #2c3e50;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h1>霍兰德职业倾向测试</h1>
    <div class="intro">
        <p>同学你好：</p>
        <p>本测验量表将帮助您发现和确定自己的职业兴趣和能力特长，从而更好地做出求职择业的决策。如果您已经考虑好或选择好了自己的职业，本测验将使您的这种考虑或选择具有理论基础，或向您展示其他合适的职业；如果您至今尚未确定职业方向，本测验将帮助您根据自己的情况选择一个恰当的职业目标。</p>
        <p>本测验将从你的兴趣爱好、你的胜任能力、你的岗位偏好、你的能力特长四个方面来测验你的职业倾向，所有问题都没有时间限制，也没有对错之分，根据自己的第一反应作答即可。</p>
    </div>

    <form id="hollandTestForm">
        <!-- 活动一：喜欢/不喜欢 -->
        <div class="section">
            <h2>活动一：兴趣爱好</h2>
            <p>如果你喜欢做的某件事情，请选择"喜欢"；如果不喜欢，请选择"不喜欢"。</p>

            <div class="activity">
                <div class="category-title">R - 现实型活动</div>
                <div class="questions" id="part1_R"></div>
            </div>

            <div class="activity">
                <div class="category-title">I - 研究型活动</div>
                <div class="questions" id="part1_I"></div>
            </div>

            <div class="activity">
                <div class="category-title">A - 艺术型活动</div>
                <div class="questions" id="part1_A"></div>
            </div>

            <div class="activity">
                <div class="category-title">S - 社会型活动</div>
                <div class="questions" id="part1_S"></div>
            </div>

            <div class="activity">
                <div class="category-title">E - 企业型活动</div>
                <div class="questions" id="part1_E"></div>
            </div>

            <div class="activity">
                <div class="category-title">C - 常规型活动</div>
                <div class="questions" id="part1_C"></div>
            </div>
        </div>

        <!-- 活动二：能/不能 -->
        <div class="section">
            <h2>活动二：胜任能力</h2>
            <p>如果你曾经做过，或者认为自己能够胜任的某项活动，请选择"是"；把你没有做过，或者不能够做好的活动请选择"否"。</p>

            <div class="activity">
                <div class="category-title">R - 现实型能力</div>
                <div class="questions" id="part2_R"></div>
            </div>

            <div class="activity">
                <div class="category-title">I - 研究型能力</div>
                <div class="questions" id="part2_I"></div>
            </div>

            <div class="activity">
                <div class="category-title">A - 艺术型能力</div>
                <div class="questions" id="part2_A"></div>
            </div>

            <div class="activity">
                <div class="category-title">S - 社会型能力</div>
                <div class="questions" id="part2_S"></div>
            </div>

            <div class="activity">
                <div class="category-title">E - 企业型能力</div>
                <div class="questions" id="part2_E"></div>
            </div>

            <div class="activity">
                <div class="category-title">C - 常规型能力</div>
                <div class="questions" id="part2_C"></div>
            </div>
        </div>

        <!-- 活动三：职业偏好 -->
        <div class="section">
            <h2>活动三：职业偏好</h2>
            <p>把你喜欢或者吸引你的工作选择"是"；把你不喜欢或者不感兴趣的工作选择"否"。</p>

            <div class="activity">
                <div class="category-title">R - 现实型职业</div>
                <div class="questions" id="part3_R"></div>
            </div>

            <div class="activity">
                <div class="category-title">I - 研究型职业</div>
                <div class="questions" id="part3_I"></div>
            </div>

            <div class="activity">
                <div class="category-title">A - 艺术型职业</div>
                <div class="questions" id="part3_A"></div>
            </div>

            <div class="activity">
                <div class="category-title">S - 社会型职业</div>
                <div class="questions" id="part3_S"></div>
            </div>

            <div class="activity">
                <div class="category-title">E - 企业型职业</div>
                <div class="questions" id="part3_E"></div>
            </div>

            <div class="activity">
                <div class="category-title">C - 常规型职业</div>
                <div class="questions" id="part3_C"></div>
            </div>
        </div>

        <!-- 活动四：能力评估 -->
        <div class="section">
            <h2>活动四：能力评估</h2>
            <p>把你自己和同龄人进行比较，在每一种特质中找到最符合自己位置的等级。对你自己进行尽可能准确的评价。请为每种能力选择一个等级（1-7）。</p>

            <h3>能力评估一</h3>
            <div class="ability-grid" id="part4">
                <!-- 将通过JS动态生成 -->
            </div>

            <h3>能力评估二</h3>
            <div class="ability-grid" id="part5">
                <!-- 将通过JS动态生成 -->
            </div>
        </div>

        <div id="result" style="margin-top: 30px; display: none;">
        <h2>测试结果</h2>
        <pre id="resultContent"></pre>
        </div>


        <button type="button" id="submitTest">生成测试结果</button>
    </form>



    <script>
        // 测试题目数据
        const testData = {
            part1: {
                R: [
                    "安装电器",
                    "修理汽车",
                    "安装机械物品",
                    "用木头做物品",
                    "学习一门技术教育课程（如工艺雕刻等）",
                    "学习一门机械绘图课程",
                    "学习一门木料加工课程",
                    "学习一门自动机械化课程",
                    "同杰出的机械师和技术工人一起工作",
                    "户外工作",
                    "操作自动化设备"
                ],
                I: [
                    "阅读科技书籍或者杂志",
                    "在研究室或者图书馆工作",
                    "参与一项科学项目",
                    "研究科学理论",
                    "工作中接触化学制品",
                    "利用数学原理解决实际问题",
                    "学习一门物理课程",
                    "学习一门化学课程",
                    "学习一门数学课程",
                    "学习一门生物课程",
                    "研究深入的学术或科技问题"
                ],
                A: [
                    "素描、画图或者喷绘",
                    "设计家具、衣服或者海报",
                    "在乐队、团队或者交响乐团中表演",
                    "练习一种乐器",
                    "制作画像或者相片",
                    "撰写小说或者剧本",
                    "学习一门艺术课程",
                    "为任一类音乐谱曲或者编曲",
                    "与天才艺术家、作家或者雕刻家一起工作",
                    "为其他人表演(舞蹈、唱歌、表演等)",
                    "阅读艺术、文学或者音乐类文章"
                ],
                S: [
                    "会见重要的教育家或者治疗专家",
                    "阅读社会性文章或者书籍",
                    "为慈善团体工作",
                    "帮助别人解决私人问题",
                    "研究青少年犯罪问题",
                    "阅读心理学文章或者书籍",
                    "参与一门社会关系课程",
                    "在一所高中教书",
                    "指导精神病患者的活动",
                    "教育成年人",
                    "当一名自愿者"
                ],
                E: [
                    "了解商业成功策略方面知识",
                    "运作属于自己的服务机构或商业活动",
                    "参加销售会议",
                    "参加一个短期有关管理和领导能力的课程培训",
                    "在某一组织中当管理人员",
                    "监督其他人的工作",
                    "会见重要的行政官员或者领导者",
                    "领导某一组织完成某些目标",
                    "参加某一政治活动",
                    "担当一个组织或者企业的顾问",
                    "阅读商业杂志或者文章"
                ],
                C: [
                    "填写收入所得税表格",
                    "对商务或者簿记中的各类数据进行各种数学运算",
                    "操作办公用品",
                    "记录开销明细",
                    "建立一个档案保存系统",
                    "学习一门会计课程",
                    "学习一门商业数学课程",
                    "对货品供求做详细记录",
                    "检查货品文案记录的错误或者缺失",
                    "更新记录或者文档",
                    "在办公室工作"
                ]
            },
            part2: {
                R: [
                    "我曾使用过锯子、车床和磨沙机等木料加工工具",
                    "我会画比例图",
                    "我能够给小汽车加油或者更换轮胎",
                    "我曾操作过诸如钻孔机、磨具、缝纫机等工具",
                    "我可以修理家具或者木制品",
                    "我会一些简单的电器修理",
                    "我能够修理家里的家具",
                    "我会使用许多木工工具",
                    "我会简单的管道修理",
                    "我可以自己制作木制品",
                    "我可以装饰自己的房子或者公寓"
                ],
                I: [
                    "我能够利用代数知识去解决数学问题",
                    "我会做科学实验或者调查",
                    "我懂得放射性元素的半衰期",
                    "我会使用对数表",
                    "我能够使用电脑进行科学问题研究",
                    "我能够描述血液中白细胞的功能",
                    "我能够解释简单的化学方程式",
                    "我理解为什么人造卫星不会掉落到地面",
                    "我能够撰写科学报告",
                    "我理解这个宇宙中的'大爆炸'理论",
                    "我理解DNA在基因中的作用"
                ],
                A: [
                    "我会演奏一种乐器",
                    "我能够参与二声部或者四声部的合唱",
                    "我可以进行乐器独奏的演出",
                    "我能够在戏剧中扮演角色",
                    "我可以对文章进行口译",
                    "我会画油画、水彩画或者雕刻",
                    "我能够作曲或者编曲",
                    "我会设计衣服、海报或者家具",
                    "我能够写出好的故事或者诗歌",
                    "我会撰写演讲稿",
                    "我可以拍摄非常吸引人的照片"
                ],
                S: [
                    "我可以轻松的和不同类型的人交谈",
                    "我擅长给他人进行讲解",
                    "我可以为社区活动做组织工作",
                    "大家会喜欢向我倾吐自己的困惑",
                    "我可以轻松地教儿童学习一些东西",
                    "我可以轻松地教成人学习一些东西",
                    "我擅长帮助沮丧或者遇到困难的人",
                    "我对社会关系有很深入的理解",
                    "我擅长教导他人",
                    "我擅长让别人感觉轻松",
                    "我和他人一起工作时比一个人做事或思考更愉快"
                ],
                E: [
                    "我知道怎么才能够做一个成功的领导者",
                    "我是一个很好的演讲者",
                    "我能够管理好一场销售活动",
                    "我可以组织好其他人的工作",
                    "我是一个有抱负、果断的人",
                    "我善于让大家按照我的思路做事情",
                    "我是一个很好的推销人员",
                    "我是一个很好的辩论者",
                    "我具有很强的说服别人的能力",
                    "我有很强的规划能力",
                    "我有一些领导能力"
                ],
                C: [
                    "我能够整理好文档和其他的一些文件",
                    "我曾经做过办公室的工作",
                    "我会使用自动记账系统",
                    "我可以在短时间内做很多文字工作",
                    "我能够使用简单资料程序设备",
                    "我可以对信人和借出进行记账",
                    "我可以对支付或者销售记录列出详细清单",
                    "我可以利用电脑输入信息",
                    "我会撰写商业信函",
                    "我能够做一些常规的办公室事务",
                    "我是一个细心的且有条理的人"
                ]
            },
            part3: {
                R: [
                    "飞机机械工",
                    "汽车机械工",
                    "木匠",
                    "卡车司机",
                    "勘测员",
                    "建筑监理师",
                    "无线电机械工",
                    "铁路机车工程师",
                    "机械师",
                    "电工",
                    "农夫",
                    "直升机飞行员",
                    "电子技术人员",
                    "焊工"
                ],
                I: [
                    "气象学家",
                    "生物学家",
                    "天文学家",
                    "医学实验室技术人员",
                    "人类学家",
                    "化学家",
                    "独立研究科学家",
                    "科技文章作者",
                    "地理学家",
                    "植物学家",
                    "科学研究工作者",
                    "物理学家",
                    "社会学研究者",
                    "环境分析家"
                ],
                A: [
                    "诗人",
                    "音乐家",
                    "小说家",
                    "演员",
                    "自由撰稿人",
                    "音乐制作人",
                    "记者",
                    "艺术家",
                    "歌手",
                    "作曲家",
                    "雕刻家",
                    "剧作家",
                    "漫画家",
                    "演艺人员"
                ],
                S: [
                    "职业顾问",
                    "社会学家",
                    "中学教师",
                    "药物滥用咨询者",
                    "青少年犯罪专家",
                    "言语障碍矫正专家",
                    "婚姻顾问",
                    "临床心理学家",
                    "社会科学教师",
                    "私人顾问",
                    "青年野外生活指导者",
                    "社会工作者",
                    "康复顾问",
                    "运动指导者"
                ],
                E: [
                    "采购员",
                    "广告经理",
                    "制造商代表",
                    "商务总监",
                    "节目主持人",
                    "售货员",
                    "房地产销售人员",
                    "百货公司经理",
                    "销售经理",
                    "公共关系执行官",
                    "电视台经理",
                    "小型商业老板",
                    "立法委员",
                    "机场主管"
                ],
                C: [
                    "簿记员",
                    "预决算人员",
                    "注册会计师",
                    "信用调查员",
                    "银行柜员",
                    "税务专家",
                    "库存管理员",
                    "电脑操作人员",
                    "金融分析家",
                    "成本估算人员",
                    "薪酬管理人员",
                    "银行审查人员",
                    "记账人员",
                    "审计人员"
                ]
            },
            part4: [
                "机械能力",
                "科研能力",
                "艺术能力",
                "教育能力",
                "销售能力",
                "公关能力"
            ],
            part5: [
                "手工技能",
                "数学技能",
                "音乐技能",
                "交流理解技能",
                "管理技能",
                "办公技能"
            ]
        };

        // 渲染题目
        function renderQuestions() {
            // 活动一
            for (const category in testData.part1) {
                const container = document.getElementById(`part1_${category}`);
                testData.part1[category].forEach((question, index) => {
                    const questionDiv = document.createElement('div');
                    questionDiv.className = 'question';
                    questionDiv.innerHTML = `
                        <div>${question}</div>
                        <div class="options">
                            <div class="option">
                                <input type="radio" id="part1_${category}_${index}_like" name="part1_${category}_${index}" value="1">
                                <label for="part1_${category}_${index}_like">喜欢</label>
                            </div>
                            <div class="option">
                                <input type="radio" id="part1_${category}_${index}_dislike" name="part1_${category}_${index}" value="0">
                                <label for="part1_${category}_${index}_dislike">不喜欢</label>
                            </div>
                        </div>
                    `;
                    container.appendChild(questionDiv);
                });
            }

            // 活动二
            for (const category in testData.part2) {
                const container = document.getElementById(`part2_${category}`);
                testData.part2[category].forEach((question, index) => {
                    const questionDiv = document.createElement('div');
                    questionDiv.className = 'question';
                    questionDiv.innerHTML = `
                        <div>${question}</div>
                        <div class="options">
                            <div class="option">
                                <input type="radio" id="part2_${category}_${index}_yes" name="part2_${category}_${index}" value="1">
                                <label for="part2_${category}_${index}_yes">是</label>
                            </div>
                            <div class="option">
                                <input type="radio" id="part2_${category}_${index}_no" name="part2_${category}_${index}" value="0">
                                <label for="part2_${category}_${index}_no">否</label>
                            </div>
                        </div>
                    `;
                    container.appendChild(questionDiv);
                });
            }

            // 活动三
            for (const category in testData.part3) {
                const container = document.getElementById(`part3_${category}`);
                testData.part3[category].forEach((question, index) => {
                    const questionDiv = document.createElement('div');
                    questionDiv.className = 'question';
                    questionDiv.innerHTML = `
                        <div>${question}</div>
                        <div class="options">
                            <div class="option">
                                <input type="radio" id="part3_${category}_${index}_yes" name="part3_${category}_${index}" value="1">
                                <label for="part3_${category}_${index}_yes">是</label>
                            </div>
                            <div class="option">
                                <input type="radio" id="part3_${category}_${index}_no" name="part3_${category}_${index}" value="0">
                                <label for="part3_${category}_${index}_no">否</label>
                            </div>
                        </div>
                    `;
                    container.appendChild(questionDiv);
                });
            }

            // 活动四 - 第一部分
            const part4Container = document.getElementById('part4');
            testData.part4.forEach((ability, index) => {
                const column = document.createElement('div');
                column.className = 'ability-column';
                column.innerHTML = `<div class="ability-name">${ability}</div>`;

                for (let i = 7; i >= 1; i--) {
                    const option = document.createElement('div');
                    option.className = 'ability-option';
                    option.innerHTML = `
                        <input type="radio" id="part4_${index}_${i}" name="part4_${index}" value="${i}">
                        <label for="part4_${index}_${i}">${i}</label>
                    `;
                    column.appendChild(option);
                }

                part4Container.appendChild(column);
            });

            // 活动四 - 第二部分
            const part5Container = document.getElementById('part5');
            testData.part5.forEach((ability, index) => {
                const column = document.createElement('div');
                column.className = 'ability-column';
                column.innerHTML = `<div class="ability-name">${ability}</div>`;

                for (let i = 7; i >= 1; i--) {
                    const option = document.createElement('div');
                    option.className = 'ability-option';
                    option.innerHTML = `
                        <input type="radio" id="part5_${index}_${i}" name="part5_${index}" value="${i}">
                        <label for="part5_${index}_${i}">${i}</label>
                    `;
                    column.appendChild(option);
                }

                part5Container.appendChild(column);
            });
        }

        // 收集测试结果
        function collectTestResults() {
            const result = {
                results: {
                    part1: {
                        dimensions: {
                            realistic: [],
                            investigative: [],
                            artistic: [],
                            social: [],
                            enterprising: [],
                            conventional: []
                        }
                    },
                    part2: {
                        dimensions: {
                            realistic: [],
                            investigative: [],
                            artistic: [],
                            social: [],
                            enterprising: [],
                            conventional: []
                        }
                    },
                    part3: {
                        dimensions: {
                            realistic: [],
                            investigative: [],
                            artistic: [],
                            social: [],
                            enterprising: [],
                            conventional: []
                        }
                    },
                    part4: {
                        dimensions: {
                            realistic: 1,
                            investigative: 1,
                            artistic: 1,
                            social: 1,
                            enterprising: 1,
                            conventional: 1
                        }
                    },
                    part5: {
                        dimensions: {
                            realistic: 1,
                            investigative: 1,
                            artistic: 1,
                            social: 1,
                            enterprising: 1,
                            conventional: 1
                        }
                    }
                }
            };

            // 收集活动一
            const part1Map = {
                R: "realistic",
                I: "investigative",
                A: "artistic",
                S: "social",
                E: "enterprising",
                C: "conventional"
            };

            for (const category in testData.part1) {
                const dimension = part1Map[category];
                testData.part1[category].forEach((_, index) => {
                    const selected = document.querySelector(`input[name="part1_${category}_${index}"]:checked`);
                    result.results.part1.dimensions[dimension].push(selected ? parseInt(selected.value) : 0);
                });
            }

            // 收集活动二
            for (const category in testData.part2) {
                const dimension = part1Map[category];
                testData.part2[category].forEach((_, index) => {
                    const selected = document.querySelector(`input[name="part2_${category}_${index}"]:checked`);
                    result.results.part2.dimensions[dimension].push(selected ? parseInt(selected.value) : 0);
                });
            }

            // 收集活动三
            for (const category in testData.part3) {
                const dimension = part1Map[category];
                testData.part3[category].forEach((_, index) => {
                    const selected = document.querySelector(`input[name="part3_${category}_${index}"]:checked`);
                    result.results.part3.dimensions[dimension].push(selected ? parseInt(selected.value) : 0);
                });
            }

            // 收集活动四 - 第一部分
            const part4Map = {
                0: "realistic",
                1: "investigative",
                2: "artistic",
                3: "social",
                4: "enterprising",
                5: "conventional"
            };

            testData.part4.forEach((_, index) => {
                const selected = document.querySelector(`input[name="part4_${index}"]:checked`);
                const dimension = part4Map[index];
                result.results.part4.dimensions[dimension] = selected ? parseInt(selected.value) : 1;
            });

            // 收集活动四 - 第二部分
            const part5Map = {
                0: "realistic",
                1: "investigative",
                2: "artistic",
                3: "social",
                4: "enterprising",
                5: "conventional"
            };

            testData.part5.forEach((_, index) => {
                const selected = document.querySelector(`input[name="part5_${index}"]:checked`);
                const dimension = part5Map[index];
                result.results.part5.dimensions[dimension] = selected ? parseInt(selected.value) : 1;
            });

            return [result];
        }

    // 提交测试结果
    document.getElementById('submitTest').addEventListener('click', function() {
        const testResults = collectTestResults();
        const resultJson = JSON.stringify(testResults, null, 2);

        // 解析 JSON 字符串
        const parsedResult = JSON.parse(resultJson);

        // 显示结果
        document.getElementById('resultContent').textContent = `霍兰德测试结果为：${parsedResult[0].data}`;
        document.getElementById('result').style.display = 'block';

        // 发送到后端
        fetch('huolande_test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken') // 如果是Django需要CSRF token
            },
            body: JSON.stringify({ content: testResults })
        })
        .then(response => response.json())
        .then(data => {
            // 处理后端返回的结果
            document.getElementById('resultContent').textContent = `霍兰德测试结果为：${data.data}`;
            document.getElementById('result').style.display = 'block';
        })
        .catch(error => {
            console.error('Error:', error);
        });
    });


        // 获取CSRF token的函数（Django需要）
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // 页面加载时渲染题目
        document.addEventListener('DOMContentLoaded', renderQuestions);
    </script>
</body>
</html>