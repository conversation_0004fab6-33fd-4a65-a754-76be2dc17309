--
-- Create model MeetingVoiceRecord
--
CREATE TABLE `ai_meeting_voice_record` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `origin` varchar(100) NOT NULL, `msg_time` datetime(6) NULL, `voice_file` varchar(100) NOT NULL, `voice_content` longtext NULL, `is_convert_voice` bool NOT NULL, `is_extract_question` bool NOT NULL, `extract_question_status` varchar(100) NOT NULL, `extract_question_content` longtext NULL);
--
-- Create model MeetingQuestion
--
CREATE TABLE `ai_meeting_question` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `question_type` varchar(100) NOT NULL, `question_sub_type` varchar(100) NOT NULL, `question_content` longtext NULL, `meeting_record_id` bigint NOT NULL);
CREATE INDEX `ai_meeting_voice_record_add_time_020d5acc` ON `ai_meeting_voice_record` (`add_time`);
CREATE INDEX `ai_meeting_question_add_time_3e37454d` ON `ai_meeting_question` (`add_time`);
CREATE INDEX `ai_meeting_question_meeting_record_id_776b99e6` ON `ai_meeting_question` (`meeting_record_id`);