import json
import uuid

from django.db import models, transaction

from django_ext.utils.date_utils import utc2str
from .dataset import DatasetDocument
from ._base import BaseModel


class Account(BaseModel):
    name = models.Char<PERSON><PERSON>('名称', max_length=32)
    api_key = models.CharField(max_length=32, unique=True)
    secret_key = models.CharField(max_length=100, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '账号'
        db_table = 'ai_account'


class App(BaseModel):
    account = models.ForeignKey(Account, on_delete=models.CASCADE, db_constraint=False)
    app_no = models.CharField('应用id', max_length=32, unique=True)
    app_type = models.CharField('开发应用类型', max_length=32, blank=True)
    run_type = models.Char<PERSON>ield('应用方式', max_length=16, blank=True)
    mode = models.Char<PERSON><PERSON>('模式', max_length=32, blank=True)
    name = models.Char<PERSON>ield('应用名称', max_length=32, blank=True)
    is_public = models.BooleanField('是否公开', default=False)
    app_model_config = models.ForeignKey('AppModelConfig', on_delete=models.CASCADE, db_constraint=False)

    class Meta:
        verbose_name = verbose_name_plural = '应用'
        db_table = 'ai_app'

    @classmethod
    def gen_app_no(cls):
        uuid_str = uuid.uuid4().hex[:16]
        return f'app_{uuid_str}'

    @classmethod
    def create_coze_app(
            cls,
            app_type,
            app_name,
            bot_id
    ):
        # 默认提示词处理类型
        prompt_type = AppModelConfig.PromptType.for_params

        app_no = cls.gen_app_no()
        run_type = 'coze'

        model_config = AppModelConfig.objects.create(
            app_no=app_no,
            mode='chat',
            run_type=run_type,
            model_provider='',
            model_id='',
            third_app_key=bot_id,
            support_params=[],
            prompt_type=prompt_type,
            prompt_templates=[]
        )
        App.objects.create(
            account_id=1,
            app_no=app_no,
            app_type=app_type,
            run_type=run_type,
            name=app_name,
            mode='chat',
            app_model_config=model_config
        )

    @classmethod
    def create_native_app(
            cls,
            app_type,
            app_name,
            mode,
            prompt_type=None
    ):
        # 默认提示词处理类型
        if prompt_type is None:
            prompt_type = AppModelConfig.PromptType.for_workflow

        app_no = cls.gen_app_no()
        run_type = 'native'

        model_config = AppModelConfig.objects.create(
            app_no=app_no,
            mode=mode,
            run_type=run_type,
            model_provider='tongyi',
            model_id='qwen-turbo',
            support_params=[],
            prompt_type=prompt_type,
            prompt_templates=[]
        )
        App.objects.create(
            account_id=1,
            app_no=app_no,
            app_type=app_type,
            run_type=run_type,
            name=app_name,
            mode=mode,
            app_model_config=model_config
        )


class AppModelConfig(BaseModel):
    # for_workflow 工作流目前需要自定义代码逻辑，后期需要放入配置项
    class PromptType(models.TextChoices):
        for_params = ('for_params', '支持参数')
        for_template = ('for_template', '支持模板')
        for_workflow = ('for_workflow', '支持工作流')

    app_no = models.CharField('应用id', max_length=32, db_index=True)
    mode = models.CharField('模式', max_length=32)
    run_type = models.CharField('应用方式', max_length=16, blank=True)
    model_provider = models.CharField('模型提供方', max_length=32, blank=True)
    model_id = models.CharField('模型名称', max_length=64, blank=True)
    model_params = models.JSONField('模型参数', null=True)
    third_app_key = models.CharField('三方应用id', max_length=32, blank=True)
    support_params = models.JSONField('支持参数', null=True)
    prompt_type = models.CharField(
        '提示词类型', max_length=32,
        choices=PromptType.choices,
        default=PromptType.for_params
    )
    prompt_templates = models.JSONField('适用提示词模版', null=True)
    support_tools = models.JSONField('支持工具', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '应用模式配置'
        db_table = 'ai_app_model_config'

    @property
    def app(self) -> App | None:
        return App.objects.filter(app_no=self.app_no).first()

    @property
    def prompt_template_list(self) -> list:
        return self.prompt_templates or []

    @property
    def model_params_dict(self) -> dict:
        return self.model_params or {}


class PromptTemplate(BaseModel):
    name = models.CharField('标题', max_length=32, blank=True)
    app_no = models.CharField('应用id', max_length=32, db_index=True, blank=True)
    prompt_content = models.TextField('提示词内容')
    debug_prompt_content = models.TextField('调试提示词内容', null=True)
    is_enabled = models.BooleanField('是否启用', default=True)
    special_variables = models.JSONField('特殊变量', null=True)
    custom_variables = models.JSONField('自定义变量', null=True)
    model_provider = models.CharField('模型提供方', max_length=32, blank=True)
    model_id = models.CharField('模型名称', max_length=64, blank=True)
    model_params = models.JSONField('模型参数', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '提示词模版配置'
        db_table = 'ai_prompt_template'

    def get_debug_prompt_content(self):
        return self.debug_prompt_content or self.prompt_content

    @property
    def special_variable_list(self) -> list:
        return self.special_variables or []

    @property
    def custom_variable_list(self) -> list:
        return self.custom_variables or []

    @property
    def model_params_dict(self) -> dict:
        return self.model_params or {}

    @property
    def query_in_prompt(self) -> bool:
        return 'query' in self.special_variable_list

    def assemble_prompt(self, query, inputs: dict, prompt_content=None) -> str:
        if not prompt_content:
            if inputs.get('is_debug'):
                prompt_content = self.get_debug_prompt_content()
            else:
                prompt_content = self.prompt_content

        for special_variable in self.special_variable_list:
            if special_variable == 'query':
                prompt_content = prompt_content.replace('{{query}}', query)
        for custom_variable in self.custom_variable_list:
            if inputs.get(custom_variable) is None:
                raise ValueError(f'user inputs [{custom_variable}] is required')
            prompt_content = prompt_content.replace('{{' + custom_variable + '}}', inputs.get(custom_variable))
        return prompt_content

    @classmethod
    def update_debug_content(cls, app_no, content):
        prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no=app_no).first()
        if not prompt_template:
            return

        # 提示词内容未变动，则不更新
        if prompt_template.debug_prompt_content == content:
            return

        with transaction.atomic():
            prompt_template.debug_prompt_content = content
            prompt_template.save(update_fields=['debug_prompt_content'])
            PromptTemplateChangeLog.objects.create(
                prompt_template=prompt_template,
                is_debug=True,
                prompt_content=content
            )


class PromptTemplateChangeLog(BaseModel):
    prompt_template = models.ForeignKey(PromptTemplate, on_delete=models.CASCADE, db_constraint=False)
    is_debug = models.BooleanField('是否调试', default=False)
    prompt_content = models.TextField('提示词内容')
    model_params = models.JSONField('模型参数', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '提示词模版修改日志'
        db_table = 'ai_prompt_template_change_log'


class InvokeFrom(models.TextChoices):
    api = ('api', '接口调用')
    console = ('console', '后台调用')


class Conversation(BaseModel):
    conversation_no = models.CharField('会话id', max_length=36, unique=True)
    conversation_hash = models.CharField('会话Hash', max_length=255, blank=True)
    app = models.ForeignKey(App, on_delete=models.CASCADE, db_constraint=False)
    app_model_config = models.ForeignKey('AppModelConfig', on_delete=models.CASCADE, db_constraint=False)
    model_provider = models.CharField('模型提供方', max_length=32, blank=True)
    model_id = models.CharField('模型名称', max_length=64, blank=True)
    inputs = models.JSONField('表单参数', null=True)
    model_params = models.JSONField('模型参数', null=True)
    pre_prompt = models.TextField('预设提示', null=True)
    name = models.CharField('会话名称', max_length=200, blank=True)
    status = models.CharField('会话状态', max_length=32, default='normal')
    dataset = models.ForeignKey('app.Dataset', on_delete=models.CASCADE, db_constraint=False, null=True)
    document_nos = models.JSONField('文档编号列表', null=True)
    only_use_dataset = models.BooleanField('是否仅从本地知识库回答', default=False)
    from_account = models.ForeignKey(Account, on_delete=models.CASCADE, db_constraint=False)
    from_biz_id = models.CharField('来源业务id', max_length=32, blank=True)
    invoke_from = models.CharField('调用来源', max_length=32, choices=InvokeFrom.choices, default=InvokeFrom.api)
    scene_info = models.JSONField('业务场景', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '会话历史'
        db_table = 'ai_conversation'

    @property
    def document_no_list(self) -> list:
        return self.document_nos or []

    def get_scene_info(self) -> dict:
        return self.scene_info or {}


class Message(BaseModel):
    class MessageType(models.TextChoices):
        normal = ('normal', '普通消息')
        question = ('question', '解题助手')
        waikan = ('waikan', '外刊出题')
        waikan_again = ('waikan_again', '外刊出题-薄弱题型专项练习')
        code = ('code', '代码优化')
        math_question = ('math_question', '数学解题助手')
        grammar = ('grammar', '英语长难句语法分析')


    message_no = models.CharField('消息id', max_length=36, unique=True)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, db_constraint=False)
    conversation_hash = models.CharField('会话Hash', max_length=255, blank=True)
    app = models.ForeignKey(App, on_delete=models.CASCADE, db_constraint=False)
    app_model_config = models.ForeignKey('AppModelConfig', on_delete=models.CASCADE, db_constraint=False)
    model_provider = models.CharField('模型提供方', max_length=32, blank=True)
    model_id = models.CharField('模型名称', max_length=64, blank=True)
    inputs = models.JSONField('表单参数', null=True)
    query = models.TextField('用户输入', null=True)
    message_type = models.CharField(
        '消息类型', max_length=32, choices=MessageType.choices, default=MessageType.normal)
    file_objs = models.JSONField('文件对象', null=True)
    image_text = models.TextField('图片内容', null=True)
    message = models.JSONField('整体输入', null=True)
    message_tokens = models.IntegerField('消息tokens', default=0)
    answer = models.TextField('回答', null=True)
    answer_tokens = models.IntegerField('回答tokens', default=0)
    total_tokens = models.IntegerField('全部tokens', default=0)
    response_latency = models.FloatField('响应时间', default=0)
    # not_answered, normal, error, replaced
    status = models.CharField('消息状态', max_length=32, default='not_answered')
    stopped_by = models.CharField('停止原因', max_length=16, blank=True)
    error = models.TextField('错误信息', null=True)
    replaced_message_no = models.CharField('被替换的消息id', max_length=36, blank=True)
    is_query_rewrite = models.BooleanField('是否query改写', default=False)
    is_sensitive = models.BooleanField(default=False, verbose_name='是否为敏感内容')
    sensitive_content = models.TextField(null=True, verbose_name='敏感内容')
    is_answer_token_exceed = models.BooleanField(default=False, verbose_name='是否回答token接近上限')
    from_account = models.ForeignKey(Account, on_delete=models.CASCADE, db_constraint=False)
    from_biz_id = models.CharField('来源业务id', max_length=32, blank=True)
    invoke_from = models.CharField('调用来源', max_length=32, choices=InvokeFrom.choices, default=InvokeFrom.api)
    is_exception = models.BooleanField(default=False, verbose_name='是否异常')
    exception_reason = models.TextField(null=True, verbose_name='异常原因')
    exception_image = models.TextField(null=True, verbose_name='异常图片')
    userinfo = models.JSONField(null=True, verbose_name='使用着基本信息')
    chat_id = models.CharField(default='', max_length=64, verbose_name='coze chat id')
    malicious_attack_type = models.IntegerField(
        '恶意攻击类型',
        null=True,
        blank=True,
        choices=[(i, str(i)) for i in range(7)]  # 限制为 0-9 的枚举
    )
    malicious_attack_type_display = models.TextField('恶意攻击类型描述', null=True, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '会话消息'
        db_table = 'ai_message'

    @property
    def rag_message(self):
        return RagMessage.objects.filter(message=self, is_deleted=False).first()

    @property
    def query_rewrite(self):
        if not self.is_query_rewrite:
            return None
        return MessageQueryRewrite.objects.filter(message=self, is_deleted=False).first()

    @property
    def message_inputs(self) -> dict:
        return self.inputs or {}

    @property
    def file_objs_list(self) -> list:
        return self.file_objs or []

    @property
    def publish_message_id(self) -> str:
        message_id = self.message_no
        if self.replaced_message_no:
            message_id = self.replaced_message_no
        return message_id


class MessageQueryRewrite(BaseModel):
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, db_constraint=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, db_constraint=False)
    # 只取上一个消息的问答
    chat_history = models.JSONField('聊天历史', null=True)
    origin_query = models.TextField('原始query', null=True)
    prompt = models.TextField('提示词', null=True)
    new_query = models.TextField('改写后query', null=True)

    class Meta:
        verbose_name = verbose_name_plural = 'query改写'
        db_table = 'ai_message_query_rewrite'


class RagMessage(BaseModel):
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, db_constraint=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, db_constraint=False)
    # 根据搜索上下文问答
    content = models.JSONField('整体输入', null=True)
    answer = models.TextField('回答', null=True)
    message_tokens = models.IntegerField('消息tokens', default=0)
    answer_tokens = models.IntegerField('回答tokens', default=0)
    latency = models.FloatField('响应时间', default=0)
    is_model_rating = models.BooleanField('是否模型评分', default=False)
    model_score = models.IntegerField('模型得分', default=0)  # 0-100
    # 直接问答
    direct_content = models.JSONField('直接问答整体输入', null=True)
    direct_answer = models.TextField('直接回答', null=True)
    direct_message_tokens = models.IntegerField('消息tokens', default=0)
    direct_answer_tokens = models.IntegerField('回答tokens', default=0)
    direct_latency = models.FloatField('响应时间', default=0)

    class Meta:
        verbose_name = verbose_name_plural = 'RAG消息记录'
        db_table = 'ai_rag_message'


class MessageTracing(BaseModel):
    class TracingType(models.TextChoices):
        knowledge_query_split = ('knowledge_query_split', '问题关键词拆分')
        knowledge_query_split_json = ('knowledge_query_split_json', '问题关键词拆分Json')
        knowledge_query_split_ner = ('knowledge_query_split_ner', '问题拆分实体')
        knowledge_query_db = ('knowledge_query_db', '关键词查询数据库')
        knowledge_query_local = ('knowledge_query_local', '通用模式查询本地知识库')
        knowledge_query_llm = ('knowledge_query_llm', '通用模式查询LLM')
        knowledge_deep_query_local = ('knowledge_deep_query_local', '深度模式查询本地知识库')
        knowledge_deep_query_llm = ('knowledge_deep_query_llm', '深度模式查询本地知识库')
        knowledge_question_db = ('knowledge_question_db', '查询知识点真题')
        knowledge_deep_question = ('knowledge_deep_question', '输出真题解析')
        knowledge_deep_no_question = ('knowledge_deep_no_question', '未查询到真题')
        knowledge_question_video = ('knowledge_question_video', '知识点关联视频')

        question_ocr_result = ('question_ocr_result', 'OCR识别题目')
        evaluate_ocr_text_completeness = ('evaluate_ocr_text_completeness', 'OCR完整性结果判断')
        query_three_questions = ('query_three_questions', '查询3道相似题目')
        compare_user_input = ('compare_user_input', '筛选结果与原题比较')
        analysis_question = ('analysis_question', '解析题目')

        code_optimization = ('code_optimization', '代码优化')
        waikan_question_first_report = ('waikan_question_first_report', '外刊出题')
        waikan_question_second = ('waikan_question_second', '外刊出题-薄弱专项出题')
        waikan_report_second = ('waikan_report_second', '外刊出题-薄弱专项批改')
        complex_sentence_analysis = ('complex_sentence_analysis', '长难句解析')
        code_exercise = ('code_exercise', '代码练习')
        chat2_llm_ocr = ('chat2_llm_ocr', '知舟问答2-OCR识别')
        chat2_scan_llm = ('chat2_scan_llm', '知舟问答2-判断恶意攻击')
        chat2_determine_subject = ('chat2_determine_subject', '知舟问答2-分配科目')
        chat2_question_search = ('chat2_question_search', '知舟问答2-检索真题')
        chat2_answer = ('chat2_answer', '知舟问答2-回答问题')

    message = models.ForeignKey(Message, on_delete=models.CASCADE, db_constraint=False)
    # 目前：query_determine, query_rewrite, query_retrieval, context_answer, context_answer_score
    # 暂无：direct_answer
    type = models.CharField('类型', max_length=32, blank=True)
    query = models.TextField('用户输入', null=True)
    content = models.JSONField('整体输入', null=True)
    answer = models.TextField('回答', null=True)
    message_tokens = models.IntegerField('消息tokens', default=0)
    answer_tokens = models.IntegerField('回答tokens', default=0)
    total_tokens = models.IntegerField('全部tokens', default=0)
    latency = models.FloatField('响应时间', default=0)
    model_provider = models.CharField('模型提供方', max_length=32, blank=True)
    model_id = models.CharField('模型名称', max_length=64, blank=True)
    model_params = models.JSONField('模型参数', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '消息追踪记录'
        db_table = 'ai_message_tracing'

    def get_type_display(self) -> str:
        type_map = dict(MessageTracing.TracingType.choices)
        return type_map.get(self.type)

    def get_log_info(self) -> dict:
        log_type = self.get_type_display()
        input_content = self.query
        output_content = self.answer
        message_tokens = self.message_tokens
        answer_tokens = self.answer_tokens
        is_answer_markdown = False

        if self.type == 'knowledge_query_db':
            answer_json = json.loads(self.answer)
            if isinstance(answer_json, dict):
                output_content = '\n'.join(answer_json.keys())
            elif isinstance(answer_json, list):
                output_content = '\n'.join([i['name'] for i in answer_json])
        elif self.type == 'knowledge_query_local':
            is_answer_markdown = True
        elif self.type == 'knowledge_query_llm':
            is_answer_markdown = True
        elif self.type == 'knowledge_deep_query_local':
            is_answer_markdown = True
        elif self.type == 'knowledge_deep_query_llm':
            is_answer_markdown = True
        elif self.type == 'knowledge_question_db':
            answer_json = json.loads(self.answer)
            output_content = '\n'.join([i['question'] for i in answer_json])
        elif self.type == 'knowledge_deep_question':
            is_answer_markdown = True
        elif self.type == 'knowledge_deep_no_question':
            is_answer_markdown = True
        elif self.type == 'knowledge_question_video':
            document_nos = self.answer.split('\n') if self.answer else []
            if not document_nos:
                output_content = ''
            else:
                qs = DatasetDocument.objects.filter(document_no__in=document_nos)
                output_content = '\n'.join([i.name for i in qs])
        elif self.type == 'chat2_answer':
            is_answer_markdown = True

        return {
            'log_type': log_type,
            'log_type_code': self.type,
            'add_time': utc2str(self.add_time),
            'input_content': input_content,
            'output_content': output_content,
            'model_id': self.model_id,
            'message_tokens': message_tokens,
            'answer_tokens': answer_tokens,
            'is_answer_markdown': is_answer_markdown,
        }

    def get_log_display(self) -> str:
        if self.type == 'knowledge_query_split':
            return f'查询问题拆分：\n返回值：{self.answer}\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'knowledge_query_split_json':
            return f'解析查询问题拆分json：\n返回值：{self.answer}'
        elif self.type == 'knowledge_query_split_ner':
            return f'解析查询问题拆分实体：\n返回值：{self.answer}\n耗时：{self.latency}'
        elif self.type == 'knowledge_query_db':
            return f'查询本地数据库：\n返回值：{self.answer}\n耗时：{self.latency}'
        elif self.type == 'knowledge_query_local':
            return f'通用模式查询本地知识库：【{self.query}】\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'knowledge_query_llm':
            return f'通用模式查询LLM：【{self.query}】\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'knowledge_deep_query_local':
            return f'深度模式查询本地知识库：【{self.query}】\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'knowledge_deep_query_llm':
            return f'深度模式查询LLM：【{self.query}】\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'knowledge_question_db':
            return f'查询包含知识点【{self.query}】真题：\n返回值：{self.answer}\n耗时：{self.latency}'
        elif self.type == 'knowledge_deep_question':
            return f'查询到题目【{self.query}】\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'knowledge_deep_no_question':
            return f'未查询到【{self.query}】相关题目\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'knowledge_question_video':
            return f'查询到视频：【{self.answer}】'
        elif self.type == 'question_ocr_result':
            return f'OCR识别题目：【{self.answer}】\n耗时：{self.latency}'
        elif self.type == 'evaluate_ocr_text_completeness':
            return f'OCR结果判断：【{self.answer}】\n耗时：{self.latency}'
        elif self.type == 'query_three_questions':
            try:
                questions = json.loads(self.answer)
                questions_str = '\n\n'.join([f'问题{i}. {q["question"]}' for i, q in enumerate(questions, start=1)])
                return f'查询3道相似题目\n{questions_str}\n耗时：{self.latency}'
            except:
                return f'查询3道相似题目\n结果：{self.answer}\n耗时：{self.latency}'
        elif self.type == 'compare_three_inputs':
            return f'比较3道相似题目\n结果：{self.answer}\n耗时：{self.latency}'
        elif self.type == 'compare_user_input':
            return f'与原题比较\n结果：{self.answer}\n耗时：{self.latency}'
        elif self.type == 'analysis_question':
            return f'解析题目\n{self.query}\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'analysis_question_extra':
            return f'解析额外内容\n{self.query}\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'code_optimization':
            return f'代码优化\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'complex_sentence_analysis':
            return f'英语长难句语法分析\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'chapter_note_generation':
            return f'章节笔记生成\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'lecture_note_generation':
            return f'随堂笔记生成\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'math_problem_recognition':
            return f'数学题目识别\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'math_problem_solving':
            return f'数学解题助手\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'learning_report_generator_single':
            return f'基于试卷的学习报告生成\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'learning_report_generator_mul':
            return f'基于试卷和学习记录的数学测试报告生成\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'waikan_report_first':
            pass
        elif self.type == 'waikan_question_first_report':
            return f'外刊阅读\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'waikan_question_second':
            return f'外刊阅读薄弱项专项出题\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'waikan_report_second':
            return f'外刊阅读专项出题批改\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'college_analysis':
            return f'院校分析助手\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'kaoyan_review_plan':
            return f'考研复习计划\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
        elif self.type == 'code_exercise':
            return f'代码练习\n消耗token：{self.answer_tokens}\n耗时：{self.latency}'
            


class MessageToolCall(BaseModel):
    message = models.ForeignKey(Message, on_delete=models.CASCADE, db_constraint=False)
    tool_call_id = models.CharField('工具id', max_length=100, blank=True)
    name = models.CharField('工具名称', max_length=100, blank=True)
    arguments = models.CharField('工具调用参数', max_length=255, blank=True)
    content = models.TextField('工具调用结果', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '消息工具调用'
        db_table = 'ai_message_tool_call'


class SensitiveConfig(BaseModel):
    app = models.ForeignKey(App, on_delete=models.CASCADE, db_constraint=False)
    words = models.JSONField(null=True, verbose_name='敏感词列表')
    white_words = models.JSONField(null=True, verbose_name='白名单列表')

    class Meta:
        verbose_name = verbose_name_plural = '敏感词配置'
        db_table = 'ai_sensitive_config'

    @property
    def words_list(self) -> list:
        return self.words or []

    @property
    def white_words_list(self) -> list:
        return self.white_words or []


class MessageTask(BaseModel):
    class ProcessStatus(models.TextChoices):
        waiting = ('waiting', '未开始')
        ing = ('ing', '进行中')
        success = ('success', '成功')
        fail = ('fail', '失败')

    message = models.ForeignKey(Message, on_delete=models.CASCADE, db_constraint=False)
    task_id = models.CharField('任务id', max_length=36, unique=True, null=None)
    process_status = models.CharField(
        '执行状态', max_length=16, choices=ProcessStatus.choices, default='waiting', db_index=True)
    fail_reason = models.TextField('失败原因', null=True)
    retry_times = models.IntegerField('重试次数', default=0)
    last_retry_time = models.DateTimeField('最后重试时间', null=True)
    next_retry_time = models.DateTimeField(
        '下一次重试时间', null=True, db_index=True)

    class Meta:
        verbose_name = verbose_name_plural = '模型消息任务表'
        db_table = 'ai_message_task'


class QuestionNewRecord(BaseModel):
    message = models.ForeignKey(Message, on_delete=models.CASCADE, db_constraint=False, null=True)
    question_pic_url = models.CharField('题目链接', max_length=255)
    question_content = models.TextField('题目内容', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '新题记录'
        db_table = 'ai_question_new_record'


class UnifiedExaminationQuestion(BaseModel):

    class LibraryType(models.IntegerChoices):
        CS408 = (1, '计算机408')

    question_num = models.IntegerField(verbose_name='题目编号')
    question_no = models.IntegerField(verbose_name='真实的题目编号', default=0)
    library = models.IntegerField(verbose_name='题库类型', choices=LibraryType.choices, default=LibraryType.CS408)
    year = models.CharField(verbose_name='年份', default='', max_length=4)
    lost_result = models.BooleanField(verbose_name='是否确实数据', default=False)
    has_llm_analysis = models.BooleanField(verbose_name='是否使用llm提取解析', default=False)
    title = models.TextField(verbose_name='题干', null=True)
    answer_body = models.TextField(verbose_name='answer_body', null=True)
    choice_body = models.TextField(verbose_name='choice_body', null=True)
    analysis = models.TextField(verbose_name='解析', null=True)
    content = models.JSONField(verbose_name='题目内容', null=True)
    llm_analysis = models.TextField(verbose_name='大模型解析结果', null=True)

    @property
    def answer(self) -> list[str]:
        if self.answer_body:
            try:
                return json.loads(self.answer_body)
            except Exception:
                return []
        else:
            return []

    @property
    def choice(self):
        if self.choice_body:
            try:
                return json.loads(self.choice_body)
            except Exception:
                return []
        else:
            return []

    class Meta:
        verbose_name = verbose_name_plural = '统考真题'
        db_table = 'unified_examination_question'


class zhihenRadar(BaseModel):

    assistant_id = models.CharField(verbose_name='助教id', max_length=36, db_index=True, blank=True)
    subject_id = models.CharField(verbose_name='学科id', max_length=36,blank=True)
    question_id = models.CharField(verbose_name='问题id', max_length=36, blank=True)
    question = models.TextField(verbose_name='问题', null=True)
    answer_id = models.CharField(verbose_name='回答id', max_length=36, null=None)
    answer = models.TextField(verbose_name='回答', null=True)
    status = models.IntegerField(verbose_name='是否为AI',default=-1)
    explain = models.TextField(verbose_name='解释', null=True)
    analysis = models.TextField(verbose_name='具体判断',max_length=36,blank=True,default="None")

    class Meta:
        verbose_name = verbose_name_plural = '智痕雷达'
        db_table = 'ai_zhihen_radar'


class KnowledgePointPsychology(BaseModel):
    lession_name = models.CharField(verbose_name='课节名称', max_length=36, db_index=True)
    knowledge_point = models.JSONField(verbose_name='知识点')

    class Meta:
        verbose_name = verbose_name_plural = '学科出题'
        db_table = 'ai_knowledge_psychology'


class GenerateQuestion(BaseModel):
    subject_id = models.CharField(verbose_name='学科id', max_length=36, db_index=True)
    main_subject = models.CharField(verbose_name='学科名称', max_length=36,null=True)
    question = models.TextField(verbose_name='题干', null=True)
    options = models.TextField(verbose_name='选项', null=True)
    answer = models.CharField(verbose_name='答案', max_length=36, null=True)
    analysis = models.TextField(verbose_name='解析', null=True)
    knowledge = models.CharField(verbose_name='知识点', max_length=36, null=True)
    error_point = models.TextField(verbose_name='易错点', null=True)
    course_section_id = models.CharField(verbose_name='课节id', max_length=36, null=True)
    chapter = models.CharField(verbose_name='课节名称', max_length=36, null=True)

    class Meta:
        verbose_name = verbose_name_plural = '知识点出题'
        db_table = 'ai_generate_question'


class CoursePackageContent(BaseModel):
    unit_name = models.CharField(verbose_name='单元名称', max_length=36, db_index=True)
    subject = models.CharField(verbose_name='学科', max_length=36, null=True)
    course_package_name = models.CharField(verbose_name='课包名称', max_length=36, null=True)
    stage_name = models.CharField(verbose_name='阶段名称', max_length=36, null=True)
    combine_unit_name = models.CharField(verbose_name='组合单元名称', max_length=36, null=True)
    study_target = models.TextField(verbose_name='学习目标', null=True)
    study_guidence = models.TextField(verbose_name='学习指导', null=True)
    unit_task = models.TextField(verbose_name='单元任务', null=True)
    unit_date = models.CharField(verbose_name='单元跨度时间', max_length=36, null=True)
    important_sections = models.TextField(verbose_name='重点章节', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '往年课包内容'
        db_table = 'ai_course_package_content'


class CoursePackageWithTime(BaseModel):
    subject = models.CharField(verbose_name='学科', max_length=36, null=True)
    course_date = models.CharField(verbose_name='课程时间', max_length=36, null=True)
    course_name = models.TextField(verbose_name='课程名称', null=True)

    class Meta:
        verbose_name = verbose_name_plural = '往年课包课程添加时间'
        db_table= 'ai_course_package_time'


class KaoGangAnalysis(BaseModel):
    subject = models.CharField(verbose_name='学科', max_length=36, null=True)
    kaogang_content = models.TextField(verbose_name='考纲内容', null=True)
    kaogang_logic = models.TextField(verbose_name='考纲科目逻辑', null=True)
    class Meta:
        verbose_name = verbose_name_plural = '考纲分析'
        db_table = 'ai_kaogang_analysis'


class CoursePackageContentByChapter(BaseModel):
    chapter_name = models.CharField(verbose_name='章节名称', max_length=36, db_index=True)
    unit_name = models.CharField(verbose_name='单元名称', max_length=36, db_index=True)
    subject = models.CharField(verbose_name='学科', max_length=36, null=True)
    course_package_name = models.CharField(verbose_name='课包名称', max_length=36, null=True)
    stage_name = models.CharField(verbose_name='阶段名称', max_length=36, null=True)
    combine_unit_name = models.CharField(verbose_name='组合单元名称', max_length=36, null=True)
    study_target = models.TextField(verbose_name='学习目标', null=True)
    study_guidence = models.TextField(verbose_name='学习指导', null=True)
    chapter_task = models.TextField(verbose_name='章节任务', null=True)
    chapter_date = models.CharField(verbose_name='章节跨度时间', max_length=36, null=True)
    spend_time = models.CharField(verbose_name='学习时长', max_length=36, null=True)
    major_or_minor = models.CharField(verbose_name='重要程度', max_length=36, null=True)
    knowledge_exam_count_and_avg_difficulity = models.JSONField(verbose_name='对应章节考察知识点考频及平均难度', max_length=108, null=True)
    important_degree = models.CharField(verbose_name='考频重要程度', max_length=36, null=True)

    class Meta:
        verbose_name = verbose_name_plural = '往年课包内容'
        db_table = 'ai_course_package_content_by_chapter'


class PersonalizedExamSyllabus(BaseModel):
    student_score = models.IntegerField(verbose_name='学生总分', null=True)
    exam_syllabus = models.TextField(verbose_name='个性化考纲', null=True)
    subject = models.CharField(verbose_name='学科', max_length=36, null=True)
    class Meta:
        verbose_name = verbose_name_plural = '个性化考纲'
        db_table = 'ai_personalized_exam_syllabus'