import re

from bs4 import BeautifulSoup

from app.core.rag.models.document import Document


class KnowledgeService:

    @classmethod
    def get_knowledge_from_document(cls, documents: list[Document]) -> list:
        knowledge_list = []
        # 正则表达式匹配表格
        table_pattern = r'\|[^|]+\|[^|]+\|\n\|-+\|-+\|\n((\|[^|]+\|[^|]+\|\n?)+)'
        rows_pattern = r'\|([^|]+)\|([^|]+)\|'
        for doc in documents:
            if '<table>' in doc.page_content:
                soup = BeautifulSoup(doc.page_content, 'html.parser')
                for table in soup.find_all('table'):
                    table_data = []
                    for row in table.find_all('tr'):
                        row_data = []
                        for cell in row.find_all('td'):
                            cell_text = cell.get_text(separator='\n', strip=False)
                            row_data.append(cell_text)
                        if row_data:
                            table_data.append(row_data)
                    for i in table_data:
                        if len(i) != 2:
                            continue
                        name = i[0].strip()
                        definition = i[1].strip().replace('\\n', '\n')

                        knowledge_list.append({
                            'name': name,
                            'definition': definition,
                        })
            else:
                # doc_str = doc.page_content.replace('\r\n', '\n').replace('\r', '\n')
                # rows = re.findall(r'\|(.*?)\|(.*?)\|\n', doc_str)

                rows = doc.page_content.split('\n')
                # 提取并打印每一行的数据
                for row in rows[2:]:
                    # name, definition = row
                    if row[0] == '|':
                        row = row[1:]
                    if row[-1] == '|':
                        row = row[:-1]

                    columns = row.split('|')
                    if len(columns) < 2:
                        continue
                    name = columns[0]
                    definition = '|'.join(columns[1:])

                    name = name.strip()
                    definition = definition.strip()

                    definition = definition.replace('\\n', '\n')
                    # 定义需要处理块级别公式
                    if '$$' in definition:
                        cleaned_definition_arr = []
                        definition_arr = definition.split('\n')
                        for definition_item in definition_arr:
                            # 去除 $$前的空格
                            if '$$' in definition_item:
                                cleaned_text = definition_item.lstrip()
                                cleaned_definition_arr.append(cleaned_text)
                            else:
                                cleaned_definition_arr.append(definition_item)
                        definition = '\n'.join(cleaned_definition_arr)

                    #     # 提取整个表格
                    # table_match = re.search(table_pattern, doc_str)
                    # if not table_match:
                    #     continue
                    #
                    # table_str = table_match.group(1)
                    #
                    # for row_str in table_str.split('\n'):
                    #     rows_matches = re.search(rows_pattern, row_str)
                    #     if not rows_matches:
                    #         continue
                    #     name = rows_matches.group(1).strip().replace('\\n', '\n')

                    knowledge_list.append({
                        'name': name,
                        'definition': definition,
                    })

        return knowledge_list

    @classmethod
    def replace_latex(cls, match):
        latex_pattern = r'\$[^$]+?\$'
        latex_formulas = re.findall(latex_pattern, match, re.DOTALL)
        for formula in latex_formulas:
            # print({'t': formula})
            # print({'t': formula.replace("\\", "\\")})
            match = match.replace(formula, formula.replace('\\\\', '\\'))
        return match.replace('\\n', '\n')
