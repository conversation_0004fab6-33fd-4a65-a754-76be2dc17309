import json
import random
import time

from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.features.base_feature import BaseFeature
from app.core.rag.index_processor.constant.index_type import IndexType
from app.models import DatasetDocument, Knowledge


class KnowledgeQueryDBFeature(BaseFeature):

    def _knowledge_contain_keywords(self, kg_name: str, keywords: list):
        kws = []
        for i in keywords:
            if i in kg_name:
                kws.append(i)
        return kws

    def _get_knowledge_contain_kws(self, all_kgs: list[Knowledge], keywords: list) -> dict:
        res = {}
        for kg in all_kgs:
            contain_keywords = self._knowledge_contain_keywords(kg.name, keywords)
            res[kg.id] = {
                'kg': kg,
                'contain_keywords': contain_keywords,
            }
        return res

    def _get_max_contain_kw(self, kgs_with_kws: dict, keywords: list):
        # 包含全部关键词的知识点
        max_contain_kws = []
        max_contain_kw_kg = None
        for kg_info in kgs_with_kws.values():
            kg = kg_info['kg']
            contain_keywords = list(set(kg_info['contain_keywords']) & set(keywords))
            if len(contain_keywords) > len(max_contain_kws):
                max_contain_kws = contain_keywords
                max_contain_kw_kg = kg
        # 特殊处理知识点和关键词一样的场景
        if len(max_contain_kws) == 1:
            for kg_info in kgs_with_kws.values():
                if kg_info['kg'].name == max_contain_kws[0]:
                    max_contain_kw_kg = kg_info['kg']
                    break

        return max_contain_kw_kg, max_contain_kws

    def _get_main_knowledge_list(self, kgs_with_kws: dict, keywords: list) -> list:
        kws = keywords.copy()
        res = []
        while len(kws):
            kg, max_contain_kws = self._get_max_contain_kw(kgs_with_kws, kws)
            if not max_contain_kws:
                break
            res.append({
                'kg': kg,
                'max_contain_kws': max_contain_kws,
            })
            kws = list(set(kws) - set(max_contain_kws))
        return res

    def _get_other_kgs(self, kgs_with_kws: dict, keywords: list, exclude_kg_ids: list, size=3) -> list:
        kgs = []
        for kg_info in kgs_with_kws.values():
            kg = kg_info['kg']
            if kg.id in exclude_kg_ids:
                continue

            if set(kg_info['contain_keywords']) & set(keywords):
                kgs.append(kg)

        return random.sample(kgs, k=size) if len(kgs) > size else kgs

    def run(
            self,
            query: str,
            keywords: list,
            original_knowledge_document_no: str,
            is_recommend: bool = False,
            tracing_log: MessageTracingLog | None = None
    ) -> list:
        if not keywords:
            return []

        start_time = time.perf_counter()
        kg_document = DatasetDocument.objects.filter(
            index_type=IndexType.KNOWLEDGE_INDEX.value,
            original_knowledge_document_no=original_knowledge_document_no,
            is_deleted=False
        ).first()

        kg_results = []
        if is_recommend:
            kgs = Knowledge.objects.filter(dataset_document=kg_document, name__in=keywords)
            for kg in kgs:
                kg_results.append({
                    'name': kg.name,
                    'definition': kg.definition,
                    'others': [],
                })
        else:
            all_kgs_map = {}
            for k in keywords:
                # 暂时最多查出25个相关的知识点
                kgs = Knowledge.objects.filter(dataset_document=kg_document, name__icontains=k)[:10]
                for kg in kgs:
                    all_kgs_map[kg.id] = kg

            all_kgs = list(all_kgs_map.values())
            kgs_with_kws = self._get_knowledge_contain_kws(all_kgs, keywords)

            main_knowledge_list = self._get_main_knowledge_list(kgs_with_kws, keywords)

            if main_knowledge_list:
                # 有用的的知识点
                exist_kg_ids = [i['kg'].id for i in main_knowledge_list]

                # 获取主体知识点的推荐知识点
                not_exist_kws = set(keywords)
                for i in main_knowledge_list:
                    other_kgs_ = self._get_other_kgs(kgs_with_kws, i['max_contain_kws'], exist_kg_ids, size=3)
                    exist_kg_ids.extend([i.id for i in other_kgs_])
                    kg_results.append({
                            'name': i['kg'].name,
                            'definition': i['kg'].definition,
                            'others': [j.name for j in other_kgs_],
                    })

                    not_exist_kws = not_exist_kws - set(i['max_contain_kws'])
                not_exist_kws = list(not_exist_kws)
            else:
                not_exist_kws = keywords

            if not_exist_kws:
                for k in not_exist_kws:
                    kg_results.append({
                        'name': k,
                        'definition': '',
                        'others': [],
                    })

        latency = time.perf_counter() - start_time
        if tracing_log:
            tracing_log.add_tracing(
                tracing_type='knowledge_query_db',
                query=json.dumps(keywords, ensure_ascii=False),
                answer=json.dumps(kg_results, ensure_ascii=False),
                latency=latency,
            )
        return kg_results
