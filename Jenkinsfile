node('jnlp') {
    stage('Prepare') {
        echo "1.Prepare Stage"
        checkout scm
        script {
            build_tag = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()

            build_tag = "${env.BRANCH_NAME}-${build_tag}"
        }
    }
    stage('Test') {
      echo "2.Test Stage"
    }
    stage('Build') {
        echo "3.Build Docker Image Stage"
        withCredentials([usernamePassword(credentialsId: 'AliyunDocker', passwordVariable: 'AliyunDockerPassword', usernameVariable: '<PERSON>yunDockerUser')]) {
            sh "docker login --username=${AliyunDockerUser} -p ${AliyunDockerPassword} registry.cn-shanghai.aliyuncs.com"
            sh "docker build -t registry.cn-shanghai.aliyuncs.com/yantu_k8s/yantucs-ai-app:${build_tag} ."
        }
    }
    stage('Push') {
        echo "4.Push Docker Image Stage"
        withCredentials([usernamePassword(credentialsId: '<PERSON>yunDocker', passwordVariable: '<PERSON><PERSON><PERSON>ockerPassword', usernameVariable: '<PERSON>yunDockerUser')]) {
            sh "docker login --username=${AliyunDockerUser} -p ${AliyunDockerPassword} registry.cn-shanghai.aliyuncs.com"
            sh "docker push registry.cn-shanghai.aliyuncs.com/yantu_k8s/yantucs-ai-app:${build_tag}"
        }
    }
    stage('Deploy') {
        echo "5. Deploy Stage"
         if (env.BRANCH_NAME == 'preview') {
             sh "sed -i 's/<BUILD_TAG>/${build_tag}/' ./k8s/preview.yaml"
             sh "kubectl apply -f ./k8s/preview.yaml --record"
        } else {
             sh "sed -i 's/<BUILD_TAG>/${build_tag}/' ./k8s/dev.yaml"
             sh "kubectl apply -f ./k8s/dev.yaml --record"
        }
    }

    stage('SonarQube analysis') {
        def scannerHome = tool 'SonarScanner';
        withSonarQubeEnv('SonarQube') {
          withEnv(["JAVA_HOME=/appstore/jenkins20220403/java/graalvm-jdk-17.0.10+11.1/"]){
            sh "${scannerHome}/bin/sonar-scanner -Dsonar.token=sqp_5951eb9a1954368b6d5b82c8212f02583a91af9b"
          }
        }
    }
}