# Generated by Django 4.2 on 2025-04-02 02:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0035_cozeworkflowresult_process_fail_reason_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnWordReciteBasicAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(db_index=True, max_length=100, verbose_name='用户id')),
                ('right_num', models.IntegerField(default=0, verbose_name='正确数量')),
            ],
            options={
                'verbose_name': '摸底测试答卷',
                'verbose_name_plural': '摸底测试答卷',
                'db_table': 'ai_english_word_recite_basic_answer',
            },
        ),
        migrations.CreateModel(
            name='EnWordReciteBasicPaper',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_num', models.IntegerField(default=0, verbose_name='题目数量')),
            ],
            options={
                'verbose_name': '摸底测试卷',
                'verbose_name_plural': '摸底测试卷',
                'db_table': 'ai_english_word_recite_basic_paper',
            },
        ),
        migrations.CreateModel(
            name='EnWordRecitePlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_id', models.CharField(db_index=True, max_length=100, verbose_name='用户id')),
                ('gen_plan_content', models.TextField(null=True, verbose_name='生成计划内容')),
                ('plan_content', models.JSONField(null=True, verbose_name='计划详情')),
                ('recite_words', models.JSONField(null=True, verbose_name='背诵单词列表')),
                ('basic_answer', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitebasicanswer')),
            ],
            options={
                'verbose_name': '背单词计划',
                'verbose_name_plural': '背单词计划',
                'db_table': 'ai_english_word_recite_plan',
            },
        ),
        migrations.CreateModel(
            name='EnWordReciteQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('question_type', models.CharField(blank=True, max_length=16, verbose_name='问题类型')),
                ('question', models.TextField(null=True, verbose_name='题干')),
                ('options', models.JSONField(null=True, verbose_name='选项')),
                ('analysis', models.TextField(null=True, verbose_name='解析')),
                ('answer', models.TextField(null=True, verbose_name='答案')),
                ('word', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordlibrary')),
            ],
            options={
                'verbose_name': '英语背单词题目',
                'verbose_name_plural': '英语背单词题目',
                'db_table': 'ai_english_word_recite_question',
            },
        ),
        migrations.CreateModel(
            name='EnWordRecitePlanRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('gen_plan_content', models.TextField(null=True, verbose_name='生成计划内容')),
                ('plan_content', models.JSONField(null=True, verbose_name='计划详情')),
                ('recite_words', models.JSONField(null=True, verbose_name='背诵单词列表')),
                ('plan', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordreciteplan')),
            ],
            options={
                'verbose_name': '背单词计划调整记录',
                'verbose_name_plural': '背单词计划调整记录',
                'db_table': 'ai_english_word_recite_plan_record',
            },
        ),
        migrations.CreateModel(
            name='EnWordReciteDayRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('day', models.DateField(null=True, verbose_name='背诵日期')),
                ('day_seq', models.IntegerField(default=1, verbose_name='第几日')),
                ('user_answer', models.CharField(blank=True, max_length=16, verbose_name='用户答案')),
                ('is_right', models.BooleanField(default=False, verbose_name='是否正确')),
                ('plan', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordreciteplan')),
                ('plan_record', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordreciteplanrecord')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitequestion')),
                ('word', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordlibrary')),
            ],
            options={
                'verbose_name': '用户每日背诵记录',
                'verbose_name_plural': '用户每日背诵记录',
                'db_table': 'ai_english_word_recite_day_record',
            },
        ),
        migrations.CreateModel(
            name='EnWordReciteBasicPaperDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('basic_paper', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitebasicpaper')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitequestion')),
            ],
            options={
                'verbose_name': '摸底测试卷详情',
                'verbose_name_plural': '摸底测试卷详情',
                'db_table': 'ai_english_word_recite_basic_paper_detail',
            },
        ),
        migrations.CreateModel(
            name='EnWordReciteBasicAnswerDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('user_answer', models.CharField(blank=True, max_length=16, verbose_name='用户答案')),
                ('is_right', models.BooleanField(default=False, verbose_name='是否正确')),
                ('is_answered', models.BooleanField(default=False, verbose_name='是否答题')),
                ('basic_answer', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitebasicanswer')),
                ('basic_paper', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitebasicpaper')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitequestion')),
                ('word', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordlibrary')),
            ],
            options={
                'verbose_name': '摸底测试答卷详情',
                'verbose_name_plural': '摸底测试答卷详情',
                'db_table': 'ai_english_word_recite_basic_answer_detail',
            },
        ),
        migrations.AddField(
            model_name='enwordrecitebasicanswer',
            name='basic_paper',
            field=models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitebasicpaper'),
        ),
    ]
