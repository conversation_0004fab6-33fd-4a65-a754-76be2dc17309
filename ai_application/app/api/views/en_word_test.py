import random
from collections import defaultdict

from django.core.cache import cache
from django.db import transaction
from django.db.models import Count

from app.api.dto import TestPaperSubmitDto, QuestionAnswerDto
from app.api.serializers import EnglishWordTestQuestionSerializer
from app.api.validators import TestPaperSubmitValidator, QuestionAnswerValidator
from app.models import (
    EnglishWordTestQuestion, EnglishWordLibrary, EnglishWordTestRecord, EnglishWordTestRecordDetail,
    EnglishWordTestAnswer, EnglishWordTestAnswerDetail, EnglishWordWrongQuestion
)
from app.errors import ParameterError
from django_ext import base_view
from django_ext.response import make_response


class EnglishWordQuestionView(base_view.BaseView):
    serializer_class = EnglishWordTestQuestionSerializer
    """
    按照高中低频（10:3:2）随机出题，默认出15道题，每个单词一道题
    """

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        if not EnglishWordTestQuestion.objects.filter(is_deleted=False).exists():
            return make_response({'record_id': 0, 'question_list': []})

        # 查询是否有未做完的试卷
        last_record: EnglishWordTestRecord = EnglishWordTestRecord.objects.filter(
            is_deleted=False, user_id=user_id, question_num__gt=0).order_by('-id').first()
        if last_record and not last_record.englishwordtestanswer_set.exists():
            details = last_record.englishwordtestrecorddetail_set.order_by('id').all().select_related('question')
            questions = [d.question for d in details]
            ser = self.serializer_class(questions, many=True)
            return make_response({
                'record_id': last_record.id,
                'question_list': ser.data,
            })

        # 查询已经出的题
        no_question_words = []

        # 记录已经出的题目
        exist_question_ids = []
        exist_word_ids = []
        exist_question_qs = EnglishWordTestRecordDetail.objects.filter(
            is_deleted=False, record__user_id=user_id
        ).select_related('question')
        for q in exist_question_qs:
            if q.question_id not in exist_question_ids:
                exist_question_ids.append(q.question_id)
            if q.question.en_word_id not in exist_word_ids:
                exist_word_ids.append(q.question.en_word_id)

        if exist_word_ids:
            # 查询这些单词的所有题目
            question_qs = EnglishWordTestQuestion.objects.filter(
                is_deleted=False, en_word_id__in=exist_word_ids
            )
            word_question_map = defaultdict(list)
            for q in question_qs:
                word_question_map[q.en_word_id].append(q.id)

            # 过滤已经出完题的单词
            for en_word_id, question_ids in word_question_map.items():
                if not (set(question_ids) - set(exist_question_ids)):
                    no_question_words.append(en_word_id)

        # TODO 有题目的单词, 未导入全部单词，临时处理
        # 有题目的单词缓存一下
        cache_key = 'en_word_has_q_words'
        has_question_words = cache.get(cache_key)
        if has_question_words is None:
            has_question_words_qs = EnglishWordTestQuestion.objects.values('en_word_id').annotate(question_count=Count('id'))
            has_question_words = [i['en_word_id'] for i in has_question_words_qs]
            cache.set(cache_key, has_question_words, timeout=300)

        if no_question_words:
            qs = EnglishWordLibrary.objects.filter(
                is_deleted=False, id__in=has_question_words,
            ).exclude(id__in=no_question_words).values('id', 'word_freq')
        else:
            qs = EnglishWordLibrary.objects.filter(
                is_deleted=False, id__in=has_question_words,
            ).values('id', 'word_freq')
        word_freq_group = defaultdict(list)
        for i in qs:
            word_freq_group[i['word_freq']].append(i['id'])

        high_words = word_freq_group.get('high', [])
        middle_words = word_freq_group.get('middle', [])
        low_words = word_freq_group.get('low', [])
        # 高中低频（10:3:2）
        high_num = min(10, len(high_words))
        middle_num = min(3, len(middle_words))
        low_num = min(2, len(low_words))

        high_words = random.sample(high_words, high_num)
        middle_words = random.sample(middle_words, middle_num)
        low_words = random.sample(low_words, low_num)

        all_words = [*high_words, *middle_words, *low_words]
        question_qs = EnglishWordTestQuestion.objects.filter(is_deleted=False,
            en_word_id__in=all_words).exclude(id__in=exist_question_ids)
        word_question_group = defaultdict(list)
        for i in question_qs:
            word_question_group[i.en_word_id].append(i)

        questions = []
        for en_word_id, word_questions in word_question_group.items():
            if not word_questions:
                continue
            questions.extend(random.sample(word_questions, 1))

        with transaction.atomic():
            record = EnglishWordTestRecord.objects.create(
                user_id=user_id,
                question_num=len(questions)
            )
            detail_objs = []
            for question in questions:
                detail_objs.append(EnglishWordTestRecordDetail(
                    record=record, question=question
                ))
            EnglishWordTestRecordDetail.objects.bulk_create(detail_objs)

        ser = self.serializer_class(questions, many=True)
        return make_response({
            'record_id': record.id,
            'question_list': ser.data,
        })


class EnglishWordQuestionSubmitView(base_view.BaseView):
    validator_class = TestPaperSubmitValidator

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        dto: TestPaperSubmitDto = self.validate_request(request.data)
        record: EnglishWordTestRecord = EnglishWordTestRecord.objects.filter(is_deleted=False, id=dto.record_id).first()
        if not record:
            raise ParameterError('提交记录不存在')

        question: EnglishWordTestQuestion = EnglishWordTestQuestion.objects.filter(
            is_deleted=False, id=dto.answer_detail.question_id,
        ).first()
        if not question:
            raise Exception('问题不存在')

        is_right = dto.answer_detail.user_answer == question.answer
        answer = EnglishWordTestAnswer.objects.filter(is_deleted=False, record=record).first()
        with transaction.atomic():
            if not answer:
                answer = EnglishWordTestAnswer(
                    record=record,
                    right_num=1 if is_right else 0
                )
                answer.save()
            elif is_right:
                answer.right_num += 1
                answer.save()

            EnglishWordTestAnswerDetail.objects.create(
                record=record,
                answer=answer,
                question=question,
                user_answer=dto.answer_detail.user_answer,
                is_right=is_right,
                is_answered=True
            )
            if not is_right:
                EnglishWordWrongQuestion.objects.create(user_id=user_id, question=question)

        return make_response({'answer_id': answer.id})


class EnglishWordWrongQuestionCountView(base_view.BaseView):
    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        num = EnglishWordWrongQuestion.objects.filter(is_deleted=False, user_id=user_id, is_right_again=False).count()
        return make_response({
            'wrong_question_num': num,
        })


class EnglishWordWrongQuestionView(base_view.BaseView):
    serializer_class = EnglishWordTestQuestionSerializer

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        question_record = EnglishWordWrongQuestion.objects.filter(
            is_deleted=False, user_id=user_id, is_right_again=False
        ).order_by('id').first()
        if not question_record:
            return make_response({'has_question': False, 'question': None})

        question: EnglishWordTestQuestion = question_record.question
        ser = self.serializer_class(question)
        return make_response({
            'has_question': True,
            'question': ser.data
        })


class EnglishWordWrongQuestionSubmitView(base_view.BaseView):
    validator_class = QuestionAnswerValidator

    def post(self, request, *args, **kwargs):
        user_id = request.data.get('userinfo', {}).get('user_id')
        if not user_id:
            raise ParameterError()

        dto: QuestionAnswerDto = self.validate_request(request.data)
        question: EnglishWordTestQuestion = EnglishWordTestQuestion.objects.filter(is_deleted=False, id=dto.question_id).first()
        if not question:
            raise Exception('问题不存在')

        wrong_question = EnglishWordWrongQuestion.objects.filter(
            is_deleted=False, user_id=user_id, question=question, is_right_again=False).first()
        if not wrong_question:
            return make_response()

        wrong_question.answer_num += 1
        if question.answer == dto.user_answer:
            wrong_question.is_right_again = True
        wrong_question.save()
        return make_response()
