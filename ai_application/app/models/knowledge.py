from django.db import models

from ._base import BaseModel


class Knowledge(BaseModel):
    dataset = models.ForeignKey('Dataset', on_delete=models.CASCADE, db_constraint=False)
    dataset_document = models.ForeignKey('DatasetDocument', on_delete=models.CASCADE, db_constraint=False)
    name = models.CharField('名称', max_length=100, blank=True)
    definition = models.TextField('定义', null=True)
    index_node_id = models.CharField('索引节点ID', max_length=255, blank=True)

    class Meta:
        verbose_name = verbose_name_plural = '知识点'
        db_table = 'ai_knowledge'


class KnowledgeStore(BaseModel):
    course_id = models.CharField('课程ID', max_length=40, db_index=True)
    name = models.CharField('名称', max_length=100, blank=True)
    definition = models.TextField('定义', null=True)
    search_count = models.IntegerField(default=0, verbose_name='搜索次数')
    like_count = models.IntegerField(default=0, verbose_name='点赞次数')
    desc = models.TextField('描述', null=True)
    is_usable = models.BooleanField('是否可用', default=True)

    class Meta:
        verbose_name = verbose_name_plural = '知识点内容存储'
        db_table = 'ai_knowledge_store'


class CourseVideoContent(BaseModel):
    dataset = models.ForeignKey('Dataset', on_delete=models.CASCADE, db_constraint=False)
    name = models.CharField('名称', max_length=100, blank=True)
    content = models.TextField('内容', null=True)
    is_optimized = models.BooleanField('是否已优化', default=False)
    optimized_content = models.TextField('优化内容', null=True)
    subtitle_note = models.TextField('字幕笔记', null=True)
    is_subtitle_note_enabled = models.BooleanField('字幕笔记是否可用', default=False)

    class Meta:
        verbose_name = verbose_name_plural = '课程视频内容'
        db_table = 'ai_course_video_content'

    def get_content(self) -> str:
        return self.optimized_content if self.is_optimized else self.content


class CourseVideoKeyword(BaseModel):
    dataset = models.ForeignKey('Dataset', on_delete=models.CASCADE, db_constraint=False)
    dataset_document = models.ForeignKey('DatasetDocument', on_delete=models.CASCADE, db_constraint=False)
    video_content = models.ForeignKey('CourseVideoContent', on_delete=models.CASCADE, db_constraint=False)
    keyword = models.CharField('关键词', max_length=100, blank=True)
    weight = models.FloatField('权重', default=0)

    class Meta:
        verbose_name = verbose_name_plural = '课程视频内容关键词'
        db_table = 'ai_course_video_keyword'
