# Generated by Django 4.2 on 2025-03-25 06:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0029_knowledgelibrary_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EnglishWordLibrary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('word', models.CharField(blank=True, db_index=True, max_length=100, verbose_name='单词')),
                ('word_freq', models.Char<PERSON><PERSON>(blank=True, max_length=32, verbose_name='词频')),
                ('part', models.Char<PERSON>ield(blank=True, max_length=100, verbose_name='part')),
                ('title1', models.Char<PERSON><PERSON>(blank=True, max_length=100, verbose_name='一级标题')),
                ('title2', models.CharField(blank=True, max_length=100, verbose_name='二级标题')),
                ('title3', models.CharField(blank=True, max_length=100, verbose_name='三级标题')),
            ],
            options={
                'verbose_name': '英语单词库',
                'verbose_name_plural': '英语单词库',
                'db_table': 'ai_data_english_word_library',
            },
        ),
    ]
