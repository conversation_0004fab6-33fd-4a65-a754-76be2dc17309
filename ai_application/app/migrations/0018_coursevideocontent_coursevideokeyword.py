# Generated by Django 4.2 on 2025-01-09 07:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0017_englishsentenceanalysis_alter_message_message_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='CourseVideoContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='名称')),
                ('content', models.TextField(null=True, verbose_name='内容')),
                ('is_optimized', models.BooleanField(default=False, verbose_name='是否已优化')),
                ('optimized_content', models.TextField(null=True, verbose_name='优化内容')),
                ('dataset', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.dataset')),
            ],
            options={
                'verbose_name': '课程视频内容',
                'verbose_name_plural': '课程视频内容',
                'db_table': 'ai_course_video_content',
            },
        ),
        migrations.CreateModel(
            name='CourseVideoKeyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('keyword', models.CharField(blank=True, max_length=100, verbose_name='关键词')),
                ('weight', models.FloatField(default=0, verbose_name='权重')),
                ('dataset', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.dataset')),
                ('dataset_document', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.datasetdocument')),
                ('video_content', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.coursevideocontent')),
            ],
            options={
                'verbose_name': '课程视频内容关键词',
                'verbose_name_plural': '课程视频内容关键词',
                'db_table': 'ai_course_video_keyword',
            },
        ),
    ]
