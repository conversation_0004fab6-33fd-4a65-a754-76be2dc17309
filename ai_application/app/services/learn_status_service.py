from django.conf import settings
from django.db import connection
from app.api.dto import CozeChatMessageDto
from django.db.utils import OperationalError
from app.models import Account, App
from app.models.main_subject import StudentLearnStat
from app.services.coze_app_generate_service import coze_chat_block
from app.services.student_learn_stat.learn_stat_service import LearnStatService
from app.libs.ding_robot import send_dingtalk_message
import requests
from datetime import datetime, timedelta
import json, uuid


def get_learning_data(user_id, rec_days):
    """获取学情数据"""
    app_code = settings.BI_API_APP_CODE
    api_config = {
        "name": "学情数据",
        "url": "http://ytbi.kaoyanvip.cn/learning_situation/get_detail_by_userid",
        "method": "POST",
        "headers": {
            "Authorization": f"appCode {app_code}"
        },
        "body": {
            "user_id": user_id,
            "rec_days": rec_days
        }
    }
    
    try:
        # 发送请求
        response = requests.post(
            api_config["url"],
            json=api_config["body"],
            headers=api_config["headers"]
        )
        # 检查响应状态
        response.raise_for_status()
        # 解析JSON响应
        data = response.json()
        data = json.dumps(data, indent=2, ensure_ascii=False)
        data = json.loads(data)
        data = data["data"][0]
        
        # 返回数据
        return data
        
    except requests.exceptions.RequestException as e:
        error_msg = f"学情数据API请求失败: {str(e)}\n用户ID: {user_id}\n请求天数: {rec_days}\nURL: {api_config['url']}"
        send_dingtalk_message(error_msg)
        return None
    
    except json.JSONDecodeError as e:
        error_msg = f"学情数据JSON解析失败: {str(e)}\n用户ID: {user_id}\n请求天数: {rec_days}\nURL: {api_config['url']}\n响应内容: {response.text if 'response' in locals() else '无响应内容'}"
        send_dingtalk_message(error_msg)
        return None


def generate_learning_data(user_id,rec_days):
    # 根据userid调用api返回用户基本信息
    data = get_learning_data(user_id,rec_days)
    day_range_str = f"近{rec_days}天"
    # 当前日期
    today = datetime.today()
    date_range_str = f"{today.strftime('%Y-%m-%d')}"
    # 处理返回信息，标准化格式
    # 解析课程学习进度
    course_dn_detail = json.loads(data.get('course_dn_detail', '[]'))
    course_section_output = []
    for index, course in enumerate(course_dn_detail, start=1):
        course_section_output.append(
            f"{index}. 课程编码：{course.get('course_number', '')}\n"
            f"课程名称：{course.get('course_name', '')}\n"
            f"课节名称：{course.get('stage_name', '')+course.get('coursesection_name', '')}\n"
            f"学完率：{course.get('percent', 0)}%"
        )

    # 解析 APP 答题信息
    app_answer_info_str = data.get('app_answer_info')
    app_answer_info = json.loads(app_answer_info_str) if app_answer_info_str is not None else []
    app_answer_output = []
    for answer in app_answer_info:
        average_score = answer.get('avg_score')
        total_score = answer.get('total_score')
        if average_score is not None and total_score is not None:
            app_answer_output.append(
                f"试卷ID：{answer.get('paper_id', '')} 试卷名称：{answer.get('paper_name', '')} "
                f"测试类别：{answer.get('assembly_type_name', '')} 平均得分：{answer.get('avg_score', 0)} "
                f"总分：{answer.get('total_score', 0)}"
            )

    # 解析数学答题信息
    math_answer_info_str = data.get('math_answer_info')
    math_answer_info = json.loads(math_answer_info_str) if math_answer_info_str is not None else []
    math_answer_output = []
    for answer in math_answer_info:
        math_answer_output.append(
            f"试卷名称：{answer.get('paper_name', '')} 测试类型：{answer.get('exam_type', '')} "
            f"正确率：{answer.get('accuracy', 0)}%"
        )

    # 解析政治答题信息
    zz_mold_answer_info_str = data.get('zz_mold_answer_info')
    zz_mold_answer_info = json.loads(zz_mold_answer_info_str) if zz_mold_answer_info_str is not None else []
    zz_kd_answer_info_str = data.get('zz_kd_answer_info')
    zz_kd_answer_info = json.loads(zz_kd_answer_info_str) if zz_kd_answer_info_str is not None else []

    zz_mold_output = []
    zz_kd_output = []
    for answer in zz_mold_answer_info:
        zz_mold_output.append(
            f"刷题模块：{answer.get('exercise_module', '')} 刷题次数：{answer.get('exercise_times', '')} "
            f"正确率：{answer.get('accuracy', '')}%"
        )
    for answer in zz_kd_answer_info:
        zz_kd_output.append(
            f"考点名称：{answer.get('exam_knowledge_points', '')} 刷题次数：{answer.get('exercise_times', '')} "
            f"正确率：{answer.get('accuracy', '')}%"
        )

    # 格式化输出
    course_section_str = '\n'.join(course_section_output)
    app_answer_str = '\n'.join(app_answer_output) if app_answer_output else '暂无数据'
    math_answer_str = '\n'.join(math_answer_output) if math_answer_output else '暂无数据'
    zz_mold_str = '\n'.join(zz_mold_output)
    zz_kd_str = '\n'.join(zz_kd_output)

    # 获取薄弱知识点
    weakness_kgs = LearnStatService.get_weakness_kgs_by_days(user_id, days=rec_days)
    knowledge_str_list = []
    for item in weakness_kgs:
        subject = item.get('subject', '')
        kgs = ', '.join(item.get('kgs', []))
        knowledge_str_list.append(f"{subject}: {kgs}")
    knowledge_str = '\n'.join(knowledge_str_list) if knowledge_str_list else '暂无薄弱知识点信息'

    query = f"""
报告周期: {day_range_str}
报告日期：{date_range_str}
一、{day_range_str}课节学习明细
距上次看课已过去的天数：{data.get('learning_datediff', 0)}
{course_section_str}
二、所有答题情况明细
1. APP测试:
{app_answer_str}
薄弱知识点：
{knowledge_str}
2. 数学小程序：
{math_answer_str}
3. 政治小程序：
{zz_mold_str}
(刷题知识点分布：)
{zz_kd_str}
"""
    task_id = str(uuid.uuid4())  # 生成唯一任务ID
    return query,task_id


def submit_learn_status(record: StudentLearnStat):
    query, task_id = generate_learning_data(record.user_id,record.rec_days)

    try:
        # 1. 获取账号信息
        from_account = Account.objects.first()
        # 2. 获取智能体模型
        app_model = App.objects.filter(app_type='student_learn_stat').first()
        # 3. 准备参数
        dto = CozeChatMessageDto(
            app_id=app_model.app_no,
            query=query,
            conversation_id='',
            stream=False,
            userinfo={"user_id": record.user_id, "rec_days": record.rec_days}
        )
        
        # 4. 调用智能体
        answer = coze_chat_block(
            app_model=app_model,
            dto=dto,
            from_account=from_account
        )

        # 5.存储数据，加上task_id
        record.query = query
        record.analysis = answer
        record.status = "SUCCESS"
        record.save()
    except OperationalError as db_error:
        connection.close()
    except Exception as e:
        record.query = query
        record.fail_reason = str(e)
        record.status = "FAIL"
        record.save()

        send_dingtalk_message(str(e))


def check_learn_status(task_id):
    study_analysis=StudentLearnStat.objects.filter(task_id=task_id).first()
    if not study_analysis:
        return "错误：未找到有效的任务ID"
    # 检查任务状态"

    return study_analysis.analysis
