import os
from celery import Celery
from celery.schedules import crontab
from django.conf import settings
from kombu import Queue, Exchange
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
# 创建 Celery 实例
app = Celery('ai_application')
# 使用你的 Django settings
app.config_from_object('django.conf:settings', namespace='CELERY')
# 自动从所有已注册的 Django app 中加载任务模块
app.autodiscover_tasks()
app.conf.broker_connection_retry_on_startup = True
# 进程池可以同时运行进程数量
app.conf.CELERYD_CONCURRENCY = 10
# 限制每个工作进程（worker）在重启之前可以处理的最大任务数。
app.conf.max_tasks_per_child = 100
# 使用 Django 的时区设置
app.conf.enable_utc = False
app.conf.timezone = "Asia/Shanghai"

# 特定目录的定时任务需要配置imports
app.conf.imports = [
    "app.tasks.auto_check_llm_request_task",
    "app.tasks.auto_check_message_error_task",
    "app.tasks.auto_check_document_overtime_task",
    "app.tasks.auto_check_document_deleted_task",
    "app.tasks.auto_check_process_message_task",
    "app.tasks.auto_gen_hot_article_source_task",
    "app.tasks.auto_gen_hot_article_viewpoint_task",
    "app.tasks.sync_question_data_task",
    "app.tasks.auto_check_course_note_task",
    "app.tasks.auto_check_api_task",
    "app.tasks.auto_gen_en_word_test_question",
    "app.tasks.auto_gen_en_word_recite_question",
    "app.tasks.auto_check_meeting_voice",
    "app.tasks.auto_gen_knowledge_detail",
    "app.tasks.auto_check_coze_workflow_result_task",
    "app.tasks.auto_check_english_paper_task",
    "app.tasks.auto_check_english_recite_plan",
    "app.tasks.auto_check_student_learn_stat_task",
    "app.services.document_proofreader.celery_tasks.document_proofreader_async_task",
    "app.tasks.auto_check_supervise_learn_stat_task",
    "app.tasks.auto_check_supervise_init_status",
    "app.tasks.ai_practice_task",
]

app.conf.beat_schedule = {
    'auto_check_message_error_task': {
        'task': 'app.tasks.auto_check_message_error_task.auto_check_message_error_task',
        'schedule': 300,
    },
    'auto_check_document_overtime_task': {
        'task': 'app.tasks.auto_check_document_overtime_task.auto_check_document_overtime_task',
        'schedule': 60 * 5,
    },
    'auto_check_document_deleted': {
        'task': 'app.tasks.auto_check_document_deleted_task.auto_check_document_deleted_task',
        'schedule': 3600,
    },
    'auto_check_process_message_task': {
        'task': 'app.tasks.auto_check_process_message_task.auto_check_process_message_task',
        'schedule': 60,
    },
    'auto_gen_hot_article_source_task': {
        'task': 'app.tasks.auto_gen_hot_article_source_task.auto_gen_hot_article_source_task',
        'schedule': 3600,
    },
    'auto_gen_hot_article_viewpoint_task': {
        'task': 'app.tasks.auto_gen_hot_article_viewpoint_task.auto_gen_hot_article_viewpoint_task',
        'schedule': 3600,
    },
    'sync_question_data_task': {
        'task': 'app.tasks.sync_question_data_task.auto_check_question_data_task',
        'schedule': 100,
    },
    'auto_check_course_note_task': {
        'task': 'app.tasks.auto_check_course_note_task.auto_check_course_note_task',
        'schedule': 60,
    },
    'auto_check_course_video_note_task': {
        'task': 'app.tasks.auto_check_course_note_task.auto_check_course_video_note_task',
        'schedule': 60,
    },
    'auto_check_course_video_note_gen_task': {
        'task': 'app.tasks.auto_check_course_note_task.auto_check_course_video_note_gen_task',
        'schedule': 60,
    },
    'auto_check_course_note_status_task': {
        'task': 'app.tasks.auto_check_course_note_task.auto_check_course_note_status_task',
        'schedule': 60,
    },
    # 'auto_check_coze_workflow_result_task': {
    #     'task': 'app.tasks.auto_check_coze_workflow_result_task.auto_check_coze_workflow_result_task',
    #     'schedule': 30,
    # },
    'auto_check_english_paper_explain_gen': {
        'task': 'app.tasks.auto_check_english_paper_task.auto_check_english_paper_explain_gen',
        'schedule': 60,
    },
    'auto_check_english_paper_summary_explain_gen': {
        'task': 'app.tasks.auto_check_english_paper_task.auto_check_english_paper_summary_explain_gen',
        'schedule': 30,
    },
    'auto_check_english_paper_status': {
        'task': 'app.tasks.auto_check_english_paper_task.auto_check_english_paper_status',
        'schedule': 30,
    },
    'auto_check_student_learn_stat_gen': {
        'task': 'app.tasks.auto_check_student_learn_stat_task.auto_check_student_learn_stat_gen',
        'schedule': 30,
    },
    'check_async_proofreader_tasks': {
        'task': 'app.services.document_proofreader.celery_tasks.document_proofreader_async_task.check_async_proofreader_tasks',
        'schedule': 10,
    },
    'auto_check_supervise_learn_stat_gen': {
        'task': 'app.tasks.auto_check_supervise_learn_stat_task.auto_check_supervise_learn_stat_gen',
        'schedule': 5,
    },
    'auto_check_supervise_init_status_gen': {
        'task': 'app.tasks.auto_check_supervise_init_status.auto_check_supervise_init_stat_gen',
        'schedule': 5,
    },
    'auto_check_answer_report_task': {
        'task': 'app.tasks.ai_practice_task.auto_check_answer_report_task',
        'schedule': 60,
    },
}

if settings.ENVIRONMENT == settings.ENV_PRODUCT:
    app.conf.beat_schedule['auto_check_llm_request_task'] = {
        'task': 'app.tasks.auto_check_llm_request_task.auto_check_llm_request_task',
        'schedule': 300,
    }

if settings.ENVIRONMENT == settings.ENV_TEST:
    app.conf.beat_schedule['auto_check_api_task'] = {
        'task': 'app.tasks.auto_check_api_task.auto_check_api_task',
        # 'schedule': 300,
        'schedule': crontab(hour='*', minute='0'),
    }
    app.conf.beat_schedule['auto_check_course_note_task_debug'] = {
        'task': 'app.tasks.auto_check_course_note_task.auto_check_course_note_task_debug',
        'schedule': 60,
    }
    app.conf.beat_schedule['auto_check_course_note_task_debug'] = {
        'task': 'app.tasks.auto_check_meeting_voice.auto_check_course_note_task_debug',
        'schedule': 60,
    }
    # app.conf.beat_schedule['auto_check_meeting_voice_convert'] = {
    #     'task': 'app.tasks.auto_check_meeting_voice.auto_check_meeting_voice_convert',
    #     'schedule': 60,
    # }
    # app.conf.beat_schedule['auto_check_meeting_voice_extract'] = {
    #     'task': 'app.tasks.auto_check_meeting_voice.auto_check_meeting_voice_extract',
    #     'schedule': 60,
    # }
    # app.conf.beat_schedule['auto_check_english_recite_plan_adjust'] = {
    #     'task': 'app.tasks.auto_check_english_recite_plan.auto_check_english_recite_plan_adjust',
    #     'schedule': crontab(hour='1', minute='*/15'),
    # }
    # app.conf.beat_schedule['auto_gen_en_word_test_question'] = {
    #     'task': 'app.tasks.auto_gen_en_word_test_question.auto_gen_en_word_test_question',
    #     'schedule': 60,
    # }
    # app.conf.beat_schedule['auto_check_en_word_test_question'] = {
    #     'task': 'app.tasks.auto_gen_en_word_test_question.auto_check_en_word_test_question',
    #     'schedule': 60,
    # }
    # app.conf.beat_schedule['auto_gen_en_word_recite_question'] = {
    #     'task': 'app.tasks.auto_gen_en_word_recite_question.auto_gen_en_word_recite_question',
    #     'schedule': 60,
    # }
    # app.conf.beat_schedule['auto_check_en_word_recite_question'] = {
    #     'task': 'app.tasks.auto_gen_en_word_recite_question.auto_check_en_word_recite_question',
    #     'schedule': 60,
    # }
