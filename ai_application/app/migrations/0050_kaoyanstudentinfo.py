# Generated by Django 4.2 on 2025-04-23 02:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0049_merge_20250423_1052'),
    ]

    operations = [
        migrations.CreateModel(
            name='KaoYanStudentInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('student_info', models.JSONField(blank=True, null=True, verbose_name='学生信息')),
                ('college_analysis', models.J<PERSON><PERSON>ield(blank=True, null=True, verbose_name='院校分析结果')),
                ('intensive_choice', models.JSONField(blank=True, null=True, verbose_name='冲刺档')),
                ('steady_choice', models.JSONField(blank=True, null=True, verbose_name='稳妥档')),
                ('safety_choice', models.JSONField(blank=True, null=True, verbose_name='保底档')),
            ],
            options={
                'verbose_name': '考研学生信息',
                'verbose_name_plural': '考研学生信息',
                'db_table': 'ai_kao_yan_student_info',
            },
        ),
    ]
