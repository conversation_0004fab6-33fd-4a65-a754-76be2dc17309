import numpy as np
from scipy.special import erf
from scipy.optimize import minimize
import matplotlib.pyplot as plt
import matplotlib as mpl

# --- 1. 优化设置 ---
# 定义模型函数，p是参数列表 [A1, A2, B]
def model_function(x, p):
    A1, A2, B = p
    A3 = 10.0 - A1 - A2
    C1, C2, C3 = 0.1, 0.5, 4.0
    term1 = A1 * erf(B * (x - C1))
    term2 = A2 * erf(B * (x - C2))
    term3 = A3 * erf(B * (x - C3))
    return term1 + term2 + term3

# 定义误差函数，用于优化
def error_function(p):
    A1, A2, B = p
    # 惩罚无效的参数组合
    if not (0 < A1 < 10 and 0 < A2 < 10 and 0 < (A1 + A2) < 10 and B > 0):
        return 1e9
    
    y_at_2_5 = model_function(2.5, p)
    y_at_4 = model_function(4.0, p)
    
    # 计算与目标的平方误差
    error = (y_at_2_5 - 3.0)**2 + (y_at_4 - 7.0)**2
    return error

# --- 2. 执行优化 ---
# 初始猜测值
initial_guess = [1.5, 2.5, 2.0]
# 为参数设置合理的边界
bounds = ((0.01, 9.99), (0.01, 9.99), (0.1, 5.0))

# 运行优化器
result = minimize(error_function, initial_guess, method='L-BFGS-B', bounds=bounds)

# 提取优化后的最佳参数
A1_opt, A2_opt, B_opt = result.x
A3_opt = 10.0 - A1_opt - A2_opt

# --- 3. 定义最终函数 ---
def custom_nonlinear_function_v3(x):
    """
    一个经过优化的、具有三个拐点的非线性函数。
    由三个高斯误差函数(erf)复合而成。

    该函数具有以下特性：
    - 在 x=0.1, x=0.5, 和 x=4.0 处有三个拐点。
    - 在 x=2.5 时，函数值约等于 3。
    - 在 x=4.0 时，函数值约等于 7。
    - 渐近线为 y = ±10。

    Args:
        x (float or np.ndarray): 输入值或NumPy数组。

    Returns:
        float or np.ndarray: 计算后的函数输出值。
    """
    # --- 使用经过数值优化找到的最佳参数 ---
    A1 = A1_opt
    A2 = A2_opt
    A3 = A3_opt
    B = B_opt
    C1, C2, C3 = 0.1, 0.5, 4.0

    term1 = A1 * erf(B * (x - C1))
    term2 = A2 * erf(B * (x - C2))
    term3 = A3 * erf(B * (x - C3))
    
    return term1 + term2 + term3

# --- 4. 使用示例 ---
if __name__ == '__main__':
    # 设置绘图环境
    mpl.rcParams['font.sans-serif'] = ['SimHei']
    mpl.rcParams['axes.unicode_minus'] = False

    print("--- 优化结果 ---")
    print(f"找到的最佳参数: A1={A1_opt:.4f}, A2={A2_opt:.4f}, A3={A3_opt:.4f}, B={B_opt:.4f}")
    
    # 验证关键点
    y_at_2_5 = custom_nonlinear_function_v3(2.5)
    y_at_4 = custom_nonlinear_function_v3(4.0)
    print("\n--- 使用优化后参数的函数表现 ---")
    print(f"函数在 x = 2.5 时的值为: {y_at_2_5:.4f} (目标: 3.0)")
    print(f"函数在 x = 4.0 时的值为: {y_at_4:.4f} (目标: 7.0)")

    # 生成绘图数据
    x_values = np.linspace(-1, 8, 800)
    y_values = custom_nonlinear_function_v3(x_values)

    # 创建图表
    plt.figure(figsize=(12, 8))
    plt.plot(x_values, y_values, 'b-', linewidth=2.5, label='优化后的函数 f(x)')
    plt.axhline(y=10, color='r', linestyle='--', label='渐近线 y=±10')
    plt.axhline(y=-10, color='r', linestyle='--')
    
    # 标记拐点
    inflection_points_x = [0.1, 0.5, 4.0]
    for px in inflection_points_x:
        plt.axvline(x=px, color='gray', linestyle=':')
    # 只为第一个拐点线添加图例标签，避免重复
    plt.axvline(x=inflection_points_x[0], color='gray', linestyle=':', label='拐点')

    # 标记目标点
    target_points_x = [2.5, 4.0]
    target_points_y = [y_at_2_5, y_at_4]
    plt.scatter(target_points_x, target_points_y, color='purple', zorder=5, s=80, label='目标验证点')
    plt.text(2.5 + 0.1, y_at_2_5, f'(2.5, {y_at_2_5:.2f})', fontsize=11)
    plt.text(4.0 + 0.1, y_at_4, f'(4.0, {y_at_4:.2f})', fontsize=11)

    # 设置图表属性
    plt.title('优化后的三拐点非线性函数', fontsize=16)
    plt.xlabel('x', fontsize=14)
    plt.ylabel('f(x)', fontsize=14)
    plt.grid(True)
    plt.legend()
    plt.ylim(-11, 11)

    plt.show()
