# Generated by Django 4.2 on 2025-04-17 12:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0047_enwordrecitebasicpaper_paper_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='englishwordlibrary',
            name='antonym',
            field=models.JSONField(null=True, verbose_name='反义词'),
        ),
        migrations.AddField(
            model_name='englishwordlibrary',
            name='real_example_sentence',
            field=models.JSONField(null=True, verbose_name='真题例句'),
        ),
        migrations.AddField(
            model_name='englishwordlibrary',
            name='synonym',
            field=models.JSONField(null=True, verbose_name='同义词'),
        ),
        migrations.AddField(
            model_name='enwordrecitedayplan',
            name='review_words',
            field=models.JSONField(null=True, verbose_name='复习单词列表'),
        ),
        migrations.AddField(
            model_name='enwordreciteplanrecord',
            name='is_manual',
            field=models.BooleanField(default=False, verbose_name='是否手动生成'),
        ),
        migrations.AddField(
            model_name='enwordrecitequestion',
            name='example_sentence',
            field=models.TextField(null=True, verbose_name='例句'),
        ),
        migrations.AlterField(
            model_name='enwordrecitedayplan',
            name='day',
            field=models.DateField(verbose_name='背诵日期'),
        ),
        migrations.AlterField(
            model_name='enwordrecitedayrecord',
            name='day',
            field=models.DateField(verbose_name='背诵日期'),
        ),
        migrations.AlterField(
            model_name='enwordrecitequestion',
            name='question_type',
            field=models.CharField(choices=[('en2ch', '英译中'), ('multi_define', '一词多义'), ('relate_define', '同义反义')], default='en2ch', max_length=16, verbose_name='问题类型'),
        ),
        migrations.CreateModel(
            name='EnWordReciteReviewRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('review_day', models.DateField(verbose_name='复习日期')),
                ('user_answer', models.CharField(blank=True, max_length=16, verbose_name='用户答案')),
                ('is_right', models.BooleanField(default=False, verbose_name='是否正确')),
                ('day_plan', models.ForeignKey(db_constraint=False, null=True, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitedayplan')),
                ('plan', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordreciteplan')),
                ('plan_record', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordreciteplanrecord')),
                ('question', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.enwordrecitequestion')),
                ('word', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordlibrary')),
            ],
            options={
                'verbose_name': '用户复习记录',
                'verbose_name_plural': '用户复习记录',
                'db_table': 'ai_english_word_recite_review_record',
            },
        ),
        migrations.CreateModel(
            name='EnWordReciteReviewPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('first_add_day', models.DateField(verbose_name='首次记录日期')),
                ('review_num', models.IntegerField(default=0, verbose_name='复习次数')),
                ('is_review_continue', models.BooleanField(default=True, verbose_name='是否继续复习')),
                ('next_review_day', models.DateField(null=True, verbose_name='下次记录日期')),
                ('word', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.englishwordlibrary')),
            ],
            options={
                'verbose_name': '用户复习计划',
                'verbose_name_plural': '用户复习计划',
                'db_table': 'ai_english_word_recite_review_plan',
            },
        ),
    ]
