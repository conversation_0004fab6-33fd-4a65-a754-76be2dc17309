import logging
from http import HTT<PERSON>tatus
from typing import Generator, cast

import dashscope
from dashscope import get_tokenizer
from dashscope.api_entities.dashscope_response import GenerationResponse
from dashscope.common.error import ServiceUnavailableError
from django.conf import settings

from app.core.model_runtime.entities.llm_entities import LLMResultChunk, LLMResultChunkDelta, LLMResult, LLMUsage
from app.core.model_runtime.model_providers._base.large_language_model import LargeLanguageModel
from app.core.prompt.entities import PromptMessage, SystemPromptMessage, AssistantPromptMessage, UserPromptMessage
from app.core.utils.latex_utils import replace_latex
from app.errors import ContainSensitiveError

logger = logging.getLogger(__name__)


class TongyiLargeLanguageModel(LargeLanguageModel):
    model_provider = 'tongyi'
    tokenizers = {}

    def _invoke(
            self,
            model: str,
            prompt_messages: list[PromptMessage],
            model_parameters: dict,
            tools: list[str] | None = None,
            stream: bool = True
    ) -> LLMResult | Generator:
        return self._generate(model, prompt_messages, model_parameters, stream)

    def get_num_tokens(self, model: str, prompt_messages: list[PromptMessage]) -> int:
        """
        Get number of tokens for given prompt messages
        """
        if model in self.tokenizers:
            tokenizer = self.tokenizers[model]
        else:
            tokenizer = get_tokenizer(model)
            self.tokenizers[model] = tokenizer

        # convert string to token ids
        tokens = tokenizer.encode(self._convert_messages_to_prompt(prompt_messages))

        return len(tokens)

    def _generate(
            self,
            model: str,
            prompt_messages: list[PromptMessage],
            model_parameters: dict,
            stream: bool = True
    ) -> LLMResult | Generator:
        params = {
            'model': model,
            'api_key': settings.DASHSCOPE_API_KEY,
            'messages': self._convert_prompt_messages_to_tongyi_messages(prompt_messages),
            **model_parameters,
        }
        response = dashscope.Generation.call(
            **params,
            result_format='message',
            stream=True,
            incremental_output=True
        )
        stream_response = self._handle_generate_stream_response(model, response, prompt_messages, model_parameters)
        if stream:
            return stream_response
        return self._handle_generate_response_for_stream(stream_response)

    def _handle_generate_response_for_stream(self, stream_response: Generator) -> LLMResult:
        model_provider = ''
        model = ''
        model_params = {}
        prompt_messages = []
        text = ''
        usage = None

        for result in stream_response:
            result = cast(LLMResultChunk, result)
            if not model_provider:
                model_provider = result.model_provider
            if not model:
                model = result.model
            if not model_params:
                model_params = result.model_params

            text += result.delta.message.content

            if not prompt_messages:
                prompt_messages = result.prompt_messages

            if result.delta.usage:
                usage = result.delta.usage

        if not usage:
            usage = LLMUsage.empty_usage()

        # 通义需要处理latex公式
        text = replace_latex(text)
        message = AssistantPromptMessage(content=text)

        llm_result = LLMResult(
            model_provider=self.model_provider,
            model=model or '',
            model_params=model_params or {},
            prompt_messages=prompt_messages,
            message=message,
            usage=usage,
            is_answer_token_exceed=LLMResult.judge_answer_token_exceed(model_params, usage)
        )
        return llm_result

    # ⚠️废弃
    def ___handle_generate_response(
            self,
            model: str,
            response: GenerationResponse,
            prompt_messages: list[PromptMessage]
    ) -> LLMResult:
        if response.status_code != 200 and response.status_code != HTTPStatus.OK:
            logger.error(f'tongyi handle_generate_response error, '
                         f'status code: {response.status_code}, message: {response.message}')
            if 'contain inappropriate content' in response.message:
                return LLMResult(
                    model_provider=self.model_provider,
                    model=model,
                    message=AssistantPromptMessage(
                        content=settings.LLM_SENSITIVE_ANSWER,
                    ),
                    prompt_messages=prompt_messages,
                    usage=LLMUsage.empty_usage(),
                    is_sensitive=True,
                    is_success=False,
                )

        if not response.output:
            logger.error(f'tongyi handle_generate_response error: {response}')
            return LLMResult(
                model_provider=self.model_provider,
                model=model,
                message=AssistantPromptMessage(
                    content=settings.LLM_NETWORK_ERROR_ANSWER,
                ),
                prompt_messages=prompt_messages,
                usage=LLMUsage.empty_usage(),
                is_success=False,
            )

        answer = response.output.choices[0].message.content
        # 通义需要处理latex公式
        answer = replace_latex(answer)
        assistant_prompt_message = AssistantPromptMessage(
            content=answer,
        )

        usage = self._calc_response_usage(response.usage.input_tokens, response.usage.output_tokens)

        result = LLMResult(
            model_provider=self.model_provider,
            model=model,
            message=assistant_prompt_message,
            prompt_messages=prompt_messages,
            usage=usage,
        )
        return result

    def _handle_generate_stream_response(
            self,
            model: str,
            responses: Generator[GenerationResponse, None, None],
            prompt_messages: list[PromptMessage],
            model_parameters: dict,
    ) -> Generator:
        # 通义需要处理latex公式
        is_last_cut = False
        temp_exp_str = ''

        for index, response in enumerate(responses):
            if response.status_code != 200 and response.status_code != HTTPStatus.OK:
                logger.error(f'tongyi handle_generate_response error, '
                             f'status code: {response.status_code}, message: {response.message}')
                # 判断是否触发阿里敏感词
                # Output data may contain inappropriate content.
                # Input data may contain inappropriate content.
                if 'contain inappropriate content' in response.message:
                    raise ContainSensitiveError(detail=settings.LLM_SENSITIVE_ANSWER)

                raise ServiceUnavailableError(
                    f"Failed to invoke model {model}, status code: {response.status_code}, "
                    f"message: {response.message}"
                )

            # TODO 判断结束类型
            resp_finish_reason = response.output.choices[0].finish_reason

            # transform usage
            usage = response.usage
            usage = self._calc_response_usage(usage.input_tokens, usage.output_tokens)

            resp_content = response.output.choices[0].message.content
            if not resp_content:
                continue

            # 处理公式转换
            if is_last_cut:
                resp_content = f'{temp_exp_str}{resp_content}'
                is_last_cut = False
                temp_exp_str = ''
            if resp_content and resp_content[-1] == '\\':
                is_last_cut = True
                temp_exp_str = resp_content
                continue
            resp_content = replace_latex(resp_content)

            yield LLMResultChunk(
                model_provider=self.model_provider,
                model=model,
                model_params=model_parameters,
                prompt_messages=prompt_messages,
                delta=LLMResultChunkDelta(
                    index=index,
                    message=AssistantPromptMessage(content=resp_content),
                    usage=usage
                )
            )

    def _convert_messages_to_prompt(self, messages: list[PromptMessage]) -> str:
        messages = messages.copy()  # don't mutate the original list

        text = "".join(
            self._convert_one_message_to_text(message)
            for message in messages
        )

        # trim off the trailing ' ' that might come from the "Assistant: "
        return text.rstrip()

    def _convert_one_message_to_text(self, message: PromptMessage) -> str:
        human_prompt = "\n\nHuman:"
        ai_prompt = "\n\nAssistant:"
        content = message.content

        if isinstance(message, UserPromptMessage):
            message_text = f"{human_prompt} {content}"
        elif isinstance(message, AssistantPromptMessage):
            message_text = f"{ai_prompt} {content}"
        elif isinstance(message, SystemPromptMessage):
            message_text = content
        else:
            raise ValueError(f"Got unknown type {message}")

        return message_text

    def _convert_prompt_messages_to_tongyi_messages(
            self,
            prompt_messages: list[PromptMessage],
    ) -> list[dict]:
        """
        Convert prompt messages to tongyi messages

        :param prompt_messages: prompt messages
        :return: tongyi messages
        """
        tongyi_messages = []
        for prompt_message in prompt_messages:
            if isinstance(prompt_message, SystemPromptMessage):
                tongyi_messages.append({
                    'role': 'system',
                    'content': prompt_message.content,
                })
            elif isinstance(prompt_message, UserPromptMessage):
                tongyi_messages.append({
                    'role': 'user',
                    'content': prompt_message.content,
                })
            elif isinstance(prompt_message, AssistantPromptMessage):
                content = prompt_message.content
                tongyi_messages.append({
                    'role': 'assistant',
                    'content': content if content else '',
                })
            else:
                raise ValueError(f"Got unknown type {prompt_message}")

        return tongyi_messages

