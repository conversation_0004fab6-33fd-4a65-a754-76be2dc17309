import pdb
import json


from app.models.main_subject import SuperViseInitStudentStatus
from api_client.yantucs_data.client import yantucs_data_client

from datetime import date,datetime
from app.models import App, Account, PromptTemplate
from app.models.exam_analyis import ExamAnalysisKnowledgePointWithStats
from django.forms.models import model_to_dict
from django.core.cache import cache

import logging

logger = logging.getLogger(__name__)

class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        elif hasattr(obj, 'isoformat'):  # Handle Django DateField
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):  # Handle Django model instances
            return str(obj)
        return super().default(obj)


class SupervisePlanScheme:

    @classmethod
    def get_report(cls, outline_numbers, user_id):
        from_account = Account.objects.first()
        record = SuperViseInitStudentStatus.objects.filter(user_id=user_id).first()

        data = {
            "学生分析": record.analysis,
            "课包信息": [],
            "开始时间": record.date,
            "考试时间": record.exam_date
        }

        # 解析 record.query 字符串为字典
        try:
            if record.query is None:
                logger.warning("record.query 为 None，使用空字典")
                query_data = {}
            elif isinstance(record.query, str):
                # 尝试使用 json.loads，如果失败则使用 eval（不推荐但兼容现有数据）
                try:
                    query_data = json.loads(record.query)
                except json.JSONDecodeError:
                    # 如果不是标准 JSON，尝试使用 eval（需要谨慎）
                    query_data = eval(record.query)
            elif isinstance(record.query, dict):
                query_data = record.query
            else:
                logger.warning(f"record.query 类型未知: {type(record.query)}，使用空字典")
                query_data = {}
        except Exception as e:
            logger.error(f"解析 record.query 失败: {str(e)}", exc_info=True)
            query_data = {}

        # 提取目标院校信息中的考试范围
        target_info = []
        target_schools = query_data.get("目标院校信息", [])

        logger.info(f"找到 {len(target_schools)} 个目标院校")

        for target_school in target_schools:
            if isinstance(target_school, dict):
                # 获取目标院校基本信息
                basic_info = target_school.get("目标院校基本信息", {})
                school_name = basic_info.get("目标院校", "未知院校")
                exam_scope = basic_info.get("考试范围", {})

                logger.info(f"提取院校信息 - 学校名: {school_name}, 考试范围: {exam_scope}")

                target_info.append({
                    "学校名": school_name,
                    "考试范围": exam_scope
                })

        # 并行处理所有 outline
        from concurrent.futures import ThreadPoolExecutor, as_completed

        with ThreadPoolExecutor(max_workers=min(len(outline_numbers), 4)) as executor:
            # 提交所有 outline 处理任务
            outline_futures = {
                executor.submit(cls.get_course_unit, outline): outline
                for outline in outline_numbers
            }

            # 收集结果，保持原有顺序
            outline_results = {}
            for future in as_completed(outline_futures):
                outline = outline_futures[future]
                try:
                    result = future.result()
                    outline_results[outline] = result
                except Exception as e:
                    logger.error(f"处理 outline {outline} 失败: {str(e)}", exc_info=True)
                    outline_results[outline] = []

            # 按原有顺序添加结果，并添加课程名称
            for outline in outline_numbers:
                data["课包信息"].append(outline_results.get(outline, []))

        # 从 record.query 中提取"五、入学基础定位"部分
        entrance_foundation_positioning = ""
        if hasattr(record, 'query') and record.query:
            try:
                # 将 query 转换为字符串进行文本提取
                query_text = str(record.query) if record.query else ""
                from app.utils.text_analysis_utils import TextAnalysisUtils
                entrance_foundation_positioning = TextAnalysisUtils.extract_entrance_foundation_positioning(query_text)
                logger.info(f"提取到入学基础定位部分，长度: {len(entrance_foundation_positioning)}")
            except Exception as e:
                logger.error(f"提取入学基础定位失败: {str(e)}", exc_info=True)

        # 准备新流程的数据结构
        data.update({
            "学生分析": record.analysis,
            "目标院校信息": target_info,
            "学习阶段": getattr(record, 'study_stage', '备考初期'),  # 默认值，如果字段不存在
            "入学基础定位": entrance_foundation_positioning  # 新增：从 record.query 提取的入学基础定位
        })

        logger.info(f"准备发送的数据结构: {json.dumps(data, ensure_ascii=False, cls=DateTimeEncoder)[:500]}...")

        # 获取计划分析实例
        supervise_plan_scheme_stat: App = App.objects.filter(
            is_deleted=False,
            app_type='supervise_plan_scheme'
        ).first()

        if not supervise_plan_scheme_stat:
            logger.error("未找到 supervise_plan_scheme 类型的 App")
            return json.dumps({"error": "未找到对应的应用配置"}, ensure_ascii=False)

        logger.info(f"找到 App: {supervise_plan_scheme_stat.app_no}, mode: {supervise_plan_scheme_stat.mode}")

        # 获取 PromptTemplate
        pt = PromptTemplate.objects.filter(app_no='supervise_plan_scheme').first()
        logger.info(f"找到 PromptTemplate: {pt is not None}, 内容长度: {len(pt.prompt_content) if pt and pt.prompt_content else 0}")

        # 使用封装好的 AppGenerateService.generate 方法
        try:
            from app.api.dto import ChatMessageDto
            from app.services.app_generate_service import AppGenerateService

            # 创建 ChatMessageDto 对象
            chat_dto = ChatMessageDto(
                app_id=supervise_plan_scheme_stat.app_no,
                query=json.dumps(data, ensure_ascii=False, cls=DateTimeEncoder),
                stream=False,
                inputs=data,
                pre_prompt=pt.prompt_content if pt else ''
            )

            logger.info("SupervisePlanScheme Chat DTO Created")

            # 生成报告
            logger.info("调用 AppGenerateService.generate 生成报告")
            result = AppGenerateService.generate(chat_dto, from_account)
            logger.info(f"AppGenerateService 返回结果类型: {type(result)}")

            # 处理不同类型的返回值
            if isinstance(result, dict):
                # 字典类型返回值
                content = result.get('answer', '')
                logger.info(f"从字典中提取 answer 内容，长度: {len(content) if content else 0}")
            elif hasattr(result, 'message') and hasattr(result.message, 'content'):
                # LLMResult 类型返回值
                content = result.message.content
                logger.info(f"从 LLMResult.message.content 提取内容，长度: {len(content) if content else 0}")
            elif hasattr(result, 'text'):
                # 带有 text 属性的对象
                content = result.text
                logger.info(f"从 result.text 提取内容，长度: {len(content) if content else 0}")
            else:
                # 其他类型，转为字符串
                logger.warning(f"未知的返回类型: {type(result)}，尝试转为字符串")
                content = str(result) if result else ''
                logger.info(f"转换为字符串后长度: {len(content) if content else 0}")

            # 检查内容是否为 JSON 字符串，如果是，尝试提取 supervise_plan_scheme 字段
            if content and content.strip().startswith('{') and content.strip().endswith('}'):
                try:
                    json_content = json.loads(content)
                    if isinstance(json_content, dict) and 'supervise_plan_scheme' in json_content:
                        logger.info("从 JSON 中提取 supervise_plan_scheme 字段")
                        content = json_content['supervise_plan_scheme']
                except Exception as e:
                    logger.warning(f"解析 JSON 失败: {str(e)}")

            return content

        except Exception as e:
            logger.error(f"使用 AppGenerateService 执行失败: {str(e)}", exc_info=True)
            return json.dumps({"error": f"生成报告失败: {str(e)}"}, ensure_ascii=False)

    @classmethod
    def extract_data_summary_comparison(cls, analysis_text):
        """
        从学生分析文本中提取"六、数据总结与对比"部分

        Args:
            analysis_text (str): 学生分析文本

        Returns:
            str: 提取的数据总结与对比部分
        """
        from app.utils.text_analysis_utils import TextAnalysisUtils
        return TextAnalysisUtils.extract_data_summary_comparison(analysis_text)

    @classmethod
    def get_course_unit(cls,outline_number):
        data = []
        analyse_data = cls.get_outline_by_id_cached(outline_number)

        for stage in analyse_data["structure"]:
            stage_info = {
                "阶段名称": stage.get("stage_name"),
                "单元信息": []
            }

            child_info = stage.get("children")

            # 收集所有需要处理的 unit
            units_to_process = []
            unit_groups = []

            for info in child_info:
                if info["type"] == "unit":
                    units_to_process.append(("direct", info, stage_info["单元信息"]))
                elif info["type"] == "unit_group":
                    group_info = {
                        "组合单元名称": info["name"],
                        "分支单元信息": []
                    }
                    unit_groups.append(group_info)
                    stage_info["单元信息"].append(group_info)

                    for unit in info["children"]:
                        units_to_process.append(("group", unit, group_info["分支单元信息"]))

            # 并行处理所有 unit
            if units_to_process:
                cls._process_units_parallel(units_to_process)

            data.append(stage_info)
        
        applied_subjects = []
        for subject in analyse_data["subjects"]:
            applied_subjects.append(subject["subject_name"])

        unit_data={
            "适用学科":applied_subjects,
            "课包名称":analyse_data.get("name",None),
            "课包信息":data
        }

        return unit_data

    @classmethod
    def _process_units_parallel(cls, units_to_process):
        """并行处理多个 unit"""
        from concurrent.futures import ThreadPoolExecutor, as_completed

        # 限制并发数，避免过多的并发请求
        max_workers = min(len(units_to_process), 6)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有 unit 处理任务
            unit_futures = {}
            for unit_type, unit, target_list in units_to_process:
                future = executor.submit(cls._process_single_unit, unit)
                unit_futures[future] = (unit_type, unit, target_list)

            # 收集结果
            for future in as_completed(unit_futures):
                unit_type, unit, target_list = unit_futures[future]
                try:
                    unit_info = future.result()
                    if unit_info:
                        target_list.append(unit_info)
                except Exception as e:
                    logger.error(f"处理 unit {unit.get('name', 'unknown')} 失败: {str(e)}", exc_info=True)
                    continue

    @classmethod
    def _process_single_unit(cls, unit):
        """处理单个 unit，返回 unit_info"""
        unit_info = {
            "单元名称": unit.get("name"),
            '课程信息': [],
            "计划时长": unit.get("plan_class_hours")
        }

        course_sections = unit.get("course_sections")
        if not course_sections:
            return unit_info
        for course in course_sections:
            course_info = {
                "课程类型":course.get("mold","其他"),
                "课程名称": course.get("name"),
                "涉及知识点": [],
                "涉及重要知识点": [],
                "主修/辅修":course.get("nature_status"),
                "课程时间": course.get("add_time"),
                "课程ID": course.get("course_section_id")  # 添加课程ID字段用于后续匹配
            }
            courses_id_list = [course.get("course_section_id")]
            unit_info["课程信息"].append(course_info)

            # 获取课程详情和试卷详情
            cls._fetch_course_knowledge_points(courses_id_list, course_info)

        return unit_info

    @classmethod
    def unit_append(cls, my_list, unit):
        """保持原有接口兼容性的方法"""
        unit_info = cls._process_single_unit(unit)
        if unit_info:
            my_list.append(unit_info)

    @classmethod
    def _fetch_course_knowledge_points(cls, courses_id_list, course_info):
        """获取课程的知识点信息"""
        if not courses_id_list:
            return

        # 并行获取课程详情
        from concurrent.futures import ThreadPoolExecutor, as_completed
        from tenacity import retry, stop_after_attempt, wait_exponential

        @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
        def get_paper_with_retry(intid):
            try:
                return yantucs_data_client.get_paper_details_by_intid(intid)
            except Exception as e:
                logger.error(f"获取试卷详情失败，试卷ID: {intid}, 错误: {str(e)}", exc_info=True)
                raise

        with ThreadPoolExecutor(max_workers=8) as executor:
            try:
                # 获取所有课程详情
                logger.info(f"开始获取课程详情，课程ID数量: {len(courses_id_list)}")
                courses_details = cls.get_courses_by_ids_cached(courses_id_list)

                # 为每个课程创建一个知识点集合
                course_knowledge_points = {course_id: set() for course_id in courses_details.keys()}
                print(f"课程知识点集合",course_knowledge_points)

                # 同时创建一个字符串形式的ID映射，处理ID类型不一致问题
                course_knowledge_points_str = {str(course_id): set() for course_id in courses_details.keys()}

                # 收集所有需要获取的试卷ID及对应的课程ID
                paper_intids_with_course = []
                for course_id, details in courses_details.items():
                    sub_dict = details.get("class_test") or details.get("stage_test") or details.get("chapter_test")
                    if not sub_dict:
                        continue

                    # 处理不同类型的测试结构
                    if details.get("class_test"):
                        for sub in sub_dict:
                            if "paper_intid" in sub and sub["paper_intid"]:
                                paper_intids_with_course.append((sub["paper_intid"], course_id))
                    else:
                        if "paper_intid" in sub_dict and sub_dict["paper_intid"]:
                            paper_intids_with_course.append((sub_dict["paper_intid"], course_id))

                logger.info(f"需要获取的试卷ID数量: {len(paper_intids_with_course)}")

                # 并行获取所有试卷详情（带重试机制）
                paper_futures = {
                    executor.submit(get_paper_with_retry, intid): (intid, course_id)
                    for intid, course_id in paper_intids_with_course
                }

                # 处理获取到的试卷详情，将知识点分配给对应课程
                for future in as_completed(paper_futures):
                    try:
                        paper = future.result()
                        intid, course_id = paper_futures[future]
                        cls._extract_knowledge_points_for_course(paper, course_knowledge_points, course_knowledge_points_str, course_id)

                    except Exception as e:
                        logger.error(f"获取试卷详情失败，试卷ID: {paper_futures[future][0]}, 错误: {str(e)}", exc_info=True)
                        continue

                # 将知识点分配给对应的课程
                cls._assign_knowledge_points_to_course(course_info, course_knowledge_points, course_knowledge_points_str, courses_details)

            except Exception as e:
                logger.error(f"获取课程详情失败: {str(e)}", exc_info=True)
                raise

    @classmethod
    def _assign_knowledge_points_to_course(cls, course_info, course_knowledge_points, course_knowledge_points_str, courses_details):
        """将知识点分配给course_info中对应的课程"""
        # 获取当前课程的ID
        current_course_id = course_info.get("课程ID")
        if not current_course_id:
            return

        current_course_time = course_info.get("课程时间")
        if not current_course_time:
            return

        # 处理course_id可能是字符串的情况
        if isinstance(current_course_id, str) and current_course_id.isdigit():
            current_course_id = int(current_course_id)

        # 获取该课程的知识点（尝试多种匹配方式）
        knowledge_points = set()

        # 尝试整数ID匹配
        if current_course_id in course_knowledge_points:
            knowledge_points.update(course_knowledge_points[current_course_id])

        # 尝试字符串ID匹配
        if str(current_course_id) in course_knowledge_points_str:
            knowledge_points.update(course_knowledge_points_str[str(current_course_id)])

        # 遍历所有课程进行模糊匹配
        for course_id in course_knowledge_points.keys():
            if str(course_id) == str(current_course_id):
                knowledge_points.update(course_knowledge_points[course_id])
                break

        for course_id_str in course_knowledge_points_str.keys():
            if course_id_str == str(current_course_id):
                knowledge_points.update(course_knowledge_points_str[course_id_str])
                break

        if knowledge_points:
            # 设置所有知识点到"涉及知识点"字段
            course_info["涉及知识点"] = list(knowledge_points)

            # 查询数据库中这些知识点的exam_count值
            knowledge_point_stats = ExamAnalysisKnowledgePointWithStats.objects.filter(
                point_name__in=list(knowledge_points)
            )

            # 筛选出exam_count大于8的重要知识点
            important_knowledge_points = [
                stat.point_name for stat in knowledge_point_stats if stat.exam_count > 8
            ]

            # 设置重要知识点到"涉及重要知识点"字段
            course_info["涉及重要知识点"] = important_knowledge_points

        elif courses_details:
            # 调试信息：输出课程详情以便调试
            logger.debug(f"课程 {current_course_id} 未找到知识点，课程详情: {courses_details.get(current_course_id, 'Not found')}")

    @classmethod
    def _extract_knowledge_points_for_course(cls, paper, course_knowledge_points, course_knowledge_points_str, course_id):
        """从试卷中提取知识点并添加到指定课程的知识点集合中"""
        paper_detail = paper.get("paper_detail", {})
        knowledge_points = course_knowledge_points.get(course_id, set())
        knowledge_points_str = course_knowledge_points_str.get(str(course_id), set())
        
        for group in paper_detail.get("groups", []):
            for question in group.get("questions", []):
                for knowledge in question.get("knowledges", []):
                    if knowledge and knowledge.get("name"):
                        knowledge_points.add(knowledge["name"])
                        knowledge_points_str.add(knowledge["name"])

        course_knowledge_points[course_id] = knowledge_points
        course_knowledge_points_str[str(course_id)] = knowledge_points_str

    @classmethod
    def get_outline_by_id_cached(cls, outline_number, cache_timeout=300):
        """带缓存的 outline 获取方法"""
        cache_key = f"outline_structure_{outline_number}"
        cached_data = cache.get(cache_key)

        if cached_data is not None:
            logger.info(f"从缓存获取 outline {outline_number}")
            return cached_data

        logger.info(f"从API获取 outline {outline_number}")
        data = yantucs_data_client.get_outline_by_id(outline_number)
        cache.set(cache_key, data, timeout=cache_timeout)
        return data

    @classmethod
    def get_courses_by_ids_cached(cls, course_ids, cache_timeout=300):
        """带缓存的课程详情获取方法"""
        # 对课程ID列表进行排序，确保缓存键的一致性
        sorted_ids = sorted(course_ids)
        cache_key = f"courses_details_{hash(tuple(sorted_ids))}"
        cached_data = cache.get(cache_key)

        if cached_data is not None:
            logger.info(f"从缓存获取课程详情，数量: {len(course_ids)}")
            return cached_data

        logger.info(f"从API获取课程详情，数量: {len(course_ids)}")
        data = yantucs_data_client.get_courses_by_ids(course_ids)
        cache.set(cache_key, data, timeout=cache_timeout)
        return data

    @classmethod
    def parse_nature_status(cls,nature_status):
        
        mapping = {
            "major": "必修",
            "minor": "辅修"
        }

        return mapping[nature_status] if nature_status in mapping else None