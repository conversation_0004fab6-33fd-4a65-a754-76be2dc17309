import json
import os

from nacos import NacosClient


class Nacos:
    def __init__(self, data_id, group):
        self._client = None
        self.server_addresses = os.environ.get('SERVER_ADDRESSES', 'mse-23e8eb40-p.nacos-ans.mse.aliyuncs.com')
        self.namespace = os.environ.get('SERVER_NAMESPACE', '36c975ad-5369-4939-bdee-786209baf846')
        self.data_id = data_id
        self.group = group

    @property
    def client(self):
        if self._client:
            return self._client

        self._client = NacosClient(self.server_addresses, namespace=self.namespace)
        return self._client

    def get_config(self):
        return self.client.get_config(data_id=self.data_id, group=self.group)

    def get_json_config(self):
        return json.loads(self.get_config())


nacos = Nacos(data_id='AI_APPLICATION', group='YANTU')
