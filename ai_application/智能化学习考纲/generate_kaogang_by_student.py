import ast
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import django
import json
import re
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ai_application.settings")
django.setup()

import json
import re
import logging
import os

import numpy as np
import pandas as pd
from typing import Dict, Optional, Union
from app.models import ExamAnalysisKnowledgePointWithStats
from concurrent.futures import ThreadPoolExecutor, as_completed
from django.conf import settings
from langchain_openai import ChatOpenAI
from langchain_core.prompts import PromptTemplate
from app.models import KaoGangAnalysis, SuperViseInitStudentStatus, PersonalizedExamSyllabus

# 添加日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置文本大模型
llm_text = ChatOpenAI(
    openai_api_key=settings.DOUBAO_API_KEY,
    openai_api_base="https://ark.cn-beijing.volces.com/api/v3",
    model_name="doubao-seed-1-6-thinking-250715",
    max_tokens=20480
)


def load_knowledge_priority():
    """
    加载知识点优先度数据
    """
    try:
        with open(os.path.join(os.path.dirname(__file__), '..', 'sorted_knowledge_priority.json'), 'r',
                  encoding='utf-8') as f:
            priority_data = json.load(f)
        return priority_data
    except Exception as e:
        logger.error(f"加载知识点优先度数据失败: {e}")
        return {}


def generate_all_subjects_kaogang(kaogang_content, student_info, knowledge_plan):
    """
    调用大模型：一次性为所有科目生成个性化考纲，并根据知识点优先度分配掌握程度
    """
    template = """### 角色：你是一个专业的408计算机考研辅导专家。请根据以下信息为用户生成408计算机考研的个性化学习大纲，包含数据结构、计算机组成原理、操作系统、计算机网络四个科目。

## 当前时间：2025年

## 408计算机考研大纲内容：
{kaogang_content}

## 学生目标分数：
{student_info}

## 考到目标分数优先需要掌握的知识点：
{knowledge_plan}

# 要求：
请结合408计算机考研大纲内容和学生基础信息，为学生生成一份个性化的408计算机考研学习大纲：
    - 大纲需根据目标分数选择性的推荐对应的考点内容
    - 大纲需要包含数据结构、计算机组成原理、操作系统、计算机网络四个科目
    - 列表需包含章节、考点内容、重要程度、考试题型、掌握程度、难度等级这几列
    - 在每个科目考研大纲列表的最后一列加上掌握程度列，仅用五角星数量表示即可
      - 掌握程度标识对应五角星数量：
            了解，选学   ：  ☆
            了解，识记   ：  ☆☆
            理解，会用   ：  ☆☆☆
            熟悉，重点练 ： ☆☆☆☆
            掌握，必须会 ： ☆☆☆☆☆
    - 将难度等级列的1、3、5、7、9分别对应一级、二级、三级、四级、五级

# 掌握程度要求：
- 根据考到目标分数优先需要掌握的知识点:{knowledge_plan}调整考点内容掌握程度：
    考点内容掌握程度调整规则（参考核心标准）：
        1. 同时还要根据学生的目标总分、以及难度调整考点内容的掌握程度
            - 对应的目标总分越高、对难度高的考点内容的掌握程度也越高
            - 对应的目标总分越低，对难度高的考点内容的掌握程度要适当降低
        2. 根据优先需要掌握的知识点顺序，越排在前面的掌握程度需要越高
        
# 推荐的考点内容要求：
    1. 根据目标分数选择性的推荐对应的考点内容：
        - 目标分数大于125分时，推荐难度程度为四级、五级为主的考点内容为主，三级为辅
        - 目标分数大于115分时，推荐难度等级为三级、四级、五级考点内容的为主
        - 目标分数在80-115分时，推荐难度等级为三级、四级的考点内容为主，二级、五级为辅
        - 目标分数小于80分时，推荐难度等级为一级、二级、三级的考点内容为主
        
            
### 输出内容结构要求：
- 需在标题下面说明用户的目标分数，在学科中间的间隙加上一些风趣的鼓励解释的话语，
- 无需解释说明大纲内容的难度等级及掌握程度，只需直接输出用户大纲内容及备考建议
- 输出格式应包含清晰的标题结构，如"## 数据结构"、"## 计算机组成原理"等
- 重要程度务必用★五角星数量表示程度高低，掌握程度务必用☆五角星数量表示程度高低
- 用户的个性化大纲需是结合目标分数的掌握程度达四星五星的考点为主
- 同时输出的考点内容需以推荐的考点内容要求里的核心规则为准

### 限制：
- 推荐的考点内容要求需重点注意
- 备考建议不可以出现时间规划
- 无需根据目标总分来划分到每个科目的目标分数
- 输出内容应结构清晰，便于学生理解和执行

请根据以上信息生成详细的个性化408计算机考研学习大纲。
"""

    prompt = PromptTemplate.from_template(template)
    llm_chain = prompt | llm_text

    # 调用大模型生成所有科目的个性化考研大纲
    result = llm_chain.invoke({
        "kaogang_content": kaogang_content or '',
        "student_info": student_info or '',
        "knowledge_plan":knowledge_plan
    })

    return result


def generate_personal_kaogang(target_score):
    """
    根据KaoGangAnalysis数据库记录和SuperViseInitStudentStatus数据库记录生成个性化考研大纲
    """
    try:
        # 检查数据库中是否已存在该分数的考纲且exam_syllabus不为空
        existing_record = PersonalizedExamSyllabus.objects.filter(student_score=target_score).first()
        # if existing_record and existing_record.exam_syllabus:
        #     logger.info(f"分数 {target_score} 的个性化考纲已存在，跳过生成")
        #     return target_score, "exists"

        # 获取数学学科的考纲内容
        kaogang_content = KaoGangAnalysis.objects.filter(subject="408计算机").first()
        print(f"🚀", kaogang_content)
        if not kaogang_content:
            logger.error("KaoGangAnalysis表中没有408计算机科目的数据")
            return target_score, None

        # 根据subject2_content值确定alpha和beta参数
        if target_score >= 115:
            alpha = 0.6
            beta = 0.2
        elif 80 <= target_score < 115:
            alpha = 0.7
            beta = 0.3
        else:  # target_score < 80
            alpha = 0.8
            beta = 0.4

        SUBJECT_MAX_SCORES = {
            '计算机组成原理': 45,
            '计算机网络': 25,
            '操作系统': 35,
            '数据结构': 45
        }

        # 读取数据
        df = load_knowledge_points()

        # 初始化（默认不按科目配分，直接全局把 150 分按频次分摊）
        selector = KnowledgePointSelector(
            df=df,
            exam_total_score=150,
            alpha=alpha,
            beta=beta,
            kappa=1.0,
            use_subject_caps=False,  # 若想尊重科目配分改 True
            subject_max_scores=SUBJECT_MAX_SCORES
        )

        plan = selector.recommend_to_target(
            target_score=target_score,
            safety=1.05,
            ensure_per_subject_k=2,  # 或 {'计算机网络':2, '数据结构':1}
            rank_by='priority'  # 也可 'gain'
        )
        # print(plan.to_string(index=False))
        knowledge_plan = plan.to_string(index=False)

        # 调用大模型一次性处理四门学科
        result = generate_all_subjects_kaogang(kaogang_content.kaogang_content, target_score, knowledge_plan)

        # 组合结果
        content = result.content if hasattr(result, 'content') else str(result)
        
        # # 保存到PersonalizedExamSyllabus数据库
        # if existing_record and existing_record.exam_syllabus:
        #     # 如果记录已存在且exam_syllabus不为空，跳过该条记录
        #     logger.info(f"分数 {target_score} 的个性化考纲已存在，跳过更新")
        #     return target_score, "exists"

        if existing_record and existing_record.exam_syllabus:
            # 如果记录已存在且exam_syllabus不为空,更新这条记录
            existing_record.exam_syllabus = content
            existing_record.save()
            logger.info(f"个性化考纲已创建到数据库，学生分数: {target_score}")
        
        return target_score, content

    except Exception as e:
        logger.error(f"生成个性化考研大纲时发生未预期的异常: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return target_score, None


# -*- coding: utf-8 -*-


# ---------------------------
# 1) 数据加载
# ---------------------------
def load_knowledge_points(subjects=None) -> pd.DataFrame:
    """
    从数据库读取知识点数据 -> DataFrame
    需要字段: subject, knowledge_point, total_freq, avg_difficulty
    其他字段保留以便后续扩展，但本简化方案不使用。
    """
    if subjects is None:
        subjects = ['计算机组成原理', '计算机网络', '操作系统', '数据结构']

    data = {
        'subject': [],
        'knowledge_point': [],
        'total_freq': [],
        'avg_difficulty': [],
        'mcq_freq': [],
        'mcq_difficulty': [],
        'subjective_freq': [],
        'subjective_difficulty': []
    }
    for subj in subjects:
        qs = ExamAnalysisKnowledgePointWithStats.objects.filter(subject=subj).values()
        for item in qs:
            data['subject'].append(item["subject"])
            data['knowledge_point'].append(item["point_name"])
            data['total_freq'].append(item.get("exam_count", 0))
            data['avg_difficulty'].append(item.get("avg_difficulty", 0.0))
            data['mcq_freq'].append(item.get("choice_count", 0))
            data['mcq_difficulty'].append(item.get("choice_avg_difficulty", 0.0))
            data['subjective_freq'].append(item.get("comprehensive_count", 0))
            data['subjective_difficulty'].append(item.get("comprehensive_avg_difficulty", 0.0))

    df = pd.DataFrame(data)
    # 基础清洗与类型安全
    for col in ['total_freq', 'avg_difficulty']:
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0.0)
    return df


# ---------------------------
# 2) 优先级与选集：核心类
# ---------------------------
class KnowledgePointSelector:
    """
    简化方案（科目内 z-score + 近似分值质量 + 可学性 + 贪心累加到目标分）
    """

    def __init__(
            self,
            df: pd.DataFrame,
            exam_total_score: float = 150.0,
            alpha: float = 0.7,  # priority 对考频的权重
            beta: float = 0.3,  # priority 对难度惩罚的权重
            kappa: float = 1.0,  # learnability 中对难度的敏感度
            use_subject_caps: bool = False,  # 是否按科目满分分摊分值质量
            subject_max_scores: Optional[Dict[str, float]] = None,
            subject_col: str = 'subject'
    ):
        self.df = df.copy()
        self.exam_total_score = float(exam_total_score)
        self.alpha = float(alpha)
        self.beta = float(beta)
        self.kappa = float(kappa)
        self.use_subject_caps = bool(use_subject_caps)
        self.subject_max_scores = subject_max_scores or {}
        self.subject_col = subject_col

        self._validate()
        self._compute_groupwise_zscores()
        self._compute_score_mass()
        self._compute_learnability()
        self._compute_gain()
        self._compute_priority()

    # ---------- 工具 ----------
    def _validate(self):
        needed = {'subject', 'knowledge_point', 'total_freq', 'avg_difficulty'}
        missing = needed - set(self.df.columns)
        if missing:
            raise ValueError(f"缺少必要字段: {missing}")
        # 过滤无 subject / 知识点的脏数据
        self.df['subject'] = self.df['subject'].astype(str)
        self.df['knowledge_point'] = self.df['knowledge_point'].astype(str)
        self.df = self.df[(self.df['subject'] != '') & (self.df['knowledge_point'] != '')].reset_index(drop=True)

    @staticmethod
    def _zscore_safe(x: pd.Series) -> pd.Series:
        mu = x.mean()
        sigma = x.std(ddof=0)
        if sigma == 0 or np.isnan(sigma):
            return pd.Series(np.zeros(len(x)), index=x.index)
        return (x - mu) / sigma

    # ---------- 核心计算 ----------
    def _compute_groupwise_zscores(self):
        # 科目内 z-score
        self.df['z_total_freq'] = self.df.groupby(self.subject_col)['total_freq'].transform(self._zscore_safe)
        self.df['z_avg_difficulty'] = self.df.groupby(self.subject_col)['avg_difficulty'].transform(self._zscore_safe)

    def _compute_score_mass(self):
        """
        以考频近似“分值质量”。
        - 默认: 全局按 total_freq 占比把 exam_total_score 分摊到每个知识点。
        - 可选: use_subject_caps=True 时，先在科目内按占比分摊到科目满分，再汇总（更贴合官方配分）。
        """
        if self.use_subject_caps:
            # 按科目满分分摊
            if not self.subject_max_scores:
                raise ValueError("use_subject_caps=True 但未提供 subject_max_scores。")
            self.df['score_mass'] = 0.0
            for subj, g in self.df.groupby(self.subject_col):
                cap = float(self.subject_max_scores.get(subj, 0.0))
                den = g['total_freq'].sum()
                if den <= 0:
                    # 若该科目无频次，均分该科满分（或直接给 0；此处采用均分更温和）
                    share = (cap / max(len(g), 1))
                    self.df.loc[g.index, 'score_mass'] = share
                else:
                    self.df.loc[g.index, 'score_mass'] = cap * (g['total_freq'] / den)
            # 校验总体近似为 sum(cap)
        else:
            # 全局分摊
            den = self.df['total_freq'].sum()
            if den <= 0:
                # 极端情况：所有频次为 0，则全体均分到总分
                self.df['score_mass'] = self.exam_total_score / max(len(self.df), 1)
            else:
                self.df['score_mass'] = self.exam_total_score * (self.df['total_freq'] / den)

    def _compute_learnability(self):
        """
        根据难度的科目内 z 分数，使用逻辑函数得到可学性(0~1)，难度越大越小。
        l = 1 / (1 + exp(kappa * z_diff))
        """
        z = self.df['z_avg_difficulty'].astype(float)
        self.df['learnability'] = 1.0 / (1.0 + np.exp(self.kappa * z))

    def _compute_gain(self):
        """
        预期可拿分 = 分值质量 * 可学性
        """
        self.df['gain'] = self.df['score_mass'] * self.df['learnability']

    def _compute_priority(self):
        """
        性价比优先级：priority = alpha * z_freq - beta * z_diff
        """
        self.df['priority'] = self.alpha * self.df['z_total_freq'] - self.beta * self.df['z_avg_difficulty']

    # ---------- 对外接口 ----------
    def recommend_global(self, top_k: int = 30, by: str = 'priority') -> pd.DataFrame:
        """
        返回全局 Top-K，by 可选 'priority' 或 'gain'
        """
        by = by.lower()
        if by not in ('priority', 'gain'):
            raise ValueError("by 必须是 'priority' 或 'gain'")

        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority'
        ]
        df_sorted = (self.df.sort_values([by, 'total_freq', 'avg_difficulty'],
                                         ascending=[False, False, True])
                     .loc[:, cols]
                     .reset_index(drop=True))
        return df_sorted.head(top_k)

    def recommend_per_subject(self, k_per_subject: int = 5, by: str = 'priority') -> pd.DataFrame:
        """
        每科 Top-k，by 可选 'priority' 或 'gain'
        """
        by = by.lower()
        if by not in ('priority', 'gain'):
            raise ValueError("by 必须是 'priority' 或 'gain'")

        def topk(g):
            return g.sort_values([by, 'total_freq', 'avg_difficulty'],
                                 ascending=[False, False, True]).head(k_per_subject)

        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority'
        ]
        out = (self.df.groupby(self.subject_col, group_keys=False)
               .apply(topk)
               .loc[:, cols]
               .reset_index(drop=True))
        return out

    def recommend_to_target(
            self,
            target_score,
            safety: float = 1.05,
            ensure_per_subject_k: Optional[Union[int, Dict[str, int]]] = None,
            rank_by: str = 'priority'
    ) -> pd.DataFrame:
        """
        给定目标分，输出一个“学习清单”，使累计 gain ≥ target_score * safety。
        - ensure_per_subject_k:
            * None: 不强制覆盖
            * int: 每个科目至少取 k 个
            * dict: 每科自定义保底数量，如 {'计算机网络':2, '数据结构':1}
        - rank_by: 'priority' 或 'gain'
        返回列包含 cumulative_gain 以便查看覆盖进度。
        """
        rank_by = rank_by.lower()
        if rank_by not in ('priority', 'gain'):
            raise ValueError("rank_by 必须是 'priority' 或 'gain'")

        # 1) 初步排序（稳定 tie-breaker：total_freq desc, avg_difficulty asc, kp 名字字典序）
        base = self.df.assign(
            kp_key=self.df['knowledge_point'].astype(str)
        ).sort_values(
            [rank_by, 'total_freq', 'avg_difficulty', 'kp_key'],
            ascending=[False, False, True, True]
        ).drop(columns=['kp_key'])

        # 2) 先做科目保底（如果需要）
        picked_idx = []
        if ensure_per_subject_k is not None:
            if isinstance(ensure_per_subject_k, int):
                want = {s: ensure_per_subject_k for s in base[self.subject_col].unique()}
            elif isinstance(ensure_per_subject_k, dict):
                want = {str(k): int(v) for k, v in ensure_per_subject_k.items()}
            else:
                raise ValueError("ensure_per_subject_k 需为 int 或 dict")

            for subj, need in want.items():
                if need <= 0:
                    continue
                g = base[base[self.subject_col] == subj]
                take = g.head(need)
                picked_idx.extend(list(take.index))

        picked_idx = list(dict.fromkeys(picked_idx))  # 去重保持顺序
        picked = base.loc[picked_idx].copy()
        rest = base.drop(index=picked_idx)

        # 3) 贪心补齐到目标 gain
        goal = float(target_score) * float(safety)
        if not picked.empty:
            picked['cumulative_gain'] = picked['gain'].cumsum()
        else:
            picked = pd.DataFrame(columns=base.columns.tolist() + ['cumulative_gain'])

        current = picked['gain'].sum() if not picked.empty else 0.0
        for i, row in rest.iterrows():
            if current >= goal:
                break
            picked = pd.concat([picked, row.to_frame().T], ignore_index=True)
            current += float(row['gain'])
            picked.loc[picked.index[-1], 'cumulative_gain'] = current

        # 4) 结果整理
        cols = [
            'subject', 'knowledge_point',
            'total_freq', 'avg_difficulty',
            'z_total_freq', 'z_avg_difficulty',
            'score_mass', 'learnability', 'gain',
            'priority', 'cumulative_gain'
        ]
        result = picked.loc[:, cols].reset_index(drop=True)

        # 附加元信息
        result.attrs['target_score'] = target_score
        result.attrs['safety'] = safety
        result.attrs['goal_gain'] = goal
        result.attrs['achieved_gain'] = float(result['gain'].sum()) if not result.empty else 0.0
        result.attrs['alpha'] = self.alpha
        result.attrs['beta'] = self.beta
        result.attrs['kappa'] = self.kappa
        result.attrs['use_subject_caps'] = self.use_subject_caps
        return result


# ---------------------------
# 3) 示例运行
# ---------------------------
# if __name__ == '__main__':
#     SUBJECT_MAX_SCORES = {
#         '计算机组成原理': 45,
#         '计算机网络': 25,
#         '操作系统': 35,
#         '数据结构': 45
#     }
#
#     # 读取数据
#     df = load_knowledge_points()
#
#     # 初始化（默认不按科目配分，直接全局把 150 分按频次分摊）
#     selector = KnowledgePointSelector(
#         df=df,
#         exam_total_score=150,
#         alpha=0.7,
#         beta=0.3,
#         kappa=1.0,
#         use_subject_caps=False,  # 若想尊重科目配分改 True
#         subject_max_scores=SUBJECT_MAX_SCORES
#     )
#
#     # 全局 Top-30（按 priority）
#     print("=" * 60)
#     print("全局 Top-30（按 priority）")
#     print("=" * 60)
#     print(selector.recommend_global(top_k=30, by='priority').to_string(index=False))
#
#     # 每科 Top-5（按 priority）
#     print("\n" + "=" * 60)
#     print("各科 Top-5（按 priority）")
#     print("=" * 60)
#     per_subj = selector.recommend_per_subject(k_per_subject=5, by='priority')
#     for subj, g in per_subj.groupby('subject'):
#         print(f"\n【{subj}】")
#         print(g.drop(columns=['subject']).to_string(index=False))
#
#     # 面向目标分：114 分（含 5% 安全系数），先每科保底 2 个，再全局补齐
#     print("\n" + "=" * 60)
#     print("学习清单：面向 114/150（safety=1.05），每科保底 2 个（按 priority 先选）")
#     print("=" * 60)
#     plan = selector.recommend_to_target(
#         target_score=114,
#         safety=1.05,
#         ensure_per_subject_k=2,  # 或 {'计算机网络':2, '数据结构':1}
#         rank_by='priority'  # 也可 'gain'
#     )
#     print(plan.to_string(index=False))
#     print("\n--- 摘要 ---")
#     print(f"目标分: 114  | 安全系数: 1.05  | 覆盖目标 gain: {plan.attrs['goal_gain']:.2f}")
#     print(f"累计 gain: {plan.attrs['achieved_gain']:.2f}  | 选中条目: {len(plan)}")
#     print(
#         f"α={plan.attrs['alpha']}, β={plan.attrs['beta']}, κ={plan.attrs['kappa']}, use_subject_caps={plan.attrs['use_subject_caps']}")
#

def save_result_as_markdown(result, filename):
    """
    将结果保存为Markdown文件

    Args:
        result: 模型生成的结果
        filename: 保存的文件名

    Returns:
        str: 保存的文件路径
    """
    # 确保文件以.md结尾
    if not filename.endswith('.md'):
        filename += '.md'

    # 保存到文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(result)

    return filename

def batch_generate_kaogang(min_score=45, max_score=150, max_workers=10):
    """
    批量并行生成指定分数范围内的个性化考纲
    
    Args:
        min_score: 最低分数
        max_score: 最高分数
        max_workers: 最大并发线程数
    """
    logger.info(f"开始批量生成 {min_score}-{max_score} 分的个性化考纲")
    
    # 创建分数列表
    scores = list(range(min_score, max_score + 1))
    
    # 使用线程池并发执行
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_score = {executor.submit(generate_personal_kaogang, score): score for score in scores}
        
        # 收集结果
        results = []
        for future in as_completed(future_to_score):
            score = future_to_score[future]
            try:
                result = future.result()
                results.append(result)
                logger.info(f"分数 {score} 的考纲生成完成")
            except Exception as e:
                logger.error(f"分数 {score} 的考纲生成出错: {e}")
                
    logger.info(f"批量生成完成，共处理 {len(results)} 个分数的考纲")
    return results

if __name__ == "__main__":
    # 批量生成45-150分的个性化考纲
    batch_generate_kaogang(45, 150)
    
    # 如果需要单独生成某个分数的考纲并保存为文件，可以使用以下代码
    # result = generate_personal_kaogang(100)
    # if result:
    #     # 保存结果为文件
    #     saved_file = save_result_as_markdown(result, "408个性化考研大纲.md")
    #     print(f"个性化考研大纲已保存到: {saved_file}")
    #     
    #     # 打印结果内容
    #     print("生成的个性化考研大纲内容:")
    #     print(result)
    # else:
    #     print("生成个性化考研大纲失败")