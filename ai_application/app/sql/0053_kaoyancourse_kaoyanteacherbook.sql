--
-- Create model <PERSON><PERSON><PERSON><PERSON><PERSON>ourse
--
CREATE TABLE `ai_kao_yan_course` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `name` varchar(255) NOT NULL);
--
-- Create model <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
--
CREATE TABLE `ai_kao_yan_teacher_book` (`id` bigint AUTO_INCREMENT NOT NULL PRIMARY KEY, `add_time` datetime(6) NOT NULL, `modified_time` datetime(6) NOT NULL, `is_deleted` bool NOT NULL, `teacher_name` varchar(255) NOT NULL, `book_name` varchar(255) NOT NULL, `teacher_profile` longtext NOT NULL);
CREATE INDEX `ai_kao_yan_course_add_time_68c4b288` ON `ai_kao_yan_course` (`add_time`);
CREATE INDEX `ai_kao_yan_teacher_book_add_time_69c5936d` ON `ai_kao_yan_teacher_book` (`add_time`);