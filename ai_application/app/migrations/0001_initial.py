# Generated by Django 4.2 on 2024-07-17 13:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('name', models.CharField(max_length=32, verbose_name='名称')),
                ('api_key', models.CharField(max_length=32, unique=True)),
                ('secret_key', models.CharField(blank=True, max_length=100)),
            ],
            options={
                'verbose_name': '账号',
                'verbose_name_plural': '账号',
                'db_table': 'ai_account',
            },
        ),
        migrations.CreateModel(
            name='App',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('app_no', models.CharField(max_length=32, unique=True, verbose_name='应用id')),
                ('mode', models.CharField(blank=True, max_length=32, verbose_name='模式')),
                ('name', models.CharField(blank=True, max_length=32, verbose_name='应用名称')),
                ('is_public', models.BooleanField(default=False, verbose_name='是否公开')),
                ('account', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.account')),
            ],
            options={
                'verbose_name': '应用',
                'verbose_name_plural': '应用',
                'db_table': 'ai_app',
            },
        ),
        migrations.CreateModel(
            name='AppModelConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('app_no', models.CharField(db_index=True, max_length=32, verbose_name='应用id')),
                ('mode', models.CharField(max_length=32, verbose_name='模式')),
                ('run_type', models.CharField(blank=True, max_length=16, verbose_name='应用方式')),
                ('model_provider', models.CharField(blank=True, max_length=32, verbose_name='模型提供方')),
                ('model_id', models.CharField(blank=True, max_length=32, verbose_name='模型名称')),
                ('third_app_key', models.CharField(blank=True, max_length=32, verbose_name='三方应用id')),
                ('support_params', models.JSONField(null=True, verbose_name='支持参数')),
            ],
            options={
                'verbose_name': '应用模式配置',
                'verbose_name_plural': '应用模式配置',
                'db_table': 'ai_app_model_config',
            },
        ),
        migrations.CreateModel(
            name='Conversation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('conversation_no', models.CharField(max_length=36, unique=True, verbose_name='会话id')),
                ('model_provider', models.CharField(blank=True, max_length=32, verbose_name='模型提供方')),
                ('model_id', models.CharField(blank=True, max_length=32, verbose_name='模型名称')),
                ('inputs', models.JSONField(null=True, verbose_name='表单参数')),
                ('model_params', models.JSONField(null=True, verbose_name='模型参数')),
                ('pre_prompt', models.TextField(null=True, verbose_name='预设提示')),
                ('name', models.CharField(blank=True, max_length=200, verbose_name='会话名称')),
                ('status', models.CharField(default='normal', max_length=32, verbose_name='会话状态')),
                ('app', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.app')),
                ('app_model_config', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.appmodelconfig')),
                ('from_account', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.account')),
            ],
            options={
                'verbose_name': '会话历史',
                'verbose_name_plural': '会话历史',
                'db_table': 'ai_conversation',
            },
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('add_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('modified_time', models.DateTimeField(auto_now=True, verbose_name='修改时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='删除状态')),
                ('message_no', models.CharField(max_length=36, unique=True, verbose_name='消息id')),
                ('model_provider', models.CharField(blank=True, max_length=32, verbose_name='模型提供方')),
                ('model_id', models.CharField(blank=True, max_length=32, verbose_name='模型名称')),
                ('query', models.TextField(null=True, verbose_name='用户输入')),
                ('message', models.JSONField(null=True, verbose_name='整体输入')),
                ('message_tokens', models.IntegerField(default=0, verbose_name='消息tokens')),
                ('answer', models.TextField(null=True, verbose_name='回答')),
                ('answer_tokens', models.IntegerField(default=0, verbose_name='回答tokens')),
                ('total_tokens', models.IntegerField(default=0, verbose_name='全部tokens')),
                ('response_latency', models.FloatField(default=0, verbose_name='响应时间')),
                ('status', models.CharField(default='not_answered', max_length=32, verbose_name='消息状态')),
                ('stopped_by', models.CharField(blank=True, max_length=16, verbose_name='停止原因')),
                ('error', models.TextField(null=True, verbose_name='错误信息')),
                ('replaced_message_no', models.CharField(blank=True, max_length=36, verbose_name='被替换的消息id')),
                ('app', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.app')),
                ('app_model_config', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.appmodelconfig')),
                ('conversation', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.conversation')),
                ('from_account', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.account')),
            ],
            options={
                'verbose_name': '会话消息',
                'verbose_name_plural': '会话消息',
                'db_table': 'ai_message',
            },
        ),
        migrations.AddField(
            model_name='app',
            name='app_model_config',
            field=models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='app.appmodelconfig'),
        ),
    ]
