<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>个性考纲分析指导</title>
    <script src="https://cdn.kaoyanvip.cn/marked/13.0.2/marked.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            width: 70%;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .user-info {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
        .user-info-item {
            display: inline-block;
            margin-right: 30px;
            font-weight: bold;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #333;
        }
        .subject-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .subject-item {
            background-color: #f0f8ff;
            border: 1px solid #cce6ff;
            border-radius: 5px;
            padding: 15px;
            min-width: 150px;
            text-align: center;
        }
        .subject-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .subject-score {
            font-size: 20px;
            color: #e67300;
            font-weight: bold;
        }
        .submit-btn {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .submit-btn:hover {
            background-color: #005a87;
        }
        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .result-container {
            display: none;
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        .result-content {
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            font-size: 14px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 100%;
            box-sizing: border-box;
        }
        /* 添加Markdown样式支持 */
        .result-content h1, .result-content h2, .result-content h3,
        .result-content h4, .result-content h5, .result-content h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        .result-content h1 {
            font-size: 2em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }
        .result-content h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }
        .result-content p {
            margin-top: 0;
            margin-bottom: 16px;
        }
        .result-content ul, .result-content ol {
            padding-left: 2em;
            margin-top: 0;
            margin-bottom: 16px;
        }
        .result-content ul ul, .result-content ul ol,
        .result-content ol ol, .result-content ol ul {
            margin-top: 0;
            margin-bottom: 0;
        }
        .result-content li {
            margin-top: 0.25em;
            margin-bottom: 0.25em;
        }
        .result-content li > p {
            margin-top: 16px;
        }
        .result-content pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
        }
        .result-content code {
            background-color: rgba(27,31,35,0.05);
            border-radius: 6px;
            padding: 0.2em 0.4em;
            font-size: 85%;
        }
        .result-content pre code {
            background: none;
            padding: 0;
        }
        .result-content blockquote {
            margin: 0;
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
        }
        .result-content table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 16px;
            margin-bottom: 16px;
        }
        .result-content table th, .result-content table td {
            padding: 6px 13px;
            border: 1px solid #dfe2e5;
        }
        .result-content table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }
        .error-container {
            display: none;
            background: #fff5f5;
            border: 2px solid #fed7d7;
            border-radius: 10px;
            padding: 20px;
            color: #c53030;
            margin-top: 20px;
        }
        .error-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container" style="width: 70%; margin: 0 auto;">
        <div class="header">
            <h1>个性考纲分析指导</h1>
        </div>
        
        <div class="user-info">
            {% if user_id %}
            <div class="user-info-item">
                <strong>用户ID:</strong> {{ user_id }}
            </div>
            {% endif %}
            
            {% if initial_score %}
            <div class="user-info-item">
                <strong>初试分数:</strong> {{ initial_score }}
            </div>
            {% endif %}
        </div>
        
        <div class="section">
            <div class="section-title">考试目标及分数目标</div>
            <div class="subject-list">
                {% if syllabus_data %}
                {% for subject in syllabus_data %}
                <div class="subject-item">
                    <div class="subject-name">{{ subject.subject }}</div>
                    <div class="subject-score">{{ subject.score }}</div>
                </div>
                {% endfor %}
                {% else %}
                <p>暂无考纲数据</p>
                {% endif %}
            </div>
        </div>
        
        <!-- 408个性化考纲按钮和显示区域 -->
        <div style="margin-top: 30px;">
            <button id="politicsBtn" class="submit-btn" onclick="showUnsupportedSubject('政治')" style="margin-right: 10px;">政治个性化考纲</button>
            <button id="englishBtn" class="submit-btn" onclick="showUnsupportedSubject('英语')" style="margin-right: 10px;">英语个性化考纲</button>
            <button id="mathBtn" class="submit-btn" onclick="showUnsupportedSubject('数学')" style="margin-right: 10px;">数学个性化考纲</button>
            <button id="syllabusBtn" class="submit-btn" onclick="get408Syllabus()">408个性化考纲</button>
            
            <div id="syllabusLoading" class="loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在获取考纲...</div>
            </div>
            
            <div class="result-container" id="syllabusContentContainer">
                <div class="result-content" id="syllabusContent"></div>
            </div>
            
            <div class="error-container" id="syllabusErrorContainer">
                <div class="error-title">获取失败</div>
                <div id="syllabusError"></div>
            </div>
        </div>
    </div>

    <script>
        // 显示不支持科目的提示
        function showUnsupportedSubject(subjectName) {
            // 隐藏可能正在显示的加载状态和错误状态
            document.getElementById('syllabusLoading').style.display = 'none';
            document.getElementById('syllabusErrorContainer').style.display = 'none';
            
            // 显示结果容器并设置提示内容
            const contentContainer = document.getElementById('syllabusContentContainer');
            const contentElement = document.getElementById('syllabusContent');
            
            contentElement.innerHTML = '<p style="text-align: center; color: #666; font-size: 16px; padding: 20px;">暂不支持' + subjectName + '科目</p>';
            contentContainer.style.display = 'block';
        }
        
        // 获取408个性化考纲
        async function get408Syllabus() {
            // 检查是否提供了task_id参数
            const urlParams = new URLSearchParams(window.location.search);
            const taskId = urlParams.get('task_id');
            
            if (!taskId) {
                alert('缺少任务ID参数');
                return;
            }

            const syllabusBtn = document.getElementById('syllabusBtn');
            const syllabusLoading = document.getElementById('syllabusLoading');
            const syllabusContentContainer = document.getElementById('syllabusContentContainer');
            const syllabusContent = document.getElementById('syllabusContent');
            const syllabusErrorContainer = document.getElementById('syllabusErrorContainer');
            const syllabusError = document.getElementById('syllabusError');

            // 重置显示状态
            syllabusContentContainer.style.display = 'none';
            syllabusErrorContainer.style.display = 'none';
            syllabusLoading.style.display = 'block';
            syllabusBtn.disabled = true;

            try {
                const response = await fetch(`/console/app/408_personal_syllabus?task_id=${encodeURIComponent(taskId)}`);
                
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.status === 'success') {
                    // 处理内容以确保不会超出边界
                    const content = result.data || '';
                    // 使用marked渲染Markdown内容
                    syllabusContent.innerHTML = marked.parse(content);
                    syllabusContentContainer.style.display = 'block';
                } else {
                    const errorMessage = result.message || '获取考纲失败';
                    syllabusError.textContent = errorMessage;
                    syllabusErrorContainer.style.display = 'block';
                }
            } catch (error) {
                const errorMessage = `获取考纲失败: ${error.message}`;
                syllabusError.textContent = errorMessage;
                syllabusErrorContainer.style.display = 'block';
            } finally {
                syllabusLoading.style.display = 'none';
                syllabusBtn.disabled = false;
            }
        }
        
        // 页面加载完成后自动执行
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里添加页面加载完成后的初始化逻辑
        });
    </script>
</body>
</html>