from app.constants.app import AppMode
from app.core.apps.message_tracing_log import MessageTracingLog
from app.core.entities.app_entities import ChatAppGenerateEntity, CompletionAppGenerateEntity
from app.core.invoke_llm import get_llm_model_config, query_llm_by_prompt
from app.models import PromptTemplate, Message


class JudgeIsQuestionFeature:

    def __init__(self, app_no):
        self.prompt_template: PromptTemplate = PromptTemplate.objects.filter(
            app_no='judge_is_408_question').first()
        if not self.prompt_template:
            raise Exception('prompt template not found')

    def run(
            self,
            application_generate_entity: ChatAppGenerateEntity | CompletionAppGenerateEntity,
            message: Message
    ) -> bool:
        if message.file_objs:
            return True

        user_inputs = application_generate_entity.inputs.copy()
        prompt_template = self.prompt_template
        # 构建 prompt
        pre_prompt = prompt_template.assemble_prompt(
            query='',
            inputs=user_inputs,
            prompt_content=''
        )
        model_conf = get_llm_model_config(
            app_model_conf=application_generate_entity.model_conf,
            prompt_template=prompt_template
        )

        result = query_llm_by_prompt(
            model_conf=model_conf,
            app_mode=AppMode.COMPLETION,
            pre_prompt=pre_prompt,
            query=message.query,
            stream=False
        )

        MessageTracingLog(message).add_tracing_by_llm_result(
            tracing_type='judge_is_question',
            query=message.query,
            llm_result=result
        )

        try:
            is_question = int(result)
        except:
            is_question = 0

        return bool(is_question)
