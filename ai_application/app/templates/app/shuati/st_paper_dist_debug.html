<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出题策略调试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.1.0/github-markdown-light.min.css">
    <script src="https://cdn.kaoyanvip.cn/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.kaoyanvip.cn/marked/13.0.2/marked.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            height: 150vh;
            box-sizing: border-box;
        }
        .panel-container {
            flex: 1;
            display: flex;
            margin-bottom: 20px;
            gap: 20px;
        }
        .panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .panel-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .input-area {
            flex: 1;
            margin-bottom: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .regular-input {
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100%;
            box-sizing: border-box;
        }
        textarea {
            width: 100%;
            height: 100%;
            padding: 10px;
            box-sizing: border-box;
            border: 1px solid #ccc;
            border-radius: 4px;
            resize: none;
        }
        .output-area {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #f9f9f9;
            overflow-y: auto;
        }
        .button-container {
            margin-bottom: 10px;
        }
        .secondary-button {
            background-color: #f0f0f0;
            color: #333;
        }
        .secondary-button:hover {
            background-color: #e0e0e0;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        /* 底部按钮样式 */
        .bottom-actions {
            margin-top: 20px;
            text-align: center;
        }
        .continue-btn {
            padding: 12px 24px;          /* 增大按钮尺寸 */
            background-color: #4CAF50;   /* 与提交按钮保持一致的绿色 */
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;             /* 增大字体 */
        }

        .continue-btn:hover {
            background-color: #45a049;   /* 与提交按钮保持一致的悬停效果 */
        }
        /* 自定义alert样式 */
        .custom-alert {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: none;
            max-width: 80%;
            text-align: center;
        }
        .custom-alert-message {
            margin-bottom: 20px;
            font-size: 16px;
        }
        .custom-alert-button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .custom-alert-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 9999;
            display: none;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            display: none;
        }
        .loading-spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4CAF50;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 上半部分 -->
    <div class="panel-container">
        <div class="panel">
            <div class="panel-title">生成出题策略</div>
            <div class="input-area">
                <textarea placeholder="提示词输入..." id="upper-input">{{ dist_prompt }}</textarea>
            </div>
            <div class="button-container">
                <button id="btn-dist-prompt-submit" class="secondary-button">保存提示词</button>
            </div>
            <div class="input-area">
                <textarea placeholder="知识点掌握情况" id="upper-regular-input" class="regular-input"></textarea>
            </div>
            <div class="button-container">
                <button id="btn-dist-submit">提交</button>
            </div>
        </div>
        <div class="panel">
            <div class="panel-title">出题策略输出</div>
            <div class="output-area" id="upper-result"></div>
        </div>
    </div>

    <!-- 下半部分 -->
    <div class="panel-container">
        <div class="panel">
            <div class="panel-title">出题策略参数化</div>
            <div class="input-area">
                <textarea placeholder="提示词输入..." id="lower-input">{{ dist_param_prompt }}</textarea>
            </div>
            <div class="button-container">
                <button id="btn-dist-param-prompt-submit" class="secondary-button">保存提示词</button>
            </div>
            <div class="input-area">
                <textarea placeholder="出题策略" id="lower-regular-input" class="regular-input"></textarea>
            </div>
            <div class="button-container">
                <button id="btn-dist-param-submit">提交</button>
            </div>
        </div>
        <div class="panel">
            <div class="panel-title">参数化输出</div>
            <div class="output-area" id="lower-result"></div>
        </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
        <button class="continue-btn" id="continue-btn" style="padding: 12px 24px; background-color: #4CAF50; font-size: 16px;">使用当前抽题策略继续出题</button>
    </div>

    <!-- 自定义alert结构 -->
    <div class="custom-alert-overlay" id="custom-alert-overlay"></div>
    <div class="custom-alert" id="custom-alert">
        <div class="custom-alert-message" id="custom-alert-message"></div>
        <button class="custom-alert-button" id="custom-alert-button">确定</button>
    </div>

    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <script>
        function showCustomAlert(message) {
            $('#custom-alert-message').text(message);
            $('#custom-alert-overlay').show();
            $('#custom-alert').show();
        }

        $(function() {
            // 绑定自定义alert按钮事件
            $('#custom-alert-button').click(function() {
                $('#custom-alert-overlay').hide();
                $('#custom-alert').hide();
            });

            // 绑定继续出题按钮事件
            $('#continue-btn').click(function() {
                // 获取当前URL中的user_id参数
                const userId = new URLSearchParams(window.location.search).get('user_id');
                // 跳转到与返回首页相同的地址
                window.location.href = `/console/app/st_paper_index?user_id=${userId || ''}`;
            });

            $('#btn-dist-submit').click(async () => {
                const prompt = $('#upper-input').val();
                const input_text = $('#upper-regular-input').val();
                if (!prompt) {
                    showCustomAlert(`请输入生成出题策略提示词`);
                    return
                }

                if (!input_text) {
                    showCustomAlert(`请输入知识点掌握情况`);
                    return
                }

                let buffer = ''; // 事件缓冲区
                const eventSeparator = '\n\n'; // SSE事件分隔符

                // 显示loading
                $('#loading-overlay').show();
                try {
                    const response = await fetch('{% url 'st_paper_dist_gen' %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            input_text: input_text,
                        })
                    });
                    if (!response.ok) throw new Error(`服务器错误: ${response.status}`);

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder('utf-8', { fatal: true });
                    let fullResponse = '';

                    while (true) {
                        const {done, value} = await reader.read();
                        if (done) break;

                        // 解码并累积到缓冲区
                        buffer += decoder.decode(value, {stream: true});

                        // 分割完整的SSE事件
                        let eventEndIndex;
                        while ((eventEndIndex = buffer.indexOf(eventSeparator)) !== -1) {
                            const eventStr = buffer.slice(0, eventEndIndex).trim();
                            buffer = buffer.slice(eventEndIndex + eventSeparator.length);

                            // 处理每一行data
                            const lines = eventStr.split('\n');
                            for (const line of lines) {
                                if (line.startsWith('data: ')) {
                                    const jsonStr = line.slice(6).trim(); // 移除"data: "前缀
                                    if (!jsonStr) continue;

                                    try {
                                        const data = JSON.parse(jsonStr);

                                        // 实时更新回答内容
                                        if (data.answer) {
                                            fullResponse += data.answer;
                                            console.log(fullResponse)
                                            $('#upper-result').html(marked.parse(fullResponse));
                                            // 将生成的出题策略自动填入到下半部分的出题策略输入框
                                            $('#lower-regular-input').val(fullResponse);
                                        }

                                        // 更新统计信息
                                        if (data.event === 'message_end') {
                                            // 隐藏loading
                                            $('#loading-overlay').hide();
                                            console.log('消息结束，统计信息:', data);
                                        }
                                    } catch (parseError) {
                                        console.error('解析失败的JSON数据:', jsonStr);
                                        console.error('解析错误:', parseError);
                                    }
                                }
                            }
                        }
                    }
                    // 处理剩余不完整的事件
                    if (buffer) {
                        console.warn('检测到未完整的SSE事件:', buffer);
                    }
                } catch (error) {
                    // 隐藏loading
                    $('#loading-overlay').hide();
                    console.error('请求失败:', error);
                    showCustomAlert(`请求失败: ${error.message}`);
                }
            })

            $('#btn-dist-param-submit').click(async () => {
                const prompt = $('#lower-input').val();
                const input_text = $('#lower-regular-input').val();
               // 获取URL中的user_id参数
                const userId = new URLSearchParams(window.location.search).get('user_id');
                if (!prompt) {
                    showCustomAlert(`请输入出题策略参数化提示词`);
                    return
                }

                if (!input_text) {
                    showCustomAlert(`请输入出题策略`);
                    return
                }

                // 显示loading
                $('#loading-overlay').show();
                try {
                    const response = await fetch('{% url 'st_paper_dist_param_gen' %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            input_text: input_text,
                            user_id: userId || ''
                        })
                    });

                    const result = await response.json();
                    if (result.code === 0) {
                        $('#lower-result').html(result.data.content);
                    } else {
                        showCustomAlert(result.msg);
                    }
                    // 隐藏loading
                    $('#loading-overlay').hide();

                } catch (error) {
                    // 隐藏loading
                    $('#loading-overlay').hide();
                    console.error('请求失败:', error);
                    showCustomAlert(`请求失败: ${error.message}`);
                }
            })

            $('#btn-dist-prompt-submit').click(async () => {
                const prompt_content = $('#upper-input').val();

                try {
                    const response = await fetch('{% url 'st_paper_dist_prompt_save' %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            app_no: 'shuati_paper_dist',
                            prompt_content: prompt_content,
                        })
                    });

                    const result = await response.json();
                    if (result.code === 0) {
                        showCustomAlert('保存成功！');
                    } else {
                        showCustomAlert(result.msg);
                    }
                } catch (error) {
                    console.error('请求失败:', error);
                    showCustomAlert(`请求失败: ${error.message}`);
                }
            })

            $('#btn-dist-param-prompt-submit').click(async () => {
                const prompt_content = $('#lower-input').val();

                try {
                    const response = await fetch('{% url 'st_paper_dist_prompt_save' %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            app_no: 'shuati_paper_dist_param',
                            prompt_content: prompt_content,
                        })
                    });

                    const result = await response.json();
                    if (result.code === 0) {
                        showCustomAlert('保存成功！');
                    } else {
                        showCustomAlert(result.msg);
                    }
                } catch (error) {
                    console.error('请求失败:', error);
                    showCustomAlert(`请求失败: ${error.message}`);
                }
            })

        })
    </script>
</body>
</html>