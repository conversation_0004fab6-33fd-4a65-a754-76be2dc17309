# 阿纪
import logging

from api_client.base_client import BaseDataClient
from api_client.decorators import catch_error

logger = logging.getLogger(__name__)

__all__ = ['vector_data_client']


class VectorDataClient(BaseDataClient):
    # 生产环境使用vpc内网域名
    host = 'https://sodo-vpc.kaoyanvip.cn'
    host_test = 'https://sodo.kaoyanvip.cn'
    host_local = 'https://all-search.kaoyan-vip.cn'

    @property
    def base_url(self):
        return self.host

    @catch_error({'data': {'total': 0, 'data': []}})
    def get_answer(self, params: dict) -> dict:
        """
        :param params:

        :return:
        """
        url = '/api/v1/dataSearch'
        return self._post(url, data=params, timeout=5)

    @catch_error({'data': {'total': 0, 'data': []}})
    def get_questions_by_ids(self, question_ids: list) -> dict:
        """
        :param question_ids:
        :return:
        """
        url = '/api/v1/searchQuestionByIdList'
        return self._post(url, data={
            'questionIdList': question_ids,
        }, timeout=5)

    @catch_error({'data': {'total': 0, 'data': []}})
    def get_detail(self, params: dict) -> dict:
        """
        :param params:

        :return:
        """
        url = '/api/v1/searchQuestionByIdList'
        return self._post(url, data=params, timeout=5)


vector_data_client = VectorDataClient()
