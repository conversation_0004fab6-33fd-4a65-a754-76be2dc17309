import logging
import time

from django.utils import timezone

from app.core.indexing_runner import IndexingRunner
from app.core.rag.index_processor.index_processor_factory import IndexProcessorFactory
from app.models import Dataset, DatasetDocument, DocumentSegment

logger = logging.getLogger(__name__)


def retry_document_indexing_task(dataset_no: str, document_nos: list[str]):
    logger.info('Start indexing document: {}'.format(document_nos))
    start_at = time.perf_counter()

    dataset: Dataset = Dataset.objects.filter(
            is_deleted=False, dataset_no=dataset_no).first()
    # 资料库不存在，则不处理
    if not dataset:
        return

    dataset_documents = DatasetDocument.objects.filter(
        is_deleted=False, document_no__in=document_nos
    )

    for dataset_document in dataset_documents:
        try:
            # clean old data
            index_processor = IndexProcessorFactory(dataset_document.index_type).init_index_processor()
            segments = DocumentSegment.objects.filter(dataset_document=dataset_document)
            if segments:
                index_node_ids = [segment.index_node_id for segment in segments]
                # delete from vector index
                index_processor.clean(dataset, index_node_ids)
                DocumentSegment.objects.filter(dataset_document=dataset_document).update(
                    is_deleted=True, modified_time=timezone.now()
                )

            # start parsing
            dataset_document.indexing_status = 'parsing'
            dataset_document.processing_started_at = timezone.now()
            dataset_document.save(update_fields=['indexing_status', 'processing_started_at'])

            indexing_runner = IndexingRunner()
            indexing_runner.run(dataset, [dataset_document])
        except Exception as ex:
            logger.error('Retry index error:' + str(ex))
            dataset_document.indexing_status = 'error'
            dataset_document.error = str(ex)
            dataset_document.stopped_at = timezone.now()
            dataset_document.save(update_fields=['indexing_status', 'error', 'stopped_at'])

    end_at = time.perf_counter()
    logger.info('Retry dataset: {} latency: {}'.format(dataset_no, end_at - start_at))
