from rest_framework import serializers

from app.api.api_dto.ai_practice import AIPracticePaperReqDto, AIPracticePaperSubmitDto, \
    AIPracticePaperQuestionSubmitDto
from django_ext.base_validator import BaseValidator


class AIPracticePaperReqValidator(BaseValidator):
    user_id = serializers.CharField()
    subject_id = serializers.CharField()
    core_course_code = serializers.CharField()

    dto_class = AIPracticePaperReqDto


class AIPracticeSubjectiveAnswerValidator(BaseValidator):
    images = serializers.ListField(child=serializers.Char<PERSON>ield(), required=False, allow_empty=True)
    text = serializers.Char<PERSON>ield(required=False, allow_blank=True)


class AIPracticeAnswerDetailValidator(BaseValidator):
    question_id = serializers.IntegerField()
    choice_answer = serializers.ListField(child=serializers.Char<PERSON>ield(), required=False, allow_empty=True)
    subjective_answer = AIPracticeSubjectiveAnswerValidator(required=False, allow_null=True)


class AIPracticePaperSubmitValidator(BaseValidator):
    user_id = serializers.CharField()
    paper_id = serializers.IntegerField()
    answer_detail = serializers.ListField(child=AIPracticeAnswerDetailValidator(), allow_empty=True)

    dto_class = AIPracticePaperSubmitDto


class AIPracticePaperQuestionSubmitValidator(BaseValidator):
    user_id = serializers.CharField()
    paper_id = serializers.IntegerField()
    question_id = serializers.IntegerField()
    choice_answer = serializers.ListField(child=serializers.CharField(), required=False, allow_empty=True)
    subjective_answer = AIPracticeSubjectiveAnswerValidator(required=False, allow_null=True)

    dto_class = AIPracticePaperQuestionSubmitDto
