import numpy as np
import pandas as pd
import os
import django


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_application.settings')
django.setup()
from app.models import ExamAnalysisKnowledgePointWithStats


import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans

def load_sample_data():
    """
    创建一个模拟的知识点数据 DataFrame，结构与您的描述一致。
    """
    data = {
        'subject': [],
        'knowledge_point': [],
        'exam_count': [],
        'avg_difficulty': [],
        'mcq_freq': [],
        'mcq_difficulty': [],
        'comprehensive_count': [],
        'comprehensive_avg_difficulty': []
    }

    # 模拟数据
    subjects = ['计算机组成原理', '计算机网络', '操作系统', '数据结构']
    for subject in subjects:
        queryset = ExamAnalysisKnowledgePointWithStats.objects.filter(subject=subject).values()
        for item in queryset:
            data['subject'].append(item["subject"])
            data['knowledge_point'].append(item["point_name"])
            data['exam_count'].append(item["exam_count"])
            data['avg_difficulty'].append(item["avg_difficulty"])
            data['mcq_freq'].append(item["choice_count"])
            data['mcq_difficulty'].append(item["choice_avg_difficulty"])
            data['comprehensive_count'].append(item["comprehensive_count"])
            data['comprehensive_avg_difficulty'].append(item["comprehensive_avg_difficulty"])

    return pd.DataFrame(data)

def calculate_score_allocation(knowledge_df, subject_max_scores, target_score):
    """
    根据知识点数据，计算各科目的最优分数分配。
    
    :param knowledge_df: 从load_sample_data()获取的原始DataFrame。
    :param subject_max_scores: 包含各科目满分的字典。
    :param target_score: 你的目标总分。
    :return: 一个包含最终分数分配结果的DataFrame。
    """
    print("--- 开始执行分数优化分配计算 ---")
    
    # 1. 数据清洗和重命名
    df = knowledge_df.rename(columns={
        'point_name': 'knowledge_point',
        'exam_count': 'total_freq',
        'comprehensive_count': 'comp_freq'
    })
    df.fillna(0, inplace=True)
    df = df[df['total_freq'] > 0].copy()

    # 2. 特征工程：计算全局和组内Z-Score
    features = ['total_freq', 'avg_difficulty', 'comp_freq', 'comprehensive_avg_difficulty']
    
    scaler = StandardScaler()
    global_z = scaler.fit_transform(df[features])
    for i, col in enumerate(features):
        df[f'global_z_{col}'] = global_z[:, i]

    for col in features:
        df[f'local_z_{col}'] = df.groupby('subject')[col].transform(
            lambda x: (x - x.mean()) / (x.std(ddof=0) + 1e-9)
        ).fillna(0)

    # 3. K-Means聚类
    feature_cols_for_clustering = [col for col in df.columns if 'global_z' in col or 'local_z' in col]
    X = df[feature_cols_for_clustering]
    N_CLUSTERS = 4
    kmeans = KMeans(n_clusters=N_CLUSTERS, random_state=42, n_init='auto')
    df['cluster'] = kmeans.fit_predict(X)

    # 4. 解读簇并计算性价比分数
    z_centroids = df.groupby('cluster')[feature_cols_for_clustering].mean()
    cluster_scores = (z_centroids['global_z_total_freq'] * 1.0 -
                      z_centroids['global_z_avg_difficulty'] * 1.5 +
                      z_centroids['local_z_total_freq'] * 1.5 -
                      z_centroids['local_z_avg_difficulty'] * 2.0)
    
    min_score, max_score = cluster_scores.min(), cluster_scores.max()
    cost_performance_score = 1 + 4 * (cluster_scores - min_score) / (max_score - min_score + 1e-9)
    df['cp_score'] = df['cluster'].map(cost_performance_score)

    # 5. 使用修正后的逻辑计算科目优先度
    # 核心修正：使用相对重要性(local_z_freq)而非绝对频率
    df['point_value'] = (1 + df['local_z_total_freq']) * df['cp_score']
    subject_priority = df.groupby('subject')['point_value'].sum().to_frame(name='priority_P')

    # 6. 迭代分配分数
    df_alloc = subject_priority
    df_alloc['max_score'] = df_alloc.index.map(subject_max_scores)

    total_priority = df_alloc['priority_P'].sum()
    df_alloc['allocated_score'] = target_score * (df_alloc['priority_P'] / (total_priority + 1e-9))

    while True:
        overflow_mask = df_alloc['allocated_score'] > df_alloc['max_score']
        if not overflow_mask.any(): break
        excess_score = (df_alloc.loc[overflow_mask, 'allocated_score'] - df_alloc.loc[overflow_mask, 'max_score']).sum()
        df_alloc.loc[overflow_mask, 'allocated_score'] = df_alloc.loc[overflow_mask, 'max_score']
        eligible_mask = df_alloc['allocated_score'] < df_alloc['max_score']
        if not eligible_mask.any() or excess_score <= 0: break
        eligible_priority_sum = df_alloc.loc[eligible_mask, 'priority_P'].sum()
        df_alloc.loc[eligible_mask, 'allocated_score'] += excess_score * (df_alloc.loc[eligible_mask, 'priority_P'] / (eligible_priority_sum + 1e-9))

    df_alloc['final_score'] = np.round(df_alloc['allocated_score'])
    score_diff = target_score - df_alloc['final_score'].sum()
    if score_diff != 0:
        eligible_for_adj = (df_alloc['final_score'] < df_alloc['max_score']) & (df_alloc['final_score'] > 0)
        if eligible_for_adj.any():
            highest_priority_subject = df_alloc[eligible_for_adj]['priority_P'].idxmax()
            df_alloc.loc[highest_priority_subject, 'final_score'] += score_diff
        elif not df_alloc.empty:
            df_alloc.iloc[0, df_alloc.columns.get_loc('final_score')] += score_diff
            
    print("--- 计算完成 ---")
    return df_alloc

# ==================== 主执行区 ====================
if __name__ == '__main__':
    # 1. 定义考试基本信息
    SUBJECT_MAX_SCORES = {'数据结构': 45, '计算机组成原理': 45, '操作系统': 35, '计算机网络': 25}
    TARGET_SCORE = 114

    # 2. 从数据源加载数据
    raw_knowledge_data = load_sample_data()

    # 3. 调用核心函数进行计算
    final_allocation_result = calculate_score_allocation(
        knowledge_df=raw_knowledge_data,
        subject_max_scores=SUBJECT_MAX_SCORES,
        target_score=TARGET_SCORE
    )

    # 4. 展示最终结果
    print("\n" + "="*50)
    print("最终分数分配建议 (已修正)")
    print("="*50)
    print(final_allocation_result[['max_score', 'final_score']].astype(int))
    print("-"*50)
    print(f"验证总分: {final_allocation_result['final_score'].sum():.0f}")
    print("="*50)